package com.kaiba.m.core.aspect;

import com.kaiba.lib.base.annotation.api.KbCheckFrequency;
import com.kaiba.lib.base.annotation.api.KbCheckFrequencyReqPosition;
import com.kaiba.lib.base.response.KbCode;
import com.kaiba.lib.base.response.KbException;
import com.kaiba.lib.base.util.ServletRequestUtils;
import io.lettuce.core.RedisException;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import java.lang.annotation.Annotation;
import java.util.concurrent.TimeUnit;

/**
 * author: lyux
 * date: 2023-11-10
 */
@Slf4j
@Aspect
@Component
public class FrequencyCheckAspect {

    private final StringRedisTemplate redisTemplate;

    public FrequencyCheckAspect(StringRedisTemplate redisTemplate) {
        this.redisTemplate = redisTemplate;
    }

    @Pointcut("@annotation(com.kaiba.lib.base.annotation.api.KbCheckFrequency) && within(com.kaiba.m.core.controller..*)")
    private void frequencyCheckMethod() {}

    @Pointcut("@annotation(com.kaiba.lib.base.annotation.api.KbCheckFrequency) && within(com.kaiba.m.fuse.controller..*)")
    private void frequencyCheckMethod_fuse() {}

    @Around("frequencyCheckMethod() || frequencyCheckMethod_fuse()")
    public Object aroundController(ProceedingJoinPoint joinPoint) throws Throwable {
        MethodSignature methodSignature = (MethodSignature) joinPoint.getSignature();
        KbCheckFrequency annotation = methodSignature.getMethod().getAnnotation(KbCheckFrequency.class);
        if (null != annotation) {
            check(joinPoint, annotation);
        }
        return joinPoint.proceed(joinPoint.getArgs());
    }

    public void check(ProceedingJoinPoint joinPoint, KbCheckFrequency frequencyAnnotation) {
        HttpServletRequest request = ServletRequestUtils.getCurrentRequest();
        if (null == request) {
            // we check request frequency against user. treat it as permitted if no userId is provided.
            return;
        }
        String key = frequencyAnnotation.key();
        String keyStr;
        if (KbCheckFrequencyReqPosition.PARAM == frequencyAnnotation.position()) {
            keyStr = request.getParameter(key);
        } else if (KbCheckFrequencyReqPosition.HEADER == frequencyAnnotation.position()) {
            keyStr = request.getHeader(key);
        } else {
            throw new KbException(KbCode.ILLEGAL_STATE,
                    "frequency position not supported yet: " + frequencyAnnotation.position());
        }

        if (null == keyStr) {
            // we check request frequency against request param.
            // treat it as permitted if specified value is not present.
            return;
        }
        int frequency = frequencyAnnotation.value();
        String message = frequencyAnnotation.message();
        String redisKey = "java_core_request_frequency_" + joinPoint + "_" + keyStr;
        try {
            Boolean hasKey = redisTemplate.hasKey(redisKey);
            if (null != hasKey && hasKey) {
                log.info("request too soon: " + redisKey);
                throw new KbException(KbCode.REQUEST_TOO_SOON)
                        .setReadableMessage(message)
                        .setLevel(KbException.LEVEL_DEBUG);
            } else {
                redisTemplate.opsForValue().set(redisKey, keyStr, frequency, TimeUnit.SECONDS);
            }
        } catch (RedisException e) {
            // redis exception, treat it as frequency permitted.
            log.error("check api frequency fail due to redis exception. key: " + redisKey, e);
        }
    }


}
