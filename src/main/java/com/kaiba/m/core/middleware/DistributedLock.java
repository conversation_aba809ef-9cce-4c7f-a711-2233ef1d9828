package com.kaiba.m.core.middleware;

import com.kaiba.lib.base.response.KbCode;
import com.kaiba.lib.base.response.KbException;
import com.kaiba.lib.base.util.StringUtils;
import org.springframework.data.redis.connection.RedisConnection;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.connection.RedisStringCommands;
import org.springframework.data.redis.connection.ReturnType;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.types.Expiration;

import java.util.UUID;
import java.util.concurrent.TimeUnit;

/**
 * author: jason, lyux
 * date: 20-10-27
 */
public class DistributedLock {

    private static final String KEY_PREFIX = "java_core_distributed_lock_";

    private final RedisConnection connection;
    private final String lockId;

    public DistributedLock(RedisTemplate<?, ?> redisTemplate) {
        this(getConnectionFromTemplate(redisTemplate));
    }

    public DistributedLock(RedisConnection connection) {
        this.connection = connection;
        this.lockId = UUID.randomUUID().toString();
    }

    public Boolean releaseDistributedLock(String mark) {
        String cacheKey = getCacheKey(mark);
        String script = "if redis.call('get', KEYS[1]) == ARGV[1] then return redis.call('del', KEYS[1]) else return 0 end";
        Long result = connection.eval(
                script.getBytes(), ReturnType.INTEGER, 1, cacheKey.getBytes(), lockId.getBytes());
        return result != null && result == 1;
    }

    public Boolean tryGetDistributedLock(String mark, int ttl) {
        String cacheKey = getCacheKey(mark);
        return connection.set(
                cacheKey.getBytes(),
                lockId.getBytes(),
                Expiration.milliseconds(ttl),
                RedisStringCommands.SetOption.SET_IF_ABSENT);
    }

    public Boolean tryGetDistributedLockWithRetry(String mark, int ttl, int retryTimes) {
        int retries = 0;
        while (retries < retryTimes) {
            if (tryGetDistributedLock(mark, ttl)) {
                return true; // 成功获取锁，返回true
            } else {
                retries++;
                // 等待一段时间后进行重试
                try {
                    TimeUnit.MILLISECONDS.sleep(1000);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                }
            }
        }
        return false;
    }

    public Boolean isLocked(String mark) {
        String cacheKey = getCacheKey(mark);
        byte[] data = connection.get(cacheKey.getBytes());
        if (data == null) {
            return false;
        } else {
            String lockId = new String(data);
            return !lockId.equals(this.lockId);
        }
    }

    // --------------------------------------------------

    private static String getCacheKey(String mark) {
        if (StringUtils.isEmpty(mark)) {
            throw new KbException(KbCode.ILLEGAL_ARGUMENT, "mark should not be null");
        }
        return KEY_PREFIX + mark;
    }

    private static RedisConnection getConnectionFromTemplate(RedisTemplate<?, ?> redisTemplate) {
        RedisConnectionFactory connectionFactory = redisTemplate.getConnectionFactory();
        if (connectionFactory == null) {
            throw new KbException(KbCode.ILLEGAL_STATE, "redis connection factory null");
        }
        return connectionFactory.getConnection();
    }

}
