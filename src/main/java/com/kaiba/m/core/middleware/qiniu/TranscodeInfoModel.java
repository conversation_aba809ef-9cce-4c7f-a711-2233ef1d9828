package com.kaiba.m.core.middleware.qiniu;

import lombok.Getter;
import lombok.Setter;

/**
 * author: lyux
 * date: 2020-10-14
 */
@Getter
@Setter
public class TranscodeInfoModel {

    private final String fops;
    private final String pipeline;

    public TranscodeInfoModel(String fops, String pipeline) {
        this.fops = fops;
        this.pipeline = pipeline;
    }

    public String fopsWithSaveAs(String saveAs) {
        return fops + "|saveas/" + saveAs;
    }

}
