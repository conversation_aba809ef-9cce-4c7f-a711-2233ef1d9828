package com.kaiba.m.core.middleware.qiniu;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * author: lyux
 * date: 2020-09-25
 */
@Data
@NoArgsConstructor
public class QiniuPipelineResult {

    /** 持久化处理的进程 ID, 即前文中的 <persistentId> */
    private String id;

    /** 处理源文件的文件名 */
    private String inputKey;

    /** 处理源文件所在的空间名 */
    private String inputBucket;

    /** 云处理操作的处理队列, 如 1380653776.kaiba_audio */
    private String pipeline;

    /** 云处理请求的请求id, 主要用于七牛技术人员的问题排查 */
    private String reqid;

    /** 任务结果状态码: 0-成功, 1-等待处理, 2-正在处理, 3-处理失败, 4-通知提交失败 */
    private Integer code;

    /** 与状态码相对应的详细描述 */
    private String desc;

    /** 如果处理失败, 该字段会给出失败的详细原因 */
    private String error;

    private List<Item> items;

    @Data
    @NoArgsConstructor
    public static class Item {

        /** 任务结果状态码: 0-成功, 1-等待处理, 2-正在处理, 3-处理失败, 4-通知提交失败 */
        private Integer code;

        /** 与状态码相对应的详细描述 */
        private String desc;

        /** 转码结果 key, 即转码后的文件 key */
        private String key;

        /** 所执行的云处理操作命令fopN. 如:
         *  avthumb/mp3/ab/16k/ar/16000|saveas/a2FpYmE6OTEyY2U5NjFlMjNjNjRmMGY5MDIwNjNkMjMwZjRlZTlfbXAz
         */
        private String cmd;

        /** 云处理结果保存在服务端的唯一 hash 标识. */
        private String hash;

        /** 当用户执行 saveas 时, 如果未加 force 且指定的 bucket:key 存在, 则返回 1, 告诉用户返回的是旧数据 */
        private Integer returnOld;
    }
}
