package com.kaiba.m.core.middleware;

import com.google.common.collect.Lists;
import com.kaiba.lib.base.constant.KbSignType;
import com.kaiba.lib.base.middleware.apivalidator.signsalt.IKbSignSaltProvider;
import com.kaiba.lib.base.response.KbCode;
import com.kaiba.lib.base.response.KbException;
import com.kaiba.lib.base.util.ServletRequestUtils;
import java.util.List;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import java.util.concurrent.TimeUnit;

@Component
public class WebAuthSignSaltProvider implements IKbSignSaltProvider {

    private final StringRedisTemplate redisTemplate;

    public WebAuthSignSaltProvider(StringRedisTemplate redisTemplate) {
        this.redisTemplate = redisTemplate;
    }

    @Override
    public KbSignType getSignType() {
        return KbSignType.WX_WEB_AUTH;
    }

    @Override
    public List<String> getSalts() {
        HttpServletRequest request = ServletRequestUtils.getCurrentRequest();
        String openId = request.getParameter("openId");
        if (null == openId) {
            throw (new KbException(KbCode.REQUEST_PARAM_INVALID, "check kbSign, invalid userId: null")).setLevel(1);
        } else {
            return Lists.newArrayList(redisTemplate.opsForValue().get(saltKey(openId)));
        }
    }

    public void saveSalt(String key,String salt){
        redisTemplate.opsForValue().set(saltKey(key),salt,20, TimeUnit.MINUTES);
    }

    private static String saltKey(String key){
        return "web_auth_"+key;
    }
}
