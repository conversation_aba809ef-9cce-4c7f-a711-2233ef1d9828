package com.kaiba.m.core.middleware.mongo.utils;

import org.springframework.data.mongodb.core.query.Update;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * author: lyux
 * date: 2020-04-27
 *
 * 封装 mongo 原生的 {@link Update}, 加入 null 与 empty 判断, 以方便流式调用.
 * 其中 empty 为 true 的判断适用于以下类型:
 * {@link String}, 判断其为 null, 或者只包含空白符
 * {@link List}, 判断其为 null, 或者 size 为 0
 * {@link Map}, 判断其为 null, 或者 size 为 0
 * {@link Set}, 判断其为 null, 或者 size 为 0
 */
public class KbMongoUpdate extends Update {

    /**
     * {@link #set(String, Object, UpdateMode)} 方法的工作模式.
     */
    public enum UpdateMode {

        /** 不做改动 */
        UNMODIFIABLE,

        /** 值非 null 则 update */
        SET_IF_NOT_NULL,

        /** 值非空则 update */
        SET_IF_NOT_EMPTY,

        /** 值为 null 则 unset */
        UNSET_IF_NULL,

        /** 值为空则 unset */
        UNSET_IF_EMPTY,

        /** 值非 null 则 update, 值为 null 则 unset */
        SET_UNSET_ON_NULL,

        /** 值非空则 update, 值为空则 unset */
        SET_UNSET_ON_EMPTY,
    }

    // -----------------------------------------------------

    public KbMongoUpdate() {
    }

    public KbMongoUpdate set(String key, Object value, UpdateMode mode) {
        switch (mode) {
            case SET_IF_NOT_NULL:
                return setIfNotNull(key, value);
            case SET_IF_NOT_EMPTY:
                return setIfNotEmpty(key, value);
            case UNSET_IF_NULL:
                return unsetIfNull(key, value);
            case UNSET_IF_EMPTY:
                return unsetIfEmpty(key, value);
            case SET_UNSET_ON_NULL:
                return setUnsetOnNull(key, value);
            case SET_UNSET_ON_EMPTY:
                return setUnsetOnEmpty(key, value);
            default:
                return this;
        }
    }

    public KbMongoUpdate setUnsetOnNull(String key, Object value) {
        if (value == null) {
            unset(key);
        } else {
            set(key, value);
        }
        return this;
    }

    public KbMongoUpdate setUnsetOnEmpty(String key, Object value) {
        if (Utils.isEmpty(value)) {
            unset(key);
        } else {
            set(key, value);
        }
        return this;
    }

    public KbMongoUpdate setIfNotNull(String key, Object value) {
        if (value != null) {
            set(key, value);
        }
        return this;
    }

    public KbMongoUpdate setIfNotEmpty(String key, Object value) {
        if (!Utils.isEmpty(value)) {
            set(key, value);
        }
        return this;
    }

    public KbMongoUpdate setWhen(boolean when, String key, Object value) {
        if (when) {
            set(key, value);
        }
        return this;
    }

    public KbMongoUpdate unsetIfNull(String key, Object value) {
        if (value == null) {
            unset(key);
        }
        return this;
    }

    public KbMongoUpdate unsetIfEmpty(String key, Object value) {
        if (Utils.isEmpty(value)) {
            unset(key);
        }
        return this;
    }

    public KbMongoUpdate unsetWhen(boolean when, String key) {
        if (when) {
            unset(key);
        }
        return this;
    }

}
