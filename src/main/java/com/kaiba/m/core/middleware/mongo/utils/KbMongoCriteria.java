package com.kaiba.m.core.middleware.mongo.utils;

import org.springframework.data.mongodb.core.query.Criteria;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * author: lyux
 * date: 2020-04-27
 *
 * 封装 mongo 原生的 {@link Criteria}, 加入 null 与 empty 判断, 以方便流式调用.
 * 其中 empty 为 true 的判断适用于以下类型:
 * {@link String}, 判断其为 null, 或者只包含空白符
 * {@link List}, 判断其为 null, 或者 size 为 0
 * {@link Map}, 判断其为 null, 或者 size 为 0
 * {@link Set}, 判断其为 null, 或者 size 为 0
 */
public class KbMongoCriteria extends Criteria {

    public KbMongoCriteria() {
    }

    public KbMongoCriteria criteriaIfNotNull(String key, Object value) {
        if (value != null) {
            and(key).is(value);
        }
        return this;
    }

    public KbMongoCriteria criteriaIfNotEmpty(String key, Object value) {
        if (!Utils.isEmpty(value)) {
            and(key).is(value);
        }
        return this;
    }

    public KbMongoCriteria criteriaInIfNotEmpty(String key, Collection<?> c) {
        if (!Utils.isEmpty(c)) {
            and(key).in(c);
        }
        return this;
    }

    public KbMongoCriteria regexIfNotEmpty(String key, String regex) {
        if (!Utils.isEmpty(regex)) {
            and(key).regex(regex);
        }
        return this;
    }

}
