package com.kaiba.m.core.middleware.yunxin;

import com.google.gson.reflect.TypeToken;
import com.kaiba.m.core.util.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.web.client.RestTemplateBuilder;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.web.client.RestTemplate;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * https://dev.yunxin.163.com/docs/product/%E7%9B%B4%E6%92%AD/%E6%9C%8D%E5%8A%A1%E7%AB%AFAPI%E6%96%87%E6%A1%A3/2.%E7%9B%B4%E6%92%AD%E7%AE%A1%E7%90%86?pos=toc-0-8
 */
@Slf4j
public class YunXinApi {
    private static final String APP_KEY = "9234a69a91d649a691f6bf5ac1fb6f9e";
    private static final String APP_SECRET = "d50d622a0e7f4e529266653dddb4ac04";
    private static final String HOST = "https://vcloud.163.com";
    private static final RestTemplate restTemplate = new RestTemplateBuilder().build();


    private static HttpHeaders getHttpHeaders() {
        String curTime = String.valueOf(System.currentTimeMillis() / 1000);
        String nonce = UUID.randomUUID().toString();
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set("AppKey", APP_KEY);
        headers.set("Nonce", nonce);
        headers.set("CurTime", curTime);
        headers.set("CheckSum", CheckSumBuilder.getCheckSum(APP_SECRET, nonce, curTime));
        return headers;
    }

    public static YXResponse<Channel> addChannel(String name) {
        String url = HOST + "/app/channel/create";
        Map<String, String> param = new HashMap<>();
        param.put("name", name);
        HttpEntity<String> entity = new HttpEntity<>(JsonUtils.getGson().toJson(param), getHttpHeaders());
        String res = restTemplate.postForObject(url, entity, String.class);
        log.info(res);
        YXResponse<Channel> response = JsonUtils.getGson().fromJson(res, new TypeToken<YXResponse<Channel>>() {
        }.getType());
        return response;
    }

    public static YXResponse<Void> updateChannel(String cid, String name) {
        String url = HOST + "/app/channel/update";
        Map<String, String> param = new HashMap<>();
        param.put("cid", cid);
        param.put("name", name);
        HttpEntity<String> entity = new HttpEntity<>(JsonUtils.getGson().toJson(param), getHttpHeaders());
        String res = restTemplate.postForObject(url, entity, String.class);
        log.info(res);
        YXResponse<Void> response = JsonUtils.getGson().fromJson(res, new TypeToken<YXResponse<Void>>() {
        }.getType());
        return response;
    }

    /**
     * 自动录制开关
     * @param cid
     * @param needRecord 0 关闭， 1 开启
     * @return
     */
    public static YXResponse<Void> autoRecordSwitch(String cid, Integer needRecord){
        String url = HOST + "/app/channel/autoRecordSwitch";
        Map<String, Object> param = new HashMap<>();
        param.put("cid", cid);
        param.put("needRecord", needRecord);
        HttpEntity<String> entity = new HttpEntity<>(JsonUtils.getGson().toJson(param), getHttpHeaders());
        String res = restTemplate.postForObject(url, entity, String.class);
        log.info(res);
        YXResponse<Void> response = JsonUtils.getGson().fromJson(res, new TypeToken<YXResponse<Void>>() {
        }.getType());
        return response;
    }

    public static YXResponse<Void> updateConfig(String cid, String name,Integer siteId){
        String url = HOST + "/app/record/channel/updateConfig";
        Map<String, Object> param = new HashMap<>();
        param.put("cid", cid);
        param.put("duration", 120);
        param.put("format", 0);
        param.put("filename", name+"_"+siteId);
        HttpEntity<String> entity = new HttpEntity<>(JsonUtils.getGson().toJson(param), getHttpHeaders());
        String res = restTemplate.postForObject(url, entity, String.class);
        log.info(res);
        YXResponse<Void> response = JsonUtils.getGson().fromJson(res, new TypeToken<YXResponse<Void>>() {
        }.getType());
        return response;
    }

    public static YXResponse<Status> channelStats(String cid) {
        String url = HOST + "/app/channelstats";
        Map<String, String> param = new HashMap<>();
        param.put("cid", cid);

        HttpEntity<String> entity = new HttpEntity<>(JsonUtils.getGson().toJson(param), getHttpHeaders());
        String res = restTemplate.postForObject(url, entity, String.class);
        log.info(res);
        YXResponse<Status> response = JsonUtils.getGson().fromJson(res, new TypeToken<YXResponse<Status>>() {
        }.getType());
        return response;
    }

    public static YXResponse<YXPage<YXVideoRecord>> getRecordList(String cid, Integer page, Integer pageSize) {
        String url = HOST + "/app/videolist";
        Map<String, Object> param = new HashMap<>();
        param.put("cid", cid);
        param.put("pnum", page);
        param.put("records", pageSize);

        HttpEntity<String> entity = new HttpEntity<>(JsonUtils.getGson().toJson(param), getHttpHeaders());
        String res = restTemplate.postForObject(url, entity, String.class);
        log.info(res);
        YXResponse<YXPage<YXVideoRecord>> response = JsonUtils.getGson().fromJson(res, new TypeToken<YXResponse<YXPage<YXVideoRecord>>>() {
        }.getType());
        return response;

    }
}
