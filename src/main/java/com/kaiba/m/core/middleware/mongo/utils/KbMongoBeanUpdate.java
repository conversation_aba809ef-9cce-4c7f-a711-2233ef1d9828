package com.kaiba.m.core.middleware.mongo.utils;

import com.kaiba.lib.base.assembler.Assembler;
import com.kaiba.lib.base.response.KbCode;
import com.kaiba.lib.base.response.KbException;
import org.springframework.data.mongodb.core.query.Update;

import java.util.Map;

/**
 * author: lyux
 * date: 2020-04-27
 *
 * 封装 mongo 原生的 {@link Update}, 加入 null 与 empty 判断, 加入从 bean 中提取数据的能力, 以方便流式调用.
 * 其中 empty 判断参见父类 {@link KbMongoUpdate}
 */
public class KbMongoBeanUpdate extends KbMongoUpdate {

    private Object bean;
    private Map<String, Object> beanFieldMap;

    public KbMongoBeanUpdate(Object bean) {
        this.bean = bean;
    }

    public KbMongoBeanUpdate setByMode(String key, UpdateMode mode) {
        set(key, beanValue(key), mode);
        return this;
    }

    public KbMongoBeanUpdate setUnsetOnNull(String key) {
        setUnsetOnNull(key, beanValue(key));
        return this;
    }

    public KbMongoBeanUpdate setUnsetOnEmpty(String key) {
        setUnsetOnEmpty(key, beanValue(key));
        return this;
    }

    public KbMongoBeanUpdate setIfNotNull(String key) {
        setIfNotNull(key, beanValue(key));
        return this;
    }

    public KbMongoBeanUpdate setIfNotEmpty(String key) {
        setIfNotEmpty(key, beanValue(key));
        return this;
    }

    public KbMongoBeanUpdate setWhen(boolean when, String key) {
        if (when) {
            set(key, beanValue(key));
        }
        return this;
    }

    public KbMongoBeanUpdate unsetIfNull(String key) {
        unsetIfNull(key, beanValue(key));
        return this;
    }

    public KbMongoBeanUpdate unsetIfEmpty(String key) {
        unsetIfEmpty(key, beanValue(key));
        return this;
    }

    // -----------------------------------------------------

    public KbMongoBeanUpdate setAllField() {
        Map<String, Object> beanMap = beanMap();
        for (String key: beanMap.keySet()) {
            set(key, beanMap.get(key));
        }
        return this;
    }

    public KbMongoBeanUpdate setAllFieldByMode(UpdateMode mode) {
        Map<String, Object> beanMap = beanMap();
        for (String key: beanMap.keySet()) {
            set(key, beanMap.get(key), mode);
        }
        return this;
    }

    // -----------------------------------------------------

    private Map<String, Object> beanMap() {
        if (beanFieldMap == null) {
            if (null == bean) {
                throw new KbException(KbCode.ILLEGAL_ARGUMENT, "bean null");
            }
            beanFieldMap = Assembler.on().withBean(bean).result();
        }
        return beanFieldMap;
    }

    private Object beanValue(String key) {
        return beanMap().get(key);
    }

}
