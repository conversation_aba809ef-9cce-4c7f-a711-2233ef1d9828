package com.kaiba.m.core.middleware.mongo.utils;

import com.kaiba.lib.base.assembler.Assembler;
import com.kaiba.lib.base.response.KbCode;
import com.kaiba.lib.base.response.KbException;
import org.springframework.data.mongodb.core.query.Criteria;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * author: lyux
 * date: 2020-04-27
 *
 * 封装 mongo 原生的 {@link Criteria}, 加入 null 与 empty 判断, 以方便流式调用.
 * 其中 empty 为 true 的判断适用于以下类型:
 * {@link String}, 判断其为 null, 或者只包含空白符
 * {@link List}, 判断其为 null, 或者 size 为 0
 * {@link Map}, 判断其为 null, 或者 size 为 0
 * {@link Set}, 判断其为 null, 或者 size 为 0
 */
public class KbMongoBeanCriteria extends KbMongoCriteria {

    private Object bean;
    private Map<String, Object> beanFieldMap;

    public KbMongoBeanCriteria(Object bean) {
        this.bean = bean;
    }

    public KbMongoBeanCriteria criteriaIfNotNull(String key) {
        criteriaIfNotNull(key, beanValue(key));
        return this;
    }

    public KbMongoBeanCriteria criteriaIfNotEmpty(String key) {
        criteriaIfNotEmpty(key, beanValue(key));
        return this;
    }

    public KbMongoBeanCriteria criteriaInIfNotEmpty(String key) {
        criteriaInIfNotEmpty(key, (Collection<?>) beanValue(key));
        return this;
    }

    // -----------------------------------------------------

    private Map<String, Object> beanMap() {
        if (beanFieldMap == null) {
            if (null == bean) {
                throw new KbException(KbCode.ILLEGAL_ARGUMENT, "bean null");
            }
            beanFieldMap = Assembler.on().withBean(bean).result();
        }
        return beanFieldMap;
    }

    private Object beanValue(String key) {
        return beanMap().get(key);
    }

}
