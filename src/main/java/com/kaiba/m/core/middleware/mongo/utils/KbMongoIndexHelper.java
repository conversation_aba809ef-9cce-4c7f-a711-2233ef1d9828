package com.kaiba.m.core.middleware.mongo.utils;

import lombok.extern.slf4j.Slf4j;
import org.springframework.data.mapping.context.MappingContext;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.convert.MongoConverter;
import org.springframework.data.mongodb.core.index.IndexInfo;
import org.springframework.data.mongodb.core.index.IndexOperations;
import org.springframework.data.mongodb.core.index.MongoPersistentEntityIndexResolver;
import org.springframework.data.mongodb.core.mapping.BasicMongoPersistentEntity;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.MongoMappingContext;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.io.File;
import java.io.IOException;
import java.net.URL;
import java.util.*;

/**
 * author: lyux
 * date: 2021-05-19
 */
@Slf4j
@Component
public class KbMongoIndexHelper {

    private final MongoTemplate mongoTemplate;
    private final MongoConverter mongoConverter;

    public KbMongoIndexHelper(MongoTemplate mongoTemplate, MongoConverter mongoConverter) {
        this.mongoTemplate = mongoTemplate;
        this.mongoConverter = mongoConverter;
    }

    @Async
    public void ensureIndexForAllAsync() {
        ensureIndexForAll();
    }

    public void ensureIndexForAll() {
        long init = System.currentTimeMillis();
        MappingContext<?, ?> mappingContext = this.mongoConverter.getMappingContext();
        if (mappingContext instanceof MongoMappingContext) {
            MongoMappingContext mongoMappingContext = (MongoMappingContext) mappingContext;
            for (BasicMongoPersistentEntity<?> persistentEntity : mongoMappingContext.getPersistentEntities()) {
                Class<?> clazz = persistentEntity.getType();
                ensureIndexForClass(clazz);
            }
        }
        log.info("mongo ensureIndexForAll, takes {} millis", (System.currentTimeMillis() - init));
    }

    public void ensureIndexForPackage(String packageName) throws ClassNotFoundException, IOException {
        List<Class<?>> classes = getClasses(packageName);
        if (classes.size() != 0) {
            long init = System.currentTimeMillis();
            for (Class<?> clazz : classes) {
                ensureIndexForClass(clazz);
            }
            log.info("mongo ensureIndexForPackage: " + packageName + ", " +
                    "takes {} millis", (System.currentTimeMillis() - init));
        }
    }

    public void ensureIndexForClass(String classFullName) throws ClassNotFoundException {
        Class<?> clazz = Class.forName(classFullName);
        ensureIndexForClass(clazz);
    }

    public void ensureIndexForClass(Class<?> clazz) {
        if (clazz.isAnnotationPresent(Document.class)) {
            long init = System.currentTimeMillis();
            MappingContext<?, ?> mappingContext = this.mongoConverter.getMappingContext();
            MongoMappingContext mongoMappingContext = (MongoMappingContext) mappingContext;
            IndexOperations indexOps = mongoTemplate.indexOps(clazz);
            MongoPersistentEntityIndexResolver resolver = new MongoPersistentEntityIndexResolver(mongoMappingContext);
            resolver.resolveIndexFor(clazz).forEach(indexOps::ensureIndex);
            log.info("mongo ensureIndexForClass: " + clazz.getName() + ", " +
                    "takes {} millis", (System.currentTimeMillis() - init));
        } else {
            log.info("class is not annotated by @Document : " + clazz.getName());
        }
    }

    // ----------------------------------------------------------------
    /**
     * Scans all classes accessible from the context class loader which belong to the given package and subpackages.
     */
    private static List<Class<?>> getClasses(String packageName)
            throws ClassNotFoundException, IOException {
        ClassLoader classLoader = Thread.currentThread().getContextClassLoader();
        assert classLoader != null;
        String path = packageName.replace('.', '/');
        Enumeration<URL> resources = classLoader.getResources(path);
        List<File> dirs = new LinkedList<>();
        while (resources.hasMoreElements()) {
            URL resource = resources.nextElement();
            dirs.add(new File(resource.getFile()));
        }
        ArrayList<Class<?>> classes = new ArrayList<>();
        for (File directory : dirs) {
            classes.addAll(findClasses(directory, packageName));
        }
        return classes;
    }

    /**
     * Recursive method used to find all classes in a given directory and subdirs.
     *
     * @param directory   The base directory
     * @param packageName The package name for classes found inside the base directory
     */
    private static List<Class<?>> findClasses(File directory, String packageName) throws ClassNotFoundException {
        if (!directory.exists()) {
            return Collections.emptyList();
        }
        File[] files = directory.listFiles();
        if (files == null || files.length == 0) {
            return Collections.emptyList();
        }
        List<Class<?>> classes = new LinkedList<>();
        for (File file : files) {
            if (file.isDirectory()) {
                assert !file.getName().contains(".");
                classes.addAll(findClasses(file, packageName + "." + file.getName()));
            } else if (file.getName().endsWith(".class")) {
                classes.add(Class.forName(packageName + '.' + file.getName().substring(0, file.getName().length() - 6)));
            }
        }
        return classes;
    }

}
