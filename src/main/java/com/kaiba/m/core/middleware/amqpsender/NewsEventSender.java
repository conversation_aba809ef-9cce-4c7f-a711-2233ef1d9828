package com.kaiba.m.core.middleware.amqpsender;

import com.kaiba.lib.base.constant.KbAmqp;
import com.kaiba.lib.base.domain.news.NewsMqEventModel;
import com.kaiba.lib.base.domain.note.NoteMqEventModel;
import com.kaiba.m.core.util.JsonUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.AmqpTemplate;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version NewsEventSender, v0.1 2023/12/8 14:19 daopei Exp $
 **/
@Slf4j
@Component
@RequiredArgsConstructor
public class NewsEventSender {

    private final AmqpTemplate amqpTemplate;

    public void send(NewsMqEventModel eventModel) {
        amqpTemplate.convertAndSend(
                KbAmqp.NEWS_CHANNEL.getExchange(),
                KbAmqp.NEWS_CHANNEL.getRouting(),
                JsonUtils.getGson().toJson(eventModel)
        );
        log.info("send news mq success , data : {}",JsonUtils.getGson().toJson(eventModel));
    }
}
