package com.kaiba.m.core.middleware.mongo.utils;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * author: lyux
 * date: 2020-09-07
 */
final class Utils {

    static boolean isEmpty(Object value) {
        if (null == value) {
            return true;
        }
        if (value instanceof String && ((String) value).length() == 0) {
            return true;
        }
        if (value instanceof List && ((List<?>) value).size() == 0) {
            return true;
        }
        if (value instanceof Map && ((Map<?, ?>) value).size() == 0) {
            return true;
        }
        if (value instanceof Set && ((Set<?>) value).size() == 0) {
            return true;
        }
        return false;
    }

}
