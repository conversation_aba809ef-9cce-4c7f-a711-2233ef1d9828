package com.kaiba.m.core.middleware.qiniu;

import com.kaiba.lib.base.response.KbCode;
import com.kaiba.lib.base.response.KbException;
import com.kaiba.lib.base.util.GsonUtils;
import com.kaiba.lib.base.util.StringUtils;
import com.qiniu.common.QiniuException;
import com.qiniu.http.Response;
import com.qiniu.processing.OperationManager;
import com.qiniu.storage.BucketManager;
import com.qiniu.storage.Configuration;
import com.qiniu.storage.Region;
import com.qiniu.storage.UploadManager;
import com.qiniu.storage.model.FetchRet;
import com.qiniu.storage.model.FileInfo;
import com.qiniu.util.Auth;
import com.qiniu.util.StringMap;
import com.qiniu.util.UrlSafeBase64;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.URL;
import java.net.URLConnection;
import java.util.UUID;
import java.util.concurrent.TimeUnit;

/**
 * author: lyux
 * date: 2020-09-18
 *
 * 参考文档:
 *
 * https://github.com/qiniu/java-sdk
 *
 * https://developer.qiniu.com/kodo/manual/1201/access-token
 *
 * https://developer.qiniu.com/kodo/manual/1206/put-policy
 *
 */
@Slf4j
@Component
public class QiniuManager {

    public static final String QINIU_HOST = "static.kaiba315.com.cn";
    public static final String HTTPS_QINIU_HOST = "https://" + QINIU_HOST;
    public static final String HTTP_QINIU_HOST = "http://" + QINIU_HOST;

    private static final String ACCESS_KEY = "tU-GisLrkEEK1af07N1_R7Sqm6gPJz8gdrp-cIE-";
    private static final String SECRET_KEY = "ERQDwTtq7KnO9NTZYj9SS_eCXi0NT8iiL2G7_uk1";
    private static final String BUCKET = "kaiba";

    private static final String FILE_TOKEN_CACHE_KEY = "java_qiniu_file_token";
    private static final Long FILE_TOKEN_EXPIRE = QiniuUploadTokenType.FILE.getExpire().getSeconds();
    private static final Long FILE_TOKEN_CACHE_EXPIRE = FILE_TOKEN_EXPIRE - TimeUnit.MINUTES.toSeconds(5);

    private final Configuration config;
    private final Auth auth;
    private final UploadManager uploadManager;
    private final BucketManager bucketManager;
    private final OperationManager operationManager;
    private final StringRedisTemplate stringRedisTemplate;
    private final String apiHost;
    private final String qiniuHost;

    public QiniuManager(
            StringRedisTemplate stringRedisTemplate,
            @Value("${kb.host.api}") String apiHost,
            @Value("${kb.host.static}") String qiniuHost
    ) {
        this.config = new Configuration(Region.region0());
        this.auth = Auth.create(ACCESS_KEY, SECRET_KEY);
        this.uploadManager = new UploadManager(config);
        this.bucketManager = new BucketManager(auth, config);
        this.operationManager = new OperationManager(auth, config);
        this.stringRedisTemplate = stringRedisTemplate;
        this.apiHost = apiHost;
        this.qiniuHost = qiniuHost;
    }

    // ------------------------------------------------------
    // upload

    public UploadManager getUploadManager() {
        return uploadManager;
    }

    public void upload(String path, String key, String token) {
        try {
            Response res = uploadManager.put(path, key, token);
            log.debug("upload to qiniu by path success: " + res.bodyString());
        } catch (QiniuException e) {
            throw new KbException(KbCode.ERROR, "upload to qiniu by path fail: " + path + " -> " + key, e);
        }
    }

    public void upload(byte[] data, String key, String token) {
        try {
            Response res = uploadManager.put(data, key, token);
            log.debug("upload to qiniu by data success: " + res.bodyString());
        } catch (QiniuException e) {
            throw new KbException(KbCode.ERROR, "upload to qiniu by data fail: " + key, e);
        }
    }

    // 将一个给定的 url 对应的文件上传到开吧的七牛云上. 用于外链图片转换为开吧自有图片等场景.
    public String uploadUrl(String u, long maxSizeInByte) {
        byte[] data;
        int byteSum = 0;
        int byteRead;
        InputStream inStream = null;
        ByteArrayOutputStream outStream = null;
        try {
            URL url = new URL(u);
            URLConnection conn = url.openConnection();
            inStream = conn.getInputStream();
            outStream = new ByteArrayOutputStream();
            byte[] buffer = new byte[4096];
            while ((byteRead = inStream.read(buffer)) != -1) {
                byteSum += byteRead;
                if (byteSum > maxSizeInByte) {
                    throw new KbException(KbCode.ERROR, "input stream byte size exceed max: " + maxSizeInByte);
                }
                outStream.write(buffer, 0, byteRead);
            }
            data = outStream.toByteArray();
        } catch (IOException e) {
            throw new RuntimeException(e);
        } finally {
            try {
                if (inStream != null) {
                    inStream.close();
                }
                if (outStream != null) {
                    outStream.close();
                }
            } catch (IOException ignore) {
            }
        }
        String key = StringUtils.toMd5(data);
        upload(data, key, getFileUploadToken());
        return key;
    }

    public String uploadUrlWithToken(String u, String key,String token) {
        byte[] data;
        int byteRead;
        InputStream inStream = null;
        ByteArrayOutputStream outStream = null;
        try {
            URL url = new URL(u);
            URLConnection conn = url.openConnection();
            inStream = conn.getInputStream();
            outStream = new ByteArrayOutputStream();
            byte[] buffer = new byte[4096];
            while ((byteRead = inStream.read(buffer)) != -1) {
                outStream.write(buffer, 0, byteRead);
            }
            data = outStream.toByteArray();
        } catch (IOException e) {
            throw new RuntimeException(e);
        } finally {
            try {
                if (inStream != null) {
                    inStream.close();
                }
                if (outStream != null) {
                    outStream.close();
                }
            } catch (IOException ignore) {
            }
        }
        upload(data, key, token);
        return key;
    }

    public String asyncFetch(String sourceUrl, String fileName, String callbackUrl, String callbackBody) {
        StringMap stringMap = new StringMap();
        stringMap.putNotNull("key", fileName);
        stringMap.putNotNull("callbackurl", callbackUrl);
        stringMap.putNotNull("callbackbody", callbackBody);
        Response ret;
        try {
            ret = bucketManager.asyncFetch(sourceUrl, BUCKET, stringMap);
        } catch (QiniuException e) {
            log.error("qiniu asyncFetch fail: {}, e:{}" + e.response.toString(), e);
            throw new KbException(KbCode.THIRD_PARTY_REQUEST_FAIL).li();
        }
        log.info("qiniu fetch response:{}", ret.toString());
        return fileName;
    }
    public void upload(byte[] data, String key) {
        upload(data, key, getFileUploadToken());
    }

    public void uploadFile(String path, String key) {
        upload(path, key, getFileUploadToken());
    }

    public void uploadVoiceMP3(String path, String key, boolean transCode) {
        upload(path, key, getUploadToken(key, QiniuUploadTokenType.VOICE_MP3, transCode));
    }

    public void uploadVoiceAMR(String path, String key, boolean transCode) {
        upload(path, key, getUploadToken(key, QiniuUploadTokenType.VOICE_AMR, transCode));
    }

    public String getUploadToken(String fileName, QiniuUploadTokenType type, boolean transcode) {
        if (transcode) {
            // saveAs 为转码后的音频文件名. 文件名与后续的播放, 下载等逻辑完全脱钩, 文件后缀仅为方便调试而提供的参考:
            // 以 _t.mp3 结尾的文件, 为转码后的 mp3 文件
            // 以 _t.amr 结尾的文件, 为转码后的 amr 文件
            String saveAs = null;
            switch (type) {
                case VOICE_MP3: {
                    int idx = fileName.lastIndexOf(".mp3");
                    String saveAsName = idx > 0 ? fileName.substring(0, idx) + "_t.amr" : fileName + "_t.amr";
                    saveAs = UrlSafeBase64.encodeToString(BUCKET + ":" + saveAsName);
                    break;
                }
                case VOICE_AMR: {
                    int idx = fileName.lastIndexOf(".amr");
                    String saveAsName = idx > 0 ? fileName.substring(0, idx) + "_t.mp3" : fileName + "_t.mp3";
                    saveAs = UrlSafeBase64.encodeToString(BUCKET + ":" + saveAsName);
                    break;
                }
            }
            if (saveAs == null) {
                log.info("transcode required but no file name is given");
                return getFileUploadToken();
            }
            String callback = type.getCallback() == null ? null : "https://" + apiHost + type.getCallback();
            StringMap policy = new StringMap()
                    .put("persistentOps", type.getFopsType().fopsWithSaveAs(saveAs))
                    .put("persistentPipeline", type.getFopsType().getPipeline())
                    .putNotEmpty("persistentNotifyUrl", callback);
            return auth.uploadToken(BUCKET, null, type.getExpire().getSeconds(), policy);
        } else {
            return getFileUploadToken();
        }
    }

    public String getFileUploadToken() {
        String token = stringRedisTemplate.opsForValue().get(FILE_TOKEN_CACHE_KEY);
        if (token == null) {
            token = auth.uploadToken(BUCKET, null, FILE_TOKEN_EXPIRE, null);
            stringRedisTemplate.opsForValue().set(FILE_TOKEN_CACHE_KEY, token, FILE_TOKEN_CACHE_EXPIRE, TimeUnit.SECONDS);
        }
        return token;
    }

    public String getMimeType(String fileName) {
        try {
            BucketManager bucketManager = new BucketManager(auth, config);
            FileInfo fileInfo = bucketManager.stat(BUCKET, fileName);
            return fileInfo.mimeType;
        } catch (QiniuException e) {
            throw new KbException(KbCode.REQUEST_FAIL, "获取文件类型失败");
        }
    }

    public static AudioPipelineParseModel parseAudioPipelineCallback(QiniuPipelineResult result) {
        AudioPipelineParseModel model = new AudioPipelineParseModel();
        if (result != null && result.getCode() != null && result.getCode() == 0
                && result.getItems() != null && result.getItems().size() != 0) {
            QiniuPipelineResult.Item item = result.getItems().get(0);
            if (item.getCmd().startsWith(QiniuFopsType.VOICE_AMR.getFops())) {
                model.ok = true;
                model.key = result.getInputKey();
                model.mp3Key = result.getInputKey();
                model.amrKey = item.getKey();
            } else if (item.getCmd().startsWith(QiniuFopsType.VOICE_MP3.getFops())) {
                model.ok = true;
                model.key = result.getInputKey();
                model.amrKey = result.getInputKey();
                model.mp3Key = item.getKey();
            } else {
                model.ok = false;
            }
        } else {
            model.ok = false;
        }
        return model;
    }

    // ------------------------------------------------------
    // transcode

    public String transcode(String key, String saveAs, String callback, TranscodeInfoModel transcodeInfo) {
        String urlBase64 = UrlSafeBase64.encodeToString(BUCKET + ":" + saveAs);
        String fops = transcodeInfo.fopsWithSaveAs(urlBase64);
        String pipeline = transcodeInfo.getPipeline();
        try {
            return operationManager.pfop(BUCKET, key, fops, pipeline, callback);
        } catch (QiniuException e) {
            log.error("qiniu transcode fail: " + e.response.toString());
            throw new RuntimeException(e);
        }
    }

    public String transcodeMulti(String key, String saveAs, String callback, TranscodeInfoModel... transcodeInfoMulti) {
        if (transcodeInfoMulti == null || transcodeInfoMulti.length == 0) {
            throw new KbException(KbCode.ILLEGAL_ARGUMENT);
        }
        String pipeline = transcodeInfoMulti[0].getPipeline();
        String[] fopsArr = new String[transcodeInfoMulti.length];
        for (int i = 0; i < transcodeInfoMulti.length; i ++) {
            TranscodeInfoModel tt = transcodeInfoMulti[i];
            String urlBase64 = UrlSafeBase64.encodeToString(BUCKET + ":" + saveAs);
            fopsArr[i] = tt.fopsWithSaveAs(urlBase64);
        }
        String fops = String.join(";", fopsArr);
        try {
            return operationManager.pfop(BUCKET, key, fops, pipeline, callback);
        } catch (QiniuException e) {
            log.error("qiniu transcode (multi) fail: " + e.response.toString());
            throw new RuntimeException(e);
        }
    }

    private static final String HTTPS_QINIU_PREFIX = HTTPS_QINIU_HOST + "/";
    private static final String HTTP_QINIU_PREFIX = HTTP_QINIU_HOST + "/";

    public static String getQiniuKey(String urlOrKey) {
        if (urlOrKey == null) {
            return null;
        } else if (urlOrKey.startsWith(HTTPS_QINIU_PREFIX)) {
            return urlOrKey.replace(HTTPS_QINIU_PREFIX, "");
        } else if (urlOrKey.startsWith(HTTP_QINIU_PREFIX)) {
            return urlOrKey.replace(HTTP_QINIU_PREFIX, "");
        } else {
            return urlOrKey;
        }
    }

    /**
     * @param source 原始字符串
     * @param targetSuffix 要替换的目标后缀. 举例: ".mp4"
     * @return 替换后的字符串
     */
    public static String replaceSuffix(String source, String targetSuffix) {
        int idx = source.lastIndexOf(".");
        return idx > 0 ? source.substring(0, idx) + targetSuffix : source + targetSuffix;
    }

}
