package com.kaiba.m.core.middleware.alilogger;

import com.aliyun.openservices.aliyun.log.producer.*;
import com.aliyun.openservices.log.common.LogItem;
import com.kaiba.lib.base.util.ServletRequestUtils;
import com.kaiba.lib.base.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.annotation.PostConstruct;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.*;

/**
 * author: lyux
 * date: 19-8-4
 */
@Slf4j
@Component
public class RequestLogger implements HandlerInterceptor {

    private static final Set<String> IGNORE_URI_SET = new HashSet<>();
    static {
        IGNORE_URI_SET.add("/healthcheck");
    }

    private static final Map<String, String> PARAM_RENAME_MAP = new HashMap<>();
    static {
        PARAM_RENAME_MAP.put("version", "v");
    }

    @Value("${ali.log.access_key_id}")
    private String aliLogAccessKeyId;

    @Value("${ali.log.access_key_secret}")
    private String aliLogAccessKeySecret;

    @Value("${ali.log.endpoint}")
    private String aliLogEndpoint;

    @Value("${kaiba.log.request.ali_log_project:}")
    private String aliLogProject;

    @Value("${kaiba.log.request.ali_log_store:}")
    private String aliLogStore;

    @Value("${kaiba.log.request.ali_log_topic:}")
    private String aliLogTopic;

    private Producer producer;

    @PostConstruct
    private void init() {
        if (StringUtils.isEmpty(aliLogProject) || StringUtils.isEmpty(aliLogStore) || StringUtils.isEmpty(aliLogTopic)) {
            return;
        }
        ProjectConfigs projectConfigs = new ProjectConfigs();
        projectConfigs.put(new ProjectConfig(aliLogProject, aliLogEndpoint, aliLogAccessKeyId, aliLogAccessKeySecret));
        ProducerConfig producerConfig = new ProducerConfig(projectConfigs);
        producerConfig.setBatchSizeThresholdInBytes(3 * 1024 * 1024);
        producerConfig.setBatchCountThreshold(40960);
        producerConfig.setIoThreadCount(8);
        producerConfig.setTotalSizeInBytes(104857600);
        producer = new LogProducer(producerConfig);
    }

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) {
        if (null == producer) {
            return true;
        }
        if (IGNORE_URI_SET.contains(request.getRequestURI())) {
            return true;
        }
        try {
            LogItem logItem = new LogItem();
            logItem.PushBack("r_uri", request.getRequestURI());
            logItem.PushBack("r_method", request.getMethod());
            logItem.PushBack("r_addr", ServletRequestUtils.getRequestIp(request));
            logItem.PushBack("r_protocol", request.getProtocol());
            request.getParameterMap().forEach((k, v) -> {
                String value = v.length == 1 ? v[0] : Arrays.toString(v);
                String rename = PARAM_RENAME_MAP.get(k);
                if (null == rename) {
                    logItem.PushBack(k, value);
                } else {
                    logItem.PushBack(rename, value);
                }
            });
            producer.send(aliLogProject, aliLogStore, aliLogTopic, request.getLocalAddr(), logItem);
        } catch (Exception e) {
            log.error("send request log through ali LogProducer fail", e);
        }
        return true;
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) {

    }

}
