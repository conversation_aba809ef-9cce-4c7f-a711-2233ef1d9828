package com.kaiba.m.core.middleware.instantcache;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.Collections;
import java.util.List;

/**
 * author: lyux
 * date: 2020-08-07
 */
@Data
@ToString
@NoArgsConstructor
class KbInstantCaffeineExpireMessage {

    private String cacheMarker;

    private List<Object> cacheKeys;

    private Boolean expireAll;

    boolean isValid() {
        if (cacheMarker == null || cacheMarker.length() == 0) {
            return false;
        }
        if (expireAll == null) {
            return false;
        }
        if (cacheKeys != null && cacheKeys.size() != 0 && expireAll) {
            return false;
        }
        return true;
    }

    static KbInstantCaffeineExpireMessage asExpireOne(String cacheMarker, Object cacheKey) {
        KbInstantCaffeineExpireMessage message = new KbInstantCaffeineExpireMessage();
        message.setCacheMarker(cacheMarker);
        message.setCacheKeys(Collections.singletonList(cacheKey));
        message.setExpireAll(false);
        return message;
    }

    static KbInstantCaffeineExpireMessage asExpireMany(String cacheMarker, List<Object> cacheKeys) {
        KbInstantCaffeineExpireMessage message = new KbInstantCaffeineExpireMessage();
        message.setCacheMarker(cacheMarker);
        message.setCacheKeys(cacheKeys);
        message.setExpireAll(false);
        return message;
    }

    static KbInstantCaffeineExpireMessage asExpireAll(String cacheMarker) {
        KbInstantCaffeineExpireMessage message = new KbInstantCaffeineExpireMessage();
        message.setCacheMarker(cacheMarker);
        message.setExpireAll(true);
        return message;
    }

}
