package com.kaiba.m.core.middleware.mongo.support;

import com.kaiba.m.core.middleware.mongo.interfaces.IKbMongo;
import org.springframework.data.mongodb.core.MongoOperations;
import org.springframework.data.mongodb.repository.query.MongoEntityInformation;
import org.springframework.data.mongodb.repository.support.SimpleMongoRepository;

/**
 * author: lyux
 * date: 18-9-7
 */
public class KbMongoRepositorySupport<T, ID> extends SimpleMongoRepository<T, ID> implements IKbMongo {

    private final MongoOperations mongo;

    public KbMongoRepositorySupport(MongoEntityInformation<T, ID> metadata, MongoOperations mongoOperations) {
        super(metadata, mongoOperations);
        this.mongo = mongoOperations;
    }

    @Override
    public MongoOperations mongo() {
        return mongo;
    }
}
