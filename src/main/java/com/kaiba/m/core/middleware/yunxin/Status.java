package com.kaiba.m.core.middleware.yunxin;

import lombok.Data;
import lombok.ToString;

@Data
@ToString
public class Status {
    private Long ctime;

    private String cid;

    private String name;

    /**
     * {@link com.kaiba.m.core.middleware.yunxin.constant.YXStatus}
     */
    private int status;

    private int type;

    private int uid;

    private int needRecord;

    private int format;

    private int duration;

    private String filename;
}
