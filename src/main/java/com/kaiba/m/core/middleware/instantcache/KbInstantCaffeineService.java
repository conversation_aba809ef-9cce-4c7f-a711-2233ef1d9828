package com.kaiba.m.core.middleware.instantcache;

import com.github.benmanes.caffeine.cache.LoadingCache;
import com.kaiba.lib.base.response.KbCode;
import com.kaiba.lib.base.response.KbException;
import com.kaiba.m.core.util.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.listener.PatternTopic;
import org.springframework.data.redis.listener.RedisMessageListenerContainer;
import org.springframework.data.redis.listener.adapter.MessageListenerAdapter;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * author: lyux
 * date: 2020-08-07
 *
 * 利用 redis 的 pubsub 功能, 实现进程缓存 caffeine 在各个服务节点间同时驱逐某个缓存的 key, 以达到缓存实时更新的目的.
 */
@Slf4j
@Component
public class KbInstantCaffeineService {

    private static final String REDIS_PUB_SUB_CHANNEL = "instant_cache";

    private final StringRedisTemplate stringRedisTemplate;
    private final Map<String, KbInstantCaffeineLoadingCache<?, ?>> map = new HashMap<>();

    public KbInstantCaffeineService(StringRedisTemplate stringRedisTemplate) {
        this.stringRedisTemplate = stringRedisTemplate;
    }

    @Bean
    public RedisMessageListenerContainer container(
            RedisConnectionFactory connectionFactory, MessageListenerAdapter instantCacheMessageListenerAdapter) {
        RedisMessageListenerContainer container = new RedisMessageListenerContainer();
        container.setConnectionFactory(connectionFactory);
        container.addMessageListener(instantCacheMessageListenerAdapter, new PatternTopic(REDIS_PUB_SUB_CHANNEL));
        return container;
    }

    @Bean
    public MessageListenerAdapter instantCacheMessageListenerAdapter() {
        return new MessageListenerAdapter(this, "receiveExpireMessage");
    }

    public <K, V> LoadingCache<K, V> registerInstantCache(String marker, LoadingCache<K, V> loadingCache) {
        KbInstantCaffeineLoadingCache<K, V> wrappedCache =
                new KbInstantCaffeineLoadingCache<>(this, loadingCache, marker);
        if (map.put(marker, wrappedCache) != null) {
            throw new KbException(KbCode.ILLEGAL_ARGUMENT, "instant cache marker already exists: " + marker);
        }
        return wrappedCache;
    }

    void notifyCacheExpireOne(String cacheMarker, Object cacheKey) {
        KbInstantCaffeineExpireMessage message = KbInstantCaffeineExpireMessage.asExpireOne(cacheMarker, cacheKey);
        String msg = JsonUtils.getGson().toJson(message);
        stringRedisTemplate.convertAndSend(REDIS_PUB_SUB_CHANNEL, msg);
    }

    void notifyCacheExpireMany(String cacheMarker, List<Object> cacheKeys) {
        KbInstantCaffeineExpireMessage message = KbInstantCaffeineExpireMessage.asExpireMany(cacheMarker, cacheKeys);
        String msg = JsonUtils.getGson().toJson(message);
        stringRedisTemplate.convertAndSend(REDIS_PUB_SUB_CHANNEL, msg);
    }

    void notifyCacheExpireAll(String cacheMarker) {
        KbInstantCaffeineExpireMessage message = KbInstantCaffeineExpireMessage.asExpireAll(cacheMarker);
        String msg = JsonUtils.getGson().toJson(message);
        stringRedisTemplate.convertAndSend(REDIS_PUB_SUB_CHANNEL, msg);
    }

    public void receiveExpireMessage(String msg) {
        log.info("instant cache service, received expire message: " + msg);
        KbInstantCaffeineExpireMessage message = JsonUtils.toModelIgnoreError(msg, KbInstantCaffeineExpireMessage.class);
        if (message == null || !message.isValid()) {
            return;
        }
        KbInstantCaffeineLoadingCache<?, ?> loadingCache = map.get(message.getCacheMarker());
        if (loadingCache != null) {
            if (message.getExpireAll()) {
                loadingCache.doInvalidateAll();
            } else {
                if (message.getCacheKeys().size() > 1) {
                    loadingCache.doInvalidateMany(message.getCacheKeys());
                } else {
                    loadingCache.doInvalidateOne(message.getCacheKeys().get(0));
                }
            }
        }
    }

}
