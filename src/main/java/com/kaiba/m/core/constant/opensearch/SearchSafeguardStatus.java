package com.kaiba.m.core.constant.opensearch;

import com.kaiba.lib.base.constant.IKbEnumValueGetter;

/**
 * <AUTHOR>
 * @date 2022/08/26 11:06
 **/
public enum SearchSafeguardStatus implements IKbEnumValueGetter {
    /** 维权状态 */
    PEDDING(0, "待审核"),
    UN_APPROVE(1, "未通过"),
    APPROVE(2, "已审核"),
    PLATFORM_EXPOSURE(3, "平台曝光"),
    PROGRAM_EXPOSURE(4, "节目曝光"),
    CANCEL(5, "撤诉"),
    COERCEEND(6, "强制结案");

    private final int value;
    private final String description;

    SearchSafeguardStatus(int value, String description) {
        this.value = value;
        this.description = description;
    }

    @Override
    public int getValue() {
        return value;
    }

    @Override
    public String getDescription() {
        return description;
    }
}
