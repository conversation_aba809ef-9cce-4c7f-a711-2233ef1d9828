package com.kaiba.m.core.constant.app.cyx;

import lombok.Getter;

import java.util.Optional;

/**
 * <AUTHOR>
 * @version CYXArticleGroupType, v0.1 2025/6/30 11:57 daopei Exp $
 **/
@Getter
public enum CYXArticleGroupType {
    HOME("首页"),
    THREAD("主题"),
    THEATRE("剧场"),
    ;

    private final String description;

    CYXArticleGroupType(String description) {
        this.description = description;
    }

    public static Optional<CYXArticleGroupType> resolveByName(String name) {
        if (name == null) {
            return Optional.empty();
        }
        for (CYXArticleGroupType value : values()) {
            if (value.name().equals(name)) {
                return Optional.of(value);
            }
        }
        return Optional.empty();
    }
}
