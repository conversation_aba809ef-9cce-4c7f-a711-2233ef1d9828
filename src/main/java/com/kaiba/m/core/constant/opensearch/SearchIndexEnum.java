package com.kaiba.m.core.constant.opensearch;

import com.kaiba.lib.base.constant.IKbEnumValueGetter;
import lombok.Getter;

import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2022/08/26 11:06
 **/
@Getter
public enum SearchIndexEnum implements IKbEnumValueGetter {
    /** 索引 */
    DEFAULT(1,"default:", "默认索引"),
    DEFAULT_HIDDEN(2, "idx_default_all:", "带隐含搜索字段索引"),
    TIME(3, "idx_time:", "搜索时间范围"),
    USER(4, "idx_user:", "搜索用户"),
    ARRAY(5, "idx_array:", "搜索扩展数组"),
    ID(6, "id:", "搜索id"),
    ;

    private final int value;
    private final String index;
    private final String description;

    SearchIndexEnum(int value, String index, String description) {
        this.value = value;
        this.index = index;
        this.description = description;
    }

    @Override
    public int getValue() {
        return value;
    }

    @Override
    public String getDescription() {
        return description;
    }

    public static Optional<SearchIndexEnum> valueOf(Integer value) {
        if (null == value) {
            return Optional.empty();
        }
        for (SearchIndexEnum v : values()) {
            if (v.getValue() == value) {
                return Optional.of(v);
            }
        }
        return Optional.empty();
    }
}
