package com.kaiba.m.core.constant.opensearch;

import com.kaiba.lib.base.constant.IKbEnumValueGetter;

import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2022/08/26 11:06
 **/
public enum SearchRankEnum implements IKbEnumValueGetter {
    /** 排序规则 */
    DEFAULT(1,"default", "默认排序"),
    TEXT_RELEVANCE(2, "strategy_text_relevance", "文本相关度"),
    TIME_FIRST(3, "strategy_time_first", "时间优先"),
    TEXT_TIME(4, "strategy_text_time", "文本时间综合排序"),
    ;

    private final int value;
    private final String rule;
    private final String description;

    SearchRankEnum(int value, String rule, String description) {
        this.value = value;
        this.rule = rule;
        this.description = description;
    }

    @Override
    public int getValue() {
        return value;
    }

    public String getRule() {
        return rule;
    }

    @Override
    public String getDescription() {
        return description;
    }

    public static Optional<SearchRankEnum> valueOf(Integer value) {
        if (null == value) {
            return Optional.empty();
        }
        for (SearchRankEnum v : values()) {
            if (v.getValue() == value) {
                return Optional.of(v);
            }
        }
        return Optional.empty();
    }
}
