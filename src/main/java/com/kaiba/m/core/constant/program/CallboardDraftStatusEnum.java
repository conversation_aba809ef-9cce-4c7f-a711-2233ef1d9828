package com.kaiba.m.core.constant.program;

import com.kaiba.lib.base.constant.IKbEnumValueGetter;
import java.util.Optional;

/**
 * author: duanyf
 * date: 2024-07-18
 **/
public enum CallboardDraftStatusEnum implements IKbEnumValueGetter {
    NORMAL(1, "正常的"),
    DELETE(2, "已删除"),
    ;

    private int value;
    private String description;

    CallboardDraftStatusEnum(int value, String description) {
        this.value = value;
        this.description = description;
    }

    @Override
    public int getValue() {
        return this.value;
    }

    @Override
    public String getDescription() {
        return description;
    }

    public static Optional<CallboardDraftStatusEnum> valueOf(Integer value) {
        if (null == value) {
            return Optional.empty();
        }
        for (CallboardDraftStatusEnum v : values()) {
            if (v.getValue() == value) {
                return Optional.of(v);
            }
        }
        return Optional.empty();
    }
}
