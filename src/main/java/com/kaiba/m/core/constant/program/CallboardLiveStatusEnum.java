package com.kaiba.m.core.constant.program;

import com.kaiba.lib.base.constant.IKbEnumValueGetter;
import java.util.Optional;

/**
 * author: duanyf
 * date: 2024-07-18
 **/
public enum CallboardLiveStatusEnum implements IKbEnumValueGetter {
    UNPLAY(0, "未播报"),
    PLAYED(1, "已播报"),
    DELETE(2, "已删除"),
    ;

    private int value;
    private String description;

    CallboardLiveStatusEnum(int value, String description) {
        this.value = value;
        this.description = description;
    }

    @Override
    public int getValue() {
        return this.value;
    }

    @Override
    public String getDescription() {
        return description;
    }

    public static Optional<CallboardLiveStatusEnum> valueOf(Integer value) {
        if (null == value) {
            return Optional.empty();
        }
        for (CallboardLiveStatusEnum v : values()) {
            if (v.getValue() == value) {
                return Optional.of(v);
            }
        }
        return Optional.empty();
    }
}
