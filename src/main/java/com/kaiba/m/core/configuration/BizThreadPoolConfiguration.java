package com.kaiba.m.core.configuration;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.Executor;

/**
 * <AUTHOR>
 * @version BizThreadPoolConfiguration, v0.1 2025/1/8 11:41 daopei Exp $
 **/
@Slf4j
@Configuration
public class BizThreadPoolConfiguration {


    @Bean("mediaAssetExecutor")
    public Executor mediaAssetExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(4);
        executor.setMaxPoolSize(20);
        executor.setQueueCapacity(100);
        executor.setThreadNamePrefix("mediaAsset-");
        executor.setRejectedExecutionHandler((r, executor1) -> log.warn("mediaAsset-rejected"));
        return executor;
    }
}
