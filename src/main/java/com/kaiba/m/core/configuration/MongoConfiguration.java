package com.kaiba.m.core.configuration;

import com.kaiba.m.core.middleware.mongo.support.KbMongoRepositorySupport;
import com.kaiba.m.core.repository.RepositoryPackageMarker;
import com.mongodb.client.MongoClient;
import com.mongodb.client.MongoClients;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.mongodb.MongoDatabaseFactory;
import org.springframework.data.mongodb.config.AbstractMongoClientConfiguration;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.convert.DefaultMongoTypeMapper;
import org.springframework.data.mongodb.core.convert.MappingMongoConverter;
import org.springframework.data.mongodb.repository.config.EnableMongoRepositories;
import org.springframework.lang.NonNull;

/**
 * author: lyux
 * date: 18-7-16
 */
@Configuration
@EnableMongoRepositories(
        basePackageClasses = {
                RepositoryPackageMarker.class,
                com.kaiba.m.fuse.repository.RepositoryPackageMarker.class
        },
        repositoryBaseClass = KbMongoRepositorySupport.class)
public class MongoConfiguration extends AbstractMongoClientConfiguration {

    @Value("${kb.mongo.uri}")
    private String mongoUri;

    @Value("${kb.mongo.main-db}")
    private String mongoMainDb;

    public MongoConfiguration() {
    }

    @NonNull
    @Override
    protected String getDatabaseName() {
        return mongoMainDb;
    }

    @Bean
    @NonNull
    @Override
    public MongoClient mongoClient() {
        return MongoClients.create(mongoUri);
    }

    @Bean
    @NonNull
    @Override
    public MongoTemplate mongoTemplate(MongoDatabaseFactory mongoDbFactory, MappingMongoConverter converter) {
        converter.setTypeMapper(new DefaultMongoTypeMapper(null));
        return super.mongoTemplate(mongoDbFactory, converter);
    }

}
