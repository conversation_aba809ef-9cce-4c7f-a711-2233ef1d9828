package com.kaiba.m.core.configuration;

import com.kaiba.lib.base.annotation.data.KbRefreshMongoIndex;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.event.EventListener;
import org.springframework.data.mapping.context.MappingContext;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.convert.MongoConverter;
import org.springframework.data.mongodb.core.index.IndexOperations;
import org.springframework.data.mongodb.core.index.MongoPersistentEntityIndexResolver;
import org.springframework.data.mongodb.core.mapping.BasicMongoPersistentEntity;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.MongoMappingContext;

import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeParseException;

/**
 * author: lyux
 * date: 20-03-31
 */
@Slf4j
@Configuration
public class MongoIndexConfiguration {

    private final MongoTemplate mongoTemplate;
    private final MongoConverter mongoConverter;

    public MongoIndexConfiguration(MongoTemplate mongoTemplate, MongoConverter mongoConverter) {
        this.mongoTemplate = mongoTemplate;
        this.mongoConverter = mongoConverter;
    }

    @EventListener(ApplicationReadyEvent.class)
    public void initIndicesAfterStartup() {
        log.info("Mongo InitIndicesAfterStartup init");
        long init = System.currentTimeMillis();
        MappingContext<?, ?> mappingContext = this.mongoConverter.getMappingContext();
        if (mappingContext instanceof MongoMappingContext) {
            MongoMappingContext mongoMappingContext = (MongoMappingContext) mappingContext;
            for (BasicMongoPersistentEntity<?> persistentEntity : mongoMappingContext.getPersistentEntities()) {
                Class<?> clazz = persistentEntity.getType();
                if (clazz.isAnnotationPresent(Document.class)) {
                    boolean ensureIndex = false;
                    KbRefreshMongoIndex rf = clazz.getAnnotation(KbRefreshMongoIndex.class);
                    if (rf != null && !rf.value().equals(KbRefreshMongoIndex.DEFAULT_UNTIL)) {
                        try {
                            long until = LocalDate.parse(rf.value())
                                    .atStartOfDay(ZoneId.systemDefault())
                                    .toInstant().toEpochMilli();
                            ensureIndex = System.currentTimeMillis() < until;
                        } catch (DateTimeParseException e) {
                            log.warn("Mongo InitIndicesAfterStartup, entity annotation date malformed: " + rf.value());
                        }
                    }

                    if (ensureIndex) {
                        log.info("Mongo InitIndicesAfterStartup, about to ensure index for: " + clazz.getName());
                        IndexOperations indexOps = mongoTemplate.indexOps(clazz);
                        MongoPersistentEntityIndexResolver resolver = new MongoPersistentEntityIndexResolver(mongoMappingContext);
                        resolver.resolveIndexFor(clazz).forEach(indexOps::ensureIndex);
                    } else {
                        log.info("Mongo InitIndicesAfterStartup, skip ensure index for: " + clazz.getName());
                    }
                }
            }
        }
        log.info("Mongo InitIndicesAfterStartup take: {}", (System.currentTimeMillis() - init));
    }

}
