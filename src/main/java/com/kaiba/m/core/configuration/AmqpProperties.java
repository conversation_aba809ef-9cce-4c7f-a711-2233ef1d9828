package com.kaiba.m.core.configuration;

import lombok.Data;
import lombok.ToString;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * author: lyux
 * date: 2020-04-07
 */
@Data
@ToString
@Component
@ConfigurationProperties(prefix = "kb.rabbitmq")
public class AmqpProperties {

    // -----------------------------------------------------------------------------

    private String instanceId;

    private String host;

    private int port;

    private String vhost;

    private String username;

    private String password;

}
