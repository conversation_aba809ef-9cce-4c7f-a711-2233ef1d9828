package com.kaiba.m.core.configuration.amqp;

import com.google.gson.JsonSyntaxException;
import com.kaiba.lib.base.response.KbException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.listener.ConditionalRejectingErrorHandler;

/**
 * author: lyux
 * date: 19-3-8
 */
@Slf4j
public class KbDefaultAmqpExceptionStrategy extends ConditionalRejectingErrorHandler.DefaultExceptionStrategy {

    @Override
    protected boolean isUserCauseFatal(Throwable cause) {
        if (cause instanceof JsonSyntaxException
                || cause instanceof KbException) {
            log.error("receive amqp message fail", cause);
            return true;
        }
        return false;
    }
}
