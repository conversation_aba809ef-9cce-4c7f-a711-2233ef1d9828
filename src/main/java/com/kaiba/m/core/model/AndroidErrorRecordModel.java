package com.kaiba.m.core.model;

import com.kaiba.lib.base.domain.user.UserModel;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Created by shenxl on 2021/12/16
 */
@Data
@NoArgsConstructor
public class AndroidErrorRecordModel {

    /** 异常上报时间. 时间戳, 单位毫秒 */
    private Long reportTime;

    /** 异常发生时间. 时间戳, 单位毫秒 */
    private Long happenTime;

    /** 异常上报时间(yyyy-MM-dd HH:mm:ss) */
    private String reportTimeStr;

    /** 异常发生时间(yyyy-MM-dd HH:mm:ss) */
    private String happenTimeStr;

    /** app最大内存，单位byte */
    private Long maxAppMemory;

    /** app可用内存，单位byte */
    private Long freeAppMemory;

    /** 系统总内存，单位byte */
    private Long totalSystemMemory;

    /** 系统可用内存，单位byte */
    private Long freeSystemMemory;

    /** 系统总存储空间，单位byte */
    private Long totalStorage;

    /** 系统可用存储空间，单位byte */
    private Long freeStorage;

    /** app最大内存 */
    private String maxAppMemoryStr;

    /** app可用内存 */
    private String freeAppMemoryStr;

    /** 系统总内存 */
    private String totalSystemMemoryStr;

    /** 系统可用内存 */
    private String freeSystemMemoryStr;

    /** 系统总存储空间 */
    private String totalStorageStr;

    /** 系统可用存储空间 */
    private String freeStorageStr;

    /** 异常类型 */
    private String type;

    /** 异常信息 */
    private String message;

    /** 堆栈 */
    private String trace;

    /** 报错时所处的activity */
    private String currentActivity;

    /** 是否是测试包 */
    private Boolean isDebug;

    /** app版本号 */
    private Integer vc;

    /** 操作系统版本号 */
    private String dv;

    /** 手机品牌型号 */
    private String db;

    /** 端类型 */
    private String ep;

    /** 设备类型 */
    private String cid;

    private UserModel user;
}