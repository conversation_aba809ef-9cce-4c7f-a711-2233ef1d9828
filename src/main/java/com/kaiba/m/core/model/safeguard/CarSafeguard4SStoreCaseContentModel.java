package com.kaiba.m.core.model.safeguard;

import com.kaiba.lib.base.domain.common.Image;
import com.kaiba.lib.base.domain.common.Video;
import com.kaiba.lib.base.domain.workorder.WOACLStringData;
import com.kaiba.m.core.service.workorder.carsafeguard.CarSafeguardBizType;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <AUTHOR>
 * @version CarSafeguard4SStoreContentCaseModel, v0.1 2024/7/22 16:02 daopei Exp $
 **/
@EqualsAndHashCode(callSuper = true)
@Data
public class CarSafeguard4SStoreCaseContentModel extends CarSafeguardCaseContentModel {

    private String id;
    /**
     * 发布者 id. 注意: 发布者未必就是案件提问者. 在后台由管理员录入的问题, 发布者即管理员.
     */
    private Integer userId;
    /**
     * 内容类型{@link CarSafeguardBizType}
     */
    private String contentType;
    /**
     * 问题描述:维权问题的文字内容描述
     */
    private String content;
    /**
     * 内容: 图片列表
     */
    private List<Image> images;
    /**
     * 内容: 视频
     */
    private Video video;
    /**
     * 内容: 用户个人信息列表
     */
    private List<WOACLStringData> personal;
    /**
     * 初始投诉对象. 系统中暂无录入的商家.
     * 该属性在未指定投诉商家的时候支持填入一个商家名称的文本.
     */
    private String originResolverLack;
    /**
     * 更新时间, 单位毫秒.
     */
    private Long updateTime;
    /**
     * 创建时间, 单位毫秒.
     */
    private Long createTime;
    /**
     * 地区编码:杭州330100
     */
    private String source;
    /**
     * 车辆是否挂牌,1:挂牌 0:未挂牌
     */
    private Integer carRegistration;
    /**
     * 车辆车牌号:如果车辆已挂牌该属性有值
     */
    private String carRegistrationNo;
    /**
     * 用户名称
     */
    private String personalName;
    /**
     * 用户手机号
     */
    private String personalMobile;
    /**
     * 车型品牌
     */
    private String carBrand;
    /**
     * 车型系列
     */
    private String carSeries;
    /**
     * 购车时间
     */
    private Long carBuyTime;
    /**
     * 出现问题时间
     */
    private String carHappenTime;
    /**
     * 行驶里程数
     */
    private String carMileage;


    @Override
    public String obtainContentId() {
        return id;
    }

    @Override
    public String obtainContentText() {
        return content;
    }
}
