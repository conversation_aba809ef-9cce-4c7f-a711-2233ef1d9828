package com.kaiba.m.core.model.app;

import com.kaiba.lib.base.constant.apphome.AppHomeTabType;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.List;

/**
 * author: lyux
 * date: 2023-10-09
 */
@Data
@ToString
@NoArgsConstructor
public class AppTabBubbleParam {

    /** 电台 id. */
    private Integer siteId;

    /** 用户 ID. 非必传. */
    private Integer userId;

    /** 上次点击记录. */
    private List<ClearRecord> clearRecords;


    @Data
    @ToString
    @NoArgsConstructor
    public static class ClearRecord {

        /** 气泡 ID */
        private String id;

        /** {@link AppHomeTabType} */
        private String tab;

        /** 上次点击时间, 单位毫秒 */
        private Long clearTime;
    }

}
