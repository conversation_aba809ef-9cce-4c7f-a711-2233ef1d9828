package com.kaiba.m.core.model.artmap;

import com.google.common.collect.Lists;
import com.kaiba.lib.base.domain.artmap.PerformanceModel;
import java.util.List;
import lombok.Data;

/**
 * Description: 日历化的演艺演出VO
 * Author: ZM227
 * Date: 2025/6/13 10:52
 */
@Data
public class CalenderPerformanceModel {

    /**
     * 演出时间
     * 当日0点毫秒数
     */
    private Long playingTime;

    /**
     * 演出列表
     */
    private List<PerformanceModel> performances = Lists.newArrayList();

}
