package com.kaiba.m.core.model.artmap;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * Description: 艺术地图每日推荐VO
 * Author: ZM227
 * Date: 2025/6/6 15:38
 */
@Data
@ToString
@NoArgsConstructor
public class RecommendModel {

    /**
     * 推荐编码
     */
    private String recommendCode;

    /**
     * 推荐标题
     */
    private String title;

    /**
     * 推荐开始时间
     */
    private Long startTime;

    /**
     * 推荐状态 0-下架 1-上架
     */
    private Integer status;

    /**
     * 推荐结束时间
     */
    private Long endTime;

    /**
     * 推荐分组编码
     */
    private String groupKey;

    /**
     * 分组名称
     */
    private String groupName;

    /**
     * 创建时间
     */
    private Long createTime;

    /**
     * 更新时间
     */
    private Long updateTime;

}
