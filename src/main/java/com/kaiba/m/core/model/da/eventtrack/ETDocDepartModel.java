package com.kaiba.m.core.model.da.eventtrack;

import lombok.Data;

/**
 * <AUTHOR>
 * @version ETDocDepartModel, v0.1 2025/4/17 10:47 daopei Exp $
 **/
@Data
public class ETDocDepartModel {

    private String id;

    /** 部门. 部门是单位的下属组织. */
    private String depart;

    /** 部门名称. */
    private String name;

    /** 所属单位. */
    private String channel;

    /** 创建时间 */
    private Long createTime;

    /** 修改时间 */
    private Long updateTime;
}
