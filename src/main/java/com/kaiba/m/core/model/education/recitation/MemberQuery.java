package com.kaiba.m.core.model.education.recitation;

import java.util.List;
import lombok.Data;

/**
 * Description: 成员查询条件
 * Author: ZM227
 * Date: 2024/8/5 10:14
 */
@Data
public class MemberQuery {

    /**
     * 成员code
     */
    private String memberCode;

    /**
     * 成员姓名
     */
    private String name;

    /**
     * 联系电话
     */
    private String connectPhone;

    /**
     * 年龄类型:1-少儿,2-成人
     */
    private List<Integer> ageType;

    /**
     * 成员类型:0-非朗诵团员,1-朗诵团员
     */
    private List<Integer> memberType;

    /**
     * 年龄类型:1-少儿,2-成人
     * 排除
     */
    private List<Integer> exceptAgeType;

    /**
     * 成员类型:0-非朗诵团员,1-朗诵团员
     * 排除
     */
    private List<Integer> exceptMemberType;

    /**
     * 分页
     */
    private Integer page;

    /**
     * 页包含
     */
    private Integer pageSize;
}
