package com.kaiba.m.core.model.safeguard.request;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;


/**
 * <AUTHOR>
 * @version CS4SStoreCaseCreateModel, v0.1 2024/7/12 16:50 daopei Exp $
 **/
@EqualsAndHashCode(callSuper = true)
@Data
@ToString
@NoArgsConstructor
public class CS4SStoreCaseCreateModel extends CarSafeguardCaseCreateModel {


    /**
     * 车系
     */
    private String carBrand;
    /**
     * 车型
     */
    private String carSeries;
    /**
     * 车辆是否挂牌,1:挂牌 0:未挂牌
     */
    private Integer carRegistration;
    /**
     * 车辆车牌号:如果车辆已挂牌该属性有值
     */
    private String carRegistrationNo;
    /**
     * 购车时间
     */
    private Long carBuyTime;
    /**
     * 出现问题时间
     */
    private String carHappenTime;
    /**
     * 行驶里程数
     */
    private String carMileage;
}
