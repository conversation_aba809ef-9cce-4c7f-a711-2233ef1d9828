package com.kaiba.m.core.model.education.recitation.dto;

import com.kaiba.m.core.domain.education.recitation.enums.AgeTypeEnum;
import com.kaiba.m.core.domain.education.recitation.enums.MemberTypeEnum;
import java.util.List;
import lombok.Data;
import org.springframework.data.domain.Pageable;

/**
 * Description: 成员查询DTO
 * Author: ZM227
 * Date: 2024/8/5 10:18
 */
@Data
public class MemberQueryDTO {

    /**
     * 成员code
     */
    private String memberCode;

    /**
     * 成员姓名
     */
    private String name;

    /**
     * 联系电话
     */
    private String connectPhone;

    /**
     * 年龄类型:1-少儿,2-成人
     */
    private List<AgeTypeEnum> ageType;

    /**
     * 成员类型:0-非朗诵团员,1-朗诵团员
     */
    private List<MemberTypeEnum> memberType;

    /**
     * 年龄类型:1-少儿,2-成人
     * 排除
     */
    private List<AgeTypeEnum> exceptAgeType;

    /**
     * 成员类型:0-非朗诵团员,1-朗诵团员
     * 排除
     */
    private List<MemberTypeEnum> exceptMemberType;

    /**
     * 分页信息
     */
    private Pageable pageable;

}
