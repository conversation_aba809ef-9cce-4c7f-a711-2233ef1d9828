package com.kaiba.m.core.model.appcyx;

import com.kaiba.lib.base.domain.common.ActionLink;
import com.kaiba.m.core.constant.app.cyx.CYXArticleGroupType;
import lombok.Data;
import org.springframework.data.mongodb.core.index.Indexed;

/**
 * 超有戏-文章分组模型
 * <AUTHOR>
 * @version YHJArticleGroupModel, v0.1 2025/6/23 10:24 daopei Exp $
 **/
@Data
public class CYXArticleGroupModel {

    private String id;

    /** 资讯分组KEY */
    private String groupKey;

    /** 分组类型。 {@link CYXArticleGroupType}*/
    private String groupType;

    /** 分组标题 */
    private String title;

    /** 状态. SHOW/HIDE {@link CYXArticleGroupState} */
    private String state;

    /** 排序依据, 查询时以此字段降序排列. */
    @Indexed
    private Long seq;

    /**
     * 指定的排序依据, 降序, 最大值 999.
     * 最终会根据算法生成 {@link #seq}. 因此本字段仅做记录, 实际排序应使用 {@link #seq} 字段
     */
    private Long idx;

    /**
     * 排序风格.
     * 目前客户端可支持：
     * WATERFALL 瀑布流
     * HORIZONTAL 横向滑动
     *  */
    private String style;

    /** 显示更多跳转动作配置 */
    private ActionLink showMore;
}
