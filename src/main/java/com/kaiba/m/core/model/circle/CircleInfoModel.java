package com.kaiba.m.core.model.circle;

import com.kaiba.lib.base.domain.note.NoteThreadModel;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.List;

/**
 * author: lyux
 * date: 19-5-30
 */
@Data
@ToString
@NoArgsConstructor
public class CircleInfoModel {

    private String searchHint;

    private List<NoteThreadModel> threadList;

    public CircleInfoModel(String searchHint, List<NoteThreadModel> threadList) {
        this.searchHint = searchHint;
        this.threadList = threadList;
    }
}
