package com.kaiba.m.core.model.roadcondition;

import com.kaiba.lib.base.domain.common.Audio;
import com.kaiba.lib.base.domain.common.Image;
import com.kaiba.lib.base.domain.common.Video;
import com.kaiba.lib.base.domain.note.NoteModel;
import com.kaiba.lib.base.domain.user.UserModel;

import java.util.List;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * <AUTHOR>
 * @date 2024/05/21 17:49
 **/
@Data
@ToString
@NoArgsConstructor
public class RoadConditionModel {

    private String id;

    /** 电台 id */
    private Integer siteId;

    /** 地区编码 */
    private Integer source;

    /** 用户 id */
    private Integer userId;

    /** 经度 */
    private Double lng;

    /** 纬度 */
    private Double lat;

    /** 文字内容 */
    private String content;

    /** 地址,此地址为地图SDK逆地理信息得到的第一条街道拼接数据 */
    private String location_name;

    /** 关联帖子 id */
    private String postId;

    /** 地址,此地址为用户自主选择的地址 */
    private String street;

    /** 剩余时间 */
    private String remainTime;

    /** 图片列表 */
    private List<Image> pic;

    /** 音频 */
    private Audio audio;

    /** 视频 */
    private Video video;

    /** 创建时间 s */
    private Long createtime;

    // -------------------------------------------

    /** 帖子回复总量 */
    private Integer count;
    /** 图片数量 */
    private Integer picCount;
    /** 视频数量 */
    private Integer videoCount;
    /** 是否有多媒体信息 0否 1是 */
    private Integer mediaCount;
    /** 帖子点赞总量 */
    private Integer praise_count;
    /** 是否对该帖子点过赞 0否 1是 */
    private Integer is_praise;
    /** 用户信息 */
    private UserModel userInfo;
    /** 帖子信息 */
    private NoteModel noteInfo;
}
