package com.kaiba.m.core.model.safeguard;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.List;

/**
 * 汽车维权首页数据
 * <AUTHOR>
 * @version CSMainPageModel, v0.1 2024/8/20 14:23 daopei Exp $
 **/
@Data
@ToString
@NoArgsConstructor
public class CSMainPageModel {

    /** 开吧维权上报 */
    private Long totalCase;

    /** 开吧成功维权 */
    private Long closedCase;

    /** 已有围观数 */
    private Long followCase;

    /** 最近的用户满分好评列表 */
    private List<CSUserRatingModel> topRatingList;
}
