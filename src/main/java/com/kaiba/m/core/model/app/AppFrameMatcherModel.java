package com.kaiba.m.core.model.app;

import com.kaiba.lib.base.constant.applego.AppComponentState;
import com.kaiba.m.core.domain.apphome.AppHomeFrame;
import com.kaiba.m.core.domain.applego.LegoPage;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.Set;

/**
 * author: lyux
 * date: 2024-09-30
 *
 * 组件化和首页框架分流配置. 条目一旦创建不可变更.
 * 各匹配条件之间取 and 逻辑.
 */
@Data
@ToString
@NoArgsConstructor
public class AppFrameMatcherModel {

    /** 物理主键 */
    private String id;

    /** 后台展示的备注 */
    private String remark;

    /** 状态 {@link AppComponentState} */
    private Integer state;

    /** 匹配条件: 电台 id. 必填. */
    private Integer siteId;

    /** 匹配条件: 最小版本号(包含), 如 70001. 必填. */
    private Integer vcMin;

    /** 匹配条件: 最大版本号(包含), 如 70301. 必填. */
    private Integer vcMax;

    /** 匹配条件: 用户 id. */
    private Set<Integer> userIds;

    /** 匹配条件: 用户花名册实例 key. 读取花名册后载入userId放入{@link #userIds} */
    private String rosterKey;

    /** 资源定义: 首页组件化页面 id, {@link LegoPage#getId()}. */
    private String mainPageId;

    /** 资源定义: 首页框架 id, {@link AppHomeFrame#getId()}. */
    private String homeFrameId;

    /** 更新时间, 单位毫秒 */
    private Long updateTime;

    /** 创建时间, 单位毫秒 */
    private Long createTime;

    // -------------------------------------------------

    /** 最小版本号 可读版, 如 7.0.1. */
    private String vnMin;

    /** 最大版本号 可读版, 如 7.3.1. */
    private String vnMax;

}
