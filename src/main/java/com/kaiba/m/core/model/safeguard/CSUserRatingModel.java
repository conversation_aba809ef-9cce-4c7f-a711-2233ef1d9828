package com.kaiba.m.core.model.safeguard;

import com.kaiba.lib.base.domain.user.UserModel;
import com.kaiba.lib.base.domain.workorder.WOCaseModel;
import com.kaiba.lib.base.domain.workorder.WOEventModel;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * 汽车维权用户评价模型
 * <AUTHOR>
 * @version CSUserRatingModel, v0.1 2024/8/20 14:24 daopei Exp $
 **/
@Data
@ToString
@NoArgsConstructor
public class CSUserRatingModel {

    /** 对应案件. {@link WOCaseModel} */
    private String caseId;

    /** 评分事件. {@link WOEventModel} */
    private String eventId;

    /** 评分事件内容 id. {@link WOEventModel#getNoteId()} */
    private String noteId;

    /** 评分人 id. */
    private Integer userId;

    /** 以文字表示的评论. */
    private String message;

    // ----------------------------------------------

    /** 评分人 */
    private UserModel user;
}
