package com.kaiba.m.core.model.appcyx;

import lombok.Getter;

import java.util.Optional;

/**
 * <AUTHOR>
 * @version CYXArticleGroupState, v0.1 2025/6/23 14:56 daopei Exp $
 **/
@Getter
public enum CYXArticleGroupState {
    SHOW("显示"),
    HIDE("隐藏"),
    ;

    private final String desc;

    CYXArticleGroupState(String desc) {
        this.desc = desc;
    }

    public Optional<CYXArticleGroupState> resolverByName(String name) {
        for (CYXArticleGroupState value : values()) {
            if (value.name().equals(name)) {
                return Optional.of(value);
            }
        }
        return Optional.empty();
    }
}
