package com.kaiba.m.core.model.safeguard.request;

import com.kaiba.lib.base.constant.workorder.WOIdentity;
import com.kaiba.lib.base.domain.common.Image;
import com.kaiba.lib.base.domain.common.Video;
import com.kaiba.lib.base.domain.workorder.WOCaseOriginModel;
import com.kaiba.lib.base.domain.workorder.WOTeamModel;
import com.kaiba.lib.base.util.StringUtils;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.List;

/**
 * <AUTHOR>
 * @version CarSafeguardCaseCreateModel, v0.1 2024/7/16 16:10 daopei Exp $
 **/
@Data
@ToString
@NoArgsConstructor
public class CarSafeguardCaseCreateModel {


    /** 内容: 问题的文字描述 */
    private String content;

    /** 内容: 图片列表 */
    private List<Image> images;

    /** 内容: 视频 */
    private Video video;

    /** 提问者, 如果由 C 端用户创建, 则提问者即创建人. */
    private Integer clientUserId;

    /** 提问者手机号, 仅用于主持者代为提问时, 真实提问者后续认领该问题. 仅主持者可见. */
    private String clientMobile;

    /** 提问者称呼, 可以是真实姓名, 也可以是称谓 (比如李先生, 王大爷等). */
    private String clientName;

    /** 案件来源, {@link WOCaseOriginModel#getOrigin()}. */
    private String origin;

    /** 案件来源地区 */
    private String source;

    /**
     * 初始投诉对象, {@link WOTeamModel#getId()},
     *  - 可为空. 为空表示未有对应的入住商家处理,由节目组处理
     *  - 不为空. 由节目组进行审核, 审核完成后由商家处理
     * 只能是身份为 {@link WOIdentity#RESOLVER} 的用户组.
     */
    private String originResolver;

    /**
     * 投诉对象缺失. 表明无选中的指定投诉对象.
     * 该属性在未指定投诉商家的时候支持填入一个商家名称的文本.
     */
    private String originResolverLack;

    /** 客户端发起案件时, 如果有填写手机号, 且所填手机号与用户绑定手机不同, 则需要验证码 */
    private String vcode;

    /** 案件标签合集 */
    private List<Long> tagCodes;


    /** 个人信息: 姓名 */
    private PersonalInfo personalName;

    /** 个人信息: 手机 */
    private PersonalInfo personalMobile;

    // ----------------------------------------------

    @Data
    @ToString
    @NoArgsConstructor
    public static class PersonalInfo {

        /** 信息 */
        private String data;

        /** 是否对管局可见, 默认可见. */
        private Boolean visible;

        public boolean isEmpty() {
            return StringUtils.isEmpty(data);
        }

    }
}
