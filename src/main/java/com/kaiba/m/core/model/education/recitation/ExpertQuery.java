package com.kaiba.m.core.model.education.recitation;

import lombok.Data;
import org.springframework.data.domain.Pageable;

/**
 * Description: 专家查询条件
 * Author: ZM227
 * Date: 2024/8/5 15:34
 */
@Data
public class ExpertQuery {

    /**
     * 专家code
     */
    private String expertCode;

    /**
     * 专家姓名
     */
    private String name;

    /**
     * 专家类型:0-普通,1-资深
     */
    private Integer expertType;

    /**
     * 分页
     */
    private Integer page;

    /**
     * 页包含
     */
    private Integer pageSize;

}
