package com.kaiba.m.core.model.education.recitation;

import com.kaiba.lib.base.constant.education.KidGrade;
import com.kaiba.lib.base.domain.education.SchoolModel;
import java.util.List;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * Description: 报名记录VO
 * Author: ZM227
 * Date: 2024/8/7 16:37
 */
@Data
@ToString
@NoArgsConstructor
public class ApplyRecordModel {

    /**
     * 报名记录code
     */
    private String recordCode;

    /**
     * 关联资源code
     */
    private String resourceCode;

    /**
     * 姓名
     */
    private String name;

    /**
     * 年龄
     */
    private Integer age;

    /**
     * 联系电话
     */
    private String connectPhone;

    /**
     * 性别:1-男,2-女
     */
    private Integer gender;

    /**
     * 报名类型
     * 1, "声音环游记"
     * 2, "朗诵团"
     */
    private Integer recordCate;

    /**
     * 报名子类型
     * 0, "未知"
     * 1, "寒暑假集训"
     * 2, "周末研学营"
     * 3, "节目互动"
     */
    private List<Integer> subRecordCate;

    /**
     * 学校名称
     */
    private String schoolName;

    /**
     * 年级信息: 学生年级
     * {@link KidGrade}
     */
    private Integer grade;

    /**
     * 年级信息: 记录生成时学生所在学校
     * {@link SchoolModel#getId()}
     */
    private String schoolId;

    /**
     * 创建时间, 时间戳毫秒数
     */
    private Long createTime;
}
