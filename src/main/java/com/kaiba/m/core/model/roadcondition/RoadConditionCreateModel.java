package com.kaiba.m.core.model.roadcondition;

import com.kaiba.lib.base.domain.common.Audio;
import com.kaiba.lib.base.domain.common.Image;
import com.kaiba.lib.base.domain.common.Video;

import java.util.List;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * <AUTHOR>
 * @date 2024/05/21 17:49
 **/
@Data
@ToString
@NoArgsConstructor
public class RoadConditionCreateModel {

    /** 电台 id */
    private Integer siteId;

    /** 地区编码 */
    private Integer source;

    /** 用户 id */
    private Integer userId;

    /** 经度 */
    private Double lng;

    /** 纬度 */
    private Double lat;

    /** 有效时间 s */
    private Integer duration;

    /** 文字内容 */
    private String content;

    /** 地址,此地址为地图SDK逆地理信息得到的第一条街道拼接数据 */
    private String locationName;

    /** 地址,此地址为用户自主选择的地址 */
    private String street;

    /** 图片列表 */
    private List<Image> pic;

    /** 音频 */
    private Audio audio;

    /** 视频 */
    private Video video;

}
