package com.kaiba.m.core.model.safeguard.search;

import com.kaiba.lib.base.domain.workorder.WOTeamModel;
import lombok.Data;
import org.springframework.data.annotation.Transient;

import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;

/**
 * 汽车维权 查询搜索基础模型
 * <AUTHOR>
 * @version CSQuerySearchBaseModel, v0.1 2024/7/31 14:41 daopei Exp $
 **/
@Data
public class CSQuerySearchBaseModel {

    /** 查询条件: 当前或过往处理人, {@link WOTeamModel}. */
    private String anyResolver;
    /** 投诉用户 */
    private Integer clientUserId;
    /** 投诉人姓名 */
    private String clientName;
    /** 投诉人手机号 */
    private String clientMobile;
    /** 投诉对象 */
    private String originResolver;
    /** 维权内容检索 */
    private String searchContent;
    /** 业务类型 */
    private String contentType;
    /** 办结类型 */
    private List<String> closeTypes;
    /** 投诉工单创建时间开始 */
    private Long rangeSt;
    /** 投诉工单创建时间结束 */
    private Long rangeEt;
    /** 上报渠道 */
    private String origin;
    /** 维权状态 */
    private List<String> states;
    /** 维权业务子状态 */
    private String bizState;
    /** 案件标签.筛选条件 内部为and条件 */
    private List<Long> tagCodes;
    /** 页码 */
    private Integer page;
    /** 页数 */
    private Integer pageSize;

    /** 基础排序. 默认为 {@link #SORT_BY_CREATE_TIME_DESC}*/
    private Integer sortBy;
    /**
     * 标签排序, 拥有标签则优先排序, 多个标签匹配则权重更高
     * 优先级高于sortBy
     */
    private LinkedList<Long> sortTag;

    /**
     * 标签排序策略. 管理sortTag的权重排序策略. 默认 {@link #SORT_TAG_VALUE_DEFAULT}
     * 1. {@link #SORT_TAG_VALUE_DEFAULT} 默认权重排序. 所有{@link #sortTag} 内部标签相同权重,具体数值查询内部逻辑
     * 2. {@link #SORT_TAG_VALUE_DESC} . {@link #sortTag} 内部标签按照数组权重值递减,具体数值查询内部逻辑
     * 3. {@link #SORT_TAG_VALUE_ASC} . {@link #sortTag} 内部标签按照数组权重值递增,具体数值查询内部逻辑
     * */
    private Integer sortTagStrategy;

    // ---------------------------------

    /** 排序方式 - 按创建时间倒序 */
    @Transient
    public static final int SORT_BY_CREATE_TIME_DESC = 1;

    /** 排序方式 - 按更新时间倒序 */
    @Transient
    public static final int SORT_BY_UPDATE_TIME_DESC = 2;

    /** 标签排序方式 - 标签使用默认权重排序 */
    @Transient
    public static final int SORT_TAG_VALUE_DEFAULT = 1;

    /** 标签排序方式 - 标签权重数值默认递减 */
    @Transient
    public static final int SORT_TAG_VALUE_DESC = 2;

    /** 标签排序方式 - 标签权重数值默认递增 */
    @Transient
    public static final int SORT_TAG_VALUE_ASC = 3;


    /**
     * 过滤标签. 初始化并且添加
     * @param tagCode
     */
    public void addTagCode(Long tagCode) {
        if (tagCode == null) {
            return;
        }
        if (tagCodes == null) {
            tagCodes = new ArrayList<>();
        }
        tagCodes.add(tagCode);
    }

    /**
     * 排序标签. 初始化并且添加
     * @param tagCode
     */
    public void addSortTagCode(Long tagCode) {
        if (tagCode == null) {
            return;
        }
        if (sortTag == null) {
            sortTag = new LinkedList<>();
        }
        sortTag.add(tagCode);
    }

    /**
     * 排序标签全空,则添加
     * @param tagCode
     */
    public void addSortTagCodeIfAllAbsent(Long tagCode) {
        if (tagCode == null) {
            return;
        }
        if (sortTag == null) {
            sortTag = new LinkedList<>();
        }
        if (!sortTag.isEmpty()) {
            return;
        }
        sortTag.add(tagCode);
    }


    /**
     * 是否需要采用标签排序
     * @return
     */
    public boolean needSortByTag() {
        return this.sortTag != null && !this.sortTag.isEmpty();
    }

}
