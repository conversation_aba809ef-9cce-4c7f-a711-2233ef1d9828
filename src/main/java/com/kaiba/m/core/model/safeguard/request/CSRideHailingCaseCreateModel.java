package com.kaiba.m.core.model.safeguard.request;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * 网约车维权新增
 *
 * <AUTHOR>
 * @version CSRideHailingCaseCreateModel, v0.1 2024/7/16 16:17 daopei Exp $
 **/
@EqualsAndHashCode(callSuper = true)
@Data
@ToString
@NoArgsConstructor
public class CSRideHailingCaseCreateModel extends CarSafeguardCaseCreateModel {
    /**
     * 网约车类型 快车、专车、出租车、顺风车、租车、代驾
     */
    private String rideHailingType;
    /**
     * 约车时间
     */
    private Long rideHailingTime;
    /**
     * 网约车车牌号
     */
    private String rideHailingCarNo;
    /**
     * 用户约车手机号
     */
    private String rideHailingUserPhone;
}
