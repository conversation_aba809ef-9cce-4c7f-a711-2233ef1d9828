package com.kaiba.m.core.model.artmap.dto;

import com.kaiba.m.core.domain.education.recitation.enums.CommonStatusEnum;
import java.time.Instant;
import java.time.LocalDate;
import java.time.ZonedDateTime;
import java.util.List;
import lombok.Data;
import org.springframework.data.domain.Pageable;

/**
 * Description: 日历化演艺演出查询DTO
 * Author: ZM227
 * Date: 2025/6/13 9:59
 */
@Data
public class CalendarPerformQueryDTO {

    /**
     * 演艺演出code列表
     */
    private List<String> performanceCodes;

    /**
     * 演出日期起始
     */
    private Instant performanceDateStart;

    /**
     * 演出日期截止
     */
    private Instant performanceDateEnd;

    /**
     * 场馆Code
     */
    private String venuesCode;

    /**
     * 状态
     */
    private Integer status = CommonStatusEnum.VALID.getCode();

    /**
     * 分页信息
     */
    private Pageable pageable;
}
