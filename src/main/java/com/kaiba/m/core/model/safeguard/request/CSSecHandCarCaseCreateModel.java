package com.kaiba.m.core.model.safeguard.request;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * <AUTHOR>
 * @version CSSecHandCarCaseCreateModel, v0.1 2024/7/16 16:17 daopei Exp $
 **/
@EqualsAndHashCode(callSuper = true)
@Data
@ToString
@NoArgsConstructor
public class CSSecHandCarCaseCreateModel extends CarSafeguardCaseCreateModel {

    /**
     * 二手车用户身份
     */
    private String secHandCarRole;
    /**
     * 二手车交易时间
     */
    private Long secHandCarDealTime;
}
