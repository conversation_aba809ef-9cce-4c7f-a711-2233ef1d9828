package com.kaiba.m.core.controller.education.recitation.backend;

import com.kaiba.lib.base.constant.KbHeader;
import com.kaiba.lib.base.middleware.KbRes;
import com.kaiba.lib.base.response.KbEntity;
import com.kaiba.m.core.controller.education.recitation.mapper.RecitationConMapping;
import com.kaiba.m.core.model.education.recitation.dto.MemberQueryDTO;
import com.kaiba.m.core.domain.education.recitation.Member;
import com.kaiba.m.core.model.education.recitation.MemberModel;
import com.kaiba.m.core.model.education.recitation.MemberQuery;
import com.kaiba.m.core.service.education.recitation.MemberQueryService;
import com.kaiba.m.core.util.PageUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.util.List;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.domain.Sort.Direction;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * Description: 成员查询Controller
 * Author: ZM227
 * Date: 2024/8/5 9:55
 */
@Slf4j
@RestController
@RequestMapping("/backend/recitation/usr/member/query")
@Api(tags = "后端朗诵团成员查询")
public class MemberBEQueryController {

    @Resource
    private KbRes kbRes;
    @Resource
    private MemberQueryService memberQueryService;
    @Resource
    private RecitationConMapping recitationConMapping;

    /**
     * 查询成员列表
     *
     * @param userId 当前操作开吧用户Id
     * @param query  查询条件
     * @return 成员分页数据
     */
    @PostMapping("/queryMemberList")
    @ApiOperation(value = "查询成员列表")
    public KbEntity<List<MemberModel>> queryMemberList(
        @RequestHeader(name = KbHeader.KB_USER_ID) Integer userId,
        @RequestBody MemberQuery query) {
        MemberQueryDTO queryDTO = recitationConMapping.memberQueryToDTO(query);
        Pageable pageable = PageUtils.ofDefault(query.getPage(), query.getPageSize(),
            Sort.by(Direction.DESC, "createTime"));
        queryDTO.setPageable(pageable);
        Page<Member> members = memberQueryService.conditionQueryMembers(queryDTO);
        Page<MemberModel> memberModels = new PageImpl<>(
            members.getContent().stream().map(m -> recitationConMapping.domainToModel(m))
                .collect(Collectors.toList()), members.getPageable(), members.getTotalElements());
        return kbRes.ok(memberModels);
    }

    @PostMapping("/queryMemberDetail")
    @ApiOperation(value = "查询成员详情")
    public KbEntity<MemberModel> queryMemberDetail(
        @RequestHeader(name = KbHeader.KB_USER_ID) Integer userId,
        @RequestParam(required = false) String memberCode,
        @RequestParam(required = false) String name,
        @RequestParam(required = false) String connectPhone) {
        Member member = memberQueryService.findExistMember(memberCode, name, connectPhone);
        return kbRes.ok(recitationConMapping.domainToModel(member));
    }
}
