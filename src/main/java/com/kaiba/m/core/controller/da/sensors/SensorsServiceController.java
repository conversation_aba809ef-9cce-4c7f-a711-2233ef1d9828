package com.kaiba.m.core.controller.da.sensors;

import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.LoadingCache;
import com.kaiba.lib.base.assembler.Mapper;
import com.kaiba.lib.base.constant.KbModule;
import com.kaiba.lib.base.domain.da.sensors.pgc.SensorsPGCObjUserActCountModel;
import com.kaiba.lib.base.domain.da.sensors.pgc.SensorsPGCObjUserActModel;
import com.kaiba.lib.base.domain.da.sensors.virtual.SensorsVirtualExpressionModel;
import com.kaiba.lib.base.lang.verifier.F;
import com.kaiba.lib.base.lang.verifier.Verifier;
import com.kaiba.lib.base.lang.verifier.VerifierBuilder;
import com.kaiba.lib.base.middleware.KbRes;
import com.kaiba.lib.base.response.KbCode;
import com.kaiba.lib.base.response.KbEntity;
import com.kaiba.lib.base.response.KbException;
import com.kaiba.lib.base.service.ICounterService;
import com.kaiba.lib.base.service.ISensorsService;
import com.kaiba.m.core.domain.da.sensors.virtual.SensorsVirtualExpression;
import com.kaiba.m.core.middleware.instantcache.KbInstantCaffeineService;
import com.kaiba.m.core.repository.da.sensors.virtual.SensorsVirtualExpressionRepository;
import com.kaiba.m.core.service.da.sensors.SensorsCacheService;
import com.kaiba.m.core.service.da.sensors.SensorsLocalStatService;
import lombok.EqualsAndHashCode;
import lombok.NonNull;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.web.bind.annotation.RestController;

import java.time.Duration;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @version SensorsServiceController, v0.1 2023/12/25 16:51 daopei Exp $
 **/
@RestController
public class SensorsServiceController implements ISensorsService {

    private final static List<String> allowedBusiness = Arrays.asList(
            KbModule.NEWS.name(),
            KbModule.NEWS_NEO.name(),
            KbModule.REVEAL.name(),
            KbModule.WORK_ORDER.name()
            );

    @NonNull
    private final KbRes kbRes;
    @NonNull
    private final SensorsCacheService sensorsCacheService;
    @NonNull
    private final SensorsLocalStatService sensorsService;
    @NonNull
    private final SensorsVirtualExpressionRepository repository;
    @NonNull
    private final ICounterService counterService;

    private final LoadingCache<CacheKey, SensorsVirtualExpressionModel> veCacheByModule;

    private final LoadingCache<CacheKey, SensorsVirtualExpressionModel> veCacheByContent;

    public SensorsServiceController(
            @NonNull KbRes kbRes, 
            @NonNull SensorsCacheService sensorsCacheService,
            @NonNull SensorsLocalStatService sensorsService,
            @NonNull SensorsVirtualExpressionRepository repository, 
            @NonNull ICounterService counterService,
            @NonNull KbInstantCaffeineService kbInstantCaffeineService
    ) {
        this.kbRes = kbRes;
        this.sensorsCacheService = sensorsCacheService;
        this.sensorsService = sensorsService;
        this.repository = repository;
        this.counterService = counterService;
        this.veCacheByModule = kbInstantCaffeineService.registerInstantCache("ve_cache_by_module",
                Caffeine.newBuilder()
                        .expireAfterWrite(Duration.ofMinutes(30))
                        .refreshAfterWrite(Duration.ofMinutes(3))
                        .build(this::getBusinessCacheFromDB));
        this.veCacheByContent = kbInstantCaffeineService.registerInstantCache("ve_cache_by_content",
                Caffeine.newBuilder()
                        .expireAfterWrite(Duration.ofMinutes(30))
                        .refreshAfterWrite(Duration.ofMinutes(3))
                        .build(this::getContentCacheFromDB));
    }

    @Override
    public KbEntity<List<SensorsPGCObjUserActCountModel>> kbPGCCountListByCacheRT(List<SensorsPGCObjUserActModel> query) {
        return kbRes.ok(sensorsCacheService.getListFromCache(query));
    }

    @Override
    public KbEntity<SensorsPGCObjUserActCountModel> kbPGCCountByCacheRT(SensorsPGCObjUserActModel query) {
        if (!allowedBusiness.contains(query.getBusiness())) {
            return kbRes.ok();
        }
        return kbRes.ok(sensorsCacheService.getFromCache(query));
    }

    @Override
    public KbEntity<Long> kbPGCViewCacheIncr(SensorsPGCObjUserActModel model) {
        return kbRes.ok(sensorsCacheService.incrViewCount(model));
    }

    @Override
    public KbEntity<SensorsPGCObjUserActCountModel> kbPGCCountRT(SensorsPGCObjUserActModel query) {
        SENSORS_QUERY_VERIFIER.verify(query);
        return sensorsService.querySum(query)
                .map(kbRes::ok)
                .orElseGet(() -> kbRes.err(KbCode.RESOURCE_NOT_FOUND));
    }

    @Override
    public KbEntity<Long> calculateFromCache(Long c, String business, String contentKey) {
        if (c == null) {
            return kbRes.ok();
        }
        SensorsVirtualExpressionModel expressionModel = getFromCache(business, contentKey);
        if (expressionModel == null) {
            return kbRes.ok(c);
        }
        Long vvc = counterService.runVirtualExpression(expressionModel.getExpression(), c, 0L, contentKey == null ? null : contentKey.hashCode(), null, null).data().orElse(null);
        return kbRes.ok(vvc);
    }

    @Override
    public KbEntity<Long> calculateFromCacheByModel(Long c, String business, SensorsPGCObjUserActModel model) {
        String contentKey = mapRefId2ExpressionKey(model.getRef1(), model.getRef2(), model.getRef3());
        return calculateFromCache(c, business, contentKey);
    }



    @Override
    public KbEntity<SensorsVirtualExpressionModel> getByBusinessAndContentKeyFromCache(String business, String contentKey) {
        return kbRes.ok(getFromCache(business, contentKey));
    }

    @Override
    public KbEntity<List<SensorsVirtualExpressionModel>> getPage(
            String business,
            String contentKey,
            Integer type,
            Integer page,
            Integer pageSize
    ) {
        return kbRes.ok(repository.findByParam(
                business,
                contentKey,
                type,
                p(page, pageSize)).map(obj -> Mapper.map(obj, SensorsVirtualExpressionModel.class)));
    }

    @Override
    public KbEntity<SensorsVirtualExpressionModel> createExpression(SensorsVirtualExpressionModel model) {
        CREATE_VERIFIER.verify(model);
        Optional<SensorsVirtualExpression> op;
        if (model.getType() == 1) {
            op = repository.findFirstByBusinessAndType(model.getBusiness(), model.getType());
        } else {
            String contentKey = mapRefId2ExpressionKey(model.getRef1(), model.getRef2(), model.getRef3());
            if (contentKey == null) {
                return kbRes.err(KbCode.ILLEGAL_ARGUMENT, "请填写业务引用标识refId");
            }
            model.setContentKey(contentKey);
            op = repository.findFirstByBusinessAndContentKeyAndType(model.getBusiness(), model.getContentKey(), model.getType());
        }
        if (op.isPresent()) {
            return kbRes.err(KbCode.RESOURCE_ALREADY_EXIST);
        }
        return kbRes.ok(Mapper.map(repository.save(Mapper.map(model, SensorsVirtualExpression.class)), SensorsVirtualExpressionModel.class));
    }


    @Override
    public KbEntity<SensorsVirtualExpressionModel> updateExpression(SensorsVirtualExpressionModel model) {
        UPDATE_VERIFIER.verify(model);
        SensorsVirtualExpression entity = repository.findById(model.getId()).orElseThrow(() -> new KbException(KbCode.RESOURCE_NOT_FOUND).li());
        entity.setExpression(model.getExpression());
        entity.setDescription(model.getDescription());
        if (entity.getType() == 2) {
            CacheKey cacheKey = new CacheKey(entity.getBusiness(), entity.getContentKey());
            veCacheByContent.invalidate(cacheKey);
        } else {
            CacheKey cacheKey = new CacheKey(entity.getBusiness(), entity.getBusiness());
            veCacheByModule.invalidate(cacheKey);
        }
        return kbRes.ok(Mapper.map(repository.save(entity), SensorsVirtualExpressionModel.class));
    }


    @Override
    public KbEntity<SensorsVirtualExpressionModel> updateState(String id, Integer state) {
        SensorsVirtualExpression entity = repository.findById(id).orElseThrow(() -> new KbException(KbCode.RESOURCE_NOT_FOUND).li());
        entity.setState(state);
        if (entity.getType() == 2) {
            CacheKey cacheKey = new CacheKey(entity.getBusiness(), entity.getContentKey());
            veCacheByContent.invalidate(cacheKey);
        } else {
            CacheKey cacheKey = new CacheKey(entity.getBusiness(), null);
            veCacheByModule.invalidate(cacheKey);
        }
        return kbRes.ok(Mapper.map(repository.save(entity), SensorsVirtualExpressionModel.class));
    }


    private SensorsVirtualExpressionModel getBusinessCacheFromDB(CacheKey cacheKey) {
        return repository.findFirstByBusinessAndTypeAndState(cacheKey.business, 1, 1)
                .map(obj -> Mapper.map(obj, SensorsVirtualExpressionModel.class)).orElse(null);
    }

    private SensorsVirtualExpressionModel getContentCacheFromDB(CacheKey cacheKey) {
        return repository.findFirstByBusinessAndContentKeyAndTypeAndState(cacheKey.business, cacheKey.contentKey, 2, 1)
                .map(obj -> Mapper.map(obj, SensorsVirtualExpressionModel.class)).orElse(null);
    }

    private SensorsVirtualExpressionModel getFromCache(String module, String contentKey) {
        CacheKey cacheKey = new CacheKey(module, contentKey);
        SensorsVirtualExpressionModel expression = veCacheByContent.get(cacheKey);
        if (expression != null) {
            return expression;
        }
        return veCacheByModule.get(cacheKey);
    }

    private static final Verifier<SensorsPGCObjUserActModel> SENSORS_QUERY_VERIFIER = new VerifierBuilder<SensorsPGCObjUserActModel>()
            .defaultOrElseThrow()
            .and(F.str(SensorsPGCObjUserActModel::getBusiness).notNull().r("业务模块不可为空"))
            .and(F.str(SensorsPGCObjUserActModel::getRef1).notNull().r("关联对象ID不可为空"))
            .create();

    private static final Verifier<SensorsVirtualExpressionModel> CREATE_VERIFIER = new VerifierBuilder<SensorsVirtualExpressionModel>()
            .defaultOrElseThrow()
            .and(F.str(SensorsVirtualExpressionModel::getExpression).notNull().r("表达式不可为空"))
            .and(F.str(SensorsVirtualExpressionModel::getBusiness).notNull().r("模块不可为空"))
            .and(F.intF(SensorsVirtualExpressionModel::getType).notNull().r("数据类型不可为空,选择1(基础配置)或者2(业务配置)"))
            .create();

    private static final Verifier<SensorsVirtualExpressionModel> UPDATE_VERIFIER = new VerifierBuilder<SensorsVirtualExpressionModel>()
            .defaultOrElseThrow()
            .and(F.str(SensorsVirtualExpressionModel::getId).notNull().r("ID不可为空"))
            .and(F.str(SensorsVirtualExpressionModel::getExpression).notNull().r("表达式不可为空"))
            .and(F.str(SensorsVirtualExpressionModel::getBusiness).notNull().r("模块不可为空"))
            .and(F.intF(SensorsVirtualExpressionModel::getType).notNull().r("数据类型不可为空,选择1(基础配置)或者2(业务配置)"))
            .create();

    private Pageable p(Integer page, Integer pageSize) {
        int p = page == null? 0 : page - 1;
        int ps = pageSize == null? 20 : pageSize;
        return PageRequest.of(p, ps);
    }


    public static String mapRefId2ExpressionKey(String ref1, String ref2, String ref3) {
        List<String> contentKeyElements = new ArrayList<>();
        if (ref1 != null) {
            contentKeyElements.add(ref1);
        }
        if (ref2 != null) {
            contentKeyElements.add(ref2);
        }
        if (ref3 != null) {
            contentKeyElements.add(ref3);
        }
        if (contentKeyElements.isEmpty()) {
            return null;
        }
        return String.join("|", contentKeyElements);
    }


    @EqualsAndHashCode
    private static class CacheKey {
        private final String business;
        private final String contentKey;

        public CacheKey(String business, String contentKey) {
            this.business = business;
            this.contentKey = contentKey;
        }
    }
}

