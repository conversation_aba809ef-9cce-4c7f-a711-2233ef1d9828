package com.kaiba.m.core.controller.search;

import com.kaiba.lib.base.middleware.KbRes;
import com.kaiba.lib.base.response.KbEntity;
import com.kaiba.m.core.service.opensearch.OpenSearchService;

import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2023/07/19 14:03
 **/
@Slf4j
@RestController()
@RequestMapping("/openSearch/sync")
public class OpenSearchSyncController {

    private final KbRes kbRes;
    private final OpenSearchService syncService;

    public OpenSearchSyncController(
            KbRes kbRes,
            OpenSearchService syncService
    ) {
        this.kbRes = kbRes;
        this.syncService = syncService;
    }

    @PostMapping(path = "/syncNewsData")
    public KbEntity<String> syncNewsData(
            @RequestParam String startDate,
            @RequestParam String endDate
    ) {
        syncService.syncNewsData(startDate, endDate);
        return kbRes.ok();
    }

    @PostMapping(path = "/syncNoteData")
    public KbEntity<String> syncNoteData(
            @RequestParam String startDate,
            @RequestParam String endDate
    ) {
        syncService.syncNoteData(startDate, endDate);
        return kbRes.ok();
    }

    @PostMapping(path = "/syncProgramData")
    public KbEntity<Void> syncProgramData(
            @RequestParam String startDate,
            @RequestParam String endDate
    ) {
        syncService.syncProgramData(startDate, endDate);
        return kbRes.ok();
    }

    @PostMapping(path = "/syncUserData")
    public KbEntity<Void> syncUserData(
            @RequestParam String startDate,
            @RequestParam String endDate
    ) {
        syncService.syncUserData(startDate, endDate);
        return kbRes.ok();
    }

    @PostMapping(path = "/syncNeoNewsData")
    public KbEntity<Void> syncNeoNewsData(
            @RequestParam String startDate,
            @RequestParam String endDate
    ) {
        syncService.syncNeoNewsData(startDate, endDate);
        return kbRes.ok();
    }

    // -------------------------------------------------

    @PostMapping("/delSearchData")
    public KbEntity<Void> delSearchData(
            @RequestParam String searchModule,
            @RequestParam String id
    ) {
        log.info("delete open search module: {}, id: {}", searchModule, id);
        syncService.delSearchData(searchModule, id);
        return kbRes.ok();
    }

}
