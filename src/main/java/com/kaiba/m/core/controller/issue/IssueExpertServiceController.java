package com.kaiba.m.core.controller.issue;

import com.kaiba.lib.base.assembler.Mapper;
import com.kaiba.lib.base.constant.issue.*;
import com.kaiba.lib.base.constant.user.UserRole;
import com.kaiba.lib.base.domain.car.CarBrandModel;
import com.kaiba.lib.base.domain.car.CarModel;
import com.kaiba.lib.base.domain.issue.*;
import com.kaiba.lib.base.domain.site.ZoneModel;
import com.kaiba.lib.base.domain.user.UserModel;
import com.kaiba.lib.base.middleware.KbRes;
import com.kaiba.lib.base.response.KbCode;
import com.kaiba.lib.base.response.KbEntity;
import com.kaiba.lib.base.response.KbException;
import com.kaiba.lib.base.service.*;
import com.kaiba.lib.base.util.ArrayTypeHolder;
import com.kaiba.lib.base.util.StringUtils;
import com.kaiba.m.core.domain.issue.IssueExpert;
import com.kaiba.m.core.domain.issue.IssueExpertApplication;
import com.kaiba.m.core.service.DocumentDeletionService;
import com.kaiba.m.core.service.issue.IssueExpertService;
import com.kaiba.m.core.service.issue.IssueService;
import com.kaiba.m.core.service.issue.IssueUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.web.bind.annotation.RestController;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * author: lyux
 * date: 18-9-11
 */
@Slf4j
@RestController
public class IssueExpertServiceController implements IIssueExpertService {

    private final KbRes kbRes;
    private final IssueService issueService;
    private final IssueExpertService issueExpertService;
    private final DocumentDeletionService deletionService;
    private final IZoneService zoneService;
    private final IUserService userService;
    private final IUserAccountService accountService;
    private final ICarService carService;

    @Autowired
    public IssueExpertServiceController(
            KbRes kbRes,
            IssueService issueService,
            IssueExpertService issueExpertService,
            DocumentDeletionService deletionService,
            IZoneService zoneService,
            IUserService userService,
            IUserAccountService accountService,
            ICarService carService
    ) {
        this.kbRes = kbRes;
        this.issueService = issueService;
        this.issueExpertService = issueExpertService;
        this.deletionService = deletionService;
        this.zoneService = zoneService;
        this.userService = userService;
        this.accountService = accountService;
        this.carService = carService;
    }

    @Override
    public KbEntity<IssueExpertModel> login(Integer expertId, String deviceToken) {
        KbEntity<IssueExpertModel> result = getExpertDetail(expertId);
        issueExpertService.updateLogin(expertId);
        if (!StringUtils.isEmpty(deviceToken)) {
            accountService.updateExpertDeviceToken(expertId, deviceToken);
        }
        return result;
    }

    @Override
    public KbEntity<IssueExpertModel> getExpertBasic(Integer expertId) {
        return issueExpertService.expertById(expertId)
                .map(expert -> toModel(expert, false))
                .map(kbRes::ok)
                .orElse(kbRes.err(KbCode.ISSUE_EXPERT_NOT_EXISTS, "expert not exists: " + expertId));
    }

    @Override
    public KbEntity<IssueExpertModel> getExpertInformation(Integer expertId) {
        return issueExpertService.expertById(expertId)
                .map(expert -> {
                    IssueExpertModel model = expert2information(expert);
                    if (expert.getSpecialBrand() != null && !expert.getSpecialBrand().isEmpty()) {
                        model.setSpecialBrand(carService
                                .getCarBrandListIn(expert.getSpecialBrand().toArray(new String[0]))
                                .getData());
                    }
                    return model;
                })
                .map(kbRes::ok)
                .orElse(kbRes.err(KbCode.ISSUE_EXPERT_NOT_EXISTS, "expert not exists: " + expertId));
    }

    @Override
    public KbEntity<IssueExpertModel> getExpertDetail(Integer expertId) {
        return issueExpertService.expertById(expertId)
                .map(expert -> toModel(expert, true))
                .map(kbRes::ok)
                .orElse(kbRes.err(KbCode.ISSUE_EXPERT_NOT_EXISTS, "expert not exists: " + expertId));
    }

    @Override
    public KbEntity<List<IssueExpertModel>> getExpertListIn(Integer[] expertIdList) {
        return kbRes.ok(toModelList(issueExpertService.expertListIn(expertIdList)));
    }

    @Override
    public KbEntity<List<IssueExpertModel>> getExpertListAll(Integer page, Integer pageSize) {
        return kbRes.ok(toModelList(issueExpertService.expertListAll(page, pageSize), true));
    }

    @Override
    public KbEntity<List<IssueExpertModel>> getExpertListBySpecial(
            String carCode, String area, Integer source, Integer page, Integer pageSize) {
        String carBrandCode = carService.getCar(carCode).data().map(CarModel::getBrandCode).orElse(null);
        return kbRes.ok(toModelList(issueExpertService.expertListBySpecial(carBrandCode, page, pageSize)));
    }

    @Override
    public KbEntity<List<IssueExpertModel>> getExpertListByFollower(Integer userId, Integer page, Integer pageSize) {
        return kbRes.ok(toModelList(issueExpertService.expertListByFollower(userId, page, pageSize)));
    }

    @Override
    public KbEntity<List<IssueExpertModel>> getExpertListByRecommend(Integer allFields) {
        return kbRes.ok(toModelList(
                issueExpertService.expertListByRecommend(), allFields != null && allFields > 0));
    }

    @Override
    public KbEntity<List<IssueExpertModel>> getExpertListPageable(
            Integer source, Integer level, Integer page, Integer pageSize,
            String[] sortAsc, String[] sortDesc) {
        PageRequest pageRequest;
        List<Sort.Order> sortOrders = new LinkedList<>();
        if (null != sortDesc) {
            for (String field : sortDesc) {
                sortOrders.add(new Sort.Order(Sort.Direction.DESC, field));
            }
        }
        if (null != sortAsc) {
            for (String field : sortAsc) {
                sortOrders.add(new Sort.Order(Sort.Direction.ASC, field));
            }
        }
        if (sortOrders.size() == 0) {
            pageRequest = PageRequest.of(page - 1, pageSize);
        } else {
            pageRequest = PageRequest.of(page - 1, pageSize, Sort.by(sortOrders));
        }
        Page<IssueExpert> expertPage = issueExpertService.expertListPageable(
                source, level, pageRequest);
        KbEntity<List<IssueExpertModel>> result = kbRes.ok(toModelList(expertPage.getContent(), true));
        result.setTotalPage((long) expertPage.getTotalPages());
        return result;
    }

    @Override
    public KbEntity<List<PayLevelModel>> getDefaultPayLevelList() {
        return kbRes.ok(IssueConsts.ISSUE_PAY_LEVELS_IM_DEFAULT);
    }

    @Override
    public KbEntity<List<PayLevelModel>> getDefaultPhonePayLevelList() {
        return kbRes.ok(IssueConsts.ISSUE_PAY_LEVELS_PHONE_DEFAULT);
    }

    @Override
    public KbEntity<List<ExpertLevelModel>> getExpertLevelList() {
        return kbRes.ok(Arrays.stream(ExpertLevel.values())
                .map(level -> new ExpertLevelModel(level.getValue(), level.getTitle()))
                .collect(Collectors.toList()));
    }

    @Override
    public KbEntity<List<ExpertRankModel>> getExpertRankList() {
        return kbRes.ok(Arrays.stream(ExpertRank.values())
                .map(rank -> new ExpertRankModel(rank.getValue(), rank.getName()))
                .collect(Collectors.toList()));
    }

    @Override
    public KbEntity<Long> getExpertFollowerCount(Integer expertId) {
        return kbRes.ok(issueExpertService.followerCount(expertId));
    }

    @Override
    public KbEntity<Long> getExpertPraiseCount(Integer expertId) {
        return kbRes.ok(issueService.issuePraiseCountByExpert(expertId));
    }

    @Override
    public KbEntity<Long> getExpertAnswerCount(Integer expertId, Long since) {
        if (since != null && since > 0) {
            return kbRes.ok(issueService.issueCountByExpert(expertId, IssueState.SEALED_ISSUE_STATES, since));
        } else {
            return kbRes.ok(issueService.issueCountByExpert(expertId, IssueState.SEALED_ISSUE_STATES));
        }
    }

    @Override
    public KbEntity<Boolean> getExpertUserFollowed(Integer expertId, Integer userId) {
        return kbRes.ok(issueExpertService.isUserFollowed(expertId, userId));
    }

    @Override
    public KbEntity<String> followExpert(Integer userId, Integer expertId) {
        issueExpertService.expertById(expertId).orElseThrow(KbException.supplier(KbCode.ISSUE_EXPERT_NOT_EXISTS));
        issueExpertService.followExpert(expertId, userId);
        return kbRes.ok();
    }

    @Override
    public KbEntity<String> unfollowExpert(Integer userId, Integer expertId) {
        issueExpertService.unfollowExpert(expertId, userId);
        return kbRes.ok();
    }

    @Override
    public KbEntity<String> recommendExpert(Integer expertId, Boolean recommend) {
        issueExpertService.expertRecommend(expertId, recommend);
        return kbRes.ok();
    }

    @Override
    public KbEntity<String> updateExpertSummary(Integer expertId,
                                                Integer answerCount, Integer praiseCount, Integer followerCount,
                                                Double satisfactionRatio, Double speedRatio, Double qualityRatio) {
        issueExpertService.updateSummary(expertId,
                answerCount, praiseCount, followerCount,
                satisfactionRatio, speedRatio, qualityRatio);
        return kbRes.ok();
    }

    @Override
    public KbEntity<IssueExpertModel> createExpert(
            Integer userId, String description, String userName, String avatar,
            Integer channelId, Integer rank, Integer level, String company,
            String[] specialCarCodes, String[] specialAreas, String[] certificates,
            String[] idCards, String idCardNumber) {

        issueExpertService.expertById(userId).ifPresent(issueExpert -> {
            log.warn("create expert, already exist: " + issueExpert);
            throw new KbException(KbCode.ILLEGAL_ARGUMENT, "expert already exists: " + userId)
                    .setReadableMessage("技师已存在");
        });
        UserModel user = userService.getBasic(userId).dataOrThrow();
        if (user.getRole() == null || user.getRole() == 0) {
            userService.updateUserRole(userId, UserRole.ISSUE_EXPERT.getValue());
        } else if (user.getRole() != UserRole.ISSUE_EXPERT.getValue()) {
            throw new KbException(KbCode.ILLEGAL_STATE, "user role exists and is not expert: " +
                    user.getRole() + " -> " + UserRole.ISSUE_EXPERT.getValue())
                    .setReadableMessage("用户已注册为其他角色");
        }
        ExpertLevel expertLevel = ExpertLevel.valueOf(level).orElse(ExpertLevel.LEVEL1);
        ExpertRank expertRank = ExpertRank.valueOf(rank).orElse(ExpertRank.RANK1);

        IssueExpert expert = new IssueExpert();
        expert.setId(userId);
        expert.setChannelId(channelId);
        expert.setAvatar(null == avatar ? user.getAvatar() : avatar);
        expert.setUserName(null == userName ? user.getUserName() : userName);
        expert.setMobile(user.getMobile());
        expert.setSex(user.getSex());
        expert.setSource(user.getSource());
        expert.setCompany(company);
        expert.setDescription(description);
        expert.setLevel(expertLevel.getValue());
        expert.setRank(expertRank.getValue());
        if (specialCarCodes != null && specialCarCodes.length > 0) {
            if (specialCarCodes.length > 5) {
                return kbRes.err(KbCode.ILLEGAL_ARGUMENT, "special car should be less than 5");
            }
            expert.setSpecialBrand(carService.getCarBrandListIn(specialCarCodes)
                    .check().getData().stream()
                    .map(CarBrandModel::getCode)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList()));
        }
        if (null != specialAreas && specialAreas.length != 0) {
            expert.setSpecialArea(Arrays.asList(specialAreas));
        }
        if (null != certificates && certificates.length != 0) {
            expert.setCertificate(Arrays.asList(certificates));
        }
        expert.setIdCard(Arrays.asList(idCards));
        expert.setIdCardNumber(idCardNumber);
        expert.setCreateTime(System.currentTimeMillis() / 1000);
        expert = issueExpertService.expertCreate(expert);

        IssueExpertModel model = expert2detail(expert);
        model.setTitle(expertLevel.getTitle());
        return kbRes.ok(model);
    }

    @Override
    public KbEntity<String> updateExpert(
            Integer expertId, String name, String avatar, String description,
            Integer level, Integer rank, String company, Integer source,
            String[] specialCarCodes, String[] specialAreas, String[] certificates,
            String[] idCards, String idCardNumber) {
        if (null != level) {
            ExpertLevel.valueOf(level).orElseThrow(KbException.supplier(KbCode.REQUEST_PARAM_INVALID));
        }
        if (null != rank) {
            ExpertRank.valueOf(rank).orElseThrow(KbException.supplier(KbCode.REQUEST_PARAM_INVALID));
        }
        issueExpertService.expertById(expertId).orElseThrow(KbException.supplier(KbCode.ISSUE_EXPERT_NOT_EXISTS));
        IssueExpert expert = new IssueExpert();
        expert.setId(expertId);
        expert.setAvatar(avatar);
        expert.setUserName(name);
        expert.setCompany(company);
        expert.setLevel(level);
        expert.setRank(rank);
        expert.setDescription(description);
        expert.setSource(source);
        expert.setIdCardNumber(idCardNumber);
        if (null != specialCarCodes && specialCarCodes.length != 0) {
            List<CarBrandModel> specialBrandList = carService
                    .getCarBrandListIn(specialCarCodes)
                    .dataOrThrow();
            expert.setSpecialBrand(specialBrandList.stream()
                    .map(CarBrandModel::getCode)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList()));
        }
        if (null != specialAreas && specialAreas.length != 0) {
            expert.setSpecialArea(Arrays.asList(specialAreas));
        }
        if (null != certificates && certificates.length != 0) {
            expert.setCertificate(Arrays.asList(certificates));
        }
        if (null != idCards && idCards.length != 0) {
            expert.setIdCard(Arrays.asList(idCards));
        }
        issueExpertService.expertUpdate(expert);
        return kbRes.ok();
    }

    @Override
    public KbEntity<IssueExpertApplicationModel> apply(
            Integer userId, String userName, String mobile, Integer channelId,
            String avatar, Integer gender, Integer source, String company, String description,
            String[] specialCarCodes, String[] specialAreas, String[] certificates, String[] idCard, String idCardNumber) {

        ExpertApplicationChannelType channelType = ExpertApplicationChannelType.valueOf(channelId).orElseThrow(KbException.supplier(KbCode.REQUEST_PARAM_INVALID));
        if (!StringUtils.isValidMobile(mobile)) {
            return kbRes.err(KbCode.REQUEST_PARAM_INVALID,
                    "not a valid mobile: " + mobile,
                    "无效的手机号");
        }
        if (!StringUtils.isValidIdCard(idCardNumber)) {
            return kbRes.err(KbCode.REQUEST_PARAM_INVALID,
                    "not a valid idCardNumber: " + idCardNumber,
                    "无效的身份证号");
        }
        if (issueExpertService.applicationExistsByUserAndState(userId, ExpertApplicationState.WAITING)) {
            return kbRes.err(KbCode.ILLEGAL_STATE,
                    "already have active application for user: " + userId,
                    "您已经提交过申请, 请等待审核");
        }
        Optional<UserModel> opUser = userService.getBasic(userId).data();
        if (!opUser.isPresent()) {
            return kbRes.err(KbCode.REQUEST_PARAM_INVALID,
                    "user not exists: " + userId,
                    "用户不存在");
        }
        if (null == idCard || idCard.length < 2) {
            return kbRes.err(KbCode.REQUEST_PARAM_INVALID,
                    "IDCard is requested",
                    "请分开上传身份证照片");
        }
        UserModel user = opUser.get();
        if (user.getRole() != null && user.getRole() != 0) {
            return kbRes.err(KbCode.REQUEST_PARAM_INVALID,
                    "user role already exists: " + user.getRole(),
                    "用户已担任其他特殊角色");
        }
        List<CarBrandModel> specialCarBrands = null;
        if (specialCarCodes != null && specialCarCodes.length > 0) {
            if (specialCarCodes.length > 5) {
                return kbRes.err(KbCode.ILLEGAL_ARGUMENT,
                        "special car should be less than 5",
                        "最多可选择5个车型");
            }
            specialCarBrands = carService
                    .getCarBrandListIn(specialCarCodes)
                    .check().getData().stream()
                    .filter(brand -> brand != null && brand.getCode() != null)
                    .collect(Collectors.toList());
        }

        IssueExpertApplication application = new IssueExpertApplication();
        application.setUserId(userId);
        application.setAvatar(null == avatar ? user.getAvatar() : avatar);
        application.setUserName(null == userName ? user.getUserName() : userName);
        application.setMobile(null == mobile ? user.getMobile() : mobile);
        application.setChannelId(channelType.getValue());
        application.setSex(null == gender ? user.getSex() : gender);
        application.setSource(null == source ? user.getSource() : source);
        application.setCompany(company);
        application.setDescription(description);
        application.setIdCard(Arrays.asList(idCard));
        application.setIdCardNumber(idCardNumber);
        application.setState(ExpertApplicationState.WAITING.getValue());
        if (null != specialCarBrands && specialCarBrands.size() != 0) {
            application.setSpecialBrand(specialCarBrands.stream()
                    .map(CarBrandModel::getCode)
                    .collect(Collectors.toList()));
        }
        if (null != specialAreas && specialAreas.length != 0) {
            application.setSpecialArea(Arrays.asList(specialAreas));
        }
        if (null != certificates && certificates.length != 0) {
            application.setCertificate(Arrays.asList(certificates));
        }
        long current = System.currentTimeMillis() / 1000;
        application.setCreateTime(current);
        application.setUpdateTime(current);
        application = issueExpertService.applicationCreate(application);
        return kbRes.ok(application2model(application));
    }

    @Override
    public KbEntity<IssueExpertModel> applicationAccept(
            Integer userId, String applicationId, Integer level, Integer rank) {
        IssueExpertApplication application = issueExpertService
                .applicationById(applicationId)
                .orElseThrow(()->new KbException(KbCode.RESOURCE_NOT_FOUND).li());
        ExpertLevel expertLevel = ExpertLevel.valueOf(level).orElse(ExpertLevel.LEVEL1);
        ExpertRank expertRank = ExpertRank.valueOf(rank).orElse(ExpertRank.RANK1);
        String[] specialCarCodes =
                application.getSpecialBrand() == null || application.getSpecialBrand().size() == 0 ?
                        null : application.getSpecialBrand().toArray(ArrayTypeHolder.EMPTY_STRING_ARRAY);
        String[] specialAreas =
                application.getSpecialArea() == null || application.getSpecialArea().size() == 0 ?
                        null : application.getSpecialArea().toArray(ArrayTypeHolder.EMPTY_STRING_ARRAY);
        String[] certificates =
                application.getCertificate() == null || application.getCertificate().size() == 0 ?
                        null : application.getCertificate().toArray(ArrayTypeHolder.EMPTY_STRING_ARRAY);
        String[] idCards =
                application.getIdCard() == null || application.getIdCard().size() == 0 ?
                        null : application.getIdCard().toArray(ArrayTypeHolder.EMPTY_STRING_ARRAY);
        IssueExpertModel expert = createExpert(
                application.getUserId(),
                application.getDescription(),
                application.getUserName(),
                application.getAvatar(),
                application.getChannelId(),
                expertRank.getValue(),
                expertLevel.getValue(),
                application.getCompany(),
                specialCarCodes,
                specialAreas,
                certificates,
                idCards,
                application.getIdCardNumber()
        ).dataOrThrow();
        issueExpertService.applicationAccept(userId, applicationId);
        return kbRes.ok(expert);
    }

    @Override
    public KbEntity<String> applicationRefuse(
            Integer userId, String applicationId, String reason) {
        IssueExpertApplication application = issueExpertService
                .applicationById(applicationId)
                .orElseThrow(()->new KbException(KbCode.RESOURCE_NOT_FOUND).li());
        if (application.getState() != ExpertApplicationState.WAITING.getValue()) {
            return kbRes.err(KbCode.ILLEGAL_STATE);
        }
        issueExpertService.applicationRefuse(userId, applicationId, reason);
        return kbRes.ok();
    }

    @Override
    public KbEntity<String> applicationCancel(
            Integer userId, String applicationId) {
        IssueExpertApplication application = issueExpertService
                .applicationById(applicationId)
                .orElseThrow(()->new KbException(KbCode.RESOURCE_NOT_FOUND).li());
        if (!application.getUserId().equals(userId)) {
            return kbRes.err(KbCode.AUTH_NOT_OWNER);
        }
        if (application.getState() != ExpertApplicationState.WAITING.getValue()) {
            return kbRes.err(KbCode.ILLEGAL_STATE);
        }
        issueExpertService.applicationCancel(userId, applicationId);
        return kbRes.ok();
    }

    @Override
    public KbEntity<IssueExpertApplicationModel> getApplicationById(String id) {
        return issueExpertService.applicationById(id)
                .map(this::application2model)
                .map(kbRes::ok)
                .orElse(kbRes.err(KbCode.RESOURCE_NOT_FOUND, "application not exists: " + id));
    }

    @Override
    public KbEntity<List<IssueExpertApplicationModel>> getApplicationListByUserId(
            Integer userId, Integer[] states, Integer page, Integer pageSize) {
        Page<IssueExpertApplication> pageResult = issueExpertService.applicationListByUser(userId, states, page, pageSize);
        List<IssueExpertApplicationModel> list = application2modelList(pageResult.getContent());
        KbEntity<List<IssueExpertApplicationModel>> result = kbRes.ok(list);
        result.setTotalPage((long) pageResult.getTotalPages());
        return result;
    }

    @Override
    public KbEntity<List<IssueExpertApplicationModel>> getApplicationListByMobile(
            String mobile, Integer[] states, Integer page, Integer pageSize) {
        Page<IssueExpertApplication> pageResult = issueExpertService.applicationListByMobile(mobile, states, page, pageSize);
        KbEntity<List<IssueExpertApplicationModel>> result = kbRes.ok(application2modelList(pageResult.getContent()));
        result.setTotalPage((long) pageResult.getTotalPages());
        return result;
    }

    @Override
    public KbEntity<List<IssueExpertApplicationModel>> getApplicationListByMobileOrName(
            String word, Integer[] states, Integer page, Integer pageSize) {
        Page<IssueExpertApplication> pageResult = issueExpertService.applicationListByMobileOrName(word, states, page, pageSize);
        KbEntity<List<IssueExpertApplicationModel>> result = kbRes.ok(application2modelList(pageResult.getContent()));
        result.setTotalPage((long) pageResult.getTotalPages());
        return result;
    }

    @Override
    public KbEntity<List<IssueExpertApplicationModel>> getApplicationList(
            Integer[] states, Integer page, Integer pageSize) {
        Page<IssueExpertApplication> pageResult = issueExpertService.applicationList(states, page, pageSize);
        KbEntity<List<IssueExpertApplicationModel>> result = kbRes.ok(application2modelList(pageResult.getContent()));
        result.setTotalPage((long) pageResult.getTotalPages());
        return result;
    }

    @Override
    public KbEntity<String> expertDelete(Integer expertId, Integer userId, String reason) {
        deletionService.doDeletion("issue_expert", expertId, userId, reason);
        userService.updateUserRole(userId, UserRole.USER.getValue());
        return kbRes.ok();
    }

    @Override
    public KbEntity<IssueExpertModel> expertDeleteRecover(Integer expertId, Integer userId) {
        IssueExpert expert = deletionService.recover("issue_expert", expertId, IssueExpert.class);
        userService.updateUserRole(userId, UserRole.ISSUE_EXPERT.getValue());
        return kbRes.ok(toModel(expert, true));
    }

    @Override
    public KbEntity<List<IssueExpertModel>> getDeletedExpertList(Integer page, Integer pageSize) {
        Page<IssueExpert> expertPage = deletionService.getListByCollection("issue_expert", IssueExpert.class, page, pageSize);
        KbEntity<List<IssueExpertModel>> result = kbRes.ok(toModelList(expertPage.getContent(), true));
        result.setTotalPage((long) expertPage.getTotalPages());
        return result;
    }

    // ---------------------------------------------------

    private IssueExpertModel toModel(IssueExpert expert, boolean allFields) {
        IssueExpertModel model = expert2detail(expert);
        if (expert.getSpecialBrand() != null && !expert.getSpecialBrand().isEmpty()) {
            model.setSpecialBrand(carService
                    .getCarBrandListIn(expert.getSpecialBrand().toArray(new String[0]))
                    .getData());
        }
        if (allFields) {
            if (expert.getSource() != null) {
                model.setLocation(getCityNameBySource(expert.getSource()));
            }
            if (model.getPayLevels() == null || model.getPayLevels().isEmpty()) {
                model.setPayLevels(IssueConsts.ISSUE_PAY_LEVELS_IM_EXPERT);
            }
            if (model.getPhonePayLevels() == null || model.getPhonePayLevels().isEmpty()) {
                model.setPhonePayLevels(IssueConsts.ISSUE_PAY_LEVELS_PHONE_EXPERT);
            }
        }
        return model;
    }

    private List<IssueExpertModel> toModelList(List<IssueExpert> experts) {
        return toModelList(experts, false);
    }

    private List<IssueExpertModel> toModelList(List<IssueExpert> experts, boolean allFields) {
        if (experts.isEmpty()) {
            return Collections.emptyList();
        }
        List<String> brandCodeList = experts.stream()
                .filter(expert -> expert.getSpecialBrand() != null && !expert.getSpecialBrand().isEmpty())
                .flatMap((Function<IssueExpert, Stream<String>>) expert -> expert.getSpecialBrand().stream())
                .collect(Collectors.toList());
        Map<String, CarBrandModel> brands;
        if (brandCodeList.isEmpty()) {
            brands = Collections.emptyMap();
        } else {
            KbEntity<List<CarBrandModel>> result = carService.getCarBrandListIn(brandCodeList.toArray(new String[0]));
            brands = result.getData() == null ?
                    Collections.emptyMap() :
                    result.getData().stream().collect(
                            Collectors.toMap(CarBrandModel::getCode, brand -> brand, (k1, k2) -> k1));
        }
        return experts.stream()
                .map(expert -> {
                    IssueExpertModel model;
                    if (allFields) {
                        model = expert2detail(expert);
                        if (expert.getSource() != null) {
                            model.setLocation(getCityNameBySource(expert.getSource()));
                        }
                        if (model.getPayLevels() == null || model.getPayLevels().isEmpty()) {
                            model.setPayLevels(IssueConsts.ISSUE_PAY_LEVELS_IM_EXPERT);
                        }
                        if (model.getPhonePayLevels() == null || model.getPhonePayLevels().isEmpty()) {
                            model.setPhonePayLevels(IssueConsts.ISSUE_PAY_LEVELS_PHONE_EXPERT);
                        }
                    } else {
                        model = expert2model(expert);
                    }
                    if (expert.getSpecialBrand() != null && !expert.getSpecialBrand().isEmpty()) {
                        model.setSpecialBrand(expert.getSpecialBrand().stream()
                                .map(brands::get)
                                .filter(Objects::nonNull)
                                .collect(Collectors.toList()));
                    }
                    return model;
                })
                .filter(model -> model.getId() != null)
                .collect(Collectors.toList());
    }

    private static IssueExpertModel expert2model(IssueExpert expert) {
        IssueExpertModel model = new IssueExpertModel();
        model.setId(expert.getId());
        model.setUserName(expert.getUserName());
        model.setAvatar(expert.getAvatar());
        model.setSex(expert.getSex());
        model.setLevel(expert.getLevel());
        model.setRank(expert.getRank());
        model.setTitle(ExpertLevel.valueOf(expert.getLevel()).orElse(ExpertLevel.LEVEL1).getTitle());
        model.setPraiseCount(expert.getPraiseCount());
        model.setAnswerCount(expert.getAnswerCount());
        model.setFollowerCount(expert.getFollowerCount());
        return model;
    }

    private static IssueExpertModel expert2detail(IssueExpert expert) {
        IssueExpertModel model = expert2model(expert);
        if (expert.getPayLevels() != null && !expert.getPayLevels().isEmpty()) {
            model.setPayLevels(IssueUtils.payLevel2modelList(expert.getPayLevels()));
        }
        if (expert.getPhonePayLevels() != null && !expert.getPhonePayLevels().isEmpty()) {
            model.setPhonePayLevels(IssueUtils.payLevel2modelList(expert.getPhonePayLevels()));
        }
        model.setRankTitle(ExpertRank.valueOf(expert.getRank()).orElse(ExpertRank.RANK1).getName());
        model.setDescription(expert.getDescription());
        model.setCompany(expert.getCompany());
        model.setSource(expert.getSource());
        model.setRecommend(expert.getRecommend());
        model.setSatisfactionRatio(expert.getSatisfactionRatio());
        model.setQualityRatio(expert.getQualityRatio());
        model.setSpeedRatio(expert.getSpeedRatio());
        model.setIncome(expert.getIncome());
        model.setLoginTime(expert.getLoginTime());
        return model;
    }

    private static IssueExpertModel expert2information(IssueExpert expert) {
        IssueExpertModel model = expert2detail(expert);
        if (expert.getCertificate() != null && !expert.getCertificate().isEmpty()) {
            model.setCertificate(expert.getCertificate());
        }
        if (expert.getIdCard() != null && !expert.getIdCard().isEmpty()) {
            model.setIdCard(expert.getIdCard());
        }
        model.setIdCardNumber(expert.getIdCardNumber());
        return model;
    }

    // ---------------------------------------------------

    private IssueExpertApplicationModel application2model(IssueExpertApplication application) {
        IssueExpertApplicationModel model = Mapper.map(application, IssueExpertApplicationModel.class);
        model.setMessage(ExpertApplicationState
                .valueOf(application.getState())
                .map(ExpertApplicationState::getMessage)
                .orElse(null));
        if (application.getSource() != null) {
            model.setLocation(getCityNameBySource(application.getSource()));
        }
        if (application.getSpecialBrand() != null && application.getSpecialBrand().size() != 0) {
            model.setSpecialBrand(carService
                    .getCarBrandListIn(application.getSpecialBrand().toArray(new String[0]))
                    .check().getData().stream()
                    .filter(brand -> brand != null && brand.getCode() != null)
                    .collect(Collectors.toList()));
        }
        return model;
    }

    private List<IssueExpertApplicationModel> application2modelList(List<IssueExpertApplication> applicationList) {
        if (applicationList.isEmpty()) {
            return Collections.emptyList();
        }
        List<String> brandCodeList = applicationList.stream()
                .filter(app -> app.getSpecialBrand() != null && !app.getSpecialBrand().isEmpty())
                .flatMap((Function<IssueExpertApplication, Stream<String>>) app -> app.getSpecialBrand().stream())
                .collect(Collectors.toList());
        Map<String, CarBrandModel> brands;
        if (brandCodeList.isEmpty()) {
            brands = Collections.emptyMap();
        } else {
            KbEntity<List<CarBrandModel>> result = carService.getCarBrandListIn(brandCodeList.toArray(new String[0]));
            brands = result.getData() == null ?
                    Collections.emptyMap() :
                    result.getData().stream().collect(
                            Collectors.toMap(CarBrandModel::getCode, brand -> brand, (k1, k2) -> k1));
        }
        return applicationList.stream()
                .map(application -> {
                    IssueExpertApplicationModel model = Mapper.map(application, IssueExpertApplicationModel.class);
                    model.setMessage(ExpertApplicationState
                            .valueOf(application.getState())
                            .map(ExpertApplicationState::getMessage)
                            .orElse(null));
                    if (application.getSource() != null) {
                        model.setLocation(getCityNameBySource(application.getSource()));
                    }
                    if (application.getSpecialBrand() != null && !application.getSpecialBrand().isEmpty()) {
                        model.setSpecialBrand(application.getSpecialBrand().stream()
                                .map(brands::get)
                                .filter(Objects::nonNull)
                                .collect(Collectors.toList()));
                    }
                    return model;
                })
                .collect(Collectors.toList());
    }

    private String getCityNameBySource(Integer source) {
        return zoneService.getZoneBySource(source, true)
                .map(ZoneModel::getName)
                .data().orElse("未知地市");
    }

}
