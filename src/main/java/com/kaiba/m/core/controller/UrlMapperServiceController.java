package com.kaiba.m.core.controller;

import com.kaiba.lib.base.assembler.Mapper;
import com.kaiba.lib.base.domain.urlmap.UrlMapCateQueryModel;
import com.kaiba.lib.base.domain.urlmap.UrlMapperCategoryModel;
import com.kaiba.lib.base.domain.urlmap.UrlMapperModel;
import com.kaiba.lib.base.middleware.KbRes;
import com.kaiba.lib.base.response.KbCode;
import com.kaiba.lib.base.response.KbEntity;
import com.kaiba.lib.base.response.KbException;
import com.kaiba.lib.base.service.IUrlMapperCategoryService;
import com.kaiba.lib.base.service.IUrlMapperService;
import com.kaiba.lib.base.service.IUrlMapperVisitService;
import com.kaiba.m.core.domain.urlmap.UrlMapper;
import com.kaiba.m.core.domain.urlmap.UrlMapperCategory;
import com.kaiba.m.core.model.urlmapper.CategoryQueryDTO;
import com.kaiba.m.core.service.urlmap.UrlCounterService;
import com.kaiba.m.core.service.urlmap.UrlMapperCategoryService;
import com.kaiba.m.core.service.urlmap.UrlMapperService;
import java.io.IOException;
import java.net.MalformedURLException;
import java.net.URL;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.PageRequest;
import org.springframework.web.bind.annotation.RestController;

/**
 * author: lyux
 * date: 19-12-27
 */
@Slf4j
@RestController
public class UrlMapperServiceController implements IUrlMapperService, IUrlMapperVisitService,
    IUrlMapperCategoryService {

    private final KbRes kbRes;
    private final String apiHost;
    private final String page404;
    private final UrlMapperService urlMapperService;
    private final UrlCounterService urlCounterService;
    private final UrlMapperCategoryService urlMapperCategoryService;

    public UrlMapperServiceController(
        KbRes kbRes,
        UrlMapperService urlMapperService,
        UrlCounterService urlCounterService,
        UrlMapperCategoryService urlMapperCategoryService,
        @Value("${kb.host.api}") String apiHost,
        @Value("${kb.host.page}") String pageHost
    ) {
        this.kbRes = kbRes;
        this.urlMapperService = urlMapperService;
        this.urlCounterService = urlCounterService;
        this.urlMapperCategoryService = urlMapperCategoryService;
        this.apiHost = apiHost;
        this.page404 = "https://" + pageHost + "/404";
    }

    @Override
    public KbEntity<Void> visit(String key, HttpServletRequest request,
        HttpServletResponse response) {
        String url = urlCounterService.visitActualUrl(key, request);
        //log.info("redirect " + key + " -> " + url);
        if (null == url) {
            url = page404;
        }
        try {
            response.sendRedirect(url);
        } catch (IOException e) {
            throw new KbException(KbCode.REQUEST_FAIL, e);
        }
        return kbRes.ok();
    }

    @Override
    public KbEntity<Void> recordVisit(String key, HttpServletRequest request) {
        if (null != key) {
            urlCounterService.recordVisitByKey(key, request);
            return kbRes.ok();
        } else {
            return kbRes.err(KbCode.REQUEST_PARAM_INVALID);
        }
    }

    @Override
    public KbEntity<UrlMapperModel> createUrlMapper(String url, Integer siteId,
        Boolean needDecorate, Long uvEndTime) {
        if (null == url) {
            return kbRes.err(KbCode.REQUEST_PARAM_INVALID, "url null");
        }
        {
            UrlMapper urlMapper = urlMapperService.getUrlMapperByUrl(url).orElse(null);
            if (urlMapper != null) {
                if (urlMapper.getNeedDecorate().equals(needDecorate)) {
                    if (uvEndTime != null && uvEndTime.equals(urlMapper.getUvEndTime())) {
                        return updateUrlMapper(urlMapper.getKey(), null, needDecorate, uvEndTime);
                    } else {
                        return kbRes.ok(mapper2model(urlMapper));
                    }
                } else {
                    return kbRes.err(KbCode.REQUEST_PARAM_INVALID,
                        "url already exist: " + url,
                        "链接映射已存在, 且配置不同");
                }
            }
        }

        if (null == uvEndTime) {
            uvEndTime = (System.currentTimeMillis() / 1000) + TimeUnit.DAYS.toSeconds(45);
        }
        try {
            URL u = new URL(url);
            if (null == needDecorate) {
                needDecorate =
                    u.getHost().endsWith("kaiba315.com.cn") || u.getHost().endsWith("kaiba315.com");
            }
            if (UrlMapperService.isMapperUrl(u)) {
                return kbRes.err(KbCode.REQUEST_PARAM_INVALID,
                    "url mapper maybe in loop: " + url,
                    "链接存在循环映射风险");
            }
        } catch (MalformedURLException e) {
            return kbRes.err(KbCode.REQUEST_PARAM_INVALID, "malformed url: " + url, "链接格式错误");
        }

        UrlMapper urlMapper = urlMapperService.createUrlMapper(url, siteId, needDecorate,
            uvEndTime);
        log.info("create url mapper: " + urlMapper);
        return kbRes.ok(mapper2model(urlMapper));
    }

    @Override
    public KbEntity<UrlMapperModel> createMapperByCategory(String categoryId, String name,
        String url) {
        UrlMapper urlMapper;
        UrlMapperCategory category = urlMapperCategoryService.getCategoryById(categoryId);
        if (Objects.isNull(category)) {
            return kbRes.err(KbCode.RESOURCE_NOT_FOUND,
                "mapper url category not exist categoryId: " + categoryId, "分类不存在");
        }
        if (StringUtils.isBlank(url)) {
            urlMapper = urlMapperService.createUrlMapper(category.getUrl(), name, categoryId, category.getUvEndTime());
        } else {
            urlMapper = urlMapperService.createUrlMapper(url, name, categoryId, category.getUvEndTime());
        }
        log.info("create url mapper: " + urlMapper);
        return kbRes.ok(mapper2model(urlMapper));
    }

    @Override
    public KbEntity<UrlMapperModel> updateUrlMapper(String key, String url, Boolean needDecorate,
        Long uvEndTime) {
        UrlMapper urlMapper;
        if (key != null) {
            urlMapper = urlMapperService.updateSettingsByKey(key, needDecorate, uvEndTime);
        } else if (url != null) {
            urlMapper = urlMapperService.updateSettingsByUrl(url, needDecorate, uvEndTime);
        } else {
            return kbRes.err(KbCode.REQUEST_PARAM_INVALID,
                "key and url null", "key 和 url 必须提供一个");
        }
        log.info("update url mapper: " + urlMapper);
        if (urlMapper == null) {
            return kbRes.err(KbCode.RESOURCE_NOT_FOUND,
                "mapper not found", "找不到相关映射");
        } else {
            urlCounterService.invalidateCache(urlMapper.getKey());
            return kbRes.ok(mapper2model(urlMapper));
        }
    }

    @Override
    public KbEntity<UrlMapperModel> updateUrlMapperBasicInfo(String mapperId, String name,
        Integer state, String oriUrl) {
        UrlMapper urlMapper = urlMapperService.getUrlMapperById(mapperId);
        log.info("update url mapper: " + urlMapper);
        if (Objects.isNull(urlMapper)) {
            return kbRes.err(KbCode.RESOURCE_NOT_FOUND, "mapper not found", "找不到相关映射");
        } else {
            urlMapperService.updateBasicInfoById(mapperId, name, state, oriUrl);
            urlCounterService.invalidateCache(urlMapper.getKey());
            return kbRes.ok(mapper2model(urlMapper));
        }
    }

    @Override
    public KbEntity<List<UrlMapperModel>> getListByCategory(String categoryId, Integer page,
        Integer pageSize) {
        return kbRes.ok(urlMapperService.getUrlMappersByCategoryId(categoryId, page, pageSize)
            .map(this::mapper2model));
    }

    @Override
    public KbEntity<List<UrlMapperModel>> getList(Integer siteId, Integer page, Integer pageSize) {
        return kbRes.ok(urlMapperService
            .getUrlMapperPage(siteId, page, pageSize)
            .map(this::mapper2model));
    }

    @Override
    public KbEntity<List<UrlMapperModel>> getListInIds(String[] ids) {
        return kbRes.ok(urlMapperService
            .getUrlMapperListInIds(ids).stream()
            .map(this::mapper2model)
            .collect(Collectors.toList()));
    }

    @Override
    public KbEntity<List<UrlMapperModel>> getListInKeys(String[] keys) {
        return kbRes.ok(urlMapperService
            .getUrlMapperListInKeys(keys).stream()
            .map(this::mapper2model)
            .collect(Collectors.toList()));
    }

    @Override
    public KbEntity<List<UrlMapperModel>> getListInUrls(String[] urls) {
        return kbRes.ok(urlMapperService
            .getUrlMapperListInUrls(urls).stream()
            .map(this::mapper2model)
            .collect(Collectors.toList()));
    }

    @Override
    public KbEntity<UrlMapperModel> getByUrl(String url) {
        if (null == url) {
            return kbRes.err(KbCode.REQUEST_PARAM_INVALID, "url null");
        }
        return urlMapperService.getUrlMapperByUrl(url)
            .map(this::mapper2model)
            .map(kbRes::ok)
            .orElse(kbRes.err(KbCode.RESOURCE_NOT_FOUND, "mapper not found for url " + url));
    }

    @Override
    public KbEntity<UrlMapperModel> getByKey(String key) {
        if (null == key) {
            return kbRes.err(KbCode.REQUEST_PARAM_INVALID, "key null");
        }
        return urlMapperService.getUrlMapperByKey(key)
            .map(this::mapper2model)
            .map(kbRes::ok)
            .orElse(kbRes.err(KbCode.RESOURCE_NOT_FOUND, "mapper not found for key " + key));
    }

    @Override
    public KbEntity<Void> flushCountData() {
        urlCounterService.flush();
        return kbRes.ok();
    }

    @Override
    public KbEntity<Void> increasePv(String key, Long pv) {
        if (null == key) {
            return kbRes.err(KbCode.REQUEST_PARAM_INVALID, "key null");
        }
        urlMapperService.increasePv(key, pv == null ? 0 : pv, 0, 0, 0, 0);
        return kbRes.ok();
    }

    @Override
    public KbEntity<Void> updateUv(String key, Long cidUv, Long uidUv, Long ipUv) {
        if (null == key) {
            return kbRes.err(KbCode.REQUEST_PARAM_INVALID, "key null");
        }
        urlMapperService.updateUv(key,
            cidUv == null ? 0 : cidUv,
            uidUv == null ? 0 : uidUv,
            ipUv == null ? 0 : ipUv);
        return kbRes.ok();
    }

    private UrlMapperModel mapper2model(UrlMapper urlMapper) {
        UrlMapperModel model = Mapper.map(urlMapper, UrlMapperModel.class);
        model.setMapUrl("https://" + apiHost + "/p/" + urlMapper.getKey());
        // 如果不可用，返回404页面
        if (Objects.equals(urlMapper.getState(), 0)) {
            model.setMapUrl(page404);
        }
        return model;
    }

    @Override
    public KbEntity<UrlMapperCategoryModel> createUrlMapperCategory(String url, String name,
        String channelKey, Long uvEndTime, Integer count) {
        try {
            UrlMapperCategory category = urlMapperCategoryService.createUrlMapperCategory(url, name,
                channelKey, uvEndTime, count);
            return kbRes.ok(Mapper.map(category, UrlMapperCategoryModel.class));
        } catch (Exception e) {
            return kbRes.err(KbCode.ERROR, "createUrlMapperCategory error", e.getMessage());
        }
    }

    @Override
    public KbEntity<UrlMapperCategoryModel> updateUrlMapperCategory(String categoryId, String url,
        String channelKey, String name, Long uvEndTime, Boolean syncUrl) {
        try {
            UrlMapperCategory category = urlMapperCategoryService.updateCategoryUrl(categoryId, url,
                channelKey, name, uvEndTime, syncUrl);
            return kbRes.ok(Mapper.map(category, UrlMapperCategoryModel.class));
        } catch (Exception e) {
            return kbRes.err(KbCode.ERROR, "updateCategoryUrl error", e.getMessage());
        }
    }

    @Override
    public KbEntity<UrlMapperCategoryModel> deleteCategory(String categoryId,
        Boolean deleteMapper) {
        try {
            urlMapperCategoryService.deleteCategory(categoryId, deleteMapper);
            return kbRes.ok();
        } catch (Exception e) {
            return kbRes.err(KbCode.ERROR, "deleteCategory error", e.getMessage());
        }
    }

    @Override
    public KbEntity<List<UrlMapperCategoryModel>> queryCategoryByCondition(UrlMapCateQueryModel queryModel) {
        CategoryQueryDTO queryDTO = Mapper.map(queryModel, CategoryQueryDTO.class);
        int p = queryModel.getPage() == null ? 0 : queryModel.getPage() - 1;
        int ps = queryModel.getPageSize() == null ? 100 : queryModel.getPageSize();
        queryDTO.setPageable(PageRequest.of(p, ps));
        return kbRes.ok(urlMapperCategoryService.findAllByCondition(queryDTO)
            .map(a -> Mapper.map(a, UrlMapperCategoryModel.class)));
    }
}
