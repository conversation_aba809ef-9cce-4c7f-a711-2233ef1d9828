package com.kaiba.m.core.controller.kri;

import com.kaiba.lib.base.assembler.Mapper;
import com.kaiba.lib.base.constant.kri.KRIMatcherFact;
import com.kaiba.lib.base.domain.kri.KbResource;
import com.kaiba.lib.base.domain.kri.matcher.KRIExistMatcherModel;
import com.kaiba.lib.base.domain.kri.matcher.KRIMatchResult;
import com.kaiba.lib.base.domain.kri.matcher.KRIMatcherModifyModel;
import com.kaiba.lib.base.domain.kri.matcher.KRIValueMatcherModel;
import com.kaiba.lib.base.middleware.KbRes;
import com.kaiba.lib.base.response.KbCode;
import com.kaiba.lib.base.response.KbEntity;
import com.kaiba.lib.base.response.KbException;
import com.kaiba.lib.base.service.IKRIMatcherService;
import com.kaiba.m.core.domain.kri.matcher.KRIExistMatcher;
import com.kaiba.m.core.domain.kri.matcher.KRIValueMatcher;
import com.kaiba.m.core.service.kri.matcher.MatcherMatchService;
import com.kaiba.m.core.service.kri.matcher.MatcherPersistService;
import com.kaiba.m.core.service.kri.matcher.MatcherUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Objects;

/**
 * author: lyux
 * date: 2024-12-19
 */
@Slf4j
@RestController
public class KRIMatcherServiceController implements IKRIMatcherService {

    private final KbRes kbRes;
    private final MatcherMatchService matchService;
    private final MatcherPersistService persistService;

    public KRIMatcherServiceController(
            KbRes kbRes,
            MatcherMatchService matchService,
            MatcherPersistService persistService
    ) {
        this.kbRes = kbRes;
        this.matchService = matchService;
        this.persistService = persistService;
    }

    @Override
    public KbEntity<KRIMatchResult> matchByFactAndKRI(
            String fact, String biz, String unit, String ref1, String ref2, String ref3) {
        KbResource res = new KbResource(biz, unit, ref1, ref2, ref3);
        return matchService.matchByFact(res, fact)
                .map(data -> {
                    String d = MatcherUtil.DATA_EMPTY.equals(data) ? null : data;
                    return new KRIMatchResult(fact, d, true);
                })
                .map(kbRes::ok)
                .orElseGet(() -> kbRes.ok(new KRIMatchResult(fact, null, false)));
    }

    @Override
    public KbEntity<KRIValueMatcherModel> createValueMatcher(KRIMatcherModifyModel model) {
        if (model == null || model.getBiz() == null) {
            return kbRes.msg(KbCode.REQUEST_PARAM_MISSING, "缺少 biz");
        }
        KRIMatcherFact fact = KRIMatcherFact.resolveByName(model.getFact()).orElseThrow(
                () -> new KbException(KbCode.REQUEST_PARAM_INVALID).r("未知的事实类型").li());
        KRIValueMatcher created = persistService.upsertValueMatcher(model.obtainKbResource(), fact, model.getData());
        return kbRes.ok(Mapper.map(created, KRIValueMatcherModel.class));
    }

    @Override
    public KbEntity<KRIValueMatcherModel> updateValueMatcher(KRIMatcherModifyModel model) {
        KbResource resource = model.obtainKbResource();
        KRIMatcherFact fact = KRIMatcherFact.resolveByName(model.getFact()).orElseThrow(
                () -> new KbException(KbCode.REQUEST_PARAM_INVALID).r("未知的事实类型").li());
        KRIValueMatcher matcher = persistService.getValueMatcherByFact(resource, model.getFact()).orElse(null);
        if (matcher == null) {
            return kbRes.msg(KbCode.RESOURCE_NOT_FOUND, "匹配器不存在");
        } else if (Objects.equals(matcher.getData(), model.getData())) {
            return kbRes.ok(Mapper.map(matcher, KRIValueMatcherModel.class));
        } else {
            KRIValueMatcher updated = persistService.upsertValueMatcher(resource, fact, model.getData());
            return kbRes.ok(Mapper.map(updated, KRIValueMatcherModel.class));
        }
    }

    @Override
    public KbEntity<Void> deleteValueMatcherById(String matcherId) {
        KRIValueMatcher matcher = persistService.getValueMatcherById(matcherId).orElse(null);
        if (matcher == null) {
            return kbRes.msg(KbCode.RESOURCE_NOT_FOUND, "匹配器不存在");
        } else {
            persistService.deleteValueMatcherById(matcherId);
            return kbRes.ok();
        }
    }

    @Override
    public KbEntity<KRIValueMatcherModel> getValueMatcherByFactAndKRI(
            String fact, String biz, String unit, String ref1, String ref2, String ref3) {
        KbResource res = new KbResource(biz, unit, ref1, ref2, ref3);
        return persistService.getValueMatcherByFact(res, fact)
                .map(matcher -> Mapper.map(matcher, KRIValueMatcherModel.class))
                .map(kbRes::ok)
                .orElseGet(() -> kbRes.err(KbCode.RESOURCE_NOT_FOUND));
    }

    @Override
    public KbEntity<List<KRIValueMatcherModel>> getValueMatcherListByFact(
            String fact, String biz, String unit, String ref1, String ref2, Integer page, Integer pageSize) {
        KbResource res = new KbResource(biz, unit, ref1, ref2, null);
        return kbRes.ok(persistService.getValueMatcherPageByFact(res, fact, page, pageSize)
                .map(matcher -> Mapper.map(matcher, KRIValueMatcherModel.class)));
    }

    @Override
    public KbEntity<List<KRIValueMatcherModel>> getValueMatcherListByKRI(
            String biz, String unit, String ref1, String ref2, String ref3, Integer page, Integer pageSize) {
        KbResource res = new KbResource(biz, unit, ref1, ref2, ref3);
        return kbRes.ok(persistService.getValueMatcherPageByFact(res, null, page, pageSize)
                .map(matcher -> Mapper.map(matcher, KRIValueMatcherModel.class)));
    }

    @Override
    public KbEntity<KRIExistMatcherModel> createExistMatcher(KRIMatcherModifyModel model) {
        if (model == null || model.getBiz() == null) {
            return kbRes.msg(KbCode.REQUEST_PARAM_MISSING, "缺少 biz");
        }
        KRIMatcherFact fact = KRIMatcherFact.resolveByName(model.getFact()).orElseThrow(
                () -> new KbException(KbCode.REQUEST_PARAM_INVALID).r("未知的事实类型").li());
        KRIExistMatcher created = persistService.upsertExistMatcher(model.obtainKbResource(), fact, model.getData());
        return kbRes.ok(Mapper.map(created, KRIExistMatcherModel.class));
    }

    @Override
    public KbEntity<KRIExistMatcherModel> updateExistMatcher(KRIMatcherModifyModel model) {
        KbResource resource = model.obtainKbResource();
        KRIMatcherFact fact = KRIMatcherFact.resolveByName(model.getFact()).orElseThrow(
                () -> new KbException(KbCode.REQUEST_PARAM_INVALID).r("未知的事实类型").li());
        KRIExistMatcher matcher = persistService.getExistMatcherByFact(resource, model.getFact()).orElse(null);
        if (matcher == null) {
            return kbRes.msg(KbCode.RESOURCE_NOT_FOUND, "匹配器不存在");
        } else if (Objects.equals(matcher.getData(), model.getData())) {
            return kbRes.ok(Mapper.map(matcher, KRIExistMatcherModel.class));
        } else {
            KRIExistMatcher updated = persistService.upsertExistMatcher(resource, fact, model.getData());
            return kbRes.ok(Mapper.map(updated, KRIExistMatcherModel.class));
        }
    }

    @Override
    public KbEntity<Void> deleteExistMatcherById(String matcherId) {
        KRIExistMatcher matcher = persistService.getExistMatcherById(matcherId).orElse(null);
        if (matcher == null) {
            return kbRes.msg(KbCode.RESOURCE_NOT_FOUND, "匹配器不存在");
        } else {
            persistService.deleteExistMatcherById(matcherId);
            return kbRes.ok();
        }
    }

    @Override
    public KbEntity<KRIExistMatcherModel> getExistMatcherByFactAndKRI(
            String fact, String biz, String unit, String ref1, String ref2) {
        KbResource res = new KbResource(biz, unit, ref1, ref2, null);
        return persistService.getExistMatcherByFact(res, fact)
                .map(matcher -> Mapper.map(matcher, KRIExistMatcherModel.class))
                .map(kbRes::ok)
                .orElseGet(() -> kbRes.err(KbCode.RESOURCE_NOT_FOUND));
    }

    @Override
    public KbEntity<List<KRIExistMatcherModel>> getExistMatcherListByFact(
            String fact, String biz, String unit, String ref1, Integer page, Integer pageSize) {
        KbResource res = new KbResource(biz, unit, ref1, null, null);
        return kbRes.ok(persistService.getValueMatcherPageByFact(res, fact, page, pageSize)
                .map(matcher -> Mapper.map(matcher, KRIExistMatcherModel.class)));
    }

    @Override
    public KbEntity<List<KRIExistMatcherModel>> getExistMatcherListByKRI(
            String biz, String unit, String ref1, String ref2, Integer page, Integer pageSize) {
        KbResource res = new KbResource(biz, unit, ref1, ref2, null);
        return kbRes.ok(persistService.getValueMatcherPageByFact(res, null, page, pageSize)
                .map(matcher -> Mapper.map(matcher, KRIExistMatcherModel.class)));
    }
}
