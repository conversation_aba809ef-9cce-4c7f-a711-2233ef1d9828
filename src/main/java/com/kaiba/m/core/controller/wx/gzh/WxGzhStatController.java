package com.kaiba.m.core.controller.wx.gzh;

import com.kaiba.lib.base.middleware.KbRes;
import com.kaiba.lib.base.response.KbEntity;
import com.kaiba.m.core.service.wx.gzh.WxGzhStatService;

import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2024/09/13 10:33
 **/
@Slf4j
@RestController
public class WxGzhStatController {

    private final KbRes kbRes;
    private final WxGzhStatService statService;

    public WxGzhStatController(
            KbRes kbRes,
            WxGzhStatService statService
    ) {
        this.kbRes = kbRes;
        this.statService = statService;
    }

    @PostMapping("/datav_tmp/gzh/getStatistic")
    public KbEntity<Object> getGzhStatistic() {
        return kbRes.ok(statService.getGzhStatistic(CacheType.ALL.name()));
    }

    @PostMapping("/datav_tmp/gzh/getGenderStatistic")
    public KbEntity<Map<String, String>> getGzhGenderStatistic() {
        return kbRes.ok(statService.getStatMap(CacheType.GENDER.name()));
    }

    @PostMapping("/datav_tmp/gzh/getAgeStatistic")
    public KbEntity<Map<String, String>> getGzhAgeStatistic() {
        return kbRes.ok(statService.getStatMap(CacheType.AGE.name()));
    }

    @PostMapping("/datav_tmp/gzh/saveGenderStatistic")
    public KbEntity<Void> saveGzhGenderStatistic(@RequestBody Map<String, String> data) {
        statService.cacheStatMap(CacheType.GENDER.name(), data);
        return kbRes.ok();
    }

    @PostMapping("/datav_tmp/gzh/saveAgeStatistic")
    public KbEntity<Void> saveGzhAgeStatistic(@RequestBody Map<String, String> data) {
        statService.cacheStatMap(CacheType.AGE.name(), data);
        return kbRes.ok();
    }

    @PostMapping("/datav_tmp/gzh/refreshStatisticByManager")
    public KbEntity<Void> refreshStatisticByManager() {
        statService.refreshGzhStatCache(CacheType.ALL.name());
        return kbRes.ok();
    }

    @Getter
    private enum CacheType {
        REPLY, REALTIME_MSG, GENDER, AGE, ALL
    }
}
