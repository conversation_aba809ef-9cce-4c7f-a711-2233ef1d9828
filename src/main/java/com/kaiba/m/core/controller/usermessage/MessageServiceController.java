package com.kaiba.m.core.controller.usermessage;

import com.kaiba.lib.base.constant.usermessage.UserMessageType;
import com.kaiba.lib.base.domain.user.UserMessageCreateModel;
import com.kaiba.lib.base.domain.user.UserMessageModel;
import com.kaiba.lib.base.middleware.KbRes;
import com.kaiba.lib.base.response.KbCode;
import com.kaiba.lib.base.response.KbEntity;
import com.kaiba.lib.base.response.KbException;
import com.kaiba.lib.base.service.IMessageService;
import com.kaiba.lib.base.util.appaction.AppActionType;
import com.kaiba.m.core.middleware.amqpsender.UserMessageEventSender;
import com.kaiba.m.core.service.message.MessageServiceFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * author: lyux
 * date: 18-9-28
 */
@RestController
public class MessageServiceController implements IMessageService {

    private final KbRes kbRes;
    private final MessageServiceFactory messageServiceFactory;
    private final UserMessageEventSender messageEventSender;

    @Autowired
    public MessageServiceController(KbRes kbRes,
                                    MessageServiceFactory messageServiceFactory,
                                    UserMessageEventSender messageEventSender) {
        this.kbRes = kbRes;
        this.messageServiceFactory = messageServiceFactory;
        this.messageEventSender = messageEventSender;
    }

    @Override
    public KbEntity<List<UserMessageModel>> getUnreadMessages(
            Integer userId, Integer endpoint, String lastId, Integer pageSize) {
        return kbRes.ok(messageServiceFactory
                .getMessageService(endpoint)
                .orElseThrow(() -> new KbException(KbCode.NOT_SUPPORT_YET))
                .getMessageList(userId, lastId, pageSize));
    }

    @Override
    public KbEntity<List<UserMessageModel>> getHistoryMessages(
            Integer userId, Integer[] typeList, Integer endpoint, String lastId, Integer pageSize) {
        return kbRes.ok(messageServiceFactory
                .getMessageService(endpoint)
                .orElseThrow(() -> new KbException(KbCode.NOT_SUPPORT_YET).li())
                .getHistoryMessageList(userId, typeList, lastId, pageSize));
    }

    @Override
    public KbEntity<String> getLastReadId(Integer userId, Integer endpoint) {
        return kbRes.ok(messageServiceFactory
                .getMessageService(endpoint)
                .orElseThrow(() -> new KbException(KbCode.NOT_SUPPORT_YET).li())
                .getReadMarker(userId));
    }

    @Override
    public KbEntity<UserMessageModel> sendMessage(
            Integer userId, Integer type,
            String title, String subTitle,
            String extra, String referenceId) {
        UserMessageType messageType = UserMessageType.valueOf(type)
                .orElseThrow(() -> new KbException(KbCode.REQUEST_PARAM_INVALID).li());
        return kbRes.ok(messageEventSender.on(userId, messageType)
                .title(title, subTitle)
                .extra(extra)
                .referenceId(referenceId)
                .send());
    }

    @Override
    public KbEntity<UserMessageModel> sendMessageByBody(UserMessageCreateModel model) {
        UserMessageType messageType = UserMessageType.valueOf(model.getType())
                .orElseThrow(() -> new KbException(KbCode.REQUEST_PARAM_INVALID).li());
        AppActionType actionType = null;
        if (model.getAction() != null) {
            actionType = AppActionType.valueOfAction(model.getAction());
        }
        return kbRes.ok(messageEventSender.on(model.getUserId(), messageType)
                .title(model.getTitle(), model.getSubTitle())
                .action(actionType)
                .actionParams(model.getActionParams())
                .sendOrThrow());
    }

}
