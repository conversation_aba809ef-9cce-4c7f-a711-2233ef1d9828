package com.kaiba.m.core.controller.user;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.event.SyncReadListener;
import com.kaiba.lib.base.assembler.Mapper;
import com.kaiba.lib.base.constant.KbHttpHeaders;
import com.kaiba.lib.base.constant.user.UserRosterVipLevel;
import com.kaiba.lib.base.domain.user.UserRosterModel;
import com.kaiba.lib.base.domain.user.UserRosterOrganizationModel;
import com.kaiba.lib.base.domain.user.UserRosterPermissionModel;
import com.kaiba.lib.base.domain.user.UserRosterSensitiveFlag;
import com.kaiba.lib.base.middleware.KbRes;
import com.kaiba.lib.base.response.KbCode;
import com.kaiba.lib.base.response.KbEntity;
import com.kaiba.lib.base.service.IUserRosterService;
import com.kaiba.lib.base.util.ServletRequestUtils;
import com.kaiba.lib.base.util.StringUtils;
import com.kaiba.m.core.domain.user.User;
import com.kaiba.m.core.service.user.UserRosterExcelModel;
import com.kaiba.m.core.service.user.UserRosterService;
import com.kaiba.m.core.service.user.UserService;
import io.swagger.annotations.ApiOperation;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version UserRosterServiceController, v0.1 2023/11/28 17:26 daopei Exp $
 **/
@Slf4j
@RequiredArgsConstructor
@RestController
public class UserRosterServiceController implements IUserRosterService {

    @NonNull
    private final UserRosterService userRosterService;

    @NonNull
    private final KbRes kbRes;

    @NonNull
    private final UserService userService;


    @Override
    public KbEntity<UserRosterModel> getById(String id) {
        return userRosterService.getById(id)
                .map(user -> Mapper.map(user, UserRosterModel.class))
                .map(kbRes::ok)
                .orElse(kbRes.err(KbCode.RESOURCE_NOT_FOUND,"未找到对应花名册用户"));
    }

    @Override
    public KbEntity<UserRosterModel> getByMobileAndInstanceKey(String mobile, String instanceKey) {
        return userRosterService.getByMobileAndInstance(mobile, instanceKey)
                .map(user -> Mapper.map(user, UserRosterModel.class))
                .map(kbRes::ok)
                .orElse(kbRes.err(KbCode.RESOURCE_NOT_FOUND,null,"未找到对应花名册用户"));
    }

    @Override
    public KbEntity<UserRosterModel> getByUserIdAndInstanceKey(Integer userId, String instanceKey) {
        return userRosterService.getByUserIdAndInstance(userId, instanceKey)
                .map(user -> Mapper.map(user, UserRosterModel.class))
                .map(kbRes::ok)
                .orElse(kbRes.err(KbCode.RESOURCE_NOT_FOUND,null,"未找到对应花名册用户"));    }

    @Override
    public KbEntity<List<UserRosterModel>> getByUserIdInAndInstanceKey(List<Integer> userIdList, String instanceKey) {
        return kbRes.ok(
                userRosterService.getByBindUserIdInAndInstanceKey(userIdList, instanceKey)
                        .stream().map(user -> Mapper.map(user, UserRosterModel.class)).collect(Collectors.toList()));
    }

    @Override
    public KbEntity<List<UserRosterModel>> getPageByInstanceKey(String instanceKey, Integer userId, String userName, String mobile, Integer state, String organizationId, Long sensitiveFlag, Integer page, Integer pageSize) {
        UserRosterSensitiveFlag flag = sensitiveFlag == null ? UserRosterSensitiveFlag.STRICT : new UserRosterSensitiveFlag(sensitiveFlag);
        return kbRes.ok(
                userRosterService.getPageByInstanceKey(instanceKey, userId, userName, mobile, state, organizationId, page, pageSize)
                        .map(user -> Mapper.map(user, UserRosterModel.class))
                        .map(user -> userSensitiveFilter(user, flag))
        );
    }

    @Override
    public KbEntity<UserRosterModel> updateVipLevel(String id, Integer vipLevel) {
        return kbRes.ok(Mapper.map(userRosterService.updateVipLevel(id, vipLevel), UserRosterModel.class));
    }

    @Override
    public KbEntity<UserRosterModel> updateState(String id, Integer state) {
        return kbRes.ok(Mapper.map(userRosterService.updateState(id, state), UserRosterModel.class));
    }

    @Override
    public KbEntity<UserRosterModel> updateBindUser(String id, Integer bindUserId) {
        return kbRes.ok(Mapper.map(userRosterService.updateBindUser(id, bindUserId), UserRosterModel.class));
    }

    @Override
    public KbEntity<UserRosterModel> createUser(UserRosterModel user) {
        return kbRes.ok(Mapper.map(userRosterService.createUser(user), UserRosterModel.class));
    }

    @Override
    public KbEntity<UserRosterModel> updateUser(UserRosterModel user) {
        return kbRes.ok(Mapper.map(userRosterService.updateUser(user), UserRosterModel.class));
    }

    @Override
    public KbEntity<Void> createUserBatch(List<UserRosterModel> userList) {
        userRosterService.createUserBatch(userList);
        return kbRes.ok();
    }

    // --------------- 单位管理 -------------


    @Override
    public KbEntity<List<UserRosterOrganizationModel>> getOrgPageByInstanceKey(String instanceKey, Integer state, Integer page, Integer pageSize) {
        return kbRes.ok(userRosterService.getOrgPageByInstanceKey(instanceKey, state, page, pageSize)
                .map(model -> Mapper.map(model, UserRosterOrganizationModel.class)));
    }

    @Override
    public KbEntity<UserRosterOrganizationModel> getByNameAndInstanceKey(String instanceKey, String name) {
        return userRosterService.getOrgByNameAndInstanceKey(name, instanceKey)
                .map(model -> Mapper.map(model, UserRosterOrganizationModel.class))
                .map(kbRes::ok)
                .orElse(kbRes.err(KbCode.RESOURCE_NOT_FOUND, "未找到对应单位"));
    }

    @Override
    public KbEntity<UserRosterOrganizationModel> createOrganization(UserRosterOrganizationModel model) {
        return kbRes.ok(Mapper.map(userRosterService.createOrganization(model), UserRosterOrganizationModel.class));
    }

    @Override
    public KbEntity<UserRosterOrganizationModel> updateOrganization(UserRosterOrganizationModel model) {
        return kbRes.ok(Mapper.map(userRosterService.updateOrganization(model), UserRosterOrganizationModel.class));
    }

    @Override
    public KbEntity<Void> saveUserPermission(String rosterId, List<String> menuId) {
        Integer userId = KbHttpHeaders.KB_USER_ID.getValidIntegerHeaderOrNull(ServletRequestUtils.getCurrentRequest());
        userRosterService.saveUserPermission(rosterId, menuId, userId);
        return kbRes.ok();
    }

    @Override
    public KbEntity<List<String>> getPermissionByUser(String rosterId) {
        return kbRes.ok(userRosterService.getPermissionByUser(rosterId));
    }

    @Override
    public KbEntity<List<UserRosterPermissionModel>> getPermissionByUsers(List<String> rosterIds) {
        return kbRes.ok(
                userRosterService.getPermissionByUsers(rosterIds)
                        .stream()
                        .map(m -> Mapper.map(m, UserRosterPermissionModel.class))
                        .collect(Collectors.toList()));
    }

    @Override
    public KbEntity<Void> addPermission(List<UserRosterPermissionModel> permission) {
        Integer userId = KbHttpHeaders.KB_USER_ID.getValidIntegerHeaderOrNull(ServletRequestUtils.getCurrentRequest());
        userRosterService.addPermission(permission, userId);
        return kbRes.ok();
    }

    @Override
    public KbEntity<Void> deletePermission(List<UserRosterPermissionModel> permission) {
        Integer userId = KbHttpHeaders.KB_USER_ID.getValidIntegerHeaderOrNull(ServletRequestUtils.getCurrentRequest());
        userRosterService.deletePermission(permission, userId);
        return kbRes.ok();
    }


    @ApiOperation("导入花名册用户")
    @PostMapping(path = "/manage/importUser")
    KbEntity<Void> importUser(@RequestParam MultipartFile file) {
        SyncReadListener readListener = new SyncReadListener();
        try {
            EasyExcel.read(file.getInputStream(), UserRosterExcelModel.class, readListener).sheet().doRead();
            List<UserRosterModel> userRoster = readListener.getList()
                    .stream()
                    .map(e -> (UserRosterExcelModel)e)
                    .map(UserRosterServiceController::mapUserRoster)
                    .peek(user -> {
                        Integer userId = userService.getBasicByMobile(user.getMobile()).map(User::getId).orElse(null);
                        user.setBindUserId(userId);
                    })
                    .collect(Collectors.toList());
            log.info("import user roster size:{}", userRoster.size());
            createUserBatch(userRoster);
        } catch (IOException e) {
            log.error("user roster excel import error, message:{}", e.getMessage());
            return kbRes.err(KbCode.REQUEST_FAIL);
        }
        return kbRes.ok();
    }


    private UserRosterModel userSensitiveFilter(UserRosterModel user, UserRosterSensitiveFlag flag) {
        //vip用户默认全局不展示手机号,且不受配置影响
        if (flag.isMaskMobile() || user.getVipLevel() != null) {
            user.setMobile(mobileMask(user.getMobile()));
        }
        if (flag.isMaskUserId() || user.getVipLevel() != null) {
            user.setBindUserId(userIdMask(user));
        }
        return user;
    }

    private static String mobileMask(String mobile) {
        return StringUtils.replace(mobile, 3, 7, "*");
    }

    private static Integer userIdMask(UserRosterModel user) {
        if (user.getVipLevel() != null && user.getVipLevel() == UserRosterVipLevel.VIP_1.getValue()) {
            return -1;
        }
        return user.getBindUserId();
    }

    private static UserRosterModel mapUserRoster(UserRosterExcelModel excelModel) {
        UserRosterModel user = new UserRosterModel();
        user.setUserName(excelModel.getUserName());
        user.setMobile(excelModel.getMobile());
        user.setInstanceKey(excelModel.getInstanceKey());
        user.setOrganizationId(excelModel.getOrgId());

        return user;
    }
}
