package com.kaiba.m.core.controller.artmap.performance.backend;

import com.kaiba.lib.base.constant.KbHeader;
import com.kaiba.lib.base.domain.artmap.PerformanceModel;
import com.kaiba.lib.base.middleware.KbRes;
import com.kaiba.lib.base.response.KbEntity;
import com.kaiba.m.core.model.artmap.dto.CalendarPerformQueryDTO;
import com.kaiba.m.core.service.artmap.PerformanceQueryService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.time.Instant;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort.Direction;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * Description: 艺术地图管理后台演艺演出查询
 * Author: ZM227
 * Date: 2025/6/16 11:43
 */
@Slf4j
@RestController
@RequestMapping("/backend/artMap/performance/query")
@Api(tags = "艺术地图管理后台演艺演出查询")
public class PerformanceBEQueryController {


    @Resource
    private KbRes kbRes;
    @Resource
    private PerformanceQueryService performanceQueryService;

    @PostMapping("/usr/queryRangeCount")
    @ApiOperation(value = "查询时间段范围内演出数量")
    public KbEntity<Map<Long, Integer>> queryRangeCount(
        @RequestHeader(name = KbHeader.KB_USER_ID) Integer userId,
        @RequestParam Long startTime, @RequestParam Long endTime) {
        return kbRes.ok(performanceQueryService.queryRangeCount(startTime, endTime));
    }

    @PostMapping("/usr/queryPerformanceList")
    @ApiOperation(value = "查询单日演出列表")
    public KbEntity<List<PerformanceModel>> queryPerformanceList(
        @RequestHeader(name = KbHeader.KB_USER_ID) Integer userId,
        @RequestParam Long performanceDate,
        @RequestParam(required = false, defaultValue = "1") Integer page,
        @RequestParam(required = false, defaultValue = "20") Integer pageSize) {
        CalendarPerformQueryDTO queryDTO = new CalendarPerformQueryDTO();
        queryDTO.setPerformanceDateStart(Instant.ofEpochMilli(performanceDate));
        queryDTO.setPerformanceDateEnd(Instant.ofEpochMilli(performanceDate));
        if (Objects.nonNull(page) && Objects.nonNull(pageSize)) {
            queryDTO.setPageable(PageRequest.of(page - 1, pageSize));
        }
        return kbRes.ok(performanceQueryService.queryPerformanceList(queryDTO));
    }

}
