package com.kaiba.m.core.controller.user;

import com.aliyun.dypnsapi20170525.Client;
import com.aliyun.dypnsapi20170525.models.GetMobileRequest;
import com.aliyun.dypnsapi20170525.models.GetMobileResponse;
import com.aliyun.teaopenapi.models.Config;
import lombok.extern.slf4j.Slf4j;

/**
 * 阿里一键登录
 * */
@Slf4j
class AliMobileService {

    private static final String ENDPOINT = "dypnsapi.aliyuncs.com";
    private static final String ACCESS_KEY_ID = "LTAI5tGTixJLsA18M77fjeAp";
    private static final String ACCESS_SECRETE = "******************************";

    private static Client createClient() throws Exception {
        Config config = new Config()
                .setAccessKeyId(ACCESS_KEY_ID)
                .setAccessKeySecret(ACCESS_SECRETE)
                .setEndpoint(ENDPOINT);
        return new Client(config);
    }

    static String getMobile(String accessToken) throws Exception {
        Client client = createClient();
        GetMobileRequest request = new GetMobileRequest()
                .setAccessToken(accessToken);
        GetMobileResponse response = client.getMobile(request);
        if (response.getBody() != null){
            if ("OK".equals(response.getBody().getCode())){
                return response.getBody().getGetMobileResultDTO().getMobile();
            } else {
                log.info("auto detect mobile code error, error code : " + response.getBody().getCode());
            }
        }
        return "";
    }

}
