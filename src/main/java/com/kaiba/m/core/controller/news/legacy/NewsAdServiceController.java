package com.kaiba.m.core.controller.news.legacy;

import com.kaiba.lib.base.constant.advertisement.NewsAdType;
import com.kaiba.lib.base.domain.advertisement.NewsAdModel;
import com.kaiba.lib.base.middleware.KbRes;
import com.kaiba.lib.base.response.KbCode;
import com.kaiba.lib.base.response.KbEntity;
import com.kaiba.lib.base.response.KbException;
import com.kaiba.lib.base.service.INewsAdService;
import com.kaiba.lib.base.service.IUserService;
import com.kaiba.m.core.domain.advertisement.NewsAd;
import com.kaiba.m.core.service.advertisement.NewsAdService;
import com.kaiba.m.core.util.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Slf4j
@RestController
public class NewsAdServiceController implements INewsAdService {

    private final KbRes kbRes;
    private final NewsAdService newsAdService;
    private final IUserService userService;

    public NewsAdServiceController(KbRes kbRes, NewsAdService newsAdService, IUserService userService) {
        this.kbRes = kbRes;
        this.newsAdService = newsAdService;
        this.userService = userService;
    }

    @Override
    public KbEntity<Void> addListAd(String title, String content, Integer isReference, String url, String images, Integer browser,
                                    Integer siteId, Integer userId, String siteTitle) {
        NewsAd ad = new NewsAd();
        ad.setTitle(title);
        ad.setSiteId(siteId);
        ad.setSiteTitle(siteTitle);
        ad.setIsReference(isReference);
        ad.setUrl(url);
        ad.setContent(content);
        ad.setDisplay(0);
        ad.setIsSign(0);
        ad.setImages(JsonUtils.toImageList(images));
        ad.setBrowser(browser);
        ad.setCreateUser(userId);
        ad.setViewCount(0);
        ad.setType(NewsAdType.OUTSIDE_AD.getValue());
        newsAdService.add(ad);
        return kbRes.ok();
    }

    @Override
    public KbEntity<Void> addInsideAd(String title, String content, Integer isReference, String url, String images, Integer browser,
                                      Integer siteId, Integer userId, String siteTitle) {
        NewsAd ad = new NewsAd();
        ad.setTitle(title);
        ad.setSiteId(siteId);
        ad.setSiteTitle(siteTitle);
        ad.setIsReference(isReference);
        ad.setUrl(url);
        ad.setContent(content);
        ad.setDisplay(0);
        ad.setIsSign(0);
        ad.setImages(JsonUtils.toImageList(images));
        ad.setBrowser(browser);
        ad.setCreateUser(userId);
        ad.setViewCount(0);
        ad.setType(NewsAdType.INSIDE_AD.getValue());
        newsAdService.add(ad);
        return kbRes.ok();
    }

    @Override
    public KbEntity<Void> edit(String id, String title, String content, Integer isReference, String url, String images,
                               Integer browser, String siteTitle) {
        newsAdService.findById(id).ifPresent(ad -> {
            ad.setTitle(title);
            ad.setSiteTitle(siteTitle);
            ad.setContent(content);
            ad.setIsReference(isReference);
            ad.setUrl(url);
            ad.setImages(JsonUtils.toImageList(images));
            ad.setBrowser(browser);
            newsAdService.edit(ad);
        });
        return kbRes.ok();
    }

    @Override
    public KbEntity<Void> delete(String id) {
        newsAdService.delete(id);
        return kbRes.ok();
    }

    @Override
    public KbEntity<Void> sign(String id, Integer userId) {
        newsAdService.sign(id, 1, userId);
        return kbRes.ok();
    }

    @Override
    public KbEntity<Void> cancelSign(String id, Integer userId) {
        newsAdService.sign(id, 0, userId);
        return kbRes.ok();
    }

    @Override
    public KbEntity<NewsAdModel> detail(String id) {
        NewsAd ad = newsAdService.findById(id).orElseThrow(()->new KbException(KbCode.RESOURCE_NOT_FOUND).li());
        return kbRes.ok(newsAdToModel(ad));
    }

    @Override
    public KbEntity<List<NewsAdModel>> searchInsideAdList(Integer siteId, String title, Integer page, Integer pageSize) {
        Page<NewsAd> adPage = newsAdService.findBySiteIdAndTypeAndTitle(siteId, NewsAdType.INSIDE_AD, title, page, pageSize);
        return kbRes.ok(adPage.map(this::newsAdToModel));
    }

    @Override
    public KbEntity<List<NewsAdModel>> searchListAdList(Integer siteId, String title, Integer page, Integer pageSize) {
        Page<NewsAd> adPage = newsAdService.findBySiteIdAndTypeAndTitle(siteId, NewsAdType.OUTSIDE_AD, title, page, pageSize);
        return kbRes.ok(adPage.map(this::newsAdToModel));
    }

    private NewsAdModel newsAdToModel(NewsAd ad) {
        if (ad != null) {
            NewsAdModel model = new NewsAdModel();
            model.setId(ad.getId());
            model.setTitle(ad.getTitle());
            model.setSiteTitle(ad.getSiteTitle());
            model.setContent(ad.getContent());
            model.setSiteId(ad.getSiteId());
            model.setIsReference(ad.getIsReference());
            model.setUrl(ad.getUrl());
            model.setBrowser(ad.getBrowser());
            model.setImages(ad.getImages());
            model.setViewCount(ad.getViewCount());
            model.setSignTime(ad.getSignTime());
            model.setIsSign(ad.getIsSign());
            model.setDisplay(ad.getDisplay());
            if (ad.getIsSign() == 1) {
                model.setSignUser(userService.getBasic(ad.getSignUser()).getData());
            }
            return model;
        } else {
            return null;
        }
    }
}
