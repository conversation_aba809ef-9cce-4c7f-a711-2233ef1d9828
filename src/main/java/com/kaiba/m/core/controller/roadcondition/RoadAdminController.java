package com.kaiba.m.core.controller.roadcondition;

import com.kaiba.lib.base.constant.KbHeader;
import com.kaiba.lib.base.middleware.KbRes;
import com.kaiba.lib.base.response.KbEntity;
import com.kaiba.m.core.model.roadcondition.RoadConditionCreateModel;
import com.kaiba.m.core.model.roadcondition.RoadConditionModel;
import com.kaiba.m.core.model.roadcondition.RoadRankingModel;
import com.kaiba.m.core.service.roadcondition.RoadConditionService;

import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2024/05/21 17:02
 **/
@Slf4j
@RestController()
@RequestMapping("/road")
public class RoadAdminController {

    private final KbRes kbRes;
    private final RoadConditionService conditionService;

    public RoadAdminController(
            KbRes kbRes,
            RoadConditionService conditionService
    ) {
        this.kbRes = kbRes;
        this.conditionService = conditionService;
    }

    @ApiOperation("用户路况-发布路况")
    @PostMapping(path = "/addRoadMsg")
    public KbEntity<Void> addRoadMsg(
            @RequestHeader(name = KbHeader.KB_USER_ID) Integer userId,
            @RequestHeader(name = KbHeader.KB_SITE_ID) Integer siteId,
            @RequestHeader(name = KbHeader.KB_SOURCE) Integer source,
            @RequestBody RoadConditionCreateModel createModel
    ) {
        createModel.setSiteId(siteId);
        createModel.setUserId(userId);
        createModel.setSource(source);
        conditionService.add(createModel);
        return kbRes.ok();
    }

    @ApiOperation("用户路况-删除路况")
    @PostMapping(path = "/delRoadMsgById")
    public KbEntity<Void> delRoadMsgById(
            @RequestHeader(name = KbHeader.KB_USER_ID) Integer userId,
            @RequestParam String roadMsgId
    ) {
        log.info("del road msg: {}, by user: {}", roadMsgId, userId);
        conditionService.delRoadMsgById(roadMsgId);
        return kbRes.ok();
    }

    @ApiOperation("用户路况-延长路况过期时间")
    @PostMapping(path = "/expandTimeById")
    public KbEntity<Void> expandTimeById(
            @RequestParam String id,
            @RequestParam Integer minute
    ) {
        conditionService.expandTimeById(id, minute);
        return kbRes.ok();
    }

    @ApiOperation("用户路况-路况详情")
    @PostMapping(path = "/getDetailById")
    public KbEntity<RoadConditionModel> getDetailById(
            @RequestParam String id
    ) {
        return kbRes.ok(conditionService.getRoadMsgDetailById(id));
    }

    @ApiOperation("用户路况-路况列表")
    @PostMapping(path = "/getRoadMsgListBySource")
    public KbEntity<List<RoadConditionModel>> getRoadMsgListBySource(
            @RequestHeader(name = KbHeader.KB_SOURCE) Integer source,
            @RequestParam(required = false, defaultValue = "1") Integer page,
            @RequestParam(required = false, defaultValue = "20") Integer pageSize
    ) {
        return kbRes.ok(conditionService.getRoadMsgListBySource(source, page, pageSize));
    }

    @ApiOperation("用户路况-路况排行")
    @PostMapping(path = "/getRankingList")
    public KbEntity<RoadRankingModel> getRankingList(
            @RequestHeader(name = KbHeader.KB_SOURCE) Integer source
    ) {
        return kbRes.ok(conditionService.getRankingList(source));
    }

    @ApiOperation("用户路况-根据时间范围获取路况排行")
    @PostMapping(path = "/getRankingListByTimeRange")
    public KbEntity<List<RoadRankingModel.RoadRankingItemModel>> getRankingListByTimeRange(
            @RequestHeader(name = KbHeader.KB_SOURCE) Integer source,
            @RequestParam Long startTime,
            @RequestParam Long endTime
    ) {
        return kbRes.ok(conditionService.getRankingListByTimeRange(source, startTime, endTime));
    }
}
