package com.kaiba.m.core.controller.note;

import com.kaiba.lib.base.constant.KbHeader;
import com.kaiba.lib.base.constant.Values;
import com.kaiba.lib.base.domain.user.UserModel;
import com.kaiba.lib.base.middleware.KbRes;
import com.kaiba.lib.base.response.KbCode;
import com.kaiba.lib.base.response.KbEntity;
import com.kaiba.lib.base.rte.RelativeTimeExpression;
import com.kaiba.lib.base.service.IUserService;
import com.kaiba.lib.base.util.mock.MockUtils;
import com.kaiba.m.core.service.note.event.handler.PraiseRankTempHandler;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;
import java.util.Random;

/**
 * author: lyux
 * date: 2022-05-24
 */
@Slf4j
@RestController()
@RequestMapping("/n/note/PraiseRank")
public class NotePraiseRankTempController {

    private static final String SECRET = "34e72249934ccbb44ca36cdf93951d7c";

    private final KbRes rest;
    private final PraiseRankTempHandler rankHandler;
    private final IUserService userService;

    public NotePraiseRankTempController(
            @Qualifier("kbApiRes") KbRes rest,
            PraiseRankTempHandler rankHandler,
            IUserService userService
    ) {
        this.rest = rest;
        this.rankHandler = rankHandler;
        this.userService = userService;
    }

    @PostMapping(path = "/getRankListByThread")
    public KbEntity<RankList> getRankListByThread(
            @RequestHeader(name = KbHeader.KB_USER_ID, required = false) Integer userId,
            @RequestParam(required = false) String threadId,
            @RequestParam(required = false) String threadKey
    ) {
        List<PraiseRankTempHandler.Rank> list;
        if (threadId == null && threadKey == null) {
            return rest.err(KbCode.REQUEST_PARAM_MISSING);
        } else {
            list = rankHandler.getRankListByFromThread(threadId, threadKey);
            return rest.ok(new RankList(list, userId));
        }
    }

    @PostMapping(path = "/getRawRankListByThread")
    public KbEntity<RankList> getRawRankListByThread(
            @RequestHeader(name = KbHeader.KB_USER_ID, required = false) Integer userId,
            @RequestParam(required = false) String threadId,
            @RequestParam(required = false) String threadKey
    ) {
        List<PraiseRankTempHandler.Rank> list;
        if (threadId == null && threadKey == null) {
            return rest.err(KbCode.REQUEST_PARAM_MISSING);
        } else {
            list = rankHandler.getRankListFromRedisByFromThread(threadId, threadKey);
            return rest.ok(new RankList(list, userId));
        }
    }

    @PostMapping(path = "/getMockRankListByThread")
    public KbEntity<RankList> getMockRankListByThread(
            @RequestHeader(name = KbHeader.KB_USER_ID, required = false) Integer userId,
            @RequestParam(required = false) String threadId,
            @RequestParam(required = false) String threadKey
    ) {
        return rest.ok(mockRankList(userId));
    }

    // ------------------------------------------------------

    @PostMapping(path = "/refreshRedisCache")
    public KbEntity<RankList> refreshRedisCache(
            @RequestParam() String secret,
            @RequestParam(required = false) String threadId,
            @RequestParam(required = false) String threadKey,
            @RequestParam(required = false, defaultValue = "false") Boolean async,
            @RequestParam(required = false, defaultValue = "1") Integer page,
            @RequestParam(required = false, defaultValue = "100") Integer pageSize
    ) {
        if (SECRET.equals(secret)) {
            if (threadId == null && threadKey == null) {
                return rest.err(KbCode.REQUEST_PARAM_MISSING);
            } else {
                rankHandler.refreshRankCache(threadId, threadKey, async, page, pageSize);
                return rest.ok();
            }
        } else {
            return rest.err(KbCode.AUTH_FAIL);
        }
    }

    @PostMapping(path = "/refreshRedisCacheAll")
    public KbEntity<RankList> refreshRedisCacheAll(
            @RequestParam() String secret
    ) {
        if (SECRET.equals(secret)) {
            rankHandler.refreshRankCacheAll();
            return rest.ok();
        } else {
            return rest.err(KbCode.AUTH_FAIL);
        }
    }

    @PostMapping(path = "/initConfig")
    public KbEntity<List<PraiseRankTempHandler.Rank>> initConfig(
            @RequestParam() String secret
    ) {
        if (SECRET.equals(secret)) {
            rankHandler.init();
            return rest.ok();
        } else {
            return rest.err(KbCode.AUTH_FAIL);
        }
    }

    // 每小时整点执行
    @XxlJob("note-thread-temp-praise-rank-refresh")
    public ReturnT<String> refreshCacheJob(String param) {
        rankHandler.refreshRankCacheAll();
        return ReturnT.SUCCESS;
    }

    // ------------------------------------------------------

    private RankList mockRankList(Integer userId) {
        Random random = new Random();
        int count = 10 + random.nextInt(300);
        if (count > 100) { count = 100; }
        RankList model = new RankList();
        model.ranks = new ArrayList<>(count);
        for (int i = 0; i < count; i ++) {
            int nameLength = random.nextInt(8) + 2;
            UserModel user = new UserModel();
            user.setUserId(random.nextInt(200_000));
            user.setUserName(MockUtils.randomChineseString(nameLength));
            user.setSex(random.nextInt(3));
            if (user.getSex() == Values.GENDER_MALE) {
                user.setAvatar("https://static.kaiba315.com.cn/sys/head/500/male.jpg");
            } else if (user.getSex() == Values.GENDER_FEMALE) {
                user.setAvatar("https://static.kaiba315.com.cn/sys/head/500/female.jpg");
            } else {
                user.setAvatar("https://static.kaiba315.com.cn/sys/head/500/default.jpg");
            }

            PraiseRankTempHandler.Rank rank = new PraiseRankTempHandler.Rank();
            rank.setRank(i + 1);
            rank.setUser(user);
            rank.setPraise((100 - i) * 3 + random.nextInt(3));
            rank.setNoteId("5d36f888c4698897338b457e");
            model.ranks.add(rank);
        }

        if (userId != null) {
            int userfactor = random.nextInt(3);
            if (count < 100 || userfactor == 0) { // user in rank
                model.bestRank = random.nextInt(count);
                model.bestNoteId = "5d36f888c4698897338b457e";
                PraiseRankTempHandler.Rank rank = model.ranks.get(model.bestRank);
                rank.setUser(userService.getBasic(userId).dataOrThrow());
            } else if (userfactor == 1) { // user out of rank
                model.bestRank = count + random.nextInt(100);
                model.bestNoteId = "5d36f888c4698897338b457e";
            } else { // user not participated
                model.bestRank = null;
            }
        }

        model.setRefreshTime(RelativeTimeExpression.calculateByCurrentInMillis("^H-H"));
        return model;
    }

    @Data
    @NoArgsConstructor
    private static class RankList {
        private Integer bestRank;
        private String bestNoteId;
        private Long refreshTime;
        private List<PraiseRankTempHandler.Rank> ranks;

        public RankList(List<PraiseRankTempHandler.Rank> ranks, Integer userId) {
            this.ranks = ranks;
            if (userId != null && ranks != null && !ranks.isEmpty()) {
                for (PraiseRankTempHandler.Rank rank : ranks) {
                    if (rank.getUser() != null && userId.equals(rank.getUser().getUserId())) {
                        bestRank = rank.getRank();
                        bestNoteId = rank.getNoteId();
                    }
                }
            }
            this.refreshTime = RelativeTimeExpression.calculateByCurrentInMillis("^H");
        }
    }

}
