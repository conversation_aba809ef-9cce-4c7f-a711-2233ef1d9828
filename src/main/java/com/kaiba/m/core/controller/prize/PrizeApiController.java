package com.kaiba.m.core.controller.prize;

import com.kaiba.lib.base.annotation.api.KbCheckFrequency;
import com.kaiba.lib.base.annotation.apiparam.*;
import com.kaiba.lib.base.constant.KbHeader;
import com.kaiba.lib.base.constant.KbProperties;
import com.kaiba.lib.base.constant.prize.PrizeGiveUpReason;
import com.kaiba.lib.base.constant.prize.PrizeState;
import com.kaiba.lib.base.constant.prize.PrizeType;
import com.kaiba.lib.base.domain.common.ActionLink;
import com.kaiba.lib.base.domain.prize.PrizeModel;
import com.kaiba.lib.base.domain.prize.PrizeQueryModel;
import com.kaiba.lib.base.domain.prize.WriteOffResult;
import com.kaiba.lib.base.domain.user.UserAddressModel;
import com.kaiba.lib.base.middleware.KbRes;
import com.kaiba.lib.base.response.KbCode;
import com.kaiba.lib.base.response.KbEntity;
import com.kaiba.lib.base.service.IPrizeService;
import com.kaiba.lib.base.service.IUserAddressService;
import com.kaiba.lib.base.util.StringUtils;
import com.kaiba.lib.base.util.appaction.AppActionType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * author: lyux
 * date: 19-7-24
 */
@Slf4j
@RestController
@RequestMapping("/n/prize")
@Validated
public class PrizeApiController {

    private final KbRes kbRes;
    private final IPrizeService prizeService;
    private final IUserAddressService addressService;

    public PrizeApiController(
            @Qualifier("kbApiRes") KbRes kbRes,
            IPrizeService prizeService,
            IUserAddressService addressService
    ) {
        this.kbRes = kbRes;
        this.prizeService = prizeService;
        this.addressService = addressService;
    }

    @PostMapping("/getPrizeById")
    public KbEntity<PrizeModel> getPrizeById(
            @RequestHeader(KbHeader.KB_USER_ID) @KbUserId Integer userId,
            @RequestParam() @KbObjectId String prizeId
    ) {
        KbEntity<PrizeModel> entity = prizeService.getPrizeById(prizeId, true);
        return checkPrizeEntityForUser(entity, userId);
    }

    @PostMapping("/getFirstPrizeByIdOrRef")
    public KbEntity<PrizeModel> getFirstPrizeByIdOrRef(
            @RequestHeader(KbHeader.KB_USER_ID) @KbUserId Integer userId,
            @RequestParam(required = false) @KbObjectId String prizeId,
            @RequestParam(required = false) String refId1,
            @RequestParam(required = false) String refId2,
            @RequestParam(required = false) String refId3
    ) {
        if (prizeId == null) {
            PrizeQueryModel query = new PrizeQueryModel();
            query.setUserId(userId);
            query.setReferenceId1(refId1);
            query.setReferenceId2(refId2);
            query.setReferenceId3(refId3);
            query.setNeedDispatchDetail(true);
            query.setPage(1);
            query.setPageSize(1);
            KbEntity<PrizeModel> entity = kbRes.asIs(prizeService.getPrizeListByBody(query)
                    .map(list -> list == null || list.size() == 0 ? null : list.get(0)));
            return checkPrizeEntityForUser(entity, userId);
        } else {
            return getPrizeById(userId, prizeId);
        }
    }

    @PostMapping("/getPrizeListByIdOrRef")
    public KbEntity<List<PrizeModel>> getPrizeListByIdOrRef(
            @RequestHeader(KbHeader.KB_USER_ID) @KbUserId Integer userId,
            @RequestParam(required = false) @KbObjectId String prizeId,
            @RequestParam(required = false) String refId1,
            @RequestParam(required = false) String refId2,
            @RequestParam(required = false) String refId3,
            @RequestParam(required = false, defaultValue = "1") @KbPage Integer page,
            @RequestParam(required = false, defaultValue = "15") @KbPageSize Integer pageSize
    ) {
        if (prizeId == null) {
            return kbRes.asIs(prizeService.getPrizeListByReferenceId(
                    refId1, refId2, refId3, null, null, page, pageSize, false)
                    .map(list -> filterPrizeListForUser(list, userId)));
        } else {
            return getPrizeById(userId, prizeId).map(Collections::singletonList);
        }
    }

    @PostMapping(path = "/getPrizeListByBody", consumes = "application/json;charset=UTF-8")
    public KbEntity<List<PrizeModel>> getPrizeListByBody(
            @RequestHeader(KbHeader.KB_USER_ID) @KbUserId Integer userId,
            @RequestBody() PrizeQueryModel param
    ) {
        param.setNeedDispatchDetail(false);
        param.setUserId(userId);
        return kbRes.asIs(prizeService.getPrizeListByBody(param));
    }

    @PostMapping("/getPrizeListByUserId")
    public KbEntity<List<PrizeModel>> getPrizeListByUserId(
            @RequestHeader(KbHeader.KB_USER_ID) @KbUserId Integer userId,
            @RequestParam(required = false) Integer state,
            @RequestParam(required = false) @KbSiteId Integer siteId,
            @RequestParam(required = false, defaultValue = "1") @KbPage Integer page,
            @RequestParam(required = false, defaultValue = "15") @KbPageSize Integer pageSize
    ) {
        return kbRes.asIs(prizeService
                .getPrizeListByUserId(userId, state, siteId, page, pageSize, false));
    }

    @PostMapping("/updatePrizeAddressByTemplate")
    public KbEntity<PrizeModel> updatePrizeAddressByTemplate(
            @RequestHeader(KbHeader.KB_USER_ID) @KbUserId Integer userId,
            @RequestParam() @KbObjectId String prizeId,
            @RequestParam() @KbObjectId String addressId
    ) {
        PrizeModel prize = prizeService.getPrizeById(prizeId, false).dataOrThrow();
        if (!userId.equals(prize.getUserId())) {
            return kbRes.err(KbCode.AUTH_NOT_OWNER);
        }
        if (StringUtils.isEmpty(prize.getAddressId())) {
            UserAddressModel address = addressService.createImplAddressFromTemplate(addressId).dataOrThrow();
            return kbRes.asIs(prizeService.updatePrizeUserAddress(prizeId, userId, address.getId()));
        } else {
            return kbRes.err(KbCode.REQUEST_FAIL, null, "如需更换地址, 请联系客服处理");
        }
    }

    @PostMapping("/updatePrizeAddress")
    public KbEntity<PrizeModel> updatePrizeAddress(
            @RequestHeader(KbHeader.KB_USER_ID) @KbUserId Integer userId,
            @RequestParam() @KbObjectId String prizeId,
            @RequestParam() @KbObjectId String addressImplId
    ) {
        PrizeModel prize = prizeService.getPrizeById(prizeId, false).dataOrThrow();
        if (!userId.equals(prize.getUserId())) {
            return kbRes.err(KbCode.AUTH_NOT_OWNER);
        }
        if (StringUtils.isEmpty(prize.getAddressId())) {
            if (addressService.getImplAddressById(addressImplId).data().isPresent()) {
                return kbRes.asIs(prizeService.updatePrizeUserAddress(prizeId, userId, addressImplId));
            } else {
                return kbRes.err(KbCode.REQUEST_FAIL, "address not exists", "地址不存在");
            }
        } else {
            return kbRes.err(KbCode.REQUEST_FAIL, null, "如需更换地址, 请联系客服处理");
        }
    }

    @PostMapping("/updatePrizeMobile")
    public KbEntity<PrizeModel> updatePrizeMobile(
            @RequestHeader(KbHeader.KB_USER_ID) @KbUserId Integer userId,
            @RequestParam() @KbObjectId String prizeId,
            @RequestParam() String mobile
    ) {
        PrizeModel prize = prizeService.getPrizeById(prizeId, false).dataOrThrow();
        if (!userId.equals(prize.getUserId())) {
            return kbRes.err(KbCode.AUTH_NOT_OWNER);
        }
        if (!StringUtils.isValidMobile(mobile)) {
            return kbRes.err(KbCode.REQUEST_PARAM_INVALID, null, "如需更换地址, 请联系客服处理");
        }
        return kbRes.asIs(prizeService.updatePrizeContactMobile(prizeId, userId, mobile));
    }

    @KbCheckFrequency
    @PostMapping("/confirmPrize")
    public KbEntity<PrizeModel> confirmPrize(
            @RequestHeader(KbHeader.KB_USER_ID) @KbUserId Integer userId,
            @RequestParam() @KbObjectId String prizeId
    ) {
        PrizeModel prize = prizeService.getPrizeById(prizeId, false).dataOrThrow();
        if (!userId.equals(prize.getUserId())) {
            return kbRes.err(KbCode.AUTH_NOT_OWNER);
        }
        return kbRes.asIs(prizeService.confirmPrize(userId, userId, prizeId, prize.getV()));
    }

    @PostMapping("/writeOff/getPrizeById")
    public KbEntity<PrizeModel> getWriteOffPrizeById(
            @RequestParam() @KbObjectId String prizeId
    ) {
        return kbRes.asIs(prizeService
                .getPrizeById(prizeId, true)
                .peek(PrizeApiController::removePrizeSensitiveInfo));
    }

    @PostMapping("/writeOff/getWriteOffResultByPrizeId")
    public KbEntity<WriteOffResult> getWriteOffResultByPrizeId(
            @RequestParam() @KbObjectId String prizeId
    ) {
        return kbRes.asIs(prizeService
                .getPrizeWriteOffResult(prizeId)
                .peek(PrizeApiController::removePrizeSensitiveInfo));
    }

    @KbCheckFrequency()
    @PostMapping("/writeOff/writeOffWithCode")
    public KbEntity<WriteOffResult> writeOffWithCode(
            @RequestHeader(KbHeader.KB_USER_ID) @KbUserId Integer userId,
            @RequestParam(required = false) @KbUserId Integer targetUserId,
            @RequestParam() @KbObjectId String prizeId,
            @RequestParam() String writeOffCode
    ) {
        return kbRes.asIs(prizeService
                .writeOffWithCodeV1(userId, targetUserId, prizeId, writeOffCode)
                .peek(PrizeApiController::removePrizeSensitiveInfo));
    }

    // ---------------------------------------------

    private KbEntity<PrizeModel> checkPrizeEntityForUser(KbEntity<PrizeModel> entity, Integer userId) {
        if (!entity.isOk()) {
            return entity;
        }
        PrizeModel prize = entity.getData();
        if (prize == null) {
            return kbRes.err(KbCode.PRIZE_NOT_EXISTS);
        }
        if (!userId.equals(prize.getUserId())) {
            return kbRes.err(KbCode.AUTH_NOT_OWNER);
        }
        if (prize.getDispatchMethod() != null) {
            // 核销码不可以传递给 C 端用户
            prize.getDispatchMethod().setWriteOffCode(null);
        }
        if (prize.getState() == PrizeState.NOT_CLAIMED.getValue()
                && prize.getExpireTime() != null
                && System.currentTimeMillis() / 1000 > prize.getExpireTime()) {
            // 奖品信息已经过期
            prizeService.giveUpPrize(
                    prize.getId(), KbProperties.ADMIN_USER_ID, PrizeGiveUpReason.TIMEOUT.getValue());
            prize.setState(PrizeState.GIVE_UP.getValue());
        } else {
            if (prize.getType() == PrizeType.CASH.getValue()) {
                ActionLink link = ActionLink.asAction(
                        AppActionType.PAGE_MY_BALANCE.getAction(), "可至余额页面查看", null);
                if (prize.getLinks() == null) {
                    prize.setLinks(Collections.singletonList(link));
                } else {
                    prize.getLinks().add(link);
                }
            }
        }
        return kbRes.ok(prize);
    }

    private static List<PrizeModel> filterPrizeListForUser(List<PrizeModel> prizeList, Integer userId) {
        return prizeList.stream().filter(prize -> prize.getUserId().equals(userId)).collect(Collectors.toList());
    }

    private static void removePrizeSensitiveInfo(WriteOffResult result) {
        if (result != null) {
            removePrizeSensitiveInfo(result.getPrize());
        }
    }

    private static void removePrizeSensitiveInfo(PrizeModel prize) {
        if (prize != null) {
            prize.setUserMobile(null);
            if (prize.getDispatchMethod() != null) {
                prize.getDispatchMethod().setWriteOffCode(null);
            }
        }
    }

}
