package com.kaiba.m.core.controller.thirdparty;

import com.kaiba.lib.base.assembler.Mapper;
import com.kaiba.lib.base.constant.KbEndpoint;
import com.kaiba.lib.base.domain.thirdparty.ThirdPartyAccessModel;
import com.kaiba.lib.base.middleware.KbRes;
import com.kaiba.lib.base.middleware.jwt.KbJWTHandler;
import com.kaiba.lib.base.response.KbCode;
import com.kaiba.lib.base.response.KbEntity;
import com.kaiba.lib.base.response.KbException;
import com.kaiba.lib.base.service.IThirdPartyAuthService;
import com.kaiba.m.core.domain.thirdparty.ThirdPartyAccess;
import com.kaiba.m.core.service.thirdparty.ThirdPartyAccessService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RestController;

import java.time.Duration;
import java.util.List;

/**
 * author: lyux
 * date: 2020-09-03
 */
@Slf4j
@RestController
public class ThirdPartyAuthServiceController implements IThirdPartyAuthService {

    private final KbRes kbRes;
    private final KbJWTHandler jwtHandler;
    private final ThirdPartyAccessService accessService;

    public ThirdPartyAuthServiceController(
            KbRes kbRes,
            KbJWTHandler jwtHandler,
            ThirdPartyAccessService accessService
    ) {
        this.kbRes = kbRes;
        this.jwtHandler = jwtHandler;
        this.accessService = accessService;
    }

    @Override
    public KbEntity<String> acquireToken(String accessId, Long expire) {
        if (expire <= 0 || expire > 86400) {
            expire = 10800L;
        }
        ThirdPartyAccess access = getAccessOrThrow(accessId, true);
        String jwt = jwtHandler.createJWT(accessId, KbEndpoint.THIRD_PARTY, Duration.ofSeconds(expire));
        return kbRes.ok(accessService.encryptByAccess(access, jwt));
    }

    @Override
    public KbEntity<ThirdPartyAccessModel> getAccessById(String accessId, Boolean allowCache) {
        return accessService.getAccess(accessId, allowCache)
                .map(ThirdPartyAuthServiceController::access2model)
                .map(kbRes::ok)
                .orElse(kbRes.err(KbCode.RESOURCE_NOT_FOUND));
    }

    @Override
    public KbEntity<String> getAccessSaltById(String accessId, Boolean allowCache) {
        return accessService.getAccess(accessId, allowCache)
                .map(ThirdPartyAccess::getSalt)
                .map(kbRes::ok)
                .orElse(kbRes.err(KbCode.RESOURCE_NOT_FOUND));
    }

    @Override
    public KbEntity<String> getAccessPrivateKeyById(String accessId) {
        return accessService.getAccess(accessId, false)
                .map(ThirdPartyAccess::getPrivateKey)
                .map(kbRes::ok)
                .orElse(kbRes.err(KbCode.RESOURCE_NOT_FOUND));
    }

    @Override
    public KbEntity<String> getAccessPublicKeyById(String accessId) {
        return accessService.getAccess(accessId, false)
                .map(ThirdPartyAccess::getPublicKey)
                .map(kbRes::ok)
                .orElse(kbRes.err(KbCode.RESOURCE_NOT_FOUND));
    }

    @Override
    public KbEntity<List<ThirdPartyAccessModel>> getAccessListAll(Integer page, Integer pageSize) {
        return kbRes.ok(accessService.getAccessListAll(page, pageSize)
                .map(ThirdPartyAuthServiceController::access2model));
    }

    @Override
    public KbEntity<ThirdPartyAccessModel> createAccess(String name, String description) {
        return kbRes.ok(access2model(accessService.createAccess(name, description)));
    }

    @Override
    public KbEntity<Void> updateAccessDescription(String accessId, String description) {
        accessService.updateAccessDescription(accessId, description);
        return kbRes.ok();
    }

    @Override
    public KbEntity<Void> updateAccessEnabled(String accessId, Boolean enabled) {
        accessService.updateAccessEnabled(accessId, enabled);
        return kbRes.ok();
    }

    @Override
    public KbEntity<String> refreshAccessKeyPair(String accessId) {
        return kbRes.ok(accessService.refreshAccessKeyPair(accessId));
    }

    @Override
    public KbEntity<String> refreshAccessSalt(String accessId) {
        return kbRes.ok(accessService.refreshAccessSalt(accessId));
    }

    @Override
    public KbEntity<String> refreshAccessToken(String accessId, Long expireAtMS) {
        long now = System.currentTimeMillis();
        if (now > expireAtMS) {
            return kbRes.err(KbCode.REQUEST_PARAM_INVALID, "expire less than current timestamp");
        }
        Duration duration = Duration.ofMillis(expireAtMS - now);
        return kbRes.ok(accessService.refreshAccessToken(accessId, duration));
    }

    @Override
    public KbEntity<String> encryptByAccess(String accessId, String data, Boolean considerEnabled) {
        return kbRes.ok(accessService.encryptByAccess(getAccessOrThrow(accessId, considerEnabled), data));
    }

    @Override
    public KbEntity<String> decryptByAccess(String accessId, String data, Boolean considerEnabled) {
        return kbRes.ok(accessService.decryptByAccess(getAccessOrThrow(accessId, considerEnabled), data.getBytes()));
    }

    private ThirdPartyAccess getAccessOrThrow(String accessId, boolean considerEnabled) {
        ThirdPartyAccess access = accessService.getAccess(accessId, true).orElseThrow(() ->
                new KbException(KbCode.RESOURCE_NOT_FOUND, "access not found: " + accessId)
                        .setReadableMessage("未找到凭证").li());
        if (considerEnabled && access.getEnabled() != null && !access.getEnabled()) {
            throw new KbException(KbCode.ILLEGAL_STATE, "access is disabled: " + accessId)
                    .setReadableMessage("凭证已禁用").li();
        }
        return access;
    }

    private static ThirdPartyAccessModel access2model(ThirdPartyAccess access) {
        ThirdPartyAccessModel model = Mapper.map(access, ThirdPartyAccessModel.class);
        model.setPrivateKey(null);
        return model;
    }

}
