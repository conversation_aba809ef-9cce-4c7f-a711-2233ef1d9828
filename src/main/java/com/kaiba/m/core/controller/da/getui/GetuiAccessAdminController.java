package com.kaiba.m.core.controller.da.getui;

import com.kaiba.lib.base.constant.KbHeader;
import com.kaiba.lib.base.domain.minitheatre.MTTheatreModel;
import com.kaiba.lib.base.middleware.KbRes;
import com.kaiba.lib.base.response.KbCode;
import com.kaiba.lib.base.response.KbEntity;
import com.kaiba.lib.base.response.KbException;
import com.kaiba.lib.base.service.IMiniTheatreService;
import com.kaiba.m.core.controller.da.getui.model.GTABestDramaModel;
import com.kaiba.m.core.controller.da.getui.model.GTAItemContentUsage;
import com.kaiba.m.core.controller.da.getui.service.GTAContentService;
import com.kaiba.m.core.domain.da.getui.GTAFocusContent;
import com.kaiba.m.core.domain.da.getui.GTAItemContent;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 个推大屏数据管理后台接口
 *
 * <AUTHOR>
 * @version GetuiAccessAdminController, v0.1 2024/12/18 13:44 daopei Exp $
 **/
@Slf4j
@Validated
@RestController
@RequestMapping("/GetuiDA/admin")
public class GetuiAccessAdminController {

    private final KbRes kbRes;
    private final GTAContentService contentService;
    private final IMiniTheatreService miniTheatreService;

    public GetuiAccessAdminController(
            KbRes kbRes,
            GTAContentService contentService,
            IMiniTheatreService miniTheatreService
    ) {
        this.kbRes = kbRes;
        this.contentService = contentService;
        this.miniTheatreService = miniTheatreService;
    }

    // -------------------------------------------------------
    // 新闻首页大屏 - 重点内容

    @ApiOperation("重点内容列表")
    @PostMapping(path = "/getFocusContentList")
    public KbEntity<List<GTAFocusContent>> getFocusContentList(
            @RequestParam(required = false) Integer state,
            @RequestParam(required = false, defaultValue = "1") Integer page,
            @RequestParam(required = false, defaultValue = "20") Integer pageSize
    ) {
        return kbRes.ok(contentService.getFocusContentList(state, page, pageSize));
    }

    @ApiOperation("新增重点内容")
    @PostMapping(path = "/createFocusContent")
    public KbEntity<GTAFocusContent> createFocusContent(
            @RequestHeader(KbHeader.KB_USER_ID) Integer userId,
            @RequestBody GTAFocusContent content
    ) {
        return kbRes.ok(contentService.createFocusContent(userId, content));
    }

    @ApiOperation("修改重点内容")
    @PostMapping(path = "/updateFocusContent")
    public KbEntity<GTAFocusContent> updateFocusContent(
            @RequestHeader(KbHeader.KB_USER_ID) Integer userId,
            @RequestBody GTAFocusContent content
    ) {
        return kbRes.ok(contentService.updateFocusContent(userId, content));
    }

    @ApiOperation("删除重点内容")
    @PostMapping(path = "/deleteFocusContent")
    public KbEntity<Void> deleteFocusContent(
            @RequestHeader(KbHeader.KB_USER_ID) Integer userId,
            @RequestParam String contentId
    ) {
        contentService.deleteFocusContent(userId, contentId);
        return kbRes.ok();
    }

    @ApiOperation("修改重点内容排序")
    @PostMapping(path = "/updateFocusContentIdx")
    public KbEntity<Void> updateFocusContentIdx(
            @RequestHeader(KbHeader.KB_USER_ID) Integer userId,
            @RequestBody List<GTAFocusContent> contentList
    ) {
        contentService.updateFocusContentIdx(userId, contentList);
        return kbRes.ok();
    }

    // -------------------------------------------------------
    // 超有戏大屏 - 热门海报

    @ApiOperation("新增热门海报")
    @PostMapping(path = "/createBestDrama")
    public KbEntity<GTABestDramaModel> createBestDrama(
            @RequestHeader(KbHeader.KB_USER_ID) Integer userId,
            @RequestBody GTABestDramaModel bestDrama
    ) {
        return kbRes.ok(contentService.createBestDrama(userId, bestDrama));
    }

    @ApiOperation("删除热门海报")
    @PostMapping(path = "/deleteBestDrama")
    public KbEntity<Void> deleteBestDrama(
            @RequestHeader(KbHeader.KB_USER_ID) Integer userId
    ) {
        contentService.deleteBestDrama(userId);
        return kbRes.ok();
    }

    @ApiOperation("获取热门海报")
    @PostMapping(path = "/getBestDrama")
    public KbEntity<GTABestDramaModel> getBestDrama() {
        return contentService.getBestDrama()
                .map(kbRes::ok)
                .orElse(kbRes.ok());
    }

    // -------------------------------------------------------
    // 超有戏大屏 - 热门票房

    @ApiOperation("新增热门票房")
    @PostMapping(path = "/createBestBoxOffice")
    public KbEntity<GTABestDramaModel> createBestBoxOffice(
            @RequestHeader(KbHeader.KB_USER_ID) Integer userId,
            @RequestBody GTABestDramaModel bestDrama
    ) {
        return kbRes.ok(contentService.createBestBoxOffice(userId, bestDrama));
    }

    @ApiOperation("删除热门票房")
    @PostMapping(path = "/deleteBestBoxOffice")
    public KbEntity<Void> deleteBestBoxOffice(
            @RequestHeader(KbHeader.KB_USER_ID) Integer userId
    ) {
        contentService.deleteBestBoxOffice(userId);
        return kbRes.ok();
    }

    @ApiOperation("获取热门票房")
    @PostMapping(path = "/getBestBoxOffice")
    public KbEntity<GTABestDramaModel> getBestBoxOffice() {
        return contentService.getBestBoxOffice()
                .map(kbRes::ok)
                .orElse(kbRes.ok());
    }

    // -------------------------------------------------------
    // 剧好看大屏 - 热门短剧

    @ApiOperation("新增热门短剧")
    @PostMapping(path = "/createFocusTheatre")
    public KbEntity<GTAItemContent> createFocusTheatre(
            @RequestHeader(KbHeader.KB_USER_ID) Integer userId,
            @RequestParam(required = false) @ApiParam("剧场ID") String theatreId
    ) {
        MTTheatreModel theatre = miniTheatreService.getTheatreById(theatreId, false).data()
                .orElseThrow(() -> new KbException(KbCode.RESOURCE_NOT_FOUND));
        GTAItemContent content = contentService.upsertItemContentByUniqueUsage(
                GTAItemContentUsage.FOCUS_MINI_THEATRE, theatreId, theatre.getTitle(), theatre.getCoverImage());
        return kbRes.ok(content);
    }

    @ApiOperation("删除热门短剧")
    @PostMapping(path = "/deleteFocusTheatre")
    public KbEntity<Void> deleteFocusTheatre(
            @RequestHeader(KbHeader.KB_USER_ID) Integer userId
    ) {
        contentService.deleteItemContentByUniqueUsage(GTAItemContentUsage.FOCUS_MINI_THEATRE);
        return kbRes.ok();
    }

    @ApiOperation("获取热门短剧")
    @PostMapping(path = "/getFocusTheatre")
    public KbEntity<GTAItemContent> getFocusTheatre() {
        return contentService.getItemContentByUniqueUsage(GTAItemContentUsage.FOCUS_MINI_THEATRE)
                .map(kbRes::ok)
                .orElse(kbRes.ok());
    }

    // -------------------------------------------------------

}
