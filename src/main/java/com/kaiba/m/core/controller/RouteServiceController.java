package com.kaiba.m.core.controller;

import com.kaiba.lib.base.assembler.Mapper;
import com.kaiba.lib.base.constant.route.KbRouteRuleState;
import com.kaiba.lib.base.constant.route.KbRouteState;
import com.kaiba.lib.base.domain.route.*;
import com.kaiba.lib.base.middleware.KbRes;
import com.kaiba.lib.base.response.KbCode;
import com.kaiba.lib.base.response.KbEntity;
import com.kaiba.lib.base.response.KbException;
import com.kaiba.lib.base.service.IRouteService;
import com.kaiba.lib.base.util.ArrayTypeHolder;
import com.kaiba.m.core.domain.route.KbAuthRule;
import com.kaiba.m.core.domain.route.KbDispatchRule;
import com.kaiba.m.core.domain.route.KbMatchRule;
import com.kaiba.m.core.domain.route.KbRoute;
import com.kaiba.m.core.service.route.RouteService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.RestController;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * author: lyux
 * date: 2021-04-12
 */
@Slf4j
@RestController
public class RouteServiceController implements IRouteService {

    private final KbRes kbRes;
    private final RouteService routeService;

    public RouteServiceController(KbRes kbRes, RouteService routeService) {
        this.kbRes = kbRes;
        this.routeService = routeService;
    }

    @Override
    public KbEntity<RouteModel> createRouteByBody(RouteModificationModel createModel) {
        return createOrModifyRouteByBody(createModel, routeService::createRoute);
    }

    @Override
    public KbEntity<RouteModel> createRoute(
            String[] paths,
            String dispatchRuleId, String[] matchRuleIds, String[] authRuleIds,
            String[] permission, String permissionMessage, Integer state
    ) {
        RouteModificationModel createModel = new RouteModificationModel();
        createModel.setPaths(Arrays.asList(paths));
        createModel.setDispatchRuleId(dispatchRuleId);
        createModel.setMatchRuleIds(matchRuleIds == null ? null : Arrays.asList(matchRuleIds));
        createModel.setAuthRuleIds(authRuleIds == null ? null : Arrays.asList(authRuleIds));
        createModel.setPermissions(permission == null ? null : Arrays.asList(permission));
        createModel.setPermissionMessage(permissionMessage);
        createModel.setState(state);
        return createOrModifyRouteByBody(createModel, routeService::createRoute);
    }

    @Override
    public KbEntity<RouteModel> updateRouteByBody(
            RouteModificationModel updateModel) {
        return createOrModifyRouteByBody(updateModel, r -> routeService.updateRoute(r, true));
    }

    @Override
    public KbEntity<RouteModel> updateRoute(
            String routeId, String[] paths,
            String dispatchRuleId, String[] matchRuleIds, String[] authRuleIds,
            String[] permission, String permissionMessage, Integer state
    ) {
        RouteModificationModel updateModel = new RouteModificationModel();
        updateModel.setId(routeId);
        updateModel.setPaths(paths == null ? null : Arrays.asList(paths));
        updateModel.setDispatchRuleId(dispatchRuleId);
        updateModel.setMatchRuleIds(matchRuleIds == null ? null : Arrays.asList(matchRuleIds));
        updateModel.setAuthRuleIds(authRuleIds == null ? null : Arrays.asList(authRuleIds));
        updateModel.setPermissions(permission == null ? null : Arrays.asList(permission));
        updateModel.setPermissionMessage(permissionMessage);
        updateModel.setState(state);
        return createOrModifyRouteByBody(updateModel, r -> routeService.updateRoute(r, false));
    }

    @Override
    public KbEntity<Void> updateRouteState(String routeId, Integer state) {
        KbRouteState routeState = KbRouteState.valueOf(state).orElseThrow(() ->
                new KbException(KbCode.REQUEST_PARAM_INVALID, "wrong state: " + state).r("错误的状态值").li());
        routeService.updateRouteState(routeId, routeState);
        return kbRes.ok();
    }

    @Override
    public KbEntity<Void> deleteRoute(String routeId) {
        routeService.deleteRoute(routeId);
        return kbRes.ok();
    }

    @Override
    public KbEntity<RouteModel> getRouteById(String routeId) {
        return routeService.getRouteById(routeId)
                .map(this::route2model)
                .map(kbRes::ok)
                .orElse(kbRes.err(KbCode.RESOURCE_NOT_FOUND));
    }

    @Override
    public KbEntity<List<RouteModel>> getRouteListByIdIn(String[] routeIds) {
        List<KbRoute> routes = routeService.getRouteListByIdIn(routeIds);
        return kbRes.ok(routeList2model(routes));
    }

    @Override
    public KbEntity<List<RouteModel>> getRouteListByPath(String path, Integer page, Integer pageSize) {
        Page<KbRoute> routePage = routeService.getRoutePageByPath(path, page, pageSize);
        List<KbRoute> routeList = routePage.getContent();
        List<RouteModel> modelList = routeList2model(routeList);
        return kbRes.on(KbCode.OK)
                .setData(modelList)
                .setTotalPage(routePage.getTotalPages())
                .setTotalCount(routePage.getTotalElements())
                .create();
    }

    @Override
    public KbEntity<List<RouteModel>> getRouteListByPathStartWith(String path, Integer page, Integer pageSize) {
        Page<KbRoute> routePage = routeService.getRoutePageByPathStartWith(path, page, pageSize);
        List<KbRoute> routeList = routePage.getContent();
        List<RouteModel> modelList = routeList2model(routeList);
        return kbRes.on(KbCode.OK)
                .setData(modelList)
                .setTotalPage(routePage.getTotalPages())
                .setTotalCount(routePage.getTotalElements())
                .create();
    }

    @Override
    public KbEntity<List<RouteModel>> getRouteList(Integer[] states, Integer page, Integer pageSize) {
        Page<KbRoute> routePage = routeService.getRoutePage(states, page, pageSize);
        List<KbRoute> routeList = routePage.getContent();
        List<RouteModel> modelList = routeList2model(routeList);
        return kbRes.on(KbCode.OK)
                .setData(modelList)
                .setTotalPage(routePage.getTotalPages())
                .setTotalCount(routePage.getTotalElements())
                .create();
    }

    @Override
    public KbEntity<List<RouteModel>> getRouteListByModel(RouteQueryModel query) {
        Page<KbRoute> routePage = routeService.getRoutePageByModel(query);
        List<KbRoute> routeList = routePage.getContent();
        List<RouteModel> modelList = routeList2model(routeList);
        return kbRes.on(KbCode.OK)
                .setData(modelList)
                .setTotalPage(routePage.getTotalPages())
                .setTotalCount(routePage.getTotalElements())
                .create();
    }

    @Override
    public KbEntity<MatchRuleModel> createMatchRuleByBody(MatchRuleModel createModel) {
        KbMatchRule created = routeService.createMatchRule(Mapper.map(createModel, KbMatchRule.class));
        return kbRes.ok(Mapper.map(created, MatchRuleModel.class));
    }

    @Override
    public KbEntity<MatchRuleModel> updateMatchRuleByBody(MatchRuleModel updateModel) {
        KbMatchRule updated = routeService.updateMatchRule(Mapper.map(updateModel, KbMatchRule.class), true);
        return kbRes.ok(Mapper.map(updated, MatchRuleModel.class));
    }

    @Override
    public KbEntity<Void> updateMatchRuleState(String matchRuleId, Integer state) {
        KbRouteRuleState ruleState = KbRouteRuleState.valueOf(state).orElseThrow(() ->
                new KbException(KbCode.REQUEST_PARAM_INVALID, "wrong state: " + state).r("错误的状态值").li());
        routeService.updateMatchRuleState(matchRuleId, ruleState);
        return kbRes.ok();
    }

    @Override
    public KbEntity<Void> deleteMatchRule(String matchRuleId) {
        routeService.deleteMatchRule(matchRuleId);
        return kbRes.ok();
    }

    @Override
    public KbEntity<MatchRuleModel> getMatchRuleById(String matchRuleId) {
        return routeService.getMatchRuleById(matchRuleId)
                .map(matchRule -> Mapper.map(matchRule, MatchRuleModel.class))
                .map(kbRes::ok)
                .orElse(kbRes.err(KbCode.RESOURCE_NOT_FOUND));
    }

    @Override
    public KbEntity<List<MatchRuleModel>> getMatchRuleListByIdIn(String[] matchRuleIds) {
        List<MatchRuleModel> list = routeService.getMatchRuleListByIdIn(matchRuleIds).stream()
                .map(matchRule -> Mapper.map(matchRule, MatchRuleModel.class))
                .collect(Collectors.toList());
        return kbRes.ok(list);
    }

    @Override
    public KbEntity<List<MatchRuleModel>> getMatchRuleList(
            Integer scope, Integer state, Integer page, Integer pageSize) {
        Page<MatchRuleModel> rulePage = routeService.getMatchRulePage(state, scope, page, pageSize)
                .map(matchRule -> Mapper.map(matchRule, MatchRuleModel.class));
        return kbRes.ok(rulePage);
    }

    @Override
    public KbEntity<DispatchRuleModel> createDispatchRuleByBody(DispatchRuleModel createModel) {
        KbDispatchRule created = routeService.createDispatchRule(Mapper.map(createModel, KbDispatchRule.class));
        return kbRes.ok(Mapper.map(created, DispatchRuleModel.class));
    }

    @Override
    public KbEntity<DispatchRuleModel> createDispatchRule(
            Integer downstreamType, String target,
            Integer prefixStrip, String prefixAdd,
            Integer redirectCode, Boolean redirectWithParam,
            Integer dummyKbCode, String dummyMessage,
            Integer scope, String name, String description
    ) {
        DispatchRuleModel createModel = new DispatchRuleModel();
        createModel.setDownstreamType(downstreamType);
        createModel.setTarget(target);
        createModel.setPrefixStrip(prefixStrip);
        createModel.setPrefixAdd(prefixAdd);
        createModel.setRedirectCode(redirectCode);
        createModel.setRedirectWithParam(redirectWithParam);
        createModel.setDummyKbCode(dummyKbCode);
        createModel.setDummyMessage(dummyMessage);
        createModel.setScope(scope);
        createModel.setName(name);
        createModel.setDescription(description);
        return createDispatchRuleByBody(createModel);
    }

    @Override
    public KbEntity<DispatchRuleModel> updateDispatchRuleByBody(DispatchRuleModel updateModel) {
        KbDispatchRule updated = routeService.updateDispatchRule(
                Mapper.map(updateModel, KbDispatchRule.class), true);
        return kbRes.ok(Mapper.map(updated, DispatchRuleModel.class));
    }

    @Override
    public KbEntity<DispatchRuleModel> updateDispatchRule(
            String dispatchRuleId, Integer downstreamType, String target,
            Integer prefixStrip, String prefixAdd,
            Integer redirectCode, Boolean redirectWithParam,
            Integer dummyKbCode, String dummyMessage,
            Integer scope, String name, String description
    ) {
        DispatchRuleModel updateModel = new DispatchRuleModel();
        updateModel.setId(dispatchRuleId);
        updateModel.setDownstreamType(downstreamType);
        updateModel.setTarget(target);
        updateModel.setPrefixStrip(prefixStrip);
        updateModel.setPrefixAdd(prefixAdd);
        updateModel.setRedirectCode(redirectCode);
        updateModel.setRedirectWithParam(redirectWithParam);
        updateModel.setDummyKbCode(dummyKbCode);
        updateModel.setDummyMessage(dummyMessage);
        updateModel.setScope(scope);
        updateModel.setName(name);
        updateModel.setDescription(description);
        KbDispatchRule updated = routeService.updateDispatchRule(
                Mapper.map(updateModel, KbDispatchRule.class), false);
        return kbRes.ok(Mapper.map(updated, DispatchRuleModel.class));
    }

    @Override
    public KbEntity<Void> deleteDispatchRule(String dispatchRuleId) {
        routeService.deleteDispatchRule(dispatchRuleId);
        return kbRes.ok();
    }

    @Override
    public KbEntity<DispatchRuleModel> getDispatchRuleById(String dispatchRuleId) {
        return routeService.getDispatchRuleById(dispatchRuleId)
                .map(rule -> Mapper.map(rule, DispatchRuleModel.class))
                .map(kbRes::ok)
                .orElse(kbRes.err(KbCode.RESOURCE_NOT_FOUND));
    }

    @Override
    public KbEntity<List<DispatchRuleModel>> getDispatchRuleListByIdIn(String[] dispatchRuleIds) {
        List<DispatchRuleModel> list = routeService.getDispatchRuleListByIdIn(dispatchRuleIds).stream()
                .map(rule -> Mapper.map(rule, DispatchRuleModel.class))
                .collect(Collectors.toList());
        return kbRes.ok(list);
    }

    @Override
    public KbEntity<List<DispatchRuleModel>> getDispatchRuleList(Integer scope, Integer page, Integer pageSize) {
        Page<DispatchRuleModel> rulePage = routeService.getDispatchRulePage(scope, page, pageSize)
                .map(rule -> Mapper.map(rule, DispatchRuleModel.class));
        return kbRes.ok(rulePage);
    }

    @Override
    public KbEntity<AuthRuleModel> createAuthRuleByBody(AuthRuleModel createModel) {
        KbAuthRule created = routeService.createAuthRule(Mapper.map(createModel, KbAuthRule.class));
        return kbRes.ok(Mapper.map(created, AuthRuleModel.class));
    }

    @Override
    public KbEntity<AuthRuleModel> createAuthRule(
            Integer method, Integer[] endpoint, Long loginTimeLimit,
            Integer state, Integer scope,
            String name, String description
    ) {
        AuthRuleModel createModel = new AuthRuleModel();
        createModel.setMethod(method);
        createModel.setEndpoint(endpoint == null ? null : Arrays.asList(endpoint));
        createModel.setLoginTimeLimit(loginTimeLimit);
        createModel.setState(state);
        createModel.setScope(scope);
        createModel.setName(name);
        createModel.setDescription(description);
        return createAuthRuleByBody(createModel);
    }

    @Override
    public KbEntity<AuthRuleModel> updateAuthRuleByBody(AuthRuleModel updateModel) {
        KbAuthRule updated = routeService.updateAuthRule(
                Mapper.map(updateModel, KbAuthRule.class), true);
        return kbRes.ok(Mapper.map(updated, AuthRuleModel.class));
    }

    @Override
    public KbEntity<AuthRuleModel> updateAuthRule(
            String authRuleId, Integer method, Integer[] endpoint, Long loginTimeLimit,
            Integer state, Integer scope,
            String name, String description
    ) {
        AuthRuleModel updateModel = new AuthRuleModel();
        updateModel.setId(authRuleId);
        updateModel.setMethod(method);
        updateModel.setEndpoint(endpoint == null ? null : Arrays.asList(endpoint));
        updateModel.setLoginTimeLimit(loginTimeLimit);
        updateModel.setState(state);
        updateModel.setScope(scope);
        updateModel.setName(name);
        updateModel.setDescription(description);
        KbAuthRule updated = routeService.updateAuthRule(
                Mapper.map(updateModel, KbAuthRule.class), false);
        return kbRes.ok(Mapper.map(updated, AuthRuleModel.class));
    }

    @Override
    public KbEntity<Void> updateAuthRuleState(String authRuleId, Integer state) {
        KbRouteRuleState ruleState = KbRouteRuleState.valueOf(state).orElseThrow(() ->
                new KbException(KbCode.REQUEST_PARAM_INVALID, "wrong state: " + state).r("错误的状态值").li());
        routeService.updateAuthRuleState(authRuleId, ruleState);
        return kbRes.ok();
    }

    @Override
    public KbEntity<Void> deleteAuthRule(String authRuleId) {
        routeService.deleteAuthRule(authRuleId);
        return kbRes.ok();
    }

    @Override
    public KbEntity<AuthRuleModel> getAuthRuleById(String authRuleId) {
        return routeService.getAuthRuleById(authRuleId)
                .map(rule -> Mapper.map(rule, AuthRuleModel.class))
                .map(kbRes::ok)
                .orElse(kbRes.err(KbCode.RESOURCE_NOT_FOUND));
    }

    @Override
    public KbEntity<List<AuthRuleModel>> getAuthRuleListByIdIn(String[] authRuleIds) {
        List<AuthRuleModel> list = routeService.getAuthRuleListByIdIn(authRuleIds).stream()
                .map(rule -> Mapper.map(rule, AuthRuleModel.class))
                .collect(Collectors.toList());
        return kbRes.ok(list);
    }

    @Override
    public KbEntity<List<AuthRuleModel>> getAuthRuleList(
            Integer scope, Integer state, Integer page, Integer pageSize) {
        Page<AuthRuleModel> rulePage = routeService.getAuthRulePage(state, scope, page, pageSize)
                .map(rule -> Mapper.map(rule, AuthRuleModel.class));
        return kbRes.ok(rulePage);
    }

    // ---------------------------------------------------------------

    private KbEntity<RouteModel> createOrModifyRouteByBody(
            RouteModificationModel m, Function<KbRoute, KbRoute> modify) {
        DispatchRuleModel dispatchRule = null;
        if (m.getDispatchRuleId() != null) {
            dispatchRule = routeService
                    .getDispatchRuleById(m.getDispatchRuleId())
                    .map(rule -> Mapper.map(rule, DispatchRuleModel.class))
                    .orElseThrow(() ->
                            new KbException(KbCode.REQUEST_PARAM_INVALID,
                                    "dispatch rule not found: " + m.getDispatchRuleId()).r("派发规则不存在").li());
        }
        List<MatchRuleModel> matchRules = null;
        if (m.getMatchRuleIds() != null && m.getMatchRuleIds().size() != 0) {
            matchRules = routeService
                    .getMatchRuleListByIdIn(m.getMatchRuleIds().toArray(ArrayTypeHolder.EMPTY_STRING_ARRAY)).stream()
                    .map(matheRule -> Mapper.map(matheRule, MatchRuleModel.class))
                    .collect(Collectors.toList());
            if (m.getMatchRuleIds().size() != matchRules.size()) {
                throw new KbException(KbCode.REQUEST_PARAM_INVALID, "match rule mismatch").r("匹配规则不存在").li();
            }
        }
        List<AuthRuleModel> authRules = null;
        if (m.getAuthRuleIds() != null && m.getAuthRuleIds().size() != 0) {
            authRules = routeService
                    .getAuthRuleListByIdIn(m.getAuthRuleIds().toArray(ArrayTypeHolder.EMPTY_STRING_ARRAY)).stream()
                    .map(authRule -> Mapper.map(authRule, AuthRuleModel.class))
                    .collect(Collectors.toList());
            if (m.getAuthRuleIds().size() != authRules.size()) {
                throw new KbException(KbCode.REQUEST_PARAM_INVALID, "auth rule mismatch").r("鉴权规则不存在").li();
            }
        }
        KbRoute modified = modify.apply(Mapper.map(m, KbRoute.class));
        RouteModel model = Mapper.map(modified, RouteModel.class);
        model.setDispatchRule(dispatchRule);
        model.setMatchRules(matchRules);
        model.setAuthRules(authRules);
        return kbRes.ok(model);
    }

    private RouteModel route2model(KbRoute route) {
        RouteModel model = Mapper.map(route, RouteModel.class);
        model.setDispatchRule(routeService
                .getDispatchRuleById(route.getDispatchRuleId())
                .map(dispatchRule -> Mapper.map(dispatchRule, DispatchRuleModel.class))
                .orElse(null));
        if (route.getMatchRuleIds() != null) {
            model.setMatchRules(routeService
                    .getMatchRuleListByIdIn(route.getMatchRuleIds().toArray(ArrayTypeHolder.EMPTY_STRING_ARRAY)).stream()
                    .map(matheRule -> Mapper.map(matheRule, MatchRuleModel.class))
                    .collect(Collectors.toList()));
        }
        if (route.getAuthRuleIds() != null) {
            model.setAuthRules(routeService
                    .getAuthRuleListByIdIn(route.getAuthRuleIds().toArray(ArrayTypeHolder.EMPTY_STRING_ARRAY)).stream()
                    .map(authRule -> Mapper.map(authRule, AuthRuleModel.class))
                    .collect(Collectors.toList()));
        }
        return model;
    }

    private List<RouteModel> routeList2model(List<KbRoute> routes) {
        Set<String> dispatchRuleIds = new HashSet<>();
        Set<String> matchRuleIds = new HashSet<>();
        Set<String> authRuleIds = new HashSet<>();
        for (KbRoute route : routes) {
            dispatchRuleIds.add(route.getDispatchRuleId());
            if (route.getMatchRuleIds() != null) {
                matchRuleIds.addAll(route.getMatchRuleIds());
            }
            if (route.getAuthRuleIds() != null) {
                authRuleIds.addAll(route.getAuthRuleIds());
            }
        }
        Map<String, DispatchRuleModel> dispatchRuleMap = routeService
                .getDispatchRuleListByIdIn(dispatchRuleIds.toArray(ArrayTypeHolder.EMPTY_STRING_ARRAY)).stream()
                .map(dispatchRule -> Mapper.map(dispatchRule, DispatchRuleModel.class))
                .collect(Collectors.toMap(DispatchRuleModel::getId, r -> r));
        Map<String, MatchRuleModel> matchRuleMap = routeService
                .getMatchRuleListByIdIn(matchRuleIds.toArray(ArrayTypeHolder.EMPTY_STRING_ARRAY)).stream()
                .map(matheRule -> Mapper.map(matheRule, MatchRuleModel.class))
                .collect(Collectors.toMap(MatchRuleModel::getId, r -> r));
        Map<String, AuthRuleModel> authRuleMap = routeService
                .getAuthRuleListByIdIn(authRuleIds.toArray(ArrayTypeHolder.EMPTY_STRING_ARRAY)).stream()
                .map(authRule -> Mapper.map(authRule, AuthRuleModel.class))
                .collect(Collectors.toMap(AuthRuleModel::getId, r -> r));
        return routes.stream()
                .map(route -> {
                    RouteModel model = Mapper.map(route, RouteModel.class);
                    model.setDispatchRule(dispatchRuleMap.get(model.getDispatchRuleId()));
                    if (model.getDispatchRule() == null) {
                        log.warn("dispatch rule not found: " + model.getDispatchRuleId());
                    }
                    if (model.getMatchRuleIds() != null) {
                        model.setMatchRules(
                                model.getMatchRuleIds().stream().map(matchRuleMap::get).collect(Collectors.toList()));
                    }
                    if (model.getAuthRuleIds() != null) {
                        model.setAuthRules(
                                model.getAuthRuleIds().stream().map(authRuleMap::get).collect(Collectors.toList()));
                    }
                    return model;
                })
                .collect(Collectors.toList());
    }

}
