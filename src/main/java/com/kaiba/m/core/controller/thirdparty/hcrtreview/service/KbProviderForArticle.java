package com.kaiba.m.core.controller.thirdparty.hcrtreview.service;

import com.kaiba.lib.base.constant.news.article.NewsContentType;
import com.kaiba.lib.base.constant.news.article.NewsState;
import com.kaiba.lib.base.domain.common.Image;
import com.kaiba.lib.base.domain.common.KbTimeRange;
import com.kaiba.lib.base.domain.news.article.ArticleModel;
import com.kaiba.lib.base.domain.news.article.ArticleQueryModel;
import com.kaiba.lib.base.service.INeoNewsArticleService;
import com.kaiba.lib.base.util.StringUtils;
import com.kaiba.m.core.controller.thirdparty.hcrtreview.constant.KbBizType;
import com.kaiba.m.core.controller.thirdparty.hcrtreview.constant.KbHCRTReviewConst;
import com.kaiba.m.core.controller.thirdparty.hcrtreview.constant.KbItemType;
import com.kaiba.m.core.controller.thirdparty.hcrtreview.model.KbContent;
import com.kaiba.m.core.controller.thirdparty.hcrtreview.model.KbResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * author: lyux
 * date: 2024-10-24
 */
@Slf4j
@Component
public class KbProviderForArticle implements IKbProvider {

    private final INeoNewsArticleService articleService;

    public KbProviderForArticle(INeoNewsArticleService articleService) {
        this.articleService = articleService;
    }

    @Override
    public KbBizType getBiz() {
        return KbBizType.ARTICLE;
    }

    @Override
    public KbResult getContentList(int page, int pageSize, long until) {
        int max = 100_000 / pageSize;
        for (int i = page; i < max; i ++) {
            KbResult result = request(i, pageSize, until);
            if (result.getContents().isEmpty() && result.getHasNext()) {
                if (KbHCRTReviewConst.DEBUG) {
                    log.info("HCRTReview, get note, result content empty, yet hasNext is true, continue loop");
                }
            } else {
                if (KbHCRTReviewConst.DEBUG) {
                    log.info(result.toString());
                }
                return result;
            }
        }

        return KbResult.asLast(KbBizType.ARTICLE);
    }

    private KbResult request(int page, int pageSize, long until) {
        ArticleQueryModel query = new ArticleQueryModel();
        query.setStates(Arrays.asList(NewsState.SIGNED.name(), NewsState.ONLINE.name()));
        query.setPage(page);
        query.setPageSize(pageSize);
        query.setWithContent(true);
        query.setUpdateTimeRange(KbTimeRange.rangeGTE(until));
        List<ArticleModel> articleList = articleService.getArticleListByQuery(query)
                .data().orElse(Collections.emptyList());
        List<KbContent> contentList = articleList.stream()
                .map(article -> {
                    KbContent content = new KbContent(KbBizType.ARTICLE, article.getId());
                    if (article.getTitle() != null) {
                        content.add(KbItemType.TEXT, article.getTitle(), "title", "标题");
                    }
                    if (!StringUtils.isEmpty(article.getSubtitle())) {
                        content.add(KbItemType.TEXT, article.getSubtitle(), "subtitle", "副标题");
                    }
                    if (article.getCovers() != null && !article.getCovers().isEmpty()) {
                        for (Image image : article.getCovers()) {
                            content.add(image, "cover", "封面");
                        }
                    }
                    if (article.getContent() != null && article.getContentType() != null) {
                        NewsContentType type = NewsContentType.resolveByName(article.getContentType())
                                .orElse(NewsContentType.NONE);
                        if (type.isHtml()) {
                            content.add(KbItemType.HTML, article.getContent(), "body", "富文本正文");
                        } else if (type.isHasContent()) {
                            content.add(KbItemType.TEXT, article.getContent(), "body", "纯文本正文");
                        }
                    }
                    if (article.getVideo() != null && article.getVideo().getVideoUrl() != null) {
                        String videoUrl = KbContent.assembleMediaUrl(article.getVideo().getVideoUrl());
                        content.add(KbItemType.VIDEO, videoUrl, "video", "正文视频");
                        if (article.getVideo().getCoverUrl() != null) {
                            String videoCoverUrl = KbContent.assembleMediaUrl(article.getVideo().getCoverUrl());
                            content.add(KbItemType.IMAGE, videoCoverUrl, "video_cover", "正文视频封面");
                        }
                    }
                    return content;
                })
                .filter(KbContent::notEmpty)
                .collect(Collectors.toList());
        KbResult result = new KbResult(KbBizType.ARTICLE);
        result.setContents(contentList);
        if (articleList.isEmpty()) {
            result.setHasNext(false);
        } else {
            result.setHasNext(true);
            result.setNextPage(page + 1);
        }
        return result;
    }

}
