package com.kaiba.m.core.controller.program;

import com.google.gson.reflect.TypeToken;
import com.kaiba.lib.base.assembler.Mapper;
import com.kaiba.lib.base.constant.program.BroadcastAudioChannel;
import com.kaiba.lib.base.constant.program.BroadcastAudioPlayer;
import com.kaiba.lib.base.domain.common.AppVersionRange;
import com.kaiba.lib.base.domain.program.BroadcastAudioModel;
import com.kaiba.lib.base.middleware.KbRes;
import com.kaiba.lib.base.response.KbCode;
import com.kaiba.lib.base.response.KbEntity;
import com.kaiba.lib.base.response.KbException;
import com.kaiba.lib.base.service.IBroadcastAudioService;
import com.kaiba.lib.base.util.StringUtils;
import com.kaiba.m.core.domain.program.BroadcastAudio;
import com.kaiba.m.core.service.program.BroadcastAudioService;
import com.kaiba.m.core.util.JsonUtils;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.web.client.RestTemplateBuilder;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Slf4j
@RestController
public class BroadcastAudioServiceController implements IBroadcastAudioService {

    @Value("${kaiba.job.audio.id.owner}")
    private String mOwnerAudioId;

    @Value("${kaiba.job.audio.id.qting}")
    private String mQTingAudioId;

    private final KbRes kbRes;
    private final BroadcastAudioService audioService;
    private static final RestTemplate restTemplate = new RestTemplateBuilder().build();

    public BroadcastAudioServiceController(KbRes kbRes, BroadcastAudioService audioService) {
        this.kbRes = kbRes;
        this.audioService = audioService;
    }

    @Override
    public KbEntity<Void> upsertBase(String id, Integer siteId, String url, String name , String channel, String channelId, Boolean rebroadcast) {
        BroadcastAudioChannel.valueOf(channel);
        if (StringUtils.isEmpty(url) && StringUtils.isEmpty(channelId)){
            return kbRes.err(KbCode.REQUEST_PARAM_INVALID,"","url and channelId cann't be empty as same time");
        }

        BroadcastAudio audio;
        if (StringUtils.isEmpty(id)){
            audio = new BroadcastAudio();
            audio.setSiteId(siteId);
            audio.setRebroadcast(rebroadcast);
            audio.setIOSPlayer(BroadcastAudioPlayer.ORIGIN.name());
            audio.setAndroidPlayer(BroadcastAudioPlayer.ORIGIN.name());
            audio.setExclusiveVersion(Collections.emptyList());
        } else {
            audio = audioService.getDetailById(id).orElseThrow(()->new KbException(KbCode.RESOURCE_NOT_FOUND).li());
        }
        audio.setUrl(url);
        audio.setName(name);
        audio.setChannel(channel);
        audio.setChannelId(channelId);
        audioService.save(audio);

        return kbRes.ok();
    }

    @Override
    public KbEntity<Void> updateById(String id, String url, String name, String channel, String channelId, String iOSPlayer,
                                     String androidPlayer, String exclusiveVersion
    ) {
        BroadcastAudio audio = audioService.getDetailById(id).orElseThrow(()->new KbException(KbCode.RESOURCE_NOT_FOUND).li());
        if (!audio.getRebroadcast() && !StringUtils.isEmpty(url)) {
            audio.setUrl(url);
        }
        if (!StringUtils.isEmpty(name)) {
            audio.setName(name);
        }
        if (audio.getRebroadcast() && !StringUtils.isEmpty(url)) {
            audio.setChannelId(channelId);
        }
        if (!StringUtils.isEmpty(channel)){
            audio.setChannel(channel);
        }
        if (!StringUtils.isEmpty(iOSPlayer)){
            BroadcastAudioPlayer.valueOf(iOSPlayer);
            audio.setIOSPlayer(iOSPlayer);
        }
        if (!StringUtils.isEmpty(androidPlayer)){
            BroadcastAudioPlayer.valueOf(androidPlayer);
            audio.setAndroidPlayer(androidPlayer);
        }
        if (!StringUtils.isEmpty(exclusiveVersion)){
            List<AppVersionRange> rangeList = JsonUtils.getGson().fromJson(exclusiveVersion, new TypeToken<List<AppVersionRange>>(){}.getType());
            audio.setExclusiveVersion(rangeList);
        }
        audioService.save(audio);
        return kbRes.ok();
    }

    @Override
    public KbEntity<Void> updateByChannel(String channel, String iOSPlayer, String androidPlayer, String exclusiveVersion) {
        BroadcastAudioPlayer iPlayer = null;
        BroadcastAudioPlayer aPlayer = null;
        if (!StringUtils.isEmpty(iOSPlayer)){
            iPlayer = BroadcastAudioPlayer.valueOf(iOSPlayer);
        }
        if (!StringUtils.isEmpty(androidPlayer)){
            aPlayer = BroadcastAudioPlayer.valueOf(androidPlayer);
        }
        List<AppVersionRange> ranges = JsonUtils.getGson().fromJson(exclusiveVersion, new TypeToken<List<AppVersionRange>>(){}.getType());
        audioService.updateByChannel(channel, iPlayer, aPlayer, ranges);
        return kbRes.ok();
    }

    @Override
    public KbEntity<Void> addExclusiveVersionById(String id, String exclusiveVersion){
        BroadcastAudio audio = audioService.getDetailById(id).orElseThrow(()->new KbException(KbCode.RESOURCE_NOT_FOUND).li());
        List<AppVersionRange> ranges = JsonUtils.getGson().fromJson(exclusiveVersion, new TypeToken<List<AppVersionRange>>(){}.getType());
        if (audio.getExclusiveVersion() == null){
            audio.setExclusiveVersion(ranges);
        }else {
            audio.getExclusiveVersion().addAll(ranges);
        }
        audioService.save(audio);
        return kbRes.ok();
    }

    @Override
    public KbEntity<Void> changeToDefault(String id) {
        audioService.changeToDefault(id);
        return kbRes.ok();
    }

    @Override
    public KbEntity<Void> deleteById(Integer siteId, String id) {
        audioService.delete(siteId, id);
        return kbRes.ok();
    }

    @Override
    public KbEntity<List<BroadcastAudioModel>> getListBySiteId(Integer siteId) {
        List<BroadcastAudio> list = audioService.getListBySiteIdAndRebroadcast(siteId);
        List<BroadcastAudioModel> models = list.stream().map(this::audio2Model).collect(Collectors.toList());
        return kbRes.ok(models);
    }

    @XxlJob("rizhao-audio-stream")
    public ReturnT<String> refreshRiZhaoAudioStream(String param) {
        String url = "http://v.rzw.com.cn/m2o/player/drm.php?url=http://stream.rzw.com.cn/gbjtsh/sd/live.m3u8";
        String response = restTemplate.getForObject(url,String.class);
        log.info("ri zhao new broad url: "+ url);
        if (!StringUtils.isEmpty(response)){
            Optional<BroadcastAudio> optional = audioService.getDefaultBySiteIdAndRebroadcast(53, false);
            optional.ifPresent(audio -> {
                audio.setUrl(response);
                audioService.save(audio);
            });
        }
        return ReturnT.SUCCESS;
    }

    @XxlJob("hz-audio-stream-start")
    public ReturnT<String> refreshAudioStreamHZStart(String param) {
        // todo 临时方案
        log.info("[- change audio stream to owner -] " + mOwnerAudioId);
        audioService.changeToDefault(mOwnerAudioId);
        return ReturnT.SUCCESS;
    }

    @XxlJob("hz-audio-stream-end")
    public ReturnT<String> refreshAudioStreamHZEnd(String param) {
        // todo 临时方案
        log.info("[- change audio stream to qting -] " + mQTingAudioId);
        audioService.changeToDefault(mQTingAudioId);
        return ReturnT.SUCCESS;
    }

    private BroadcastAudioModel audio2Model(BroadcastAudio audio) {
        BroadcastAudioModel model = Mapper.map(audio, BroadcastAudioModel.class);
        model.setChannelName(BroadcastAudioChannel.valueOf(audio.getChannel()).getDescription());
        model.setIOSPlayerName(BroadcastAudioPlayer.valueOf(audio.getIOSPlayer()).getDescription());
        model.setAndroidPlayerName(BroadcastAudioPlayer.valueOf(audio.getAndroidPlayer()).getDescription());
        return model;
    }
}
