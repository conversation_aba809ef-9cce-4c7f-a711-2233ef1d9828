package com.kaiba.m.core.controller.user;

import com.kaiba.lib.base.annotation.apiparam.KbPage;
import com.kaiba.lib.base.assembler.Mapper;
import com.kaiba.lib.base.constant.KbHeader;
import com.kaiba.lib.base.constant.user.UserOrnamentState;
import com.kaiba.lib.base.domain.user.UserModel;
import com.kaiba.lib.base.domain.user.ornament.*;
import com.kaiba.lib.base.middleware.KbRes;
import com.kaiba.lib.base.response.KbCode;
import com.kaiba.lib.base.response.KbEntity;
import com.kaiba.lib.base.response.KbException;
import com.kaiba.m.core.domain.user.ornament.*;
import com.kaiba.m.core.service.user.UserBasicCacheService;
import com.kaiba.m.core.service.user.UserService;
import com.kaiba.m.core.service.user.ornament.UserOrnamentCacheService;
import com.kaiba.m.core.service.user.ornament.UserOrnamentDataService;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.domain.Page;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.*;
import java.util.stream.Collectors;

/**
 * author: lyux
 * date: 2025-04-25
 */
@Slf4j
@RestController
@RequestMapping("/n/user/ornament")
@Validated
public class UserOrnamentApiController {

    private final KbRes kbRes;
    private final UserService userService;
    private final UserBasicCacheService userBasicCacheService;
    private final UserOrnamentDataService ornamentDataService;
    private final UserOrnamentCacheService ornamentCacheService;

    public UserOrnamentApiController(
            @Qualifier("kbApiRes") KbRes kbRes,
            UserService userService,
            UserBasicCacheService userBasicCacheService,
            UserOrnamentDataService ornamentDataService,
            UserOrnamentCacheService ornamentCacheService
    ) {
        this.kbRes = kbRes;
        this.userService = userService;
        this.userBasicCacheService = userBasicCacheService;
        this.ornamentDataService = ornamentDataService;
        this.ornamentCacheService = ornamentCacheService;
    }

    // ------------------------------------------------------------------

    @ApiOperation("设置头冠是否在指定的用户信息栏显示. 用户必须首先拥有该头冠.")
    @PostMapping(path = "/usr/updateDisplayUserCrest")
    public KbEntity<Void> updateDisplayUserCrest(
            @RequestHeader(KbHeader.KB_USER_ID) Integer userId,
            @RequestParam(required = false) @ApiParam("空值表示清空") String crest
    ) {
        if (crest == null) {
            userService.updateDisplayCrest(userId, null);
            userBasicCacheService.invalidateUser(userId);
            return kbRes.ok();
        } else if (ornamentDataService.getCrestByUserAndCrest(userId, crest).isPresent()) {
            userService.updateDisplayCrest(userId, crest);
            userBasicCacheService.invalidateUser(userId);
            return kbRes.ok();
        } else {
            return kbRes.msg(KbCode.ILLEGAL_STATE, "尚未获取该头冠");
        }
    }

    @ApiOperation("设置多个徽章的用户信息栏显示展示状态, 不在列表中的徽章将在用户信息栏隐藏. 用户必须首先拥有该徽章")
    @PostMapping(path = "/usr/updateDisplayUserPendantList")
    public KbEntity<Void> updateDisplayUserPendantList(
            @RequestHeader(KbHeader.KB_USER_ID) Integer userId,
            @RequestParam(required = false) @ApiParam("传空列表会隐藏全部徽章") String[] pendants
    ) {
        if (pendants == null || pendants.length == 0) {
            userService.updateDisplayPendants(userId, null);
        } else {
            Set<String> set = new HashSet<>(Arrays.asList(pendants));
            List<String> list = new ArrayList<>(set);
            List<UserPendant> userOwnedList = ornamentDataService
                    .getPendantListByUserAndPendantIn(userId, list);
            if (userOwnedList.size() != list.size()) {
                return kbRes.msg(KbCode.ILLEGAL_STATE, "尚未获取徽章");
            }
            userService.updateDisplayPendants(userId, list);
        }
        userBasicCacheService.invalidateUser(userId);
        return kbRes.ok();
    }

    @ApiOperation("设置认证是否在某个用户信息栏显示. 用户必须首先拥有该认证")
    @PostMapping(path = "/usr/updateDisplayUserCred")
    KbEntity<Void> updateDisplayUserCred(
            @RequestHeader(KbHeader.KB_USER_ID) Integer userId,
            @RequestParam(required = false) @ApiParam("空值表示清空") String cred
    ) {
        if (cred == null) {
            userService.updateDisplayCred(userId, null);
            userBasicCacheService.invalidateUser(userId);
            return kbRes.ok();
        } else if (ornamentDataService.getCredByUserAndCred(userId, cred).isPresent()) {
            userService.updateDisplayCred(userId, cred);
            userBasicCacheService.invalidateUser(userId);
            return kbRes.ok();
        } else {
            return kbRes.msg(KbCode.ILLEGAL_STATE, "尚未获取该认证");
        }
    }

    // ------------------------------------------------------------------

    @ApiOperation("根据用户ID获取在用户信息栏展示的用户头冠信息")
    @PostMapping(path = "/obj/getDisplayCrestByUser")
    public KbEntity<UserCrestModel> getDisplayCrestByUser(
            @RequestParam() Integer userId
    ) {
        String displayCrest = userBasicCacheService.getUserById(userId)
                .map(UserModel::getDisplayCrest).orElse(null);
        if (displayCrest == null) {
            return kbRes.ok();
        } else {
            return getCrestByUser(userId, displayCrest);
        }
    }

    @ApiOperation("根据用户ID和头冠标识获取用户拥有的头冠信息, 未获取该头冠则返回空")
    @PostMapping(path = "/obj/getCrestByUser")
    public KbEntity<UserCrestModel> getCrestByUser(
            @RequestParam() Integer userId,
            @RequestParam() String crest
    ) {
        CrestModel crestDef = ornamentCacheService.getCrest(crest)
                .map(UserOrnamentApiController::crest2model)
                .orElseThrow(() -> new KbException(KbCode.RESOURCE_NOT_FOUND).r("头冠未知或已下线").li());
        return ornamentDataService.getCrestByUserAndCrest(userId, crest)
                .map(c -> Mapper.map(c, UserCrestModel.class))
                .map(m -> {
                    m.setCrestDef(crestDef);
                    return m;
                })
                .map(kbRes::ok)
                .orElseGet(kbRes::ok);
    }

    @ApiOperation("根据用户ID获取用户拥有的所有头冠信息")
    @PostMapping(path = "/obj/getCrestListByUser")
    public KbEntity<List<UserCrestModel>> getCrestListByUser(
            @RequestParam() Integer userId,
            @RequestParam(required = false, defaultValue = "1") @KbPage Integer page,
            @RequestParam(required = false, defaultValue = "15") @KbPage Integer pageSize
    ) {
        Page<UserCrest> ucPage = ornamentDataService.getCrestPageByUser(userId, page, pageSize);
        Set<String> set = ucPage.map(UserCrest::getCrest).toSet();
        Map<String, CrestDef> defMap = ornamentCacheService.getCrestMap(set);
        return kbRes.ok(ucPage.map(u -> {
            UserCrestModel model = Mapper.map(u, UserCrestModel.class);
            model.setCrestDef(crest2model(defMap.get(u.getCrest())));
            return model;
        }));
    }

    // ------------------------------------------------------------------

    @ApiOperation("根据用户ID获取在用户信息栏展示的用户徽章信息")
    @PostMapping(path = "/obj/getDisplayPendantListByUser")
    public KbEntity<List<UserPendantModel>> getDisplayPendantListByUser(
            @RequestParam() Integer userId
    ) {
        List<String> displayPendants = userBasicCacheService.getUserById(userId)
                .map(UserModel::getDisplayPendants).orElse(null);
        if (displayPendants == null || displayPendants.isEmpty()) {
            return kbRes.ok();
        } else {
            Map<String, PendantDef> defMap = ornamentCacheService.getPendantMap(displayPendants);
            List<UserPendantModel> list = ornamentDataService.getPendantListByUserAndPendantIn(userId, displayPendants).stream()
                    .map(u -> Mapper.map(u, UserPendantModel.class))
                    .peek(m -> m.setPendantDef(pendant2model(defMap.get(m.getPendant()))))
                    .filter(m -> m.getPendantDef() != null)
                    .collect(Collectors.toList());
            return kbRes.ok(list);
        }
    }

    @ApiOperation("根据用户ID和徽章标识获取用户拥有的徽章信息, 未获取该徽章则返回空")
    @PostMapping(path = "/obj/getPendantByUser")
    public KbEntity<UserPendantModel> getPendantByUser(
            @RequestParam() Integer userId,
            @RequestParam() String pendant
    ) {
        PendantModel def = ornamentCacheService.getPendant(pendant)
                .map(UserOrnamentApiController::pendant2model)
                .orElseThrow(() -> new KbException(KbCode.RESOURCE_NOT_FOUND).r("徽章未定义").li());
        return ornamentDataService.getPendantByUserAndPendant(userId, pendant)
                .map(c -> Mapper.map(c, UserPendantModel.class))
                .map(m -> {
                    m.setPendantDef(def);
                    return m;
                })
                .map(kbRes::ok)
                .orElseGet(kbRes::ok);
    }

    @ApiOperation("根据用户ID获取用户拥有的所有徽章信息")
    @PostMapping(path = "/obj/getPendantListByUser")
    public KbEntity<List<UserPendantModel>> getPendantListByUser(
            @RequestParam() Integer userId,
            @RequestParam(required = false, defaultValue = "1") @KbPage Integer page,
            @RequestParam(required = false, defaultValue = "15") @KbPage Integer pageSize
    ) {
        Page<UserPendant> ucPage = ornamentDataService.getPendantPageByUser(userId, page, pageSize);
        Set<String> set = ucPage.map(UserPendant::getPendant).toSet();
        Map<String, PendantDef> defMap = ornamentCacheService.getPendantMap(set);
        return kbRes.ok(ucPage.map(u -> {
            UserPendantModel model = Mapper.map(u, UserPendantModel.class);
            model.setPendantDef(pendant2model(defMap.get(u.getPendant())));
            return model;
        }));
    }

    // ------------------------------------------------------------------

    @ApiOperation("根据用户ID获取在用户信息栏展示的用户认证信息")
    @PostMapping(path = "/obj/getDisplayCredByUser")
    KbEntity<UserCredModel> getDisplayCredByUser(
            @RequestParam() Integer userId,
            @RequestParam(required = false, defaultValue = "false") @ApiParam("是否填充头冠和徽章数据结构") Boolean fill
    ) {
        String displayCred = userBasicCacheService.getUserById(userId)
                .map(UserModel::getDisplayCred).orElse(null);
        if (displayCred == null) {
            return kbRes.ok();
        } else {
            return getCredByUser(userId, displayCred, fill);
        }
    }

    @ApiOperation("根据用户ID和认证标识获取用户拥有的认证信息, 未获取该认证则返回空")
    @PostMapping(path = "/obj/getCredByUser")
    KbEntity<UserCredModel> getCredByUser(
            @RequestParam() Integer userId,
            @RequestParam() String cred,
            @RequestParam(required = false, defaultValue = "false") @ApiParam("是否填充头冠和徽章数据结构") Boolean fill
    ) {
        CredModel def = ornamentCacheService.getCred(cred)
                .map(c -> cred2model(c, fill))
                .orElseThrow(() -> new KbException(KbCode.RESOURCE_NOT_FOUND).r("认证未定义").li());
        return ornamentDataService.getCredByUserAndCred(userId, cred)
                .map(c -> Mapper.map(c, UserCredModel.class))
                .map(m -> {
                    m.setCredDef(def);
                    return m;
                })
                .map(kbRes::ok)
                .orElseGet(kbRes::ok);
    }

    @ApiOperation("根据用户ID获取用户拥有的所有认证信息")
    @PostMapping(path = "/obj/getCredListByUser")
    KbEntity<List<UserCredModel>> getCredListByUser(
            @RequestParam() Integer userId,
            @RequestParam(required = false, defaultValue = "1") @KbPage Integer page,
            @RequestParam(required = false, defaultValue = "15") @KbPage Integer pageSize,
            @RequestParam(required = false, defaultValue = "false") @ApiParam("是否填充头冠和徽章数据结构") Boolean fill
    ) {
        Page<UserCred> ucPage = ornamentDataService.getCredPageByUser(userId, page, pageSize);
        if (ucPage.isEmpty()) {
            return kbRes.ok();
        }
        Set<String> credSet = ucPage.map(UserCred::getCred).toSet();
        Map<String, CredDef> defMap = ornamentCacheService.getCredMap(credSet);
        Map<String, CredModel> credMap = new HashMap<>(credSet.size());
        Set<String> crestSet = fill ? new HashSet<>(credSet.size()) : Collections.emptySet();
        Set<String> pendantSet = fill ? new HashSet<>(credSet.size()) : Collections.emptySet();
        defMap.forEach((k, v) -> {
            if (v != null && v.getCred() != null) {
                CredModel cred = cred2model(v, false);
                if (cred != null && cred.getCred() != null) {
                    credMap.put(k, cred);
                    if (fill && v.getCrest() != null) {
                        crestSet.add(v.getCrest());
                    }
                    if (fill && v.getPendant() != null) {
                        pendantSet.add(v.getPendant());
                    }
                }
            }
        });

        if (fill) {
            Map<String, CrestDef> crestMap = ornamentCacheService.getCrestMap(crestSet);
            Map<String, PendantDef> pendantMap = ornamentCacheService.getPendantMap(pendantSet);
            credMap.forEach((k, v) -> {
                v.setCrestDef(crest2model(crestMap.get(v.getCrest())));
                v.setPendantDef(pendant2model(pendantMap.get(v.getPendant())));
            });
        }

        return kbRes.ok(ucPage.map(u -> {
            UserCredModel model = Mapper.map(u, UserCredModel.class);
            model.setCredDef(credMap.get(u.getCred()));
            return model;
        }));
    }

    // ------------------------------------------------------------------

    private static CrestModel crest2model(CrestDef def) {
        if (def == null || def.getCrest() == null || UserOrnamentState.HIDE.name().equals(def.getState())) {
            return null;
        }
        CrestModel model = new CrestModel();
        model.setCrest(def.getCrest());
        model.setImage(def.getImage());
        model.setName(def.getName());
        model.setDesc(def.getDesc());
        return model;
    }

    private static PendantModel pendant2model(PendantDef def) {
        if (def == null || def.getPendant() == null || UserOrnamentState.HIDE.name().equals(def.getState())) {
            return null;
        }
        PendantModel model = new PendantModel();
        model.setPendant(def.getPendant());
        model.setImage(def.getImage());
        model.setWidth(def.getWidth());
        model.setHeight(def.getHeight());
        model.setName(def.getName());
        model.setDesc(def.getDesc());
        return model;
    }

    private CredModel cred2model(CredDef def, boolean fill) {
        if (def == null || def.getCred() == null || UserOrnamentState.HIDE.name().equals(def.getState())) {
            return null;
        }
        CredModel model = new CredModel();
        model.setCred(def.getCred());
        model.setBadge(def.getBadge());
        model.setNameColor(def.getNameColor());
        model.setPendant(def.getPendant());
        model.setCrest(def.getCrest());
        model.setName(def.getName());
        model.setDesc(def.getDesc());
        if (fill && model.getCrest() != null) {
            ornamentCacheService.getCrest(def.getCrest()).ifPresent(
                    crestDef -> model.setCrestDef(crest2model(crestDef)));
        }
        if (fill && model.getPendant() != null) {
            ornamentCacheService.getPendant(def.getPendant()).ifPresent(
                    pendantDef -> model.setPendantDef(pendant2model(pendantDef)));
        }
        return model;
    }

}
