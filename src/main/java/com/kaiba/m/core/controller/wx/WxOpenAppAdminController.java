package com.kaiba.m.core.controller.wx;

import com.kaiba.lib.base.middleware.KbRes;
import com.kaiba.lib.base.response.KbCode;
import com.kaiba.lib.base.response.KbEntity;
import com.kaiba.lib.base.response.KbException;
import com.kaiba.lib.base.util.GsonUtils;
import com.kaiba.m.core.service.wx.WxOpenCacheService;
import com.kaiba.m.core.service.wx.WxOpenService;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.mp.bean.result.WxMpCurrentAutoReplyInfo;
import me.chanjar.weixin.open.bean.result.WxAmpLinkResult;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @version WxOpenAppAdminController, v0.1 2024/5/8 15:11 daopei Exp $
 **/
@Slf4j
@RestController
@RequestMapping("/wx/open/app")
public class WxOpenAppAdminController {

    private final KbRes kbRes;
    private final WxOpenCacheService cacheService;
    private final WxOpenService wxOpenService;

    public WxOpenAppAdminController(
            KbRes kbRes,
            WxOpenCacheService cacheService,
            WxOpenService wxOpenService
    ) {
        this.kbRes = kbRes;
        this.cacheService = cacheService;
        this.wxOpenService = wxOpenService;
    }


    @ApiParam("微信公众号内关键字回复配置查询")
    @PostMapping("/mp/getKeywordReply")
    public KbEntity<List<WxMpCurrentAutoReplyInfo.AutoReplyRule>> getMpOriginKeywordReply(
            @RequestParam Integer siteId,
            @RequestParam(required = false, defaultValue = "1") Integer page,
            @RequestParam(required = false, defaultValue = "20") Integer pageSize
    ) {
        /**
         * 微信公众号关键字回复缓存分页查询
         * 如果缓存过期则触发加载,重载时数据为空会写入空数据保证缓存key有效,避免缓存穿透
         */
        if (!cacheService.isMpOriginKeywordRelyCacheExist(siteId)) {
            cacheService.reloadMpOriginKeywordReplyCache(siteId);
        }
        return kbRes.ok(cacheService.getMpOriginKeywordReplyCache(siteId, page, pageSize));
    }

    @ApiParam("微信公众号内关键字回复配置重载主动触发")
    @PostMapping("/mp/reloadKeywordReply")
    public KbEntity<Void> reloadMpOriginKeywordReply(
            @RequestParam Integer siteId
    ) {
        /**
         * 重载微信关键字回复缓存
         * 查询微信api后写入redis,注意调用频次
         */
        List<WxMpCurrentAutoReplyInfo.AutoReplyRule> rules = cacheService.reloadMpOriginKeywordReplyCache(siteId);
        if (rules.isEmpty()) {
            return kbRes.err(KbCode.RESOURCE_NOT_FOUND, null, "微信公众号未找到有效配置");
        }
        return kbRes.ok();
    }

    /**
     * <pre>
     * 关联小程序
     * 关联流程（需要公众号和小程序管理员双方确认）：
     * 1、第三方平台调用接口发起关联
     * 2、公众号管理员收到模板消息，同意关联小程序。
     * 3、小程序管理员收到模板消息，同意关联公众号。
     * 4、关联成功
     * 等待管理员同意的中间状态可使用“获取公众号关联的小程序”接口进行查询。
     * 请求方式：POST(HTTPS)
     * 请求地址：
     * <a href="https://api.weixin.qq.com/cgi-bin/wxopen/wxamplink?access_token=TOKEN">https://api.weixin.qq.com/cgi-bin/wxopen/wxamplink?access_token=TOKEN</a>
     * 文档地址：
     * <a href="https://developers.weixin.qq.com/doc/oplatform/Third-party_Platforms/2.0/api/Official__Accounts/Mini_Program_Management_Permission.html">https://developers.weixin.qq.com/doc/oplatform/Third-party_Platforms/2.0/api/Official__Accounts/Mini_Program_Management_Permission.html</a>
     * <pre>
     * @param siteId 电台ID
     * @param relatedMaAppId 关联的小程序APPID,目前仅能绑定《开吧报名征集》小程序有效
     * @return
     *
     */
    @ApiParam("微信公众号内关联小程序")
    @PostMapping("/mp/wxamplink")
    public KbEntity<Void> wxampLink(
            @RequestParam Integer siteId,
            @RequestParam @ApiParam("关联的小程序APPID") String relatedMaAppId
    ) {
        String appId = cacheService.getWxAppConfig(siteId).getAppId();

        try {
            wxOpenService.getWxOpenComponentService().getWxMpServiceByAppid(appId)
                    .wxAmpLink(relatedMaAppId, "0", "0");
        } catch (WxErrorException e) {
            log.error("微信公众号绑定小程序错误, e:{}", GsonUtils.getGson().toJson(e));
            throw new KbException(KbCode.REQUEST_FAIL).r("微信公众号绑定小程序错误").li();
        }
        return kbRes.ok();
    }

    @ApiParam("微信公众号解绑小程序")
    @PostMapping("/mp/wxampunlink")
    public KbEntity<Void> wxampUnLink(
            @RequestParam Integer siteId,
            @RequestParam @ApiParam("关联的小程序APPID") String relatedMaAppId
    ) {
        String appId = cacheService.getWxAppConfig(siteId).getAppId();

        try {
            wxOpenService.getWxOpenComponentService().getWxMpServiceByAppid(appId)
                    .wxAmpUnLink(relatedMaAppId);
        } catch (WxErrorException e) {
            log.error("微信公众号解绑小程序错误, e:{}", GsonUtils.getGson().toJson(e));
            throw new KbException(KbCode.REQUEST_FAIL);
        }
        return kbRes.ok();
    }

    @ApiParam("微信公众号关联小程序查询")
    @PostMapping("/mp/wxamplinkget")
    public KbEntity<WxAmpLinkResult> wxampLinkGet(
            @RequestParam Integer siteId
    ) {
        String appId = cacheService.getWxAppConfig(siteId).getAppId();

        try {
            WxAmpLinkResult result =
                    wxOpenService.getWxOpenComponentService().getWxMpServiceByAppid(appId)
                            .getWxAmpLink();
            return kbRes.ok(result);
        } catch (WxErrorException e) {
            log.error("微信公众号绑定小程序查询错误, e:{}", GsonUtils.getGson().toJson(e));
            throw new KbException(KbCode.REQUEST_FAIL);
        }
    }
}
