package com.kaiba.m.core.controller.shipment;

import com.kaiba.lib.base.assembler.Mapper;
import com.kaiba.lib.base.domain.shipment.ShipmentModel;
import com.kaiba.lib.base.middleware.KbRes;
import com.kaiba.lib.base.response.KbCode;
import com.kaiba.lib.base.response.KbEntity;
import com.kaiba.lib.base.service.IShipmentService;
import com.kaiba.m.core.domain.shipment.Shipment;
import com.kaiba.m.core.service.shipment.ShipmentService;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version ShipmentServiceController, v0.1 2025/3/6 16:07 daopei Exp $
 **/
@RestController
public class ShipmentServiceController implements IShipmentService {

    private final KbRes kbRes;
    private final ShipmentService shipmentService;

    public ShipmentServiceController(
            KbRes kbRes,
            ShipmentService shipmentService
    ) {
        this.kbRes = kbRes;
        this.shipmentService = shipmentService;
    }

    @Override
    public KbEntity<ShipmentModel> createShipment(Integer userId, ShipmentModel shipmentModel) {
        Shipment shipment = shipmentService.createShipment(shipmentModel);
        return kbRes.ok(Mapper.map(shipment, ShipmentModel.class));
    }

    @Override
    public KbEntity<ShipmentModel> updateShipment(Integer userId, ShipmentModel shipmentModel) {
        Shipment shipment = shipmentService.updateShipment(shipmentModel);
        return kbRes.ok(Mapper.map(shipment, ShipmentModel.class));
    }

    @Override
    public KbEntity<ShipmentModel> getShipmentById(String shipmentId) {
        return shipmentService.getShipmentById(shipmentId)
                .map(shipment ->Mapper.map(shipment, ShipmentModel.class))
                .map(kbRes::ok)
                .orElse(kbRes.err(KbCode.RESOURCE_NOT_FOUND));
    }


    @Override
    public KbEntity<List<ShipmentModel>> getShipmentByIds(List<String> shipmentIds) {
        return kbRes.ok(shipmentService.getShipmentByIds(shipmentIds)
                .stream()
                .map(shipment -> Mapper.map(shipment, ShipmentModel.class))
                .collect(Collectors.toList()));
    }
}
