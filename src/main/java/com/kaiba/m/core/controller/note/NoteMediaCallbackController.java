package com.kaiba.m.core.controller.note;

import com.kaiba.lib.base.domain.fuse.media.MediaAssetCallbackBizNotifyModel;
import com.kaiba.lib.base.middleware.KbRes;
import com.kaiba.lib.base.response.KbCode;
import com.kaiba.lib.base.response.KbEntity;
import com.kaiba.m.core.service.note.note.NoteMediaService;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 帖子回调控制器， 不在网关层暴露，仅在负载均衡内部调用
 * <AUTHOR>
 * @version NoteCallbackController, v0.1 2025/6/30 10:45 daopei Exp $
 **/
@RestController
@RequestMapping("/note/media/callback")
public class NoteMediaCallbackController {

    private final KbRes kbRes;
    private final NoteMediaService noteMediaService;

    public NoteMediaCallbackController(
            KbRes kbRes,
            NoteMediaService noteMediaService
    ) {
        this.kbRes = kbRes;
        this.noteMediaService = noteMediaService;
    }

    @ApiOperation("媒资处理完成回调通知")
    @PostMapping("/finish")
    public KbEntity<Void> finish(
            @RequestBody() MediaAssetCallbackBizNotifyModel notify
    ) {
        if (notify == null ||  notify.getMediaId() == null) {
            return kbRes.err(KbCode.REQUEST_PARAM_MISSING);
        }
        noteMediaService.mediaFinish(notify.getMediaId());
        return kbRes.ok();
    }
}
