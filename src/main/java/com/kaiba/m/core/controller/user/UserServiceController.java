package com.kaiba.m.core.controller.user;

import com.kaiba.lib.base.annotation.apiparam.KbUserId;
import com.kaiba.lib.base.assembler.Mapper;
import com.kaiba.lib.base.constant.user.UserRole;
import com.kaiba.lib.base.domain.data.datav.DatavTimeBucketStatModel;
import com.kaiba.lib.base.domain.user.UserInfoModel;
import com.kaiba.lib.base.domain.user.UserModel;
import com.kaiba.lib.base.domain.user.UserSensitive;
import com.kaiba.lib.base.domain.user.UserUpdateInfoModel;
import com.kaiba.lib.base.middleware.KbRes;
import com.kaiba.lib.base.response.KbCode;
import com.kaiba.lib.base.response.KbEntity;
import com.kaiba.lib.base.response.KbException;
import com.kaiba.lib.base.service.IUserService;
import com.kaiba.m.core.domain.user.User;
import com.kaiba.m.core.domain.user.UserAccWX;
import com.kaiba.m.core.service.user.UserBasicCacheService;
import com.kaiba.m.core.service.user.UserService;
import com.kaiba.m.core.service.user.blacklist.UserBlacklistService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import java.util.*;
import java.util.stream.Collectors;

/**
 * author: lyux
 * date: 18-10-3
 */
@Slf4j
@RestController
public class UserServiceController implements IUserService {

    private final KbRes kbRes;
    private final UserService userService;
    private final UserBasicCacheService userCacheService;
    private final UserBlacklistService userBlacklistService;

    @Autowired
    public UserServiceController(
            KbRes kbRes,
            UserService userService,
            UserBasicCacheService userCacheService,
            UserBlacklistService userBlacklistService
    ) {
        this.kbRes = kbRes;
        this.userService = userService;
        this.userCacheService = userCacheService;
        this.userBlacklistService = userBlacklistService;
    }

    @Override
    public KbEntity<UserModel> getBasic(Integer userId) {
        return userCacheService.getUserById(userId)
                .map(kbRes::ok)
                .orElse(kbRes.err(KbCode.USER_NOT_EXISTS, "user not exists: " + userId));
    }

    @Override
    public KbEntity<UserModel> invalidUserBasicCache(Integer userId) {
        userCacheService.invalidateUser(userId);
        return getBasic(userId);
    }

    @Override
    public KbEntity<UserModel> getBasicByMobile(String mobile) {
        return userService
                .getBasicByMobile(mobile)
                .map(UserServiceController::user2model)
                .map(kbRes::ok)
                .orElse(kbRes.err(KbCode.USER_NOT_EXISTS, "user not exists for mobile: " + mobile));
    }

    @Override
    public KbEntity<UserModel> getBasicByName(String name) {
        return userService
                .getBasicByName(name)
                .map(UserServiceController::user2model)
                .map(kbRes::ok)
                .orElse(kbRes.err(KbCode.USER_NOT_EXISTS, "user not exists for name: " + name));
    }

    @Override
    public KbEntity<UserModel> getBasicByAccount(String account) {
        return userService
                .getByLoginNameOrMobile(account)
                .map(UserServiceController::user2model)
                .map(kbRes::ok)
                .orElse(kbRes.err(KbCode.USER_NOT_EXISTS, "user not exists: " + account));
    }

    @Override
    public KbEntity<Boolean> checkUserExist(String mobile, String userName, String loginName) {
        Optional<User> optional = userService.getBasicByMobile(mobile);
        boolean exist = userService.exitByUserNameOrLoginName(userName, loginName);
        return kbRes.ok(optional.isPresent() || exist);
    }

    @Override
    public KbEntity<List<UserModel>> getBasicListIn(Integer[] userIdList) {
        if (userIdList == null || userIdList.length == 0) {
            return kbRes.ok(Collections.emptyList());
        } else {
            return kbRes.ok(userCacheService.getUserListByIdIn(Arrays.asList(userIdList)));
        }
    }

    @Override
    public KbEntity<List<UserModel>> getBasicListByUpdateTimeDesc(Integer page, Integer pageSize) {
        return kbRes.ok(userService.getBasicListOrderByUpdateTimeDesc(page, pageSize).stream()
                .map(UserServiceController::user2model)
                .collect(Collectors.toList()));
    }

    @Override
    public KbEntity<UserSensitive> getSensitiveById(Integer userId) {
        return userService.getSensitive(userId)
                .map(UserServiceController::user2sensitive)
                .map(kbRes::ok)
                .orElse(kbRes.err(KbCode.USER_NOT_EXISTS, "user not exists: " + userId));
    }

    @Override
    public KbEntity<List<UserSensitive>> getSensitiveListIn(Integer[] userIds) {
        return kbRes.ok(userService.getSensitiveListIn(userIds).stream()
                .map(UserServiceController::user2sensitive)
                .collect(Collectors.toList()));
    }

    @Override
    public KbEntity<List<UserModel>> getDetailListIn(Integer[] userIdList) {
        return kbRes.ok(userService.getDetailListIn(userIdList).stream()
                .map(UserServiceController::user2model)
                .collect(Collectors.toList()));
    }

    @Override
    public KbEntity<UserModel> getDetail(Integer userId) {
        return userService.getDetail(userId)
                .map(user -> {
                    UserModel model = user2model(user);
                    userCacheService.attachUserOrnaments(model);
                    return model;
                })
                .map(kbRes::ok)
                .orElse(kbRes.err(KbCode.USER_NOT_EXISTS, "user not exists: " + userId));
    }

    @Override
    public KbEntity<UserModel> getUserAuthInfoById(Integer userId) {
        return userService.getDetail(userId)
                .map(user -> {
                    UserModel model = new UserModel();
                    model.setUserId(user.getId());
                    model.setMobile(user.getMobile());
                    model.setPassword(user.getPassword());
                    return model;
                })
                .map(kbRes::ok)
                .orElse(kbRes.err(KbCode.USER_NOT_EXISTS, "user not exists: " + userId));
    }

    @Override
    public KbEntity<String> getMobileById(Integer userId) {
        // 由于早期 mobile 字段入库未做严格检测, 导致用户手机号有座机等情况, 因此判断逻辑暂时定位长度大于7的字符串
        return userService.getMobile(userId)
                .map(String::trim)
                .filter(mobile -> mobile.length() > 7)
                .map(kbRes::ok)
                .orElse(kbRes.ok());
    }

    @Override
    public KbEntity<String> getWXOpenIdById(Integer userId, String entity) {
        return userService.getWXAcc(userId, entity)
                .map(UserAccWX::getOpenId)
                .map(kbRes::ok)
                .orElse(kbRes.err(KbCode.RESOURCE_NOT_FOUND));
    }

    @Override
    public KbEntity<List<String>> getInterestCarListById(Integer userId) {
        List<String> list = new LinkedList<>();
        userCacheService.getUserById(userId).ifPresent(user -> {
            if (user.getSeries() != null) {
                list.add(user.getSeries());
            }
        });
        userService.getUserInfoById(userId).ifPresent(info -> {
            if (info.getInterestedCar() != null) {
                list.addAll(info.getInterestedCar());
            }
        });
        return kbRes.ok(list);
    }

    @Override
    public KbEntity<String> getUserPasswordById(Integer userId) {
        return userService.getDetail(userId)
                .map(User::getPassword)
                .map(kbRes::ok)
                .orElse(kbRes.err(KbCode.USER_NOT_EXISTS, "user not exists: " + userId));
    }

    @Override
    public KbEntity<Long> getUserTotal(Integer siteId) {
        return kbRes.ok(userService.getUserTotal(siteId));
    }

    @Override
    public KbEntity<Void> refreshUserTotal(String secret, Integer siteId) {
        if (!"werydga34g23wt13sdfb8o5k".equals(secret)) {
            return kbRes.ok();
        }
        userService.refreshTotal(siteId);
        return kbRes.ok();
    }

    @Override
    public KbEntity<Long> getCountByLoginTime(Long since) {
        return kbRes.ok(userService.countByLoginTime(since));
    }

    @Override
    public KbEntity<UserInfoModel> getUserInfoById(Integer userId) {
        return userService.getUserInfoById(userId)
                .map(userInfo -> Mapper.map(userInfo, UserInfoModel.class))
                .map(kbRes::ok)
                .orElse(kbRes.err(KbCode.USER_NOT_EXISTS, "user info not exists: " + userId));
    }

    @Override
    public KbEntity<UserModel> createUser(
            String loginName, String userName, String avatar, String mobile, Integer sex,
            Integer source, Integer siteId, String passwordMd5) {
        User user = new User();
        user.setLoginName(loginName);
        user.setUserName(userName);
        user.setAvatar(avatar);
        user.setMobile(mobile);
        user.setSex(sex);
        user.setSource(source);
        user.setSiteId(siteId);
        user.setPassword(passwordMd5);
        user = userService.createUser(user);
        return kbRes.ok(user2model(user));
    }

    @Override
    public KbEntity<UserModel> unsetCarInfo(Integer userId, String carNo, String series) {
        if(carNo == null && series == null) {
            return kbRes.err(KbCode.REQUEST_PARAM_INVALID, "carNo and series null", "必须有一个非空传值");
        }
        return kbRes.ok(user2model(userService.unsetCarInfo(userId, carNo, series)));
    }

    @Override
    public KbEntity<UserModel> unsetAuthContent(Integer userId) {
        return kbRes.ok(user2model(userService.unsetAuthContent(userId)));
    }

    @Override
    public KbEntity<UserModel> updateUserAuthContent(Integer userId, String authContent) {
        return kbRes.ok(user2model(userService.updateUserAuthContent(userId, authContent)));
    }

    @Override
    public KbEntity<UserModel> updateUserInfo(UserUpdateInfoModel updateModel) {
        if (updateModel.getUserId() == null) {
            return kbRes.err(KbCode.REQUEST_PARAM_INVALID, "userId null", "没有指定用户id");
        }
        if (updateModel.isEmpty()) {
            return kbRes.err(KbCode.REQUEST_PARAM_INVALID, "all update fields null", "没有填写要更新的字段");
        }
        User user = Mapper.map(updateModel, User.class);
        user.setId(updateModel.getUserId());
        return kbRes.ok(user2model(userService.updateUser(user)));
    }

    @Override
    public KbEntity<Void> updateUserRole(Integer userId, Integer role) {
        UserRole userRole = UserRole.valueOf(role).orElseThrow(() -> new KbException(KbCode.REQUEST_PARAM_INVALID));
        userService.updateUserRole(userId, userRole);
        userCacheService.invalidateUser(userId);
        return kbRes.ok();
    }

    @Override
    public KbEntity<Void> unbindMobile(Integer userId){
        userService.updateMobile(userId,"");
        return kbRes.ok();
    }

    @Override
    public KbEntity<Void> addInterestedCar(Integer userId, String carCode) {
        userService.addInterestedCar(userId, carCode);
        return kbRes.ok();
    }

    @Override
    public KbEntity<Void> removeInterestedCar(Integer userId, String carCode) {
        userService.removeInterestedCar(userId, carCode);
        return kbRes.ok();
    }

    @Override
    public KbEntity<Boolean> isInUserBlacklist(
            @KbUserId Integer userId, @KbUserId Integer targetUserId, Boolean allowCache) {
        return kbRes.ok(userBlacklistService.isInBlacklist(userId, targetUserId, allowCache));
    }

    @Override
    public KbEntity<Set<Integer>> getUserBlacklist(@KbUserId Integer userId, Boolean allowCache) {
        return kbRes.ok(userBlacklistService.getBlacklist(userId, allowCache));
    }

    @Override
    public KbEntity<List<UserModel>> getUserBlacklistAsUser(@KbUserId Integer userId, Boolean allowCache) {
        Set<Integer> userIdList = userBlacklistService.getBlacklist(userId, allowCache);
        Map<Integer, UserModel> userMap = userService.getBasicListIn(userIdList).stream()
                .collect(Collectors.toMap(User::getId, UserServiceController::user2model, (k1, k2) -> k1));
        return kbRes.ok(userIdList.stream()
                .map(userMap::get)
                .filter(Objects::nonNull)
                .collect(Collectors.toList()));
    }

    @Override
    public KbEntity<Void> addToUserBlacklist(@KbUserId Integer userId, @KbUserId Integer targetUserId) {
        userBlacklistService.addToBlacklist(userId, targetUserId);
        return kbRes.ok();
    }

    @Override
    public KbEntity<Void> removeFromUserBlacklist(@KbUserId Integer userId, @KbUserId Integer targetUserId) {
        userBlacklistService.removeFromBlacklist(userId, targetUserId);
        return kbRes.ok();
    }

    @Override
    public KbEntity<List<UserModel>> getListByUpdateTimeBetween(Long st, Long et, Integer page, Integer pageSize) {
        return kbRes.ok(userService.getListByUpdateTimeBetween(st, et, page, pageSize).stream()
            .map(UserServiceController::user2model).collect(Collectors.toList()));
    }

    @Override
    public KbEntity<List<DatavTimeBucketStatModel>> statUserByDay(Integer siteId, Long startTime, Long endTime) {
        return kbRes.ok(userService.statUserByDay(siteId, startTime, endTime));
    }

    @Override
    public KbEntity<List<DatavTimeBucketStatModel>> statUserByTimeInterval(Integer siteId, Long st, Long et, Long interval) {
        return kbRes.ok(userService.statUserByTimeInterval(siteId, st, et, interval));
    }

    @Override
    public KbEntity<Long> countAllUser(String secret) {
        if(!"f1pio24th12p4t182ho4o7y2".equals(secret)) {
            return kbRes.ok(0L);
        }
        return kbRes.ok(userService.countAllUser());
    }

    // -----------------------------------------------

    private static UserModel user2model(User user) {
        if (user == null) {
            return null;
        }
        UserModel model = Mapper.map(user, UserModel.class);
        model.setUserId(user.getId());
        model.setPassword(null);
        if (user.getScore() != null) {
            model.setLevel(score2level(user.getScore()));
        }
        return model;
    }

    private static UserSensitive user2sensitive(User user) {
        if (user == null) {
            return null;
        }
        UserSensitive model = Mapper.map(user, UserSensitive.class);
        model.setUserId(user.getId());
        return model;
    }

    // 将用户积分值转换为用户等级, 以太阳星星月亮等表示, 类似QQ等级
    private static String score2level(int score) {
        if (score <= 0) {
            return "0000";
        }

        // 计算用户积分应该归为哪一个十进制等级
        int tmpScore = 1;
        int level = 0;
        for (int i = 0; i < 1000; i++) {
            int tmpScoreEnd = tmpScore + i * 10;
            if (score < tmpScoreEnd) {
                level = i;
                break;
            } else {
                tmpScore = tmpScoreEnd;
            }
        }

        // 将十进制等级转为五进制
        Stack<String> stack = new Stack<>();
        StringBuilder sb = new StringBuilder(0);
        while (level != 0) {
            stack.add(Integer.toString(level % 5));
            level = level / 5;
        }
        while (!stack.isEmpty()) {
            sb.append(stack.pop());
        }

        // 将结果补全为 4 位字符
        String result = sb.toString();
        if (result.length() == 0) {
            return "0000";
        } else if (result.length() == 1) {
            return "000" + result;
        } else if (result.length() == 2) {
            return "00" + result;
        } else if (result.length() == 3) {
            return "0" + result;
        } else {
            return result;
        }
    }

}
