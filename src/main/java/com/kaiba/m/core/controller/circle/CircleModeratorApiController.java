package com.kaiba.m.core.controller.circle;

import com.kaiba.lib.base.annotation.apiparam.KbPage;
import com.kaiba.lib.base.annotation.apiparam.KbPageSize;
import com.kaiba.lib.base.annotation.apiparam.KbUserId;
import com.kaiba.lib.base.constant.KbHeader;
import com.kaiba.lib.base.constant.auth.AuthResultState;
import com.kaiba.lib.base.domain.circle.CircleThreadModel;
import com.kaiba.lib.base.domain.circle.CircleTopicModel;
import com.kaiba.lib.base.domain.note.*;
import com.kaiba.lib.base.domain.user.UserModel;
import com.kaiba.lib.base.middleware.KbRes;
import com.kaiba.lib.base.response.KbCode;
import com.kaiba.lib.base.response.KbEntity;
import com.kaiba.lib.base.response.KbException;
import com.kaiba.lib.base.service.IAuthService;
import com.kaiba.lib.base.service.ICircleService;
import com.kaiba.lib.base.service.INoteService;
import com.kaiba.lib.base.service.IUserService;
import com.kaiba.lib.base.util.TimeUtils;
import com.kaiba.m.core.service.circle.CircleApiService;
import com.kaiba.m.core.service.circle.CircleModeratorApiService;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * author: lyux
 * date: 2024-09-26
 *
 * 圈子版主管理接口
 */
@Slf4j
@RestController
@RequestMapping("/n/circle/moderator")
@Validated
public class CircleModeratorApiController {

    private final KbRes kbRes;
    private final CircleModeratorApiService moderatorApiService;
    private final CircleApiService circleApiService;
    private final ICircleService circleService;
    private final INoteService noteService;
    private final IUserService userService;
    private final IAuthService authService;

    public CircleModeratorApiController(
            @Qualifier("kbApiRes") KbRes kbRes,
            CircleModeratorApiService moderatorApiService,
            CircleApiService circleApiService,
            ICircleService circleService,
            INoteService noteService,
            IUserService userService,
            IAuthService authService) {
        this.kbRes = kbRes;
        this.moderatorApiService = moderatorApiService;
        this.circleApiService = circleApiService;
        this.circleService = circleService;
        this.noteService = noteService;
        this.userService = userService;
        this.authService = authService;
    }

    // ------------------------------------------------
    // 圈子管理相关

    @ApiOperation("圈子: 当前用户是否版主")
    @PostMapping("/usr/circle/isModerator")
    public KbEntity<Boolean> isModerator(
            @RequestHeader(KbHeader.KB_USER_ID) Integer userId
    ) {
        return kbRes.ok(moderatorApiService.isModerator(userId));
    }

    @ApiOperation("圈子: 当前用户是否指定圈子的版主")
    @PostMapping("/usr/circle/isCircleModerator")
    public KbEntity<Boolean> isCircleModerator(
            @RequestHeader(KbHeader.KB_USER_ID) Integer userId,
            @RequestParam() String circleId
    ) {
        return kbRes.ok(moderatorApiService.isCircleModerator(circleId, userId));
    }

    @ApiOperation("圈子: 获取圈子详情, 以话题数据结构返回, CircleTopicModel")
    @PostMapping("/usr/circle/getCircleByCircleId")
    public KbEntity<CircleTopicModel> getCircleByCircleId(
            @RequestHeader(KbHeader.KB_USER_ID) Integer userId,
            @RequestParam() String circleId
    ) {
        if (moderatorApiService.isCircleModerator(circleId, userId)) {
            return circleApiService.getCircleById(circleId)
                    .map(kbRes::ok)
                    .orElseGet(() -> kbRes.err(KbCode.RESOURCE_NOT_FOUND));
        } else {
            return kbRes.msg(KbCode.AUTH_NO_PERMISSION, "没有权限管理这个圈子");
        }
    }

    @ApiOperation("圈子: 获取圈子详情, 以话题板块数据结构返回, CircleThreadModel")
    @PostMapping("/usr/circle/getCircleThreadByThreadId")
    public KbEntity<CircleThreadModel> getCircleThreadByThreadId(
            @RequestHeader(KbHeader.KB_USER_ID) Integer userId,
            @RequestParam() String threadId
    ) {
        moderatorByThreadOrThrow(threadId, userId);
        return kbRes.asIs(circleService.getCircleByThreadId(threadId));
    }

    @ApiOperation("圈子: 获取当前用户作为版主的圈子列表")
    @PostMapping("/usr/circle/getCircleListByModerator")
    public KbEntity<List<CircleTopicModel>> getCircleListByModerator(
            @RequestHeader(KbHeader.KB_USER_ID) Integer userId
    ) {
        List<String> circleIdList = moderatorApiService.getCircleIdListByModerator(userId);
        return kbRes.ok(circleApiService.getTopicListByIds(circleIdList));
    }

    @ApiOperation("圈子: 获取圈子的版主列表")
    @PostMapping("/obj/circle/getModeratorListByCircle")
    public KbEntity<List<UserModel>> getModeratorListByCircle(
            @RequestParam() String circleId
    ) {
        List<Integer> userIdList = moderatorApiService.getModeratorListByCircleId(circleId);
        if (userIdList == null || userIdList.isEmpty()) {
            return kbRes.ok(Collections.emptyList());
        } else {
            Map<Integer, UserModel> userMap = userService.getBasicMapByIds(new HashSet<>(userIdList)).dataOrThrow();
            return kbRes.ok(userIdList.stream()
                    .map(userMap::get)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList()));
        }
    }

    @ApiOperation("圈子: 发布圈子公告")
    @PostMapping("/usr/circle/createCircleContent")
    public KbEntity<NoteModel> createCircleContent(
            @RequestHeader(KbHeader.KB_USER_ID) Integer userId,
            @RequestBody() NoteCreateModel model
    ) {
        String circleId = model.getReferenceId();
        if (moderatorApiService.isCircleModerator(circleId, userId)) {
            return kbRes.asIs(circleService.createCircleContent(userId, model));
        } else {
            return kbRes.msg(KbCode.AUTH_NO_PERMISSION, "没有发布公告的权限");
        }
    }

    @ApiOperation("圈子: 更新圈子信息")
    @PostMapping("/usr/circle/updateCircle")
    public KbEntity<Void> updateCircle(
            @RequestHeader(KbHeader.KB_USER_ID) Integer userId,
            @RequestParam() String circleId,
            @RequestParam(required = false) String title,
            @RequestParam(required = false) String topicAvatar,
            @RequestParam(required = false) String hotTopicImage,
            @RequestParam(required = false) String hotTopicColor
    ) {
        if (moderatorApiService.isCircleModerator(circleId, userId)) {
            return kbRes.asIs(circleService
                    .updateCircle(userId, circleId, title, topicAvatar, hotTopicImage, hotTopicColor)
                    .map(circle -> null));
        } else {
            return kbRes.msg(KbCode.AUTH_NO_PERMISSION, "没有更新圈子信息的权限");
        }
    }

    @ApiOperation("圈子: 更新板块的分享信息")
    @PostMapping("/usr/circle/updateCircleShareByThreadId")
    public KbEntity<Void> updateCircleShareByThreadId(
            @RequestHeader(KbHeader.KB_USER_ID) Integer userId,
            @RequestParam() String circleId,
            @RequestParam(required = false) String title,
            @RequestParam(required = false) String content,
            @RequestParam(required = false) String image,
            @RequestParam(required = false) String url
    ) {
        if (moderatorApiService.isCircleModerator(circleId, userId)) {
            return kbRes.asIs(circleService.updateCircleShare(userId, circleId, title, content, image, url)
                    .map(t -> null));
        } else {
            return kbRes.msg(KbCode.AUTH_NO_PERMISSION, "没有更新圈子分享信息的权限");
        }
    }

    @ApiOperation("圈子: 变更板块的审核级别")
    @PostMapping("/usr/circle/updateThreadCondition")
    public KbEntity<Void> updateThreadCondition(
            @RequestHeader(KbHeader.KB_USER_ID) Integer userId,
            @RequestParam() String threadId,
            @RequestParam() Integer condition
    ) {
        moderatorByThreadOrThrow(threadId, userId);
        return kbRes.asIs(noteService.updateThreadCondition(userId, threadId, condition).map(t -> null));
    }

    @ApiOperation("圈子: 变更板块的置顶贴数量上限")
    @PostMapping("/usr/circle/updateThreadTopMax")
    public KbEntity<Void> updateThreadTopMax(
            @RequestHeader(KbHeader.KB_USER_ID) Integer userId,
            @RequestParam() String threadId,
            @RequestParam() Integer topMax
    ) {
        moderatorByThreadOrThrow(threadId, userId);
        return kbRes.asIs(noteService.updateThreadTopMax(userId, threadId, topMax).map(t -> null));
    }

    @ApiOperation("圈子: 变更板块的热议配置")
    @PostMapping("/usr/circle/updateThreadHotMax")
    public KbEntity<Void> updateThreadHotMax(
            @RequestHeader(KbHeader.KB_USER_ID) Integer userId,
            @RequestParam() String threadId,
            @RequestParam() Integer hotMax,
            @RequestParam(required = false) String hotTitle,
            @RequestParam(required = false) Boolean hotPrefer
    ) {
        moderatorByThreadOrThrow(threadId, userId);
        return kbRes.asIs(noteService.updateThreadHotMax(userId, threadId, hotMax, hotTitle, hotPrefer).map(t -> null));
    }

    @ApiOperation("圈子: 发布帖子")
    @PostMapping("/usr/circle/addNote")
    public KbEntity<NoteModel> addNote(
            @RequestHeader(KbHeader.KB_USER_ID) Integer userId,
            @RequestBody() NoteCreateModel model
    ) {
        if (model.getOriginThreadId() == null) {
            return kbRes.msg(KbCode.REQUEST_PARAM_MISSING, "请选择要发布的圈子");
        }
        moderatorByThreadOrThrow(model.getOriginThreadId(), userId);
        model.setUserId(userId);
        model.setIsHonorCondition(false);
        model.setIsConsiderThreadListRoute(true);
        return kbRes.asIs(noteService.noteCreateByBody(model));
    }

    @ApiOperation("圈子: 发布帖子并设为热议")
    @PostMapping("/usr/circle/addNoteAndSetHot")
    public KbEntity<NoteModel> addNoteAndSetHot(
            @RequestHeader(KbHeader.KB_USER_ID) Integer userId,
            @RequestBody() NoteCreateModel model
    ) {
        return addNote(userId, model).peek(note -> {
            String title = TimeUtils.millis2string(System.currentTimeMillis(), TimeUtils.TIME_FORMATTER_DATE1);
            noteService.setNoteAsHot(userId, note.getId(), note.getOriginThreadId(), title, null);
        });
    }

    // -------------------------------------------------------------
    // 帖子管理相关

    @ApiOperation("帖子: 获取帖子")
    @PostMapping("/usr/note/getNoteById")
    public KbEntity<NoteModel> getNoteById(
            @RequestHeader(KbHeader.KB_USER_ID) Integer userId,
            @RequestParam() String noteId,
            @RequestParam(required = false) String threadId,
            @RequestParam(required = false, defaultValue = "1") Long grainFlag
    ) {
        moderatorByNoteAndThreadOrThrow(noteId, threadId, userId);
        return kbRes.asIs(noteService.getNoteById(noteId, null, threadId, grainFlag));
    }

    @ApiOperation("帖子: 用户排名列表")
    @PostMapping("/usr/note/getNoteCommentUserRankingByThreadId")
    public KbEntity<List<UserRankingModel>> getNoteCommentUserRankingByThreadId(
            @RequestHeader(KbHeader.KB_USER_ID) Integer userId,
            @RequestParam() String threadId,
            @RequestParam() Long startTime,
            @RequestParam() Long endTime,
            @RequestParam(required = false, defaultValue = "1") Integer page,
            @RequestParam(required = false, defaultValue = "15") Integer pageSize
    ) {
        moderatorByThreadOrThrow(threadId, userId);
        return kbRes.asIs(noteService.getNoteCommentUserRankingByThreadId(
                userId, threadId, startTime, endTime, page, pageSize));
    }

    @ApiOperation("帖子审核: 根据板块获取待审核帖子列表")
    @PostMapping("/usr/note/getNoteReviewListByThread")
    public KbEntity<List<NoteModel>> getNoteReviewListByThread(
            @RequestHeader(KbHeader.KB_USER_ID) Integer userId,
            @RequestParam() String threadId,
            @RequestParam(required = false, defaultValue = "1") @KbPage Integer page,
            @RequestParam(required = false, defaultValue = "20") @KbPageSize Integer pageSize
    ) {
        moderatorByThreadOrThrow(threadId, userId);
        return kbRes.asIs(noteService.getNoteReviewListByThread(userId, new String[] { threadId }, page, pageSize));
    }

    @ApiOperation("帖子审核: 帖子审核通过")
    @PostMapping("/usr/note/approveNoteReview")
    public KbEntity<NoteModel> approveNoteReview(
            @RequestHeader(KbHeader.KB_USER_ID) Integer userId,
            @RequestParam() String noteReviewId,
            @RequestParam() String threadId
    ) {
        NoteModel review = noteService.getNoteReviewById(noteReviewId).dataOrThrow();
        if (review.getThreadIds() == null || review.getThreadIds().isEmpty()) {
            return kbRes.msg(KbCode.AUTH_NO_PERMISSION, "没有该贴的审核权限(1)");
        } else if (!review.getThreadIds().contains(threadId)) {
            return kbRes.msg(KbCode.AUTH_NO_PERMISSION, "没有该贴的审核权限(2)");
        } else {
            moderatorByThreadOrThrow(threadId, userId);
            return kbRes.asIs(noteService.noteReviewApprove(userId, noteReviewId));
        }
    }

    @ApiOperation("帖子审核: 帖子审核不予通过")
    @PostMapping("/usr/note/refuseNoteReview")
    public KbEntity<Void> refuseNoteReview(
            @RequestHeader(KbHeader.KB_USER_ID) Integer userId,
            @RequestParam() String noteReviewId,
            @RequestParam() String threadId,
            @RequestParam(required = false) String reason
    ) {
        NoteModel review = noteService.getNoteReviewById(noteReviewId).dataOrThrow();
        if (review.getThreadIds() == null || review.getThreadIds().isEmpty()) {
            return kbRes.msg(KbCode.AUTH_NO_PERMISSION, "没有该贴的审核权限(1)");
        } else if (!review.getThreadIds().contains(threadId)) {
            return kbRes.msg(KbCode.AUTH_NO_PERMISSION, "没有该贴的审核权限(2)");
        } else {
            moderatorByThreadOrThrow(threadId, userId);
            return kbRes.asIs(noteService.noteReviewRefuse(userId, noteReviewId, reason));
        }
    }

    @ApiOperation("帖子: 帖子设置可赞/禁赞")
    @PostMapping("/usr/note/updateNoteAllowPraise")
    public KbEntity<Void> updateNoteAllowPraise(
            @RequestHeader(KbHeader.KB_USER_ID) Integer userId,
            @RequestParam() String noteId,
            @RequestParam() Boolean allowPraise
    ) {
        moderatorByNoteOrThrow(noteId, userId);
        return kbRes.asIs(noteService.updateNoteAllowPraise(noteId, allowPraise));
    }

    @ApiOperation("帖子: 帖子设置可评/禁评")
    @PostMapping("/usr/note/updateNoteAllowComment")
    public KbEntity<Void> updateNoteAllowComment(
            @RequestHeader(KbHeader.KB_USER_ID) Integer userId,
            @RequestParam() String noteId,
            @RequestParam() Boolean allowComment
    ) {
        moderatorByNoteOrThrow(noteId, userId);
        return kbRes.asIs(noteService.updateNoteAllowComment(noteId, allowComment));
    }

    @ApiOperation("帖子: 帖子更改状态. 可用于实现: 恢复为正常贴-1; 软删除-2; 仅作者可见-3")
    @PostMapping("/usr/note/updateNoteState")
    public KbEntity<Void> updateNoteState(
            @RequestHeader(KbHeader.KB_USER_ID) Integer userId,
            @RequestParam() String noteId,
            @RequestParam() Integer state
    ) {
        moderatorByNoteOrThrow(noteId, userId);
        return kbRes.asIs(noteService.noteUpdateState(noteId, state));
    }

    @ApiOperation("帖子: 擦亮帖子")
    @PostMapping("/usr/note/refreshNote")
    public KbEntity<Void> refreshNote(
            @RequestHeader(KbHeader.KB_USER_ID) Integer userId,
            @RequestParam() String noteId
    ) {
        moderatorByNoteOrThrow(noteId, userId);
        return kbRes.asIs(noteService.noteRefresh(noteId, userId));
    }

    @ApiOperation("帖子: 帖子置顶")
    @PostMapping("/usr/note/setNoteAsTop")
    public KbEntity<Void> setNoteAsTop(
            @RequestHeader(KbHeader.KB_USER_ID) Integer userId,
            @RequestParam() String noteId,
            @RequestParam() String threadId
    ) {
        moderatorByNoteAndThreadOrThrow(noteId, threadId, userId);
        return kbRes.asIs(noteService.setNoteAsTop(userId, noteId, threadId));
    }

    @ApiOperation("帖子: 帖子取消置顶")
    @PostMapping("/usr/note/removeNoteAsTop")
    public KbEntity<Void> removeNoteAsTop(
            @RequestHeader(KbHeader.KB_USER_ID) Integer userId,
            @RequestParam() String noteId,
            @RequestParam() String threadId
    ) {
        moderatorByNoteAndThreadOrThrow(noteId, threadId, userId);
        return kbRes.asIs(noteService.removeNoteAsTop(userId, noteId, threadId));
    }

    @ApiOperation("帖子: 帖子设为热议: 将该贴置为热议帖第一位. 已热议的帖子信息会更新.")
    @PostMapping("/usr/note/setNoteAsHot")
    public KbEntity<Void> setNoteAsHot(
            @RequestHeader(KbHeader.KB_USER_ID) Integer userId,
            @RequestParam() String noteId,
            @RequestParam() String threadId,
            @RequestParam(required = false) String title,
            @RequestParam(required = false) String cover
    ) {
        moderatorByNoteAndThreadOrThrow(noteId, threadId, userId);
        return kbRes.asIs(noteService.setNoteAsHot(userId, noteId, threadId, title, cover));
    }

    @ApiOperation("帖子: 帖子取消热议.")
    @PostMapping("/usr/note/removeNoteAsHot")
    public KbEntity<Void> removeNoteAsHot(
            @RequestHeader(KbHeader.KB_USER_ID) Integer userId,
            @RequestParam() String noteId,
            @RequestParam() String threadId
    ) {
        moderatorByNoteAndThreadOrThrow(noteId, threadId, userId);
        return kbRes.asIs(noteService.removeNoteAsHot(userId, noteId, threadId));
    }

    @ApiOperation("帖子: 擦亮热议 - 将该贴置为热议帖第一位.")
    @PostMapping("/usr/note/refreshNoteAsHot")
    public KbEntity<Void> refreshNoteAsHot(
            @RequestHeader(KbHeader.KB_USER_ID) Integer userId,
            @RequestParam() String noteId,
            @RequestParam() String threadId
    ) {
        moderatorByNoteAndThreadOrThrow(noteId, threadId, userId);
        return kbRes.asIs(noteService.refreshNoteAsHot(userId, noteId, threadId));
    }


    @ApiOperation("帖子: 评论更改状态. 可用于实现: 恢复为正常-1; 软删除-2; 仅作者可见-3")
    @PostMapping("/usr/note/updateCommentState")
    public KbEntity<Void> updateCommentState(
            @RequestHeader(KbHeader.KB_USER_ID) Integer userId,
            @RequestParam() String commentId,
            @RequestParam() Integer state
    ) {
        NoteCommentModel comment = noteService.getCommentById(commentId, true).dataOrThrow();
        moderatorByNoteOrThrow(comment.getNoteId(), userId);
        return kbRes.asIs(noteService.commentUpdateState(commentId, state));
    }

    @ApiOperation("帖子: 设置帖子的置顶评论")
    @PostMapping("/usr/note/setNoteStickyComment")
    public KbEntity<Void> setNoteStickyComment(
            @RequestHeader(KbHeader.KB_USER_ID) Integer userId,
            @RequestParam() String noteId,
            @RequestParam() String commentId
    ) {
        moderatorByNoteOrThrow(noteId, userId);
        return kbRes.asIs(noteService.setNoteStickyComment(userId, noteId, commentId));
    }

    @ApiOperation("帖子: 取消帖子的置顶评论")
    @PostMapping("/usr/note/removeNoteStickyComment")
    public KbEntity<Void> removeNoteStickyComment(
            @RequestHeader(KbHeader.KB_USER_ID) Integer userId,
            @RequestParam() String noteId
    ) {
        moderatorByNoteOrThrow(noteId, userId);
        return kbRes.asIs(noteService.removeNoteStickyComment(userId, noteId));
    }

    @ApiOperation("评论: 获取评论")
    @PostMapping("/usr/note/getCommentById")
    public KbEntity<NoteCommentModel> getCommentById(
            @RequestParam() String commentId
    ) {
        return kbRes.asIs(noteService.getCommentById(commentId, false));
    }

    @ApiOperation("评论审核: 根据帖子获取待审核评论列表")
    @PostMapping("/usr/note/getCommentReviewListByNote")
    public KbEntity<List<NoteCommentModel>> getCommentReviewListByNote(
            @RequestHeader(KbHeader.KB_USER_ID) Integer userId,
            @RequestParam() String noteId,
            @RequestParam(required = false, defaultValue = "1") @KbPage Integer page,
            @RequestParam(required = false, defaultValue = "20") @KbPageSize Integer pageSize
    ) {
        moderatorByNoteOrThrow(noteId, userId);
        return kbRes.asIs(noteService.getCommentReviewListByNote(userId, noteId, page, pageSize));
    }

    @ApiOperation("评论审核: 评论审核通过")
    @PostMapping("/usr/note/approveCommentReview")
    public KbEntity<Void> approveCommentReview(
            @RequestHeader(KbHeader.KB_USER_ID) Integer userId,
            @RequestParam() String commentReviewId
    ) {
        NoteCommentModel review = noteService.getCommentReviewById(userId, commentReviewId).dataOrThrow();
        NoteModel note = noteService
                .getNoteById(review.getNoteId(), null, null, NoteGrainFlag.MINIMUM_GRAIN.getFlag())
                .dataOrThrow();
        moderatorByThreadIdListOrThrow(note.getThreadIds(), userId);
        return kbRes.asIs(noteService.commentReviewApprove(userId, commentReviewId));
    }

    @ApiOperation("评论审核: 评论审核不予通过")
    @PostMapping("/usr/note/refuseCommentReview")
    public KbEntity<Void> refuseCommentReview(
            @RequestHeader(KbHeader.KB_USER_ID) Integer userId,
            @RequestParam() String commentReviewId,
            @RequestParam(required = false) String reason
    ) {
        NoteCommentModel review = noteService.getCommentReviewById(userId, commentReviewId).dataOrThrow();
        NoteModel note = noteService
                .getNoteById(review.getNoteId(), null, null, NoteGrainFlag.MINIMUM_GRAIN.getFlag())
                .dataOrThrow();
        moderatorByThreadIdListOrThrow(note.getThreadIds(), userId);
        return kbRes.asIs(noteService.commentReviewRefuse(userId, commentReviewId, reason));
    }

    // -------------------------------------------------------------
    // 用户管理相关

    @ApiOperation("黑名单: 获取用户黑名单列表")
    @PostMapping("/usr/user/getUserBlacklist")
    public KbEntity<List<UserModel>> getUserBlacklist(
            @RequestHeader(KbHeader.KB_USER_ID) Integer userId
    ) {
        return kbRes.asIs(userService.getUserBlacklistAsUser(userId, true));
    }

    @ApiOperation("黑名单: 是否在用户黑名单列表内")
    @PostMapping("/usr/user/isInUserBlacklist")
    public KbEntity<Boolean> isInUserBlacklist(
            @RequestHeader(KbHeader.KB_USER_ID) Integer userId,
            @RequestParam() @KbUserId Integer targetUserId
    ) {
        return kbRes.asIs(userService.isInUserBlacklist(userId, targetUserId, false));
    }

    @ApiOperation("黑名单: 将用户添加至黑名单列表")
    @PostMapping("/usr/user/addToUserBlacklist")
    public KbEntity<Void> addToUserBlacklist(
            @RequestHeader(KbHeader.KB_USER_ID) Integer userId,
            @RequestParam() @KbUserId Integer targetUserId
    ) {
        return kbRes.asIs(userService.addToUserBlacklist(userId, targetUserId));
    }

    @ApiOperation("黑名单: 从黑名单中移除用户")
    @PostMapping("/usr/user/removeFromUserBlacklist")
    public KbEntity<Void> removeFromUserBlacklist(
            @RequestHeader(KbHeader.KB_USER_ID) Integer userId,
            @RequestParam() @KbUserId Integer targetUserId
    ) {
        return kbRes.asIs(userService.removeFromUserBlacklist(userId, targetUserId));
    }

    @ApiOperation("禁言: 禁言帖子作者 1 天")
    @PostMapping("/usr/user/banUserByNote")
    public KbEntity<Void> banUserByNote(
            @RequestHeader(KbHeader.KB_USER_ID) Integer userId,
            @RequestParam() String noteId
    ) {
        NoteModel note = noteService
                .getNoteById(noteId, null, null, NoteGrainFlag.MINIMUM_GRAIN.getFlag())
                .dataOrThrow();
        moderatorByThreadIdListOrThrow(note.getThreadIds(), userId);
        long expireAt = (System.currentTimeMillis() / 1000) + TimeUnit.DAYS.toSeconds(1);
        return kbRes.asIs(authService.grantRoleToUser(
                userId, "app.read_only", note.getUserId(), expireAt, "版主禁言发帖用户"))
                .map(s -> null);
    }

    @ApiOperation("禁言: 禁言帖子评论作者 1 天")
    @PostMapping("/usr/user/banUserByComment")
    public KbEntity<Void> banUserByComment(
            @RequestHeader(KbHeader.KB_USER_ID) Integer userId,
            @RequestParam() String noteId,
            @RequestParam() String commentId
    ) {
        NoteCommentModel comment = noteService.getCommentById(commentId, true).dataOrThrow();
        if (!comment.getNoteId().equals(noteId)) {
            return kbRes.err(KbCode.AUTH_NO_PERMISSION, "note mismatch", "没有管理权限");
        }
        moderatorByNoteOrThrow(noteId, userId);
        long expireAt = (System.currentTimeMillis() / 1000) + TimeUnit.DAYS.toSeconds(1);
        return kbRes.asIs(authService.grantRoleToUser(
                userId, "app.read_only", comment.getUser().getUserId(), expireAt, "版主禁言评论用户"))
                .map(s -> null);
    }

    @ApiOperation("禁言: 取消发帖用户禁言")
    @PostMapping("/usr/user/cancelBanUserByNote")
    public KbEntity<Boolean> cancelBanUserByNote(
            @RequestHeader(KbHeader.KB_USER_ID) Integer userId,
            @RequestParam() String noteId
    ) {
        NoteModel note = noteService
                .getNoteById(noteId, null, null, NoteGrainFlag.MINIMUM_GRAIN.getFlag())
                .dataOrThrow();
        moderatorByThreadIdListOrThrow(note.getThreadIds(), userId);
        return kbRes.asIs(authService.removeRoleFromUser(userId, "app.read_only", note.getUserId()))
                .map(s -> null);
    }

    @ApiOperation("禁言: 取消评论用户禁言")
    @PostMapping("/usr/user/cancelBanUserByComment")
    public KbEntity<Boolean> cancelBanUserByComment(
            @RequestHeader(KbHeader.KB_USER_ID) Integer userId,
            @RequestParam() String noteId,
            @RequestParam() String commentId
    ) {
        NoteCommentModel comment = noteService.getCommentById(commentId, true).dataOrThrow();
        if (!comment.getNoteId().equals(noteId)) {
            return kbRes.err(KbCode.AUTH_NO_PERMISSION, "note mismatch", "没有管理权限");
        }
        moderatorByNoteOrThrow(noteId, userId);
        return kbRes.asIs(authService.removeRoleFromUser(userId, "app.read_only", comment.getUser().getUserId()))
                .map(s -> null);
    }

    @ApiOperation("禁言: 获取用户是否被禁言")
    @PostMapping("/obj/user/isUserBanned")
    public KbEntity<Boolean> isUserBanned(
            @RequestParam() Integer targetUserId
    ) {
        return kbRes.asIs(authService.hasPermission(targetUserId, "app.post.content", null)
                .map(authList -> {
                    if (authList == null || authList.isEmpty()) {
                        return false;
                    } else {
                        Integer authState = authList.get(0).getState();
                        return authState == null || authState != AuthResultState.PASS.getValue();
                    }
                }));
    }

    // -------------------------------------------------------------

    private void moderatorByNoteAndThreadOrThrow(String noteId, String threadId, Integer userId) {
        NoteModel note = noteService
                .getNoteById(noteId, null, null, NoteGrainFlag.MINIMUM_GRAIN.getFlag())
                .dataOrThrow();
        if (note.getThreadIds() == null || note.getThreadIds().isEmpty()) {
            throw new KbException(KbCode.AUTH_NO_PERMISSION).r("未授权管理该帖子(1)").li();
        }
        if (!note.getThreadIds().contains(threadId)) {
            throw new KbException(KbCode.AUTH_NO_PERMISSION).r("未授权管理该帖子(2)").li();
        }
        moderatorByThreadOrThrow(threadId, userId);
    }

    private void moderatorByNoteOrThrow(String noteId, Integer userId) {
        NoteModel note = noteService
                .getNoteById(noteId, null, null, NoteGrainFlag.MINIMUM_GRAIN.getFlag())
                .dataOrThrow();
        moderatorByThreadIdListOrThrow(note.getThreadIds(), userId);
    }

    private void moderatorByThreadIdListOrThrow(List<String> threadIds, Integer userId) {
        if (threadIds == null || threadIds.isEmpty()) {
            throw new KbException(KbCode.AUTH_NO_PERMISSION).r("未授权管理该帖子(3)").li();
        } else {
            for (String threadId : threadIds) {
                if (circleApiService.getCircleByThreadId(threadId)
                        .map(circle -> moderatorApiService.isCircleModerator(circle.getCircleId(), userId))
                        .orElse(false))
                {
                    return;
                }
            }
        }
        throw new KbException(KbCode.AUTH_NO_PERMISSION).r("未授权管理该帖子(4)").li();
    }

    private void moderatorByThreadOrThrow(String threadId, Integer userId) {
        circleApiService.getCircleByThreadId(threadId)
                .map(circle -> moderatorApiService.isCircleModerator(circle.getCircleId(), userId))
                .orElseThrow(() -> new KbException(KbCode.AUTH_NO_PERMISSION).r("未授权管理该话题").li());
    }

}
