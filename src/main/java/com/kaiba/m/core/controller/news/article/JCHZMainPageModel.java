package com.kaiba.m.core.controller.news.article;

import com.kaiba.lib.base.constant.news.NewsChannel;
import com.kaiba.lib.base.constant.news.NewsChannelType;
import com.kaiba.lib.base.constant.news.article.ChannelArticleListStyle;
import com.kaiba.lib.base.constant.news.article.ChannelProgrammeListStyle;
import com.kaiba.lib.base.domain.appwidget.banner.BannerInstanceModel;
import com.kaiba.lib.base.domain.common.ActionLink;
import com.kaiba.lib.base.domain.common.Image;
import com.kaiba.lib.base.domain.common.ShareModel;
import com.kaiba.lib.base.domain.news.pool.bygroup.GroupTopicTab;
import com.kaiba.lib.base.domain.news.pool.topic.TopicLayoutModel;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.List;

/**
 * copy from ChannelMainPageModel
 * <AUTHOR>
 * @version JCHZMainPageModel, v0.1 2024/9/14 17:46 daopei Exp $
 **/
@Data
@ToString
@NoArgsConstructor
public class JCHZMainPageModel {


    private String id;

    /** 频道标识. {@link NewsChannel} */
    private String channelKey;

    /** 实例标题 */
    private String title;

    /** 顶部布局. {@link TopicLayoutModel#getId()} */
    private String layoutId;

    /** 栏目列表样式类型. {@link ChannelProgrammeListStyle} */
    private String programmeListStyle;

    /** 全部文章列表样式类型. {@link ChannelArticleListStyle} */
    private String articleListStyle;

    /** tab 分组标识属性. */
    private List<GroupTopicTab> tabs;

    /** 分享配置 */
    private ShareModel share;

    /** 创建时间, 单位毫秒. 设置后不可修改. */
    private Long createTime;

    /** 更新时间, 单位毫秒. */
    private Long updateTime;

    // -------------------------------------------

    /** 频道名称 */
    private String channelName;

    /** 频道简称 */
    private String channelAbbr;

    /** 频道图标 */
    private Image channelLogo;

    /** 频道类型. {@link NewsChannelType} */
    private String channelType;

    /** 顶部布局数据 */
    private TopicLayoutModel layout;

    /** 栏目列表数据, 分段1, 展示在上半部分 */
    private List<ActionLink> programmes;

    /** 栏目列表数据, 分段2, 展示在下半部分 */
    private List<ActionLink> programmes2;

    /** 栏目列表中展示的图片轮播实例. */
    private BannerInstanceModel programmeBanner;

    // -------------------------------------------

    /** 布局视频使用最新的内容 */
    private boolean layoutVideoAuto;

    /** 布局视频直播流地址 */
    private String layoutVideoLiveUrl;

    /** 布局视频是否强制使用直播流 */
    private boolean layoutVideoLiveForce;

    /** 布局视频获取来源的栏目KEY */
    private String layoutVideoGetProgrammeKey;

    /** 栏目跳转页面过滤(title). 声明后此集合不跳转原生 */
    private List<String> programNativeFilter;

    /** programmes数据来源使用bannerInstance. 如果存在则优先展示实例配置的栏目内容 */
    private String programmesInstanceKey;

    /** programmes2数据来源使用bannerInstance. 如果存在则优先展示实例配置的栏目内容 */
    private String programmes2InstanceKey;

}
