package com.kaiba.m.core.controller.artmap.recommend.api;

import com.kaiba.lib.base.constant.KbHeader;
import com.kaiba.lib.base.middleware.KbRes;
import com.kaiba.lib.base.response.KbEntity;
import com.kaiba.m.core.model.artmap.RecommendArticleListModel;
import com.kaiba.m.core.service.artmap.RecommendQueryService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * Description: 艺术地图每日推荐API查询Controller
 * Author: ZM227
 * Date: 2025/6/9 14:47
 */
@Slf4j
@RestController
@RequestMapping("/api/artMap/recommend/query")
@Api(tags = "艺术地图每日推荐查询")
public class RecommendApiQueryController {

    @Resource
    private KbRes kbRes;
    @Resource
    private RecommendQueryService recommendQueryService;

    @PostMapping("/obj/queryCurrentRecommendList")
    @ApiOperation(value = "查询当日的每日推荐信息")
    public KbEntity<RecommendArticleListModel> queryCurrentRecommendList() {
        return kbRes.ok(recommendQueryService.queryRecommendArticleList());
    }

}
