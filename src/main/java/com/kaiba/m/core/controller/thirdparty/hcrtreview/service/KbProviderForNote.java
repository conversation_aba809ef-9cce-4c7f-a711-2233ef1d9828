package com.kaiba.m.core.controller.thirdparty.hcrtreview.service;

import com.kaiba.lib.base.constant.note.NoteCommentOrder;
import com.kaiba.lib.base.constant.note.NoteOrder;
import com.kaiba.lib.base.domain.common.Image;
import com.kaiba.lib.base.domain.note.NoteGrainFlag;
import com.kaiba.lib.base.domain.note.NoteModel;
import com.kaiba.lib.base.service.INoteService;
import com.kaiba.m.core.controller.thirdparty.hcrtreview.constant.KbBizType;
import com.kaiba.m.core.controller.thirdparty.hcrtreview.constant.KbHCRTReviewConst;
import com.kaiba.m.core.controller.thirdparty.hcrtreview.constant.KbItemType;
import com.kaiba.m.core.controller.thirdparty.hcrtreview.model.KbContent;
import com.kaiba.m.core.controller.thirdparty.hcrtreview.model.KbResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * author: lyux
 * date: 2024-10-24
 */
@Slf4j
@Component
public class KbProviderForNote implements IKbProvider {

    private final INoteService noteService;

    public KbProviderForNote(INoteService noteService) {
        this.noteService = noteService;
    }

    @Override
    public KbBizType getBiz() {
        return KbBizType.NOTE;
    }

    @Override
    public KbResult getContentList(int page, int pageSize, long until) {
        int max = 1_000_000 / pageSize;
        for (int i = page; i < max; i ++) {
            KbResult result = requestContentList(i, pageSize, until);
            if (result.getContents().isEmpty() && result.getHasNext()) {
                if (KbHCRTReviewConst.DEBUG) {
                    log.info("HCRTReview, get note, result content empty, yet hasNext is true, continue loop");
                }
            } else {
                if (KbHCRTReviewConst.DEBUG) {
                    log.info(result.toString());
                }
                return result;
            }
        }

        return KbResult.asLast(KbBizType.NOTE);
    }

    private KbResult requestContentList(int page, int pageSize, long until) {
        List<NoteModel> noteList = noteService.getNoteList(null, NOTE_GRAIN, null, page, pageSize)
                .data().orElse(Collections.emptyList());
        KbResult result = new KbResult(KbBizType.NOTE);
        if (noteList.isEmpty()) {
            result.setContents(Collections.emptyList());
            result.setHasNext(false);
        } else {
            boolean hasNext = true;
            List<KbContent> contentList = new ArrayList<>(noteList.size());
            for (NoteModel note : noteList) {
                if (note.getCreateTimeMS() == null || note.getCreateTimeMS() < until) {
                    hasNext = false;
                    continue;
                }
                KbContent content = new KbContent(KbBizType.NOTE, note.getId());
                if (note.getContent() != null) {
                    content.add(KbItemType.TEXT, note.getContent(), "text", "帖子正文");
                }
                if (note.getImages() != null && !note.getImages().isEmpty()) {
                    for (Image image : note.getImages()) {
                        content.add(image, "image", "帖子图片");
                    }
                }
                if (note.getVideo() != null) {
                    content.add(note.getVideo(), "video", "帖子视频");
                }
                if (note.getAudio() != null) {
                    content.add(note.getAudio());
                }
                if (content.notEmpty()) {
                    contentList.add(content);
                }
            }
            result.setContents(contentList);
            if (hasNext) {
                result.setNextPage(page + 1);
                result.setHasNext(true);
            } else {
                result.setHasNext(false);
            }
        }
        return result;
    }

    private static final long NOTE_GRAIN = new NoteGrainFlag.Builder()
            .setNeedUserInfo(false)
            .setNeedContent(true)
            .setNeedLocation(false)
            .setNeedCountData(false)
            .setNeedLinks(false)
            .setNeedThreads(false)
            .setNeedOriginThread(false)
            .setNeedStickyCommentData(false)
            .setNeedCommentUserInfo(false)
            .setHideContentOnDelete(false)
            .setAllowCache(false)
            .setAsViewCount(false)
            .setNeedExtra(false)
            .setNeedCommentListSize(0)
            .setNeedPraiseListSize(0)
            .setHonorAnonymous(false)
            .setHonorBlacklist(false)
            .setHonorAuthorOnly(false)
            .setNoteOrder(NoteOrder.REFRESH_TIME_DESC)
            .setCommentOrder(NoteCommentOrder.CREATE_TIME_DESC)
            .create().getFlag();

}
