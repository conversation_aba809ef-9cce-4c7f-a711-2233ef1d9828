package com.kaiba.m.core.controller.user;

import com.kaiba.lib.base.assembler.Mapper;
import com.kaiba.lib.base.domain.user.UserLoginLogCheckModel;
import com.kaiba.lib.base.domain.user.UserLoginLogModel;
import com.kaiba.lib.base.middleware.KbRes;
import com.kaiba.lib.base.response.KbCode;
import com.kaiba.lib.base.response.KbEntity;
import com.kaiba.lib.base.service.IUserLogService;
import com.kaiba.m.core.domain.user.UserLoginFailedLog;
import com.kaiba.m.core.domain.user.UserLoginLog;
import com.kaiba.m.core.service.user.UserLogService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Optional;

/**
 * author: yeqq
 * date: 20-12-10
 */
@Slf4j
@RestController
public class UserLogServiceController implements IUserLogService {

    private final KbRes kbRes;
    private final UserLogService userLogService;

    public UserLogServiceController(
            KbRes kbRes,
            UserLogService userLogService
    ) {
        this.kbRes = kbRes;
        this.userLogService = userLogService;
    }

    @Override
    public KbEntity<UserLoginLogModel> getUserLoginLogByUserId(Integer userId) {
        return userLogService.getUserLoginLogByUserId(userId)
                .map(this::userLog2model)
                .map(kbRes::ok)
                .orElse(kbRes.err(KbCode.RESOURCE_NOT_FOUND));
    }

    @Override
    public KbEntity<List<UserLoginLogModel>> getUserLoginLogListByUserId(
            Integer userId, Long startTime, Long endTime, Integer page, Integer pageSize) {
        return kbRes.ok(userLogService
                .getUserLoginLogListByUserId(userId,startTime,endTime,page,pageSize)
                .map(this::userLog2model));
    }

    @Override
    public KbEntity<List<UserLoginLogModel>> getUserLoginLogListByUserIdAndCreateTime(
            Integer userId, Long createTime, Integer page, Integer pageSize) {
        return kbRes.ok(userLogService
                .getUserLoginLogListByUserId(userId,createTime,page,pageSize)
                .map(this::userLog2model));
    }

    @Override
    public KbEntity<UserLoginLogCheckModel> checkUserLastLoginByUserId(Integer userId, Integer level) {
        if (level>3||level<1){
            return kbRes.ok();
        }
        Optional<UserLoginFailedLog> failedLog = userLogService.getUserLoginFailedLogByUserId(userId);
        List<UserLoginLog> logList = userLogService.getLast3LogByUserId(userId);
        UserLoginLog last = null;
        UserLoginLog pre = null;
        if (logList.size() > 1) {
            last = logList.get(1);
        }
        if (logList.size() > 2){
            pre = logList.get(2);
        }
        if (failedLog.isPresent() && (pre == null || pre.getCreateTime()<failedLog.get().getCreateTime())){
            return kbRes.ok(checkModel(failedLog.get()));
        }

        if (level>1){
            if( pre != null && last != null
                    && (!last.getDb().equals(pre.getDb()) ||
                    !last.getIpCity().equals(pre.getIpCity()))
                    && (!failedLog.isPresent() || failedLog.get().getCreateTime() < pre.getCreateTime() )
            ) {
                if (last.getDb().equals(pre.getDb())){
                    return kbRes.ok(checkModel(pre,2,"登录设备异常"));
                }else {
                    return kbRes.ok(checkModel(pre,2,"登录城市异常"));
                }
            }
        }
        if (level>2){
            if( pre != null ) {
                return kbRes.ok(checkModel(pre,3, "正常"));
            }
        }
        return kbRes.ok();
    }

    @Override
    public KbEntity<List<UserLoginLogCheckModel>> getLastDifferentStatusLoginLog(Integer userId) {
        List<UserLoginLogCheckModel> list = new ArrayList<>(2);
        userLogService.getUserLoginFailedLogByUserId(userId).ifPresent(t->{
            list.add(checkModel(t));
        });
        List<UserLoginLog> logList = userLogService.getLast3LogByUserId(userId);

        if (logList.size()==2){
            list.add(checkModel(logList.get(1),3,"正常"));
        }else if (logList.size()==3){
            UserLoginLog last = logList.get(1);
            UserLoginLog pre = logList.get(2);
            if(!last.getDb().equals(pre.getDb())) {
                list.add(checkModel(last, 2, "登录设备异常"));
            } else if(!last.getIpCity().equals(pre.getIpCity())){
                list.add(checkModel(last, 2, "登录城市异常"));
            } else {
                list.add(checkModel(last, 3, "正常"));
            }
        }
        list.sort(Comparator.comparing(UserLoginLogCheckModel::getLoginTime).reversed());
        return kbRes.ok(list);
    }

    private UserLoginLogModel userLog2model(UserLoginLog userLoginLog) {
        return Mapper.map(userLoginLog, UserLoginLogModel.class);
    }

    private UserLoginLogCheckModel checkModel(UserLoginFailedLog log){
        UserLoginLogCheckModel model = new UserLoginLogCheckModel();
        model.setLevel(1);
        model.setIp(log.getIp());
        model.setCity("杭州");
        model.setDevice(log.getDb());
        model.setLoginTime(log.getCreateTime()/1000);
        model.setRemark("密码错误");
        return model;
    }

    private UserLoginLogCheckModel checkModel(UserLoginLog log, Integer level, String remark){
        UserLoginLogCheckModel model = new UserLoginLogCheckModel();
        model.setLevel(level);
        model.setIp(log.getIp());
        model.setCity("杭州");
        model.setRemark(remark);
        model.setDevice(log.getDb());
        model.setLoginTime(log.getCreateTime()/1000);
        return model;
    }
}
