package com.kaiba.m.core.controller.education;

import com.kaiba.lib.base.constant.KbHeader;
import com.kaiba.lib.base.domain.education.KidModel;
import com.kaiba.lib.base.middleware.KbRes;
import com.kaiba.lib.base.response.KbEntity;
import com.kaiba.m.core.service.education.EducationK12KidService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/03/20 10:00
 */
@Slf4j
@RestController
@RequestMapping("/education/k12/api")
public class EducationK12ApiController {

    private final KbRes kbRes;
    private final EducationK12KidService kidService;

    public EducationK12ApiController(
            <PERSON>b<PERSON><PERSON> kbRes,
            EducationK12KidService kidService
    ) {
        this.kbRes = kbRes;
        this.kidService = kidService;
    }

    @PostMapping("/usr/getMyChildren")
    public KbEntity<List<KidModel>> getMyChildren(
            @RequestHeader(name = KbHeader.KB_USER_ID) Integer userId
    ) {
        return kbRes.ok(kidService.getKidListByRelativeUserId(userId));
    }
}
