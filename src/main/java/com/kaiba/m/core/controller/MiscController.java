package com.kaiba.m.core.controller;

import com.kaiba.lib.base.lang.collections.BuilderMap;
import com.kaiba.lib.base.middleware.KbRes;
import com.kaiba.lib.base.response.KbEntity;
import com.kaiba.lib.base.rte.RelativeTimeExpression;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.PostConstruct;
import java.text.SimpleDateFormat;
import java.time.Duration;
import java.util.Collections;
import java.util.Date;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * author: lyux
 * date: 18-9-4
 */
@Slf4j
@RestController
public class MiscController {

    private static final String HOSTS_PREFIX = "java_service_hosts_";

    private final KbRes kbRes;
    private final ThreadPoolExecutor kaibaExecutor;
    private final StringRedisTemplate redisTemplate;

    public MiscController(
            KbRes kbRes,
            ThreadPoolExecutor kaibaExecutor,
            StringRedisTemplate redisTemplate
    ) {
        this.kbRes = kbRes;
        this.kaibaExecutor = kaibaExecutor;
        this.redisTemplate = redisTemplate;
    }

    @PostConstruct
    private void init() {
        String commitId = kbRes.getApplicationProperties().getGitProperties().getCommitId();
        String host = kbRes.getApplicationProperties().getHost();
        String key = HOSTS_PREFIX + commitId;
        redisTemplate.opsForSet().add(key, host);
        redisTemplate.expire(key, Duration.ofDays(90));
    }

    @GetMapping("/healthcheck")
    public String healthCheck() {
        return "ok";
    }

    @GetMapping("/getApplicationProperties")
    public Map<String, String> getApplicationProperties() {
        return kbRes.getApplicationProperties().getProperties();
    }


    @GetMapping("/getApplicationProperty")
    public String getApplicationProperty(String name) {
        return kbRes.getApplicationProperties().getProperty(name);
    }

    @GetMapping("/getServiceHostList")
    public Set<String> getServiceHostList(
            @RequestParam(required = false) String commitId
    ) {
        if (commitId == null) {
            commitId = kbRes.getApplicationProperties().getGitProperties().getCommitId();
        }
        String key = HOSTS_PREFIX + commitId;
        return redisTemplate.opsForSet().members(key);
    }

    @GetMapping("/getExecutorStat")
    public Map<String, Object> getExecutorStat(
            @RequestParam() String secret
    ) {
        if ("a81fa2ce2eaa6c65dfc2b5912e576280".equals(secret)) {
            return new BuilderMap<String, Object>()
                    .putValue("corePoolSize", kaibaExecutor.getCorePoolSize())
                    .putValue("maxPoolSize", kaibaExecutor.getMaximumPoolSize())
                    .putValue("poolSize", kaibaExecutor.getPoolSize())
                    .putValue("taskCount", kaibaExecutor.getTaskCount())
                    .putValue("activeCount", kaibaExecutor.getActiveCount())
                    .putValue("completedCount", kaibaExecutor.getCompletedTaskCount())
                    .putValue("queueSize", kaibaExecutor.getQueue().size());
        } else {
            return Collections.emptyMap();
        }
    }

    @GetMapping("/calculateRelativeExpression")
    public KbEntity<CalculateRTEResult> calculateRelativeExpression(
            @RequestParam(required = false) Long timeInSecond,
            @RequestParam() String rte
    ) {
        long t = timeInSecond == null ? System.currentTimeMillis() / 1000 : timeInSecond;
        return kbRes.ok(new CalculateRTEResult(t, rte));
    }

    @Data
    private static class CalculateRTEResult {
        private String rte;
        private Long timeInSecond;
        private String timeFormatted;
        private Long resultInSecond;
        private String resultFormatted;

        public CalculateRTEResult(long timeInSecond, String rte) {
            SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            this.rte = rte;
            this.timeInSecond = timeInSecond;
            this.timeFormatted = format.format(new Date(timeInSecond * 1000));
            long result = RelativeTimeExpression.calculate(timeInSecond * 1000, rte);
            this.resultInSecond = result / 1000;
            this.resultFormatted = format.format(new Date(result));
        }
    }

}
