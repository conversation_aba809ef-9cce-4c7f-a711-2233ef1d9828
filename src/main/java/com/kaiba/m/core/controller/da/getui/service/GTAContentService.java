package com.kaiba.m.core.controller.da.getui.service;

import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.kaiba.lib.base.constant.KbModule;
import com.kaiba.lib.base.lang.collections.KbColUtils;
import com.kaiba.lib.base.response.KbCode;
import com.kaiba.lib.base.response.KbException;
import com.kaiba.lib.base.service.INeoNewsArticleService;
import com.kaiba.lib.base.util.GsonUtils;
import com.kaiba.m.core.controller.da.getui.model.GTABestDramaModel;
import com.kaiba.m.core.controller.da.getui.model.GTAHotFocusContentModel;
import com.kaiba.m.core.controller.da.getui.model.GTAItemContentUsage;
import com.kaiba.m.core.controller.da.getui.model.GTATitleAndCoverModel;
import com.kaiba.m.core.domain.da.eventtrack.ETDoc;
import com.kaiba.m.core.domain.da.getui.GTAFocusContent;
import com.kaiba.m.core.domain.da.getui.GTAItemContent;
import com.kaiba.m.core.repository.da.getui.GTAFocusContentRepository;
import com.kaiba.m.core.repository.da.getui.GTAItemContentRepository;
import com.kaiba.m.core.service.da.eventtrack.ETDocService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

import java.util.*;

/**
 * 个推大屏内容数据服务
 * 当前支持模块:
 *      1.重点内容
 *      2.热门海报
 *      3.稿件标题封面获取
 * <AUTHOR>
 * @version GTAFocusContentService, v0.1 2024/12/17 17:51 daopei Exp $
 **/
@Slf4j
@Service
public class GTAContentService {

    private final GTAFocusContentRepository gtAFocusContentRepository;
    private final GTAItemContentRepository gtaItemContentRepository;
    private final StringRedisTemplate redisTemplate;
    private final RestTemplate restTemplate;
    private final ETDocService etDocService;
    private final INeoNewsArticleService articleService;

    private final static String BEST_DRAMA_KEY = "java_core_getui_best_drama";
    private final static String BEST_BOX_OFFICE_KEY = "java_core_getui_best_box_office";
    private static final String URL_REVEAL_MAIN = "http://kaiba-activity/api/reveal/obj/getMainPageInfo";

    public GTAContentService(
            GTAFocusContentRepository gtAFocusContentRepository,
            GTAItemContentRepository gtaItemContentRepository,
            StringRedisTemplate redisTemplate,
            RestTemplate restTemplate,
            ETDocService etDocService,
            INeoNewsArticleService articleService
    ) {
        this.gtAFocusContentRepository = gtAFocusContentRepository;
        this.gtaItemContentRepository = gtaItemContentRepository;
        this.redisTemplate = redisTemplate;
        this.restTemplate = restTemplate;
        this.etDocService = etDocService;
        this.articleService = articleService;
    }


    // 重点内容----------------------

    public Page<GTAFocusContent> getFocusContentList(Integer state, Integer page, Integer pageSize) {
        if (state == null) {
            return gtAFocusContentRepository.findAll(createPageable(page, pageSize, Sort.by(Sort.Direction.ASC, "idx","createTime")));
        } else {
            return gtAFocusContentRepository.findByState(state, createPageable(page, pageSize, Sort.by(Sort.Direction.ASC, "idx","createTime")));
        }
    }


    public GTAFocusContent createFocusContent(Integer userId, GTAFocusContent content) {
        log.info("createFocusContent: {}, userId: {}" , GsonUtils.getGson().toJson(content), userId);
        if (content.getState() == null) {
            content.setState(1);
        }
        if (content.getIdx() == null) {
            content.setIdx(999);
        }
        content.setCreateTime(System.currentTimeMillis());
        content.setUpdateTime(System.currentTimeMillis());
        return gtAFocusContentRepository.save(content);
    }

    public void updateFocusContentIdx(Integer userId, List<GTAFocusContent> contentList) {
        log.info("updateFocusContentIdx: {}, userId: {}" , GsonUtils.getGson().toJson(contentList), userId);
        gtAFocusContentRepository.updateIdxBatch(contentList);
    }

    public GTAFocusContent updateFocusContent(Integer userId, GTAFocusContent content) {
        GTAFocusContent gtaFocusContent = gtAFocusContentRepository.findById(content.getId())
                .orElseThrow(() -> new KbException(KbCode.RESOURCE_NOT_FOUND).li());
        log.info("updateFocusContent: {}, userId: {}" , GsonUtils.getGson().toJson(content), userId);
        gtaFocusContent.setTitle(content.getTitle());
        gtaFocusContent.setImage(content.getImage());
        gtaFocusContent.setActList(content.getActList());
        gtaFocusContent.setNewsList(content.getNewsList());
        gtaFocusContent.setDocList(content.getDocList());
        gtaFocusContent.setState(content.getState());
        gtaFocusContent.setUpdateTime(System.currentTimeMillis());
        return gtAFocusContentRepository.save(content);
    }


    public void deleteFocusContent(Integer userId, String contentId) {
        log.info("deleteFocusContent: {}, userId: {}" , contentId, userId);
        gtAFocusContentRepository.deleteById(contentId);
    }

    // 热门海报----------------------

    public GTABestDramaModel createBestDrama(Integer userId, GTABestDramaModel bestDrama) {
        log.info("createBestDrama: {}, userId: {}" , GsonUtils.getGson().toJson(bestDrama), userId);
        redisTemplate.opsForValue().set(BEST_DRAMA_KEY, GsonUtils.getGson().toJson(bestDrama));
        return bestDrama;
    }

    public void deleteBestDrama(Integer userId) {
        log.info("deleteBestDrama: userId: {}" , userId);
        redisTemplate.expireAt(BEST_DRAMA_KEY, new Date());
    }

    public Optional<GTABestDramaModel> getBestDrama() {
        String value = redisTemplate.opsForValue().get(BEST_DRAMA_KEY);
        if (value == null) {
            return Optional.empty();
        }
        return Optional.of(GsonUtils.getGson().fromJson(value, GTABestDramaModel.class));
    }


    // 热门票房----------------------

    public GTABestDramaModel createBestBoxOffice(Integer userId, GTABestDramaModel bestDrama) {
        log.info("createBestBoxOffice: {}, userId: {}" , GsonUtils.getGson().toJson(bestDrama), userId);
        redisTemplate.opsForValue().set(BEST_BOX_OFFICE_KEY, GsonUtils.getGson().toJson(bestDrama));
        return bestDrama;
    }

    public void deleteBestBoxOffice(Integer userId) {
        log.info("deleteBestBoxOffice: userId: {}" , userId);
        redisTemplate.expireAt(BEST_BOX_OFFICE_KEY, new Date());
    }

    public Optional<GTABestDramaModel> getBestBoxOffice() {
        String value = redisTemplate.opsForValue().get(BEST_BOX_OFFICE_KEY);
        if (value == null) {
            return Optional.empty();
        }
        return Optional.of(GsonUtils.getGson().fromJson(value, GTABestDramaModel.class));
    }


    public List<GTAHotFocusContentModel> getHotFocusContent() {
        String response = requestService(URL_REVEAL_MAIN, KbColUtils.MapOpt.upsertHashMap(new HashMap<>(), "siteId", 9));

        if (response == null) {
            return null;
        }

        JsonObject root = GsonUtils.getGson().fromJson(response, JsonObject.class);
        JsonObject dateJson = root.get("data").getAsJsonObject();
        JsonArray arrayJson = dateJson.getAsJsonArray("revealRecommends");
        List<GTAHotFocusContentModel> result = new ArrayList<>();
        for (JsonElement element : arrayJson) {
            if (Objects.isNull(element) || element.isJsonNull()) {
                continue;
            }
            GTAHotFocusContentModel content = new GTAHotFocusContentModel();
            content.setSubject(element.getAsJsonObject().get("revealProgramTitle").getAsString());
            content.setContent(element.getAsJsonObject().get("revealScheduleTitle").getAsString());
            if (element.getAsJsonObject().has("viewCount")) {
                content.setVc(element.getAsJsonObject().get("viewCount").getAsInt());
            }
            result.add(content);
        }

        return result;
    }

    // 稿件查询 ----------------------

    public List<GTATitleAndCoverModel> getTitleAndCoverByDocIds(List<String> docIds) {
        log.info("getETDocByDocIds: {}", docIds);
        if (docIds == null || docIds.isEmpty()) {
            return Collections.emptyList();
        }

        List<ETDoc> docs = etDocService.getDocByIds(docIds);
        List<GTATitleAndCoverModel> result = new ArrayList<>();
        for (ETDoc doc : docs) {
            if (doc.getBiz().equals(KbModule.NEWS_NEO.name()) && "ARTICLE".equals(doc.getUnit())) {
                 articleService.getArticleById(doc.getRef1(), false, false)
                        .dataIgnoreError()
                        .map(article -> {
                            if (article.getCovers() == null || article.getCovers().isEmpty()) {
                                return GTATitleAndCoverModel.create(doc.getDocId(), doc.getTitle(), null);
                            } else {
                                return GTATitleAndCoverModel.create(doc.getDocId(), article.getTitle(), article.getCovers().get(0).getUrl());
                            }
                        })
                        .ifPresent(result::add);
            }
        }

        return result;
    }

    // ----------------------

    public Optional<GTAItemContent> getItemContentByUniqueUsage(GTAItemContentUsage usage) {
        return gtaItemContentRepository.findFirstByUsage(usage.name());
    }

    public void deleteItemContentByUniqueUsage(GTAItemContentUsage usage) {
        gtaItemContentRepository.deleteByUsage(usage.name());
    }

    public GTAItemContent upsertItemContentByUniqueUsage(
            GTAItemContentUsage usage, String contentId, String title, String image) {
        GTAItemContent content = new GTAItemContent();
        content.setUsage(usage.name());
        content.setContentId(contentId);
        content.setTitle(title);
        content.setImage(image);
        log.info("upsertItemContentByUniqueUsage: " + content);
        return gtaItemContentRepository.upsertDataByUsage(content);
    }

    // ----------------------

    private Pageable createPageable(Integer page, Integer pageSize, Sort sort) {
        int p = page == null ? 0 : page - 1;
        int ps = pageSize == null  ? 20 : pageSize;
        return PageRequest.of(p, ps, sort);
    }


    private String requestService(String url, Map<String, Object> paramMap) {
        MultiValueMap<String, Object> params = new LinkedMultiValueMap<>();
        if (paramMap != null) {
            for (Map.Entry<String, Object> stringObjectEntry : paramMap.entrySet()) {
                params.add(stringObjectEntry.getKey(), stringObjectEntry.getValue());
            }
        }
        HttpEntity<MultiValueMap<String, Object>> requestEntity = new HttpEntity<>(params);
        try {
            ResponseEntity<String> response = restTemplate.postForEntity(url, requestEntity, String.class);
            if (response.getStatusCode() != HttpStatus.OK) {
                log.error("requestService error: " + response);
                return null;
            }
            return response.getBody();
        } catch (Exception e) {
            log.error("requestService error: " + e.getMessage());
            return null;
        }
    }
}
