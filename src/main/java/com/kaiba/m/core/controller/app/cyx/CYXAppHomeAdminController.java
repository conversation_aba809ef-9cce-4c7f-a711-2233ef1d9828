package com.kaiba.m.core.controller.app.cyx;

import com.kaiba.lib.base.domain.common.ShareModel;
import com.kaiba.lib.base.middleware.KbRes;
import com.kaiba.lib.base.response.KbEntity;
import com.kaiba.m.core.model.appcyx.CYXArticleGroupIdxOrder;
import com.kaiba.m.core.model.appcyx.CYXArticleGroupModel;
import com.kaiba.m.core.service.appcyx.CYXAppHomeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 超有戏 首页管理
 * <AUTHOR>
 * @version CYXAppHomeAdminController, v0.1 2025/6/23 14:43 daopei Exp $
 **/
@Slf4j
@RestController
@RequestMapping("/admin/app/home/<USER>")
public class CYXAppHomeAdminController {

    private final KbRes kbRes;
    private final CYXAppHomeService cyxAppHomeService;

    public CYXAppHomeAdminController(
            KbRes kbRes,
            CYXAppHomeService cyxAppHomeService
    ) {
        this.kbRes = kbRes;
        this.cyxAppHomeService = cyxAppHomeService;
    }


    @PostMapping("/createGroup")
    public KbEntity<CYXArticleGroupModel> createGroup(
            @RequestBody CYXArticleGroupModel model
    ) {
        return kbRes.ok(cyxAppHomeService.createGroup(model));
    }

    @PostMapping("/deleteGroup")
    public KbEntity<Void> deleteGroup(
            @RequestParam String id
    ) {
        cyxAppHomeService.deleteGroup(id);
        return kbRes.ok();
    }

    @PostMapping("/updateGroup")
    public KbEntity<CYXArticleGroupModel> updateGroup(
            @RequestBody CYXArticleGroupModel model
    ) {
        return kbRes.ok(cyxAppHomeService.updateGroup(model));
    }

    @PostMapping("/updateGroupState")
    public KbEntity<CYXArticleGroupModel> updateGroupState(
            @RequestParam String id,
            @RequestParam String state
    ) {
        return kbRes.ok(cyxAppHomeService.updateGroupState(id, state));
    }

    @PostMapping("/updateGroupIdx")
    public KbEntity<Void> updateGroupIdx(
            @RequestBody List<CYXArticleGroupIdxOrder> orders
    ) {
        cyxAppHomeService.updateGroupIdx(orders);
        return kbRes.ok();
    }

    @PostMapping("/updateShare")
    public KbEntity<Void> updateShare(
            @RequestBody ShareModel share
    ) {
        cyxAppHomeService.updateShare(share);
        return kbRes.ok();
    }

    @PostMapping("/getShare")
    public KbEntity<ShareModel> getShare() {
        return kbRes.ok(cyxAppHomeService.getShare());
    }

    @PostMapping("/getGroupList")
    public KbEntity<List<CYXArticleGroupModel>> getGroupList(
            @RequestParam(required = false) String state,
            @RequestParam(required = false) String groupType,
            @RequestParam(required = false, defaultValue = "1") Integer page,
            @RequestParam(required = false, defaultValue = "10") Integer pageSize
    ) {
        return kbRes.ok(cyxAppHomeService.getGroupList(state, groupType, page, pageSize));
    }
}
