package com.kaiba.m.core.controller.app.testinvitation;

import com.kaiba.lib.base.assembler.Mapper;
import com.kaiba.lib.base.constant.common.KbContentState;
import com.kaiba.lib.base.domain.testinvitation.TestInvitationModel;
import com.kaiba.lib.base.domain.testinvitation.TestInvitationModifyModel;
import com.kaiba.lib.base.middleware.KbRes;
import com.kaiba.lib.base.response.KbCode;
import com.kaiba.lib.base.response.KbEntity;
import com.kaiba.lib.base.service.ITestInvitationService;
import com.kaiba.m.core.domain.testinvitation.TestInvitation;
import com.kaiba.m.core.service.testinvitation.TestInvitationService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.stream.Collectors;

/**
 * author: lyux
 * date: 2023-07-25
 */
@Slf4j
@RestController
public class TestInvitationServiceController implements ITestInvitationService {

    private final KbRes kbRes;
    private final TestInvitationService invitationService;

    public TestInvitationServiceController(KbRes kbRes, TestInvitationService invitationService) {
        this.kbRes = kbRes;
        this.invitationService = invitationService;
    }

    @Override
    public KbEntity<TestInvitationModel> createInvitation(TestInvitationModifyModel model) {
        TestInvitation invitation = invitationService.createInvitation(model);
        return kbRes.ok(invitation2model(invitation));
    }

    @Override
    public KbEntity<TestInvitationModel> updateInvitation(TestInvitationModifyModel model) {
        if (model.getId() == null) {
            return kbRes.err(KbCode.REQUEST_PARAM_INVALID, "id null", "id 不可为空");
        }
        TestInvitation invitation = invitationService.updateInvitation(model);
        return kbRes.ok(invitation2model(invitation));
    }

    @Override
    public KbEntity<Void> updateInvitationState(String invitationId, Integer state) {
        invitationService.updateInvitationState(invitationId, state);
        return kbRes.ok();
    }

    @Override
    public KbEntity<Void> deleteInvitationById(String invitationId) {
        invitationService.deleteInvitationById(invitationId);
        return kbRes.ok();
    }

    @Override
    public KbEntity<TestInvitationModel> getInvitationById(String invitationId) {
        return invitationService.getInvitationById(invitationId)
                .map(TestInvitationServiceController::invitation2model)
                .map(kbRes::ok)
                .orElseGet(() -> kbRes.err(KbCode.RESOURCE_NOT_FOUND));
    }

    @Override
    public KbEntity<TestInvitationModel> getLatestActiveInvitationByCode(Integer code) {
        return invitationService.getLastInvitationByCode(code)
                .filter(invitation -> invitation.getState() == KbContentState.ONLINE.getValue())
                .map(TestInvitationServiceController::invitation2model)
                .map(kbRes::ok)
                .orElseGet(() -> kbRes.err(KbCode.RESOURCE_NOT_FOUND, "not found", "未找到对应功能"));
    }

    @Override
    public KbEntity<TestInvitationModel> getLatestInvitationByCode(Integer code) {
        return invitationService.getLastInvitationByCode(code)
                .map(TestInvitationServiceController::invitation2model)
                .map(kbRes::ok)
                .orElseGet(() -> kbRes.err(KbCode.RESOURCE_NOT_FOUND));
    }

    @Override
    public KbEntity<List<TestInvitationModel>> getInvitationListByCode(Integer code) {
        return kbRes.ok(invitationService
                .getInvitationListByCode(code, 0, 100).stream()
                .map(TestInvitationServiceController::invitation2model)
                .collect(Collectors.toList()));
    }

    @Override
    public KbEntity<List<TestInvitationModel>> getInvitationListBySite(
            Integer siteId, Integer state, Integer page, Integer pageSize) {
        return kbRes.ok(invitationService
                .getInvitationPageBySite(siteId, state, page, pageSize)
                .map(TestInvitationServiceController::invitation2model));
    }

    private static TestInvitationModel invitation2model(TestInvitation invitation) {
        TestInvitationModel model = Mapper.map(invitation, TestInvitationModel.class);
        model.setCode(TestInvitationService.seq2code(invitation.getSeq()));
        return model;
    }

}
