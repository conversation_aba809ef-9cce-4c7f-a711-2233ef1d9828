package com.kaiba.m.core.controller;

import com.kaiba.lib.base.assembler.Mapper;
import com.kaiba.lib.base.domain.car.CarBrandModel;
import com.kaiba.lib.base.domain.car.CarModel;
import com.kaiba.lib.base.middleware.KbRes;
import com.kaiba.lib.base.response.KbCode;
import com.kaiba.lib.base.response.KbEntity;
import com.kaiba.lib.base.service.ICarService;
import com.kaiba.m.core.domain.car.Car;
import com.kaiba.m.core.domain.car.CarBrand;
import com.kaiba.m.core.service.car.CarService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.stream.Collectors;

/**
 * author: lyux
 * date: 18-10-3
 */
@Slf4j
@RestController
public class CarServiceController implements ICarService {

    private final KbRes kbRes;
    private final CarService carService;

    @Autowired
    public CarServiceController(KbRes kbRes, CarService carService) {
        this.kbRes = kbRes;
        this.carService = carService;
    }

    @Override
    public KbEntity<CarModel> getCar(String carCode) {
        return carService.getCar(carCode)
                .map(car -> Mapper.map(car, CarModel.class))
                .map(kbRes::ok)
                .orElse(kbRes.err(KbCode.CAR_NOT_EXISTS, "car not exists: " + carCode));
    }

    @Override
    public KbEntity<List<CarModel>> getCarListIn(String[] carCodeList) {
        List<Car> carList = (null == carCodeList || carCodeList.length == 0) ?
                carService.getCarList() : carService.getCarListIn(carCodeList);
        return kbRes.ok(carList
                .stream()
                .map(car -> Mapper.map(car, CarModel.class))
                .collect(Collectors.toList()));
    }

    @Override
    public KbEntity<List<CarModel>> getCarListByBrand(String carBrand) {
        return kbRes.ok(
                carService.getCarListByBrand(carBrand).stream()
                        .map(car -> Mapper.map(car, CarModel.class))
                        .collect(Collectors.toList()));
    }

    @Override
    public KbEntity<CarBrandModel> getCarBrand(String brandCode) {
        return carService.getCarBrand(brandCode)
                .map(brand -> Mapper.map(brand, CarBrandModel.class))
                .map(kbRes::ok)
                .orElse(kbRes.err(KbCode.CAR_BRAND_NOT_EXISTS, "brand not exists: " + brandCode));
    }

    @Override
    public KbEntity<CarBrandModel> getCarBrandByCar(String carCode) {
        return carService.getCarBrandByCarCode(carCode)
                .map(brand -> Mapper.map(brand, CarBrandModel.class))
                .map(kbRes::ok)
                .orElse(kbRes.err(KbCode.CAR_BRAND_NOT_EXISTS, "brand by car code not exists: " + carCode));
    }

    @Override
    public KbEntity<List<CarBrandModel>> getCarBrandListIn(String[] brandCodeList) {
        List<CarBrand> brandList = (null == brandCodeList || brandCodeList.length == 0) ?
                carService.getCarBrandList() : carService.getCarBrandListIn(brandCodeList);
        return kbRes.ok(brandList
                .stream()
                .map(brand -> Mapper.map(brand, CarBrandModel.class))
                .collect(Collectors.toList()));
    }

    @Override
    public KbEntity<String> invalidateCache() {
        carService.invalidate();
        return kbRes.ok();
    }

}
