package com.kaiba.m.core.controller.education.recitation.api;

import com.kaiba.lib.base.constant.KbHeader;
import com.kaiba.lib.base.middleware.KbRes;
import com.kaiba.lib.base.response.KbEntity;
import com.kaiba.m.core.controller.education.recitation.mapper.RecitationConMapping;
import com.kaiba.m.core.model.education.recitation.dto.ExpertQueryDTO;
import com.kaiba.m.core.domain.education.recitation.Expert;
import com.kaiba.m.core.model.education.recitation.ExpertModel;
import com.kaiba.m.core.model.education.recitation.ExpertQuery;
import com.kaiba.m.core.service.education.recitation.ExpertQueryService;
import com.kaiba.m.core.util.PageUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.util.List;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.domain.Sort.Direction;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * Description: 朗诵团专家Controller
 * Author: ZM227
 * Date: 2024/8/1 16:05
 */
@Slf4j
@RestController
@RequestMapping("/api/recitation/ifu/expert/query")
@Api(tags = "朗诵团专家查询")
public class ExpertApiQueryController {

    @Resource
    private KbRes kbRes;
    @Resource
    private ExpertQueryService expertQueryService;
    @Resource
    private RecitationConMapping recitationConMapping;

    /**
     * 查询专家列表
     *
     * @param userId 当前操作开吧用户Id
     * @param query  查询条件
     * @return 专家分页数据
     */
    @PostMapping("/queryExpertList")
    @ApiOperation(value = "查询专家列表")
    public KbEntity<List<ExpertModel>> queryExpertList(
        @RequestHeader(name = KbHeader.KB_USER_ID, required = false) Integer userId,
        @RequestBody ExpertQuery query) {
        ExpertQueryDTO queryDTO = recitationConMapping.expertQueryToDTO(query);
        Pageable pageable = PageUtils.ofDefault(query.getPage(), query.getPageSize(),
            Sort.by(Direction.DESC, "createTime"));
        queryDTO.setPageable(pageable);
        Page<Expert> experts = expertQueryService.conditionQueryExperts(queryDTO);
        Page<ExpertModel> expertModels = new PageImpl<>(
            experts.getContent().stream().map(m -> recitationConMapping.domainToModel(m))
                .collect(Collectors.toList()), experts.getPageable(), experts.getTotalElements());
        return kbRes.ok(expertModels);
    }

    @PostMapping("/queryExpertDetail")
    @ApiOperation(value = "查询专家详情")
    public KbEntity<ExpertModel> queryExpertDetail(
        @RequestHeader(name = KbHeader.KB_USER_ID, required = false) Integer userId,
        @RequestParam(required = false) String expertCode) {
        Expert expert = expertQueryService.queryExpertDetail(expertCode);
        return kbRes.ok(recitationConMapping.domainToModel(expert));
    }
}
