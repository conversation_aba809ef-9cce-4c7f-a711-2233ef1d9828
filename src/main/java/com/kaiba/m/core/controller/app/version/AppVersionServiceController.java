package com.kaiba.m.core.controller.app.version;

import com.google.common.collect.Lists;
import com.kaiba.lib.base.assembler.Mapper;
import com.kaiba.lib.base.constant.appversion.AppVersionState;
import com.kaiba.lib.base.domain.appversion.AppNextVersionModel;
import com.kaiba.lib.base.domain.appversion.AppVersionCreateModel;
import com.kaiba.lib.base.domain.appversion.AppVersionModel;
import com.kaiba.lib.base.middleware.KbRes;
import com.kaiba.lib.base.response.KbCode;
import com.kaiba.lib.base.response.KbEntity;
import com.kaiba.lib.base.response.KbException;
import com.kaiba.lib.base.service.IAppVersionService;
import com.kaiba.lib.base.util.StringUtils;
import com.kaiba.m.core.domain.appversion.AppVersion;
import com.kaiba.m.core.service.appversion.AppVersionCacheService;
import com.kaiba.m.core.service.appversion.AppVersionService;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * author: lyux
 * date: 2023-05-22
 */
@Slf4j
@RestController
public class AppVersionServiceController implements IAppVersionService {

    private final KbRes kbRes;
    private final AppVersionService versionService;
    private final AppVersionCacheService cacheService;

    public AppVersionServiceController(
            KbRes kbRes,
            AppVersionService versionService,
            AppVersionCacheService cacheService
    ) {
        this.kbRes = kbRes;
        this.versionService = versionService;
        this.cacheService = cacheService;
    }

    @Override
    public KbEntity<AppVersionModel> createAppVersion(AppVersionCreateModel model) {
        AppVersion version = Mapper.map(model, AppVersion.class);
        version.setSalt(Lists.newArrayList(model.getSalt()));
        AppVersion created = versionService.createVersion(version);
        return kbRes.ok(version2model(created));
    }

    @Override
    public KbEntity<AppVersionModel> updateAppVersionData(
            String versionId, String displayUrl, String packageUrl, String content, String description) {
        AppVersion updated = versionService.updateVersionData(versionId, displayUrl, packageUrl, content, description);
        return kbRes.ok(version2model(updated));
    }

    @Override
    public KbEntity<AppVersionModel> carefullyUpdateSalt(String secret, String versionId,
        String salt) {
        if (!Objects.equals("4676184cshf2ae4f9xg32bf2e60872f6", secret)) {
            return kbRes.err(KbCode.REQUEST_PARAM_INVALID, "secret is invalid");
        }
        AppVersion updated = versionService.carefullyUpdateSalt(versionId, salt);
        return kbRes.ok(version2model(updated));
    }

    @Override
    public KbEntity<AppVersionModel> updateAppVersionState(String versionId, Integer state) {
        AppVersionState s = AppVersionState.resolveByValue(state).orElseThrow(
                () -> new KbException(KbCode.REQUEST_PARAM_INVALID, "unknown state: " + state).li());
        AppVersion updated = versionService.updateVersionState(versionId, s);
        return kbRes.ok(version2model(updated));
    }

    @Override
    public KbEntity<AppVersionModel> updateVersionMark(String versionId, String mark) {
        if (StringUtils.isEmpty(mark)) {
            mark = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd_HH:mm:ss"));
        }
        AppVersion updated = versionService.updateVersionMark(versionId, mark);
        return kbRes.ok(version2model(updated));
    }

    @Override
    public KbEntity<Void> deleteVersionById(String versionId) {
        versionService.deleteVersionById(versionId);
        return kbRes.ok();
    }

    @Override
    public KbEntity<AppVersionModel> getVersionById(String versionId) {
        return versionService.getVersionById(versionId)
                .map(AppVersionServiceController::version2model)
                .map(kbRes::ok)
                .orElseGet(() -> kbRes.err(KbCode.RESOURCE_NOT_FOUND));
    }

    @Override
    public KbEntity<AppVersionModel> getVersionByPackageIdAndCode(String packageId, Integer code) {
        return versionService.getVersionByPackageIdAndCode(packageId, code)
                .map(AppVersionServiceController::version2model)
                .map(kbRes::ok)
                .orElseGet(() -> kbRes.err(KbCode.RESOURCE_NOT_FOUND));
    }

    @Override
    public KbEntity<List<AppVersionModel>> getVersionListByPackageId(
            String packageId, Integer[] states, Integer page, Integer pageSize) {
        return kbRes.ok(versionService.getVersionPageByPackageId(packageId, states, page, pageSize)
                .map(AppVersionServiceController::version2model));
    }

    @Override
    public KbEntity<AppNextVersionModel> getNextVersionByPackageIdAndCode(String packageId, Integer code) {
        return kbRes.ok(cacheService.getNextVersionFromDB(packageId, code));
    }

    // ----------------------------------------------------------

    private static AppVersionModel version2model(AppVersion version) {
        return Mapper.map(version, AppVersionModel.class);
    }

}
