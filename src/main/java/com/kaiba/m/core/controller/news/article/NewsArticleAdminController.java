package com.kaiba.m.core.controller.news.article;

import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.kaiba.lib.base.constant.KbHeader;
import com.kaiba.lib.base.constant.fuse.media.MediaAssetState;
import com.kaiba.lib.base.constant.news.article.ArticleUpdateTarget;
import com.kaiba.lib.base.constant.news.article.NewsState;
import com.kaiba.lib.base.domain.AttrUpdater;
import com.kaiba.lib.base.domain.common.ActionLink;
import com.kaiba.lib.base.domain.common.ShareModel;
import com.kaiba.lib.base.domain.fuse.media.MediaAssetCallbackBizNotifyModel;
import com.kaiba.lib.base.domain.news.article.*;
import com.kaiba.lib.base.domain.news.pool.bygroup.DisplayConfigUpdater;
import com.kaiba.lib.base.domain.news.pool.bygroup.GroupCreateModel;
import com.kaiba.lib.base.domain.news.pool.bygroup.GroupModel;
import com.kaiba.lib.base.domain.news.pool.bygroup.UpdateOrderModel;
import com.kaiba.lib.base.middleware.KbRes;
import com.kaiba.lib.base.response.KbCode;
import com.kaiba.lib.base.response.KbEntity;
import com.kaiba.lib.base.response.KbException;
import com.kaiba.lib.base.service.INeoNewsArticleService;
import com.kaiba.lib.base.service.INeoNewsByGroupService;
import com.kaiba.m.core.domain.news.article.NewsArticle;
import com.kaiba.m.core.domain.news.pool.bygroup.IdsGroup;
import com.kaiba.m.core.service.news.article.NewsArticleCacheService;
import com.kaiba.m.core.service.news.article.NewsArticleService;
import com.kaiba.m.core.service.news.pool.bygroup.NewsGroupService;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Objects;

/**
 * author: lyux
 * date: 2023-07-28
 */
@Slf4j
@RestController
@RequestMapping("/admin/NeoNews")
public class NewsArticleAdminController {

    private final KbRes kbRes;
    private final NewsArticleService articleInternalService;
    private final NewsArticleCacheService articleCacheService;
    private final NewsGroupService groupInternalService;
    private final INeoNewsArticleService articleService;
    private final INeoNewsByGroupService groupService;

    public NewsArticleAdminController(
            KbRes kbRes,
            NewsArticleService articleInternalService,
            NewsArticleCacheService articleCacheService,
            NewsGroupService groupInternalService,
            INeoNewsArticleService articleService,
            INeoNewsByGroupService groupService
    ) {
        this.kbRes = kbRes;
        this.articleInternalService = articleInternalService;
        this.articleCacheService = articleCacheService;
        this.groupInternalService = groupInternalService;
        this.articleService = articleService;
        this.groupService = groupService;
    }

    @PostMapping("/article/usr/createArticle")
    public KbEntity<ArticleModel> createArticle(
            @RequestHeader(KbHeader.KB_USER_ID) Integer userId,
            @RequestBody() ArticleCreateModel model
    ) {
        return articleService.createArticle(userId, model);
    }

    @PostMapping("/article/usr/updateArticleData")
    public KbEntity<ArticleModel> updateArticleData(
            @RequestBody() ArticleUpdateModel model
    ) {
        return articleService.updateArticleData(model);
    }

    @ApiOperation("指定更新一个或一组字段")
    @PostMapping("/article/usr/updateArticleItem")
    public KbEntity<ArticleModel> updateArticleItem(
            @RequestBody() ArticleUpdateModel model
    ) {
        return articleService.updateArticleItem(model);
    }

    @PostMapping("/article/usr/updateArticleAttr")
    public KbEntity<ArticleModel> updateArticleAttr(
            @RequestBody AttrUpdater attrUpdater) {
        return articleService.updateArticleAttr(attrUpdater);
    }

    @PostMapping("/article/usr/updateArticleAttrKeyValue")
    public KbEntity<ArticleModel> updateArticleAttrKeyValue(
            @RequestParam()  String articleId,
            @RequestParam() String key,
            @RequestParam() String value
    ) {
        return articleService.updateArticleAttrKeyValue(articleId, key, value);
    }

    @PostMapping("/article/usr/removeArticleAttrByKey")
    public KbEntity<ArticleModel> removeArticleAttrByKey(
            @RequestParam() String articleId,
            @RequestParam() String key
    ) {
        return articleService.removeArticleAttrByKey(articleId, key);
    }

    @PostMapping("/article/usr/updateArticleState")
    public KbEntity<ArticleModel> updateArticleState(
            @RequestParam() String articleId,
            @RequestParam() String state
    ) {
        return articleService.updateArticleState(articleId, state);
    }

    @ApiOperation("设置文章所属的频率频道")
    @PostMapping("/article/usr/updateArticleChannel")
    public KbEntity<Void> updateArticleChannel(
            @RequestParam() String articleId,
            @RequestParam() @ApiParam("频率频道标识") String channelKey,
            @RequestParam(required = false) @ApiParam("下属部门标识") String departKey
    ) {
        return articleService.updateArticleChannel(articleId, channelKey, departKey);
    }

    @ApiOperation("用户触发文章交互 (点赞等)")
    @PostMapping("/article/usr/doArticleReact")
    KbEntity<Long> doArticleReact(
            @RequestHeader(KbHeader.KB_USER_ID) Integer userId,
            @RequestParam() String articleId
    ) {
        return articleService.doArticleReact(userId, articleId);
    }

    @ApiOperation("用户取消文章交互 (点赞等)")
    @PostMapping("/article/usr/cancelArticleReact")
    KbEntity<Long> cancelArticleReact(
            @RequestHeader(KbHeader.KB_USER_ID) Integer userId,
            @RequestParam() String articleId
    ) {
        return articleService.cancelArticleReact(userId, articleId);
    }

    @PostMapping("/article/obj/getOrCreateArticleThread")
    KbEntity<String> getOrCreateArticleThread(
            @RequestParam() String articleId
    ) {
        return articleService.getOrCreateArticleThread(articleId);
    }

    @PostMapping("/group/usr/createGroup")
    public KbEntity<GroupModel> createGroup(
            @RequestParam() String group,
            @RequestParam() Integer siteId,
            @RequestParam(required = false) String channelKey,
            @RequestParam() String name,
            @RequestParam(required = false) String desc,
            @RequestParam(required = false) String renderer,
            @RequestParam(required = false) String style,
            @RequestParam(required = false) Integer max
    ) {
        return groupService.createGroup(group, siteId, channelKey, name, desc, renderer, style, max);
    }

    @PostMapping("/group/usr/createGroupByBody")
    public KbEntity<GroupModel> createGroupByBody(
            @RequestBody() GroupCreateModel model
    ) {
        return groupService.createGroupByBody(model);
    }

    @PostMapping("/group/usr/updateGroupChannel")
    public KbEntity<GroupModel> updateGroupChannel(
            @RequestParam() String groupId,
            @RequestParam() String channelKey
    ) {
        return groupService.updateGroupChannel(groupId, channelKey);
    }

    @ApiOperation("修改分组基础信息")
    @PostMapping("/group/usr/updateGroupInfo")
    public KbEntity<GroupModel> updateGroupInfo(
            @RequestParam() String groupId,
            @RequestParam() String name,
            @RequestParam(required = false) @ApiParam("空值表示清空该字段") String desc
    ) {
        return groupService.updateGroupInfo(groupId, name, desc);
    }

    @ApiOperation("设置文章分组 key 值. 注意: 修改该字段可能会影响业务逻辑, 应提示用户谨慎修改")
    @PostMapping("/group/usr/updateGroupKey")
    public KbEntity<GroupModel> updateGroupKey(
            @RequestParam() String groupId,
            @RequestParam() String groupKey
    ) {
        return groupService.updateGroupKey(groupId, groupKey);
    }

    @ApiOperation("修改分组下文章的默认渲染类型")
    @PostMapping("/group/usr/updateGroupRenderer")
    public KbEntity<GroupModel> updateGroupRenderer(
            @RequestParam() String groupId,
            @RequestParam() String renderer
    ) {
        return groupService.updateGroupRenderer(groupId, renderer);
    }

    @ApiOperation("设置分组下的文章最大值")
    @PostMapping("/group/usr/updateGroupMax")
    public KbEntity<GroupModel> updateGroupMax(
            @RequestParam() String groupId,
            @RequestParam() Integer max
    ) {
        return groupService.updateGroupMax(groupId, max);
    }

    @ApiOperation("设置分组下的文章列表样式")
    @PostMapping("/group/usr/updateGroupStyle")
    public KbEntity<GroupModel> updateGroupStyle(
            @RequestParam() String groupId,
            @RequestParam() String style
    ) {
        return groupService.updateGroupStyle(groupId, style);
    }

    @ApiOperation("设置分组内文章的显示配置")
    @PostMapping( "/group/usr/updateGroupOnCreateDisplayConfig")
    public KbEntity<Void> updateGroupOnCreateDisplayConfig(
            @RequestBody() DisplayConfigUpdater updater
    ) {
        return groupService.updateGroupOnCreateDisplayConfig(updater);
    }

    @ApiOperation("设置分组内文章的显示配置")
    @PostMapping("/group/usr/updateGroupOnAbsentDisplayConfig")
    public KbEntity<Void> updateGroupOnAbsentDisplayConfig(
            @RequestBody() DisplayConfigUpdater updater
    ) {
        return groupService.updateGroupOnAbsentDisplayConfig(updater);
    }

    @ApiOperation("设置分组内文章的显示配置")
    @PostMapping("/group/usr/updateGroupOnFetchDisplayConfig")
    public KbEntity<Void> updateGroupOnFetchDisplayConfig(
            @RequestBody() DisplayConfigUpdater updater
    ) {
        return groupService.updateGroupOnFetchDisplayConfig(updater);
    }

    @ApiOperation("指定排序方案, 以及排序算法依赖的时间, 支持文章的创建时间/签发时间/上线时间")
    @PostMapping("/group/usr/updateGroupSort")
    public KbEntity<GroupModel> updateGroupSort(
            @RequestParam() String groupId,
            @RequestParam() String sortMode,
            @RequestParam() String timeType
    ) {
        return groupService.updateGroupSort(groupId, sortMode, timeType);
    }

    @ApiOperation("设置分组的缓存策略参数")
    @PostMapping("/group/usr/updateGroupCacheConfig")
    public KbEntity<GroupModel> updateGroupCacheConfig(
            @RequestParam() String groupId,
            @RequestParam(required = false) @ApiParam("为空表示恢复默认") String cacheMode,
            @RequestParam(required = false) @ApiParam("为空表示恢复默认") Integer cacheCount
    ) {
        return groupService.updateGroupCacheConfig(groupId, cacheMode, cacheCount);
    }

    @ApiOperation("指定前端文章列表中展示的时间字段类型, 以及展示时间的格式")
    @PostMapping("/group/usr/updateGroupDisplayTimeType")
    public KbEntity<GroupModel> updateGroupDisplayTimeType(
            @RequestParam() String groupId,
            @RequestParam(required = false) String timeType,
            @RequestParam(required = false) String timeFormat
    ) {
        return groupService.updateGroupDisplayTimeType(groupId, timeType, timeFormat);
    }

    @ApiOperation("删除分组. 只有当分组内没有关联任何文章时, 分组才可以被删除.")
    @PostMapping("/group/usr/deleteGroupById")
    public KbEntity<Void> deleteGroupById(String groupId) {
        return groupService.deleteGroup(groupId);
    }

    @ApiOperation("将文章添加至分组")
    @PostMapping("/group/usr/addArticleToGroup")
    public KbEntity<Void> addArticleToGroup(
            @RequestParam() String articleId,
            @RequestParam() String group,
            @RequestParam(required = false) @ApiParam("人工指定的排序, 可为空") Long idx
    ) {
        NewsArticle legacy = articleInternalService.getArticleById(articleId).orElseThrow(
                () -> new KbException(KbCode.RESOURCE_NOT_FOUND).r("文章不存在").li());
        IdsGroup grp = groupInternalService.getGroup(null, group).orElseThrow(
                () -> new KbException(KbCode.RESOURCE_NOT_FOUND).r("分组不存在").li());
        groupInternalService.upsertArticleToGroup(legacy, grp, idx);
        if (legacy.getGroups() == null || !legacy.getGroups().contains(group)) {
            articleInternalService.addGroupToArticle(legacy, group);
            articleCacheService.invalidateSummaryCache(articleId);
        }
        return kbRes.ok();
    }

    @ApiOperation("将文章从分组中移除")
    @PostMapping("/group/usr/removeArticleFromGroup")
    public KbEntity<Void> removeArticleFromGroup(
            @RequestParam() String articleId,
            @RequestParam() String group
    ) {
        NewsArticle legacy = articleInternalService.getArticleById(articleId).orElseThrow(
                () -> new KbException(KbCode.RESOURCE_NOT_FOUND).r("文章不存在").li());
        groupInternalService.removeArticleFromGroup(articleId, group);
        if (legacy.getGroups() != null && legacy.getGroups().contains(group)) {
            articleInternalService.removeGroupFromArticle(legacy, group);
            articleCacheService.invalidateSummaryCache(articleId);
        }
        return kbRes.ok();
    }

    @ApiOperation("批量变更文章分组")
    @PostMapping("/group/usr/updateArticleGroups")
    public KbEntity<Void> updateArticleGroups(
            @RequestParam() String articleId,
            @RequestParam() @ApiParam("修改后的文章分组设置") List<String> groups
    ) {
        NewsArticle legacy = articleInternalService.getArticleById(articleId).orElseThrow(
                () -> new KbException(KbCode.RESOURCE_NOT_FOUND).r("文章不存在").li());
        if (NewsState.ONLINE.name().equals(legacy.getState())) {
            groupService.updateArticleGroups(articleId, groups).check();
        }
        return articleService.updateArticleGroups(articleId, groups).map(data -> null);
    }

    @ApiOperation("置顶排序: 将文章置顶")
    @PostMapping("/group/usr/setArticleAsTop")
    public KbEntity<Void> setArticleAsTop(
            @RequestParam() String articleId,
            @RequestParam() String group,
            @RequestParam(required = false, defaultValue = "999") @ApiParam("人工指定的排序, 可为空") Long idx
    ) {
        return groupService.setArticleAsTop(articleId, group, idx);
    }

    @ApiOperation("置顶排序: 取消文章置顶")
    @PostMapping("/group/usr/cancelArticleAsTop")
    public KbEntity<Void> cancelArticleAsTop(
            @RequestParam() String articleId,
            @RequestParam() String group
    ) {
        return groupService.cancelArticleAsTop(articleId, group);
    }

    @ApiOperation("置顶排序: 批量设置排序")
    @PostMapping("/group/usr/bulkUpdateArticleOrder")
    public KbEntity<Void> bulkUpdateArticleOrder(
            @RequestBody() UpdateOrderModel model
    ) {
        return groupService.bulkUpdateArticleOrder(model);
    }

    @ApiOperation("批次排序: 设置批次和顺序")
    @PostMapping("/group/usr/setArticleBatchAndIdx")
    public KbEntity<Void> setArticleBatchAndIdx(
            @RequestParam() String articleId,
            @RequestParam() String group,
            @RequestParam() @ApiParam("批次") Long batch,
            @RequestParam() @ApiParam("批次内的排序") Long batchIdx
    ) {
        return groupService.setArticleBatchAndIdx(articleId, group, batch, batchIdx);
    }

    @ApiOperation("重制文章分组关联")
    @PostMapping("/group/usr/resetArticleToGroup")
    public KbEntity<Void> resetArticleToGroup(
            @RequestParam(required = false, defaultValue = "true") Boolean articleOnlineAuto,
            @RequestBody UpdateOrderModel model
    ) {
        groupInternalService.resetGroupArticle(model, articleOnlineAuto);
        return kbRes.ok();
    }

    @PostMapping("/group/obj/getGroupByIdOrKey")
    public KbEntity<GroupModel> getGroupByIdOrKey(
            @RequestParam(required = false) String groupId,
            @RequestParam(required = false) String group
    ) {
        return groupService.getGroup(groupId, group);
    }

    @PostMapping("/group/obj/getGroupList")
    public KbEntity<List<GroupModel>> getGroupList(
            @RequestParam(required = false) Integer siteId,
            @RequestParam(required = false) String channelKey,
            @RequestParam(required = false, defaultValue = "1") Integer page,
            @RequestParam(required = false, defaultValue = "20") Integer pageSize
    ) {
        return groupService.getGroupList(siteId, channelKey, page, pageSize);
    }

    @PostMapping("/group/obj/getGroupListByIdIn")
    public KbEntity<List<GroupModel>> getGroupListByIdIn(
            @RequestParam() String[] groupIds
    ) {
        return groupService.getGroupListByIdIn(groupIds);
    }

    @PostMapping("/group/obj/getGroupListByKeyIn")
    public KbEntity<List<GroupModel>> getGroupListByKeyIn(
            @RequestParam() String[] groupKeys
    ) {
        return groupService.getGroupListByKeyIn(groupKeys);
    }

    @ApiOperation("根据分组名称匹配获取分组列表, 匹配规则为'包含关键词'")
    @PostMapping("/group/obj/searchGroupListByName")
    public KbEntity<List<GroupModel>> searchGroupListByName(
            @RequestParam(required = false) Integer siteId,
            @RequestParam(required = false) String channelKey,
            @RequestParam() String groupName,
            @RequestParam(required = false, defaultValue = "1") Integer page,
            @RequestParam(required = false, defaultValue = "20") Integer pageSize
    ) {
        return groupService.searchGroupInstanceListByName(siteId, channelKey, groupName, page, pageSize);
    }

    @PostMapping("/article/obj/getArticleById")
    public KbEntity<ArticleModel> getArticleById(
            @RequestParam() String articleId,
            @RequestParam(required = false, defaultValue = "true") Boolean withContent,
            @RequestParam(required = false, defaultValue = "false") Boolean withGroup
    ) {
        return articleService.getArticleById(articleId, withContent, withGroup);
    }

    @PostMapping("/article/obj/getArticleShareById")
    public KbEntity<ShareModel> getArticleShareById(
            @RequestParam() String articleId,
            @RequestParam(required = false) String group
    ) {
        return articleService.getArticleShareById(articleId, group);
    }

    @PostMapping("/article/obj/getArticleActionById")
    public KbEntity<ActionLink> getArticleActionById(
            @RequestParam() String articleId,
            @RequestParam(required = false) String group
    ) {
        return articleService.getArticleActionById(articleId, group);
    }

    @PostMapping("/article/obj/getArticleUrlById")
    public KbEntity<String> getArticleUrlById(
            @RequestParam() String articleId,
            @RequestParam(required = false) String group
    ) {
        return articleService.getArticleUrlById(articleId, group);
    }

    @PostMapping("/article/obj/getArticleListByIdIn")
    public KbEntity<List<ArticleModel>> getArticleListByIdIn(
            @RequestParam() String[] articleIds,
            @RequestParam(required = false, defaultValue = "false") Boolean withContent
    ) {
        return articleService.getArticleListByIdIn(articleIds, withContent);
    }

    @PostMapping("/article/obj/getArticleListByQuery")
    public KbEntity<List<ArticleModel>> getArticleListByQuery(
            @RequestBody() ArticleQueryModel query
    ) {
        return articleService.getArticleListByQuery(query);
    }

    @PostMapping("/article/obj/getArticleListByGroup")
    public KbEntity<List<ArticleModel>> getArticleListByGroup(
            @RequestParam() String group,
            @RequestParam(required = false) @ApiParam("大于0可认为有人工干预排序") Long idxMin,
            @RequestParam(required = false) @ApiParam("批次") Long batch,
            @RequestParam(required = false, defaultValue = "1") Integer page,
            @RequestParam(required = false, defaultValue = "20") Integer pageSize,
            @RequestParam(required = false, defaultValue = "false") Boolean withContent,
            @RequestParam(required = false, defaultValue = "true") Boolean withStat
    ) {
        return groupService.getArticleListByGroup(
                group, idxMin, batch, page, pageSize, withContent, withStat, withStat);
    }

    @PostMapping("/article/usr/attachQuickReplyCategoryToArticle")
    KbEntity<Void> attachQuickReplyCategoryToNews(
            @RequestParam() String articleId,
            @RequestParam() String categoryId
    ) {
        return articleService.attachQuickReplyCategoryToNews(articleId, categoryId);
    }

    @PostMapping("/article/usr/removeQuickReplyCategoryFromArticle")
    KbEntity<Void> removeQuickReplyCategoryFromArticle(
            @RequestParam() String articleId
    ) {
        return articleService.removeQuickReplyCategoryFromArticle(articleId);
    }

    @PostMapping("/article/usr/pushNewsArticle")
    public KbEntity<Void> pushNewsArticle(
        @RequestHeader(KbHeader.KB_USER_ID) Integer userId,
        @RequestParam() Integer siteId,
        @RequestParam() String articleId,
        @RequestParam(required = false) String group,
        @RequestParam(required = false) String title,
        @RequestParam(required = false) String subTitle
    ) {
        return articleService.pushNewsArticle(userId, siteId, articleId, group, title, subTitle);
    }


    @RequestMapping(value = "/article/callback/media")
    public KbEntity<Void> callbackMedia(
            @RequestBody() MediaAssetCallbackBizNotifyModel notify
    ) {
        if (notify.getSourceContext() == null) {
            log.info("media asset callback handle fail, sourceContext is null. body:{}", notify);
            return kbRes.ok();
        }
        ArticleModel articleModel;
        String fromType = "";
        // 兼容下sourceContext是json的情况
        try {
            JsonObject sourceContext = JsonParser.parseString(notify.getSourceContext()).getAsJsonObject();
            articleModel = articleService.getArticleById(
                sourceContext.has("articleId") ? sourceContext.get("articleId").getAsString() : "",
                true, false).dataOrThrow();
            articleModel.setState(
                sourceContext.has("state") ? sourceContext.get("state").getAsString() : "");
            fromType = sourceContext.has("fromType") ? sourceContext.get("fromType").getAsString() : "";
        } catch (KbException e) {
            throw e;
        } catch (Exception ignore) {
            articleModel = articleService.getArticleById(notify.getSourceContext(), true, false).dataOrThrow();
        }
        // 只处理正常状态，异常状态葫芦网需要上线
        if (!Objects.equals(notify.getState(), MediaAssetState.NORMAL.name())
            && !Objects.equals(notify.getState(), MediaAssetState.TRANSCODING.name())) {
            if (Objects.equals(notify.getState(), MediaAssetState.EXCEPTION.name()) && Objects.equals(fromType, "hooloAuto")) {
                // 葫芦网自动同步资讯，即使失败也需要变更状态
                if (StringUtils.isNotBlank(articleModel.getState())) {
                    articleService.updateArticleState(articleModel.getId(), articleModel.getState());
                }
            }
            return kbRes.ok();
        }
        if (notify.getPlayInfo() == null) {
            log.info("media asset callback handle fail, playInfo is null. body:{}", notify);
            return kbRes.ok();
        }
        if (articleModel.getVideo() == null) {
            log.info("media asset callback handle fail, video is null. body:{}", notify);
            return kbRes.ok();
        }
        ArticleVideo video = articleModel.getVideo();
        video.setMediaId(notify.getPlayInfo().getMediaId());
        video.setVideoUrl(notify.getPlayInfo().getVideoUrl());
        video.setDuration((int)Float.parseFloat(notify.getDuration()));
        video.setHeight(notify.getPlayInfo().getHeightPixel().intValue());
        video.setWidth(notify.getPlayInfo().getWidthPixel().intValue());
        // 如果没有封面图,使用默认媒资封面
        if (video.getCoverUrl() == null) {
            video.setCoverUrl(notify.getCoverUrl());
        }
        ArticleUpdateModel model = new ArticleUpdateModel();
        model.setId(articleModel.getId());
        model.setVideo(video);
        model.setTarget(ArticleUpdateTarget.VIDEO.name());
        model.setContentType(articleModel.getContentType());
        model.setRenderer(articleModel.getRenderer());
        articleService.updateArticleItem(model);
        // 需要变更状态
        if (StringUtils.isNotBlank(articleModel.getState())) {
            articleService.updateArticleState(articleModel.getId(), articleModel.getState());
        }
        return kbRes.ok();
    }

}
