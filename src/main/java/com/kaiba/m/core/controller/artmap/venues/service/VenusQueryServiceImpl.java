package com.kaiba.m.core.controller.artmap.venues.service;

import com.kaiba.lib.base.constant.KbHeader;
import com.kaiba.lib.base.domain.artmap.VenuesModel;
import com.kaiba.lib.base.domain.artmap.VenuesQueryModel;
import com.kaiba.lib.base.middleware.KbRes;
import com.kaiba.lib.base.response.KbEntity;
import com.kaiba.lib.base.service.IVenusQueryService;
import com.kaiba.m.core.controller.artmap.mapper.ArtMapConMapping;
import com.kaiba.m.core.domain.artmap.Venues;
import com.kaiba.m.core.service.artmap.VenuesQueryService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.util.List;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * Description: 场馆查询服务实现
 * Author: ZM227
 * Date: 2025/6/17 16:53
 */
@Slf4j
@RestController
@Api(tags = "艺术地图场馆查询Service")
public class VenusQueryServiceImpl implements IVenusQueryService {

    @Resource
    private KbRes kbRes;
    @Resource
    private VenuesQueryService venuesQueryService;
    @Resource
    private ArtMapConMapping artMapConMapping;

    @PostMapping("/service/usr/artMap/query/queryVenuesByCondition")
    @ApiOperation(value = "根据条件查询场馆列表")
    @Override
    public KbEntity<List<VenuesModel>> queryVenuesByCondition(Integer userId, VenuesQueryModel venuesQuery) {
        Page<Venues> venues = venuesQueryService.findVenuesByCondition(
            artMapConMapping.venuesModelToQueryDTO(venuesQuery));
        Page<VenuesModel> venuesModels = new PageImpl<>(
            venues.getContent().stream().map(m -> artMapConMapping.venuesDomainToModel(m))
                .collect(Collectors.toList()), venues.getPageable(), venues.getTotalElements());
        return kbRes.ok(venuesModels);
    }
}
