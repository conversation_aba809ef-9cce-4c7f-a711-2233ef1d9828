package com.kaiba.m.core.controller;

import com.alibaba.cloud.nacos.NacosConfigManager;
import com.alibaba.nacos.api.exception.NacosException;
import com.google.gson.reflect.TypeToken;
import com.kaiba.lib.base.domain.sentinel.DegradeModel;
import com.kaiba.lib.base.service.ISentinelRuleService;
import com.kaiba.m.core.util.JsonUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.context.WebApplicationContext;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.mvc.method.RequestMappingInfo;
import org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;


@RestController
public class SentinelRuleServiceController implements ISentinelRuleService {

    private final WebApplicationContext applicationContext;
    private final NacosConfigManager nacosConfigManager;

    @Value("${spring.cloud.sentinel.datasource.ds1.nacos.data-id}")
    private String degradeNacosDataId;

    public SentinelRuleServiceController(
            WebApplicationContext applicationContext,
            NacosConfigManager nacosConfigManager
    ) {
        this.applicationContext = applicationContext;
        this.nacosConfigManager = nacosConfigManager;
    }

    @Override
    public List<String> getHttpUrls() {
        String pckName = this.getClass().getPackage().getName();
        RequestMappingHandlerMapping requestMappingHandlerMapping = applicationContext
                .getBean("requestMappingHandlerMapping", RequestMappingHandlerMapping.class);
        Map<RequestMappingInfo, HandlerMethod> map = requestMappingHandlerMapping.getHandlerMethods();
        List<String> urls = new ArrayList<>();
        map.forEach((k, v) -> {
            Package pck = v.getBeanType().getPackage();
            if (pck.getName().contains(pckName)) {
                urls.addAll(k.getPatternsCondition().getPatterns());
            }
        });
        return urls;
    }

    @Override
    public  List<DegradeModel> getDegradeConfigFromNacos() {
        String str;
        try {
            str = nacosConfigManager.getConfigService().getConfig(degradeNacosDataId, null, 2000);
        } catch (NacosException e) {
            throw new RuntimeException(e);
        }
        return JsonUtils.getGson().fromJson(str, new TypeToken<List<DegradeModel>>() {}.getType());
    }

    @Override
    public  void publishDegradeConfigToNacos(List<DegradeModel> degradeModels) {
        try {
            nacosConfigManager.getConfigService().publishConfig(degradeNacosDataId, null, JsonUtils.getGson().toJson(degradeModels));
        } catch (NacosException e) {
            throw new RuntimeException(e);
        }
    }
}
