package com.kaiba.m.core.controller.news.article;

import com.kaiba.lib.base.assembler.Mapper;
import com.kaiba.lib.base.constant.KbEndpoint;
import com.kaiba.lib.base.constant.KbProperties;
import com.kaiba.lib.base.constant.news.article.ArticleUpdateTarget;
import com.kaiba.lib.base.constant.news.article.NewsState;
import com.kaiba.lib.base.constant.note.NoteThreadCondition;
import com.kaiba.lib.base.constant.push.PushRange;
import com.kaiba.lib.base.constant.push.PushType;
import com.kaiba.lib.base.domain.AttrUpdater;
import com.kaiba.lib.base.domain.common.ActionLink;
import com.kaiba.lib.base.domain.common.ShareModel;
import com.kaiba.lib.base.domain.da.sensors.channel.SensorsChannelModel;
import com.kaiba.lib.base.domain.news.article.*;
import com.kaiba.lib.base.domain.news.pool.bygroup.GroupModel;
import com.kaiba.lib.base.domain.note.NoteThreadModel;
import com.kaiba.lib.base.middleware.KbRes;
import com.kaiba.lib.base.response.KbCode;
import com.kaiba.lib.base.response.KbEntity;
import com.kaiba.lib.base.response.KbException;
import com.kaiba.lib.base.service.*;
import com.kaiba.lib.base.util.StringUtils;
import com.kaiba.lib.base.util.appaction.AppActionType;
import com.kaiba.m.core.domain.news.article.NewsArticle;
import com.kaiba.m.core.domain.news.article.NewsModuleConfig;
import com.kaiba.m.core.domain.news.article.NewsSiteConfig;
import com.kaiba.m.core.domain.news.pool.bygroup.IdsGroup;
import com.kaiba.m.core.middleware.DistributedLock;
import com.kaiba.m.core.service.news.article.*;
import com.kaiba.m.core.service.news.pool.bygroup.NewsGroupApiService;
import com.kaiba.m.core.service.news.pool.bygroup.NewsGroupService;
import com.kaiba.m.core.util.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.*;
import java.util.stream.Collectors;

/**
 * author: lyux
 * date: 2023-07-31
 */
@Slf4j
@RestController
public class NewsArticleServiceController implements INeoNewsArticleService {

    private final KbRes kbRes;
    private final NewsArticleService articleService;
    private final NewsArticleCacheService cacheService;
    private final NewsReactCacheService reactCacheService;
    private final NewsConfigService configService;
    private final NewsGroupService groupService;
    private final NewsGroupApiService groupApiService;
    private final INoteService noteService;
    private final StringRedisTemplate redisTemplate;
    private final ISensorsService sensorsService;
    private final ISensorsChannelService channelService;
    private final IPushService pushService;
    private final String pageHost;

    public NewsArticleServiceController(
        KbRes kbRes,
        NewsArticleService articleService,
        NewsArticleCacheService cacheService,
        NewsReactCacheService reactCacheService,
        NewsConfigService configService,
        NewsGroupService groupService,
        NewsGroupApiService groupApiService,
        INoteService noteService,
        StringRedisTemplate redisTemplate,
        ISensorsService sensorsService,
        ISensorsChannelService channelService,
        IPushService pushService,
        @Value("${kb.host.page}") String pageHost
    ) {
        this.kbRes = kbRes;
        this.articleService = articleService;
        this.cacheService = cacheService;
        this.reactCacheService = reactCacheService;
        this.configService = configService;
        this.groupService = groupService;
        this.groupApiService = groupApiService;
        this.noteService = noteService;
        this.redisTemplate = redisTemplate;
        this.sensorsService = sensorsService;
        this.channelService = channelService;
        this.pushService = pushService;
        this.pageHost = pageHost;
    }

    @Override
    public KbEntity<ArticleModel> createArticle(Integer userId, ArticleCreateModel model) {
        NewsArticle article = Mapper.map(model, NewsArticle.class);
        IdsGroup group = null;
        if (model.getGroup() != null) {
            group = groupService.getGroup(null, model.getGroup())
                .orElseThrow(() -> new KbException(KbCode.RESOURCE_NOT_FOUND).li().r("分组不存在"));
            article.setGroups(Collections.singleton(model.getGroup()));
            if (group.getDcOnCreate() != null) {
                if ((article.getCovers() == null || article.getCovers().isEmpty())
                    && group.getDcOnCreate().getCover() != null) {
                    article.setCovers(Collections.singletonList(group.getDcOnCreate().getCover()));
                }
                if (article.getViewStyle() == null
                    && group.getDcOnCreate().getViewStyle() != null) {
                    article.setViewStyle(group.getDcOnCreate().getViewStyle());
                }
                if (article.getReactStyle() == null
                    && group.getDcOnCreate().getReactStyle() != null) {
                    article.setReactStyle(group.getDcOnCreate().getReactStyle());
                }
                if (article.getReplyStyle() == null
                    && group.getDcOnCreate().getReplyStyle() != null) {
                    article.setReplyStyle(group.getDcOnCreate().getReplyStyle());
                }
                if (article.getShare() == null && !group.getDcOnCreate().isShareEnabled()) {
                    article.setShare(ShareModel.createDisabledShare());
                }
            }
        }
        NewsArticle created = articleService.createArticle(userId, article);
        if (group != null && NewsState.ONLINE.name().equals(created.getState())) {
            groupService.upsertArticleToGroup(created, group, null);
        }
        return kbRes.ok(Mapper.map(created, ArticleModel.class));
    }

    @Override
    public KbEntity<ArticleModel> updateArticleData(ArticleUpdateModel model) {
        NewsArticle legacy = articleService.getArticleById(model.getId()).orElseThrow(
            () -> new KbException(KbCode.RESOURCE_NOT_FOUND).r("文章不存在").li());
        boolean contentUpdated = !Objects.equals(legacy.getContent(), model.getContent());
        NewsArticle article = articleService.updateArticleData(legacy, model);
        cacheService.invalidateSummaryCache(article.getId());
        if (contentUpdated) {
            cacheService.invalidateContentCache(article.getId());
        }
        return kbRes.ok(Mapper.map(article, ArticleModel.class));
    }

    @Override
    public KbEntity<ArticleModel> updateArticleItem(ArticleUpdateModel model) {
        ArticleUpdateTarget target = ArticleUpdateTarget.resolveByName(model.getTarget()).orElseThrow(
                () -> new KbException(KbCode.REQUEST_PARAM_INVALID, "unknown target").r("未知的更新目标").li());
        NewsArticle legacy = articleService.getArticleById(model.getId()).orElseThrow(
                () -> new KbException(KbCode.RESOURCE_NOT_FOUND).r("文章不存在").li());
        boolean contentUpdated = !Objects.equals(legacy.getContent(), model.getContent());
        NewsArticle article = articleService.updateArticleField(legacy, model, target);
        cacheService.invalidateSummaryCache(article.getId());
        if (contentUpdated) {
            cacheService.invalidateContentCache(article.getId());
        }
        return kbRes.ok(Mapper.map(article, ArticleModel.class));
    }

    @Override
    public KbEntity<ArticleModel> updateArticleAttr(AttrUpdater attrUpdater) {
        if (attrUpdater.isAttrEmpty()) {
            return kbRes.err(KbCode.REQUEST_PARAM_INVALID, "nothing to update");
        }
        NewsArticle article = articleService.updateArticleAttr(
            attrUpdater.getId(), attrUpdater.getAttr(), attrUpdater.isReplaceAttr());
        cacheService.invalidateSummaryCache(article.getId());
        return kbRes.ok(Mapper.map(article, ArticleModel.class));
    }

    @Override
    public KbEntity<ArticleModel> updateArticleAttrKeyValue(String articleId, String key,
        String value) {
        NewsArticle article = articleService.updateArticleAttrKeyValue(articleId, key, value);
        cacheService.invalidateSummaryCache(article.getId());
        return kbRes.ok(Mapper.map(article, ArticleModel.class));
    }

    @Override
    public KbEntity<ArticleModel> removeArticleAttrByKey(String articleId, String key) {
        NewsArticle article = articleService.removeArticleAttrByKey(articleId, key);
        cacheService.invalidateSummaryCache(article.getId());
        return kbRes.ok(Mapper.map(article, ArticleModel.class));
    }

    @Override
    public KbEntity<ArticleModel> updateArticleState(String articleId, String state) {
        NewsState s = NewsState.resolveByName(state).orElseThrow(
            () -> new KbException(KbCode.REQUEST_PARAM_INVALID).r("未知的状态").li());
        NewsArticle article = articleService.updateArticleState(articleId, s);
        cacheService.invalidateSummaryCache(article.getId());
        return kbRes.ok(Mapper.map(article, ArticleModel.class));
    }

    @Override
    public KbEntity<ArticleModel> addArticleToModule(String articleId, String module) {
        // module 增减暂时不必更新缓存
        articleService.addModuleToArticle(articleId, module);
        return kbRes.ok();
    }

    @Override
    public KbEntity<ArticleModel> removeArticleFromModule(String articleId, String module) {
        // module 增减暂时不必更新缓存
        articleService.removeModuleFromArticle(articleId, module);
        return kbRes.ok();
    }

    @Override
    public KbEntity<ArticleModel> addArticleGroup(String articleId, String group) {
        NewsArticle legacy = articleService.getArticleById(articleId).orElseThrow(
            () -> new KbException(KbCode.RESOURCE_NOT_FOUND).r("文章不存在").li());
        if (legacy.getGroups() == null || !legacy.getGroups().contains(group)) {
            articleService.addGroupToArticle(legacy, group);
            cacheService.invalidateSummaryCache(articleId);
        }
        return kbRes.ok();
    }

    @Override
    public KbEntity<ArticleModel> removeArticleGroup(String articleId, String group) {
        NewsArticle legacy = articleService.getArticleById(articleId).orElseThrow(
            () -> new KbException(KbCode.RESOURCE_NOT_FOUND).r("文章不存在").li());
        if (legacy.getGroups() != null && legacy.getGroups().contains(group)) {
            articleService.removeGroupFromArticle(legacy, group);
            cacheService.invalidateSummaryCache(articleId);
        }
        return kbRes.ok();
    }

    @Override
    public KbEntity<ArticleModel> updateArticleGroups(String articleId, List<String> groups) {
        articleService.updateArticleGroups(articleId, new HashSet<>(groups));
        cacheService.invalidateSummaryCache(articleId);
        return kbRes.ok();
    }

    @Override
    public KbEntity<Void> updateArticleChannel(
            String articleId, String channelKey, String departKey) {
        NewsArticle legacy = articleService.getArticleById(articleId).orElseThrow(
            () -> new KbException(KbCode.RESOURCE_NOT_FOUND).r("文章不存在").li());
        if (!Objects.equals(legacy.getChannelKey(), channelKey)
            || !Objects.equals(legacy.getDepartKey(), departKey)) {
            articleService.updateChannel(legacy, channelKey, departKey);
            cacheService.invalidateSummaryCache(articleId);
        }
        return kbRes.ok();
    }

    @Override
    public KbEntity<Long> doArticleReact(Integer userId, String articleId) {
        return kbRes.ok(reactCacheService.incr(articleId));
    }

    @Override
    public KbEntity<Long> cancelArticleReact(Integer userId, String articleId) {
        return kbRes.ok(reactCacheService.decr(articleId));
    }

    @Override
    public KbEntity<String> getOrCreateArticleThread(String articleId) {
        ArticleModel article = cacheService.getSummaryById(articleId)
            .orElseThrow(() -> new KbException(KbCode.RESOURCE_NOT_FOUND).li());
        if (article.getThreadId() == null) {
            DistributedLock lock = new DistributedLock(redisTemplate);
            String mark = "news_article_" + articleId;
            if (lock.tryGetDistributedLock(mark, 8000)) {
                String threadId;
                try {
                    String siteThreadId = cacheService.getSiteThreadIdOrThrow(article.getSiteId());
                    List<String> moduleThreadIds = cacheService
                        .getModuleListThreadIdsOrThrow(article.getSiteId(),
                            article.getModuleIndex());
                    List<String> routeThreads = new ArrayList<>(moduleThreadIds.size() + 1);
                    routeThreads.add(siteThreadId);
                    routeThreads.addAll(moduleThreadIds);
                    String[] routeThreadsArray = new String[routeThreads.size()];
                    routeThreads.toArray(routeThreadsArray);
                    NoteThreadModel thread = noteService.createThread(
                            KbProperties.SYSTEM_USER_ID, article.getSiteId(),
                            "新资讯评论-" + article.getTitle(), null,
                            0, 0, false, routeThreadsArray,
                            NoteThreadCondition.FREE.getValue())
                        .dataOrThrow();
                    threadId = thread.getId();
                    articleService.attachArticleThread(articleId, threadId);
                    cacheService.invalidateSummaryCache(articleId);
                    return kbRes.ok(threadId);
                } catch (Exception e) {
                    log.error("attach thread to article fail", e);
                    return kbRes.err(KbCode.REQUEST_FAIL);
                } finally {
                    lock.releaseDistributedLock(mark);
                }
            } else {
                return kbRes.err(KbCode.REQUEST_RETRY_LATER);
            }
        } else {
            return kbRes.ok(article.getThreadId());
        }
    }

    @Override
    public KbEntity<ArticleModel> getArticleById(String articleId, Boolean withContent, Boolean withGroup) {
        return articleService.getArticleById(articleId)
            .map(article -> Mapper.map(article, ArticleModel.class))
            .map(kbRes::ok)
            .orElseGet(() -> kbRes.err(KbCode.RESOURCE_NOT_FOUND))
            .peek(model -> {
                if (withContent == null || !withContent) {
                    model.setContent(null);
                }
                if (withGroup != null && withGroup && model.getGroups() != null && !model.getGroups().isEmpty()) {
                    Map<String, IdsGroup> groups = groupApiService.getGroupByKeyIn(model.getGroups());
                    List<GroupModel> groupList = model.getGroups().stream()
                            .map(groups::get)
                            .filter(Objects::nonNull)
                            .map(group -> {
                                GroupModel gm = new GroupModel();
                                gm.setName(group.getName());
                                gm.setKey(group.getKey());
                                gm.setChannelKey(group.getChannelKey());
                                return gm;
                            })
                            .collect(Collectors.toList());
                    model.setGroupList(groupList);
                }
                if (model.getChannelKey() != null) {
                    SensorsChannelModel channelModel = channelService.getByKeyFromCache(
                            model.getChannelKey())
                        .dataIgnoreError().orElse(null);
                    model.setChannel(channelModel);
                }
            });
    }

    @Override
    public KbEntity<ShareModel> getArticleShareById(String articleId, String group) {
        return articleService.getArticleById(articleId)
            .map(article -> Mapper.map(article, ArticleModel.class))
            .map(article -> NewsArticleModelHelper.assembleArticleShare(pageHost, article, group))
            .map(kbRes::ok)
            .orElseGet(() -> kbRes.err(KbCode.RESOURCE_NOT_FOUND,
                "article not found or share disabled", "文章不存在或分享已禁用"));
    }

    @Override
    public KbEntity<ActionLink> getArticleActionById(String articleId, String group) {
        return articleService.getArticleById(articleId)
            .map(article -> Mapper.map(article, ArticleModel.class))
            .map(article -> {
                String url = NewsArticleModelHelper
                        .assembleArticleUrl(pageHost, articleId, article.getRenderer(), group);
                if (NewsState.DRAFT.name().equals(article.getState())
                    || NewsState.ARCHIVED.name().equals(article.getState())) {
                    NewsArticleCacheService.TempToken token = cacheService.createTempVisitToken(
                        article);
                    url = url + "&tmp=" + token.getToken();
                }
                ActionLink al = new ActionLink();
                al.setId(article.getId());
                al.setTitle(article.getTitle());
                al.setSubTitle(article.getSubtitle());
                al.putActionWebParam(url, article.getTitle());
                return al;
            })
            .map(kbRes::ok)
            .orElseGet(() -> kbRes.err(KbCode.RESOURCE_NOT_FOUND,
                "article not found or share disabled", "文章不存在或分享已禁用"));
    }

    @Override
    public KbEntity<String> getArticleUrlById(String articleId, String group) {
        return articleService.getArticleById(articleId)
            .map(article -> Mapper.map(article, ArticleModel.class))
            .map(article -> {
                String url = NewsArticleModelHelper
                        .assembleArticleUrl(pageHost, articleId, article.getRenderer(), group);
                if (NewsState.DRAFT.name().equals(article.getState())
                    || NewsState.ARCHIVED.name().equals(article.getState())) {
                    NewsArticleCacheService.TempToken token = cacheService.createTempVisitToken(
                        article);
                    url = url + "&tmp=" + token.getToken();
                }
                return url;
            })
            .map(kbRes::ok)
            .orElseGet(() -> kbRes.err(KbCode.RESOURCE_NOT_FOUND,
                "article not found or share disabled", "文章不存在或分享已禁用"));
    }

    @Override
    public KbEntity<List<ArticleModel>> getArticleListByIdIn(String[] articleIds,
        Boolean withContent) {
        if (articleIds == null || articleIds.length == 0) {
            return kbRes.ok();
        } else {
            List<String> articleIdList = Arrays.asList(articleIds);
            Map<String, NewsArticle> map = articleService.getArticleMapByIdIn(articleIdList);
            List<ArticleModel> modelList = articleIdList.stream()
                .map(articleId -> {
                    NewsArticle article = map.get(articleId);
                    if (article == null) {
                        return null;
                    } else {
                        ArticleModel model = Mapper.map(article, ArticleModel.class);
                        if (withContent == null || !withContent) {
                            model.setContent(null);
                        }
                        return model;
                    }
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
            return kbRes.ok(modelList);
        }
    }

    @Override
    public KbEntity<List<ArticleModel>> getArticleListByQuery(ArticleQueryModel query) {
        return kbRes.ok(articleService.getArticlePageByQuery(query).map(article -> {
            ArticleModel model = Mapper.map(article, ArticleModel.class);
            if (query.getWithContent() == null || !query.getWithContent()) {
                model.setContent(null);
            }
            return model;
        })).peek(articles -> {
            boolean withStat = query.getWithStat() != null && query.getWithStat();
            boolean withPushCount = query.getWithPushCount() != null && query.getWithPushCount();
            if (withStat || withPushCount) {
                for (ArticleModel article : articles) {
                    if (withStat) {
                        article.setStat(NewsArticleModelHelper.createArticleStat(article, sensorsService));
                    }
                    if (withPushCount) {
                        NewsArticleModelHelper.attachPushStat(article, cacheService.getPushCount(article.getId()));
                    }
                }
            }
            if (query.getWithGroup() != null && query.getWithGroup()) {
                Set<String> groupKeys = new HashSet<>();
                for (ArticleModel article : articles) {
                    if (article.getGroups() != null && !article.getGroups().isEmpty()) {
                        groupKeys.addAll(article.getGroups());
                    }
                }
                if (!groupKeys.isEmpty()) {
                    Map<String, IdsGroup> groups = groupApiService.getGroupByKeyIn(groupKeys);
                    for (ArticleModel article : articles) {
                        if (article.getGroups() != null && !article.getGroups().isEmpty()) {
                            List<GroupModel> groupList = article.getGroups().stream()
                                .map(groups::get)
                                .filter(Objects::nonNull)
                                .map(group -> {
                                    GroupModel model = new GroupModel();
                                    model.setName(group.getName());
                                    model.setKey(group.getKey());
                                    model.setChannelKey(group.getChannelKey());
                                    return model;
                                })
                                .collect(Collectors.toList());
                            article.setGroupList(groupList);
                        }
                    }
                }
            }
        });
    }

    @Override
    public KbEntity<Void> attachQuickReplyCategoryToNews(String articleId, String categoryId) {
        NewsArticle legacy = articleService.getArticleById(articleId).orElseThrow(
                () -> new KbException(KbCode.RESOURCE_NOT_FOUND).r("文章不存在").li());
        articleService.attachQuickReplyCategoryId(legacy, categoryId);
        return kbRes.ok();
    }

    @Override
    public KbEntity<Void> removeQuickReplyCategoryFromArticle(String articleId) {
        NewsArticle legacy = articleService.getArticleById(articleId).orElseThrow(
                () -> new KbException(KbCode.RESOURCE_NOT_FOUND).r("文章不存在").li());
        articleService.removeQuickReplyCategoryId(legacy);
        return kbRes.ok();
    }

    @Override
    public KbEntity<Void> pushNewsArticle(
            Integer userId, Integer siteId, String articleId, String group,
            String title, String subTitle) {
        NewsArticle newsArticle = articleService.getArticleById(articleId)
            .orElseThrow(() -> new KbException(KbCode.RESOURCE_NOT_FOUND).li());
        Long deadLine = System.currentTimeMillis() / 1000 + 86400;
        AppActionType action = AppActionType.PAGE_NEWS_DETAIL;
        HashMap<String, String> params = new HashMap<>();

        String pushTitle = StringUtils.isEmpty(title) ? newsArticle.getTitle() : title;
        String pushSubTitle = StringUtils.isEmpty(subTitle) ? newsArticle.getSubtitle() : subTitle;
        if (Objects.equals(AppActionType.PAGE_WX_MINI_PROGRAM.name(), newsArticle.getAction())) {
            action = AppActionType.PAGE_WX_MINI_PROGRAM;
            params.put("wxMiniUserName",
                String.valueOf(newsArticle.getActionParams().get("wxMiniUserName")));
            params.put("wxMiniPath",
                String.valueOf(newsArticle.getActionParams().get("wxMiniPath")));
        } else {
            params.put("url", NewsArticleModelHelper.assembleArticleUrl(pageHost,
                articleId, newsArticle.getRenderer(), group));
            params.put("title", pushTitle);
            params.put("siteId", newsArticle.getSiteId().toString());
            params.put("pushType", "news");
            params.put("id", newsArticle.getId());
            params.put("EventObjectId", "news-" + newsArticle.getId());
            params.put("SelfObjectId", "news-" + newsArticle.getId());
            params.put("EventChannelClassName", "新闻资讯");
            params.put("EventChannelClassId", "NEWS");
        }
        cacheService.invalidPushCountCache(newsArticle.getId());
        return pushService.addAndPush(pushTitle, pushSubTitle,
            action.getAction(), JsonUtils.getGson().toJson(params),
            String.valueOf(siteId), PushType.ANNOUNCE.getValue(),
            PushRange.SITE.getValue(), newsArticle.getId(), KbEndpoint.KAIBA.getValue(), userId,
            deadLine);
    }

    @Override
    public KbEntity<NewsSiteConfigModel> createSiteConfig(Integer siteId, String orgName) {
        NewsSiteConfig config = configService.createSiteConfig(siteId, orgName);
        return kbRes.ok(Mapper.map(config, NewsSiteConfigModel.class));
    }

    @Override
    public KbEntity<NewsSiteConfigModel> updateSiteConfigOrgName(Integer siteId, String orgName) {
        NewsSiteConfig config = configService.updateSiteOrgName(siteId, orgName);
        return kbRes.ok(Mapper.map(config, NewsSiteConfigModel.class));
    }

    @Override
    public KbEntity<NewsModuleConfigModel> createModuleConfig(
        Integer siteId, String module, String desc, String orgName) {
        NewsModuleConfig config = configService.createModuleConfig(siteId, module, desc, orgName);
        return kbRes.ok(Mapper.map(config, NewsModuleConfigModel.class));
    }

    @Override
    public KbEntity<NewsModuleConfigModel> updateModuleConfig(NewsModuleConfigUpdateModel model) {
        NewsModuleConfig config = configService.updateModuleConfig(model.getId(), model.getDesc(),
            model.getOrgName());
        return kbRes.ok(Mapper.map(config, NewsModuleConfigModel.class));
    }

    @Override
    public KbEntity<NewsSiteConfigModel> getSiteConfig(Integer siteId) {
        return configService.getSiteConfig(siteId)
            .map(config -> Mapper.map(config, NewsSiteConfigModel.class))
            .map(kbRes::ok)
            .orElseGet(() -> kbRes.err(KbCode.RESOURCE_NOT_FOUND));
    }

    @Override
    public KbEntity<NewsModuleConfigModel> getModuleConfig(Integer siteId, String module) {
        return configService.getModuleConfig(siteId, module)
            .map(config -> Mapper.map(config, NewsModuleConfigModel.class))
            .map(kbRes::ok)
            .orElseGet(() -> kbRes.err(KbCode.RESOURCE_NOT_FOUND));
    }

    @Override
    public KbEntity<List<NewsModuleConfigModel>> getModuleConfigListBySite(Integer siteId) {
        // TODO
        return kbRes.err(KbCode.NOT_SUPPORT_YET);
    }

    @PostMapping("/traverseAndCreateSiteConfig")
    public KbEntity<Void> traverseAndCreateSiteConfig() {
        configService.traverseAndCreateSiteConfig();
        return kbRes.ok();
    }

}
