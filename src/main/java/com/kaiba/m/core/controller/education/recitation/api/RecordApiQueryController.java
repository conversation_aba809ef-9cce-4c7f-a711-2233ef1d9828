package com.kaiba.m.core.controller.education.recitation.api;

import com.kaiba.lib.base.constant.KbHeader;
import com.kaiba.lib.base.middleware.KbRes;
import com.kaiba.lib.base.response.KbEntity;
import com.kaiba.m.core.controller.education.recitation.mapper.RecitationConMapping;
import com.kaiba.m.core.model.education.recitation.dto.ApplyRecordQueryDTO;
import com.kaiba.m.core.domain.education.recitation.ApplyRecord;
import com.kaiba.m.core.model.education.recitation.ApplyRecordModel;
import com.kaiba.m.core.model.education.recitation.ApplyRecordQuery;
import com.kaiba.m.core.service.education.recitation.ApplyRecordQueryService;
import com.kaiba.m.core.util.PageUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.util.List;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.domain.Sort.Direction;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * Description: 报名记录查询
 * Author: ZM227
 * Date: 2024/8/13 9:37
 */
@Slf4j
@RestController
@RequestMapping("/api/recitation/usr/record/query")
@Api(tags = "报名记录查询")
public class RecordApiQueryController {

    @Resource
    private KbRes kbRes;
    @Resource
    private ApplyRecordQueryService applyRecordQueryService;
    @Resource
    private RecitationConMapping recitationConMapping;

    @PostMapping("/queryUserRecordList")
    @ApiOperation(value = "查询自己的报名记录")
    public KbEntity<List<ApplyRecordModel>> queryUserRecordList(
        @RequestHeader(name = KbHeader.KB_USER_ID) Integer userId,
        @RequestBody ApplyRecordQuery query) {
        Pageable pageable = PageUtils.ofDefault(query.getPage(), query.getPageSize(),
            Sort.by(Direction.DESC, "createTime"));
        ApplyRecordQueryDTO queryDTO = recitationConMapping.recordQueryToDTO(query);
        queryDTO.setKbUserId(userId);
        queryDTO.setPageable(pageable);
        Page<ApplyRecord> applyRecords = applyRecordQueryService.conditionQueryRecords(queryDTO);
        Page<ApplyRecordModel> applyRecordModels = new PageImpl<>(
            applyRecords.getContent().stream().map(m -> recitationConMapping.domainToModel(m))
                .collect(Collectors.toList()), applyRecords.getPageable(), applyRecords.getTotalElements());
        return kbRes.ok(applyRecordModels);
    }
}
