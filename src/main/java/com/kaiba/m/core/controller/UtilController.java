package com.kaiba.m.core.controller;

import com.kaiba.lib.base.constant.KbEndpoint;
import com.kaiba.lib.base.domain.user.UserModel;
import com.kaiba.lib.base.middleware.KbRes;
import com.kaiba.lib.base.middleware.jwt.KbJWTHandler;
import com.kaiba.lib.base.response.KbCode;
import com.kaiba.lib.base.response.KbEntity;
import com.kaiba.lib.base.response.KbException;
import com.kaiba.lib.base.rte.RelativeTimeExpression;
import com.kaiba.lib.base.service.IUserService;
import com.kaiba.lib.base.util.AESEncrypt;
import com.kaiba.lib.base.util.appaction.AbsParamValidator;
import com.kaiba.lib.base.util.appaction.AppActionType;
import io.swagger.annotations.ApiOperation;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.text.SimpleDateFormat;
import java.time.Duration;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * author: lyux
 * date: 2023-10-13
 * 调试用接口, 不对 C 端开放
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/util")
public class UtilController {

    private final KbRes kbRes;
    private final IUserService userService;
    private final KbJWTHandler jwtHandler;

    public UtilController(
            KbRes kbRes,
            IUserService userService,
            KbJWTHandler jwtHandler
    ) {
        this.kbRes = kbRes;
        this.userService = userService;
        this.jwtHandler = jwtHandler;
    }

    @PostMapping("/getAppActionTypeList")
    public KbEntity<List<AppActionDef>> getAppActionTypeList(
            @RequestParam(required = false, defaultValue = "false") Boolean showDeprecated
    ) {
        if (showDeprecated) {
            return kbRes.ok(Arrays.stream(AppActionType.values())
                    .map(AppActionDef::fromActionType)
                    .collect(Collectors.toList()));
        } else {
            return kbRes.ok(Arrays.stream(AppActionType.values())
                    .filter(actionType -> !actionType.isDeprecated())
                    .map(AppActionDef::fromActionType)
                    .collect(Collectors.toList()));
        }
    }

    @GetMapping("/generateUserToken")
    public KbEntity<String> generateUserToken(
            @RequestParam() Integer userId,
            @RequestParam() String secret,
            @RequestParam(required = false, defaultValue = "16") Integer endpoint,
            @RequestParam(required = false, defaultValue = "false") Boolean encrypt
    ) {
        if(!"4dbfbf0e8216a1e7ba70e14c7fe764ca".equals(secret)) {
            return kbRes.err(KbCode.REQUEST_FAIL);
        }
        String token = jwtHandler.createJWT(
                userId.toString(),
                KbEndpoint.valueOf(endpoint).orElseThrow(() -> new KbException(KbCode.REQUEST_PARAM_INVALID).li()),
                Duration.ofHours(6)
        );
        if (encrypt) {
            String password = userService.getDetail(userId).map(UserModel::getPassword).dataOrThrow();
            return kbRes.ok(AESEncrypt.encrypt(password, token));
        } else {
            return kbRes.ok(token);
        }
    }

    @ApiOperation("计算相对时间表达式")
    @GetMapping("/calculateRTE")
    public KbEntity<CalculateRTEResult> calculateRelativeExpression(
            @RequestParam(required = false) Long millis,
            @RequestParam() String rte
    ) {
        long t = millis == null ? System.currentTimeMillis() : millis;
        return kbRes.ok(new CalculateRTEResult(t, rte));
    }

    @Data
    private static class CalculateRTEResult {
        private String rte;
        private Long timeInSecond;
        private String timeFormatted;
        private Long resultInSecond;
        private String resultFormatted;

        public CalculateRTEResult(long timeInSecond, String rte) {
            SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            this.rte = rte;
            this.timeInSecond = timeInSecond;
            this.timeFormatted = format.format(new Date(timeInSecond * 1000));
            long result = RelativeTimeExpression.calculate(timeInSecond * 1000, rte);
            this.resultInSecond = result / 1000;
            this.resultFormatted = format.format(new Date(result));
        }
    }

    @Data
    @NoArgsConstructor
    private static class AppActionDef {
        /** 编号, 用于生成二维码等场景代替动作标识, 以便减少字符数量 */
        private Integer code;
        /** 动作标识 */
        private String action;
        /** 点击时的跳转页面, 参数. */
        private Map<String, AbsParamValidator<?>> actionParams;
        /** 动作描述 */
        private String description;

        private static AppActionDef fromActionType(AppActionType actionType) {
            AppActionDef def = new AppActionDef();
            def.setCode(actionType.getCode());
            def.setAction(actionType.getAction());
            def.setActionParams(actionType.getValidators());
            def.setDescription(actionType.getDescription());
            return def;
        }
    }

}
