package com.kaiba.m.core.controller.artmap.venues.backend;

import com.kaiba.lib.base.constant.KbHeader;
import com.kaiba.lib.base.domain.artmap.VenuesModel;
import com.kaiba.lib.base.domain.artmap.VenuesQueryModel;
import com.kaiba.lib.base.middleware.KbRes;
import com.kaiba.lib.base.response.KbEntity;
import com.kaiba.m.core.controller.artmap.mapper.ArtMapConMapping;
import com.kaiba.m.core.domain.artmap.Venues;
import com.kaiba.m.core.service.artmap.VenuesQueryService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.util.List;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * Description: 场馆管理后端查询Controller
 * Author: ZM227
 * Date: 2025/6/6 14:43
 */
@Slf4j
@RestController
@RequestMapping("/backend/artMap/venues/query")
@Api(tags = "艺术地图管理后台场馆查询")
public class VenuesBEQueryController {

    @Resource
    private KbRes kbRes;
    @Resource
    private VenuesQueryService venuesQueryService;
    @Resource
    private ArtMapConMapping artMapConMapping;

    @PostMapping("/usr/queryVenuesByCondition")
    @ApiOperation(value = "根据条件查询场馆列表")
    public KbEntity<List<VenuesModel>> queryVenuesByCondition(
        @RequestHeader(name = KbHeader.KB_USER_ID, required = false) Integer userId,
        @RequestBody VenuesQueryModel venuesQuery) {
        Page<Venues> venues = venuesQueryService.findVenuesByCondition(
            artMapConMapping.venuesModelToQueryDTO(venuesQuery));
        Page<VenuesModel> venuesModels = new PageImpl<>(
            venues.getContent().stream().map(m -> artMapConMapping.venuesDomainToModel(m))
                .collect(Collectors.toList()), venues.getPageable(), venues.getTotalElements());
        return kbRes.ok(venuesModels);
    }

    @PostMapping("/usr/queryVenuesDetail")
    @ApiOperation(value = "查询场馆详情")
    public KbEntity<VenuesModel> queryVenuesDetail(
        @RequestHeader(name = KbHeader.KB_USER_ID, required = false) Integer userId,
        @RequestParam String venuesCode) {
        return kbRes.ok(
            artMapConMapping.venuesDomainToModel(venuesQueryService.findVenuesByCode(venuesCode)));
    }
}
