package com.kaiba.m.core.controller.app.actionholder;

import com.kaiba.lib.base.assembler.Mapper;
import com.kaiba.lib.base.domain.appactionholder.ActionHolderCreateModel;
import com.kaiba.lib.base.domain.appactionholder.ActionHolderModel;
import com.kaiba.lib.base.middleware.KbRes;
import com.kaiba.lib.base.response.KbCode;
import com.kaiba.lib.base.response.KbEntity;
import com.kaiba.lib.base.service.IAppActionHolderService;
import com.kaiba.lib.base.util.appaction.AppActionModel;
import com.kaiba.m.core.domain.appactionholder.AppActionHolder;
import com.kaiba.m.core.service.appcomponent.actionholder.AppActionHolderModelHelper;
import com.kaiba.m.core.service.appcomponent.actionholder.AppActionHolderService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RestController;

import java.util.*;
import java.util.stream.Collectors;

/**
 * author: lyux
 * date: 2023-05-22
 */
@Slf4j
@RestController
public class ActionHolderServiceController implements IAppActionHolderService {

    private final KbRes kbRes;
    private final AppActionHolderService holderService;
    private final AppActionHolderModelHelper modelHelper;

    public ActionHolderServiceController(
            KbRes kbRes,
            AppActionHolderService actionHolderService,
            AppActionHolderModelHelper modelHelper
    ) {
        this.kbRes = kbRes;
        this.holderService = actionHolderService;
        this.modelHelper = modelHelper;
    }

    @Override
    public KbEntity<ActionHolderModel> createHolder(ActionHolderCreateModel model) {
        AppActionHolder holder = new AppActionHolder();
        holder.setKey(model.getKey());
        holder.setName(model.getName());
        holder.setAction(model.getAction());
        holder.setActionParams(model.getActionParams());
        holder.setActionMD5(holder.generateActionMD5());
        if (model.getCheckActionMD5() != null && model.getCheckActionMD5()
                && holderService.isHolderExistsByActionMD5(holder.getActionMD5())) {
            return kbRes.err(KbCode.REQUEST_FAIL, "already exists by md5", "同配置 Action 已存在");
        }
        AppActionHolder created = holderService.createHolder(holder);
        return kbRes.ok(modelHelper.holder2model(created));
    }

    @Override
    public KbEntity<ActionHolderModel> updateHolder(ActionHolderModel model) {
        AppActionHolder holder = Mapper.map(model, AppActionHolder.class);
        AppActionHolder updated = holderService.updateHolderData(holder);
        return kbRes.ok(modelHelper.holder2model(updated));
    }

    @Override
    public KbEntity<Void> deleteHolderById(String id) {
        holderService.deleteHolderById(id);
        return kbRes.ok();
    }

    @Override
    public KbEntity<ActionHolderModel> getHolderById(String id) {
        return holderService.getHolderById(id)
                .map(modelHelper::holder2model)
                .map(kbRes::ok)
                .orElseGet(() -> kbRes.err(KbCode.RESOURCE_NOT_FOUND));
    }

    @Override
    public KbEntity<ActionHolderModel> getHolderByKey(String key, Boolean allowCache) {
        if (allowCache == null || allowCache) {
            return holderService.getCachedHolderByKey(key)
                    .map(modelHelper::holder2model)
                    .map(kbRes::ok)
                    .orElseGet(() -> kbRes.err(KbCode.RESOURCE_NOT_FOUND));
        } else {
            return holderService.getHolderByKey(key)
                    .map(modelHelper::holder2model)
                    .map(kbRes::ok)
                    .orElseGet(() -> kbRes.err(KbCode.RESOURCE_NOT_FOUND));
        }
    }

    @Override
    public KbEntity<Map<String, ActionHolderModel>> getHolderMapByKeyIn(String[] keys, Boolean allowCache) {
        if (keys == null || keys.length == 0) {
            return kbRes.err(KbCode.REQUEST_PARAM_MISSING);
        }
        Map<String, AppActionHolder> map;
        if (allowCache == null || allowCache) {
            map = holderService.getCachedHolderMapByKeys(Arrays.asList(keys));
        } else {
            map = holderService.getHolderListByKeys(Arrays.asList(keys)).stream()
                    .collect(Collectors.toMap(AppActionHolder::getKey, h -> h, (h1, h2) -> h1));
        }
        Map<String, ActionHolderModel> models = new HashMap<>(map.size());
        map.forEach((key, holder) -> models.put(key, modelHelper.holder2model(holder)));
        return kbRes.ok(models);
    }

    @Override
    public KbEntity<List<ActionHolderModel>> getHolderListByKeyIn(String[] keys, Boolean allowCache) {
        if (keys == null || keys.length == 0) {
            return kbRes.err(KbCode.REQUEST_PARAM_MISSING);
        }
        if (allowCache == null || allowCache) {
            Map<String, AppActionHolder> map = holderService.getCachedHolderMapByKeys(Arrays.asList(keys));
            List<ActionHolderModel> models = new ArrayList<>(map.size());
            map.forEach((key, holder) -> models.add(modelHelper.holder2model(holder)));
            return kbRes.ok(models);
        } else {
            return kbRes.ok(holderService.getHolderListByKeys(Arrays.asList(keys)).stream()
                    .map(modelHelper::holder2model).collect(Collectors.toList()));
        }
    }

    @Override
    public KbEntity<AppActionModel> getActionByKey(String key, Boolean allowCache) {
        return getHolderByKey(key, allowCache).map(modelHelper::getActionFromHolder);
    }

    @Override
    public KbEntity<Map<String, AppActionModel>> getActionMapByKeyIn(String[] keys, Boolean allowCache) {
        return getHolderMapByKeyIn(keys, allowCache).map(holders -> {
            Map<String, AppActionModel> models = new HashMap<>(holders.size());
            holders.forEach((key, holder) -> models.put(key, modelHelper.getActionFromHolder(holder)));
            return models;
        });
    }

    @Override
    public KbEntity<List<ActionHolderModel>> getHolderByActionMD5(AppActionModel model) {
        String actionMD5 = model.generateActionMD5();
        return kbRes.ok(holderService
                .getHolderPageByActionMD5(actionMD5, 1, 100).map(modelHelper::holder2model));
    }

    @Override
    public KbEntity<List<ActionHolderModel>> getHolderList(String action, Integer page, Integer pageSize) {
        if (action == null) {
            return kbRes.ok(holderService.getHolderPage(page, pageSize).map(modelHelper::holder2model));
        } else {
            return kbRes.ok(holderService.getHolderPageByAction(action, page, pageSize).map(modelHelper::holder2model));
        }
    }

    @Override
    public KbEntity<List<ActionHolderModel>> searchHolderListByNameOrAction(
            String keyword, Integer page, Integer pageSize) {
        return kbRes.ok(holderService.searchHolderPage(keyword, page, pageSize).map(modelHelper::holder2model));
    }

}
