package com.kaiba.m.core.controller.issue;

import com.google.gson.reflect.TypeToken;
import com.kaiba.lib.base.domain.issue.IssueExpertModel;
import com.kaiba.lib.base.domain.issue.IssueModel;
import com.kaiba.lib.base.domain.issue.IssueTaskModel;
import com.kaiba.lib.base.middleware.KbRes;
import com.kaiba.lib.base.response.KbEntity;
import com.kaiba.lib.base.service.IIssueExpertService;
import com.kaiba.lib.base.service.IIssueService;
import com.kaiba.lib.base.service.IIssueTaskService;
import com.kaiba.m.core.configuration.IssueScoreProperties;
import com.kaiba.m.core.domain.issuetask.IssueTask;
import com.kaiba.m.core.domain.issuetask.RongOnlineStatus;
import com.kaiba.m.core.domain.issuetask.WorkingExpert;
import com.kaiba.m.core.service.issuetask.IssueTaskService;
import com.kaiba.m.core.service.issuetask.expert.WorkingExpertService;
import com.kaiba.m.core.util.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.*;
import java.util.stream.Collectors;

/**
 * author: lyux
 * date: 18-9-11
 */
@Slf4j
@RestController
public class IssueTaskServiceController implements IIssueTaskService {

    private final KbRes kbRes;
    private final IIssueService issueService;
    private final IIssueExpertService issueExpertService;
    private final IssueTaskService issueTaskService;
    private final WorkingExpertService workingExpertService;
    private final IssueScoreProperties issueScoreProperties;

    @Autowired
    public IssueTaskServiceController(
            KbRes kbRes,
            IssueTaskService issueTaskService,
            WorkingExpertService workingExpertService,
            IIssueService issueService,
            IIssueExpertService issueExpertService,
            IssueScoreProperties issueScoreProperties
    ) {
        this.kbRes = kbRes;
        this.issueTaskService = issueTaskService;
        this.workingExpertService = workingExpertService;
        this.issueService = issueService;
        this.issueExpertService = issueExpertService;
        this.issueScoreProperties = issueScoreProperties;
    }

    @Override
    public KbEntity<List<IssueTaskModel>> getIssueTaskList(Integer expertId, Integer[] states, Integer page, Integer pageSize) {
        List<IssueTask> taskList = issueTaskService.getTaskListByExpert(expertId, states, page, pageSize);
        if (taskList.isEmpty()) {
            return kbRes.ok(Collections.emptyList());
        }
        Map<String, IssueModel> issueMap = issueService
                .getIssueBasicMap(new HashSet<>(taskList.stream().map(IssueTask::getIssueId).collect(Collectors.toList())))
                .data()
                .orElse(Collections.emptyMap());

        return kbRes.ok(taskList.stream()
                .map(task -> {
                    IssueTaskModel model = issueTaskService.task2model(task);
                    model.setIssue(issueMap.get(task.getIssueId()));
                    return model;
                })
                .collect(Collectors.toList()));
    }

    @Override
    public KbEntity<Long> getIssueTaskCount(Integer expertId, Integer[] states, Long since) {
        since = null == since ? 0L : since;
        return kbRes.ok(issueTaskService.getTaskCountByExpert(expertId, states, since));
    }

    @Override
    public KbEntity<String> startReceiveIssue(Integer expertId) {
        workingExpertService.startWorking(expertId);
        return kbRes.ok();
    }

    @Override
    public KbEntity<String> stopReceiveIssue(Integer expertId) {
        workingExpertService.stopWorking(expertId);
        return kbRes.ok();
    }

    @Override
    public KbEntity<String> acceptIssue(Integer expertId, String issueId) {
        issueTaskService.taskAcceptByExpert(issueId, expertId);
        return kbRes.ok();
    }

    @Override
    public KbEntity<String> refuseIssue(Integer expertId, String issueId) {
        issueTaskService.taskRefuseByExpert(issueId, expertId);
        return kbRes.ok();
    }

    @Override
    public KbEntity<String> addRefuseReason(
            Integer expertId, String issueTaskId,
            Integer refuseReasonId, String refuseReasonContent) {
        issueTaskService.updateRefuseReason(issueTaskId, expertId, refuseReasonId, refuseReasonContent);
        return kbRes.ok();
    }

    @Override
    public KbEntity<String> cancelIssue(Integer userId, String issueId) {
        issueTaskService.taskCancelByUser(issueId, userId);
        return kbRes.ok();
    }

    @Override
    public KbEntity<Boolean> isExpertWorking(Integer expertId) {
        Optional<WorkingExpert> op = workingExpertService.findExpertById(expertId);
        boolean isWorking = op.isPresent() && op.get().getIsWorking();
        return kbRes.ok(isWorking);
    }

    @Override
    public KbEntity<Long> getWorkingExpertCount() {
        return kbRes.ok(workingExpertService.getExpertCount());
    }

    @Override
    public KbEntity<String> setExpertVip(Integer expertId, Integer vip) {
        workingExpertService.setExpertVip(expertId, vip);
        return kbRes.ok();
    }

    @Override
    public KbEntity<List<IssueExpertModel>> getVipExpertList() {
        Map<Integer, Integer> map = workingExpertService.getExpertVipList();
        if (map.isEmpty()) {
            return kbRes.ok();
        } else {
            Integer[] ids = new Integer[map.size()];
            List<IssueExpertModel> list = issueExpertService
                    .getExpertListIn(map.keySet().toArray(ids))
                    .data().orElse(Collections.emptyList())
                    .stream()
                    .peek(expert -> expert.setVip(map.get(expert.getId())))
                    .collect(Collectors.toList());
            return kbRes.ok(list);
        }
    }

    @Override
    public KbEntity<String> start(String issueId) {
        issueTaskService.taskStart(issueId);
        return kbRes.ok();
    }

    @Override
    public ResponseEntity<Void> receiveOnlineStatus(String content) {
        List<RongOnlineStatus> statusList;
        try {
            statusList = JsonUtils.toModel(
                    content, new TypeToken<List<RongOnlineStatus>>() {}.getType());
        } catch (Exception e) {
            log.info("receive rong online status fail " + e);
            return new ResponseEntity<>(HttpStatus.OK);
        }

        if (null == statusList || statusList.size() == 0) {
            return new ResponseEntity<>(HttpStatus.OK);
        }

        for (RongOnlineStatus status : statusList) {
            if (status.getUserid() == null || status.getStatus() == null) {
                continue;
            }
            try {
                int uid = Integer.parseInt(status.getUserid());
                int state = Integer.parseInt(status.getStatus());
                long time = Long.parseLong(status.getTime());
                workingExpertService.receiveRongOnlineStatus(uid, state, status.getOs(), time);
            } catch (NumberFormatException ignore) {
            }
        }
        return new ResponseEntity<>(HttpStatus.OK);
    }

    @PostMapping("/issue/task/getWorkingExpertList")
    public KbEntity<List<WorkingExpert>> getWorkingExpertList() {
        return kbRes.ok(workingExpertService.getAllExperts());
    }

    @PostMapping("/issue/task/getWorkingExpert")
    public KbEntity<WorkingExpert> getWorkingExpert(
            @RequestParam() Integer experId
    ) {
        return kbRes.ok(workingExpertService.findExpertById(experId).orElse(null));
    }

    @PostMapping("/issue/task/resetAllWorkingExpert")
    public KbEntity<String> resetAllWorkingExpert() {
        workingExpertService.resetAllExperts();
        return kbRes.ok();
    }

    @PostMapping("/issue/task/recoverTaskFromOnGoing")
    public KbEntity<String> recoverTaskFromOnGoing(
            @RequestParam() String issueId
    ) {
        IssueModel issue = issueService.getIssueBasic(issueId).dataOrThrow();
        issueTaskService.taskRecoverFromOnGoing(issue);
        return kbRes.ok();
    }

    @PostMapping("/issue/task/getTaskServerStatus")
    public KbEntity<Object> getTaskServerStatus() {
        return kbRes.ok(issueTaskService.getRunningStatus());
    }

    @PostMapping("/issue/task/getIssueScoreProperties")
    public KbEntity<Object> getIssueScoreProperties() {
        return kbRes.ok(issueScoreProperties);
    }

}
