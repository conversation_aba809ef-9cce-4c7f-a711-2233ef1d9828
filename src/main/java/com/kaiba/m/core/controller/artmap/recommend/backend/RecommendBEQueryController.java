package com.kaiba.m.core.controller.artmap.recommend.backend;

import com.kaiba.lib.base.constant.KbHeader;
import com.kaiba.lib.base.middleware.KbRes;
import com.kaiba.lib.base.response.KbEntity;
import com.kaiba.m.core.controller.artmap.mapper.ArtMapConMapping;
import com.kaiba.m.core.domain.artmap.Recommend;
import com.kaiba.m.core.model.artmap.RecommendModel;
import com.kaiba.m.core.service.artmap.RecommendQueryService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.util.List;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * Description: 艺术地图管理后台每日推荐查询Controller
 * Author: ZM227
 * Date: 2025/6/13 16:34
 */
@Slf4j
@RestController
@RequestMapping("/backend/artMap/recommend/modify")
@Api(tags = "艺术地图管理后台每日推荐查询")
public class RecommendBEQueryController {

    @Resource
    private KbRes kbRes;
    @Resource
    private RecommendQueryService recommendQueryService;
    @Resource
    private ArtMapConMapping artMapConMapping;

    @PostMapping("/usr/queryRecommendList")
    @ApiOperation(value = "查询推荐列表")
    public KbEntity<List<RecommendModel>> queryRecommendList(
        @RequestHeader(name = KbHeader.KB_USER_ID, required = false) Integer userId,
        @RequestParam(required = false, defaultValue = "1") Integer page,
        @RequestParam(required = false, defaultValue = "15") Integer pageSize) {
        Page<Recommend> recommends = recommendQueryService.queryRecommendList(page, pageSize);
        Page<RecommendModel> recommendModels = new PageImpl<>(
            recommends.getContent().stream().map(m -> artMapConMapping.recommendDomainToModel(m))
                .collect(Collectors.toList()), recommends.getPageable(),
            recommends.getTotalElements());
        return kbRes.ok(recommendModels);
    }

}
