package com.kaiba.m.core.controller.thirdparty.hcrtreview;

import com.kaiba.lib.base.annotation.api.KbCheckSignature;
import com.kaiba.lib.base.constant.KbSignType;
import com.kaiba.lib.base.middleware.KbRes;
import com.kaiba.lib.base.response.KbCode;
import com.kaiba.lib.base.response.KbEntity;
import com.kaiba.m.core.controller.thirdparty.hcrtreview.constant.KbHCRTReviewConst;
import com.kaiba.m.core.controller.thirdparty.hcrtreview.model.KbResult;
import com.kaiba.m.core.controller.thirdparty.hcrtreview.service.KbReviewProviderService;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * author: lyux
 * date: 2024-10-24
 *
 * 对接集团内容审核服务商, 每天从开吧拉取数据, 做内容安全审核并出具报告.
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/HCRTReviewTP/core")
public class HCRTReviewApiController {

    private final KbRes kbApiRes;
    private final KbReviewProviderService reviewProviderService;

    public HCRTReviewApiController(
            @Qualifier("kbApiRes") KbRes kbApiRes,
            KbReviewProviderService reviewProviderService
    ) {
        this.kbApiRes = kbApiRes;
        this.reviewProviderService = reviewProviderService;
    }

    @ApiOperation("根据业务标识获取内容列表")
    @KbCheckSignature(type = KbSignType.THIRD_PARTY)
    @PostMapping(path = "/getContentListByBiz")
    public KbEntity<KbResult> getContentListByBiz(
            @RequestParam(required = false) String biz,
            @RequestParam(required = false) Integer page,
            @RequestParam(required = false) Integer pageSize,
            @RequestParam(required = false) @ApiParam("截止时间戳, 单位毫秒") Long until
    ) {
        if (KbHCRTReviewConst.DEBUG) {
            log.info("HCRTReview.getContentListByBiz(), " + biz + "");
        }
        if (biz == null) {
            return kbApiRes.err(KbCode.REQUEST_PARAM_MISSING, "missing biz", "请指定业务类型");
        }
        if (page == null) {
            return kbApiRes.err(KbCode.REQUEST_PARAM_MISSING, "missing page", "请指定页码");
        }
        if (pageSize == null) {
            return kbApiRes.err(KbCode.REQUEST_PARAM_MISSING, "missing pageSize", "请指定页宽");
        }
        if (until == null) {
            return kbApiRes.err(KbCode.REQUEST_PARAM_MISSING, "missing until", "请指定时间");
        }
        if (!reviewProviderService.isBizSupported(biz)) {
            return kbApiRes.err(KbCode.REQUEST_PARAM_INVALID,
                    "biz not supported yet", "业务类型尚不支持");
        }
        if (page <= 0 || pageSize <= 0) {
            return kbApiRes.err(KbCode.REQUEST_PARAM_INVALID,
                    "page and pageSize should be positive", "页码和页宽必须为正数");
        }
        if (pageSize > KbHCRTReviewConst.MAX_PAGE_SIZE) {
            return kbApiRes.err(KbCode.REQUEST_PARAM_INVALID,
                    "pageSize exceed max (" + KbHCRTReviewConst.MAX_PAGE_SIZE + ")",
                    "页宽过大, 上限1000");
        }
        if (until < KbHCRTReviewConst.MIN_UNTIL) {
            return kbApiRes.err(KbCode.REQUEST_PARAM_INVALID,
                    "until less than " + KbHCRTReviewConst.MIN_UNTIL,
                    "截止时间不可早于 2024-01-01");
        }
        if (until > System.currentTimeMillis()) {
            return kbApiRes.err(KbCode.REQUEST_PARAM_INVALID,
                    "until greater than current",
                    "截止时间大于当前时间");
        }

        return kbApiRes.ok(reviewProviderService.request(biz, page, pageSize, until));
    }

}
