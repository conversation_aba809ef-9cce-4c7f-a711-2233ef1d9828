package com.kaiba.m.core.controller.user;

import com.kaiba.lib.base.constant.KbHeader;
import com.kaiba.lib.base.domain.user.UserRosterModel;
import com.kaiba.lib.base.middleware.KbRes;
import com.kaiba.lib.base.response.KbCode;
import com.kaiba.lib.base.response.KbEntity;
import com.kaiba.lib.base.service.IUserRosterService;
import com.kaiba.lib.base.util.StringUtils;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 花名册管理,频率频道使用
 * 基于操作人所在部门管理
 * <AUTHOR>
 * @version UserRosterAdminController, v0.1 2024/2/26 16:31 daopei Exp $
 **/
@RequestMapping("/user/roster/sub")
@RestController
@RequiredArgsConstructor
public class UserRosterAdminController {

    @NonNull
    private final KbRes kbRes;
    @NonNull
    private IUserRosterService userRosterService;


    @ApiOperation("更新花名册用户状态")
    @PostMapping(path = "/manage/updateState")
    KbEntity<UserRosterModel> updateState(
            @RequestHeader(KbHeader.KB_USER_ID) Integer operateUserId,
            @RequestParam @ApiParam("花名册实例KEY") String instanceKey,
            @RequestParam @ApiParam("花名册ID") String id,
            @RequestParam Integer state
    ){
        if (!checkAccess(operateUserId, instanceKey, id)) {
            return kbRes.err(KbCode.AUTH_FAIL, null, "组织不匹配");
        }
        return userRosterService.updateState(id, state);
    }

    @ApiOperation("绑定用户ID")
    @PostMapping(path = "/manage/updateBindUser")
    KbEntity<UserRosterModel> updateBindUser(
            @RequestHeader(KbHeader.KB_USER_ID) Integer operateUserId,
            @RequestParam @ApiParam("花名册实例KEY") String instanceKey,
            @RequestParam(name = "id") @ApiParam("花名册用户ID") String id,
            @RequestParam(name = "bindUserId") Integer bindUserId
    ){
        if (!checkAccess(operateUserId, instanceKey, id)) {
            return kbRes.err(KbCode.AUTH_FAIL, null, "组织不匹配");
        }
        return userRosterService.updateBindUser(id, bindUserId);
    }

    @ApiOperation("新增花名册用户")
    @PostMapping(path = "/manage/createUser")
    KbEntity<UserRosterModel> createUser(
            @RequestHeader(KbHeader.KB_USER_ID) Integer operateUserId,
            @RequestBody() UserRosterModel user
    ){
        if (!checkAccessOrg(operateUserId, user.getInstanceKey(), user.getOrganizationId())) {
            return kbRes.err(KbCode.AUTH_FAIL, null, "组织不匹配");
        }
        return userRosterService.createUser(user);
    }

    @ApiOperation("修改花名册用户")
    @PostMapping(path = "/manage/updateUser")
    KbEntity<UserRosterModel> updateUser(
            @RequestHeader(KbHeader.KB_USER_ID) Integer operateUserId,
            @RequestBody() UserRosterModel user
    ){
        if (!checkAccess(operateUserId, user.getInstanceKey(), user.getId())) {
            return kbRes.err(KbCode.AUTH_FAIL, null, "组织不匹配");
        }
        if (!checkAccessOrg(operateUserId, user.getInstanceKey(), user.getOrganizationId())) {
            return kbRes.err(KbCode.AUTH_FAIL, null, "组织不匹配");
        }
        return userRosterService.updateUser(user);
    }


    // --------------

    private boolean checkAccess(Integer userId, String instanceKey, String rosterId) {
        UserRosterModel userRoster = userRosterService.getByUserIdAndInstanceKey(userId, instanceKey).dataOrThrow();
        UserRosterModel staff = userRosterService.getById(rosterId).dataOrThrow();
        return StringUtils.isEqual(userRoster.getOrganizationId(), staff.getOrganizationId());
    }

    private boolean checkAccessOrg(Integer userId, String instanceKey, String orgId) {
        UserRosterModel userRoster = userRosterService.getByUserIdAndInstanceKey(userId, instanceKey).dataOrThrow();
        return StringUtils.isEqual(userRoster.getOrganizationId(), orgId);
    }
}
