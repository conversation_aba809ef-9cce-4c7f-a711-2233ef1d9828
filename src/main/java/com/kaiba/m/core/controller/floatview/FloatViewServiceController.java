package com.kaiba.m.core.controller.floatview;

import com.google.gson.reflect.TypeToken;
import com.kaiba.lib.base.assembler.Mapper;
import com.kaiba.lib.base.constant.floatview.FloatViewMarkType;
import com.kaiba.lib.base.constant.floatview.FloatViewState;
import com.kaiba.lib.base.domain.floatview.FloatIconModel;
import com.kaiba.lib.base.domain.floatview.FloatWindowModel;
import com.kaiba.lib.base.middleware.KbRes;
import com.kaiba.lib.base.response.KbCode;
import com.kaiba.lib.base.response.KbEntity;
import com.kaiba.lib.base.response.KbException;
import com.kaiba.lib.base.service.IFloatViewService;
import com.kaiba.lib.base.util.GsonUtils;
import com.kaiba.m.core.domain.floatview.FloatIcon;
import com.kaiba.m.core.domain.floatview.FloatWindow;
import com.kaiba.m.core.service.floatview.FloatIconService;
import com.kaiba.m.core.service.floatview.FloatWindowService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.RestController;

import java.util.*;
import java.util.stream.Collectors;

/**
 * author: lyux
 * date: 19-12-25
 */
@Slf4j
@RestController
public class FloatViewServiceController implements IFloatViewService {

    private final KbRes kbRes;
    private final FloatIconService floatIconService;
    private final FloatWindowService floatWindowService;

    public FloatViewServiceController(
            @Qualifier("kbApiRes") KbRes kbRes,
            FloatIconService floatIconService,
            FloatWindowService floatWindowService
    ) {
        this.kbRes = kbRes;
        this.floatIconService = floatIconService;
        this.floatWindowService = floatWindowService;
    }

    @Override
    public KbEntity<FloatIconModel> createFloatIcon(
            Integer userId, Integer siteId, String mark, String icon, String action, String actionParams,
            String position, Integer sizeRatio, Boolean enableDrag, Boolean onlyForLogin,
            Long scheduledStartTime, Long scheduledEndTime, String description
    ) {
        FloatIcon floatIcon = new FloatIcon();
        floatIcon.setCreatorId(userId);
        floatIcon.setSiteId(siteId);
        floatIcon.setIcon(icon);
        floatIcon.setMark(mark);
        floatIcon.setPosition(position);
        floatIcon.setSizeRatio(sizeRatio);
        floatIcon.setEnableDrag(enableDrag);
        floatIcon.setOnlyForLogin(onlyForLogin);
        floatIcon.setScheduledEndTime(scheduledEndTime);
        floatIcon.setScheduledStartTime(scheduledStartTime);
        floatIcon.setDescription(description);
        floatIcon.setAction(action);
        if (null != actionParams) {
            Map<String, Object> ap = GsonUtils.getGson().fromJson(
                    actionParams, new TypeToken<Map<String, Object>>() {}.getType());
            floatIcon.setActionParams(ap);
        }
        return kbRes.ok(floatIcon2model(floatIconService.createFloatIcon(floatIcon)));
    }

    @Override
    public KbEntity<FloatIconModel> updateFloatIconData(
            Integer userId, String floatIconId, String icon, String action, String actionParams,
            String position, Integer sizeRatio, Boolean enableDrag, Boolean onlyForLogin,
            Long scheduledStartTime, Long scheduledEndTime, String description
    ) {
        FloatIcon floatIcon = floatIconService.getById(floatIconId).orElseThrow(() ->
                new KbException(KbCode.REQUEST_PARAM_INVALID, "float icon not exists: " + floatIconId).li());
        if (floatIcon.getState() == null || floatIcon.getState() != FloatViewState.PREPARE.getValue()) {
            return kbRes.err(KbCode.ILLEGAL_STATE,
                    "update not allowed under state: " + floatIcon,
                    "只有准备中的实例才允许修改");
        }
        floatIcon.setIcon(icon);
        floatIcon.setPosition(position);
        floatIcon.setSizeRatio(sizeRatio);
        floatIcon.setEnableDrag(enableDrag);
        floatIcon.setOnlyForLogin(onlyForLogin);
        floatIcon.setScheduledEndTime(scheduledEndTime);
        floatIcon.setScheduledStartTime(scheduledStartTime);
        floatIcon.setDescription(description);
        floatIcon.setAction(action);
        if (null != actionParams) {
            Map<String, Object> ap = GsonUtils.getGson().fromJson(
                    actionParams, new TypeToken<Map<String, Object>>() {}.getType());
            floatIcon.setActionParams(ap);
        }
        return kbRes.ok(floatIcon2model(floatIconService.updateData(floatIcon)));
    }

    @Override
    public KbEntity<FloatIconModel> signFloatIcon(Integer userId, String floatIconId, Boolean checkTimeConflict) {
        FloatIcon floatIcon = floatIconService.getById(floatIconId).orElseThrow(() ->
                new KbException(KbCode.FLOAT_ICON_NOT_EXISTS).li());
        if (checkTimeConflict && null != floatIcon.getScheduledStartTime()) {
            // 需要首先检查要发布的浮标的预定起止时间, 是否和其他活动中的浮标重合
            long startTime = floatIcon.getScheduledStartTime();
            long endTime = floatIcon.getScheduledEndTime() == null ? 0 : floatIcon.getScheduledEndTime();
            for (int i = 1; ; i ++) {
                Page<FloatIcon> page = floatIconService.getPageBySiteId(
                        floatIcon.getSiteId(), floatIcon.getMark(), FloatViewState.ACTIVE_STATES, i, 100);
                List<FloatIcon> list = page.getContent();
                for (FloatIcon fi : list) {
                    if (fi.getScheduledStartTime() == null || fi.getScheduledEndTime() == null) {
                        continue;
                    }
                    if (startTime > fi.getScheduledStartTime() && startTime < fi.getScheduledEndTime()) {
                        return kbRes.err(
                                KbCode.FLOAT_ICON_SIGN_TIME_CONFLICT,
                                "float icon schedule time conflict: " + floatIcon + " -> " + fi,
                                "浮标的开始时间和另一个浮标冲突");
                    } else if (endTime > fi.getScheduledStartTime() && endTime < fi.getScheduledEndTime()) {
                        return kbRes.err(
                                KbCode.FLOAT_ICON_SIGN_TIME_CONFLICT,
                                "float icon schedule time conflict: " + floatIcon + " -> " + fi,
                                "浮标的起止时间和另一个浮标冲突");
                    }
                }
                if (page.isLast()) {
                    break;
                }
            }
        }
        floatIcon = floatIconService.updateState(floatIcon, FloatViewState.SIGNED);
        return kbRes.ok(floatIcon2model(floatIcon));
    }

    @Override
    public KbEntity<FloatIconModel> unsignFloatIcon(Integer userId, String floatIconId) {
        FloatIcon floatIcon = floatIconService.updateState(floatIconId, FloatViewState.PREPARE);
        return kbRes.ok(floatIcon2model(floatIcon));
    }

    @Override
    public KbEntity<FloatIconModel> startFloatIcon(Integer userId, String floatIconId) {
        FloatIcon floatIcon = floatIconService.updateState(floatIconId, FloatViewState.ONLINE);
        floatIconService.stopOnlineExcept(floatIcon);
        return kbRes.ok(floatIcon2model(floatIcon));
    }

    @Override
    public KbEntity<FloatIconModel> stopFloatIcon(Integer userId, String floatIconId) {
        FloatIcon floatIcon = floatIconService.updateState(floatIconId, FloatViewState.SEALED);
        return kbRes.ok(floatIcon2model(floatIcon));
    }

    @Override
    public KbEntity<Void> deleteFloatIcon(Integer userId, String floatIconId) {
        floatIconService.deleteById(floatIconId);
        return kbRes.ok();
    }

    @Override
    public KbEntity<FloatIconModel> getFloatIconById(String floatIconId) {
        return floatIconService.getById(floatIconId)
                .map(FloatViewServiceController::floatIcon2model)
                .map(kbRes::ok)
                .orElse(kbRes.err(KbCode.RESOURCE_NOT_FOUND));
    }

    @Override
    public KbEntity<FloatIconModel> getLastFloatIconBySite(
            Integer siteId, String mark, Integer[] states) {
        return floatIconService.getLastBySiteId(siteId, mark, states)
                .map(FloatViewServiceController::floatIcon2model)
                .map(kbRes::ok)
                .orElse(kbRes.err(KbCode.RESOURCE_NOT_FOUND));
    }

    @Override
    public KbEntity<FloatIconModel> getLastSignedOrOnlineFloatIconBySite(Integer siteId, String mark) {
        Optional<FloatIconModel> opOnlineModel = floatIconService
                .getLastBySiteId(siteId, mark, FloatViewState.ONLINE_STATES)
                .map(FloatViewServiceController::floatIcon2model);
        if (opOnlineModel.isPresent()) {
            return kbRes.ok(opOnlineModel.get());
        }
        return floatIconService.getLastBySiteIdOrderByScheduleTime(siteId, FloatViewState.SIGNED)
                .map(FloatViewServiceController::floatIcon2model)
                .map(kbRes::ok)
                .orElse(kbRes.err(KbCode.RESOURCE_NOT_FOUND));
    }

    @Override
    public KbEntity<List<FloatIconModel>> getLastFloatIconListBySite(
            Integer siteId, Integer[] states, Integer page, Integer pageSize) {
        List<FloatIconModel> list = new ArrayList<>(FloatViewMarkType.values().length);
        for (FloatViewMarkType type : FloatViewMarkType.values()) {
            floatIconService.getLastBySiteId(siteId, type.getMark(), states)
                    .map(FloatViewServiceController::floatIcon2model)
                    .ifPresent(list::add);
        }
        return kbRes.ok(list);
    }

    @Override
    public KbEntity<List<FloatIconModel>> getFloatIconListBySite(
            Integer siteId, String mark, Integer[] states, Integer page, Integer pageSize) {
        return kbRes.ok(floatIconService
                .getPageBySiteId(siteId, mark, states, page, pageSize)
                .map(FloatViewServiceController::floatIcon2model));
    }

    @Override
    public KbEntity<FloatWindowModel> createFloatWindow(
            Integer userId, Integer siteId, String mark,
            String link, String backgroundColor,
            Integer widthScreenRatio, Integer widthHeightRatio,
            Boolean onlyForLogin, Boolean decorateLink,
            Long scheduledStartTime, Long scheduledEndTime, String description
    ) {
        FloatWindow floatWindow = new FloatWindow();
        floatWindow.setCreatorId(userId);
        floatWindow.setSiteId(siteId);
        floatWindow.setLink(link);
        floatWindow.setMark(mark);
        floatWindow.setBackgroundColor(backgroundColor);
        floatWindow.setWidthHeightRatio(widthHeightRatio);
        floatWindow.setWidthScreenRatio(widthScreenRatio);
        floatWindow.setOnlyForLogin(onlyForLogin);
        floatWindow.setDecorateLink(decorateLink);
        floatWindow.setScheduledEndTime(scheduledEndTime);
        floatWindow.setScheduledStartTime(scheduledStartTime);
        floatWindow.setDescription(description);
        return kbRes.ok(floatWindow2model(floatWindowService.createFloatWindow(floatWindow)));
    }

    @Override
    public KbEntity<FloatWindowModel> updateFloatWindowData(
            Integer userId, String floatWindowId,
            String link, String backgroundColor,
            Integer widthScreenRatio, Integer widthHeightRatio,
            Boolean onlyForLogin, Boolean decorateLink,
            Long scheduledStartTime, Long scheduledEndTime, String description
    ) {
        FloatWindow floatWindow = floatWindowService.getById(floatWindowId).orElseThrow(() ->
                new KbException(KbCode.RESOURCE_NOT_FOUND, "float window not exists: " + floatWindowId).li());
        if (floatWindow.getState() == null || floatWindow.getState() != FloatViewState.PREPARE.getValue()) {
            return kbRes.err(KbCode.ILLEGAL_STATE,
                    "update not allowed under state: " + floatWindow,
                    "只有准备中的实例才允许修改");
        }
        floatWindow.setLink(link);
        floatWindow.setBackgroundColor(backgroundColor);
        floatWindow.setWidthHeightRatio(widthHeightRatio);
        floatWindow.setWidthScreenRatio(widthScreenRatio);
        floatWindow.setOnlyForLogin(onlyForLogin);
        floatWindow.setDecorateLink(decorateLink);
        floatWindow.setScheduledEndTime(scheduledEndTime);
        floatWindow.setScheduledStartTime(scheduledStartTime);
        floatWindow.setDescription(description);
        return kbRes.ok(floatWindow2model(floatWindowService.updateData(floatWindow)));
    }

    @Override
    public KbEntity<FloatWindowModel> signFloatWindow(Integer userId, String floatWindowId, Boolean checkTimeConflict) {
        FloatWindow floatWindow = floatWindowService.getById(floatWindowId).orElseThrow(() ->
                new KbException(KbCode.FLOAT_WINDOW_NOT_EXISTS).li());
        if (checkTimeConflict && null != floatWindow.getScheduledStartTime()) {
            // 需要首先检查要发布的浮标的预定起止时间, 是否和其他活动中的浮标重合
            long startTime = floatWindow.getScheduledStartTime();
            long endTime = floatWindow.getScheduledEndTime() == null ? 0 : floatWindow.getScheduledEndTime();
            for (int i = 1; ; i ++) {
                Page<FloatWindow> page = floatWindowService.getPageBySiteId(
                        floatWindow.getSiteId(), floatWindow.getMark(), FloatViewState.ACTIVE_STATES, i, 100);
                List<FloatWindow> list = page.getContent();
                for (FloatWindow fw : list) {
                    if (fw.getScheduledStartTime() == null || fw.getScheduledEndTime() == null) {
                        continue;
                    }
                    if (startTime > fw.getScheduledStartTime() && startTime < fw.getScheduledEndTime()) {
                        return kbRes.err(
                                KbCode.FLOAT_WINDOW_SIGN_TIME_CONFLICT,
                                "float window schedule time conflict: " + floatWindow + " -> " + fw,
                                "浮窗的开始时间和另一个浮窗冲突");
                    } else if (endTime > fw.getScheduledStartTime() && endTime < fw.getScheduledEndTime()) {
                        return kbRes.err(
                                KbCode.FLOAT_WINDOW_SIGN_TIME_CONFLICT,
                                "float window schedule time conflict: " + floatWindow + " -> " + fw,
                                "浮窗的起止时间和另一个浮窗冲突");
                    }
                }
                if (page.isLast()) {
                    break;
                }
            }
        }
        floatWindow = floatWindowService.updateState(floatWindowId, FloatViewState.SIGNED);
        return kbRes.ok(floatWindow2model(floatWindow));
    }

    @Override
    public KbEntity<FloatWindowModel> unsignFloatWindow(Integer userId, String floatWindowId) {
        FloatWindow floatWindow = floatWindowService.updateState(floatWindowId, FloatViewState.PREPARE);
        return kbRes.ok(floatWindow2model(floatWindow));
    }

    @Override
    public KbEntity<FloatWindowModel> startFloatWindow(Integer userId, String floatWindowId) {
        FloatWindow floatWindow = floatWindowService.updateState(floatWindowId, FloatViewState.ONLINE);
        floatWindowService.stopOnlineExcept(floatWindow);
        return kbRes.ok(floatWindow2model(floatWindow));
    }

    @Override
    public KbEntity<FloatWindowModel> stopFloatWindow(Integer userId, String floatWindowId) {
        FloatWindow floatWindow = floatWindowService.updateState(floatWindowId, FloatViewState.SEALED);
        return kbRes.ok(floatWindow2model(floatWindow));
    }

    @Override
    public KbEntity<Void> deleteFloatWindow(Integer userId, String floatWindowId) {
        floatWindowService.deleteById(floatWindowId);
        return kbRes.ok();
    }

    @Override
    public KbEntity<FloatWindowModel> getFloatWindowById(String floatWindowId) {
        return floatWindowService.getById(floatWindowId)
                .map(FloatViewServiceController::floatWindow2model)
                .map(kbRes::ok)
                .orElse(kbRes.err(KbCode.RESOURCE_NOT_FOUND));
    }

    @Override
    public KbEntity<FloatWindowModel> getLastFloatWindowBySite(
            Integer siteId, String mark, Integer[] states) {
        return floatWindowService.getLastBySiteId(siteId, mark, states)
                .map(FloatViewServiceController::floatWindow2model)
                .map(kbRes::ok)
                .orElse(kbRes.err(KbCode.RESOURCE_NOT_FOUND));
    }

    @Override
    public KbEntity<List<FloatWindowModel>> getLastFloatWindowListBySite(
            Integer siteId, Integer[] states, Integer page, Integer pageSize) {
        List<FloatWindowModel> list = new ArrayList<>(FloatViewMarkType.values().length);
        for (FloatViewMarkType type : FloatViewMarkType.values()) {
            floatWindowService.getLastBySiteId(siteId, type.getMark(), states)
                    .map(FloatViewServiceController::floatWindow2model)
                    .ifPresent(list::add);
        }
        return kbRes.ok(list);
    }

    @Override
    public KbEntity<List<FloatWindowModel>> getFloatWindowListBySite(
            Integer siteId, String mark, Integer[] states, Integer page, Integer pageSize) {
        return kbRes.ok(floatWindowService
                .getPageBySiteId(siteId, mark, states, page, pageSize)
                .map(FloatViewServiceController::floatWindow2model));
    }

    @Override
    public KbEntity<List<String>> getFloatViewMarkList() {
        return kbRes.ok(Arrays.stream(FloatViewMarkType.values())
                .map(FloatViewMarkType::getMark)
                .collect(Collectors.toList()));
    }

    // --------------------------------------------------------

    private static FloatIconModel floatIcon2model(FloatIcon floatIcon) {
        return Mapper.map(floatIcon, FloatIconModel.class);
    }

    private static FloatWindowModel floatWindow2model(FloatWindow floatWindow) {
        return Mapper.map(floatWindow, FloatWindowModel.class);
    }

}
