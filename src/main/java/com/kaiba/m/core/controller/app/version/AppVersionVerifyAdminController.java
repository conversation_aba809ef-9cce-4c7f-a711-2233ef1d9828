package com.kaiba.m.core.controller.app.version;

import com.kaiba.lib.base.response.KbEntity;
import com.kaiba.m.core.domain.appversion.AppVersionVerify;
import com.kaiba.lib.base.middleware.KbRes;
import com.kaiba.m.core.service.appversion.AppVersionVerifyService;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * author: duanyf
 * date: 2024-06-25
 **/
@Slf4j
@RestController
@RequestMapping("/admin/AppVersion/verify")
public class AppVersionVerifyAdminController {

    private final KbRes kbRes;
    private final AppVersionVerifyService appVersionVerifyService;

    public AppVersionVerifyAdminController(
            KbRes kbRes,
            AppVersionVerifyService appVersionVerifyService
    ) {
        this.kbRes = kbRes;
        this.appVersionVerifyService = appVersionVerifyService;
    }

    @ApiOperation(value = "获取审核列表")
    @PostMapping(path = "/getList")
    public KbEntity<List<AppVersionVerify>> getList(
            @RequestParam(required = false) Integer endPoint,
            @RequestParam(required = false) String market,
            @RequestParam(required = false, defaultValue = "1") Integer page,
            @RequestParam(required = false, defaultValue = "15") Integer pageSize
    ) {
        return kbRes.ok(appVersionVerifyService.getList(endPoint, market, page, pageSize));
    }

    @ApiOperation("添加审核信息")
    @PostMapping("/add")
    public KbEntity<AppVersionVerify> add(
            @RequestBody AppVersionVerify model
    ) {
        return kbRes.ok(appVersionVerifyService.add(model));
    }

    @ApiOperation("删除审核信息")
    @PostMapping("/delete")
    public KbEntity<Void> delete(
            @RequestParam String id
    ) {
        appVersionVerifyService.delete(id);
        return kbRes.ok();
    }

    @ApiOperation("编辑审核信息状态")
    @PostMapping("/updateState")
    public KbEntity<AppVersionVerify> updateState(
            @RequestParam String id,
            @RequestParam Integer state
    ) {
        return kbRes.ok(appVersionVerifyService.updateState(id, state));
    }
}
