package com.kaiba.m.core.controller.site;

import com.google.gson.reflect.TypeToken;
import com.kaiba.lib.base.domain.site.SiteChooseModel;
import com.kaiba.lib.base.middleware.KbRes;
import com.kaiba.lib.base.response.KbEntity;
import com.kaiba.lib.base.service.ISiteService;
import com.kaiba.lib.base.util.GsonUtils;

import org.springframework.http.client.SimpleClientHttpRequestFactory;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2022/11/18 11:20
 **/
@Slf4j
@RestController()
@RequestMapping("/n/site")
public class SiteApiController {

    private static final String URL = "https://api.kaiba315.com.cn/index/appVerify?endpoint=11&do=iOS&cnl=AppStore&version=";
    private static final List<Integer> SITE_IDS = Arrays.asList(9, 24, 59, 66, 124, 167, 185);
    private static final String IOS_NAME = "iOS";

    private final KbRes kbRes;
    private final RestTemplate restTemplate;
    private final ISiteService siteService;

    public SiteApiController(
        KbRes kbRes,
        ISiteService siteService
    ) {
        this.kbRes = kbRes;
        this.siteService = siteService;
        this.restTemplate = new RestTemplate(new SimpleClientHttpRequestFactory());
    }

    @ApiOperation("获取电台列表")
    @PostMapping(path = "/getOpenSiteSimpleList")
    public KbEntity<SiteChooseModel> getOpenSiteSimpleList(
        @RequestHeader() String kb_do,
        @RequestParam(required = false) String version,
        @RequestParam(required = false) Integer adCode,
        @RequestParam(required = false) Integer source
    ) {
        if (IOS_NAME.equalsIgnoreCase(kb_do)) {
            String response = restTemplate.getForObject(URL + version, String.class);
            Map<String, Object> map = GsonUtils.getGson()
                .fromJson(response, new TypeToken<Map<String, Object>>() {}.getType());
            if (map != null && Objects.equals(1, (int)Double.parseDouble(map.get("data").toString()))) {
                SiteChooseModel siteModel = siteService.getOpenSiteSimpleList(adCode, source).dataOrThrow();
                siteModel.setAll(siteModel.getAll()
                    .stream()
                    .filter(t -> SITE_IDS.contains(t.getSiteId()))
                    .collect(Collectors.toList()));
                siteModel.setHot(siteModel.getHot()
                    .stream()
                    .filter(t -> SITE_IDS.contains(t.getSiteId()))
                    .collect(Collectors.toList()));
                siteModel.setLocal(siteModel.getLocal()
                    .stream()
                    .filter(t -> SITE_IDS.contains(t.getSiteId()))
                    .collect(Collectors.toList()));
                return kbRes.ok(siteModel);
            }
        }
        return siteService.getOpenSiteSimpleList(adCode, source);
    }
}
