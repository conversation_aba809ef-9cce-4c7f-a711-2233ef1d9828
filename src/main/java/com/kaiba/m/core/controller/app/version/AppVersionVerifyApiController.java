package com.kaiba.m.core.controller.app.version;

import com.kaiba.lib.base.constant.KbHeader;
import com.kaiba.lib.base.middleware.KbRes;
import com.kaiba.lib.base.response.KbEntity;
import com.kaiba.m.core.domain.appversion.AppVersionVerify;
import com.kaiba.m.core.service.appversion.AppVersionVerifyService;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

/**
 * author: duanyf
 * date: 2024-06-25
 **/
@Slf4j
@RestController
public class AppVersionVerifyApiController {

    private final KbRes kbRes;
    private final AppVersionVerifyService appVersionVerifyService;

    public AppVersionVerifyApiController(
            KbRes kbRes,
            AppVersionVerifyService appVersionVerifyService
    ) {
        this.kbRes = kbRes;
        this.appVersionVerifyService = appVersionVerifyService;
    }

    @ApiOperation(value = "获取指定版本审核状态")
    @PostMapping(path = "/api/AppVersion/verify/getState")
    public KbEntity<Integer> getState(
            @RequestHeader(name = KbHeader.KB_EP) Integer endPoint,
            @RequestHeader(name = KbHeader.KB_VC) Integer versionCode,
            @RequestParam(name = "market") String market
    ) {
        AppVersionVerify model = new AppVersionVerify();
        model.setEndPoint(endPoint);
        model.setMarket(market);
        model.setVersionCode(versionCode);
        return kbRes.ok(appVersionVerifyService.getCacheState(model));
    }

    @ApiOperation(value = "返回审核状态的值固定为0")
    @RequestMapping(path = "/index/appVerify")
    public KbEntity<Integer> getZeroState() {
        return kbRes.ok(0);
    }
}
