package com.kaiba.m.core.controller.news.pool.knowledge;

import com.google.common.collect.Lists;
import com.kaiba.lib.base.constant.KbHeader;
import com.kaiba.lib.base.domain.news.pool.knowledge.BasedAnswerModel;
import com.kaiba.lib.base.domain.news.pool.knowledge.BasedAnswerQueryModel;
import com.kaiba.lib.base.domain.news.pool.knowledge.KnowledgeBaseModel;
import com.kaiba.lib.base.domain.news.pool.knowledge.KnowledgeCateQueryModel;
import com.kaiba.lib.base.domain.news.pool.knowledge.KnowledgeCategoryModel;
import com.kaiba.lib.base.domain.news.pool.knowledge.KnowledgeLogModel;
import com.kaiba.lib.base.domain.news.pool.knowledge.KnowledgeLogQueryModel;
import com.kaiba.lib.base.middleware.KbRes;
import com.kaiba.lib.base.response.KbEntity;
import com.kaiba.lib.base.response.KbException;
import com.kaiba.m.core.service.news.pool.knowledge.IBasedAnswerQueryService;
import com.kaiba.m.core.service.news.pool.knowledge.IKnowledgeBaseQueryService;
import com.kaiba.m.core.service.news.pool.knowledge.IKnowledgeCateQueryService;
import com.kaiba.m.core.service.news.pool.knowledge.IQuestionLogQueryService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.util.List;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.PageRequest;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * Description: 知识库后台查询
 * Author: ZM227
 * Date: 2025/2/10 15:45
 */
@Slf4j
@RestController
@RequestMapping("/admin/knowledge/query")
@Api(tags = "知识库后台查询")
public class KnowledgeAdminQueryController {

    @Resource
    private KbRes kbRes;
    @Resource
    private IBasedAnswerQueryService answerQueryService;
    @Resource
    private IQuestionLogQueryService logQueryService;
    @Resource
    private IKnowledgeCateQueryService cateQueryService;
    @Resource
    private IKnowledgeBaseQueryService baseQueryService;

    /**************** 知识点相关 ****************/
//    @ApiOperation(value = "通过关键字查询知识点，返回知识点内容", notes = "通过关键字查询知识点，返回知识点内容")
//    @PostMapping("/usr/queryKnowledgeContent")
//    public KbEntity<String> queryKnowledgeContent(
//        @RequestHeader(KbHeader.KB_USER_ID) Integer userId,
//        @RequestParam String keyWords) {
//        try {
//            NewsSearchConfig newsSearchConfig = new NewsSearchConfig();
//            newsSearchConfig.setFieldsType(FieldsType.KNOWLEDGE);
//            newsSearchConfig.setKeyWords(keyWords);
//            return kbRes.ok(knowledgeQueryService.queryKnowledgeContent(newsSearchConfig));
//        } catch (KbException e) {
//            return kbRes.err(e.getCode(), e.getMessage(), e.getReadableMessage());
//        }
//    }
//
//    @ApiOperation(value = "查询知识点列表", notes = "查询知识点列表")
//    @PostMapping("/usr/queryKnowledge")
//    public KbEntity<List<KnowledgeModel>> queryKnowledge(
//        @RequestHeader(KbHeader.KB_USER_ID) Integer userId,
//        @RequestBody KnowledgeQueryModel queryModel,
//        @RequestParam(required = false, defaultValue = "1") Integer page,
//        @RequestParam(required = false, defaultValue = "20") Integer pageSize) {
//        try {
//            NewsSearchConfig searchConfig = knowledgeConMapping.queryToSearchConfig(queryModel);
//            searchConfig.setFieldsType(FieldsType.KNOWLEDGE);
//            return kbRes.ok(
//                knowledgeQueryService.queryKnowledge(searchConfig, PageRequest.of(page - 1, pageSize)));
//        } catch (KbException e) {
//            return kbRes.err(e.getCode(), e.getMessage(), e.getReadableMessage());
//        }
//    }
//
//    @ApiOperation(value = "通过知识点id查询知识点", notes = "通过知识点id查询知识点")
//    @PostMapping("/usr/findKnowledgeById")
//    public KbEntity<KnowledgeModel> findKnowledgeById(
//        @RequestHeader(KbHeader.KB_USER_ID) Integer userId,
//        @RequestParam String knowledgeId) {
//        try {
//            return kbRes.ok(knowledgeQueryService.findKnowledgeById(knowledgeId));
//        } catch (KbException e) {
//            return kbRes.err(e.getCode(), e.getMessage(), e.getReadableMessage());
//        }
//    }

    /**************** 知识库实例相关 ****************/
    @ApiOperation(value = "通过知识库实例key查询知识库实例", notes = "通过关键字询问答案")
    @PostMapping("/usr/queryKnowledgeBase")
    public KbEntity<KnowledgeBaseModel> queryKnowledgeBase(
        @RequestHeader(KbHeader.KB_USER_ID) Integer userId, @RequestParam String baseKey) {
        try {
            return kbRes.ok(baseQueryService.findBaseByKey(baseKey));
        } catch (KbException e) {
            return kbRes.err(e.getCode(), e.getMessage(), e.getReadableMessage());
        }
    }

    /**************** 知识库常用答案相关 ****************/
    @ApiOperation(value = "通过关键字询问答案", notes = "通过关键字询问答案")
    @PostMapping("/usr/askBasedAnswer")
    public KbEntity<BasedAnswerModel> askBasedAnswer(
        @RequestHeader(KbHeader.KB_USER_ID) Integer userId,
        @RequestBody BasedAnswerQueryModel queryModel) {
        try {
            return kbRes.ok(answerQueryService.queryBasedAnswer(queryModel.getBaseId(),
                Lists.newArrayList(queryModel.getCategoryId()), queryModel.getKeyWord()));
        } catch (KbException e) {
            return kbRes.err(e.getCode(), e.getMessage(), e.getReadableMessage());
        }
    }

    @ApiOperation(value = "查询知识库常用答案列表", notes = "查询知识库常用答案列表")
    @PostMapping("/usr/queryBasedAnswer")
    public KbEntity<List<BasedAnswerModel>> queryBasedAnswer(
        @RequestHeader(KbHeader.KB_USER_ID) Integer userId,
        @RequestBody BasedAnswerQueryModel queryModel,
        @RequestParam(required = false, defaultValue = "1") Integer page,
        @RequestParam(required = false, defaultValue = "20") Integer pageSize) {
        try {
            queryModel.setPageable(PageRequest.of(page - 1, pageSize));
            return kbRes.ok(answerQueryService.findAnswersByCondition(queryModel));
        } catch (KbException e) {
            return kbRes.err(e.getCode(), e.getMessage(), e.getReadableMessage());
        }
    }

    @ApiOperation(value = "通过知识点id查询知识点", notes = "通过知识点id查询知识点")
    @PostMapping("/usr/findBasedAnswerById")
    public KbEntity<BasedAnswerModel> findBasedAnswerById(
        @RequestHeader(KbHeader.KB_USER_ID) Integer userId,
        @RequestParam String answerId) {
        try {
            return kbRes.ok(answerQueryService.findBasedAnswerById(answerId));
        } catch (KbException e) {
            return kbRes.err(e.getCode(), e.getMessage(), e.getReadableMessage());
        }
    }

    /**************** 知识分类相关 ****************/
    @ApiOperation(value = "查询知识库分类", notes = "查询知识库分类")
    @PostMapping("/usr/queryKnowledgeCategory")
    public KbEntity<List<KnowledgeCategoryModel>> queryKnowledgeCategory(
        @RequestHeader(KbHeader.KB_USER_ID) Integer userId,
        @RequestParam(required = false) Integer cateType,
        @RequestParam(required = false) String name,
        @RequestParam(required = false, defaultValue = "1") Integer page,
        @RequestParam(required = false, defaultValue = "20") Integer pageSize) {
        try {
            KnowledgeCateQueryModel queryModel = new KnowledgeCateQueryModel();
            queryModel.setPageable(PageRequest.of(page - 1, pageSize));
            queryModel.setCategoryType(cateType);
            queryModel.setStatus(1);
            queryModel.setName(name);
            return kbRes.ok(cateQueryService.queryKnowledgeCategory(queryModel));
        } catch (KbException e) {
            return kbRes.err(e.getCode(), e.getMessage(), e.getReadableMessage());
        }
    }

    /**************** 知识查询日志相关 ****************/
    @ApiOperation(value = "查询知识库查询日志", notes = "查询知识库查询日志")
    @PostMapping("/usr/queryKnowledgeLog")
    public KbEntity<List<KnowledgeLogModel>> queryKnowledgeLog(
        @RequestHeader(KbHeader.KB_USER_ID) Integer userId,
        @RequestBody KnowledgeLogQueryModel queryModel,
        @RequestParam(required = false, defaultValue = "1") Integer page,
        @RequestParam(required = false, defaultValue = "20") Integer pageSize) {
        try {
            queryModel.setPageable(PageRequest.of(page - 1, pageSize));
            return kbRes.ok(logQueryService.findLogsByCondition(queryModel));
        } catch (KbException e) {
            return kbRes.err(e.getCode(), e.getMessage(), e.getReadableMessage());
        }
    }

}
