package com.kaiba.m.core.controller.user;

import com.kaiba.lib.base.annotation.apiparam.KbUserId;
import com.kaiba.lib.base.domain.user.UserModel;
import com.kaiba.lib.base.response.KbEntity;
import com.kaiba.lib.base.service.IUserService;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Set;

/**
 * author: lyux
 * date: 2022-03-14
 */
@Slf4j
@RestController()
@RequestMapping("/n/user/blacklist")
public class UserBlacklistApiController {

    private final IUserService userService;

    public UserBlacklistApiController(IUserService userService) {
        this.userService = userService;
    }

    @ApiOperation("是否在用户黑名单列表内")
    @PostMapping(path = "/isInUserBlacklist")
    public KbEntity<Boolean> isInUserBlacklist(
            @RequestHeader() @KbUserId Integer kb_uid,
            @RequestParam() @KbUserId Integer targetUserId)
    {
        return userService.isInUserBlacklist(kb_uid, targetUserId, true);
    }

    @ApiOperation("获取用户黑名单列表")
    @PostMapping(path = "/getUserBlacklist")
    public KbEntity<Set<Integer>> getUserBlacklist(
            @RequestHeader() Integer kb_uid)
    {
        return userService.getUserBlacklist(kb_uid, true);
    }

    @ApiOperation("获取用户黑名单列表")
    @PostMapping(path = "/getUserBlacklistAsUser")
    public KbEntity<List<UserModel>> getUserBlacklistAsUser(
            @RequestHeader() @KbUserId Integer kb_uid)
    {
        return userService.getUserBlacklistAsUser(kb_uid, true);
    }

    @ApiOperation("将用户添加至黑名单列表")
    @PostMapping(path = "/addToUserBlacklist")
    public KbEntity<Void> addToUserBlacklist(
            @RequestHeader() @KbUserId Integer kb_uid,
            @RequestParam() @KbUserId Integer targetUserId
    ) {
        return userService.addToUserBlacklist(kb_uid, targetUserId);
    }

    @ApiOperation("从黑名单中移除用户")
    @PostMapping(path = "/removeFromUserBlacklist")
    public KbEntity<Void> removeFromUserBlacklist(
            @RequestHeader() @KbUserId Integer kb_uid,
            @RequestParam() @KbUserId Integer targetUserId
    ) {
        return userService.removeFromUserBlacklist(kb_uid, targetUserId);
    }

}
