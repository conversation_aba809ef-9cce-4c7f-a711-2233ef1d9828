package com.kaiba.m.core.controller.circle;

import com.kaiba.lib.base.assembler.Mapper;
import com.kaiba.lib.base.constant.KbProperties;
import com.kaiba.lib.base.constant.circle.CircleCategoryType;
import com.kaiba.lib.base.constant.circle.CircleState;
import com.kaiba.lib.base.constant.circle.CircleType;
import com.kaiba.lib.base.domain.circle.*;
import com.kaiba.lib.base.domain.note.NoteCreateModel;
import com.kaiba.lib.base.domain.note.NoteModel;
import com.kaiba.lib.base.domain.note.NoteThreadModel;
import com.kaiba.lib.base.domain.tmuyun.tmuyun.ArticleApiModel;
import com.kaiba.lib.base.domain.user.UserModel;
import com.kaiba.lib.base.middleware.KbRes;
import com.kaiba.lib.base.response.KbCode;
import com.kaiba.lib.base.response.KbEntity;
import com.kaiba.lib.base.response.KbException;
import com.kaiba.lib.base.service.*;
import com.kaiba.lib.base.util.StringUtils;
import com.kaiba.m.core.domain.circle.CircleCategory;
import com.kaiba.m.core.domain.circle.CircleThread;
import com.kaiba.m.core.service.circle.CircleApiService;
import com.kaiba.m.core.service.circle.CircleModelHelper;
import com.kaiba.m.core.service.circle.CircleService;
import com.kaiba.m.core.service.circle.CircleThreadService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.RestController;

import java.util.*;
import java.util.stream.Collectors;

/**
 * author: lyux
 * date: 18-10-3
 */
@Slf4j
@RestController
public class CircleServiceController implements ICircleService {

    private final KbRes kbRes;
    private final CircleService circleService;
    private final CircleApiService circleApiService;
    private final CircleThreadService circleThreadService;
    private final CircleModelHelper circleModelHelper;
    private final INoteService noteService;
    private final IUserService userService;
    private final IVerifyService verifyService;
    private final ITmuyunService tmuyunService;

    @Autowired
    public CircleServiceController(
            KbRes kbRes,
            CircleService circleService,
            CircleApiService circleApiService,
            CircleThreadService circleThreadService,
            CircleModelHelper circleModelHelper,
            INoteService noteService,
            IUserService userService,
            IVerifyService verifyService,
            ITmuyunService tmuyunService
    ) {
        this.kbRes = kbRes;
        this.circleService = circleService;
        this.circleApiService = circleApiService;
        this.circleThreadService = circleThreadService;
        this.circleModelHelper = circleModelHelper;
        this.noteService = noteService;
        this.userService = userService;
        this.verifyService = verifyService;
        this.tmuyunService = tmuyunService;
    }

    // --------------------------------------------------------------------------
    // 新版圈子接口

    @Override
    public KbEntity<Void> initializeSiteCircle(Integer userId, Integer siteId) {
        circleThreadService.getOrInitMainCircle(siteId);
        circleThreadService.getOrInitRecommendCircle(siteId);
        return kbRes.ok();
    }

    @Override
    public KbEntity<CircleThreadModel> createCircle(Integer userId, CircleThreadCreateModel model) {
        return kbRes.ok(circleThreadService.createCircleTopic(userId, model));
    }

    @Override
    public KbEntity<CircleThreadModel> updateCircle(
            Integer userId, String circleId, String title,
            String topicAvatar,String hotTopicImage, String hotTopicColor) {
        CircleThread circle = circleThreadService.getCircleById(circleId)
                .orElseThrow(() -> new KbException(KbCode.RESOURCE_NOT_FOUND).li());
        if (!StringUtils.isEmpty(title)) {
            circleThreadService.updateCircleTitle(circle, title);
        }
        if (!StringUtils.isEmpty(hotTopicImage)
                || !StringUtils.isEmpty(hotTopicColor)
                || !StringUtils.isEmpty(topicAvatar)) {
            circleThreadService.updateCircleHotTopicBkg(circle, topicAvatar, hotTopicImage, hotTopicColor);
        }
        return getCircleById(circleId);
    }

    @Override
    public KbEntity<CircleThreadModel> updateCircleShare(
            Integer userId, String circleId, String title, String content, String image, String url) {
        circleThreadService.updateCircleShare(circleId, title, content, image, url);
        return getCircleById(circleId);
    }

    @Override
    public KbEntity<NoteThreadModel> removeCircle(Integer userId, String circleId) {
        CircleThread circle = circleThreadService.getCircleById(circleId)
                .orElseThrow(() -> new KbException(KbCode.RESOURCE_NOT_FOUND).li());
        NoteThreadModel thread = circleThreadService.removeCircleTopic(circle);
        circleThreadService.removeCategoryByCircleId(circleId);
        return kbRes.ok(thread);
    }

    @Override
    public KbEntity<Void> updateCircleState(Integer userId, String circleId, Integer state) {
        CircleState s = CircleState.valueOf(state).orElseThrow(
                () -> new KbException(KbCode.REQUEST_PARAM_INVALID, "unknown state: " + state));
        CircleThread circle = circleThreadService.getCircleById(circleId)
                .orElseThrow(() -> new KbException(KbCode.RESOURCE_NOT_FOUND).li());
        if (!state.equals(circle.getState())) {
            circleThreadService.updateCircleState(circleId, s);
        }
        return kbRes.ok();
    }

    @Override
    public KbEntity<NoteModel> createCircleContent(Integer userId, NoteCreateModel model) {
        String circleId = model.getReferenceId();
        if (circleId == null) {
            return kbRes.err(KbCode.REQUEST_PARAM_INVALID, "referenceId null");
        }
        CircleThread circle = circleThreadService.getCircleById(circleId)
                .orElseThrow(() -> new KbException(KbCode.RESOURCE_NOT_FOUND).li());
        String contentThreadId = circleThreadService.getOrInitContentThread(circleId);
        if (!contentThreadId.equals(model.getOriginThreadId())
                && (model.getThreads() == null || !model.getThreads().contains(contentThreadId))) {
            if (model.getOriginThreadId() == null) {
                model.setOriginThreadId(contentThreadId);
            } else {
                if (model.getThreads() == null || model.getThreads().size() == 0) {
                    model.setThreads(Collections.singletonList(contentThreadId));
                } else {
                    List<String> threadIds = new ArrayList<>(model.getThreads().size() + 1);
                    threadIds.addAll(model.getThreads());
                    threadIds.add(contentThreadId);
                    model.setThreads(threadIds);
                }
            }
        }
        model.setIsHonorCondition(false);
        return noteService.noteCreateByBody(model)
                .peek(note -> circleThreadService.updateCircleNote(circleId, note.getId()))
                .peek(note -> {
                    // 每次圈子话题的内容更换时, 重新投递省宣稿件
                    ArticleApiModel article = circleModelHelper.circle2tmu(circle, note);
                    if (article != null && tmuyunService.sendArticle(article).isOk()) {
                        log.info("send circle as article to tmu success: " + article);
                    }
                });
    }

    @Override
    public KbEntity<Void> upsertCircleCategory(
            Integer userId, String circleId, Integer siteId, Integer categoryType, Integer targetUserId, Long order) {
        CircleCategoryType type = CircleCategoryType.resolveByValue(categoryType).orElseThrow(() ->
                new KbException(KbCode.REQUEST_PARAM_INVALID, "unknown type: " + categoryType).li());
        if (type == CircleCategoryType.TAB_USER_SUB) {
            circleThreadService.upsertUserSubCircleCategory(siteId, circleId, targetUserId);
        } else if (type == CircleCategoryType.TOPIC_MODERATOR) {
            circleThreadService.upsertCircleCategoryByUser(
                    CircleCategoryType.TOPIC_MODERATOR, siteId, circleId, targetUserId);
        } else {
            circleThreadService.upsertSiteCircleCategory(siteId, circleId, type);
        }
        return kbRes.ok();
    }

    @Override
    public KbEntity<Void> updateCircleCategoryOrder(Integer userId, CircleOrderUpdateModel model) {
        circleThreadService.updateCircleOrderForSite(model);
        return kbRes.ok();
    }

    @Override
    public KbEntity<Void> removeCircleFromCategory(
            Integer userId, String circleId, Integer siteId, Integer categoryType, Integer targetUserId) {
        CircleCategoryType type = CircleCategoryType.resolveByValue(categoryType).orElseThrow(() ->
                new KbException(KbCode.REQUEST_PARAM_INVALID, "unknown type: " + categoryType).li());
        CircleThread circle = circleThreadService.getCircleById(circleId)
                .orElseThrow(() -> new KbException(KbCode.RESOURCE_NOT_FOUND).li());
        if (circle.getType() == CircleType.PLAZA.getValue()) {
            if (type == CircleCategoryType.TAB_HEAD) {
                return kbRes.err(KbCode.REQUEST_PARAM_INVALID,
                        "main circle should not be removed from tab head",
                        "不能将主板块从 TAB 中移除");
            }
        }
        circleThreadService.removeCircleFromCategory(targetUserId, circleId, siteId, type);
        return kbRes.ok();
    }

    @Override
    public KbEntity<CircleThreadModel> getCircleById(String circleId) {
        return circleThreadService.getCircleById(circleId)
                .map(circleModelHelper::circle2model)
                .map(kbRes::ok)
                .orElseGet(() -> kbRes.err(KbCode.RESOURCE_NOT_FOUND))
                .peek(circle -> circle.setShareModel(circleModelHelper.createCircleShareModel(circle)))
                .peek(circle -> circle.setCircleGetuiType(circleModelHelper.getCircleGetuiType(circleId, circle.getType()).getValue()));
    }

    @Override
    public KbEntity<CircleThreadModel> getCircleByThreadId(String threadId) {
        return circleThreadService.getCircleByThread(threadId)
                .map(circleModelHelper::circle2model)
                .map(kbRes::ok)
                .orElseGet(() -> kbRes.err(KbCode.RESOURCE_NOT_FOUND))
                .peek(circle -> circle.setShareModel(circleModelHelper.createCircleShareModel(circle)))
                .peek(circle -> circle.setCircleGetuiType(circleModelHelper.getCircleGetuiType(circle.getCircleId(), circle.getType()).getValue()));
    }

    @Override
    public KbEntity<CircleThreadModel> getPlazaCircleBySite(Integer siteId) {
        CircleThread circle = circleThreadService.getOrInitMainCircle(siteId);
        return kbRes.ok(circleModelHelper.circle2model(circle));
    }

    @Override
    public KbEntity<CircleThreadModel> getRecommendCircleBySite(Integer siteId) {
        CircleThread circle = circleThreadService.getOrInitRecommendCircle(siteId);
        return kbRes.ok(circleModelHelper.circle2model(circle));
    }

    @Override
    public KbEntity<List<CircleThreadModel>> getCircleListBySite(
            Integer siteId, Integer categoryType, Integer circleType, Integer page, Integer pageSize) {
        if (categoryType == null) {
            Page<CircleThread> circlePage = circleThreadService
                    .getCirclePageBySite(siteId, circleType, page, pageSize);
            return kbRes.ok(circleModelHelper.circle2page(circlePage));
        } else {
            CircleCategoryType type = CircleCategoryType.resolveByValue(categoryType).orElseThrow(
                    () -> new KbException(KbCode.REQUEST_PARAM_INVALID, "unknown category: " + categoryType).li());
            if (type.isRequireUser()) {
                return kbRes.err(KbCode.REQUEST_PARAM_INVALID, "should call getCategoryCircleListByUser()");
            } else {
                Page<CircleCategory> categoryPage = circleThreadService
                        .getCategoryPageBySite(siteId, type, page, pageSize);
                return kbRes.ok(circleModelHelper.category2circlePage(categoryPage));
            }
        }
    }

    @Override
    public KbEntity<List<CircleThreadModel>> getCircleListByCircleIdIn(String[] circleIds) {
        if (circleIds == null || circleIds.length == 0) {
            return kbRes.ok(Collections.emptyList());
        } else {
            return kbRes.ok(circleThreadService.getCircleListIn(Arrays.asList(circleIds)).stream()
                    .map(circleModelHelper::circle2model).collect(Collectors.toList()));
        }
    }

    @Override
    public KbEntity<List<CircleThreadModel>> getCircleListByThreadIdIn(String[] threadIds) {
        if (threadIds == null || threadIds.length == 0) {
            return kbRes.ok(Collections.emptyList());
        } else {
            return kbRes.ok(circleThreadService.getCircleListByThreadIn(Arrays.asList(threadIds)).stream()
                    .map(circleModelHelper::circle2model).collect(Collectors.toList()));
        }
    }

    @Override
    public KbEntity<List<CircleThreadModel>> getCircleListByTitleLike(
            Integer siteId, String title, Integer page, Integer pageSize) {
        Page<CircleThread> circlePage = circleThreadService
                .getBySiteIdAndTypeAndTitleLike(siteId, title, page, pageSize);
        return kbRes.ok(circleModelHelper.circle2page(circlePage));
    }

    @Override
    public KbEntity<List<CircleThreadModel>> getCategoryCircleListByUser(
            Integer siteId, Integer userId, Integer categoryType, Integer page, Integer pageSize) {
        CircleCategoryType type = CircleCategoryType.resolveByValue(categoryType).orElseThrow(
                () -> new KbException(KbCode.REQUEST_PARAM_INVALID, "unknown category: " + categoryType).li());
        if (!type.isRequireUser()) {
            return kbRes.msg(KbCode.REQUEST_PARAM_INVALID, "类型错误");
        }
        Page<CircleCategory> categoryPage = circleThreadService
                .getCategoryPageByUser(CircleCategoryType.TAB_USER_SUB, siteId, userId, page, pageSize);
        return kbRes.ok(circleModelHelper.category2circlePage(categoryPage));
    }

    @Override
    public KbEntity<List<UserModel>> getUserListByCategoryCircle(
            String circleId, Integer categoryType, Integer page, Integer pageSize) {
        CircleCategoryType type = CircleCategoryType.resolveByValue(categoryType).orElseThrow(
                () -> new KbException(KbCode.REQUEST_PARAM_INVALID, "unknown category: " + categoryType).li());
        if (!type.isRequireUser()) {
            return kbRes.msg(KbCode.REQUEST_PARAM_INVALID, "类型错误");
        }
        Page<CircleCategory> categoryPage = circleThreadService.getCategoryPageByCircle(circleId, type, page, pageSize);
        Set<Integer> userIds = categoryPage.getContent().stream()
                .map(CircleCategory::getUserId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());
        Map<Integer, UserModel> userMap = userService.getBasicMapByIds(userIds).dataOrThrow();
        return kbRes.ok(categoryPage.map(category -> userMap.get(category.getUserId())));
    }

    @Override
    public KbEntity<List<CircleTopicModel>> getTopicListBySite(
            Integer siteId, Integer page, Integer pageSize) {
        int p = page == null ? 0 : page - 1;
        int ps = pageSize == null ? 20 : pageSize;
        List<CircleTopicModel> list = circleApiService.getTopicCircleListBySite(siteId, p, ps);
        return kbRes.ok(list);
    }

    @Override
    public KbEntity<List<CircleThreadModel>> searchCircleByTitle(
            Integer siteId, String keyword, Integer circleType, Integer state, Integer page, Integer pageSize) {
        Page<CircleThread> circlePage = circleThreadService
                .searchPageByTitle(siteId, keyword, circleType, state, page, pageSize);
        return kbRes.ok(circleModelHelper.circle2page(circlePage));
    }

    @Override
    public KbEntity<List<CircleCategoryModel>> getCategoryList(
            Integer siteId, Integer type, Integer page, Integer pageSize) {
        CircleCategoryType categoryType = CircleCategoryType.resolveByValue(type).orElse(null);
        if (siteId == null) {
            return kbRes.ok(circleThreadService.getCategoryPage(categoryType, page, pageSize)
                    .map(category -> Mapper.map(category, CircleCategoryModel.class)));
        } else {
            return kbRes.ok(circleThreadService.getCategoryPageBySite(siteId, categoryType, page, pageSize)
                    .map(category -> Mapper.map(category, CircleCategoryModel.class)));
        }
    }

    // --------------------------------------------------------------------------
    // 老版圈子接口

    @Override
    public KbEntity<List<NoteThreadModel>> getThreadListBySite(
            Integer siteId, Integer state, Integer page, Integer pageSize) {
        List<String> threadIds = circleService.getCircleThreadListBySite(siteId, page, pageSize).stream()
                .peek(circleThread -> {
                    if (circleThread.getState() == null) {
                        circleThread.setState(CircleState.SHOW.getValue());
                    }
                })
                .filter(circleThread -> state == null || state.equals(circleThread.getState()))
                .map(CircleThread::getThreadId)
                .collect(Collectors.toList());
        List<NoteThreadModel> modelList = new ArrayList<>(threadIds.size());
        Map<String, NoteThreadModel> modelMap = noteService.getThreadMapByIdSet(new HashSet<>(threadIds)).dataOrThrow();
        for (String threadId : threadIds) {
            modelList.add(modelMap.get(threadId));
        }
        return kbRes.ok(modelList);
    }

    @Override
    public KbEntity<List<CircleThreadModel>> getCircleThreadListBySite(
            Integer siteId, Integer page, Integer pageSize) {
        if (pageSize < 1) {
            return kbRes.err(KbCode.REQUEST_PARAM_INVALID, "pageSize should be positive");
        }
        List<CircleThread> circleThreadList = circleService.getCircleThreadListBySite(siteId, page, pageSize);
        if (page == 1 && circleThreadList.size() == 0) {
            // 该地市不存在广场, 自动创建
            NoteThreadModel createThread = new NoteThreadModel();
            createThread.setSiteId(siteId);
            createThread.setTitle(CircleType.PLAZA.getTitle());
            createThread.setDescription(CircleType.PLAZA.getDescription());
            NoteThreadModel thread = noteService.createThreadByBody(createThread).dataOrThrow();
            log.info("no thread for site " + siteId + ", auto create plaza thread: " + thread);
            CircleThread circleThread = circleService.addThreadToCircle(
                    null, siteId, thread.getId(), CircleType.PLAZA, null);
            verifyService.createNoteThreadVerify(siteId, "车友圈", "车友圈全部帖子板块", thread.getId(), KbProperties.SYSTEM_USER_ID);
            return kbRes.ok(Collections.singletonList(CircleModelHelper.circle2model(circleThread, thread)));
        } else {
            String[] threadIds = circleThreadList.stream().map(CircleThread::getThreadId).toArray(String[]::new);
            List<NoteThreadModel> threadList = noteService.getThreadDetailListIn(threadIds).dataOrThrow();
            List<CircleThreadModel> modelList = new ArrayList<>(threadList.size());
            for (CircleThread circleThread : circleThreadList) {
                NoteThreadModel thread = null;
                for (NoteThreadModel t : threadList) {
                    if (t.getId().equals(circleThread.getThreadId())) {
                        thread = t;
                        break;
                    }
                }
                if (thread != null) {
                    modelList.add(CircleModelHelper.circle2model(circleThread, thread));
                }
            }
            return kbRes.ok(modelList);
        }
    }

    @Override
    public KbEntity<NoteThreadModel> createThreadToCircle(
            Integer userId, Integer siteId, String title, String description,
            Integer hotMax, Integer topMax, Integer condition, Integer order, Integer type) {
        CircleType circleType = CircleType.valueOf(type).orElse(CircleType.FIXED);
        NoteThreadModel createThread = new NoteThreadModel();
        createThread.setCreatorId(userId);
        createThread.setSiteId(siteId);
        createThread.setTitle(title);
        createThread.setDescription(description);
        createThread.setHotMax(hotMax);
        createThread.setTopMax(topMax);
        createThread.setCondition(condition);
        NoteThreadModel thread = noteService.createThreadByBody(createThread).dataOrThrow();
        circleService.addThreadToCircle(userId, siteId, thread.getId(), circleType, order);
        circleService.getMainCircleBySite(siteId).ifPresent(mainCircle ->
                noteService.updateThreadRoute(userId, thread.getId(), new String[]{mainCircle.getThreadId()}));
        return kbRes.ok(thread);
    }

    @Override
    public KbEntity<String> addThreadToCircle(Integer userId, Integer siteId, String threadId, Integer order, Integer type) {
        CircleType circleType = CircleType.valueOf(type).orElse(CircleType.UNFIXED);
        circleService.addThreadToCircle(userId, siteId, threadId, circleType, order);
        circleService.getMainCircleBySite(siteId).ifPresent(mainCircle ->
                noteService.updateThreadRoute(userId, threadId, new String[]{mainCircle.getThreadId()}));
        return kbRes.ok();
    }

    @Override
    public KbEntity<String> removeThreadFromCircle(Integer userId, Integer siteId, String threadId) {
        circleService.removeThreadToCircle(userId, siteId, threadId);
        return kbRes.ok();
    }

    @Override
    public KbEntity<String> changeThreadOrder(Integer userId, Integer siteId, String threadId, Integer order) {
        circleService.updateCircleThreadOrder(siteId, threadId, order);
        return kbRes.ok();
    }

    @Override
    public KbEntity<String> updateCircleThreadState(Integer userId, Integer siteId, String threadId, Integer state) {
        CircleState circleState = CircleState.valueOf(state).orElseThrow(() -> new KbException(
                KbCode.REQUEST_PARAM_INVALID, "update circle thread with wrong state: " + state).li());
        circleService.updateCircleThreadState(siteId, threadId, circleState);
        return kbRes.ok();
    }

    @Override
    public KbEntity<String> updateThread(
            Integer userId, Integer siteId, String threadId,
            String title, Integer condition, Integer hotMax, Integer topMax, Integer order) {
        circleService.updateCircleThreadOrder(siteId, threadId, order);
        noteService.updateThreadBasic(userId, threadId, title, hotMax, topMax, null, condition).check();
        return kbRes.ok();
    }

    // -----------------------------------------------------------------------------

}
