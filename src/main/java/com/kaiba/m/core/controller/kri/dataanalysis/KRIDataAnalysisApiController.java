package com.kaiba.m.core.controller.kri.dataanalysis;

import com.kaiba.lib.base.constant.kri.KRIMatcherFact;
import com.kaiba.lib.base.domain.kri.KbResource;
import com.kaiba.lib.base.domain.kri.fact.dataanalysis.DAChannel;
import com.kaiba.lib.base.domain.kri.fact.dataanalysis.DAGetuiBizTag;
import com.kaiba.lib.base.middleware.KbRes;
import com.kaiba.lib.base.response.KbEntity;
import com.kaiba.m.core.service.kri.matcher.MatcherMatchService;
import com.kaiba.m.core.util.JsonUtils;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * author: lyux
 * date: 2024-12-19
 */
@Slf4j
@RestController
@RequestMapping("/api/resource/stat")
public class KRIDataAnalysisApiController {

    private final KbRes kbRes;
    private final MatcherMatchService matchService;

    public KRIDataAnalysisApiController(KbRes kbRes, MatcherMatchService matchService) {
        this.kbRes = kbRes;
        this.matchService = matchService;
    }

    @ApiOperation("获取对象信息")
    @PostMapping("/getMatchedResourceInfo")
    public KbEntity<GetuiResInfo> getMatchedResourceInfo(
            @RequestParam() String biz,
            @RequestParam(required = false) String unit,
            @RequestParam(required = false) String ref1,
            @RequestParam(required = false) String ref2,
            @RequestParam(required = false) String ref3
    ) {
        KbResource resource = new KbResource(biz, unit, ref1, ref2, ref3);
        GetuiResInfo info = new GetuiResInfo();
        matchService.matchByFact(resource, KRIMatcherFact.DA_GETUI_TAG.name()).ifPresent(tagData -> {
            DAGetuiBizTag bizTag = JsonUtils.toModel(tagData, DAGetuiBizTag.class);
            info.setDaTags(bizTag.getTags());
        });
        matchService.matchByFact(resource, KRIMatcherFact.DA_CHANNEL.name()).ifPresent(channelData -> {
            DAChannel bizChannel = JsonUtils.toModel(channelData, DAChannel.class);
            info.setChannel(bizChannel.getChannel());
            info.setDepart(bizChannel.getDepart());
        });
        return kbRes.ok(info);
    }

    @ApiOperation("获取对象所属频率频道信息")
    @PostMapping("/getOwnerInfo")
    public KbEntity<GetuiResInfo> getOwnerInfo(
            @RequestParam() String biz,
            @RequestParam(required = false) String unit,
            @RequestParam(required = false) String ref1,
            @RequestParam(required = false) String ref2,
            @RequestParam(required = false) String ref3
    ) {
        KbResource resource = new KbResource(biz, unit, ref1, ref2, ref3);
        GetuiResInfo info = new GetuiResInfo();
        matchService.matchByFact(resource, KRIMatcherFact.DA_CHANNEL.name()).ifPresent(channelData -> {
            DAChannel bizChannel = JsonUtils.toModel(channelData, DAChannel.class);
            info.setChannel(bizChannel.getChannel());
            info.setDepart(bizChannel.getDepart());
        });
        return kbRes.ok(info);
    }

}
