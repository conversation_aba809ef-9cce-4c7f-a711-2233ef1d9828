package com.kaiba.m.core.controller.app.selection;

import com.kaiba.lib.base.annotation.apiparam.KbSiteId;
import com.kaiba.lib.base.constant.KbHeader;
import com.kaiba.lib.base.constant.KbModule;
import com.kaiba.lib.base.constant.apphome.AppHomeSelectionTarget;
import com.kaiba.lib.base.domain.appselection.SelectionApiModel;
import com.kaiba.lib.base.domain.appselection.SelectionListModel;
import com.kaiba.lib.base.domain.da.sensors.pgc.SensorsPGCObjUserActModel;
import com.kaiba.lib.base.middleware.KbRes;
import com.kaiba.lib.base.response.KbEntity;
import com.kaiba.lib.base.service.ISensorsService;
import com.kaiba.m.core.service.appcomponent.selection.SelectionCacheService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.stream.Collectors;

/**
 * author: lyux
 * date: 2023-08-09
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/api/HomeSelection")
public class AppHomeSelectionApiController {

    private final KbRes kbRes;
    private final SelectionCacheService selectionCacheService;

    private final ISensorsService sensorsService;

    public AppHomeSelectionApiController(
            @Qualifier("kbApiRes") KbRes kbRes,
            SelectionCacheService selectionCacheService,
            ISensorsService sensorsService
    ) {
        this.kbRes = kbRes;
        this.selectionCacheService = selectionCacheService;
        this.sensorsService = sensorsService;
    }

    @PostMapping("/getRecommendBest")
    public KbEntity<SelectionListModel> getRecommendBest(
            @RequestParam() @KbSiteId Integer siteId
    ) {
        String target = AppHomeSelectionTarget.HOME_RECOMMEND_BEST.name();
        SelectionListModel model = selectionCacheService
                .getSelectionList(siteId, target, null, 1, 20);
        return kbRes.ok(model);
    }

    @PostMapping("/getRecommendList")
    public KbEntity<SelectionListModel> getSelectionList(
            @RequestHeader(KbHeader.KB_VC) Integer vc,
            @RequestParam() @KbSiteId Integer siteId,
            @RequestParam(required = false) String lastMark,
            @RequestParam(required = false, defaultValue = "1") Integer page,
            @RequestParam(required = false, defaultValue = "20") Integer pageSize
    ) {
        String target = AppHomeSelectionTarget.HOME_RECOMMEND_LIST.name();
        SelectionListModel model = selectionCacheService
                .getSelectionList(siteId, target, lastMark, page, pageSize);

        // TODO: 7.1.0 之前的版本不支持腰封模式. 待 7.1.0 普及后可去掉这段代码
        if (vc < 70100) {
            List<SelectionApiModel> list = model.getItems().stream()
                    .filter(item -> item.getImageBelt() == null)
                    .collect(Collectors.toList());
            model.setItems(list);
        }

        model.getItems().forEach(item -> sensorsService.calculateThenSet(convert2SensorsModel(item), item, false));

        return kbRes.ok(model);
    }

    // ---------------------

    private SensorsPGCObjUserActModel convert2SensorsModel(SelectionApiModel model) {
        String module = mapKbModule(model.getOrigin());
        if (module == null) {
            return null;
        }
        SensorsPGCObjUserActModel actModel = new SensorsPGCObjUserActModel();
        actModel.setBusiness(module);
        //爆料台推荐池配置顺序特殊
        if (module.equals(KbModule.REVEAL.name())) {
            actModel.setRef2(model.getRef1());
            actModel.setRef1(model.getRef2());
            actModel.setRef3(model.getRef3());
        } else {
            actModel.setRef1(model.getRef1());
            actModel.setRef2(model.getRef2());
            actModel.setRef3(model.getRef3());
        }

        return actModel;
    }

    private String mapKbModule(String origin) {
        switch (origin) {
            case "NEWS":
                return KbModule.NEWS.name();
            case "REVEAL_TOPIC":
                return KbModule.REVEAL.name();
            default:
                return null;
        }
    }

}
