package com.kaiba.m.core.controller.push;

import com.google.gson.reflect.TypeToken;
import com.kaiba.lib.base.assembler.Mapper;
import com.kaiba.lib.base.constant.KbEndpoint;
import com.kaiba.lib.base.constant.push.PushRange;
import com.kaiba.lib.base.constant.push.PushType;
import com.kaiba.lib.base.domain.push.PushModel;
import com.kaiba.lib.base.domain.push.PushTargetModel;
import com.kaiba.lib.base.middleware.KbRes;
import com.kaiba.lib.base.response.KbCode;
import com.kaiba.lib.base.response.KbEntity;
import com.kaiba.lib.base.response.KbException;
import com.kaiba.lib.base.service.IPushService;
import com.kaiba.lib.base.util.StringUtils;
import com.kaiba.m.core.domain.push.PushTarget;
import com.kaiba.m.core.domain.push.PushTask;
import com.kaiba.m.core.service.push.PushService;
import com.kaiba.m.core.service.push.PushTargetService;
import com.kaiba.m.core.util.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * author: lyux
 * date: 18-12-7
 */
@Slf4j
@RestController
public class PushServiceController implements IPushService {

    private final KbRes kbRes;
    private final PushService pushService;
    private final PushTargetService pushTargetService;

    @Autowired
    public PushServiceController(
            KbRes kbRes,
            PushService pushService,
            PushTargetService pushTargetService
    ) {
        this.kbRes = kbRes;
        this.pushService = pushService;
        this.pushTargetService = pushTargetService;
    }

    @Override
    public KbEntity<Void> updatePushTarget(
            Integer userId, Integer siteId, Integer source, String deviceNumber,
            String pushToken, Integer endpoint, String deviceType,
            String jPushRegId, String jPushVersion, String jPushCoreVersion, Integer devicePushConfig,
            Integer pushChannel
    ) {
        if (StringUtils.isEmpty(deviceNumber) || StringUtils.isEmpty(pushToken)) {
            return kbRes.err(KbCode.REQUEST_PARAM_INVALID, "deviceNumber or pushToken is empty");
        }
        if (!"ios".equalsIgnoreCase(deviceType) && !"android".equalsIgnoreCase(deviceType) && !"ohos".equalsIgnoreCase(deviceType)) {
            return kbRes.err(KbCode.REQUEST_PARAM_INVALID, "wrong deviceType");
        }
        PushTarget target = new PushTarget();
        target.setUserId(userId);
        target.setSiteId(siteId);
        target.setSource(source);
        target.setDeviceNumber(deviceNumber);
        target.setPushToken(pushToken);
        target.setEndpoint(endpoint);
        target.setDeviceType(deviceType);
        target.setJPushRegId(jPushRegId);
        target.setJPushVersion(jPushVersion);
        target.setJPushCoreVersion(jPushCoreVersion);
        target.setDevicePushConfig(devicePushConfig);

        target.setPushChannel(pushChannel);
        pushTargetService.upsert(target);
        return kbRes.ok();
    }

    @Override
    public KbEntity<PushModel> add(
            String title, String subTitle, String action, String actionParams, String targetId, Integer type,
            Integer range, String referenceId, Integer endpoint, Integer createUserId, Long deadLine
    ) {
        PushTask pushTask = assemblePush(title, subTitle, action, actionParams, targetId, type, range, referenceId,
                endpoint, createUserId, deadLine);
        pushTask = pushService.add(pushTask);
        log.info("add push task: " + pushTask);
        return kbRes.ok(Mapper.map(pushTask, PushModel.class));
    }

    @Override
    public KbEntity<List<PushModel>> getPushListByReferenceId(String referenceId, Integer type, Integer page, Integer pageSize) {
        return kbRes.ok(pushService
                .getPushByReferenceId(referenceId, type, page, pageSize)
                .map(p -> Mapper.map(p, PushModel.class)));
    }

    @Override
    public KbEntity<PushModel> getPushById(String pushId) {
        return pushService.getPushById(pushId)
                .map(p -> Mapper.map(p, PushModel.class))
                .map(kbRes::ok)
                .orElse(kbRes.err(KbCode.RESOURCE_NOT_FOUND));
    }

    @Override
    public KbEntity<List<PushModel>> getPushListByIdIn(String[] pushIds) {
        return kbRes.ok(pushService.getPushListByIdIn(pushIds).stream()
                .map(p -> Mapper.map(p, PushModel.class))
                .collect(Collectors.toList()));
    }

    @Override
    public KbEntity<Void> addAndPush(
            String title, String subTitle, String action, String actionParams, String targetId, Integer type,
            Integer range, String referenceId, Integer endpoint, Integer createUserId, Long deadLine
    ) {
        PushTask pushTask = assemblePush(
                title, subTitle, action, actionParams, targetId, type, range,
                referenceId, endpoint, createUserId, deadLine);
        log.info("add and push task: " + pushTask);
        pushService.addAndPush(pushTask);
        return kbRes.ok();
    }

    @Override
    public KbEntity<Void> specifyPushByUserIdIn(String taskId, Integer[] userIds) {
        pushService.push(taskId, userIds);
        return kbRes.ok();
    }

    @Override
    public KbEntity<String> push(String pushId) {
        pushService.push(pushId);
        return kbRes.ok();
    }

    @Override
    public KbEntity<PushTargetModel> getPushTargetById(String pushTargetId) {
        return pushTargetService.getById(pushTargetId)
                .map(target -> Mapper.map(target, PushTargetModel.class))
                .map(kbRes::ok)
                .orElse(kbRes.err(KbCode.RESOURCE_NOT_FOUND));
    }

    @Override
    public KbEntity<PushTargetModel> getPushTargetByUserId(Integer userId, Integer endpoint) {
        return pushTargetService.getByUserIdAndEndpoint(userId, endpoint)
                .map(target -> Mapper.map(target, PushTargetModel.class))
                .map(kbRes::ok)
                .orElse(kbRes.err(KbCode.RESOURCE_NOT_FOUND));
    }

    /**
     * 20241101 关闭推送逻辑代码
     * @param pushId
     * @return
     */
    @Deprecated
    @GetMapping(path = "/push/iOSPatchPush")
    public KbEntity<Void> pushPatchIOS(
            @RequestParam() String pushId
    ) {
        //pushService.iOSBatchPush(pushId);
        return kbRes.ok();
    }

    private PushTask assemblePush(
            String title, String subTitle, String action, String actionParams, String targetId, Integer type,
            Integer range, String referenceId, Integer endPoint, Integer createUserId, Long deadLine) {
        PushTask pushTask = new PushTask();
        pushTask.setTitle(title);
        pushTask.setSubTitle(subTitle);
        pushTask.setAction(action);
        if (!StringUtils.isEmpty(actionParams)) {
            pushTask.setActionParams(JsonUtils.getGson().fromJson(
                    actionParams, new TypeToken<Map<String, Object>>() {}.getType()));
        }
        pushTask.setRange(PushRange.valueOf(range).orElseThrow(() ->
                new KbException(KbCode.REQUEST_PARAM_INVALID).li().r("wrong range")).getValue());
        if (null != targetId) {
            pushTask.setTargetId(targetId);
        }
        if (null != referenceId) {
            pushTask.setReferenceId(referenceId);
        }
        pushTask.setType(PushType.valueOf(type).orElseThrow(() ->
                new KbException(KbCode.REQUEST_PARAM_INVALID).li().r("wrong type")).getValue());
        pushTask.setEndpoint(KbEndpoint.valueOf(endPoint).orElseThrow(() ->
                new KbException(KbCode.REQUEST_PARAM_INVALID).li().r("wrong endpoint")).getValue());
        pushTask.setCreateUserId(createUserId);
        pushTask.setDeadLine(deadLine);
        return pushTask;
    }

}
