package com.kaiba.m.core.controller.education.recitation.backend;

import com.alibaba.nacos.common.utils.CollectionUtils;
import com.alibaba.nacos.common.utils.Objects;
import com.kaiba.lib.base.constant.KbHeader;
import com.kaiba.lib.base.middleware.KbRes;
import com.kaiba.lib.base.response.KbCode;
import com.kaiba.lib.base.response.KbEntity;
import com.kaiba.m.core.controller.education.recitation.mapper.RecitationConMapping;
import com.kaiba.m.core.domain.education.recitation.Member;
import com.kaiba.m.core.domain.education.recitation.enums.CommonStatusEnum;
import com.kaiba.m.core.domain.education.recitation.enums.MemberTypeEnum;
import com.kaiba.m.core.model.education.recitation.MemberModel;
import com.kaiba.m.core.service.education.recitation.MemberQueryService;
import com.kaiba.m.core.service.education.recitation.MemberService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.util.List;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * Description: 朗诵团成员Controller
 * Author: ZM227
 * Date: 2024/8/1 16:14
 */
@Slf4j
@RestController
@RequestMapping("/backend/recitation/usr/member/modify")
@Api(tags = "后端朗诵团成员管理")
public class MemberBEController {

    @Resource
    private KbRes kbRes;
    @Resource
    private MemberService memberService;
    @Resource
    private MemberQueryService memberQueryService;
    @Resource
    private RecitationConMapping recitationConMapping;

    /**
     * 新增成员
     *
     * @param userId      当前操作开吧用户Id
     * @param memberModel 朗诵团成员对象
     * @return 新增的成员Code
     */
    @PostMapping("/createMember")
    @ApiOperation(value = "新增成员")
    public KbEntity<MemberModel> createExpert(
        @RequestHeader(name = KbHeader.KB_USER_ID) Integer userId,
        @RequestBody MemberModel memberModel) {
        Member member = recitationConMapping.modelToDomain(memberModel);
        if (Objects.nonNull(
            memberQueryService.findExistMember(member.getMemberCode(), member.getName(),
                member.getConnectPhone()))) {
            return kbRes.err(KbCode.RESOURCE_ALREADY_EXIST);
        }
        return kbRes.ok(recitationConMapping.domainToModel(memberService.createMember(member)));
    }

    /**
     * 更新成员
     *
     * @param userId      当前操作开吧用户Id
     * @param memberModel 朗诵团成员对象
     * @return 更新的成员对象
     */
    @PostMapping("/updateMember")
    @ApiOperation(value = "更新成员信息")
    public KbEntity<MemberModel> updateMember(
        @RequestHeader(name = KbHeader.KB_USER_ID) Integer userId,
        @RequestBody MemberModel memberModel) {
        Member member = recitationConMapping.modelToDomain(memberModel);
        Member dbMember = memberQueryService.findExistMember(member.getMemberCode(),
            member.getName(), member.getConnectPhone());
        if (Objects.isNull(dbMember)) {
            return kbRes.err(KbCode.RESOURCE_NOT_FOUND);
        }
        return kbRes.ok(recitationConMapping.domainToModel(memberService.updateMember(member)));
    }

    /**
     * 修改成员状态
     *
     * @param userId     当前操作开吧用户Id
     * @param memberCode 朗诵团成员Code
     * @param valid      状态
     * @return 更新后成员对象
     */
    @PostMapping("/changeMemberStatus")
    @ApiOperation(value = "修改成员状态")
    public KbEntity<MemberModel> changeMemberStatus(
        @RequestHeader(name = KbHeader.KB_USER_ID) Integer userId, @RequestParam String memberCode,
        @RequestParam Boolean valid) {
        Member update = new Member();
        update.setMemberCode(memberCode);
        update.setStatus(valid ? CommonStatusEnum.VALID : CommonStatusEnum.INVALID);
        Member dbMember = memberQueryService.findExistMember(update.getMemberCode(),
            update.getName(), update.getConnectPhone());
        if (Objects.isNull(dbMember)) {
            return kbRes.err(KbCode.RESOURCE_NOT_FOUND);
        }
        return kbRes.ok(recitationConMapping.domainToModel(memberService.updateMember(update)));
    }

    /**
     * 更新成员排序列表
     *
     * @param userId 当前操作开吧用户Id
     * @param models 成员排序列表
     * @return 更新后成员对象
     */
    @PostMapping("/updateMemberSortList")
    @ApiOperation(value = "更新成员排序列表")
    public KbEntity<Void> updateMemberSortList(
        @RequestHeader(name = KbHeader.KB_USER_ID) Integer userId,
        @RequestBody List<MemberModel> models) {
        if (CollectionUtils.isEmpty(models)) {
            return kbRes.ok();
        }
        memberService.updateMemberSortList(
            models.stream().map(m -> recitationConMapping.modelToDomain(m)).collect(
                Collectors.toList()));
        return kbRes.ok();
    }

    /**
     * 从朗诵团删除成员
     *
     * @param userId     当前操作开吧用户Id
     * @param memberCode 朗诵团成员Code
     * @return 更新后成员对象
     */
    @PostMapping("/deleteRecitationMember")
    @ApiOperation(value = "从朗诵团删除成员")
    public KbEntity<MemberModel> deleteRecitationMember(
        @RequestHeader(name = KbHeader.KB_USER_ID) Integer userId,
        @RequestParam String memberCode) {
        Member update = new Member();
        update.setMemberCode(memberCode);
        update.setMemberType(MemberTypeEnum.NON_MEMBER);
        Member dbMember = memberQueryService.findExistMember(update.getMemberCode(),
            update.getName(), update.getConnectPhone());
        if (Objects.isNull(dbMember)) {
            return kbRes.err(KbCode.RESOURCE_NOT_FOUND);
        }
        return kbRes.ok(recitationConMapping.domainToModel(memberService.updateMember(update)));
    }

}
