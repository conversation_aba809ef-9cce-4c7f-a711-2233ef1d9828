package com.kaiba.m.core.controller.usermessage;

import com.google.gson.reflect.TypeToken;
import com.kaiba.lib.base.annotation.apiparam.KbObjectId;
import com.kaiba.lib.base.constant.KbHeader;
import com.kaiba.lib.base.constant.usermessage.IUserMessageFolder;
import com.kaiba.lib.base.constant.usermessage.UserMessageType;
import com.kaiba.lib.base.domain.user.UserMessageModel;
import com.kaiba.lib.base.middleware.KbRes;
import com.kaiba.lib.base.response.KbCode;
import com.kaiba.lib.base.response.KbEntity;
import com.kaiba.lib.base.service.IMessageService;
import com.kaiba.m.core.util.JsonUtils;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.*;

/**
 * author: lyux
 * date: 18-10-1
 */
@Slf4j
@RestController
@RequestMapping("/n/user/message")
@Validated
public class UserMessageApiController {

    private static final Map<Integer, UserMessageType> MESSAGE_FOLD_TYPE_MAP = new HashMap<>();
    static {
        for (UserMessageType t : UserMessageType.values()) {
            if (t.getFolder() != null) {
                MESSAGE_FOLD_TYPE_MAP.put(t.getType(), t);
            }
        }
    }

    private final KbRes kbApiRes;
    private final IMessageService messageService;

    @Autowired
    public UserMessageApiController(
            @Qualifier("kbApiRes") KbRes kbApiRes,
            IMessageService messageService
    ) {
        this.kbApiRes = kbApiRes;
        this.messageService = messageService;
    }

    @PostMapping(path = "/getUnreadMessages")
    @ApiOperation("获取未读消息列表, 结果按时间升序排列")
    KbEntity<List<UserMessageModel>> getUnreadMessages(
            @RequestHeader(name = KbHeader.KB_USER_ID) Integer userId,
            @RequestHeader(name = KbHeader.KB_EP) Integer endpoint,
            @RequestParam(required = false) @KbObjectId String lastId,
            @RequestParam(required = false, defaultValue = "20") Integer pageSize
    ) {
        List<UserMessageModel> messageList = messageService
                .getUnreadMessages(userId, endpoint, lastId, pageSize).dataOrThrow();
        return kbApiRes.ok(tryFoldMessageList(messageList));
    }

    @PostMapping(path = "/getHistoryMessages")
    @ApiOperation("获取历史消息列表, 结果按时间降序排列")
    KbEntity<List<UserMessageModel>> getHistoryMessages(
            @RequestHeader(name = KbHeader.KB_USER_ID) Integer userId,
            @RequestHeader(name = KbHeader.KB_EP) Integer endpoint,
            @RequestParam(required = false) String typeList,
            @RequestParam(required = false) @KbObjectId String lastId,
            @RequestParam(required = false, defaultValue = "20") Integer pageSize
    ) {
        Integer[] typeArray = null;
        if (null != typeList && !"".equals(typeList)) {
            List<Integer> types = JsonUtils.toModel(typeList, new TypeToken<List<Integer>>() {}.getType());
            if (null == types || types.size() > 20) {
                return kbApiRes.err(KbCode.REQUEST_PARAM_INVALID);
            } else {
                typeArray = new Integer[types.size()];
                types.toArray(typeArray);
            }
        }
        return kbApiRes.asIs(messageService.getHistoryMessages(userId, typeArray, endpoint, lastId, pageSize));
    }

    // ----------------------------------------------------------------

    private static List<UserMessageModel> tryFoldMessageList(List<UserMessageModel> messageList) {
        if (MESSAGE_FOLD_TYPE_MAP.size() == 0 || messageList == null || messageList.size() == 0) {
            return messageList;
        }
        Map<String, List<UserMessageModel>> map = null;
        for (int i = messageList.size() - 1; i >= 0; i --) {
            UserMessageModel message = messageList.get(i);
            UserMessageType type = MESSAGE_FOLD_TYPE_MAP.get(message.getType());
            IUserMessageFolder folder = type == null ? null : type.getFolder();
            if (folder != null) {
                if (map == null) {
                    map = new HashMap<>(MESSAGE_FOLD_TYPE_MAP.size());
                }
                String key = folder.folderKey(message);
                List<UserMessageModel> l = map.get(key);
                if (l == null) {
                    l = new LinkedList<>();
                    l.add(message);
                    map.put(key, l);
                } else {
                    l.add(message);
                }
                messageList.remove(i);
            }
        }
        if (map != null) {
            map.forEach((key, combineList) -> UserMessageType.valueOf(combineList.get(0).getType())
                    .map(UserMessageType::getFolder)
                    .ifPresent(folder -> messageList.add(folder.foldMessageList(combineList))));
            messageList.sort(Comparator.comparing(UserMessageModel::getId));
        }
        return messageList;
    }

}
