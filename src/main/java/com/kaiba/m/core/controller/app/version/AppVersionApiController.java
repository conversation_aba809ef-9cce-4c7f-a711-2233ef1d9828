package com.kaiba.m.core.controller.app.version;

import com.kaiba.lib.base.constant.KbHeader;
import com.kaiba.lib.base.domain.appversion.AppNextVersionModel;
import com.kaiba.lib.base.middleware.KbRes;
import com.kaiba.lib.base.response.KbEntity;
import com.kaiba.m.core.service.appversion.AppVersionCacheService;
import com.kaiba.m.core.service.appversion.AppVersionModelHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * author: lyux
 * date: 2023-05-22
 */
@Slf4j
@RestController
@RequestMapping("/api/AppVersion")
@Validated
public class AppVersionApiController {

    private final KbRes kbRes;
    private final AppVersionCacheService cacheService;

    public AppVersionApiController(
            @Qualifier("kbApiRes") KbRes kbRes,
            AppVersionCacheService cacheService
    ) {
        this.kbRes = kbRes;
        this.cacheService = cacheService;
    }

    @PostMapping("/getNextVersion")
    public KbEntity<AppNextVersionModel> getNextVersion(
            @RequestHeader(KbHeader.KB_EP) Integer endpoint,
            @RequestHeader(KbHeader.KB_DO) String deviceType,
            @RequestHeader(KbHeader.KB_VC) Integer versionCode
    ) {
        String packageId = AppVersionModelHelper.getPackageIdByEndpoint(endpoint, deviceType);
        return kbRes.ok(cacheService.getNextVersionFromCache(packageId, versionCode));
    }

}
