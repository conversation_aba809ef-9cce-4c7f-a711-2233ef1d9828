package com.kaiba.m.core.controller.news.pool.knowledge;

import com.kaiba.lib.base.domain.news.pool.knowledge.KnowledgeModel;
import com.kaiba.lib.base.middleware.KbRes;
import com.kaiba.lib.base.response.KbEntity;
import com.kaiba.lib.base.response.KbException;
import com.kaiba.lib.base.service.INewsKnowledgeService;
import com.kaiba.m.core.service.news.pool.knowledge.IKnowledgeQueryService;
import com.kaiba.m.core.service.opensearch.news.NewsSearchConfig;
import com.kaiba.m.core.service.opensearch.news.NewsSearchConfig.FieldsType;
import java.util.List;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RestController;

/**
 * Description: 知识库内部服务
 * Author: ZM227
 * Date: 2025/1/22 15:59
 */
@Slf4j
@RestController
public class KnowledgeServiceController implements INewsKnowledgeService {

    @Resource
    private KbRes kbRes;
    @Resource
    private IKnowledgeQueryService knowledgeQueryService;

    @Override
    public KbEntity<String> queryKnowledgeContent(String baseId, String keyWords, List<String> categories) {
        try {
            NewsSearchConfig newsSearchConfig = new NewsSearchConfig();
            newsSearchConfig.setFieldsType(FieldsType.KNOWLEDGE);
            newsSearchConfig.setPlusPopularity(Boolean.TRUE);
            newsSearchConfig.setBasedAnswer(Boolean.TRUE);
            newsSearchConfig.setAddLog(Boolean.TRUE);
            newsSearchConfig.setKeyWords(keyWords);
            newsSearchConfig.setCategoryIds(categories);
            newsSearchConfig.setBaseId(baseId);
            return kbRes.ok(knowledgeQueryService.queryKnowledgeContent(newsSearchConfig));
        } catch (KbException e) {
            return kbRes.err(e.getCode(), e.getMessage(), e.getReadableMessage());
        }
    }
}
