package com.kaiba.m.core.controller.da.getui.model;

import lombok.Data;

/**
 * 内容的标题和封面
 * author: lyux
 * date: 2025-05-07
 **/
@Data
public class GTATitleAndCoverModel {

    /** Doc ID. */
    private String id;

    /** 标题 */
    private String title;

    /** 封面 */
    private String cover;


    public static GTATitleAndCoverModel create(String id, String title, String cover) {
        GTATitleAndCoverModel model = new GTATitleAndCoverModel();
        model.id = id;
        model.title = title;
        model.cover = cover;
        return model;
    }
}
