package com.kaiba.m.core.controller.push;

import com.kaiba.lib.base.assembler.Mapper;
import com.kaiba.lib.base.domain.push.PushModel;
import com.kaiba.lib.base.middleware.KbRes;
import com.kaiba.lib.base.response.KbCode;
import com.kaiba.lib.base.response.KbEntity;
import com.kaiba.lib.base.service.IPushService;
import com.kaiba.m.core.domain.orientpush.OrientPush;
import com.kaiba.m.core.model.orientationpush.OrientPushModel;
import com.kaiba.m.core.service.orientpush.OrientPushService;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@RestController
public class OrientPushController {

    private final KbRes kbRes;
    private final OrientPushService orientPushService;
    private final IPushService pushService;

    public OrientPushController(
            KbRes kbRes,
            OrientPushService orientPushService,
            IPushService pushService
    ) {
        this.kbRes = kbRes;
        this.orientPushService = orientPushService;
        this.pushService = pushService;
    }

    @PostMapping(path = "/orientationPush/createOrientationPushByBody")
    public KbEntity<OrientPushModel> createOrientationPushByBody(@RequestBody() OrientPushModel model) {
        if (model == null) {
            return kbRes.err(KbCode.REQUEST_PARAM_INVALID, null, "参数错误");
        }
        return kbRes.ok(push2model(orientPushService.createOrientPush(model2push(model))));
    }

    @PostMapping(path = "/orientationPush/updateOrientationPushByBody")
    public KbEntity<OrientPushModel> updateOrientationPushByBody(@RequestBody() OrientPushModel model) {
        return kbRes.err(KbCode.NOT_SUPPORT_YET);
    }

    @PostMapping(path = "/orientationPush/push")
    public KbEntity<Void> push(
            @RequestParam(name = "id") String id
    ) {
        orientPushService.push(id);
        return kbRes.ok();
    }

    @PostMapping(path = "/orientationPush/removePush")
    public KbEntity<Void> removePush(
            @RequestParam(name = "id") String id
    ) {
        orientPushService.removePush(id);
        return kbRes.ok();
    }

    @PostMapping(path = "/orientationPush/getOrientationPushById")
    public KbEntity<OrientPushModel> getOrientationPushById(
            @RequestParam(name = "id") String id
    ) {
        return orientPushService.getOrientPushById(id)
                .map(OrientPushController::push2model)
                .map(model -> {
                    model.setPushModel(pushService.getPushById(model.getPushId()).getData());
                    return model;
                })
                .map(kbRes::ok)
                .orElse(kbRes.err(KbCode.RESOURCE_NOT_FOUND));
    }

    @PostMapping(path = "/orientationPush/getOrientationPushList")
    public KbEntity<List<OrientPushModel>> getOrientationPushList(
            @RequestParam(name = "title", required = false) String title,
            @RequestParam(name = "startTime", required = false) Long startTime,
            @RequestParam(name = "endTime", required = false) Long endTime,
            @RequestParam(name = "scope", required = false) Integer scope,
            @RequestParam(name = "page", required = false, defaultValue = "1") Integer page,
            @RequestParam(name = "pageSize", required = false, defaultValue = "15") Integer pageSize
    ) {
        Page<OrientPush> pushPage = orientPushService
                .getOrientPushPage(title, startTime,endTime, scope, page, pageSize);
        Set<String> pushIds = pushPage.getContent().stream()
                .map(OrientPush::getPushId)
                .collect(Collectors.toSet());
        Map<String, PushModel> pushMap = pushService.getPushModelMapByIds(pushIds).dataOrThrow();
        return kbRes.ok(orientPushService
                .getOrientPushPage(title, startTime,endTime, scope, page, pageSize)
                .map(OrientPushController::push2model)
                .map(model -> {
                    model.setPushModel(pushMap.get(model.getPushId()));
                    return model;
                })
        );
    }

    public static OrientPushModel push2model(OrientPush orientPush) {
        return Mapper.map(orientPush, OrientPushModel.class);
    }

    public static OrientPush model2push(OrientPushModel orientPushModel) {
        return Mapper.map(orientPushModel, OrientPush.class);
    }
}
