package com.kaiba.m.core.controller;

import com.kaiba.lib.base.assembler.Mapper;
import com.kaiba.lib.base.domain.counter.KbCounterModel;
import com.kaiba.lib.base.domain.counter.KbCounterUserRecordModel;
import com.kaiba.lib.base.domain.counter.KbCounterVirtualModel;
import com.kaiba.lib.base.domain.counter.VirtualExpressionUtil;
import com.kaiba.lib.base.middleware.KbRes;
import com.kaiba.lib.base.response.KbCode;
import com.kaiba.lib.base.response.KbEntity;
import com.kaiba.lib.base.response.KbException;
import com.kaiba.lib.base.service.ICounterService;
import com.kaiba.m.core.domain.counter.KbCounter;
import com.kaiba.m.core.domain.counter.KbCounterVirtual;
import com.kaiba.m.core.service.counter.CounterService;
import com.kaiba.m.core.service.counter.CounterSetting;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Optional;
import java.util.Random;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * author: lyux
 * date: 2021-06-02
 */
@Slf4j
@RestController
public class CounterServiceController implements ICounterService {

    private final KbRes kbRes;
    private final CounterService counterService;

    public CounterServiceController(KbRes kbRes, CounterService counterService) {
        this.kbRes = kbRes;
        this.counterService = counterService;
    }

    @Override
    public KbEntity<Long> incrCount(String counterId, String key, Integer userId, Long count) {
        return getSettingByIdOrKey(counterId, key)
                .map(setting -> counterService.incrCount(userId, setting, count))
                .map(kbRes::ok)
                .orElse(kbRes.err(KbCode.RESOURCE_NOT_FOUND));
    }

    @Override
    public KbEntity<Long> incrVirtualCount(String counterId, String key, Integer operatorId, Long count) {
        return getSettingByIdOrKey(counterId, key)
                .map(setting -> counterService.incrVirtualCount(setting, count))
                .map(kbRes::ok)
                .orElse(kbRes.err(KbCode.RESOURCE_NOT_FOUND));
    }

    @Override
    public KbEntity<Long> getDisplayCount(String counterId, String key) {
        return getSettingByIdOrKey(counterId, key)
                .map(counterService::getDisplayCount)
                .map(kbRes::ok)
                .orElse(kbRes.err(KbCode.RESOURCE_NOT_FOUND));
    }

    @Override
    public KbEntity<Long> getActualCount(String counterId, String key) {
        return getSettingByIdOrKey(counterId, key)
                .map(counterService::getActualCount)
                .map(kbRes::ok)
                .orElse(kbRes.err(KbCode.RESOURCE_NOT_FOUND));
    }

    @Override
    public KbEntity<Long> getVirtualCount(String counterId, String key) {
        return getSettingByIdOrKey(counterId, key)
                .map(counterService::getVirtualCount)
                .map(kbRes::ok)
                .orElse(kbRes.err(KbCode.RESOURCE_NOT_FOUND));
    }

    @Override
    public KbEntity<Long> getUserCount(String counterId, String key, Integer userId) {
        return getSettingByIdOrKey(counterId, key)
                .map(setting -> counterService.getUserCountFromCache(userId, setting))
                .map(kbRes::ok)
                .orElse(kbRes.err(KbCode.RESOURCE_NOT_FOUND));
    }

    @Override
    public KbEntity<KbCounterModel> createInstanceByBody(KbCounterModel createModel) {
        KbCounter counter = Mapper.map(createModel, KbCounter.class);
        KbCounter created = counterService.createCounter(counter);
        if (createModel.getVirtualCount() != null || createModel.getVirtualExpression() != null) {
            createCounterVirtual(
                    created.getId(), null, createModel.getVirtualCount(),
                    createModel.getVirtualExpression(), null);
        }
        return kbRes.ok(counter2model(created));
    }

    @Override
    public KbEntity<KbCounterModel> createInstance(
            String key, String name, String description,
            Long uidLimit, String uidLimitResetRTE, String virtualExpression,
            Long endTime, Boolean recordUser
    ) {
        KbCounterModel model = new KbCounterModel();
        model.setKey(key);
        model.setName(name);
        model.setDescription(description);
        model.setUidLimit(uidLimit);
        model.setUidLimitResetRTE(uidLimitResetRTE);
        model.setEndTime(endTime);
        model.setRecordUser(recordUser);
        if (virtualExpression != null) {
            KbCounterVirtual virtual = counterService.createVirtual(0L, virtualExpression, null);
            model.setVirtualId(virtual.getId());
        }
        return createInstanceByBody(model);
    }

    @Override
    public KbEntity<KbCounterModel> getOrCreateInstanceByKey(
            String key, Long uidLimit, String uidLimitResetRTE, Long endTime, Boolean recordUser
    ) {
        KbCounter counter = new KbCounter();
        counter.setKey(key);
        counter.setUidLimit(uidLimit);
        counter.setUidLimitResetRTE(uidLimitResetRTE);
        counter.setEndTime(endTime);
        counter.setRecordUser(recordUser);
        KbCounter created = counterService.getOrCreateCounterByKey(counter);
        return kbRes.ok(counter2model(created));
    }

    @Override
    public KbEntity<KbCounterModel> updateInstanceByBody(KbCounterModel updateModel) {
        KbCounter c = counterService.getCounterById(updateModel.getId()).orElseThrow(() ->
                new KbException(KbCode.ILLEGAL_ARGUMENT, "counter not exists: " + updateModel.getId()).li());
        KbCounter counter = Mapper.map(updateModel, KbCounter.class);
        counter.setKey(c.getKey()); // 不允许修改 key
        if (updateModel.getVirtualExpression() != null || updateModel.getVirtualCount() != null) {
            if (c.getVirtualId() == null) {
                KbCounterVirtual virtual = counterService.createVirtual(
                        updateModel.getVirtualCount(), updateModel.getVirtualExpression(), null);
                counter.setVirtualId(virtual.getId());
            } else {
                counterService.updateVirtual(
                        c.getVirtualId(), updateModel.getVirtualCount(), updateModel.getVirtualExpression(), null);
            }
        }
        KbCounter updated = counterService.updateCounter(counter);
        return kbRes.ok(counter2model(updated));
    }

    @Override
    public KbEntity<KbCounterModel> updateInstance(
            String counterId, String name, String description,
            Long uidLimit, String uidLimitResetRTE, String virtualExpression,
            Long endTime, Boolean recordUser
    ) {
        KbCounterModel model = new KbCounterModel();
        model.setId(counterId);
        model.setName(name);
        model.setDescription(description);
        model.setUidLimit(uidLimit);
        model.setUidLimitResetRTE(uidLimitResetRTE);
        model.setVirtualExpression(virtualExpression);
        model.setEndTime(endTime);
        model.setRecordUser(recordUser);
        return updateInstanceByBody(model);
    }

    @Override
    public KbEntity<KbCounterModel> clearCounter(String counterId, String key, String secret) {
        if (counterId == null) {
            counterId = counterService.getCounterByKey(key).map(KbCounter::getId)
                    .orElseThrow(() -> new KbException(KbCode.RESOURCE_NOT_FOUND).li());
        }
        counterService.clearCounter(counterId, secret);
        return kbRes.ok();
    }

    @Override
    public KbEntity<KbCounterModel> getInstanceById(String counterId) {
        return counterService.getCounterById(counterId)
                .map(this::counter2model)
                .map(kbRes::ok)
                .orElse(kbRes.err(KbCode.RESOURCE_NOT_FOUND));
    }

    @Override
    public KbEntity<KbCounterModel> getInstanceByKey(String key) {
        return counterService.getCounterByKey(key)
                .map(this::counter2model)
                .map(kbRes::ok)
                .orElse(kbRes.err(KbCode.RESOURCE_NOT_FOUND));
    }

    @Override
    public KbEntity<List<KbCounterModel>> getInstanceListByIdIn(String[] counterIds) {
        return kbRes.ok(counterService.getCounterListByIdIn(counterIds).stream()
                .map(this::counter2model)
                .collect(Collectors.toList()));
    }

    @Override
    public KbEntity<List<KbCounterModel>> getInstanceList(Integer page, Integer pageSize) {
        return kbRes.ok(counterService.getCounterPage(page, pageSize)
                .map(this::counter2model));
    }

    @Override
    public KbEntity<List<KbCounterUserRecordModel>> getUserCountRecordList(
            String counterId, Integer userId, Integer page, Integer pageSize) {
        return kbRes.ok(counterService.getUserCountRecordFromDB(userId, counterId, page, pageSize)
                .map(m -> Mapper.map(m, KbCounterUserRecordModel.class)));
    }

    @Override
    public KbEntity<KbCounterVirtualModel> createCounterVirtualByBody(
            KbCounterVirtualModel createModel) {
        KbCounterVirtual virtual = counterService.createVirtual(
                createModel.getKey(), createModel.getVirtualCount(),
                createModel.getExpression(), createModel.getDescription());
        return kbRes.ok(virtual2model(virtual));
    }

    @Override
    public KbEntity<KbCounterVirtualModel> createCounterVirtual(
            String counterId, String key, Long virtualCount, String expression, String description) {
        KbCounterVirtual virtual = counterService.createVirtual(key, virtualCount, expression, description);
        if (counterId != null) {
            counterService.updateCounterVirtualId(counterId, virtual.getId());
        }
        return kbRes.ok(virtual2model(virtual));
    }

    @Override
    public KbEntity<KbCounterVirtualModel> updateCounterVirtual(
            String virtualId, Long virtualCount, String expression, String description) {
        KbCounterVirtual virtual = counterService.updateVirtual(virtualId, virtualCount, expression, description);
        return kbRes.ok(virtual2model(virtual));
    }

    @Override
    public KbEntity<Long> updateCounterVirtualCount(
            String virtualId, String virtualKey, Integer operatorId, Long virtualCount) {
        KbCounterVirtual virtual = counterService.updateVirtualCount(virtualId, virtualCount);
        return kbRes.ok(virtual.getVirtualCount());
    }

    @Override
    public KbEntity<Long> incrCounterVirtualCount(
            String virtualId, String virtualKey, Integer operatorId, Long count) {
        KbCounterVirtual virtual = counterService.increaseVirtualCount(virtualId, count);
        return kbRes.ok(virtual.getVirtualCount());
    }

    @Override
    public KbEntity<Long> calculateVirtualCount(
            String virtualId, String virtualKey, Long originCount) {
        return null;
    }

    @Override
    public KbEntity<KbCounterVirtualModel> getVirtualById(String virtualId, String virtualKey) {
        if (virtualId != null) {
            return counterService.getVirtualById(virtualId)
                    .map(virtual -> Mapper.map(virtual, KbCounterVirtualModel.class))
                    .map(kbRes::ok)
                    .orElse(kbRes.err(KbCode.RESOURCE_NOT_FOUND));
        } else if (virtualKey != null) {
            return counterService.getVirtualByKey(virtualKey)
                    .map(virtual -> Mapper.map(virtual, KbCounterVirtualModel.class))
                    .map(kbRes::ok)
                    .orElse(kbRes.err(KbCode.RESOURCE_NOT_FOUND));
        } else {
            return kbRes.err(KbCode.REQUEST_PARAM_MISSING);
        }
    }

    @Override
    public KbEntity<List<KbCounterVirtualModel>> getVirtualByIdIn(String[] virtualIds) {
        return kbRes.ok(counterService
                .getVirtualListByIdIn(virtualIds).stream()
                .map(virtual -> Mapper.map(virtual, KbCounterVirtualModel.class))
                .collect(Collectors.toList()));
    }

    @Override
    public KbEntity<Long> runVirtualExpression(
            String expression, Long c, Long vc, Integer hash, Float r, Long t) {
        if (hash == null) {
            hash = UUID.randomUUID().hashCode();
        }
        if (r == null) {
            r = new Random().nextFloat();
        }
        if (t == null) {
            t = System.currentTimeMillis();
        }
        return kbRes.ok(VirtualExpressionUtil.calculate(expression, hash, c, vc, r, t));
    }

    // ----------------------------------------------

    private Optional<CounterSetting> getSettingByIdOrKey(String counterId, String key) {
        if (counterId != null) {
            return counterService.getSettingById(counterId);
        } else if (key != null) {
            return counterService.getSettingByKey(key);
        } else {
            throw new KbException(KbCode.REQUEST_PARAM_MISSING, "counterId and key null").li();
        }
    }

    private KbCounterModel counter2model(KbCounter kbCounter) {
        KbCounterModel model = Mapper.map(kbCounter, KbCounterModel.class);
        if (kbCounter.getId() != null || kbCounter.getKey() != null) {
            CounterSetting setting = getSettingByIdOrKey(kbCounter.getId(), kbCounter.getKey()).orElse(null);
            if (setting != null) {
                model.setDisplayCount(counterService.getDisplayCount(setting));
                if (setting.getVirtual() != null) {
                    model.setCounterVirtual(virtual2model(setting.getVirtual()));
                    model.setVirtualExpression(setting.getVirtual().getExpression());
                    model.setVirtualCount(setting.getVirtual().getVirtualCount());
                }
            }
        }
        return model;
    }

    private static KbCounterVirtualModel virtual2model(KbCounterVirtual virtual) {
        return Mapper.map(virtual, KbCounterVirtualModel.class);
    }

}
