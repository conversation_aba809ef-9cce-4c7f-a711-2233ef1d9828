package com.kaiba.m.core.controller.note;

import com.kaiba.lib.base.domain.note.NoteVerifyModel;
import com.kaiba.lib.base.middleware.KbRes;
import com.kaiba.lib.base.response.KbCode;
import com.kaiba.lib.base.response.KbEntity;
import com.kaiba.lib.base.response.KbException;
import com.kaiba.lib.base.service.INoteService;
import com.kaiba.lib.base.service.IVerifyService;
import com.kaiba.m.core.domain.note.NoteVerify;
import com.kaiba.m.core.service.note.NoteVerifyService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.stream.Collectors;

/**
 * author yanghy
 * date 2019/11/21
 */
@Slf4j
@RestController
public class VerifyServiceController implements IVerifyService {

    private final KbRes kbRes;
    private final INoteService noteService;
    private final NoteVerifyService noteVerifyService;

    @Autowired
    public VerifyServiceController(KbRes kbRes, INoteService noteService, NoteVerifyService noteVerifyService) {
        this.kbRes = kbRes;
        this.noteService = noteService;
        this.noteVerifyService = noteVerifyService;
    }

    @Override
    public KbEntity<NoteVerifyModel> createNoteThreadVerify(Integer siteId, String title, String description, String threadId, Integer userId) {
        if (null == threadId) {
            return kbRes.err(KbCode.REQUEST_PARAM_INVALID, "threadId null");
        }
        if (null == userId) {
            return kbRes.err(KbCode.REQUEST_PARAM_INVALID, "userId null");
        }
        if (null == siteId) {
            return kbRes.err(KbCode.REQUEST_PARAM_INVALID, "siteId null");
        }
        noteService.getThreadById(threadId).dataOrThrow();

        NoteVerify noteVerify = new NoteVerify();
        noteVerify.setSiteId(siteId);
        noteVerify.setTitle(title);
        noteVerify.setDescription(description);
        noteVerify.setThreadId(threadId);
        noteVerify.setCreator(userId);
        return kbRes.ok(noteVerify2model(noteVerifyService.createNoteVerify(noteVerify)));
    }

    @Override
    public KbEntity<NoteVerifyModel> getNoteVerifyById(String noteVerifyId) {
        if (null == noteVerifyId) {
            return kbRes.err(KbCode.REQUEST_PARAM_INVALID, "noteVerifyId null");
        }
        NoteVerify noteVerify = noteVerifyService.getNoteVerifyById(noteVerifyId).orElseThrow(() -> new KbException(KbCode.RESOURCE_NOT_FOUND, "noteVerify not found").li());
        return kbRes.ok(noteVerify2model(noteVerify));
    }

    @Override
    public KbEntity<List<NoteVerifyModel>> getNoteVerifyList(Integer siteId, Integer page, Integer pageSize) {
        if (null == siteId) {
            return kbRes.err(KbCode.REQUEST_PARAM_INVALID, "siteId null");
        }
        page = (page == null || page < 1) ? 0 : page - 1;
        if (null == pageSize) {
            pageSize = 15;
        }
        Page<NoteVerify> noteVerifyPage = noteVerifyService.getNoteVerifyList(siteId, page, pageSize);
        KbEntity<List<NoteVerifyModel>> result = kbRes.ok(
                noteVerifyPage.getContent().stream().map(this::noteVerify2model).collect(Collectors.toList()));
        result.setTotalPage((long) noteVerifyPage.getTotalPages());
        result.setTotalCount(noteVerifyPage.getTotalElements());
        return result;
    }

    //-------------------------------
    private NoteVerifyModel noteVerify2model(NoteVerify noteVerify) {
        NoteVerifyModel model = new NoteVerifyModel();

        model.setId(noteVerify.getId());
        model.setThreadId(noteVerify.getThreadId());
        model.setSiteId(noteVerify.getSiteId());
        model.setTitle(noteVerify.getTitle());
        model.setDescription(noteVerify.getDescription());
        model.setUserId(noteVerify.getCreator());
        model.setCreateTime(noteVerify.getCreateTime());

        return model;
    }
}
