package com.kaiba.m.core.controller.api;

import com.kaiba.lib.base.domain.user.UserModel;
import com.kaiba.lib.base.service.IUserService;
import com.kaiba.m.core.domain.enroll.EnrollData;
import com.kaiba.m.core.domain.enroll.EnrollModule;
import com.kaiba.m.core.domain.user.User;
import com.kaiba.m.core.model.enroll.EmceeCommentModel;
import com.kaiba.m.core.model.enroll.EnrollEmceeScoreModel;
import com.kaiba.m.core.service.enroll.EnrollService;

import org.apache.poi.xssf.streaming.SXSSFCell;
import org.apache.poi.xssf.streaming.SXSSFRow;
import org.apache.poi.xssf.streaming.SXSSFSheet;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.io.IOException;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.Collections;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import javax.servlet.http.HttpServletResponse;

import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2022/10/27 17:58
 **/
@Slf4j
@RestController
public class EmceeSurveyApiController {

    private static final Integer ACCESS_WINDOW_SIZE = 100000;
    private static final String EXPORT_EXCEL_SECRET = "cj10238gvbn4gn82adf4g25y";
    private static final String CONTENT_TYPE = "application/binary;charset=utf-8";
    private static final String HEADER_DISPOSITION = "Content-Disposition";
    private static final String HEADER_DISPOSITION_VALUE = "attachment;filename=";
    private static final String FILE_NAME = "主持人问卷.xlsx";

    private static final Integer[] EMCEE_SCORE_ARRAY = new Integer[] {
        1, 2, 3, 4, 5, 6, 7, 8, 9, 10
    };

    private static final String[] SCORE_TITLES = new String[] {
        "主持人", "1分", "2分", "3分", "4分", "5分",
        "6分", "7分", "8分", "9分", "10分", "平均分"
    };

    private static final String[] COMMENT_TITLES = new String[] {
        "时间", "用户名", "用户id", "手机号", "评论"
    };

    private final EnrollService enrollService;
    private final IUserService userService;

    public EmceeSurveyApiController(
        EnrollService enrollService,
        IUserService userService) {
        this.enrollService = enrollService;
        this.userService = userService;
    }

    @ApiOperation(value = "导出excel")
    @GetMapping(path = "/manage/emcee/survey/exportDataToExcel")
    public void exportDataToExcel(
        @RequestParam String secret,
        @RequestParam String enrollId,
        HttpServletResponse response
    ) {
        if(!EXPORT_EXCEL_SECRET.equals(secret)) {
            return;
        }

        SXSSFWorkbook wb = new SXSSFWorkbook(ACCESS_WINDOW_SIZE);
        SXSSFSheet sheet1 = wb.createSheet("主持人评分");
        SXSSFSheet sheet2 = wb.createSheet("用户留言");

        exportScoreToExcel(getEnrollCountByEnrollId(enrollId), sheet1);
        exportCommentToExcel(getEnrollCommentByEnrollId(enrollId), sheet2);
        exportExcel(wb, response);
    }

    // ----------------------------------------------------------

    public List<EnrollEmceeScoreModel> getEnrollCountByEnrollId(String enrollId) {
        List<EnrollEmceeScoreModel> list = new LinkedList<>();
        int pageSize = 100;
        for(int i = 1; ; i++) {
            list.addAll(enrollService.getAggrListByEnrollIdAndType(enrollId, 13, i, pageSize).getContent());
            if(list.size() < i * pageSize) {
                break;
            }
        }

        Map<String, Map<Integer, Integer>> map = list.stream().collect(
            Collectors.groupingBy(EnrollEmceeScoreModel::getEmceeId,
                Collectors.toMap(EnrollEmceeScoreModel::getScore, EnrollEmceeScoreModel::getTotal)));

        Map<String, String> emceeMap = enrollService.getEnrollModuleListByEnrollId(enrollId)
            .stream()
            .collect(Collectors.toMap(EnrollModule::getId, EnrollModule::getTitle));

        List<EnrollEmceeScoreModel> results = new LinkedList<>();
        for(String emceeId : map.keySet()) {
            Map<Integer, Integer> scoreMap = map.get(emceeId);
            EnrollEmceeScoreModel model = new EnrollEmceeScoreModel();
            model.setEmceeName(emceeMap.get(emceeId));
            List<EnrollEmceeScoreModel.EmceeScore> emceeScores = new LinkedList<>();
            int totalNum = 0;
            int totalScore = 0;
            for(int score : EMCEE_SCORE_ARRAY) {
                int num = scoreMap.get(score) == null ? 0 : scoreMap.get(score);
                totalNum += num;
                totalScore += num * score;
                emceeScores.add(new EnrollEmceeScoreModel.EmceeScore(score, num));
            }
            model.setScores(emceeScores);
            model.setAvgScore(new BigDecimal(totalScore / (double)totalNum)
                .setScale(4, RoundingMode.HALF_UP).doubleValue());
            results.add(model);
        }

        return results;
    }

    public List<EmceeCommentModel> getEnrollCommentByEnrollId(String enrollId) {
        List<EnrollData> list = new LinkedList<>();
        int pageSize = 100;
        for(int i = 1; ; i++) {
            list.addAll(enrollService.getByEnrollIdAndType(enrollId, 11, i, pageSize).getContent());
            if(list.size() < i * pageSize) {
                break;
            }
        }

        Integer[] userIds = list.stream().map(EnrollData::getUserId).toArray(Integer[]::new);
        Map<Integer, UserModel> userMap = userService.getDetailListIn(userIds)
            .data().orElse(Collections.emptyList())
            .stream()
            .filter(Objects::nonNull)
            .collect(Collectors.toMap(UserModel::getUserId, user -> user, (k1, k2) -> k2));

        return list.stream()
            .map(enrollData -> {
                EmceeCommentModel model = new EmceeCommentModel();
                model.setUserId(enrollData.getUserId());
                model.setText(enrollData.getText());
                model.setCreateTime(enrollData.getCreateTime());;
                UserModel user = userMap.get(enrollData.getUserId());
                if (user != null) {
                    model.setUserName(user.getUserName());
                    model.setMobile(user.getMobile());
                }
                return model;
            })
            .collect(Collectors.toList());
    }

    private void exportScoreToExcel(List<EnrollEmceeScoreModel> list, SXSSFSheet sheet) {
        SXSSFRow row = sheet.getRow(0);
        if(row == null) {
            row = sheet.createRow(0);
        }

        for (int j = 0; j < SCORE_TITLES.length; j++) {
            SXSSFCell cell = row.createCell(j);
            cell.setCellValue(SCORE_TITLES[j]);
        }

        int index = 0;
        if (list != null && list.size() > 0) {
            for (int i = 0; i < list.size(); i++) {
                EnrollEmceeScoreModel model = list.get(i);
                int rowNum = index + i + 1;
                row = sheet.getRow(rowNum);
                if(row == null) {
                    row = sheet.createRow(rowNum);
                }

                SXSSFCell cell = row.createCell(0);
                cell.setCellValue(model.getEmceeName());
                Map<Integer, Integer> scores = model
                    .getScores().stream()
                    .collect(Collectors.toMap(
                        EnrollEmceeScoreModel.EmceeScore::getScore,
                        EnrollEmceeScoreModel.EmceeScore::getCount));
                for(int j = 1; j < 11; j++) {
                    cell = row.createCell(j);
                    cell.setCellValue(scores.get(j));
                }
                cell = row.createCell(11);
                cell.setCellValue(model.getAvgScore());
            }
        }
    }

    private void exportCommentToExcel(List<EmceeCommentModel> list, SXSSFSheet sheet) {
        SXSSFRow row = sheet.getRow(0);
        if(row == null) {
            row = sheet.createRow(0);
        }

        for (int j = 0; j < COMMENT_TITLES.length; j++) {
            SXSSFCell cell = row.createCell(j);
            cell.setCellValue(COMMENT_TITLES[j]);
        }

        int index = 0;
        if (list != null && list.size() > 0) {
            for (int i = 0; i < list.size(); i++) {
                EmceeCommentModel model = list.get(i);
                int rowNum = index + i + 1;
                row = sheet.getRow(rowNum);
                if(row == null) {
                    row = sheet.createRow(rowNum);
                }

                SXSSFCell cell = row.createCell(0);
                cell.setCellValue(time2String(model.getCreateTime()));
                cell = row.createCell(1);
                cell.setCellValue(model.getUserName() == null ? "" : model.getUserName());
                cell = row.createCell(2);
                cell.setCellValue(model.getUserId() == null ? 0 : model.getUserId());
                cell = row.createCell(3);
                cell.setCellValue(model.getMobile() == null ? "" : model.getMobile());
                cell = row.createCell(4);
                cell.setCellValue(model.getText() == null ? "" : model.getText());
            }
        }
    }

    private void exportExcel(SXSSFWorkbook wb, HttpServletResponse response) {
        try {
            OutputStream outStream = null;
            try {
                response.reset();
                response.setContentType(CONTENT_TYPE);
                response.setHeader(HEADER_DISPOSITION, HEADER_DISPOSITION_VALUE
                    + URLEncoder.encode(FILE_NAME, StandardCharsets.UTF_8.name()));
                response.flushBuffer();
                outStream = response.getOutputStream();
                wb.write(outStream);
                outStream.flush();
            } catch (IOException e) {
                e.printStackTrace();
            } finally {
                if (!ObjectUtils.isEmpty(outStream)) {
                    outStream.close();
                }
                wb.close();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private String time2String(Long time) {
        return LocalDateTime
            .ofEpochSecond(time, 0, ZoneOffset.ofHours(8))
            .toString()
            .replaceAll("T", " ");
    }
}
