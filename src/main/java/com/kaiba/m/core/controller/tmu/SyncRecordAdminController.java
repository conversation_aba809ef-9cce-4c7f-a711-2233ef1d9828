package com.kaiba.m.core.controller.tmu;

import com.kaiba.lib.base.domain.hoolo.SyncRecordModel;
import com.kaiba.lib.base.middleware.KbRes;
import com.kaiba.lib.base.response.KbEntity;
import com.kaiba.m.core.service.hoolo.programme.SyncRecordService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.util.List;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * Description: 资讯同步记录admin Controller
 * Author: ZM227
 * Date: 2024/10/14 11:49
 */
@Slf4j
@RestController
@RequestMapping("/service/hoolo/usr/programme/sync/record")
@Api(tags = "栏目资讯导入记录")
public class SyncRecordAdminController {

    @Resource
    private KbRes kbRes;
    @Resource
    private SyncRecordService syncRecordService;

    @ApiOperation(value = "资讯同步记录查询", notes = "资讯同步记录查询")
    @PostMapping("/searchSyncRecords")
    public KbEntity<List<SyncRecordModel>> searchSyncRecords(
        @RequestParam String programmeId,
        @RequestParam Boolean onlyValid,
        @RequestParam(required = false, defaultValue = "1") Integer page,
        @RequestParam(required = false, defaultValue = "20") Integer pageSize) {
        return kbRes.ok(syncRecordService.searchRecordsByProgrammeKey(programmeId, onlyValid, page, pageSize));
    }

    @ApiOperation(value = "资讯同步记录查询by id", notes = "资讯同步记录查询by id")
    @PostMapping("/searchSyncRecordById")
    public KbEntity<SyncRecordModel> searchSyncRecordById(@RequestParam String recordId) {
        return kbRes.ok(syncRecordService.searchRecordById(recordId));
    }
}
