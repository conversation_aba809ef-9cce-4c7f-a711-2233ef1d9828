package com.kaiba.m.core.controller.legacy;

import com.kaiba.lib.base.annotation.api.KbCheckSignature;
import com.kaiba.lib.base.constant.KbSignType;
import com.kaiba.lib.base.domain.program.adrush.ActiveTimeModel;
import com.kaiba.lib.base.domain.program.adrush.AdRushModel;
import com.kaiba.lib.base.middleware.KbRes;
import com.kaiba.lib.base.response.KbCode;
import com.kaiba.lib.base.response.KbEntity;
import com.kaiba.lib.base.service.IAdRushService;
import com.kaiba.m.core.domain.program.adrush.ActiveTime;
import com.kaiba.m.core.service.program.adrush.AdRushService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 节目 - 广告抽奖
 * author: yeqq
 * date: 21-02-05
 *
 * 2025-03, 节目互动区投票广告不再提倡使用.
 */
@Deprecated
@Slf4j
@RestController
public class AdRushServiceController implements IAdRushService {

    private final KbRes kbRes;
    private final AdRushService adRushService;

    public AdRushServiceController(
            KbRes kbRes,
            AdRushService adRushService
    ) {
        this.kbRes = kbRes;
        this.adRushService = adRushService;
    }

    @Override
    @PostMapping(path = "/createAdRushByBody", consumes = "application/json;charset=UTF-8")
    public KbEntity<AdRushModel> createAdRushByBody(@RequestBody AdRushModel adRushModel) {
        if (adRushModel == null) {
            return kbRes.err(KbCode.REQUEST_PARAM_INVALID, null, "参数错误");
        }
        if (adRushModel.getRushModel() == null) {
            return kbRes.err(KbCode.REQUEST_PARAM_INVALID, null, "需要填写抽奖信息");
        }
        if (adRushModel.getActiveTimes() == null || adRushModel.getActiveTimes().size() == 0) {
            return kbRes.err(KbCode.REQUEST_PARAM_INVALID, null, "需要填写关联广告时间段");
        }
        return kbRes.ok(adRushService.createAdRush(adRushModel));
    }

    @Override
    @PostMapping(path = "/updateAdRushByBody", consumes = "application/json;charset=UTF-8")
    public KbEntity<AdRushModel> updateAdRushByBody(@RequestBody AdRushModel adRushModel) {
        if (adRushModel == null) {
            return kbRes.err(KbCode.REQUEST_PARAM_INVALID, null, "参数错误");
        }
        return kbRes.ok(adRushService.updateAdRush(adRushModel));
    }

    @Override
    public KbEntity<Void> deleteAdRushById(String adRushId) {
        adRushService.deleteAdRushById(adRushId);
        return kbRes.ok();
    }

    @KbCheckSignature(type = KbSignType.CALLBACK)
    @Override
    public KbEntity<Void> rushStateChangeCallback(String rushId, Integer lastState, Integer currentState) {
        log.info("ad rush rushStateChangeCallback, rushId: " + rushId + ", state " + lastState + " -> " + currentState);
        adRushService.updateRushState(rushId, currentState);
        return kbRes.ok();
    }

    @Override
    public KbEntity<ActiveTimeModel> updateActiveTimeByBody(ActiveTimeModel activeTimeModel) {
        return kbRes.ok(adRushService.updateActiveTime(activeTimeModel));
    }

    @Override
    public KbEntity<AdRushModel> getAdRushById(String adRushId) {
        return adRushService.getAdRushById(adRushId).map(AdRushService::adRush2model)
                .map(r -> {
                    Optional<ActiveTime> optionalReveal = adRushService.getActiveTimeByAdRushId(r.getId());
                    if (optionalReveal.isPresent()) {
                        ActiveTime activeTime = optionalReveal.get();
                        r.setActiveTimes(Collections.singletonList(AdRushService.activeTime2model(activeTime)));
                    }
                    return r;
                })
                .map(kbRes::ok)
                .orElse(kbRes.err(KbCode.RESOURCE_NOT_FOUND));
    }

    @Override
    public KbEntity<List<AdRushModel>> getAdRushListBySiteIdAndTime(Integer siteId, Long startTime, Long endTime,
                                                                    Integer[] states, Integer page, Integer pageSize) {
        List<String> adRushIds = getActiveTimeListByTimeBetween(siteId, states, startTime, endTime, page, pageSize)
                .map(list -> list.stream()
                        .map(ActiveTimeModel::getAdRushId).collect(Collectors.toList())).dataOrThrow();
        return kbRes.ok(adRushService.getAdRushListByIdIn(adRushIds).stream()
                .map(AdRushService::adRush2model).collect(Collectors.toList()));
    }

    @Override
    public KbEntity<List<ActiveTimeModel>> getActiveTimeListByTimeBetween(Integer siteId, Integer[] states,
                                                                          Long startTime, Long endTime, Integer page, Integer pageSize) {
        return kbRes.ok(adRushService.getActiveTimeList(siteId, states, startTime, endTime, page, pageSize)
                .map(AdRushService::activeTime2model));
    }

    @Override
    public KbEntity<List<AdRushModel>> getAdRushListBySiteId(Integer siteId, Integer[] types, Integer[] states,
                                                             Integer page, Integer pageSize) {
        return kbRes.ok(adRushService.getAdRushListBySiteId(siteId, states, types, page, pageSize)
                .map(AdRushService::adRush2model).map(r -> {
                    Optional<ActiveTime> optionalReveal = adRushService.getActiveTimeByAdRushId(r.getId());
                    if (optionalReveal.isPresent()) {
                        ActiveTime activeTime = optionalReveal.get();
                        r.setActiveTimes(Collections.singletonList(AdRushService.activeTime2model(activeTime)));
                    }
                    return r;
                }));
    }

}
