package com.kaiba.m.core.controller.api;

import com.kaiba.lib.base.constant.KbProperties;
import com.kaiba.lib.base.constant.note.NoteThreadCondition;
import com.kaiba.lib.base.domain.note.NoteThreadModel;
import com.kaiba.lib.base.domain.site.SiteModel;
import com.kaiba.lib.base.middleware.KbRes;
import com.kaiba.lib.base.response.KbEntity;
import com.kaiba.lib.base.service.ISiteService;
import com.kaiba.lib.base.service.INoteService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import java.util.Optional;

@Slf4j
@RestController
public class NewsInformerServiceController {

    private final static String SLIP_NOTE_THREAD_KEY = "slip_note_thread_7d13b4a9c725b81cc63e4c93821bf529";
    private final INoteService noteService;
    private final ISiteService siteService;
    private final KbRes kbRes;

    public NewsInformerServiceController(INoteService noteService, ISiteService siteService, KbRes kbRes) {
        this.noteService = noteService;
        this.siteService = siteService;
        this.kbRes = kbRes;
    }

    @PostMapping(path = "/newsInformer/getSlipNoteThread")
    public KbEntity<NoteThreadModel> getSlipNoteThread(
            @RequestParam(name = "siteId") Integer siteId
    ) {
        String key = SLIP_NOTE_THREAD_KEY + "_" + siteId;
        Optional<NoteThreadModel> optional = noteService.getThreadByKey(key).data();
        if (optional.isPresent()) {
            return kbRes.ok(optional.get());
        } else {
            SiteModel siteModel = siteService.getSiteById(siteId).dataOrThrow();
            NoteThreadModel noteThreadModel = new NoteThreadModel();
            noteThreadModel.setKey(key);
            noteThreadModel.setCreatorId(KbProperties.SYSTEM_USER_ID);
            noteThreadModel.setSiteId(siteId);
            noteThreadModel.setTitle(siteModel.getName() + "-爆料小程序-小纸条");
            noteThreadModel.setDescription(siteModel.getName() + "爆料小程序-小纸条功能板块");
            noteThreadModel.setHotMax(0);
            noteThreadModel.setTopMax(0);
            noteThreadModel.setMixReviewPageable(false);
            noteThreadModel.setRouteThreads(null);
            noteThreadModel.setCondition(NoteThreadCondition.FREE.getValue());
            NoteThreadModel newNoteThreadModel = noteService.createThreadByBody(noteThreadModel).dataOrThrow();
            return kbRes.ok(newNoteThreadModel);
        }
    }
}
