package com.kaiba.m.core.controller.app.widgetbanner;

import com.kaiba.lib.base.assembler.Mapper;
import com.kaiba.lib.base.constant.KbHeader;
import com.kaiba.lib.base.domain.appwidget.banner.BannerInstanceModel;
import com.kaiba.lib.base.middleware.KbRes;
import com.kaiba.lib.base.response.KbCode;
import com.kaiba.lib.base.response.KbEntity;
import com.kaiba.lib.base.response.KbException;
import com.kaiba.m.core.service.appcomponent.banner.AppBannerCacheModel;
import com.kaiba.m.core.service.appcomponent.banner.AppBannerCacheService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * author: lyux
 * date: 2023-05-22
 */
@Slf4j
@RestController
@RequestMapping("/api/AppBanner")
@Validated
public class AppBannerApiController {

    private final KbRes kbRes;
    private final AppBannerCacheService cacheService;

    public AppBannerApiController(
            @Qualifier("kbApiRes") KbRes kbRes,
            AppBannerCacheService cacheService
    ) {
        this.kbRes = kbRes;
        this.cacheService = cacheService;
    }

    @PostMapping("/getBannerByInstance")
    public KbEntity<BannerInstanceModel> getBannerByInstance(
            @RequestHeader(KbHeader.KB_SITE_ID) Integer siteId,
            @RequestParam(required = false) String instanceId,
            @RequestParam(required = false) String instanceKey
    ) {
        AppBannerCacheModel cache = cacheService.getCacheByInstance(siteId, instanceId, instanceKey).orElse(null);
        if (cache == null) {
            log.info("banner instance cache not found, siteId={}, instanceId={}, instanceKey={}", siteId, instanceId, instanceKey);
            return kbRes.ok();
        }
        if (!cache.isValid()) {
            return kbRes.err(KbCode.RESOURCE_NOT_FOUND, "instance not found", "数据跑丢了");
        } else {
            BannerInstanceModel model = Mapper.map(cache.getInstance(), BannerInstanceModel.class);
            if (cache.getOnlineBannerList() == null || cache.getOnlineBannerList().isEmpty()) {
                model.setImageList(cache.getDefaultBannerList());
            } else {
                model.setImageList(cache.getOnlineBannerList());
            }
            return kbRes.ok(model);
        }
    }

}
