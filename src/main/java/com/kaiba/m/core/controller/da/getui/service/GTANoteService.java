package com.kaiba.m.core.controller.da.getui.service;

import com.kaiba.lib.base.cache.LazyExpireCache;
import com.kaiba.lib.base.constant.circle.CircleCategoryType;
import com.kaiba.lib.base.domain.circle.CircleCategoryModel;
import com.kaiba.lib.base.domain.circle.CircleThreadModel;
import com.kaiba.lib.base.domain.common.Image;
import com.kaiba.lib.base.domain.note.NoteGrainFlag;
import com.kaiba.lib.base.domain.note.NoteModel;
import com.kaiba.lib.base.domain.note.NoteThreadModel;
import com.kaiba.lib.base.lang.collections.KbColUtils;
import com.kaiba.lib.base.response.KbCode;
import com.kaiba.lib.base.response.KbException;
import com.kaiba.lib.base.service.ICircleService;
import com.kaiba.lib.base.service.INoteService;
import com.kaiba.lib.base.util.StringUtils;
import com.kaiba.m.core.controller.da.getui.model.GTANoteModel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 个推大屏帖子相关服务
 * <AUTHOR>
 * @version GTANoteService, v0.1 2025/2/20 16:23 daopei Exp $
 **/
@Slf4j
@Service
public class GTANoteService {

    private final INoteService noteService;
    private final ICircleService circleService;

    private final LazyExpireCache<List<GTANoteModel>> normalCircleCache = new LazyExpireCache.Builder<List<GTANoteModel>>()
            .setExpireTime(Duration.ofMinutes(1).toMillis())
            .setSupplier(this::loadNormalCircleRecentNote)
            .create();
    private final LazyExpireCache<List<GTANoteModel>> personalCircleCache = new LazyExpireCache.Builder<List<GTANoteModel>>()
            .setExpireTime(Duration.ofMinutes(1).toMillis())
            .setSupplier(this::loadPersonalTopicRecentNote)
            .create();
    private final LazyExpireCache<List<GTANoteModel>> hbxCache = new LazyExpireCache.Builder<List<GTANoteModel>>()
            .setExpireTime(Duration.ofMinutes(1).toMillis())
            .setSupplier(this::loadHangBangXiaRecentNote)
            .create();

    public GTANoteService(
            INoteService noteService,
            ICircleService circleService
    ) {
        this.noteService = noteService;
        this.circleService = circleService;
    }

    // ------------------------------------------------

    public List<GTANoteModel> getHangBangXiaRecentNote() {
        return hbxCache.getData();
    }

    public List<GTANoteModel> getNormalCircleRecentNote() {
        return normalCircleCache.getData();
    }

    public List<GTANoteModel> getPersonalTopicRecentNote() {
        return personalCircleCache.getData();
    }

    // ------------------------------------------------

    private List<GTANoteModel> loadHangBangXiaRecentNote() {
        List<String> hbxThreads = noteService.getThreadByKey("reveal_program_main_thread_reveal_key_9").data()
                .map(NoteThreadModel::getId)
                .map(Collections::singletonList)
                .orElseThrow(() -> new KbException(KbCode.RESOURCE_NOT_FOUND, "hangbangxia thread not exists"));
        List<NoteModel> notes = loadRecentGTNoteList(hbxThreads, 10);
        return notes.stream().map(GTANoteService::map2GTANote).collect(Collectors.toList());
    }

    private List<GTANoteModel> loadNormalCircleRecentNote() {
        List<String> plazaThreads = circleService.getPlazaCircleBySite(9).data()
                .map(NoteThreadModel::getId)
                .map(Collections::singletonList)
                .orElseThrow(() -> new KbException(KbCode.RESOURCE_NOT_FOUND, "plaza thread not exists"));
        List<NoteModel> notes = loadRecentGTNoteList(plazaThreads, 10);
        return notes.stream().map(GTANoteService::map2GTANote).collect(Collectors.toList());
    }

    private List<GTANoteModel> loadPersonalTopicRecentNote() {
        List<CircleCategoryModel> categoryList = circleService
                .getCategoryList(9, CircleCategoryType.TOPIC_MODERATOR.getValue(), 1, 100)
                .dataOrThrow();
        Set<String> circleIds = categoryList.stream().map(CircleCategoryModel::getCircleId).collect(Collectors.toSet());
        Map<String, CircleThreadModel> circleMap = circleService.getCircleMapByCircleIds(circleIds).dataOrThrow();
        List<GTANoteModel> list = new LinkedList<>();
        List<String> noteIds = new ArrayList<>();
        long endTime = System.currentTimeMillis() / 1000;
        long startTime = endTime - TimeUnit.DAYS.toSeconds(30);
        for (CircleCategoryModel category : categoryList) {
            CircleThreadModel circle = circleMap.get(category.getCircleId());
            if (category.getUserId() == null || circle == null || circle.getId() == null) {
                continue;
            }
            noteService.getNoteListByCreatorId(
                    category.getUserId(), null, 9, circle.getId(),
                    default_grain.getFlag(), startTime, endTime, 1, 30)
                    .data().orElse(Collections.emptyList()).forEach(note -> {
                        if (!noteIds.contains(note.getId()) && !StringUtils.isEmpty(note.getContent()) && !KbColUtils.ListOpt.isEmpty(note.getImages())) {
                            list.add(map2GTANote(note));
                            noteIds.add(note.getId());
                        }
                    });
        }
        list.sort(Comparator.comparing(GTANoteModel::getCreateTime).reversed());
        return KbColUtils.ListOpt.getSubListByPage(list, 0, 20);
    }

    // ------------------------------------------------

    /**
     * 多次翻页查询满足条件的帖子
     * 1.有文字内容
     * 2.有图片内容
     *
     * tips:数据范围至多控制在5000以内
     */
    private List<NoteModel> loadRecentGTNoteList(List<String> threadIds, Integer limit) {
        List<NoteModel> matchNote = new ArrayList<>();
        for (int i = 1; i<= 100; i++) {
            List<NoteModel> notes = getNoteListByThreads(threadIds, i, 50);
            if (notes.isEmpty()) {
                break;
            }
            for (NoteModel note : notes) {
                if (!StringUtils.isEmpty(note.getContent()) && !KbColUtils.ListOpt.isEmpty(note.getImages())) {
                    matchNote.add(note);
                }
            }
            if (matchNote.size() >= limit) {
                break;
            }
        }
        return matchNote;
    }

    private List<NoteModel> getNoteListByThreads(List<String> threadIds, Integer page, Integer pageSize) {
        return noteService.getNoteListByThreadIn(threadIds.toArray(new String[]{}), null, default_grain.getFlag(), page, pageSize)
                .dataIgnoreError()
                .orElse(new ArrayList<>());
    }

    private static GTANoteModel map2GTANote(NoteModel note) {
        if (note == null) {
            return null;
        }
        GTANoteModel gtaNoteModel = new GTANoteModel();
        gtaNoteModel.setContent(note.getContent());
        gtaNoteModel.setUserName(note.getUser().getUserName());
        gtaNoteModel.setNoteId(note.getId());
        gtaNoteModel.setCreateTime(note.getCreateTimeMS());
        if (!KbColUtils.ListOpt.isEmpty(note.getImages())) {
           gtaNoteModel.setImages(note.getImages().stream().map(Image::getImageUrl).collect(Collectors.toList()));
        }
        if (note.getThreads() != null) {
            List<String> circleTitle =
                    note.getThreads().stream()
                    .filter(t -> t.getAttr() != null && t.getAttr().containsKey("circleTopic") && "4".equals(t.getAttr().get("circleTopic")))
                    .map(NoteThreadModel::getTitle)
                    .collect(Collectors.toList());
            gtaNoteModel.setCircleTitle(circleTitle);
        }
        return gtaNoteModel;
    }

    private static final NoteGrainFlag default_grain = NoteGrainFlag.createMinimumFlagBuilder()
            .setNeedUserInfo(true)
            .setNeedContent(true)
            .setNeedOriginThread(true)
            .setNeedThreads(true)
            .setAllowCache(true)
            .create();
}
