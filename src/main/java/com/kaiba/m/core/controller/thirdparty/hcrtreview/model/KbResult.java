package com.kaiba.m.core.controller.thirdparty.hcrtreview.model;

import com.kaiba.m.core.controller.thirdparty.hcrtreview.constant.KbBizType;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Collections;
import java.util.List;

/**
 * author: lyux
 * date: 2024-10-24
 */
@Data
@NoArgsConstructor
public class KbResult {

    /** 内容所属业务模块. */
    private String biz;

    /** 内容列表. */
    private List<KbContent> contents;

    /** 下一页的页码. */
    private Integer nextPage;

    /** 是否还有下一页数据. */
    private Boolean hasNext;

    public KbResult(KbBizType bizType) {
        this.biz = bizType.name();
    }

    public static KbResult asLast(KbBizType bizType) {
        KbResult result = new KbResult();
        result.biz = bizType.name();
        result.hasNext = false;
        result.contents = Collections.emptyList();
        return result;
    }

    @Override
    public String toString() {
        int contentSize = contents == null ? -1 : contents.size();
        return "HCRTReview-" + biz + " result, size=" + contentSize + ", next=" + nextPage;
    }
}
