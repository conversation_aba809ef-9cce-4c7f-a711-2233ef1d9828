package com.kaiba.m.core.controller.da.eventtrack;

import com.kaiba.lib.base.middleware.KbRes;
import com.kaiba.lib.base.response.KbEntity;
import com.kaiba.m.core.service.da.eventtrack.ETDocSyncService;
import com.kaiba.m.core.service.da.eventtrack.push.ETDocPushService;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @version ETDocDataController, v0.1 2025/3/28 13:47 daopei Exp $
 **/
@Slf4j
@Validated
@RestController
@RequestMapping("/eventTrack/etDoc/data")
public class ETDocDataController {

    private final KbRes kbRes;
    private final ETDocSyncService syncService;
    private final ETDocPushService pushService;

    public ETDocDataController(
            KbRes kbRes,
            ETDocSyncService syncService,
            ETDocPushService pushService
    )
    {
        this.kbRes = kbRes;
        this.syncService = syncService;
        this.pushService = pushService;
    }


    @ApiOperation("稿件同步任务触发(管理后台不接入该接口使用)")
    @PostMapping("/sync/syncAll")
    public KbEntity<Void> syncAll() {
        syncService.syncAll();
        return kbRes.ok();
    }

    @ApiOperation("稿件同步任务触发(管理后台不接入该接口使用)")
    @PostMapping("/sync/syncAllByStrategy")
    public KbEntity<Void> syncAllByStrategy(
            @RequestParam String strategy
    ) {
        syncService.syncStrategyByAll(strategy);
        return kbRes.ok();
    }

    @ApiOperation("稿件模块文章分组关联规则初始化")
    @PostMapping("/initArticleRule")
    public KbEntity<Void> initArticleRule()
    {
        syncService.initArticleRule();
        return kbRes.ok();
    }
}
