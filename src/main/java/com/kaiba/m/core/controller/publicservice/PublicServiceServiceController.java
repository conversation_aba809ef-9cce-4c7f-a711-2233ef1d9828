package com.kaiba.m.core.controller.publicservice;

import com.kaiba.lib.base.domain.publicservice.*;
import com.kaiba.lib.base.middleware.KbRes;
import com.kaiba.lib.base.response.KbCode;
import com.kaiba.lib.base.response.KbEntity;
import com.kaiba.lib.base.service.IPublicServiceService;
import com.kaiba.m.core.service.publicservice.PublicServiceApiService;
import com.kaiba.m.core.service.publicservice.PublicServiceService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.web.bind.annotation.RestController;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/1/8 10:32
 **/
@Slf4j
@RestController
public class PublicServiceServiceController implements IPublicServiceService {

    private final KbRes kbRes;
    private final PublicServiceService publicServiceService;
    private final PublicServiceApiService apiService;

    public PublicServiceServiceController(
            KbRes kbRes,
            PublicServiceService publicServiceService,
            PublicServiceApiService apiService
    ) {
        this.kbRes = kbRes;
        this.publicServiceService = publicServiceService;
        this.apiService = apiService;
    }

    @Override
    public KbEntity<PublicServiceInstanceModel> addServiceInstance(Integer userId, PublicServiceInstanceModel model) {
        model.setUserId(userId);
        return kbRes.ok(publicServiceService.addServiceInstance(model));
    }

    @Override
    public KbEntity<String> editBannerKey(String instanceKey, String bannerKey) {
        return kbRes.ok(publicServiceService.editBannerKey(instanceKey, bannerKey));
    }

    @Override
    public KbEntity<PublicServiceInstanceModel> getServiceInstance(String instanceKey) {
        return kbRes.ok(publicServiceService.getServiceInstance(instanceKey));
    }

    @Override
    public KbEntity<PublicServiceGroupModel> addServiceGroup(PublicServiceGroupModel model) {
        try {
            return kbRes.ok(publicServiceService.addServiceGroup(model));
        } catch (DuplicateKeyException e) {
            return kbRes.err(KbCode.ILLEGAL_ARGUMENT, null, "分组名称已存在");
        }
    }

    @Override
    public KbEntity<Void> bulkUpdateGroupOrder(PublicServiceGroupOrderModel model) {
        publicServiceService.bulkUpdateGroupOrder(model);
        return kbRes.ok();
    }

    @Override
    public KbEntity<PublicServiceGroupModel> editServiceGroup(PublicServiceGroupModel model) {
        return kbRes.ok(publicServiceService.editServiceGroup(model));
    }

    @Override
    public KbEntity<Void> deleteServiceGroup(String groupId) {
        publicServiceService.deleteServiceGroup(groupId);
        return kbRes.ok();
    }

    @Override
    public KbEntity<List<PublicServiceGroupModel>> getServiceGroupList(
            Integer siteId, String instanceKey, String name, Integer type, Integer state, Integer page, Integer pageSize
    ) {
        return kbRes.ok(publicServiceService.getServiceGroupListByInstanceKey(instanceKey, name, type, state, page, pageSize));
    }

    @Override
    public KbEntity<PublicServiceGroupModel> updateServiceGroupStateAsDisable(Integer userId, String groupId) {
        log.info("service group {} update state as disable, by user: {}", groupId, userId);
        return kbRes.ok(publicServiceService.updateServiceGroupStateAsDisable(groupId));
    }

    @Override
    public KbEntity<PublicServiceGroupModel> updateServiceGroupStateAsEnable(Integer userId, String groupId) {
        log.info("service group {} update state as enable, by user: {}", groupId, userId);
        return kbRes.ok(publicServiceService.updateServiceGroupStateAsEnable(groupId));
    }

    @Override
    public KbEntity<PublicServiceItemModel> addServiceItem(PublicServiceItemModel model) {
        try {
            return kbRes.ok(publicServiceService.addServiceItem(model));
        } catch (DuplicateKeyException e) {
            return kbRes.err(KbCode.ILLEGAL_ARGUMENT, null, "分组名称已存在");
        }
    }

    @Override
    public KbEntity<PublicServiceItemModel> editServiceItem(PublicServiceItemModel model) {
        return kbRes.ok(publicServiceService.editServiceItem(model));
    }

    @Override
    public KbEntity<List<PublicServiceItemModel>> getServiceItemList(String instanceKey, String name, Integer state, Integer page, Integer pageSize) {
        return kbRes.ok(publicServiceService.getServiceItemListByInstanceKey(instanceKey, name, state, page, pageSize));
    }

    @Override
    public KbEntity<List<PublicServiceItemModel>> getServiceItemListByName(String instanceKey, String name, Integer state) {
        return kbRes.ok(publicServiceService.getServiceItemListByInstanceKeyAndNameContain(instanceKey, name, state));
    }

    @Override
    public KbEntity<PublicServiceItemModel> updateServiceItemStateAsDisable(Integer userId, String itemId) {
        log.info("service item {} update state as disable, by user: {}", itemId, userId);
        return kbRes.ok(publicServiceService.updateServiceItemStateAsDisable(itemId));
    }

    @Override
    public KbEntity<PublicServiceItemModel> updateServiceItemStateAsEnable(Integer userId, String itemId) {
        log.info("service item {} update state as enable, by user: {}", itemId, userId);
        return kbRes.ok(publicServiceService.updateServiceItemStateAsEnable(itemId));
    }

    @Override
    public KbEntity<Void> addItemsToGroup(String groupId, String[] itemIds) {
        publicServiceService.addItemsToGroup(groupId, itemIds);
        return kbRes.ok();
    }

    @Override
    public KbEntity<Void> bulkUpdateItemOrder(PublicServiceItemOrderModel model) {
        publicServiceService.bulkUpdateItemOrder(model);
        return kbRes.ok();
    }

    @Override
    public KbEntity<List<PublicServiceItemModel>> getServiceGroupItemList(String groupId, Integer page, Integer pageSize) {
        return kbRes.ok(publicServiceService.getServiceGroupItemList(groupId, page, pageSize));
    }

    @Override
    public KbEntity<Void> removeItemFromGroup(String groupId, String itemId) {
        publicServiceService.removeItemFromGroup(groupId, itemId);
        return kbRes.ok();
    }

    @Override
    public KbEntity<List<PublicServiceModel>> searchService(String instanceKey) {
        List<PublicServiceModel> list = apiService.getPublicServiceList(instanceKey).getList();
        return kbRes.ok(list == null ? Collections.emptyList() : list);
    }
}
