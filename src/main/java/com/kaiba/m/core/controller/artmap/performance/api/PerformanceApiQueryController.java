package com.kaiba.m.core.controller.artmap.performance.api;

import com.kaiba.lib.base.domain.artmap.PerformanceModel;
import com.kaiba.lib.base.middleware.KbRes;
import com.kaiba.lib.base.response.KbEntity;
import com.kaiba.m.core.model.artmap.CalenderPerformanceModel;
import com.kaiba.m.core.model.artmap.dto.CalendarPerformQueryDTO;
import com.kaiba.m.core.service.artmap.PerformanceQueryService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.List;
import java.util.Objects;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort.Direction;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * Description: 艺术地图演艺演出查询
 * Author: ZM227
 * Date: 2025/6/13 9:38
 */
@Slf4j
@RestController
@RequestMapping("/api/artMap/performance/query")
@Api(tags = "艺术地图演艺演出查询")
public class PerformanceApiQueryController {

    @Resource
    private KbRes kbRes;
    @Resource
    private PerformanceQueryService performanceQueryService;

    @PostMapping("/obj/queryFrontPerformanceList")
    @ApiOperation(value = "查询一周内的演出列表，首页专用")
    public KbEntity<List<CalenderPerformanceModel>> queryFrontPerformanceList() {
        return kbRes.ok(performanceQueryService.queryFrontPerformanceList());
    }

    @PostMapping("/obj/queryPerformanceList")
    @ApiOperation(value = "查询单日演出列表")
    public KbEntity<List<PerformanceModel>> queryPerformanceList(
        @RequestParam Long performanceDate,
        @RequestParam(required = false, defaultValue = "1") Integer page,
        @RequestParam(required = false, defaultValue = "20") Integer pageSize) {
        CalendarPerformQueryDTO queryDTO = new CalendarPerformQueryDTO();
        queryDTO.setPerformanceDateStart(Instant.ofEpochMilli(performanceDate));
        queryDTO.setPerformanceDateEnd(Instant.ofEpochMilli(performanceDate));
        if (Objects.nonNull(page) && Objects.nonNull(pageSize)) {
            queryDTO.setPageable(PageRequest.of(page - 1, pageSize));
        }
        return kbRes.ok(performanceQueryService.queryPerformanceList(queryDTO));
    }

    @PostMapping("/obj/queryTodayPerformanceByVenues")
    @ApiOperation(value = "查询场馆当天演出列表")
    public KbEntity<List<PerformanceModel>> queryTodayPerformanceByVenues(
        @RequestParam String venuesCode,
        @RequestParam(required = false, defaultValue = "1") Integer page,
        @RequestParam(required = false, defaultValue = "20") Integer pageSize) {
        CalendarPerformQueryDTO queryDTO = new CalendarPerformQueryDTO();
        Instant today = LocalDate.now().atStartOfDay(ZoneId.systemDefault()).toInstant();
        queryDTO.setPerformanceDateStart(today);
        queryDTO.setPerformanceDateEnd(today);
        queryDTO.setVenuesCode(venuesCode);
        if (Objects.nonNull(page) && Objects.nonNull(pageSize)) {
            queryDTO.setPageable(PageRequest.of(page - 1, pageSize));
        }
        return kbRes.ok(performanceQueryService.queryPerformanceList(queryDTO));
    }

    @PostMapping("/obj/queryRangePerformances")
    @ApiOperation(value = "查询时间段演出列表")
    public KbEntity<List<PerformanceModel>> queryRangePerformances(
        @RequestParam Long performanceDateStart,
        @RequestParam Long performanceDateEnd,
        @RequestParam(required = false, defaultValue = "1") Integer page,
        @RequestParam(required = false, defaultValue = "20") Integer pageSize) {
        CalendarPerformQueryDTO queryDTO = new CalendarPerformQueryDTO();
        queryDTO.setPerformanceDateStart(Instant.ofEpochMilli(performanceDateStart));
        queryDTO.setPerformanceDateEnd(Instant.ofEpochMilli(performanceDateEnd));
        if (Objects.nonNull(page) && Objects.nonNull(pageSize)) {
            queryDTO.setPageable(PageRequest.of(page - 1, pageSize));
        }
        return kbRes.ok(performanceQueryService.queryPerformanceList(queryDTO));
    }

    @PostMapping("/obj/queryVenuesRangePerformances")
    @ApiOperation(value = "查询时间段演出列表")
    public KbEntity<List<PerformanceModel>> queryRangePerformances(
        @RequestParam String venuesCode,
        @RequestParam Long performanceDateStart,
        @RequestParam Long performanceDateEnd,
        @RequestParam(required = false, defaultValue = "1") Integer page,
        @RequestParam(required = false, defaultValue = "20") Integer pageSize) {
        CalendarPerformQueryDTO queryDTO = new CalendarPerformQueryDTO();
        queryDTO.setPerformanceDateStart(Instant.ofEpochMilli(performanceDateStart));
        queryDTO.setPerformanceDateEnd(Instant.ofEpochMilli(performanceDateEnd));
        queryDTO.setVenuesCode(venuesCode);
        if (Objects.nonNull(page) && Objects.nonNull(pageSize)) {
            queryDTO.setPageable(PageRequest.of(page - 1, pageSize));
        }
        return kbRes.ok(performanceQueryService.queryVenuesPerformanceList(queryDTO));
    }

}
