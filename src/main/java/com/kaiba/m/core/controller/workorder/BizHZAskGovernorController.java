package com.kaiba.m.core.controller.workorder;

import com.kaiba.lib.base.annotation.api.KbCheckSignature;
import com.kaiba.lib.base.annotation.apiparam.KbPage;
import com.kaiba.lib.base.annotation.apiparam.KbPageSize;
import com.kaiba.lib.base.annotation.apiparam.KbUserId;
import com.kaiba.lib.base.assembler.Mapper;
import com.kaiba.lib.base.cache.LazyExpireCache;
import com.kaiba.lib.base.constant.KbHeader;
import com.kaiba.lib.base.constant.KbSignType;
import com.kaiba.lib.base.constant.sms.SmsCodeMark;
import com.kaiba.lib.base.constant.workorder.WOBusiness;
import com.kaiba.lib.base.constant.workorder.WOCaseState;
import com.kaiba.lib.base.constant.workorder.WOIdentity;
import com.kaiba.lib.base.constant.workorder.WOOperation;
import com.kaiba.lib.base.domain.user.UserModel;
import com.kaiba.lib.base.domain.workorder.*;
import com.kaiba.lib.base.domain.workorder.hzaskgovernor.AGCaseCreateModel;
import com.kaiba.lib.base.domain.workorder.hzaskgovernor.AGContentModel;
import com.kaiba.lib.base.domain.workorder.hzaskgovernor.AGMainPageModel;
import com.kaiba.lib.base.domain.workorder.hzaskgovernor.AGUserRatingModel;
import com.kaiba.lib.base.lang.collections.KbColUtils;
import com.kaiba.lib.base.middleware.KbRes;
import com.kaiba.lib.base.response.KbCode;
import com.kaiba.lib.base.response.KbEntity;
import com.kaiba.lib.base.rte.RelativeTimeExpression;
import com.kaiba.m.core.domain.workorder.WOCase;
import com.kaiba.m.core.domain.workorder.WOEvent;
import com.kaiba.m.core.domain.workorder.WOTeam;
import com.kaiba.m.core.domain.workorder.hzaskgovernor.AGCaseContent;
import com.kaiba.m.core.domain.workorder.hzaskgovernor.AGResolverStat;
import com.kaiba.m.core.service.workorder.*;
import com.kaiba.m.core.service.workorder.hzaskgovernor.HZAskGovernorConsts;
import com.kaiba.m.core.service.workorder.hzaskgovernor.HZAskGovernorJobService;
import com.kaiba.m.core.service.workorder.hzaskgovernor.HZAskGovernorService;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.time.Duration;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * author: lyux
 * date: 2024-01-17
 */
@Slf4j
@RestController
@RequestMapping("/WorkOrder/biz/HZAskGovernor")
@Validated
public class BizHZAskGovernorController {

    private static final int PUBLIC_LIST_SIZE = 120;

    private final KbRes kbRes;
    private final WOCaseApiService apiService;
    private final WOTeamService teamService;
    private final WOCaseOperator operator;
    private final WOCaseStatService statService;
    private final WOMemberAccountService memberAccountService;
    private final HZAskGovernorService askGovernorService;
    private final HZAskGovernorJobService askGovernorJobService;

    public BizHZAskGovernorController(
            KbRes kbRes,
            WOCaseApiService apiService,
            WOTeamService teamService,
            WOCaseOperator operator,
            WOCaseStatService statService,
            WOMemberAccountService memberAccountService,
            HZAskGovernorService askGovernorService,
            HZAskGovernorJobService askGovernorJobService
    ) {
        this.kbRes = kbRes;
        this.apiService = apiService;
        this.teamService = teamService;
        this.operator = operator;
        this.statService = statService;
        this.memberAccountService = memberAccountService;
        this.askGovernorService = askGovernorService;
        this.askGovernorJobService = askGovernorJobService;
    }

    // ------------------------------------------------------------

    /** {@link WOOperation#CREATE} */
    @ApiOperation("提问者创建案件")
    @PostMapping("/toc/usr/client/createCase")
    public KbEntity<WOCaseModel<AGCaseContent>> createCaseByClient(
            @RequestHeader(KbHeader.KB_USER_ID) @KbUserId Integer userId,
            @RequestBody() AGCaseCreateModel model
    ) {
        WOTeamContext resolverContext = null;
        if (model.getOriginResolver() != null) {
            resolverContext = teamService.getContextByTeamOrThrow(
                    WOBusiness.HZ_ASK_GOVERNOR.name(), model.getOriginResolver());
        }
        // TODO: 手机号验证码逻辑
        if (model.getClientMobile() == null && model.getPersonalMobile() != null) {
            model.setClientMobile(model.getPersonalMobile().getData());
        }
        if (model.getClientName() == null && model.getPersonalName() != null) {
            model.setClientName(model.getPersonalName().getData());
        }
        AGCaseContent content = askGovernorService.createContent(model, userId);
        WOCase woCase = askGovernorService.createCaseByClient(model, content, userId);
        WOTeamContext clientTeam = teamService.getContextByTeamOrThrow(
                WOBusiness.HZ_ASK_GOVERNOR.name(), WOIdentity.CLIENT.asTeam());
        WOCaseModel<AGCaseContent> agCase = apiService.case2model(woCase, userId, clientTeam.getTeam());
        if (resolverContext != null) {
            String message = getAcceptMessage(resolverContext.getName());
            operator.accept(
                    woCase, woCase.getOriginResolver(),
                    WOConsts.SYSTEM_USER_ID, WOIdentity.SYSTEM.asTeam(),
                    null, message);
        }
        return kbRes.ok(agCase);
    }

    @ApiOperation("创建案件时填写的手机验证码获取")
    @KbCheckSignature(type = KbSignType.ACCOUNT)
    @PostMapping("/toc/usr/client/sendClientMobileVCode")
    public KbEntity<Void> sendClientMobileVCode(
            @RequestHeader(KbHeader.KB_USER_ID) @KbUserId Integer userId,
            @RequestParam(required = false) String mobile
    ) {
        return memberAccountService.sendSMSCode(userId, mobile, SmsCodeMark.WORK_ORDER_CLIENT_MOBILE);
    }

    @ApiOperation("获取首页数据")
    @PostMapping("/toc/obj/getMainPageModel")
    public KbEntity<AGMainPageModel> getMainPageModel() {
        return kbRes.ok(mainPageCache.getData());
    }

    @ApiOperation("获取公开的案件列表")
    @PostMapping("/toc/ifu/getPublicCaseList")
    public KbEntity<List<WOCaseModel<Object>>> getPublicCaseList(
            @RequestParam(required = false, defaultValue = "1") Integer page,
            @RequestParam(required = false, defaultValue = "20") Integer pageSize
    ) {
        List<WOCaseModel<Object>> list = publicCaseCache.getData();
        return kbRes.ok(KbColUtils.ListOpt.getSubListByPage(list, page - 1, pageSize));
    }

    // --------------------------------------------------------------

    /** {@link WOOperation#CREATE} */
    @ApiOperation("主持者创建案件")
    @PostMapping("/tob/manager/createCase")
    public KbEntity<WOCaseModel<AGCaseContent>> createCaseByManager(
            @RequestHeader(KbHeader.KB_USER_ID) @KbUserId Integer userId,
            @RequestBody() AGCaseCreateModel model
    ) {
        WOTeamContext managerTeam = teamService
                .getContextByTeamOrThrow(WOBusiness.HZ_ASK_GOVERNOR.name(), WOIdentity.MANAGER.asTeam())
                .memberOrThrow(userId);
        WOTeamContext resolverContext = null;
        if (model.getOriginResolver() != null) {
            resolverContext = teamService.getContextByTeamOrThrow(
                    WOBusiness.HZ_ASK_GOVERNOR.name(), model.getOriginResolver());
        }
        AGCaseContent content = askGovernorService.createContent(model, userId);
        WOCase woCase = askGovernorService.createCaseByManager(model, content, userId);
        WOCaseModel<AGCaseContent> agCase = apiService.case2model(woCase, userId, managerTeam.getTeam());
        if (resolverContext != null) {
            String message = getAcceptMessage(resolverContext.getName());
            operator.accept(
                    woCase, woCase.getOriginResolver(), userId, WOIdentity.MANAGER.asTeam(), null, message);
        }
        return kbRes.ok(agCase);
    }

    @ApiOperation("主持者统计所有解决者数据")
    @PostMapping("/tob/manager/statAllResolversByManager")
    public KbEntity<Void> statAllResolversByManager(
            @RequestHeader(KbHeader.KB_USER_ID) @KbUserId Integer userId
    ) {
        teamService.getContextByTeamOrThrow(WOBusiness.HZ_ASK_GOVERNOR.name(), WOIdentity.MANAGER.asTeam())
                .memberOrThrow(userId);
        askGovernorJobService.statAllTeam(null);
        return kbRes.ok();
    }

    @ApiOperation("主持者统计团队数据")
    @PostMapping("/tob/manager/statTeamByManager")
    public KbEntity<AGResolverStat> statTeamByManager(
            @RequestHeader(KbHeader.KB_USER_ID) @KbUserId Integer userId,
            @RequestParam() String team,
            @RequestParam(required = false) Long time
    ) {
        teamService.getContextByTeamOrThrow(WOBusiness.HZ_ASK_GOVERNOR.name(), WOIdentity.MANAGER.asTeam())
                .memberOrThrow(userId);
        WOTeam statTeam = teamService.getContextByTeamOrThrow(WOBusiness.HZ_ASK_GOVERNOR.name(), team).getTeam();
        if (time == null) {
            time = System.currentTimeMillis() - TimeUnit.DAYS.toMillis(365);
        }
        return kbRes.ok(askGovernorJobService.generateTeamStat(statTeam, time));
    }

    @ApiOperation("主持者获取团队统计数据")
    @PostMapping("/tob/manager/getTeamStatByTeam")
    public KbEntity<AGResolverStat> getTeamStatByTeam(
            @RequestHeader(KbHeader.KB_USER_ID) @KbUserId Integer userId,
            @RequestParam() String team
    ) {
        teamService.getContextByTeamOrThrow(WOBusiness.HZ_ASK_GOVERNOR.name(), WOIdentity.MANAGER.asTeam())
                .memberOrThrow(userId);
        WOTeam t = teamService.getContextByTeamOrThrow(WOBusiness.HZ_ASK_GOVERNOR.name(), team).getTeam();
        return askGovernorService.getTeamStatByTeamId(t.getId())
                .map(kbRes::ok)
                .orElseGet(() -> kbRes.err(KbCode.RESOURCE_NOT_FOUND));
    }

    @ApiOperation("主持者获取团队统计数据")
    @PostMapping("/tob/manager/getTeamStatList")
    public KbEntity<List<AGResolverStat>> getTeamStatList(
            @RequestHeader(KbHeader.KB_USER_ID) @KbUserId Integer userId,
            @RequestParam(required = false, defaultValue = "1") @KbPage Integer page,
            @RequestParam(required = false, defaultValue = "20") @KbPageSize Integer pageSize,
            @RequestParam(required = false, defaultValue = "ratingAverage") String sortBy
    ) {
        teamService.getContextByTeamOrThrow(WOBusiness.HZ_ASK_GOVERNOR.name(), WOIdentity.MANAGER.asTeam())
                .memberOrThrow(userId);
        return kbRes.ok(askGovernorService.getTeamStatRank(page, pageSize, sortBy));
    }

    @PostMapping("/tob/manager/testGenerateMainPageCache")
    public KbEntity<AGMainPageModel> testGenerateMainPageCache() {
        return kbRes.ok(generateMainPageCache());
    }

    @PostMapping("/tob/manager/initOriginList")
    public KbEntity<List<WOCaseOriginModel>> initOriginList() {
        String biz = WOBusiness.HZ_ASK_GOVERNOR.name();
        statService.createOrigin(biz, "WX_GZH_89_KEYWORD", "89公众号关键词", null);
        statService.createOrigin(biz, "WX_GZH_89_MENU", "89公众号菜单", null);
        statService.createOrigin(biz, "HOOLO_APP", "葫芦网-APP", null);
        statService.createOrigin(biz, "HOOLO_TEL", "葫芦网-电话", null);
        statService.createOrigin(biz, "HOOLO_QQ", "葫芦网-QQ", null);
        statService.createOrigin(biz, "HOOLO_WX", "葫芦网-WX", null);
        return kbRes.ok(statService.getOriginPageByBiz(WOBusiness.HZ_ASK_GOVERNOR.name(), false, 1, 30)
                .map(origin -> Mapper.map(origin, WOCaseOriginModel.class)));
    }

    // --------------------------------------------------------------


    private final LazyExpireCache<AGMainPageModel> mainPageCache = new LazyExpireCache.Builder<AGMainPageModel>()
            .setExpireTime(Duration.ofMinutes(5).toMillis())
            .setSupplier(this::generateMainPageCache)
            .setDataValidator(Objects::nonNull)
            .create();

    private final LazyExpireCache<List<WOCaseModel<Object>>> publicCaseCache = new LazyExpireCache.Builder<List<WOCaseModel<Object>>>()
            .setExpireTime(Duration.ofMinutes(7).toMillis())
            .setSupplier(this::generatePublicCaseCache)
            .setDataValidator(list -> list != null && !list.isEmpty())
            .create();

    private AGMainPageModel generateMainPageCache() {
        AGMainPageModel model = new AGMainPageModel();

        // 结案案件数
        long monthAgo = RelativeTimeExpression.calculateByCurrentInMillis("-30d^d");
        model.setStatClosedTotal(apiService.getClosedCaseCountByBizAndState(
                WOBusiness.HZ_ASK_GOVERNOR.name(), WOCaseState.CLOSED.name()));
        model.setStatClosedRecent(apiService.getRecentlyClosedCaseCountByBizAndState(
                monthAgo, WOBusiness.HZ_ASK_GOVERNOR.name(), WOCaseState.CLOSED.name()));

        // 用户好评滚动字幕
        WOTeam viewerTeam = teamService.getContextByTeamOrThrow(
                WOBusiness.HZ_ASK_GOVERNOR.name(), WOIdentity.VIEWER.asTeam()).getTeam();
        List<WOEvent> eventList = apiService.getTopRatingEventList(
                WOBusiness.HZ_ASK_GOVERNOR.name(), 1, 10);
        List<WOEventModel> eventModelList = apiService.event2modelList(eventList, null, viewerTeam, null);
        model.setTopRatingList(eventModelList.stream().map(event -> {
            AGUserRatingModel rating = new AGUserRatingModel();
            rating.setCaseId(event.getCaseId());
            rating.setEventId(event.getId());
            rating.setUserId(event.getUserId());
            rating.setNoteId(event.getNoteId());
            if (event.getNoteModel() != null) {
                rating.setMessage(event.getNoteModel().getContent());
            } else {
                rating.setMessage("问题已经得到解决!");
            }
            UserModel user = new UserModel();
            user.setUserName(event.getTeamModel().getName());
            user.setAvatar(event.getTeamModel().getAvatar());
            if (event.getNoteModel() != null) {
                rating.setUser(event.getNoteModel().getUser());
            } else {
                UserModel anonymousUser = UserModel.asAnonymousUser(event.getUserId());
                anonymousUser.setUserId(event.getUserId());
                anonymousUser.setUserName("匿名用户");
                rating.setUser(anonymousUser);
            }
            return rating;
        }).collect(Collectors.toList()));

        // 交办单位排行: 按回复量
        List<String> topResponseTeamIds = askGovernorService.getTeamStatRankByReply(1, 10).stream()
                .map(AGResolverStat::getTeamId)
                .collect(Collectors.toList());
        Map<String, WOTeam> topResponseTeamMap = teamService.getTeamListByIdIn(topResponseTeamIds).stream()
                .filter(team -> team.getAttr() == null
                        || !team.getAbbr().contains(HZAskGovernorConsts.TEAM_ATTR_IGNORE_RANK))
                .collect(Collectors.toMap(WOTeam::getId, t -> t, (t1, t2) -> t1));
        model.setTopResponseTeamList(topResponseTeamIds.stream()
                .map(topResponseTeamMap::get)
                .filter(Objects::nonNull).limit(6)
                .map(BizHZAskGovernorController::team2rankModel)
                .collect(Collectors.toList()));

        // 交办单位排行: 按满意度
        List<String> topRatingTeamIds = askGovernorService.getTeamStatRankByRatingAverage(1, 10).stream()
                .map(AGResolverStat::getTeamId)
                .collect(Collectors.toList());
        Map<String, WOTeam> topRatingTeamMap = teamService.getTeamListByIdIn(topRatingTeamIds).stream()
                .filter(team -> team.getAttr() == null
                        || !team.getAbbr().contains(HZAskGovernorConsts.TEAM_ATTR_IGNORE_RANK))
                .collect(Collectors.toMap(WOTeam::getId, t -> t, (t1, t2) -> t1));
        model.setTopRatingTeamList(topRatingTeamIds.stream()
                .map(topRatingTeamMap::get)
                .filter(Objects::nonNull).limit(6)
                .map(BizHZAskGovernorController::team2rankModel)
                .collect(Collectors.toList()));

        return model;
    }

    private List<WOCaseModel<Object>> generatePublicCaseCache() {
        WOCaseListQueryModel query = new WOCaseListQueryModel();
        query.setTeam(WOIdentity.VIEWER.asTeam());
        query.setStates(Collections.singletonList(WOCaseState.CLOSED.name()));
        query.setBiz(WOBusiness.HZ_ASK_GOVERNOR.name());
        query.setPageSize(50);
        WOTeamContext teamContext = teamService.getContextByTeamOrThrow(
                WOBusiness.HZ_ASK_GOVERNOR.name(), WOIdentity.VIEWER.asTeam());
        List<WOCaseModel<Object>> retList = new LinkedList<>();
        for (int i = 1; ; i ++) {
            query.setPage(i);
            Page<WOCaseModel<Object>> casePage = apiService.getCasePageByQuery(query)
                    .map(woCase -> apiService.case2model(woCase, null, teamContext.getTeam()));
            if (casePage.getSize() == 0) {
                break;
            }
            List<WOCaseModel<Object>> modelList = apiService.attachContent(casePage.getContent());
            modelList.forEach(model -> {
                model.setAcl(null);
                model.setStatRating(null);
                model.setClientMobile(null);
                AGContentModel content = (AGContentModel) model.getContent();
                if (content != null) {
                    content.setPersonal(null);
                }
            });
            retList.addAll(modelList);
            if (retList.size() > PUBLIC_LIST_SIZE || casePage.isLast()) {
                break;
            }
        }

        return retList;
    }

    private static String getAcceptMessage(String resolverName) {
        return  "问题已转交给" + resolverName + ", 交办单位正在处理中, 请耐心等待回复";
    }

    private static WOTeamModel team2rankModel(WOTeam team) {
        WOTeamModel model = new WOTeamModel();
        model.setId(team.getId());
        model.setTeam(team.getTeam());
        model.setName(team.getName());
        return model;
    }

}
