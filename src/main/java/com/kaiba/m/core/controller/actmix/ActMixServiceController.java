package com.kaiba.m.core.controller.actmix;

import com.kaiba.lib.base.assembler.Mapper;
import com.kaiba.lib.base.constant.KbModule;
import com.kaiba.lib.base.domain.actmix.ActMixBannerModel;
import com.kaiba.lib.base.domain.actmix.ActMixItemModel;
import com.kaiba.lib.base.domain.actmix.ActMixTabModel;
import com.kaiba.lib.base.middleware.KbRes;
import com.kaiba.lib.base.middleware.adminlogrecorder.AdminLogRecorder;
import com.kaiba.lib.base.response.KbCode;
import com.kaiba.lib.base.response.KbEntity;
import com.kaiba.lib.base.response.KbException;
import com.kaiba.lib.base.service.IActMixService;
import com.kaiba.lib.base.service.IAdminLogService;
import com.kaiba.m.core.domain.actmix.ActMixBanner;
import com.kaiba.m.core.domain.actmix.ActMixItem;
import com.kaiba.m.core.domain.actmix.ActMixTab;
import com.kaiba.m.core.domain.actmix.ItemVisibleFlag;
import com.kaiba.m.core.service.actmix.ActMixBannerService;
import com.kaiba.m.core.service.actmix.ActMixItemService;
import com.kaiba.m.core.service.actmix.ActMixTabService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/11/08 10:02
 **/
@Slf4j
@RestController
public class ActMixServiceController implements IActMixService {

    private static final Long ORDER_MANUAL_MULTIPLE = 1000_000_000L;

    private static final String ACT_MIX_BANNER = "ACT_MIX_BANNER";
    private static final String ACT_MIX_ITEM = "ACT_MIX_ITEM";
    private static final String ACT_MIX_TAB = "ACT_MIX_TAB";

    private final KbRes kbRes;
    private final ActMixBannerService bannerService;
    private final ActMixItemService itemService;
    private final ActMixTabService tabService;
    private final AdminLogRecorder adminLogRecorder;

    public ActMixServiceController(
        KbRes kbRes,
        ActMixBannerService bannerService,
        ActMixItemService itemService,
        ActMixTabService tabService,
        IAdminLogService adminLogService
    ) {
        this.kbRes = kbRes;
        this.bannerService = bannerService;
        this.itemService = itemService;
        this.tabService = tabService;
        this.adminLogRecorder = AdminLogRecorder.builder(adminLogService)
            .module(KbModule.ACT_MIX)
            .registerUnit(ACT_MIX_BANNER, "横幅")
            .registerUnit(ACT_MIX_ITEM, "活动")
            .registerUnit(ACT_MIX_TAB, "栏目")
            .create();
    }

    @Override
    public KbEntity<ActMixBannerModel> addBannerByBody(Integer userId, ActMixBannerModel banner) {
        ActMixBanner actMixBanner = model2Banner(banner);
        ActMixBanner saved = bannerService.addBannerByBody(actMixBanner);
        adminLogRecorder.on(ACT_MIX_BANNER).by(userId)
            .act("add_banner", "新增横幅: " + saved.getTitle()).ref1(saved.getId()).add();
        return kbRes.ok(banner2Model(saved));
    }

    @Override
    public KbEntity<List<ActMixBannerModel>> getBannerList(
        Integer siteId, Integer origin, Integer state, Integer page, Integer pageSize
    ) {
        return kbRes.ok(bannerService.getBannerList(siteId, state, origin, page, pageSize)
            .map(ActMixServiceController::banner2Model));
    }

    @Override
    public KbEntity<ActMixBannerModel> updateBannerById(Integer userId, ActMixBannerModel banner) {
        ActMixBanner actMixBanner = model2Banner(banner);
        ActMixBanner snapshot = bannerService.findById(banner.getId())
            .orElseThrow(() -> new KbException(KbCode.RESOURCE_NOT_FOUND).li());
        ActMixBanner updated = bannerService.updateBannerOrderById(actMixBanner);
        adminLogRecorder.on(ACT_MIX_BANNER).by(userId)
            .act("update_banner", "更新横幅信息: " + snapshot.getTitle())
            .ref1(banner.getId()).snapshot(snapshot).add();
        return kbRes.ok(banner2Model(updated));
    }

    @Override
    public KbEntity<ActMixBannerModel> updateBannerStateAsArchived(Integer userId, String bannerId) {
        ActMixBanner archived = bannerService.updateBannerStateAsArchived(bannerId);
        adminLogRecorder.on(ACT_MIX_BANNER).by(userId)
            .act("update_banner_archived", "归档横幅: " + archived.getTitle()).ref1(archived.getId()).add();
        return kbRes.ok(banner2Model(archived));
    }

    @Override
    public KbEntity<ActMixBannerModel> updateBannerStateAsShow(Integer userId, String bannerId) {
        ActMixBanner show = bannerService.updateBannerStateAsShow(bannerId);
        adminLogRecorder.on(ACT_MIX_BANNER).by(userId)
            .act("update_banner_show", "对外展示横幅: " + show.getTitle()).ref1(show.getId()).add();
        return kbRes.ok(banner2Model(show));
    }

    // item ------------------------------------------

    @Override
    public KbEntity<ActMixItemModel> addItemByBody(Integer userId, ActMixItemModel item) {
        ActMixItem actMixItem = model2Item(item);
        ActMixItem saved = itemService.addItemByBody(actMixItem);
        adminLogRecorder.on(ACT_MIX_ITEM).by(userId)
            .act("add_item", "新建活动: " + saved.getTitle()).ref1(saved.getId()).add();
        return kbRes.ok(item2Model(saved));
    }

    @Override
    public KbEntity<ActMixItemModel> getItemById(String itemId) {
        return itemService.getItemById(itemId)
                .map(ActMixServiceController::item2Model)
                .map(kbRes::ok)
                .orElseGet(() -> kbRes.err(KbCode.RESOURCE_NOT_FOUND));
    }

    @Override
    public KbEntity<List<ActMixItemModel>> getItemList(
        Integer siteId, String tabId, Integer type, Integer[] state, Integer page, Integer pageSize
    ) {
        return kbRes.ok(itemService.getItemList(siteId, tabId, type, state, page, pageSize)
            .map(item -> item2Model(tabId, item)));
    }

    @Override
    public KbEntity<Void> incrViewCount(String itemId) {
        itemService.incrViewCount(itemId);
        return kbRes.ok();
    }

    @Override
    public KbEntity<ActMixItemModel> updateItemStateAsPreview(Integer userId, String itemId) {
        ActMixItem preview = itemService.updateItemStateAsPreview(itemId);
        adminLogRecorder.on(ACT_MIX_ITEM).by(userId)
            .act("update_item_preview", "活动状态改为即将开始: " + preview.getTitle()).ref1(preview.getId()).add();
        return kbRes.ok(item2Model(preview));
    }

    @Override
    public KbEntity<ActMixItemModel> updateItemStateAsOnGoing(Integer userId, String itemId) {
        ActMixItem onGoing = itemService.updateItemStateAsOnGoing(itemId);
        adminLogRecorder.on(ACT_MIX_ITEM).by(userId)
            .act("update_item_ongoing", "活动状态改为开始: " + onGoing.getTitle()).ref1(onGoing.getId()).add();
        return kbRes.ok(item2Model(onGoing));
    }

    @Override
    public KbEntity<ActMixItemModel> updateItemStateAsEnded(Integer userId, String itemId) {
        ActMixItem ended = itemService.updateItemStateAsEnded(itemId);
        adminLogRecorder.on(ACT_MIX_ITEM).by(userId)
            .act("update_item_ended", "活动状态改为结束: " + ended.getTitle()).ref1(ended.getId()).add();
        return kbRes.ok(item2Model(ended));
    }

    @Override
    public KbEntity<ActMixItemModel> updateItemStateAsArchived(Integer userId, String itemId) {
        ActMixItem archived = itemService.updateItemStateAsArchived(itemId);
        adminLogRecorder.on(ACT_MIX_ITEM).by(userId)
            .act("update_item_archived", "归档活动: " + archived.getTitle()).ref1(archived.getId()).add();
        return kbRes.ok(item2Model(archived));
    }

    @Override
    public KbEntity<ActMixItemModel> updateItemById(Integer userId, ActMixItemModel item) {
        ActMixItem actMixItem = model2Item(item);
        ActMixItem snapshot = itemService.getItemById(item.getId())
            .orElseThrow(() -> new KbException(KbCode.RESOURCE_NOT_FOUND).li());
        ActMixItem updated = itemService.updateItemById(actMixItem);
        adminLogRecorder.on(ACT_MIX_ITEM).by(userId)
            .act("update_item", "更新活动: " + item.getTitle())
            .ref1(updated.getId()).snapshot(snapshot).add();
        return kbRes.ok(item2Model(updated));
    }

    @Override
    public KbEntity<ActMixItemModel> unsetItemToTopById(Integer userId, String tabId, String itemId) {
        ActMixItem unsetTop = itemService.resetItemOrderById(tabId, itemId);
        adminLogRecorder.on(ACT_MIX_ITEM).by(userId)
            .act("unset_top", "在栏目中取消置顶: " + unsetTop.getTitle())
            .ref1(unsetTop.getId()).ref2(tabId).add();
        return kbRes.ok(item2Model(tabId, unsetTop));
    }

    @Override
    public KbEntity<ActMixItemModel> setItemToTopById(Integer userId, String tabId, String itemId, Long idx) {
        long manualOrder = idx == null ? 0L : ORDER_MANUAL_MULTIPLE * idx;
        String desc = idx == null ? "在栏目中置顶活动: %s" : "栏目置顶区中手动排序：%s -> " + idx;
        ActMixItem setTop = itemService.updateItemOrderById(tabId, itemId, manualOrder);
        adminLogRecorder.on(ACT_MIX_ITEM).by(userId)
            .act("set_top", String.format(desc, setTop.getTitle()))
            .ref1(setTop.getId()).ref2(tabId).add();
        return kbRes.ok(item2Model(tabId, setTop));
    }

    // tab --------------------------------------------

    @Override
    public KbEntity<ActMixTabModel> addTab(Integer userId, ActMixTabModel model) {
        ActMixTab saved = tabService.addTab(model);
        adminLogRecorder.on(ACT_MIX_TAB).by(userId)
            .act("add_tab", "新增栏目: " + saved.getName()).ref1(saved.getId()).add();
        return kbRes.ok(tab2Model(saved));
    }

    @Override
    public KbEntity<ActMixTabModel> updateTabById(Integer userId, ActMixTabModel model) {
        ActMixTab updated = tabService.updateTabNameById(model);
        adminLogRecorder.on(ACT_MIX_TAB).by(userId)
            .act("update_tab", "更新栏目: " + model.getName()).ref1(updated.getId()).add();
        return kbRes.ok(tab2Model(updated));
    }

    @Override
    public KbEntity<ActMixTabModel> updateTabAsShow(Integer userId, String tabId) {
        ActMixTab show = tabService.updateTabAsShow(tabId);
        adminLogRecorder.on(ACT_MIX_TAB).by(userId)
            .act("update_tab_show", "更新栏目对用户可见: " + show.getName()).ref1(show.getId()).add();
        return kbRes.ok(tab2Model(show));
    }

    @Override
    public KbEntity<ActMixTabModel> updateTabAsHide(Integer userId, String tabId) {
        ActMixTab hide = tabService.updateTabAsHide(tabId);
        adminLogRecorder.on(ACT_MIX_TAB).by(userId)
            .act("update_tab_hide", "更新栏目对用户不可见: " + hide.getName()).ref1(hide.getId()).add();
        return kbRes.ok(tab2Model(hide));
    }

    @Override
    public KbEntity<List<ActMixTabModel>> getTabList(Integer siteId, Integer state) {
        return kbRes.ok(tabService.getTabListByState(siteId, state)
            .stream()
            .map(ActMixServiceController::tab2Model)
            .collect(Collectors.toList()));
    }

    @Override
    public KbEntity<ActMixTabModel> getTabById(String tabId) {
        return tabService.getTabById(tabId)
                .map(ActMixServiceController::tab2Model)
                .map(kbRes::ok)
                .orElseGet(() -> kbRes.err(KbCode.RESOURCE_NOT_FOUND));
    }

    // ------------------------------------------------

    private static ActMixBannerModel banner2Model(ActMixBanner banner) {
        return Mapper.map(banner, ActMixBannerModel.class);
    }

    private static ActMixItemModel item2Model(ActMixItem item) {
        return processItemFlag(Mapper.map(item, ActMixItemModel.class));
    }

    private static ActMixItemModel item2Model(String tabId, ActMixItem item) {
        ActMixItemModel itemModel = Mapper.map(item, ActMixItemModel.class);
        if (item.getTabOrders() != null) {
            long order = item.getTabOrders().getOrDefault(tabId, 0L);
            itemModel.setIdx(order / ORDER_MANUAL_MULTIPLE);
            itemModel.setOrder(order % ORDER_MANUAL_MULTIPLE > 0 ? 1 : 0);
        }
        return processItemFlag(itemModel);
    }

    private static ActMixTabModel tab2Model(ActMixTab tab) {
        return Mapper.map(tab, ActMixTabModel.class);
    }

    private static ActMixBanner model2Banner(ActMixBannerModel model) {
        return Mapper.map(model, ActMixBanner.class);
    }

    private static ActMixItem model2Item(ActMixItemModel model) {
        return Mapper.map(generateItemFlag(model), ActMixItem.class);
    }

    private static ActMixItemModel generateItemFlag(ActMixItemModel model) {
        model.setVisibilityFlag1(ItemVisibleFlag.Builder.on()
            .setShowWantJoin(model.getShowWantJoin())
            .setShowMarkHot(model.getShowMarkHot())
            .setShowMarkStart(model.getShowMarkStart())
            .setShowMarkEnd(model.getShowMarkEnd())
            .setShowMarkVideo(model.getShowMarkVideo())
            .create());
        return model;
    }

    private static ActMixItemModel processItemFlag(ActMixItemModel model) {
        ItemVisibleFlag.Builder builder = ItemVisibleFlag.parse(model.getVisibilityFlag1());
        model.setShowMarkHot(builder.getShowMarkHot());
        model.setShowWantJoin(builder.getShowWantJoin());
        model.setShowMarkStart(builder.getShowMarkStart());
        model.setShowMarkEnd(builder.getShowMarkEnd());
        model.setShowMarkVideo(builder.getShowMarkVideo());
        return model;
    }
}
