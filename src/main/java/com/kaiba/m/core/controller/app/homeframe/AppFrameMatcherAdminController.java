package com.kaiba.m.core.controller.app.homeframe;

import com.kaiba.lib.base.assembler.Mapper;
import com.kaiba.lib.base.constant.KbHeader;
import com.kaiba.lib.base.constant.applego.AppComponentState;
import com.kaiba.lib.base.middleware.KbRes;
import com.kaiba.lib.base.response.KbCode;
import com.kaiba.lib.base.response.KbEntity;
import com.kaiba.lib.base.response.KbException;
import com.kaiba.lib.base.util.AppVersionUtils;
import com.kaiba.m.core.domain.apphome.AppFrameMatcher;
import com.kaiba.m.core.model.app.AppFrameMatcherCreateModel;
import com.kaiba.m.core.model.app.AppFrameMatcherModel;
import com.kaiba.m.core.service.appcomponent.home.AppFrameMatcherService;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * author: lyux
 * date: 2024-09-30
 **/
@Slf4j
@RestController
@RequestMapping("/admin/AppFrameMatcher")
public class AppFrameMatcherAdminController {

    private final KbRes kbRes;
    private final AppFrameMatcherService matcherService;

    public AppFrameMatcherAdminController(
            KbRes kbRes,
            AppFrameMatcherService matcherService
    ) {
        this.kbRes = kbRes;
        this.matcherService = matcherService;
    }

    @PostMapping("/usr/createMatcher")
    public KbEntity<AppFrameMatcherModel> createMatcher(
            @RequestHeader(KbHeader.KB_SITE_ID) Integer siteId,
            @RequestBody() AppFrameMatcherCreateModel model
    ) {
        AppFrameMatcher matcher = Mapper.map(model, AppFrameMatcher.class);
        matcher.setSiteId(siteId);
        if (matcher.getVcMin() == null && model.getVnMin() != null) {
            matcher.setVcMin(AppVersionUtils.getVersionCodeByName(model.getVnMin()));
        }
        if (matcher.getVcMin() == null) {
            matcher.setVcMin(0);
        }
        if (matcher.getVcMax() == null && model.getVnMax() != null) {
            matcher.setVcMax(AppVersionUtils.getVersionCodeByName(model.getVnMax()));
        }
        if (matcher.getVcMax() == null) {
            matcher.setVcMax(999999);
        }
        if (matcher.getState() == null) {
            matcher.setState(AppComponentState.BETA.getValue());
        }
        AppFrameMatcher created = matcherService.createMatcher(matcher);
        return kbRes.ok(matcher2model(created));
    }

    @PostMapping("/usr/deleteMatcherById")
    public KbEntity<Void> deleteMatcherById(
            @RequestParam() String matcherId
    ) {
        matcherService.deleteMatcher(matcherId);
        return kbRes.ok();
    }

    @PostMapping("/usr/updateState")
    public KbEntity<Void> updateState(
            @RequestParam() String matcherId,
            @RequestParam() Integer state
    ) {
        AppComponentState.resolveByValue(state).orElseThrow(() -> new KbException(KbCode.REQUEST_PARAM_INVALID).li());
        matcherService.updateState(matcherId, state);
        return kbRes.ok();
    }

    @PostMapping("/usr/updateRemark")
    public KbEntity<Void> updateRemark(
            @RequestParam() String matcherId,
            @RequestParam() String remark
    ) {
        matcherService.updateRemark(matcherId, remark);
        return kbRes.ok();
    }

    @PostMapping("/usr/updateVersionRangeByCode")
    public KbEntity<Void> updateVersionRangeByCode(
            @RequestParam() String matcherId,
            @RequestParam(required = false, defaultValue = "0") Integer vcMin,
            @RequestParam(required = false, defaultValue = "999999") Integer vcMax
    ) {
        matcherService.updateVersionRange(matcherId, vcMin, vcMax);
        return kbRes.ok();
    }

    @PostMapping("/usr/updateVersionRangeByName")
    public KbEntity<Void> updateVersionRangeByName(
            @RequestParam() String matcherId,
            @RequestParam(required = false, defaultValue = "1.0.0") String vnMin,
            @RequestParam(required = false, defaultValue = "99.99.99") String vnMax
    ) {
        int vcMin = AppVersionUtils.getVersionCodeByName(vnMin);
        int vcMax = AppVersionUtils.getVersionCodeByName(vnMax);
        return updateVersionRangeByCode(matcherId, vcMin, vcMax);
    }

    @PostMapping("/usr/updateUsers")
    public KbEntity<Void> updateUsers(
            @RequestParam() String matcherId,
            @RequestParam() Integer[] userIds
    ) {
        if (userIds == null || userIds.length == 0) {
            return kbRes.err(KbCode.REQUEST_PARAM_MISSING, "missing userIds");
        }
        Set<Integer> userIdSet = new HashSet<>(userIds.length);
        userIdSet.addAll(Arrays.asList(userIds));
        matcherService.updateUsers(matcherId, userIdSet);
        return kbRes.ok();
    }

    @PostMapping("/usr/addUser")
    public KbEntity<Void> addUser(
            @RequestParam() String matcherId,
            @RequestParam() Integer targetUserId
    ) {
        matcherService.addUser(matcherId, targetUserId);
        return kbRes.ok();
    }

    @PostMapping("/usr/removeUser")
    public KbEntity<Void> removeUser(
            @RequestParam() String matcherId,
            @RequestParam() Integer targetUserId
    ) {
        matcherService.removeUser(matcherId, targetUserId);
        return kbRes.ok();
    }

    @PostMapping("/usr/updateRosterKey")
    public KbEntity<Void> updateRosterKey(
            @RequestParam() String matcherId,
            @RequestParam(required = false) @ApiParam("为空会清空花名册实例KEY") String rosterKey
    ) {
        matcherService.updateRosterKey(matcherId, rosterKey);
        return kbRes.ok();
    }

    @PostMapping("/usr/updateMainPageId")
    public KbEntity<Void> updateMainPageId(
            @RequestParam() String matcherId,
            @RequestParam() String mainPageId
    ) {
        matcherService.updateMainPageId(matcherId, mainPageId);
        return kbRes.ok();
    }

    @PostMapping("/usr/updateHomeFrameId")
    public KbEntity<Void> updateHomeFrameId(
            @RequestParam() String matcherId,
            @RequestParam() String homeFrameId
    ) {
        matcherService.updateHomeFrameId(matcherId, homeFrameId);
        return kbRes.ok();
    }

    @PostMapping("/obj/getMatcherById")
    public KbEntity<AppFrameMatcherModel> getMatcherById(
            @RequestParam() String matcherId
    ) {
        return matcherService.getMatcherById(matcherId)
                .map(AppFrameMatcherAdminController::matcher2model)
                .map(kbRes::ok)
                .orElseGet(() -> kbRes.err(KbCode.RESOURCE_NOT_FOUND));
    }

    @PostMapping("/obj/getMatcherPageBySiteId")
    public KbEntity<List<AppFrameMatcherModel>> getMatcherPageBySiteId(
            @RequestHeader(KbHeader.KB_SITE_ID) Integer siteId,
            @RequestParam(required = false) Integer state,
            @RequestParam(required = false, defaultValue = "1") Integer page,
            @RequestParam(required = false, defaultValue = "20") Integer pageSize
    ) {
        return kbRes.ok(matcherService.getMatcherPage(siteId, state, page, pageSize)
                .map(AppFrameMatcherAdminController::matcher2model));
    }

    private static AppFrameMatcherModel matcher2model(AppFrameMatcher matcher) {
        AppFrameMatcherModel model = Mapper.map(matcher, AppFrameMatcherModel.class);
        if (matcher.getVcMin() != null) {
            if (matcher.getVcMin() > 10000) {
                model.setVnMin(AppVersionUtils.getVersionNameByCode(matcher.getVcMin()));
            } else {
                model.setVnMin("1.0.0");
            }
        }
        if (matcher.getVcMax() != null) {
            if (matcher.getVcMax() > 10000) {
                model.setVnMax(AppVersionUtils.getVersionNameByCode(matcher.getVcMax()));
            } else {
                model.setVnMax("1.0.0");
            }
        }
        return model;
    }

}
