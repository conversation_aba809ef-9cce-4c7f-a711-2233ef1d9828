package com.kaiba.m.core.controller.app;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.kaiba.lib.base.assembler.Assembler;
import com.kaiba.lib.base.assembler.Mapper;
import com.kaiba.lib.base.constant.KbProperties;
import com.kaiba.lib.base.constant.modularization.AppModulePageStatus;
import com.kaiba.lib.base.constant.modularization.AppModulePageType;
import com.kaiba.lib.base.domain.appmodule.AppModulePageInfoModel;
import com.kaiba.lib.base.domain.appmodule.MainPageLayoutModel;
import com.kaiba.lib.base.domain.appmodule.MainPageTabModel;
import com.kaiba.lib.base.middleware.KbRes;
import com.kaiba.lib.base.response.KbCode;
import com.kaiba.lib.base.response.KbEntity;
import com.kaiba.lib.base.response.KbException;
import com.kaiba.lib.base.service.IModularizationService;
import com.kaiba.lib.base.util.GsonUtils;
import com.kaiba.lib.base.util.StringUtils;
import com.kaiba.lib.base.util.appaction.AppActionModel;
import com.kaiba.lib.base.util.appaction.AppActionType;
import com.kaiba.lib.base.util.appmodule.Layout;
import com.kaiba.lib.base.util.appmodule.constant.*;
import com.kaiba.lib.base.util.appmodule.item.*;
import com.kaiba.lib.base.util.appmodule.nagivationbar.AbsNavigationBar;
import com.kaiba.lib.base.util.appmodule.nagivationbar.NavigationBarSimple;
import com.kaiba.lib.base.util.appmodule.util.Color;
import com.kaiba.m.core.domain.modularization.AppModulePage;
import com.kaiba.m.core.domain.modularization.MainPageTabExtra;
import com.kaiba.m.core.service.modularization.ModularizationService;
import com.kaiba.m.core.service.modularization.SpringFestival;
import com.kaiba.m.core.util.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.MutablePropertyValues;
import org.springframework.beans.PropertyAccessorFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.RestController;

import java.util.*;
import java.util.stream.Collectors;

import static com.kaiba.lib.base.constant.modularization.AppModulePageType.*;

/**
 * <AUTHOR>
 * 2019/4/3
 */
@Slf4j
@RestController
public class ModularizationServiceController implements IModularizationService {

    private static final Gson gson = GsonUtils.getGson();

    private final KbRes kbRes;
    private final ModularizationService modularizationService;

    @Autowired
    public ModularizationServiceController(
            KbRes kbRes,
            ModularizationService modularizationService
    ) {
        this.kbRes = kbRes;
        this.modularizationService = modularizationService;
    }

    @Override
    public KbEntity<Object> getMainPageLayout(Integer siteId, Integer source, String pageVersion) {
        Optional<AppModulePage> opTab1 = modularizationService.getLastOnlinePage(siteId, MAIN_PAGE_TAB1, pageVersion);
        if (!opTab1.isPresent()) {
            return kbRes.err(KbCode.APP_MODULE_PAGE_NOT_FOUND, "tab1 missing");
        }
        Optional<AppModulePage> opTab2 = modularizationService.getLastOnlinePage(siteId, MAIN_PAGE_TAB2, pageVersion);
        if (!opTab2.isPresent()) {
            return kbRes.err(KbCode.APP_MODULE_PAGE_NOT_FOUND, "tab2 missing");
        }
        Optional<AppModulePage> opTab3 = modularizationService.getLastOnlinePage(siteId, MAIN_PAGE_TAB3, pageVersion);
        if (!opTab3.isPresent()) {
            return kbRes.err(KbCode.APP_MODULE_PAGE_NOT_FOUND, "tab3 missing");
        }
        Optional<AppModulePage> opTab4 = modularizationService.getLastOnlinePage(siteId, MAIN_PAGE_TAB4, pageVersion);
        if (!opTab4.isPresent()) {
            return kbRes.err(KbCode.APP_MODULE_PAGE_NOT_FOUND, "tab4 missing");
        }

        MainPageLayoutModel mainPageLayoutModel = MainPageLayoutModel.on()
                .setShowPostToProgramButton(true)
                .setPostButtonTextColor(Color.toColorString(Color.BLACK))
                .setBottomBarBackgroundColor(Color.toColorString(Color.KB_BOTTOM_BAR_BACKGROUND))
                .setTab1(page2MainPageTabModel(opTab1.get()))
                .setTab2(page2MainPageTabModel(opTab2.get()))
                .setTab3(page2MainPageTabModel(opTab3.get()))
                .setTab4(page2MainPageTabModel(opTab4.get()))
                .create();
        if(pageVersion.compareTo("6.54.0") >= 0){
            return kbRes.ok(SpringFestival.getSpringMainPageLayoutModel(siteId, mainPageLayoutModel));
        }else {
            return kbRes.ok(mainPageLayoutModel);
        }
    }

    @Override
    public KbEntity<Object> getMainPageLayoutForBeta(Integer siteId, Integer source, String pageVersion) {
        Optional<AppModulePage> opTab1 = modularizationService.getLastActivePage(siteId, MAIN_PAGE_TAB1, pageVersion);
        if (!opTab1.isPresent()) {
            return kbRes.err(KbCode.APP_MODULE_PAGE_NOT_FOUND, "tab1 missing");
        }
        Optional<AppModulePage> opTab2 = modularizationService.getLastActivePage(siteId, MAIN_PAGE_TAB2, pageVersion);
        if (!opTab2.isPresent()) {
            return kbRes.err(KbCode.APP_MODULE_PAGE_NOT_FOUND, "tab2 missing");
        }
        Optional<AppModulePage> opTab3 = modularizationService.getLastActivePage(siteId, MAIN_PAGE_TAB3, pageVersion);
        if (!opTab3.isPresent()) {
            return kbRes.err(KbCode.APP_MODULE_PAGE_NOT_FOUND, "tab3 missing");
        }
        Optional<AppModulePage> opTab4 = modularizationService.getLastActivePage(siteId, MAIN_PAGE_TAB4, pageVersion);
        if (!opTab4.isPresent()) {
            return kbRes.err(KbCode.APP_MODULE_PAGE_NOT_FOUND, "tab4 missing");
        }
        MainPageLayoutModel mainPageLayoutModel = MainPageLayoutModel.on()
                .setShowPostToProgramButton(true)
                .setPostButtonTextColor(Color.toColorString(Color.BLACK))
                .setBottomBarBackgroundColor(Color.toColorString(Color.KB_BOTTOM_BAR_BACKGROUND))
                .setTab1(page2MainPageTabModel(opTab1.get()))
                .setTab2(page2MainPageTabModel(opTab2.get()))
                .setTab3(page2MainPageTabModel(opTab3.get()))
                .setTab4(page2MainPageTabModel(opTab4.get()))
                .create();
        return kbRes.ok(mainPageLayoutModel);
    }

    @Override
    public KbEntity<MainPageTabModel> addMainPageTab(Integer siteId, Integer pageType, String jsonData, Integer userId, String pageVersion) {
        if (!StringUtils.isValidKbAppVersion(pageVersion)) {
            return kbRes.err(KbCode.APP_MODULE_VERSION, "pageVersion error:" + pageVersion);
        }
        AppModulePageType type = valueOf(pageType)
                .orElseThrow(KbException.supplier(KbCode.REQUEST_PARAM_INVALID));
        Map<String, Object> dataMap = JsonUtils.toModel(jsonData, new TypeToken<Map<String, Object>>() {
        }.getType());
        log.info("add main page tab, dataMap: " + dataMap);
        return kbRes.ok(page2MainPageTabModel(modularizationService.addPage(
                siteId, type,
                map2Layout((Map<String, Object>) dataMap.get("layout")),
                map2MainPageTabExtra(dataMap), userId, pageVersion)));
    }

    @Override
    public KbEntity<MainPageTabModel> updateMainPageTabLayout(String id, String jsonData, Integer userId, String pageVersion) {
        if (!StringUtils.isValidKbAppVersion(pageVersion)) {
            return kbRes.err(KbCode.APP_MODULE_VERSION, "pageVersion error:" + pageVersion);
        }
        Map<String, Object> dataMap = JsonUtils.toModel(jsonData, new TypeToken<Map<String, Object>>() {
        }.getType());
        AppModulePage modulePage = modularizationService.updatePageLayout(
                id,
                map2Layout((Map<String, Object>) dataMap.get("layout")),
                map2MainPageTabExtra(dataMap),
                userId,
                pageVersion);
        log.info("update main page tab, modulePage: " + modulePage);
        return kbRes.ok(page2MainPageTabModel(modulePage));
    }

    @Override
    public KbEntity<MainPageTabModel> updateMainPageTabStatus(String id, Integer status, Integer userId) {
        AppModulePageStatus targetStatus = AppModulePageStatus
                .valueOf(status)
                .orElseThrow(KbException.supplier(KbCode.REQUEST_PARAM_INVALID));
        AppModulePage modulePage = modularizationService.updatePageStatus(id, targetStatus, userId);
        return kbRes.ok(page2MainPageTabModel(modulePage));
    }

    @Override
    public KbEntity<MainPageTabModel> getMainPageTabById(String id) {
        return modularizationService.getPageById(id)
                .map(ModularizationServiceController::page2MainPageTabModel)
                .map(kbRes::ok)
                .orElse(kbRes.err(KbCode.APP_MODULE_PAGE_NOT_FOUND));
    }

    @Override
    public KbEntity<List<AppModulePageInfoModel>> getMainPageTabInfoList(
            Integer siteId, Integer pageType, Integer page, Integer pageSize) {
        AppModulePageType type = valueOf(pageType)
                .orElseThrow(KbException.supplier(KbCode.REQUEST_PARAM_INVALID));
        // TODO: AppModulePageInfoModel do not need layout data
        Page<AppModulePage> appModulePages = modularizationService.getHistoryPageList(siteId, type, page, pageSize);
        KbEntity<List<AppModulePageInfoModel>> result = kbRes.ok(appModulePages.stream()
                .map(modulePage -> Mapper.map(modulePage, AppModulePageInfoModel.class))
                .collect(Collectors.toList()));
        result.setTotalPage((long) appModulePages.getTotalPages());
        return result;
    }

    @Override
    public KbEntity<List<AppModulePageInfoModel>> getOnlineMainPageTabInfoList(
            Integer siteId, Integer pageType, Integer page, Integer pageSize) {
        AppModulePageType type = valueOf(pageType)
                .orElseThrow(KbException.supplier(KbCode.REQUEST_PARAM_INVALID));
        // TODO: AppModulePageInfoModel do not need layout data
        Page<AppModulePage> appModulePages = modularizationService.getOnlineHistoryPageList(siteId, type, page, pageSize);
        KbEntity<List<AppModulePageInfoModel>> result = kbRes.ok(appModulePages.stream()
                .map(ModularizationServiceController::page2Model)
                .collect(Collectors.toList()));
        result.setTotalPage((long) appModulePages.getTotalPages());
        return result;
    }

    @Override
    public KbEntity<List<AppModulePageInfoModel>> getBetaMainPageTabInfoList(
            Integer siteId, Integer pageType, Integer page, Integer pageSize) {
        AppModulePageType type = valueOf(pageType)
                .orElseThrow(KbException.supplier(KbCode.REQUEST_PARAM_INVALID));
        // TODO: AppModulePageInfoModel do not need layout data
        Page<AppModulePage> appModulePages = modularizationService.getBetaHistoryPageList(siteId, type, page, pageSize);
        KbEntity<List<AppModulePageInfoModel>> result = kbRes.ok(appModulePages.stream()
                .map(ModularizationServiceController::page2Model)
                .collect(Collectors.toList()));
        result.setTotalPage((long) appModulePages.getTotalPages());
        return result;
    }

    @Override
    public KbEntity<List<Map<String, Object>>> getAppActionTypeList() {
        return kbRes.ok(Arrays.stream(AppActionType.values()).map(actionType -> {
            Map<String, Object> data = new HashMap<>();
            data.put("code", actionType.getCode());
            data.put("action", actionType.getAction());
            data.put("description", actionType.getDescription());
            data.put("actionParams", actionType.getValidators());
            return data;
        }).collect(Collectors.toList()));
    }

    @Override
    public KbEntity<MainPageTabModel> getOnlineMainPageTabByType(Integer siteId, Integer pageType, String pageVersion) {
        AppModulePageType type = valueOf(pageType)
                .orElseThrow(KbException.supplier(KbCode.REQUEST_PARAM_INVALID));
        return modularizationService.getLastOnlinePage(siteId, type, pageVersion)
                .map(ModularizationServiceController::page2MainPageTabModel)
                .map(kbRes::ok)
                .orElse(kbRes.err(KbCode.APP_MODULE_PAGE_NOT_FOUND));
    }

    @Override
    public KbEntity<Map<String, Object>> getActiveMainPageTabByType(Integer siteId, Integer pageType, String pageVersion) {
        AppModulePageType type = valueOf(pageType)
                .orElseThrow(KbException.supplier(KbCode.REQUEST_PARAM_INVALID));

        Optional<AppModulePage> pageOp = modularizationService.getLastActivePage(siteId, type, pageVersion);
        AppModulePage page = null;
        if (pageOp.isPresent()) {
            page = pageOp.get();
        } else {
            switch (type) {
                case MAIN_PAGE_TAB1:
                    page = defaultForTab1(siteId);
                    break;
                case MAIN_PAGE_TAB2:
                    page = defaultForTab2(siteId);
                    break;
                case MAIN_PAGE_TAB3:
                    page = defaultForTab3(siteId);
                    break;
                case MAIN_PAGE_TAB4:
                    page = defaultForTab4(siteId);
                    break;
            }
            page = modularizationService.addPage(page);
        }
        return kbRes.ok(Assembler.on()
                .put("mainPageTab", page2MainPageTabModel(page))
                .put("mainPageStatus", page.getStatus())
                .put("pageVersion", page.getPageVersion())
                .result());
    }

    //------------------------------------------------------------------------

    private static AppModulePageInfoModel page2Model(AppModulePage page) {
        AppModulePageInfoModel model = Mapper.map(page, AppModulePageInfoModel.class);
        model.setDesc(page2MainPageTabModel(page).getDesc());
        return model;
    }

    private static AbsNavigationBar getNavigationBarFromMap(Map<String, Object> dataMap) {
        String clz = (String) dataMap.get("class");
        if (null == clz) {
            clz = (String) dataMap.get("clz");
        }
        Class<? extends AbsNavigationBar> navigationBarClass = NavigationBarType
                .ofClass(clz)
                .orElseThrow(KbException.supplier(KbCode.APP_MODULE_ILLEGAL_FORMAT));
        String json = gson.toJson(dataMap);
        return gson.fromJson(json, navigationBarClass);
    }

    private static AbsItem getItemFromMap(Map<String, Object> dataMap) {
        String clz = (String) dataMap.get("class");
        if (null == clz) {
            clz = (String) dataMap.get("clz");
        }
        Class<? extends AbsItem> absItemClass = ItemType
                .ofClass(clz)
                .orElseThrow(KbException.supplier(KbCode.APP_MODULE_ILLEGAL_FORMAT));
        return gson.fromJson(gson.toJson(dataMap), absItemClass);
    }

    private static MainPageTabModel page2MainPageTabModel(AppModulePage page) {
        MainPageTabModel.Builder pageBuilder = MainPageTabModel.on();
        if (page.getLayout() instanceof Layout) {
            pageBuilder.setLayout((Layout) page.getLayout());
        } else {
            Map<String, Object> layoutDataMap = (Map<String, Object>) page.getLayout();
            pageBuilder.setLayout(map2Layout(layoutDataMap));
        }
        if (page.getExtra() instanceof MainPageTabExtra) {
            MainPageTabExtra extra = (MainPageTabExtra) page.getExtra();
            pageBuilder.setTextColorSelected(extra.getTextColorSelected())
                    .setTextColorUnselected(extra.getTextColorUnselected())
                    .setText(extra.getText())
                    .setDesc(extra.getDesc())
                    .setIconSelected(extra.getIconSelected())
                    .setIconUnselected(extra.getIconUnselected());
        } else {
            MainPageTabExtra extra = new MainPageTabExtra();
            try {
                MutablePropertyValues propertyValues = new MutablePropertyValues((Map<String, Object>) page.getExtra());
                PropertyAccessorFactory
                        .forBeanPropertyAccess(extra)
                        .setPropertyValues(propertyValues, true, true);
            } catch (Exception e) {
                throw new KbException(KbCode.APP_MODULE_ILLEGAL_FORMAT, "extra data type wrong", e);
            }
            pageBuilder.setTextColorSelected(extra.getTextColorSelected())
                    .setTextColorUnselected(extra.getTextColorUnselected())
                    .setText(extra.getText())
                    .setDesc(extra.getDesc())
                    .setIconSelected(extra.getIconSelected())
                    .setIconUnselected(extra.getIconUnselected());
        }
        pageBuilder.setId(page.getId());
        return pageBuilder.create();
    }

    private static Layout map2Layout(Map<String, Object> layoutDataMap) {
        Layout layout = new Layout();
        try {
            if (null != layoutDataMap.get("navigationBar")) {
                layout.setNavigationBar(getNavigationBarFromMap((Map<String, Object>) layoutDataMap.get("navigationBar")));
                layoutDataMap.remove("navigationBar");
            }
            if (null != layoutDataMap.get("items")) {
                List<AbsItem> itemList = new LinkedList<>();
                List<Map<String, Object>> itemDataList = (List<Map<String, Object>>) layoutDataMap.get("items");
                for (Map<String, Object> itemData : itemDataList) {
                    itemList.add(getItemFromMap(itemData));
                }
                layout.setItems(itemList);
                layoutDataMap.remove("items");
            }
            MutablePropertyValues layoutProperties =
                    new MutablePropertyValues(layoutDataMap);
            PropertyAccessorFactory
                    .forBeanPropertyAccess(layout)
                    .setPropertyValues(layoutProperties, true, true);
        } catch (Exception e) {
            throw new KbException(KbCode.APP_MODULE_ILLEGAL_FORMAT, "layout data type wrong", e);
        }
        return layout;
    }

    private static MainPageTabExtra map2MainPageTabExtra(Map<String, Object> dataMap) {
        dataMap.remove("layout");
        MainPageTabExtra extra = new MainPageTabExtra();
        try {
            MutablePropertyValues propertyValues = new MutablePropertyValues(dataMap);
            PropertyAccessorFactory
                    .forBeanPropertyAccess(extra)
                    .setPropertyValues(propertyValues, true, true);
        } catch (Exception e) {
            throw new KbException(KbCode.APP_MODULE_ILLEGAL_FORMAT, "extra data type wrong", e);
        }
        return extra;
    }

    private static AppModulePage defaultForTab1(Integer siteId) {
        Layout layout = new Layout();
        layout.setIdentifier(LayoutIdentifier.HOME.getName());
        layout.setLayoutType(LayoutType.MODULE.getName());
        layout.setBackground("#fff9f9f9");
        layout.setPullToRefresh(true);
        layout.setShowScrollBar(false);
        layout.setOverlay(true);
        layout.setStatusBarStyle(LayoutStatusBarStyle.LIGHT.getName());

        NavigationBarSimple navBar = new NavigationBarSimple();
        navBar.setIdentifier("navigationBar");
        navBar.setClz("navigationBarMainPage");
        navBar.setBackground("#fff9f9f9");
        navBar.setTranslucentStatusBar(false);
        navBar.setTranslucentOnScroll(false);
        navBar.setTitleAlign("center");
        layout.setNavigationBar(navBar);

        List<AbsItem> itemList = new ArrayList<>(5);
        itemList.add(MainPageBanner.on().create());
        itemList.add(MainPageProgramSlider.on().create());
        itemList.add(MainPageDynamicAndSafeguardAndIssue.on().setFlag(1024).create());
        itemList.add(
                CommonBlockText.on()
                        .setText("资讯")
                        .setTextSize(22)
                        .setTextColor("#ff000000")
                        .setTextStyle(TextStyle.BOLD)
                        .setAlign(Align.LEFT)
                        .setMarginTop(10)
                        .setMarginRight(0)
                        .setMarginBottom(10)
                        .setMarginLeft(16)
                        .create()
        );
        itemList.add(MainPageNews.on().create());
        layout.setItems(itemList);

        MainPageTabExtra extra = new MainPageTabExtra();
        extra.setText("首页");
        extra.setIconSelected("http://static.kaiba315.com.cn/module/tab_clicked_home.png");
        extra.setIconUnselected("http://static.kaiba315.com.cn/module/tab_unclicked_home.png");
        extra.setTextColorSelected("#ff2c2c2c");
        extra.setTextColorUnselected("#ff777777");

        long currentTime = System.currentTimeMillis() / 1000;
        AppModulePage page = new AppModulePage();
        page.setSiteId(siteId);
        page.setType(MAIN_PAGE_TAB1.getValue());
        page.setStatus(AppModulePageStatus.ONLINE.getValue());
        page.setLastUpdateUserId(KbProperties.SYSTEM_USER_ID);
        page.setLayout(layout);
        page.setExtra(extra);
        page.setCreateTime(currentTime);
        page.setUpdateTime(currentTime);
        page.setPageVersion("6.0.0");
        page.setPageVersionNum(60000);
        return page;
    }

    private static AppModulePage defaultForTab2(Integer siteId) {
        Layout layout = new Layout();
        layout.setIdentifier(LayoutIdentifier.CIRCLE.getName());
        layout.setLayoutType(LayoutType.CIRCLE_HANGZHOU.getName());
        layout.setBackground("#fff9f9f9");
        layout.setPullToRefresh(false);
        layout.setShowScrollBar(false);
        layout.setOverlay(false);
        layout.setStatusBarStyle(LayoutStatusBarStyle.DEFAULT.getName());

        MainPageTabExtra extra = new MainPageTabExtra();
        extra.setText("圈子");
        extra.setIconSelected("http://static.kaiba315.com.cn/module/tab_clicked_circle.png");
        extra.setIconUnselected("http://static.kaiba315.com.cn/module/tab_unclicked_circle.png");
        extra.setTextColorSelected("#ff2c2c2c");
        extra.setTextColorUnselected("#ff777777");

        long currentTime = System.currentTimeMillis() / 1000;
        AppModulePage page = new AppModulePage();
        page.setSiteId(siteId);
        page.setType(MAIN_PAGE_TAB2.getValue());
        page.setStatus(AppModulePageStatus.ONLINE.getValue());
        page.setLastUpdateUserId(KbProperties.SYSTEM_USER_ID);
        page.setLayout(layout);
        page.setExtra(extra);
        page.setCreateTime(currentTime);
        page.setUpdateTime(currentTime);
        page.setPageVersion("6.0.0");
        page.setPageVersionNum(60000);
        return page;
    }

    private static AppModulePage defaultForTab3(Integer siteId) {
        Layout layout = new Layout();
        layout.setIdentifier(LayoutIdentifier.SERVICE.getName());
        layout.setLayoutType(LayoutType.ROAD_CONDITION.getName());
        layout.setBackground("#fff9f9f9");
        layout.setPullToRefresh(false);
        layout.setShowScrollBar(false);
        layout.setOverlay(false);
        layout.setStatusBarStyle(LayoutStatusBarStyle.DEFAULT.getName());

        MainPageTabExtra extra = new MainPageTabExtra();
        extra.setText("路况");
        extra.setIconSelected("http://static.kaiba315.com.cn/module/tab_clicked_service.png");
        extra.setIconUnselected("http://static.kaiba315.com.cn/module/tab_unclicked_service.png");
        extra.setTextColorSelected("#ff2c2c2c");
        extra.setTextColorUnselected("#ff777777");

        long currentTime = System.currentTimeMillis() / 1000;
        AppModulePage page = new AppModulePage();
        page.setSiteId(siteId);
        page.setType(MAIN_PAGE_TAB3.getValue());
        page.setStatus(AppModulePageStatus.ONLINE.getValue());
        page.setLastUpdateUserId(KbProperties.SYSTEM_USER_ID);
        page.setLayout(layout);
        page.setExtra(extra);
        page.setCreateTime(currentTime);
        page.setUpdateTime(currentTime);
        page.setPageVersion("6.0.0");
        page.setPageVersionNum(60000);
        return page;
    }

    private static AppModulePage defaultForTab4(Integer siteId) {
        Layout layout = new Layout();
        layout.setIdentifier(LayoutIdentifier.ME.getName());
        layout.setLayoutType(LayoutType.MODULE.getName());
        layout.setBackground("#fff9f9f9");
        layout.setPullToRefresh(false);
        layout.setShowScrollBar(false);
        layout.setOverlay(false);
        layout.setStatusBarStyle(LayoutStatusBarStyle.DEFAULT.getName());

        NavigationBarSimple navBar = new NavigationBarSimple();
        List<NavigationBarSimple.Button> buttonList = new ArrayList<>(1);
        NavigationBarSimple.Button btn = NavigationBarSimple.Button.on()
                .setIcon("http://static.kaiba315.com.cn/module/my_settings.png")
                .setPosition(NavigationBarButtonPosition.RIGHT2)
                .setActionModel(AppActionModel.on(AppActionType.PAGE_SETTINGS).create())
                .create();
        buttonList.add(btn);
        navBar.setButton(buttonList);
        navBar.setIdentifier("navigationBar");
        navBar.setClz("navigationBar");
        navBar.setBackground("#fff9f9f9");
        navBar.setTranslucentStatusBar(false);
        navBar.setTranslucentOnScroll(false);
        navBar.setTitleAlign("center");
        layout.setNavigationBar(navBar);

        List<AbsItem> itemList = new ArrayList<>(5);
        itemList.add(PersonalItemHeader.on().create());
        itemList.add(PersonalItemMyKB.on()
                .setIcon("http://static.kaiba315.com.cn/999F4045A4D68C8E7023681E8C81D1AF")
                .setTitle("我的K币")
                .setActionModel(
                        AppActionModel.on(AppActionType.PAGE_MY_KB)
                                .addActionParam("url", "https://api.kaiba315.com.cn/Scoreshop/my")
                                .addActionParam("title", "我的K币")
                                .addActionParam("requireLogin", "true")
                                .addActionParam("decorateUrl", "true")
                                .create()
                ).create()
        );
        itemList.add(PersonalItem.on()
                .setIcon("http://static.kaiba315.com.cn/AAD1B8DECD613B579B7F29424A02E7A9")
                .setTitle("我的奖品")
                .setActionModel(
                        AppActionModel.on(AppActionType.PAGE_WEB)
                                .addActionParam("url", "https://api.kaiba315.com.cn/prize/my")
                                .addActionParam("title", "我的奖品")
                                .addActionParam("requireLogin", "true")
                                .addActionParam("decorateUrl", "true")
                                .create()
                ).create()
        );
        itemList.add(PersonalItem.on()
                .setIcon("http://static.kaiba315.com.cn/66FCE5708F7B41F78C5D5031821B3963")
                .setTitle("我的红包")
                .setActionModel(
                        AppActionModel.on(AppActionType.PAGE_RED_PACKET)
                                .addActionParam("siteId", siteId)
                                .addActionParam("url", "https://api.kaiba315.com.cn/passred/index")
                                .addActionParam("title", "抢红包")
                                .addActionParam("requireLogin", "true")
                                .addActionParam("decorateUrl", "true")
                                .create()
                ).create()
        );
        itemList.add(Divider.on().setHeight(0.5f).setBackground("#ffefefef").setMarginTop(13).setMarginRight(15).setMarginBottom(13).setMarginLeft(15).create());
        itemList.add(PersonalItem.on()
                .setIcon("http://static.kaiba315.com.cn/39CA7E0E4F7E3DA946616863D3BEB58B")
                .setTitle("我的问答")
                .setActionModel(
                        AppActionModel.on(AppActionType.PAGE_ISSUE_ME).create()
                )
                .create()
        );
        itemList.add(PersonalItem.on()
                .setIcon("http://static.kaiba315.com.cn/CE109286AE4CD12800EABE7BF98D40CA")
                .setTitle("我的维权")
                .setActionModel(
                        AppActionModel.on(AppActionType.PAGE_SAFEGUARD_ME).create()
                )
                .create()
        );
        itemList.add(Divider.on().setHeight(0.5f).setBackground("#ffefefef").setMarginTop(13).setMarginRight(15).setMarginBottom(13).setMarginLeft(15).create());
        itemList.add(PersonalItem.on()
                .setIcon("http://static.kaiba315.com.cn/BE916AB52661D9FCB9D08810433F2AAE")
                .setTitle("我的关注")
                .setActionModel(
                        AppActionModel.on(AppActionType.PAGE_ISSUE_MY_FOLLOWEE).create()
                )
                .create()
        );
        itemList.add(PersonalItem.on()
                .setIcon("http://static.kaiba315.com.cn/9A1F6DCCD46DEC8A9A0BD79D1701E903")
                .setTitle("我的收藏")
                .setActionModel(
                        AppActionModel.on(AppActionType.PAGE_ISSUE_MY_COLLECTION).create()
                )
                .create()
        );
        itemList.add(Divider.on().setHeight(0.5f).setBackground("#ffefefef").setMarginTop(13).setMarginRight(15).setMarginBottom(13).setMarginLeft(15).create());
        itemList.add(PersonalItem.on()
                .setIcon("http://static.kaiba315.com.cn/33C2F0A24A6C35F02A4F7AE3C77E980C")
                .setTitle("推荐给好友")
                .setActionModel(
                        AppActionModel.on(AppActionType.RECOMMEND_TO_FRIEND).create()
                )
                .create()
        );
        itemList.add(PersonalItem.on()
                .setIcon("http://static.kaiba315.com.cn/E5BD7DF7B40E59C8C2CDABFA9443BD93")
                .setTitle("我要合作")
                .setActionModel(
                        AppActionModel.on(AppActionType.PAGE_WEB)
                                .addActionParam("url", "https://api.kaiba315.com.cn/Hezuo")
                                .addActionParam("title", "我要合作")
                                .addActionParam("requireLogin", "true")
                                .addActionParam("decorateUrl", "true")
                                .create()
                ).create()
        );
        itemList.add(Divider.on().setHeight(50f).setBackground("#fff9f9f9").setMarginTop(0).setMarginRight(0).setMarginBottom(0).setMarginLeft(0).create());
        layout.setItems(itemList);

        MainPageTabExtra extra = new MainPageTabExtra();
        extra.setText("我的");
        extra.setIconSelected("http://static.kaiba315.com.cn/module/tab_clicked_me.png");
        extra.setIconUnselected("http://static.kaiba315.com.cn/module/tab_unclicked_me.png");
        extra.setTextColorSelected("#ff2c2c2c");
        extra.setTextColorUnselected("#ff777777");

        long currentTime = System.currentTimeMillis() / 1000;
        AppModulePage page = new AppModulePage();
        page.setSiteId(siteId);
        page.setType(MAIN_PAGE_TAB4.getValue());
        page.setStatus(AppModulePageStatus.ONLINE.getValue());
        page.setLastUpdateUserId(KbProperties.SYSTEM_USER_ID);
        page.setLayout(layout);
        page.setExtra(extra);
        page.setCreateTime(currentTime);
        page.setUpdateTime(currentTime);
        page.setPageVersion("6.0.0");
        page.setPageVersionNum(60000);
        return page;
    }
}
