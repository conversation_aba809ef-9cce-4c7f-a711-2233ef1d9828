package com.kaiba.m.core.controller.news.article;

import com.kaiba.lib.base.assembler.Mapper;
import com.kaiba.lib.base.cache.redis.IntervalBasedListResult;
import com.kaiba.lib.base.constant.KbEndpoint;
import com.kaiba.lib.base.constant.KbHeader;
import com.kaiba.lib.base.constant.KbModule;
import com.kaiba.lib.base.constant.common.DummyFantasy;
import com.kaiba.lib.base.constant.news.NewsChannelType;
import com.kaiba.lib.base.constant.news.article.*;
import com.kaiba.lib.base.domain.appwidget.banner.BannerImageModel;
import com.kaiba.lib.base.domain.appwidget.banner.BannerInstanceModel;
import com.kaiba.lib.base.domain.common.ActionLink;
import com.kaiba.lib.base.domain.common.Image;
import com.kaiba.lib.base.domain.da.sensors.pgc.SensorsPGCObjUserActModel;
import com.kaiba.lib.base.domain.news.article.ArticleModel;
import com.kaiba.lib.base.domain.news.article.GroupApiQueryModel;
import com.kaiba.lib.base.domain.news.channel.CEpisodeModel;
import com.kaiba.lib.base.domain.news.channel.CProgrammeModel;
import com.kaiba.lib.base.domain.news.channel.ChannelConfigModel;
import com.kaiba.lib.base.domain.news.channel.ChannelMainPageModel;
import com.kaiba.lib.base.domain.news.pool.ArticleListModel;
import com.kaiba.lib.base.domain.news.pool.bygroup.GroupTopicModel;
import com.kaiba.lib.base.domain.news.pool.topic.TopicLayoutModel;
import com.kaiba.lib.base.lang.collections.KbColUtils;
import com.kaiba.lib.base.middleware.KbRes;
import com.kaiba.lib.base.response.KbCode;
import com.kaiba.lib.base.response.KbEntity;
import com.kaiba.lib.base.response.KbException;
import com.kaiba.lib.base.service.INeoNewsArticleService;
import com.kaiba.lib.base.service.ISensorsService;
import com.kaiba.lib.base.util.GsonUtils;
import com.kaiba.lib.base.util.StringUtils;
import com.kaiba.lib.base.util.appaction.AppActionType;
import com.kaiba.m.core.domain.news.channel.ChannelConfig;
import com.kaiba.m.core.domain.news.channel.ChannelMainPage;
import com.kaiba.m.core.domain.news.pool.bygroup.IdsGroup;
import com.kaiba.m.core.domain.news.pool.bygroup.IdsGroupArticle;
import com.kaiba.m.core.service.appcomponent.banner.AppBannerCacheModel;
import com.kaiba.m.core.service.appcomponent.banner.AppBannerCacheService;
import com.kaiba.m.core.service.news.article.NewsArticleCacheService;
import com.kaiba.m.core.service.news.article.NewsArticleModelHelper;
import com.kaiba.m.core.service.news.article.NewsReactCacheService;
import com.kaiba.m.core.service.news.channel.NewsChannelApiService;
import com.kaiba.m.core.service.news.channel.NewsChannelModelHelper;
import com.kaiba.m.core.service.news.pool.bygroup.NewsGroupApiService;
import com.kaiba.m.core.service.news.pool.bygroup.NewsGroupService;
import com.kaiba.m.core.service.news.pool.bygroup.NewsGroupTopicApiService;
import com.kaiba.m.core.service.news.pool.topic.NewsTopicLayoutService;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.web.bind.annotation.*;

import java.time.LocalTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * author: lyux
 * date: 2023-07-28
 */
@Slf4j
@RestController
@RequestMapping("/api/NeoNews/article")
public class NewsArticleApiController {

    private final KbRes kbRes;
    private final NewsArticleCacheService articleCacheService;
    private final NewsReactCacheService reactCacheService;
    private final NewsGroupApiService idsByGroupApiService;
    private final NewsGroupService idsByGroupService;
    private final NewsTopicLayoutService topicLayoutService;
    private final NewsGroupTopicApiService topicApiService;
    private final NewsChannelApiService channelApiService;
    private final INeoNewsArticleService articleService;
    private final ISensorsService sensorsService;
    private final AppBannerCacheService bannerCacheService;
    private final String pageHost;
    private final StringRedisTemplate redisTemplate;

    private final JCHZMainPageModel defaultJCHZMainPage;

    public NewsArticleApiController(
            @Qualifier("kbApiRes") KbRes kbRes,
            NewsArticleCacheService articleCacheService,
            NewsReactCacheService reactCacheService,
            NewsGroupApiService idsByGroupApiService,
            NewsGroupService idsByGroupService,
            NewsTopicLayoutService topicLayoutService,
            NewsGroupTopicApiService topicApiService,
            NewsChannelApiService channelApiService,
            INeoNewsArticleService articleService,
            ISensorsService sensorsService,
            AppBannerCacheService bannerCacheService,
            @Value("${kb.host.page}") String pageHost,
            StringRedisTemplate redisTemplate
    ) {
        this.kbRes = kbRes;
        this.articleCacheService = articleCacheService;
        this.reactCacheService = reactCacheService;
        this.idsByGroupApiService = idsByGroupApiService;
        this.idsByGroupService = idsByGroupService;
        this.topicLayoutService = topicLayoutService;
        this.topicApiService = topicApiService;
        this.channelApiService = channelApiService;
        this.articleService = articleService;
        this.sensorsService = sensorsService;
        this.bannerCacheService = bannerCacheService;
        this.pageHost = pageHost;
        this.redisTemplate = redisTemplate;
        this.defaultJCHZMainPage = JCHZMainPageMock(pageHost);
    }

    @ApiOperation("列表接口不会返回 content 字段")
    @PostMapping("/obj/getArticleListByGroup")
    public KbEntity<ArticleListModel> getArticleListByGroup(
            @RequestBody() GroupApiQueryModel query
    ) {
        if (query.getGroup() == null || query.getGroup() == null) {
            return kbRes.err(KbCode.REQUEST_PARAM_MISSING);
        }
        IdsGroup group = idsByGroupApiService.getGroupByKeyOrThrow(query.getGroup());
        ArticleListModel cached = idsByGroupApiService.getArticleList(
                group, query.getLastMark(), query.getLastId(), query.getPage(), query.getPageSize());
        ArticleListModel model = new ArticleListModel();
        model.setLastMark(cached.getLastMark());
        model.setCurrentMark(cached.getCurrentMark());
        model.setStyle(group.getStyle());
        if (cached.getArticles() == null || cached.getArticles().isEmpty()) {
            model.setArticles(Collections.emptyList());
        } else {
            model.setArticles(assembleArticleList(cached.getArticles(), group, query));
        }
        return kbRes.ok(model);
    }

    @ApiOperation("列表接口不会返回 content 字段")
    @PostMapping("/obj/getArticleListByGroupAndBatch")
    public KbEntity<ArticleListModel> getArticleListByGroupAndBatch(
            @RequestParam() String groupKey,
            @RequestParam() @ApiParam("日期批次, 格式为 20241015") Long batch
    ) {
        IdsGroup group = idsByGroupApiService.getGroupByKeyOrThrow(groupKey);
        List<String> articleIdList = idsByGroupService
                .getGroupArticlePageByGroup(group, null, batch, 1, 20)
                .stream().map(IdsGroupArticle::getArticleId).collect(Collectors.toList());
        Map<String, ArticleModel> articleMap = articleCacheService.getSummaryMapByIdIn(articleIdList);
        List<ArticleModel> articleList = articleIdList.stream()
                .map(articleMap::get)
                .filter(Objects::nonNull)
                .filter(article -> NewsState.ONLINE.name().equals(article.getState()))
                .collect(Collectors.toList());
        ArticleListModel model = new ArticleListModel();
        model.setStyle(group.getStyle());
        model.setArticles(assembleArticleList(articleList, group, new GroupApiQueryModel()));
        return kbRes.ok(model);
    }

    @ApiOperation("列表接口不会返回 content 字段")
    @PostMapping("/obj/getArticleListByChannel")
    public KbEntity<ArticleListModel> getArticleListByChannel(
            @RequestParam() String channelKey,
            @RequestParam(required = false) String lastMark,
            @RequestParam(required = false) String lastId,
            @RequestParam(required = false, defaultValue = "1") Integer page,
            @RequestParam(required = false, defaultValue = "20") Integer pageSize
    ) {
        ArticleListModel cached = channelApiService.getArticleList(channelKey, lastMark, lastId, page, pageSize);
        ArticleListModel model = new ArticleListModel();
        model.setLastMark(cached.getLastMark());
        model.setCurrentMark(cached.getCurrentMark());
        if (cached.getArticles() == null || cached.getArticles().isEmpty()) {
            model.setArticles(Collections.emptyList());
            return kbRes.ok(model);
        }
        List<ArticleModel> list = new ArrayList<>(cached.getArticles().size());
        for (ArticleModel cachedArticle : cached.getArticles()) {
            if (!NewsState.ONLINE.name().equals(cachedArticle.getState())) {
                continue;
            }
            ArticleModel article = NewsArticleModelHelper.copyArticleForFrontend(cachedArticle, null, null);
            NewsArticleModelHelper.assembleArticleAction(pageHost, article, null);
            // 浏览量暂时循环获取
            click2sensors(article, false);
            list.add(article);
        }
        model.setArticles(list);
        return kbRes.ok(model);
    }

    @ApiOperation("列表接口不会返回 content 字段")
    @PostMapping("/obj/getArticleListByIdIn")
    public KbEntity<List<ArticleModel>> getArticleListByIdIn(
            @RequestParam() String[] articleIds
    ) {
        if (articleIds == null || articleIds.length == 0) {
            return kbRes.ok(Collections.emptyList());
        } else {
            return kbRes.ok(articleCacheService.getSummaryListByIdIn(Arrays.asList(articleIds)).stream()
                    .filter(article -> NewsState.ONLINE.name().equals(article.getState()))
                    .collect(Collectors.toList()));
        }
    }

    @PostMapping("/obj/getArticleDetail")
    public KbEntity<ArticleModel> getArticleDetail(
            @RequestParam() String articleId,
            @RequestParam(required = false) String groupKey,
            @RequestParam(required = false) String tmp,
            @RequestParam(required = false, defaultValue = "true") Boolean allowCache,
            @RequestParam(required = false, defaultValue = "true") @ApiParam("是否补全分享配置. 一般客户端需要补全, H5不需要补全") Boolean fillShare
    ) {
        ArticleModel fromArticle;
        if (allowCache == null || allowCache) {
            fromArticle = articleCacheService.getSummaryById(articleId).orElseThrow(
                    () -> new KbException(KbCode.RESOURCE_NOT_FOUND).r("你要的内容我们没有找到...").li());
        } else {
            fromArticle = articleService.getArticleById(articleId, true, false).dataOrThrow();
        }
        if (fromArticle.getState() == null) {
            return kbRes.msg(KbCode.DATA_MALFORMED, "出错了...");
        }
        Long dueTime = null;
        if (NewsState.DRAFT.name().equals(fromArticle.getState())
                || NewsState.ARCHIVED.name().equals(fromArticle.getState())) {
            if (StringUtils.isEmpty(tmp)) {
                return kbRes.msg(KbCode.AUTH_FAIL, "内容信息错误、不存在、或需要授权才可以访问");
            } else {
                NewsArticleCacheService.TempToken token = articleCacheService.getTempVisitToken(tmp);
                if (articleId.equals(token.getArticleId())) {
                    dueTime = token.getDueTime();
                } else {
                    return kbRes.msg(KbCode.AUTH_FAIL, "错误的访问令牌");
                }
            }
        }
        IdsGroup group = groupKey == null ? null : idsByGroupApiService.getGroupByKey(groupKey);
        ArticleModel article = NewsArticleModelHelper.copyArticleForFrontend(fromArticle, group, null);
        article.setDueTime(dueTime);
        if (NewsState.SIGNED.name().equals(fromArticle.getState())
                || NewsState.ONLINE.name().equals(fromArticle.getState())) {
            if (fillShare == null || fillShare) {
                article.setShare(NewsArticleModelHelper.assembleArticleShare(pageHost, article, groupKey));
            }
            click2sensors(article, true);
        }
        if (!DisplayReactStyle.NONE.name().equals(article.getReactStyle())) {
            article.getStat().setReactCount(reactCacheService.getCount(articleId));
        }
        if (NewsContentType.resolveByName(fromArticle.getContentType())
                .map(NewsContentType::isHasContent).orElse(false)) {
            if (fromArticle.getContent() == null) {
                article.setContent(articleCacheService.getContentById(articleId));
            } else {
                article.setContent(fromArticle.getContent());
            }
        }
        if (article.getChannelKey() != null) {
            channelApiService.getChannelByKey(article.getChannelKey())
                    .map(ChannelConfig::getReplyStyle)
                    .ifPresent(replyStyle ->
                            NewsArticleModelHelper.determineReplyStyle(article, replyStyle));
        }
        return kbRes.ok(article);
    }

    @PostMapping("/obj/getActionByArticleId")
    public KbEntity<ActionLink> getActionByArticleId(
            @RequestParam() String articleId,
            @RequestParam(required = false) String groupKey,
            @RequestParam(required = false, defaultValue = "true") Boolean allowCache
    ) {
        ArticleModel article;
        if (allowCache == null || allowCache) {
            article = articleCacheService.getSummaryById(articleId).orElseThrow(
                    () -> new KbException(KbCode.RESOURCE_NOT_FOUND).r("你要的内容我们没有找到...").li());
        } else {
            article = articleService.getArticleById(articleId, true, false).dataOrThrow();
        }
        ActionLink al = new ActionLink();
        al.setId(article.getId());
        al.setTitle(article.getTitle());
        al.setSubTitle(article.getSubtitle());
        if (NewsRenderer.NONE.name().equals(article.getRenderer())) {
            al.setAction(article.getAction());
            al.setActionParams(article.getActionParams());
        } else {
            String url = NewsArticleModelHelper
                    .assembleArticleUrl(pageHost, articleId, article.getRenderer(), groupKey);
            al.putActionWebParam(url, article.getTitle());
        }
        return kbRes.ok(al);
    }

    @PostMapping("/obj/getOrCreateArticleThread")
    public KbEntity<String> getOrCreateArticleThread(
            @RequestParam() String articleId
    ) {
        return kbRes.asIs(articleService.getOrCreateArticleThread(articleId));
    }

    @PostMapping("/usr/likeArticle")
    public KbEntity<Long> doReactWithArticle(
            @RequestHeader(KbHeader.KB_USER_ID) Integer userId,
            @RequestParam() String articleId
    ) {
        return kbRes.asIs(articleService.doArticleReact(userId, articleId));
    }

    @PostMapping("/usr/cancelLikeArticle")
    public KbEntity<Long> cancelArticleReact(
            @RequestHeader(KbHeader.KB_USER_ID) Integer userId,
            @RequestParam() String articleId
    ) {
        return kbRes.asIs(articleService.cancelArticleReact(userId, articleId));
    }

    // --------------------------------------------------------

    @ApiOperation("分组话题: 获取话题布局")
    @PostMapping("/obj/getTopicLayoutById")
    public KbEntity<TopicLayoutModel> getTopicLayoutById(
            @RequestParam() String layoutId
    ) {
        return topicLayoutService.getCacheById(layoutId)
                .map(kbRes::ok)
                .orElseGet(() -> kbRes.err(KbCode.RESOURCE_NOT_FOUND));
    }

    @ApiOperation("分组话题: 获取分组话题")
    @PostMapping("/obj/getGroupTopicById")
    public KbEntity<GroupTopicModel> getGroupTopicById(
            @RequestParam() String topicId,
            @RequestParam(required = false, defaultValue = "false") Boolean needLayout,
            @RequestParam(required = false) String abracadabra
    ) {
        return topicApiService.getTopic(topicId, true)
                .filter(topic -> ABRACADABRA.equals(abracadabra)
                        || GroupTopicState.ONLINE.name().equals(topic.getState()))
                .map(topic -> {
                    if (topic.getLayoutId() != null && needLayout != null && needLayout) {
                        topic.setLayout(
                                topicLayoutService.getCacheById(topic.getLayoutId()).orElse(null));
                    }
                    return topic;
                })
                .map(kbRes::ok)
                .orElseGet(() -> kbRes.err(KbCode.RESOURCE_NOT_FOUND));
    }

    @ApiOperation("获取精彩杭州首页信息,专用接口. 节目数据写死")
    @PostMapping("/obj/getJCHZMainPageByKey")
    public KbEntity<JCHZMainPageModel> getJCHZMainPageByKey(
            @RequestHeader(KbHeader.KB_VC) Integer version,
            @RequestHeader(KbHeader.KB_EP) Integer endpoint,
            @RequestParam(required = false, defaultValue = "true") Boolean programmeActionNative
    ) {
        //加载页面配置, 默认栏目跳转类型是pageWeb. 客户端8.0.1之后的版本需要转化PAGE_NEWS_PROGRAMME
        JCHZMainPageModel model = getJCHZMainPageOrDefault();
        //是否需要栏目详情跳转客户端原生, 预留参数应对一些特殊情况需要特定跳转
        if (!programmeActionNative) {
            return kbRes.ok(model);
        }
        //非客户端直接返回
        if (endpoint != KbEndpoint.KAIBA_FLUTTER.getValue()) {
            return kbRes.ok(model);
        }
        if (version <= 80001) {
            return kbRes.ok(model);
        }
        //替换栏目跳转类型, 利用GSON进行深拷贝, 避免修改原内存中的数据
        JCHZMainPageModel newModel = GsonUtils.getGson().fromJson(GsonUtils.getGson().toJson(model), JCHZMainPageModel.class);
        List<String> programNativeFilter = newModel.getProgramNativeFilter() == null ? new ArrayList<>() : newModel.getProgramNativeFilter();
        if (newModel.getProgrammes() != null && newModel.getProgrammesInstanceKey() == null) {
            newModel.getProgrammes().stream()
                    .filter(p -> !programNativeFilter.contains(p.getTitle()))
                    .filter(p -> p.getAction().equals(AppActionType.PAGE_WEB.getAction()))
                    .forEach(p -> p.setAction(AppActionType.PAGE_NEWS_PROGRAMME.getAction()));
        }
        if (newModel.getProgrammes2() != null && newModel.getProgrammes2InstanceKey() == null) {
            newModel.getProgrammes2().stream()
                    .filter(p -> !programNativeFilter.contains(p.getTitle()))
                    .filter(p -> p.getAction().equals(AppActionType.PAGE_WEB.getAction()))
                    .forEach(p -> p.setAction(AppActionType.PAGE_NEWS_PROGRAMME.getAction()));
        }
        return kbRes.ok(newModel);
    }

    @ApiOperation("频道: 获取频道首页信息")
    @PostMapping("/obj/getChannelMainPageByKey")
    public KbEntity<ChannelMainPageModel> getChannelMainPageByKey(
            @RequestParam() String channelKey,
            @RequestParam(required = false, defaultValue = "true") @ApiParam("requireLayout 布局") Boolean rl,
            @RequestParam(required = false, defaultValue = "true") @ApiParam("requireContent 内容") Boolean rc,
            @RequestParam(required = false, defaultValue = "false") @ApiParam("requireProgramme 栏目") Boolean rp
    ) {
        ChannelConfig channel = channelApiService.getChannelByKey(channelKey).orElseThrow(
                () -> new KbException(KbCode.RESOURCE_NOT_FOUND).r("未知的频道").li());
        if (NDisplayState.GONE.name().equals(channel.getState())) {
            return kbRes.err(KbCode.RESOURCE_NOT_FOUND, "channel not found", "频道首页未找到");
        }
        ChannelMainPage mainPage = channelApiService.getChannelMainPage(channelKey).orElseThrow(
                () -> new KbException(KbCode.RESOURCE_NOT_FOUND).r("频道不存在").li());
        boolean requireLayout = (rl == null || rl) && mainPage.getLayoutId() != null;
        boolean requireContent = rc == null || rc;
        boolean requireProgramme = rp != null && rp
                && NewsChannelType.HCRT_CHANNEL.name().equals(channel.getType());
        ChannelMainPageModel model = new ChannelMainPageModel();
        model.setId(mainPage.getId());
        model.setChannelKey(channelKey);
        model.setChannelName(channel.getName());
        model.setChannelAbbr(channel.getAbbr());
        model.setChannelLogo(channel.getLogo());
        model.setTitle(mainPage.getTitle());
        model.setLayoutId(mainPage.getLayoutId());
        model.setProgrammeListStyle(mainPage.getProgrammeListStyle());
        model.setArticleListStyle(mainPage.getArticleListStyle());
        model.setTabs(mainPage.getTabs());
        model.setShare(mainPage.getShare());
        model.setShare(NewsChannelModelHelper.assembleChannelShare(pageHost, model));
        if (requireLayout) {
            topicLayoutService.getCacheById(mainPage.getLayoutId()).ifPresent(layout -> {
                if (requireContent) {
                    model.setLayout(layout);
                } else {
                    model.setLayout(Mapper.map(layout, TopicLayoutModel.class));
                    model.getLayout().setContent(null);
                    model.getLayout().setContentType(null);
                }
            });
        }
        if (requireProgramme) {
            model.setProgrammes(channelApiService.getProgrammeListByChannel(channelKey));
        }
        return kbRes.ok(model);
    }

    @ApiOperation("频道: 获取频道信息")
    @PostMapping("/obj/getChannelByKey")
    public KbEntity<ChannelConfigModel> getChannelByKey(
            @RequestParam() String channelKey
    ) {
        return channelApiService.getChannelByKey(channelKey)
                .map(NewsArticleApiController::channel2model)
                .map(kbRes::ok)
                .orElse(kbRes.err(KbCode.RESOURCE_NOT_FOUND));
    }

    @ApiOperation("频道: 获取频道列表. type 取 文广频道/HCRT_CHANNEL 或 区县市融媒/HZ_COUNTY")
    @PostMapping("/obj/getChannelListByType")
    public KbEntity<List<ChannelConfigModel>> getChannelListByType(
            @RequestParam() String channelType
    ) {
        List<ChannelConfig> channels = channelApiService.getChannelByType(channelType);
        return kbRes.ok(channels.stream()
                .filter(c -> NDisplayState.SHOW.name().equals(c.getState()))
                .map(NewsArticleApiController::channel2model)
                .collect(Collectors.toList()));
    }

    @ApiOperation("频道栏目: 获取栏目详情")
    @PostMapping("/obj/getProgrammeById")
    public KbEntity<CProgrammeModel> getProgrammeById(
            @RequestParam(required = false) String programmeId,
            @RequestParam(required = false) String programmeKey,
            @RequestParam(required = false, defaultValue = "false") Boolean needLayout,
            @RequestParam(required = false, defaultValue = "true") Boolean allowCache
    ) {
        if (programmeId == null && programmeKey == null) {
            return kbRes.err(KbCode.REQUEST_PARAM_MISSING);
        }
        if (programmeId == null) {
            programmeId = channelApiService.getProgrammeIdByKey(programmeKey).orElse(null);
        }
        if (programmeId == null) {
            return kbRes.err(KbCode.RESOURCE_NOT_FOUND);
        }
        return channelApiService.getProgrammeById(programmeId, allowCache)
                .filter(programme -> !NDisplayState.GONE.name().equals(programme.getState()))
                .map(programme -> {
                    if (programme.getLayoutId() != null && needLayout != null && needLayout) {
                        topicLayoutService.getCacheById(programme.getLayoutId()).ifPresent(layout -> {
                            programme.setLayout(layout);
                            if (layout.getHeaderImage() == null) {
                                layout.setHeaderImage(programme.getCover());
                            }
                        });
                    }
                    programme.setShare(NewsChannelModelHelper.assembleProgrammeShare(pageHost, programme));
                    return programme;
                })
                .map(kbRes::ok)
                .orElseGet(() -> kbRes.err(KbCode.RESOURCE_NOT_FOUND));
    }


    @ApiOperation("频道栏目: 根据频率频道获取栏目列表")
    @PostMapping("/obj/getProgrammeListByChannel")
    public KbEntity<List<CProgrammeModel>> getProgrammeListByChannel(
            @RequestParam() String channelKey
    ) {
        List<CProgrammeModel> list = channelApiService.getProgrammeListByChannel(channelKey);
        return kbRes.ok(list.stream()
                .filter(programme -> NDisplayState.SHOW.name().equals(programme.getState()))
                .collect(Collectors.toList()));
    }

    @ApiOperation("频道栏目: 根据稿件ID获取单期分组和拆条分组")
    @PostMapping("/obj/getProgrammeEpisodeByArticle")
    public KbEntity<CEpisodeModel> getProgrammeEpisodeByArticle(
            @RequestParam() @ApiParam("可以是单期稿件, 也可以是拆条稿件") String articleId
    ) {
        return channelApiService.getEpisodeByArticle(articleId)
                .map(kbRes::ok)
                .orElseGet(() -> kbRes.err(KbCode.RESOURCE_NOT_FOUND));
    }

    @ApiOperation("频道栏目: 根据栏目ID获取该栏目下的最新单期的文章ID")
    @PostMapping("/obj/getLatestEpisodeArticleIdByProgramme")
    public KbEntity<String> getLatestEpisodeArticleIdByProgramme(
            @RequestParam(required = false) String programmeId,
            @RequestParam(required = false) String programmeKey,
            @RequestParam(required = false, defaultValue = "true") Boolean allowCache
    ) {
        if (programmeId == null && programmeKey == null) {
            return kbRes.err(KbCode.REQUEST_PARAM_MISSING);
        }
        if (programmeId == null) {
            programmeId = channelApiService.getProgrammeIdByKey(programmeKey).orElse(null);
        }
        if (programmeId == null) {
            return kbRes.err(KbCode.RESOURCE_NOT_FOUND);
        }
        CProgrammeModel programme = channelApiService.getProgrammeById(programmeId, allowCache)
                .filter(p -> !NDisplayState.GONE.name().equals(p.getState()))
                .orElseThrow(() -> new KbException(KbCode.RESOURCE_NOT_FOUND).r("栏目跑丢了").li());
        IdsGroup group = idsByGroupApiService.getGroupByKeyOrThrow(programme.getGroup());
        IntervalBasedListResult<IdsGroupArticle> result =
                idsByGroupApiService.getArticleIdList(group, null, null, 1, 1);
        if (result.getList().isEmpty()) {
            return kbRes.msg("栏目是空的...");
        } else {
            return kbRes.ok(result.getList().get(0).getArticleId());
        }
    }

    // --------------------------------------------------------

    @ApiOperation("精彩杭州:首页数据缓存操作")
    @PostMapping("/manage/setJCHZMainPageCache")
    public KbEntity<Void> setJCHZMainPageCache(
            @RequestHeader(KbHeader.KB_USER_ID) Integer userId,
            @RequestParam @ApiParam("动作:s-保存覆盖,d-删除") String command,
            @RequestBody(required = false) @ApiParam("数据JSON结构, JCHZMainPageModel") JCHZMainPageModel data,
            @RequestParam @ApiParam("咒语") String abracadabra
    ) {
        log.info("user:{} operate JCHZMainPage cache, command:{}, data:{}", userId, command, data);
        if (!ABRACADABRA.equals(abracadabra)) {
            return kbRes.err(KbCode.ILLEGAL_ARGUMENT, "咒语错误");
        }
        switch (command) {
            case "s":
                redisTemplate.opsForValue().set(JCHZ_MAIN_PAGE_CACHE_KEY, GsonUtils.getGson().toJson(data));
                return kbRes.ok();
            case "d":
                redisTemplate.delete(JCHZ_MAIN_PAGE_CACHE_KEY);
                return kbRes.ok();
            default:
                return kbRes.err(KbCode.ILLEGAL_ARGUMENT, "错误的操作类型");
        }
    }

    @ApiOperation("精彩杭州:获取首页数据缓存")
    @PostMapping("/manage/getJCHZMainPageCache")
    public KbEntity<JCHZMainPageModel> getJCHZMainPageCache(
            @RequestParam @ApiParam("咒语") String abracadabra
    ) {
        if (!ABRACADABRA.equals(abracadabra)) {
            return kbRes.err(KbCode.ILLEGAL_ARGUMENT, "咒语错误");
        }
        return kbRes.ok(getJCHZMainPageCache());
    }

    // --------------------------------------------------------

    // 向神策打点
    private void click2sensors(ArticleModel model, boolean viewIncr) {
        SensorsPGCObjUserActModel act = new SensorsPGCObjUserActModel();
        act.setBusiness(KbModule.NEWS_NEO.name());
        act.setRef1(model.getId());
        long count = sensorsService.calculate(act, viewIncr);
        if (model.getGroups() != null && model.getGroups().contains("news_main_list.9")) {
            // TODO: 为了使新老资讯平滑过度, 将两边的浏览量加起来
            act.setBusiness(KbModule.NEWS.name());
            long countLegacy = sensorsService.calculate(act, false);
            count += countLegacy;
        }
        model.setVirtualViewCount(count);
    }

    private List<ArticleModel> assembleArticleList(
            List<ArticleModel> articles, IdsGroup group, GroupApiQueryModel query) {

        LinkedList<String> requireContentIdList = null;
        LinkedList<String> articleIds = null;
        List<ArticleModel> list = new ArrayList<>(articles.size());
        for (ArticleModel cachedArticle : articles) {
            if (!NewsState.ONLINE.name().equals(cachedArticle.getState())) {
                continue;
            }
            if (query.getRenderer() != null && !query.getRenderer().equals(cachedArticle.getRenderer())) {
                continue;
            }
            if (query.isRequireContent() &&
                    NewsContentType.resolveByName(cachedArticle.getContentType())
                            .map(NewsContentType::isHasContent).orElse(false)) {
                requireContentIdList = KbColUtils.ListOpt
                        .upsertLinkedList(requireContentIdList, cachedArticle.getId());
            }
            ArticleModel article = NewsArticleModelHelper.copyArticleForFrontend(cachedArticle, group, query);
            NewsArticleModelHelper.assembleArticleAction(pageHost, article, query.getGroup());
            if (query.isRequireViewStat()) {
                // 浏览量暂时循环获取
                click2sensors(article, false);
            }
            if (query.isRequireLikeStat()) {
                articleIds = KbColUtils.ListOpt
                        .upsertLinkedList(articleIds, cachedArticle.getId());
            }
            list.add(article);
        }

        Map<String, String> contentMap = requireContentIdList == null || requireContentIdList.isEmpty() ?
                Collections.emptyMap() : articleCacheService.getContentMapByIdIn(requireContentIdList);
        Map<String, Long> reactCountMap = query.isRequireLikeStat() ?
                reactCacheService.getMultiCount(articleIds) : Collections.emptyMap();

        if (!contentMap.isEmpty() || !reactCountMap.isEmpty()) {
            for (ArticleModel article : list) {
                article.setContent(contentMap.get(article.getId()));
                article.getStat().setReactCount(reactCountMap.get(article.getId()));
            }
        }

        return list;
    }

    private static ChannelConfigModel channel2model(ChannelConfig channel) {
        ChannelConfigModel model = new ChannelConfigModel();
        model.setChannel(channel.getChannel());
        model.setName(channel.getName());
        model.setAbbr(channel.getAbbr());
        model.setLogo(channel.getLogo());
        return model;
    }

    private static final String ABRACADABRA = "517c87b96af9d123c870ef04118fc203";

    private static final String JCHZ_MAIN_PAGE_CACHE_KEY = "java_core_jchz_main_page";

    private JCHZMainPageModel getJCHZMainPageCache() {
        String value = redisTemplate.opsForValue().get(JCHZ_MAIN_PAGE_CACHE_KEY);
        return value == null ? null : GsonUtils.getGson().fromJson(value, JCHZMainPageModel.class);
    }

    /**
     * 获取精彩杭州首页数据,优先从缓存中获取,若缓存不存在则返回默认数据
     * 主要功能:加载固定的banner实例和布局视频源的配置逻辑
     */
    private JCHZMainPageModel getJCHZMainPageOrDefault() {
        JCHZMainPageModel model = getJCHZMainPageCache();
        if (model == null) {
            model = defaultJCHZMainPage;
        }
        //设置banner实例
        AppBannerCacheModel bannerCache = bannerCacheService.getCacheByInstance(9, null, "jchz_programme_banner").orElse(null);
        if (bannerCache != null) {
            BannerInstanceModel programBanner = Mapper.map(bannerCache.getInstance(), BannerInstanceModel.class);
            if (bannerCache.getOnlineBannerList() == null || bannerCache.getOnlineBannerList().isEmpty()) {
                programBanner.setImageList(bannerCache.getDefaultBannerList());
            } else {
                programBanner.setImageList(bannerCache.getOnlineBannerList());
            }
            model.setProgrammeBanner(programBanner);
        }
        //读取Banner实例图片转为栏目配置
        if (model.getProgrammesInstanceKey() != null) {
            AppBannerCacheModel programmesRef = bannerCacheService.getCacheByInstance(9, null, model.getProgrammesInstanceKey()).orElse(null);
            if (programmesRef != null) {
                model.setProgrammes(
                        programmesRef.getOnlineBannerList().stream()
                        .map(this::mapImage2ActionLink)
                        .collect(Collectors.toList()));
            }
        }
        if (model.getProgrammes2InstanceKey() != null) {
            AppBannerCacheModel programmes2Ref = bannerCacheService.getCacheByInstance(9, null, model.getProgrammes2InstanceKey()).orElse(null);
            if (programmes2Ref != null) {
                //栏目2需要加载更多内容, 默认缓存20条无法满足
                List<ActionLink> programmes2 = bannerCacheService.getOnlineBanner100Cache(programmes2Ref.getInstance().getId())
                        .stream()
                        .map(this::mapImage2ActionLink)
                        .collect(Collectors.toList());
                model.setProgrammes2(programmes2);
            }
        }

        //是否启用直播流 1.是否强制启用直播源 2.是否在1930-2000点之间
        boolean useInLive = (model.isLayoutVideoLiveForce() || isTimeBetween1930And2000()) && model.getLayoutVideoLiveUrl() != null;

        //非自动获取视频源的模式直接返回
        if (!model.isLayoutVideoAuto()) {
            return model;
        }

        //自动获取视频源模式
        String videoUrl;
        String videoCoverUrl = null;

        if (useInLive) {
            //使用直播源
            videoUrl = model.getLayoutVideoLiveUrl();
        } else {
            //使用栏目最新数据
            String programmeKey = model.getLayoutVideoGetProgrammeKey() == null ? "HTV1_NEWS" : model.getLayoutVideoGetProgrammeKey();
            String programmeId = channelApiService.getProgrammeIdByKey(programmeKey).orElse(null);
            CProgrammeModel programme = getProgrammeById(programmeId, null,false, true).dataOrThrow();
            GroupApiQueryModel groupQuery = new GroupApiQueryModel();
            groupQuery.setGroup(programme.getGroup());
            groupQuery.setPage(1);
            groupQuery.setPageSize(1);
            ArticleListModel articleList = getArticleListByGroup(groupQuery).dataOrThrow();
            ArticleModel article = new ArticleModel();
            if (articleList.getArticles() != null && !articleList.getArticles().isEmpty()) {
                article = articleList.getArticles().get(0);
            }

            videoUrl = article.getVideo() == null ? null : article.getVideo().getVideoUrl();
            videoCoverUrl = article.getVideo() == null ? null : article.getVideo().getCoverUrl();
        }

        model.getLayout().setVideoUrl(videoUrl);
        model.getLayout().setVideoLive(useInLive);
        if (videoCoverUrl != null) {
            Image.Builder videoCover = new Image.Builder(videoCoverUrl);
            videoCover.size(636, 367);
            model.getLayout().setVideoCover(videoCover.create());
        }

        return model;
    }

    private ActionLink mapImage2ActionLink(BannerImageModel image) {
        ActionLink actionLink = ActionLink.asAction(image.getAction(), image.getTitle(), image.getUrl());
        actionLink.setActionParams(image.getActionParams());
        return actionLink;
    }

    /**
     * 精彩杭州MOCK数据
     * 默认使用综合频道
     */
    private static JCHZMainPageModel JCHZMainPageMock(String pageHost) {
        JCHZMainPageModel model = new JCHZMainPageModel();
        //基础数据
        model.setChannelKey("HTV1");
        model.setTitle("综合频道");
        model.setProgrammeListStyle(ChannelProgrammeListStyle.SCROLL.name());
        model.setArticleListStyle(ChannelArticleListStyle.ONLINE_TIME_DESC.name());
        model.setChannelName("杭州综合频道");
        model.setChannelAbbr("综合频道");

        //layout数据
        TopicLayoutModel layout = new TopicLayoutModel();
        layout.setStyle(DummyFantasy.CENTAUR.name());
        layout.setBkgColor("#3f4f5");
        Image.Builder headImage = new Image.Builder("https://static.kaiba315.com.cn/image/0m0z3t8ooah9lm6q2gr6.jpg");
        headImage.size(636, 367);
        layout.setHeaderImage(headImage.create());
        layout.setHeaderStyle(DummyFantasy.CENTAUR.name());
        Image.Builder videoCover = new Image.Builder("https://static.kaiba315.com.cn/image/0m10ngnduc9s6773l0z4.jpg");
        videoCover.size(636, 367);
        layout.setVideoCover(videoCover.create());
        layout.setVideoEnabled(true);
        model.setLayout(layout);
        model.setLayoutVideoAuto(true);

        //基础栏目数据
        List<ActionLink> actionLinks = new ArrayList<>();
        actionLinks.add((ActionLink) ActionLink.asAction(AppActionType.PAGE_WEB.getAction(), "杭州新闻联播", "https://static.kaiba315.com.cn/image/0m0z3gys0wc6itd7kjv8.jpg")
                .putActionParam("title", "杭州新闻联播")
                .putActionParam("programmeKey", "HTV1_NEWS")
                .putActionParam("url","https://" + pageHost + "/neo-news/programme-main-page?programmeKey=HTV1_NEWS"));
        actionLinks.add((ActionLink) ActionLink.asAction(AppActionType.PAGE_WEB.getAction(), "新闻60分", "https://static.kaiba315.com.cn/image/0m0z3jac6oldfmzihb0s.jpg")
                .putActionParam("title", "新闻60分")
                .putActionParam("programmeKey", "NEWS_60M")
                .putActionParam("url","https://" + pageHost + "/neo-news/programme-main-page?programmeKey=NEWS_60M"));
        model.setProgrammes(actionLinks);
        //栏目2数据
        List<ActionLink> actionLinks2 = new ArrayList<>();
        actionLinks2.add((ActionLink) ActionLink.asAction(AppActionType.PAGE_WEB.getAction(), "明珠新闻", "https://static.kaiba315.com.cn/image/0m22vibiwckvttztiu0b.jpg")
                .putActionParam("title", "明珠新闻")
                .putActionParam("programmeKey", "HTV2_NEWS")
                .putActionParam("url","https://" + pageHost + "/neo-news/programme-main-page?programmeKey=HTV2_NEWS"));
        actionLinks2.add((ActionLink) ActionLink.asAction(AppActionType.PAGE_WEB.getAction(), "爱上舞台", "https://static.kaiba315.com.cn/image/0lvbq6o3bmvrrt4riepw.png")
                .putActionParam("title", "爱上舞台")
                .putActionParam("programmeKey", "STAGE_PASSION")
                .putActionParam("url","https://" + pageHost + "/neo-news/programme-main-page?programmeKey=STAGE_PASSION"));
        actionLinks2.add((ActionLink) ActionLink.asAction(AppActionType.PAGE_WEB.getAction(), "阿六头新闻", "https://static.kaiba315.com.cn/0lkqvydw9hjfqoj0.png")
                .putActionParam("title", "阿六头新闻")
                .putActionParam("programmeKey", "ALIUTOU_NEWS_REPORT")
                .putActionParam("url","https://" + pageHost + "/neo-news/programme-main-page?programmeKey=ALIUTOU_NEWS_REPORT"));
        actionLinks2.add((ActionLink) ActionLink.asAction(AppActionType.PAGE_WEB.getAction(), "亮眼看世界", "https://static.kaiba315.com.cn/image/0lxvu9xy72gqe8qh14pv.jpg")
                .putActionParam("title", "亮眼看世界")
                .putActionParam("programmeKey", "HTV5_LOOK_WORLD")
                .putActionParam("url","https://" + pageHost + "/neo-news/programme-main-page?programmeKey=HTV5_LOOK_WORLD"));
        actionLinks2.add((ActionLink) ActionLink.asAction(AppActionType.PAGE_WEB.getAction(), "新动新播报", "https://static.kaiba315.com.cn/image/0m2d3ebz8zras3xas2dy.png")
                .putActionParam("title", "新动新播报")
                .putActionParam("programmeKey", "MOB_NEWS")
                .putActionParam("url","https://" + pageHost + "/neo-news/programme-main-page?programmeKey=MOB_NEWS"));
        actionLinks2.add((ActionLink) ActionLink.asAction(AppActionType.PAGE_WEB.getAction(), "警界41", "https://static.kaiba315.com.cn/image/0lx0195j7gr6eff8gtip.png")
                .putActionParam("title", "警界41")
                .putActionParam("programmeKey", "POLICE_41")
                .putActionParam("url","https://" + pageHost + "/neo-news/programme-main-page?programmeKey=POLICE_41"));
        actionLinks2.add((ActionLink) ActionLink.asAction(AppActionType.PAGE_WEB.getAction(), "杭州体育+", "https://static.kaiba315.com.cn/image/0m0ko2nkd7eyacyj8hig.jpg")
                .putActionParam("title", "杭州体育+")
                .putActionParam("programmeKey", "HTV5_HZTY_PLUS")
                .putActionParam("url","https://" + pageHost + "/neo-news/programme-main-page?programmeKey=HTV5_HZTY_PLUS"));
        actionLinks2.add((ActionLink) ActionLink.asAction(AppActionType.PAGE_WEB.getAction(), "开心茶馆", "https://static.kaiba315.com.cn/image/0m063y913ovhm18uw5s3.jpg")
                .putActionParam("title", "开心茶馆")
                .putActionParam("programmeKey", "PLEASANT_TEA_HOUSE")
                .putActionParam("url","https://" + pageHost + "/neo-news/programme-main-page?programmeKey=PLEASANT_TEA_HOUSE"));
        model.setProgrammes2(actionLinks2);

        return model;
    }

    public static boolean isTimeBetween1930And2000() {
        LocalTime currentTime = LocalTime.now();
        LocalTime startTime = LocalTime.of(19, 30);
        LocalTime endTime = LocalTime.of(20, 0);
        return !currentTime.isBefore(startTime) && currentTime.isBefore(endTime);
    }

}
