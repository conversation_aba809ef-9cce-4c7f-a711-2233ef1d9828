package com.kaiba.m.core.controller.app.homeframe;

import com.kaiba.lib.base.domain.apphome.AppServiceFrameModel;
import com.kaiba.lib.base.middleware.KbRes;
import com.kaiba.lib.base.response.KbCode;
import com.kaiba.lib.base.response.KbEntity;
import com.kaiba.lib.base.response.KbException;
import com.kaiba.lib.base.service.IAppServiceFrameService;
import com.kaiba.m.core.service.appcomponent.home.AppHomeFrameModelHelper;
import com.kaiba.m.core.service.appcomponent.home.AppServiceFrameModelHelper;
import com.kaiba.m.core.service.appcomponent.home.AppServiceFrameService;
import com.kaiba.m.core.util.PageUtils;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Sort;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @version AppServiceFrameServiceController, v0.1 2023/8/2 10:25 daopei Exp $
 **/
@Slf4j
@RestController
@RequiredArgsConstructor
public class AppServiceFrameServiceController implements IAppServiceFrameService {

    @NonNull
    private KbRes kbRes;
    @NonNull
    private AppServiceFrameService appServiceFrameService;

    @Override
    public KbEntity<AppServiceFrameModel> create(AppServiceFrameModel model) {
        return kbRes.ok(AppServiceFrameModelHelper.instance2Model(appServiceFrameService.create(model)));
    }

    @Override
    public KbEntity<AppServiceFrameModel> update(AppServiceFrameModel model) {
        return kbRes.ok(AppServiceFrameModelHelper.instance2Model(appServiceFrameService.update(model)));
    }

    @Override
    public KbEntity<Void> deleteById(String id) {
        appServiceFrameService.deleteById(id);
        return kbRes.ok();
    }

    @Override
    public KbEntity<Void> updateStateById(String id, Integer state) {
        appServiceFrameService.updateStateById(id,state);
        return kbRes.ok();
    }

    @Override
    public KbEntity<Void> refreshOnlineTime(String id) {
        appServiceFrameService.refreshOnlineTime(id);
        return kbRes.ok();
    }

    @Override
    public KbEntity<AppServiceFrameModel> getById(String id) {
        return appServiceFrameService.getById(id)
                .map(AppServiceFrameModelHelper::instance2Model)
                .map(kbRes::ok)
                .orElseThrow(() -> new KbException(KbCode.RESOURCE_NOT_FOUND));
    }

    @Override
    public KbEntity<List<AppServiceFrameModel>> getListBySiteIdAndStates(Integer siteId, List<Integer> states, Integer page, Integer pageSize) {
        return kbRes.ok(
                appServiceFrameService.getListBySiteIdAndStates(siteId, states, PageUtils.ofDefault(page, pageSize, Sort.by(Sort.Direction.DESC, "onlineTime","createTime")))
                        .map(AppServiceFrameModelHelper::instance2Model));
    }
}
