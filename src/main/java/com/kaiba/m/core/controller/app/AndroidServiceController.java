package com.kaiba.m.core.controller.app;

import com.kaiba.lib.base.annotation.api.KbCheckSignature;
import com.kaiba.lib.base.constant.KbSignType;
import com.kaiba.lib.base.middleware.KbRes;
import com.kaiba.lib.base.response.KbEntity;
import com.kaiba.m.core.domain.AndroidErrorRecord;
import com.kaiba.m.core.model.AndroidErrorRecordModel;
import com.kaiba.m.core.service.android.AndroidErrorService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * Created by shenxl on 2021/12/16
 */
@Slf4j
@RestController
public class AndroidServiceController {
    private final KbRes kbRes;
    private final AndroidErrorService androidErrorService;

    public AndroidServiceController(Kb<PERSON><PERSON> kbRes, AndroidErrorService androidErrorService) {
        this.kbRes = kbRes;
        this.androidErrorService = androidErrorService;
    }

    @KbCheckSignature(type = KbSignType.CID)
    @PostMapping(path = { "/android/createErrorRecord", "/n/android/createErrorRecord" })
    public KbEntity<String> createErrorRecord(
            @RequestHeader() Integer kb_vc,
            @RequestHeader() String kb_dv,
            @RequestHeader() String kb_db,
            @RequestHeader() String kb_ep,
            @RequestHeader() String kb_cid,
            @RequestHeader(required = false) Integer kb_uid,
            @RequestParam() String type,
            @RequestParam() Boolean isDebug,
            @RequestParam() Long happenTime,
            @RequestParam(required = false) String message,
            @RequestParam(required = false) String trace,
            @RequestParam(required = false) String currentActivity,
            @RequestParam(required = false) Long maxAppMemory,
            @RequestParam(required = false) Long freeAppMemory,
            @RequestParam(required = false) Long totalSystemMemory,
            @RequestParam(required = false) Long freeSystemMemory,
            @RequestParam(required = false) Long freeStorage,
            @RequestParam(required = false) Long totalStorage
    ) {
        AndroidErrorRecord record = new AndroidErrorRecord();
        record.setType(type);
        record.setIsDebug(isDebug);
        record.setHappenTime(happenTime);
        record.setVc(kb_vc);
        record.setMessage(message);
        record.setTrace(trace);
        record.setCurrentActivity(currentActivity);
        record.setMaxAppMemory(maxAppMemory);
        record.setFreeAppMemory(freeAppMemory);
        record.setTotalSystemMemory(totalSystemMemory);
        record.setFreeSystemMemory(freeSystemMemory);
        record.setFreeStorage(freeStorage);
        record.setTotalStorage(totalStorage);
        record.setDv(kb_dv);
        record.setDb(kb_db);
        record.setEp(kb_ep);
        record.setUserId(kb_uid);
        record.setCid(kb_cid);
        androidErrorService.create(record);
        return kbRes.ok();
    }

    @PostMapping(path = "/android/searchErrorRecord")
    public KbEntity<List<AndroidErrorRecordModel>> searchErrorRecord(
            @RequestParam(required = false) String type,
            @RequestParam(required = false) String message,
            @RequestParam(required = false) Long happenTimeStart,
            @RequestParam(required = false) Long happenTimeEnd,
            @RequestParam(required = false) Integer vc,
            @RequestParam(required = false) Integer userId,
            @RequestParam(required = false, defaultValue = "1") Integer page,
            @RequestParam(required = false, defaultValue = "15") Integer pageSize
    ) {
        return kbRes.ok(androidErrorService.search(type, message, happenTimeStart, happenTimeEnd, vc, userId, page, pageSize));
    }

    @PostMapping(path = "/android/removeErrorRecord")
    public KbEntity<String> removeErrorRecord(
            @RequestParam(required = false) String type,
            @RequestParam(required = false) Long deadline
    ) {
        androidErrorService.remove(type, deadline);
        return kbRes.ok();
    }
}