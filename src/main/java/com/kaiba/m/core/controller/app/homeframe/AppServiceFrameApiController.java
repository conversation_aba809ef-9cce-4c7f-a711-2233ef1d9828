package com.kaiba.m.core.controller.app.homeframe;

import com.kaiba.lib.base.annotation.apiparam.KbSiteId;
import com.kaiba.lib.base.domain.apphome.AppServiceFrameModel;
import com.kaiba.lib.base.middleware.KbRes;
import com.kaiba.lib.base.response.KbCode;
import com.kaiba.lib.base.response.KbEntity;
import com.kaiba.lib.base.response.KbException;
import com.kaiba.m.core.service.appcomponent.home.AppServiceFrameCacheService;
import io.swagger.annotations.ApiOperation;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @version AppServiceFrameApiController, v0.1 2023/8/2 10:56 daopei Exp $
 **/
@Slf4j
@RestController
@RequestMapping("/api/appServiceFrame")
@Validated
@RequiredArgsConstructor
public class AppServiceFrameApiController {

    @NonNull
    @Qualifier("kbApiRes")
    private KbRes kbRes;
    @NonNull
    private AppServiceFrameCacheService appServiceFrameCacheService;


    @ApiOperation("查询HomeFrame配置")
    @PostMapping("/getBySiteId")
    public KbEntity<AppServiceFrameModel> getBySiteId(
            @RequestParam @KbSiteId Integer siteId
    ) {
        return appServiceFrameCacheService.getOnlineInstanceBySiteId(siteId)
                .map(kbRes::ok)
                .orElseThrow(() -> new KbException(KbCode.RESOURCE_NOT_FOUND));
    }

    @ApiOperation("查询ServiceFrame配置-beta")
    @PostMapping("/getBetaBySiteId")
    public KbEntity<AppServiceFrameModel> getBetaBySiteId(
            @RequestParam @KbSiteId Integer siteId
    ) {
        return appServiceFrameCacheService.getBetaInstanceBySiteId(siteId)
                .map(kbRes::ok)
                .orElseThrow(() -> new KbException(KbCode.RESOURCE_NOT_FOUND));
    }
}
