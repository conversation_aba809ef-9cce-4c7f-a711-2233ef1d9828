package com.kaiba.m.core.controller;

import com.kaiba.lib.base.constant.KbEndpoint;
import com.kaiba.lib.base.domain.sms.SmsModel;
import com.kaiba.lib.base.response.KbCode;
import com.kaiba.lib.base.response.KbEntity;
import com.kaiba.lib.base.response.KbException;
import com.kaiba.lib.base.middleware.KbRes;
import com.kaiba.lib.base.service.ISmsService;
import com.kaiba.m.core.service.sms.SmsService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RestController;

import java.util.Arrays;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2018/12/20
 */
@Slf4j
@RestController
public class SmsServiceController implements ISmsService {

    private final KbRes kbRes;
    private final SmsService smsService;

    @Autowired
    public SmsServiceController(KbRes kbRes,
                                SmsService smsService) {
        this.kbRes = kbRes;
        this.smsService = smsService;
    }

    @Override
    public KbEntity<String> sendMessage(String[] targetIds, String templateId, Integer endpoint, String[] placeHolder) {
        SmsModel model = SmsModel.Builder.on()
                .addPhoneNumbers(Arrays.asList(targetIds))
                .setTemplateId(templateId)
                .setPlaceHolder(placeHolder)
                .setEndpoint(KbEndpoint.valueOf(endpoint).orElseThrow(KbException.supplier(KbCode.REQUEST_PARAM_INVALID)))
                .create();
        smsService.sendMessage(model);
        return kbRes.ok();
    }

    @Override
    public KbEntity<String> sendMessageWithParam(String[] targetIds, String templateId, Integer endpoint, Map<String, String> placeHolderMap) {
        SmsModel model = SmsModel.Builder.on()
                .addPhoneNumbers(Arrays.asList(targetIds))
                .setTemplateId(templateId)
                .setPlaceHolderMap(placeHolderMap)
                .setEndpoint(KbEndpoint.valueOf(endpoint).orElseThrow(KbException.supplier(KbCode.REQUEST_PARAM_INVALID)))
                .create();
        smsService.sendMessage(model);
        return kbRes.ok();
    }

    @Override
    public KbEntity<String> sendSmsCode(String mobile, Integer expire, String mark) {
        return kbRes.ok(smsService.sendSmsCode(mobile, KbEndpoint.KAIBA, expire, mark));
    }

    @Override
    public KbEntity<Boolean> checkSmsCode(String mobile, String code, String mark) {
        return kbRes.ok(smsService.checkSmsCode(mobile, code, mark));
    }

}
