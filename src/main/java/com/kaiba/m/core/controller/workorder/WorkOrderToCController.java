package com.kaiba.m.core.controller.workorder;

import com.kaiba.lib.base.annotation.api.KbCheckSignature;
import com.kaiba.lib.base.annotation.apiparam.KbPage;
import com.kaiba.lib.base.annotation.apiparam.KbPageSize;
import com.kaiba.lib.base.annotation.apiparam.KbUserId;
import com.kaiba.lib.base.assembler.Mapper;
import com.kaiba.lib.base.constant.KbHeader;
import com.kaiba.lib.base.constant.KbSignType;
import com.kaiba.lib.base.constant.sms.SmsCodeMark;
import com.kaiba.lib.base.constant.workorder.WOCloseReason;
import com.kaiba.lib.base.constant.workorder.WOIdentity;
import com.kaiba.lib.base.constant.workorder.WOOperation;
import com.kaiba.lib.base.domain.workorder.*;
import com.kaiba.lib.base.middleware.KbRes;
import com.kaiba.lib.base.response.KbCode;
import com.kaiba.lib.base.response.KbEntity;
import com.kaiba.lib.base.response.KbException;
import com.kaiba.lib.base.service.IUserService;
import com.kaiba.m.core.domain.workorder.WOCase;
import com.kaiba.m.core.domain.workorder.WOCloseType;
import com.kaiba.m.core.domain.workorder.WOEvent;
import com.kaiba.m.core.domain.workorder.WOFollower;
import com.kaiba.m.core.domain.workorder.WOTeam;
import com.kaiba.m.core.service.workorder.*;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.*;
import java.util.stream.Collectors;

/**
 * author: lyux
 * date: 2024-01-17
 *
 * 工单系统 C 端用户接口 (to C). C 端用户有三类:
 * 提问者 {@link WOIdentity#CLIENT},
 * 同问者 ({@link WOIdentity#FOLLOWER},
 * 浏览者 {@link WOIdentity#VIEWER}.
 */
@Slf4j
@RestController
@RequestMapping("/WorkOrder/toc/")
@Validated
public class WorkOrderToCController {

    private final KbRes kbRes;
    private final WOCaseApiService apiService;
    private final WOCaseOperator operator;
    private final WOTeamService teamService;
    private final WOMemberAccountService memberAccountService;
    private final IUserService userService;
    private final WOCloseTypeService closeService;


    public WorkOrderToCController(
            KbRes kbRes,
            WOCaseApiService apiService,
            WOCaseOperator operator,
            WOTeamService teamService,
            WOMemberAccountService memberAccountService,
            IUserService userService,
            WOCloseTypeService closeService
    ) {
        this.kbRes = kbRes;
        this.apiService = apiService;
        this.operator = operator;
        this.teamService = teamService;
        this.memberAccountService = memberAccountService;
        this.userService = userService;
        this.closeService = closeService;
    }

    // ------------------------------------------------------------

    /** {@link WOOperation#REPLY} */
    @ApiOperation("回复案件. noteId 和 message 必传一个, 若都传则只 noteId 生效.")
    @PostMapping("/usr/client/replyCase")
    public KbEntity<WOEventModel> replyCase(
            @RequestHeader(KbHeader.KB_USER_ID) @KbUserId Integer userId,
            @RequestParam() String caseId,
            @RequestParam(required = false) String noteId,
            @RequestParam(required = false) String message
    ) {
        WOCase woCase = apiService.getCaseById(caseId).orElseThrow(
                () -> new KbException(KbCode.RESOURCE_NOT_FOUND).r("案件不存在").li());
        WOEvent event = operator.reply(woCase, userId, WOIdentity.CLIENT.asTeam(), noteId, message);
        return kbRes.ok(apiService.event2model(event));
    }

    /** {@link WOOperation#CLOSE} */
    @ApiOperation("用户结案")
    @PostMapping("/usr/client/closeCase")
    public KbEntity<WOEventModel> closeCase(
            @RequestHeader(KbHeader.KB_USER_ID) @KbUserId Integer userId,
            @RequestParam() String caseId,
            @RequestParam() String reasonType,
            @RequestParam(required = false) String reasonText,
            @RequestParam(required = false) String noteId,
            @RequestParam(required = false) String message,
            @RequestParam(required = false) @ApiParam("关联业务/事件ID") String uuid
    ) {
        WOCloseType closeType = closeService.getByCode(reasonType)
                .orElseThrow(() -> new KbException(KbCode.ILLEGAL_ARGUMENT).r("未使用正确的结案原因").li());
        if (!closeType.getIdentity().contains(WOIdentity.CLIENT.name())) {
            return kbRes.err(KbCode.REQUEST_PARAM_INVALID, "reason not accepted: " + reasonType);
        }
//        if (WOCloseReason.USER_CLOSE_ACCEPT != reason
//                && WOCloseReason.USER_CLOSE_GRUDGE != reason
//                && WOCloseReason.USER_CANCEL != reason) {
//            return kbRes.err(KbCode.REQUEST_PARAM_INVALID, "reason not accepted: " + reasonType);
//        }
        WOCase woCase = apiService.getCaseById(caseId).orElseThrow(
                () -> new KbException(KbCode.RESOURCE_NOT_FOUND).r("案件不存在").li());
        WOEvent event = operator.close(woCase, userId, WOIdentity.CLIENT.asTeam(), reasonType, reasonText, noteId, message, uuid);
        return kbRes.ok(apiService.event2model(event));
    }

    /** {@link WOOperation#CLOSE_REFUSE} */
    @ApiOperation("拒绝关闭案件")
    @PostMapping("/usr/client/refuseCloseCase")
    public KbEntity<WOEventModel> refuseCloseCase(
            @RequestHeader(KbHeader.KB_USER_ID) @KbUserId Integer userId,
            @RequestParam() String caseId,
            @RequestParam(required = false) String noteId,
            @RequestParam(required = false) String message,
            @RequestParam(required = false) @ApiParam("关联业务/事件ID") String uuid
    ) {
        WOCase woCase = apiService.getCaseById(caseId).orElseThrow(
                () -> new KbException(KbCode.RESOURCE_NOT_FOUND).r("案件不存在").li());
        WOEvent event = operator.closeRefuse(woCase, userId, WOIdentity.CLIENT.asTeam(), noteId, message, uuid);
        return kbRes.ok(apiService.event2model(event));
    }

    /** {@link WOOperation#RATE} */
    @ApiOperation("案件评分")
    @PostMapping("/usr/client/rateCase")
    public KbEntity<WOEventModel> rateCase(
            @RequestHeader(KbHeader.KB_USER_ID) @KbUserId Integer userId,
            @RequestParam() String caseId,
            @RequestParam() Integer rating,
            @RequestParam(required = false) String noteId,
            @RequestParam(required = false) String message,
            @RequestParam(required = false) @ApiParam("关联业务/事件ID") String uuid
    ) {
        WOCase woCase = apiService.getCaseById(caseId).orElseThrow(
                () -> new KbException(KbCode.RESOURCE_NOT_FOUND).r("案件不存在").li());
        WOEvent event = operator.userRate(woCase, userId, WOIdentity.CLIENT.asTeam(), rating, noteId, message, uuid);
        return kbRes.ok(apiService.event2model(event));
    }

    @ApiOperation("删除案件(软删除)")
    @PostMapping("/usr/client/deleteCase")
    public KbEntity<WOEventModel> deleteCase(
            @RequestHeader(KbHeader.KB_USER_ID) @KbUserId Integer userId,
            @RequestParam() String caseId
    ) {
        WOCase woCase = apiService.getCaseById(caseId).orElseThrow(
                () -> new KbException(KbCode.RESOURCE_NOT_FOUND).r("案件不存在").li());
        if (userId.equals(woCase.getClientUserId())) {
            if (woCase.getAcl() == null) {
                operator.updateCaseAcl(caseId, Collections.singletonList(
                        new WOAccess(false, false).addWho(WOAccess.Who.byUser(userId))));
            } else {
                List<WOAccess> acl = new LinkedList<>(woCase.getAcl());
                operator.updateCaseAcl(caseId, new WOAccess.AclBuilder(acl)
                        .addAccess(new WOAccess(false, false).addWho(WOAccess.Who.byUser(userId)))
                        .create());
            }
            return kbRes.ok();
        } else {
            return kbRes.err(KbCode.AUTH_NOT_OWNER, "not case client", "没有删除权限");
        }
    }

    @ApiOperation("获取我发起的案件列表")
    @PostMapping("/usr/client/getCaseListBySelf")
    public KbEntity<List<WOCaseModel<Object>>> getCaseListBySelf(
            @RequestHeader(KbHeader.KB_USER_ID) @KbUserId Integer userId,
            @RequestParam() String biz,
            @RequestParam(required = false, defaultValue = "1") @KbPage Integer page,
            @RequestParam(required = false, defaultValue = "20") @KbPageSize Integer pageSize
    ) {
        WOCaseListQueryModel query = new WOCaseListQueryModel();
        query.setTeam(WOIdentity.CLIENT.asTeam());
        query.setClientUserId(userId);
        query.setBiz(biz);
        query.setFetchTags(true);
        query.setPage(page);
        query.setPageSize(pageSize);
        WOTeamContext teamContext = teamService.getContextByTeamOrThrow(biz, WOIdentity.CLIENT.asTeam());
        Page<WOCaseModel<Object>> casePage = apiService.getCasePageByQuery(query)
                .map(woCase -> apiService.case2model(woCase, userId, teamContext.getTeam()));
        List<WOCaseModel<Object>> caseList = apiService.attachContent(casePage.getContent());
        List<String> lastEventIds = caseList.stream()
                .map(WOCaseModel::getLastEventId).filter(Objects::nonNull).collect(Collectors.toList());
        Map<String, WOEventModel> eventMap = apiService.getEventListByIdIn(lastEventIds).stream()
                .map(event -> Mapper.map(event, WOEventModel.class))
                .collect(Collectors.toMap(WOEventModel::getId, e -> e));
        caseList.forEach(model -> model.setLastEvent(eventMap.get(model.getLastEventId())));
        return kbRes.ok(caseList).setPageInfo(casePage);
    }

    @ApiOperation("根据手机号获取待认领的案件数量")
    @PostMapping("/usr/client/getNoOwnerCaseCountByMobile")
    public KbEntity<Long> getNoOwnerCaseCountByMobile(
            @RequestHeader(KbHeader.KB_USER_ID) @KbUserId Integer userId,
            @RequestParam() String biz
    ) {
        return userService.getMobileById(userId)
                .map(mobile -> apiService.getNoOwnerCaseCountByBizAndMobile(biz, mobile))
                .dataOrElse(() -> 0L);
    }

    @ApiOperation("根据手机号认领案件")
    @KbCheckSignature(type = KbSignType.ACCOUNT)
    @PostMapping("/usr/client/attachNoOwnerCaseClientByMobile")
    public KbEntity<Void> attachNoOwnerCaseClientByMobile(
            @RequestHeader(KbHeader.KB_USER_ID) @KbUserId Integer userId,
            @RequestParam() String biz,
            @RequestParam() String mobile
    ) {
        Optional<String> op = userService.getMobileById(userId).data();
        if (op.isPresent()) {
            String userMobile = op.get();
            if (userMobile.equals(mobile)) {
                operator.attachNoOwnerCaseClientByMobile(biz, userMobile, userId);
                return kbRes.ok();
            } else {
                return kbRes.err(KbCode.AUTH_NOT_OWNER);
            }
        } else {
            return kbRes.err(KbCode.USER_NOT_BIND_MOBILE);
        }
    }

    // ------------------------------------------------------------

    @ApiOperation("关注案件时填写的手机验证码获取")
    @KbCheckSignature(type = KbSignType.ACCOUNT)
    @PostMapping("/usr/sendFollowerMobileVCode")
    public KbEntity<Void> sendFollowerMobileVCode(
            @RequestHeader(KbHeader.KB_USER_ID) @KbUserId Integer userId,
            @RequestParam(required = false) String mobile
    ) {
        return memberAccountService.sendSMSCode(userId, mobile, SmsCodeMark.WORK_ORDER_FOLLOWER_MOBILE);
    }

    @ApiOperation("获取案件的关注者")
    @PostMapping("/obj/getFollowerListByCase")
    public KbEntity<List<WOFollowerModel>> getFollowerListByCase(
            @RequestParam() String caseId,
            @RequestParam(required = false, defaultValue = "1") @KbPage Integer page,
            @RequestParam(required = false, defaultValue = "20") @KbPageSize Integer pageSize
    ) {
        Page<WOFollower> followerPage = teamService.getFollowerPageByCaseId(caseId, page, pageSize);
        return kbRes.ok(apiService.follower2modelPage(followerPage));
    }

    @ApiOperation("关注案件")
    @PostMapping("/usr/followCase")
    public KbEntity<Void> followCase(
            @RequestHeader(KbHeader.KB_USER_ID) @KbUserId Integer userId,
            @RequestParam() String caseId,
            @RequestParam(required = false) String name,
            @RequestParam(required = false) String mobile,
            @RequestParam(required = false) String message,
            @RequestParam(required = false) String vcode
    ) {
        WOCase woCase = apiService.getCaseById(caseId)
                        .orElseThrow(() -> new KbException(KbCode.RESOURCE_NOT_FOUND).r("案件不存在"));
        List<WOACLStringData> dataList = null;
        if (name != null || mobile != null) {
            dataList = new LinkedList<>();
            if (name != null) {
                dataList.add(new WOACLStringData("name", "姓名", name, new WOAccess.AclBuilder()
                        .addAccess(new WOAccess(true, true)
                                .addWho(WOAccess.Who.byUser(userId))
                                .addWho(WOAccess.Who.byCaseManager()))
                        .create()));
            }
            if (mobile != null) {
                if (!memberAccountService.verifyVCode(userId, mobile, SmsCodeMark.WORK_ORDER_FOLLOWER_MOBILE, vcode)) {
                    return kbRes.err(KbCode.USER_WRONG_VCODE);
                }
                dataList.add(new WOACLStringData("mobile", "电话", mobile, new WOAccess.AclBuilder()
                        .addAccess(new WOAccess(true, true)
                                .addWho(WOAccess.Who.byUser(userId))
                                .addWho(WOAccess.Who.byCaseManager()))
                        .create()));
            }
        }
        teamService.followCase(caseId, woCase.getBiz(), userId, message, dataList);
        return kbRes.ok();
    }

    @ApiOperation("取消关注案件")
    @PostMapping("/usr/unfollowCase")
    public KbEntity<Void> unfollowCase(
            @RequestHeader(KbHeader.KB_USER_ID) @KbUserId Integer userId,
            @RequestParam() String caseId
    ) {
        teamService.unfollowCase(caseId, userId);
        return kbRes.ok();
    }

    @ApiOperation("获取用户对案件的关注信息")
    @PostMapping("/usr/getSelfCaseFollower")
    public KbEntity<WOFollowerModel> getSelfCaseFollower(
            @RequestHeader(KbHeader.KB_USER_ID) @KbUserId Integer userId,
            @RequestParam() String caseId
    ) {
        return teamService.getFollowerByCaseIdAndUserId(caseId, userId)
                .filter(follower -> follower.getUserId().equals(userId))
                .map(follower -> Mapper.map(follower, WOFollowerModel.class))
                .map(kbRes::ok)
                .orElseGet(kbRes::ok);
    }

    @ApiOperation("获取关注的案件列表")
    @PostMapping("/usr/getFollowedCaseListByBiz")
    public KbEntity<List<WOCaseModel<Object>>> getFollowedCaseListByBiz(
            @RequestHeader(KbHeader.KB_USER_ID) @KbUserId Integer userId,
            @RequestParam() String biz,
            @RequestParam(required = false, defaultValue = "1") @KbPage Integer page,
            @RequestParam(required = false, defaultValue = "20") @KbPageSize Integer pageSize
    ) {
        WOTeamContext team = teamService.getContextByTeamOrThrow(biz, WOIdentity.FOLLOWER.asTeam());
        Page<String> caseIdPage = teamService.getFollowedCaseListByUserIdAndBiz(userId, biz, page, pageSize);
        List<WOCase> caseList = apiService.getCaseListByIdIn(caseIdPage.toList());
        Page<WOCase> casePage = new PageImpl<>(caseList, caseIdPage.getPageable(), caseIdPage.getTotalElements());
        Page<WOCaseModel<Object>> modelPage =
                casePage.map(woCase -> apiService.case2model(woCase, userId, team.getTeam()));
        return kbRes.ok(apiService.attachContent(modelPage.getContent())).setPageInfo(casePage);
    }

    @ApiOperation("获取关注的案件列表")
    @PostMapping("/usr/getFollowedCaseBriefListByBiz")
    public KbEntity<List<WOCaseModel<WOCaseContentBrief>>> getFollowedCaseBriefListByBiz(
            @RequestHeader(KbHeader.KB_USER_ID) @KbUserId Integer userId,
            @RequestParam() String biz,
            @RequestParam(required = false, defaultValue = "1") @KbPage Integer page,
            @RequestParam(required = false, defaultValue = "20") @KbPageSize Integer pageSize
    ) {
        WOTeamContext team = teamService.getContextByTeamOrThrow(biz, WOIdentity.FOLLOWER.asTeam());
        Page<String> caseIdPage = teamService.getFollowedCaseListByUserIdAndBiz(userId, biz, page, pageSize);
        List<WOCase> caseList = apiService.getCaseListByIdIn(caseIdPage.toList());
        Page<WOCase> casePage = new PageImpl<>(caseList, caseIdPage.getPageable(), caseIdPage.getTotalElements());
        Page<WOCaseModel<WOCaseContentBrief>> modelPage =
                casePage.map(woCase -> apiService.case2model(woCase, userId, team.getTeam()));
        return kbRes.ok(apiService.attachBrief(modelPage.getContent())).setPageInfo(casePage);
    }

    @ApiOperation("获取案件")
    @PostMapping("/ifu/getCaseById")
    public KbEntity<WOCaseModel<Object>> getCaseById(
            @RequestHeader(value = KbHeader.KB_USER_ID, required = false) @KbUserId Integer userId,
            @RequestParam() String caseId
    ) {
        WOCase woCase = apiService.getCaseById(caseId)
                .orElseThrow(() -> new KbException(KbCode.RESOURCE_NOT_FOUND).li());
        String userTeam;
        if (woCase.getAcl() == null || woCase.getAcl().size() == 0) {
            userTeam = WOIdentity.VIEWER.asTeam();
        } else {
            userTeam = teamService.determineUserTeamByCase(woCase, userId);
        }
        WOTeamContext teamContext = teamService.getContextByTeamOrThrow(woCase.getBiz(), userTeam);
        WOCaseModel<Object> model = apiService.case2model(woCase, userId, teamContext.getTeam());
        return kbRes.ok(apiService.attachContent(model));
    }

    @ApiOperation("获取案件, 内容以摘要形式给出")
    @PostMapping("/ifu/getCaseBriefById")
    public KbEntity<WOCaseModel<WOCaseContentBrief>> getCaseBriefById(
            @RequestHeader(value = KbHeader.KB_USER_ID, required = false) @KbUserId Integer userId,
            @RequestParam() String caseId
    ) {
        WOCase woCase = apiService.getCaseById(caseId)
                .orElseThrow(() -> new KbException(KbCode.RESOURCE_NOT_FOUND).li());
        String userTeam;
        if (woCase.getAcl() == null || woCase.getAcl().size() == 0) {
            userTeam = teamService.determineUserTeamByCase(woCase, userId);
        } else {
            userTeam = WOIdentity.VIEWER.asTeam();
        }
        WOTeamContext teamContext = teamService.getContextByTeamOrThrow(woCase.getBiz(), userTeam);
        WOCaseModel<WOCaseContentBrief> model = apiService.case2model(woCase, userId, teamContext.getTeam());
        return kbRes.ok(apiService.attachBrief(model));
    }

    @ApiOperation("获取案件的事件列表")
    @PostMapping("/ifu/getEventListByCase")
    public KbEntity<List<WOEventModel>> getEventListByCase(
            @RequestHeader(value = KbHeader.KB_USER_ID, required = false) @KbUserId Integer userId,
            @RequestParam() String caseId,
            @RequestParam(required = false, defaultValue = "1") @KbPage Integer page,
            @RequestParam(required = false, defaultValue = "20") @KbPageSize Integer pageSize
    ) {
        WOCase woCase = apiService.getCaseById(caseId)
                .orElseThrow(() -> new KbException(KbCode.RESOURCE_NOT_FOUND).li());
        String userTeam;
        if (woCase.getAcl() == null || woCase.getAcl().size() == 0) {
            userTeam = teamService.determineUserTeamByCase(woCase, userId);
        } else {
            userTeam = WOIdentity.VIEWER.asTeam();
        }
        WOTeam team = teamService.getContextByTeamOrThrow(woCase.getBiz(), userTeam).getTeam();
        List<WOEvent> eventList = apiService.getEventListByCase(caseId, page, pageSize);
        return kbRes.ok(apiService.event2modelList(eventList, userId, team, woCase));
    }

    // --------------------------------------------------

}
