package com.kaiba.m.core.controller.da.eventtrack;

import com.kaiba.lib.base.assembler.Mapper;
import com.kaiba.lib.base.domain.da.eventtrack.manual.DocManual;
import com.kaiba.lib.base.middleware.KbRes;
import com.kaiba.lib.base.response.KbCode;
import com.kaiba.lib.base.response.KbEntity;
import com.kaiba.lib.base.response.KbException;
import com.kaiba.m.core.constant.da.eventtrack.ETDocPushType;
import com.kaiba.m.core.constant.da.eventtrack.ETDocSyncStrategyType;
import com.kaiba.m.core.domain.da.eventtrack.ETDoc;
import com.kaiba.m.core.domain.da.eventtrack.ETDocDepart;
import com.kaiba.m.core.domain.da.eventtrack.ETDocExclude;
import com.kaiba.m.core.domain.da.eventtrack.ETDocModule;
import com.kaiba.m.core.domain.da.eventtrack.ETDocModuleArticleRule;
import com.kaiba.m.core.model.da.eventtrack.ETDocDepartModel;
import com.kaiba.m.core.model.da.eventtrack.ETDocExcludeModel;
import com.kaiba.m.core.model.da.eventtrack.ETDocModel;
import com.kaiba.m.core.model.da.eventtrack.ETDocModuleArticleRuleModel;
import com.kaiba.m.core.model.da.eventtrack.ETDocModuleModel;
import com.kaiba.m.core.service.da.eventtrack.ETDocService;
import com.kaiba.m.core.service.da.eventtrack.ETDocSyncService;
import com.kaiba.m.core.service.da.eventtrack.push.ETDocPushService;
import com.kaiba.m.core.model.da.eventtrack.ETDocQueryModel;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @version ETDocAdminController, v0.1 2025/4/3 15:18 daopei Exp $
 **/
@Slf4j
@Validated
@RestController
@RequestMapping("/eventTrack/etDoc/admin")
public class ETDocAdminController {

    private final KbRes kbRes;

    private final ETDocService etDocService;
    private final ETDocPushService pushService;
    private final ETDocSyncService syncService;

    public ETDocAdminController(
            KbRes kbRes,
            ETDocService etDocService,
            ETDocPushService pushService,
            ETDocSyncService syncService
    ) {
        this.kbRes = kbRes;
        this.etDocService = etDocService;
        this.pushService = pushService;
        this.syncService = syncService;
    }

    // 稿件定义文档 ---------------------------------------
    @ApiOperation("稿件定义文档查询")
    @PostMapping("/doc/docDefine")
    public KbEntity<String> getDocDefine() {
        return kbRes.ok(DocManual.toMarkdown());
    }


    // 标准稿件标签 ---------------------------------------

    @ApiOperation("稿件标签查询")
    @PostMapping("/module/list")
    public KbEntity<List<ETDocModuleModel>> getModuleList(
            @RequestParam(required = false) String module,
            @RequestParam(required = false) String name,
            @RequestParam(required = false) String category,
            @RequestParam(required = false, defaultValue = "1") Integer page,
            @RequestParam(required = false, defaultValue = "20") Integer pageSize
    ) {
        return kbRes.ok(
                etDocService.getModulePage(module, name, category, page, pageSize)
                .map(m -> Mapper.map(m, ETDocModuleModel.class)));
    }

    @ApiOperation("稿件标签新增")
    @PostMapping("/module/upsert")
    public KbEntity<ETDocModuleModel> upsertModule(
            @RequestBody ETDocModuleModel module
    ) {
        ETDocModule m = etDocService.upsertModule(Mapper.map(module, ETDocModule.class));
        if (m == null) {
            return kbRes.ok();
        } else {
            return kbRes.ok(Mapper.map(m, ETDocModuleModel.class));
        }
    }

    // 标准稿件部门 ---------------------------------------

    @ApiOperation("稿件部门查询")
    @PostMapping("/depart/list")
    public KbEntity<List<ETDocDepartModel>> getDepartList(
            @RequestParam(required = false) String channel,
            @RequestParam(required = false) List<String> departList,
            @RequestParam(required = false) String name,
            @RequestParam(required = false, defaultValue = "1") Integer page,
            @RequestParam(required = false, defaultValue = "20") Integer pageSize
    ) {
        return kbRes.ok(
                etDocService.getDepartPage(channel, name, departList, page, pageSize)
                        .map(m -> Mapper.map(m, ETDocDepartModel.class)));
    }

    @ApiOperation("稿件部门新增")
    @PostMapping("/depart/upsert")
    public KbEntity<ETDocDepartModel> upsertDepart(
            @RequestBody ETDocDepartModel depart
    ) {
       return kbRes.ok(Mapper.map(etDocService.upsertDepart(Mapper.map(depart, ETDocDepart.class)), ETDocDepartModel.class));
    }

    // 标准稿件黑名单 ---------------------------------------

    @ApiOperation("稿件部门查询")
    @PostMapping("/docExclude/list")
    public KbEntity<List<ETDocExcludeModel>> getDocExcludeList(
            @RequestBody ETDocQueryModel query
    ) {
        return kbRes.ok(etDocService.getExcludePage(query).map(doc -> Mapper.map(doc, ETDocExcludeModel.class)));
    }

    @ApiOperation("稿件部门新增")
    @PostMapping("/docExclude/upsert")
    public KbEntity<ETDocExcludeModel> upsertDepart(
            @RequestBody ETDocExcludeModel depart
    ) {
        return kbRes.ok(Mapper.map(etDocService.upsertDocExclude(Mapper.map(depart, ETDocExclude.class)), ETDocExcludeModel.class));
    }

    // 标准稿件 ---------------------------------------

    @ApiOperation("稿件查询")
    @PostMapping("/doc/list")
    public KbEntity<List<ETDocModel>> getDocList(
            @RequestBody ETDocQueryModel query
    ) {
        return kbRes.ok(etDocService.getDocPage(query).map(doc -> Mapper.map(doc, ETDocModel.class)));
    }

    @ApiOperation("稿件单个查询")
    @PostMapping("/doc/getById")
    public KbEntity<ETDocModel> getDocById(
            @RequestParam String docId
    ) {
        return etDocService.getDocById(docId)
                .map(doc -> Mapper.map(doc, ETDocModel.class))
                .map(kbRes::ok)
                .orElse(kbRes.err(KbCode.RESOURCE_NOT_FOUND));
    }


    @ApiOperation("稿件新增")
    @PostMapping("/doc/upsert")
    public KbEntity<ETDocModel> upsertDoc(
            @RequestBody ETDocModel doc
    ) {
        ETDoc newDoc = etDocService.upsertDoc(Mapper.map(doc, ETDoc.class));
        return kbRes.ok(Mapper.map(newDoc, ETDocModel.class));
    }

    @ApiOperation("稿件复写状态变更")
    @PostMapping("/doc/updateOVR")
    public KbEntity<ETDocModel> updateDocOVR(
            @RequestParam String docId,
            @RequestParam Boolean ovr
    ) {
        ETDoc newDoc = etDocService.updateDocOVR(docId, ovr);
        return kbRes.ok(Mapper.map(newDoc, ETDocModel.class));
    }

    @ApiOperation("稿件业务创建时间修改")
    @PostMapping("/doc/updateDocCreateTime")
    public KbEntity<ETDocModel> updateDocCreateTime(
            @RequestParam String docId,
            @RequestParam(required = false) Long docCreateTime
    ) {
        ETDoc newDoc = etDocService.updateDocCreateTime(docId, docCreateTime);
        return kbRes.ok(Mapper.map(newDoc, ETDocModel.class));
    }

    @ApiOperation("稿件channel更新, 复写状态打开")
    @PostMapping("/doc/upsertDocChannel")
    public KbEntity<ETDocModel> upsertDocChannel(
            @RequestParam String docId,
            @RequestParam(required = false) String channel
    ) {
        ETDoc newDoc = etDocService.updateDocChannel(docId, channel);
        return kbRes.ok(Mapper.map(newDoc, ETDocModel.class));
    }

    //标准稿件模块文章规则 ---------------------------------------
    @ApiOperation("文章稿件标签关联查询")
    @PostMapping("/module/rule/article/list")
    public KbEntity<List<ETDocModuleArticleRuleModel>> getArticleRuleList(
            @RequestParam(required = false) String groupKey,
            @RequestParam(required = false, defaultValue = "1") Integer page,
            @RequestParam(required = false, defaultValue = "20") Integer pageSize
    ) {
        return kbRes.ok(etDocService.getModuleArticleRulePage(groupKey, page, pageSize)
                .map(rule -> Mapper.map(rule, ETDocModuleArticleRuleModel.class)));
    }

    @ApiOperation("文章稿件标签关联插入")
    @PostMapping("/module/rule/article/upsert")
    public KbEntity<ETDocModuleArticleRuleModel> moduleArticleRuleUpsert(
            @RequestBody ETDocModuleArticleRuleModel rule
    ) {
        return kbRes.ok(Mapper.map(etDocService.upsertModuleArticleRule(
                Mapper.map(rule, ETDocModuleArticleRule.class)), ETDocModuleArticleRuleModel.class)
        );
    }

    @ApiOperation("文章稿件标签关联删除")
    @PostMapping("/module/rule/article/delete")
    public KbEntity<Void> moduleArticleRuleDelete(
            @RequestParam String groupKey
    ) {
        etDocService.deleteModuleArticleRule(groupKey);
        return kbRes.ok();
    }


    // 稿件推送功能 -------------------------------------

    @ApiOperation("稿件推送个推任务触发")
    @PostMapping("/push/pushByType")
    public KbEntity<Void> pushByType(
            @RequestParam String pushType
    ) {
        ETDocPushType type = ETDocPushType.resolverByName(pushType)
                .orElseThrow(() -> new KbException(KbCode.ILLEGAL_ARGUMENT, "pushType should not be null").li());
        pushService.pushWithLock(type);
        return kbRes.ok();
    }

    @ApiOperation("稿件推送个推任务状态检查")
    @PostMapping("/push/stateCheck")
    public KbEntity<Boolean> pushStateCheck(
            @RequestParam String pushType
    ) {
        ETDocPushType type = ETDocPushType.resolverByName(pushType)
                .orElseThrow(() -> new KbException(KbCode.ILLEGAL_ARGUMENT, "pushType should not be null").li());
        return kbRes.ok(pushService.checkPushTypeLock(type));
    }

    // 稿件同步功能 -------------------------------------

    @ApiOperation("稿件按照策略同步任务触发")
    @PostMapping("/sync/syncByStrategy")
    public KbEntity<Void> syncByStrategy(
            @RequestParam @ApiParam("稿件同步策略类型. ETDocSyncStrategyType") String strategy,
            @RequestParam Long startTime,
            @RequestParam Long endTime
    ) {
        syncService.syncStrategyByTime(strategy, startTime, endTime);
        return kbRes.ok();
    }

    @ApiOperation("稿件按照策略同步状态查询")
    @PostMapping("/sync/stateCheck")
    public KbEntity<Boolean> syncStateCheck(
            @RequestParam String strategy
    ) {
        ETDocSyncStrategyType.resolverByName(strategy)
                .orElseThrow(() -> new KbException(KbCode.ILLEGAL_ARGUMENT, "strategy should not be null").li());
        return kbRes.ok(syncService.checkSyncLock(strategy));
    }



}
