package com.kaiba.m.core.controller.user;

import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @date 2023/06/10 16:20
 **/
public class WeakPwdUtils {

    /** 匹配1个大写字母, 1个小写字母, 1个数字, 1个特殊字符, 8-16位 */
    private static final String PATTERN_MATCHES = "^(?=.*?[A-Z])(?=.*?[a-z])(?=.*?[0-9])(?=.*?[~!@#$%^&*_\\-+=:;.]).{8,16}$";

    /** 弱密码检测 */
    public static boolean isPasswordOk(String password) {
        return Pattern.compile(PATTERN_MATCHES).matcher(password).matches();
    }
}
