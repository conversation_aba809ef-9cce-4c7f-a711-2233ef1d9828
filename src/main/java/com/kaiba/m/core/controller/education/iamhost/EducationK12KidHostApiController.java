package com.kaiba.m.core.controller.education.iamhost;

import com.kaiba.lib.base.domain.education.iamhost.KidHostModel;
import com.kaiba.lib.base.middleware.KbRes;
import com.kaiba.lib.base.response.KbEntity;
import com.kaiba.m.core.model.education.KidBaseModel;
import com.kaiba.m.core.service.education.iamhost.EducationKidHostService;

import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/03/25 09:53
 */
@RestController
@RequestMapping("/education/k12/kidHost")
public class EducationK12KidHostApiController {

    private final KbRes kbRes;
    private final EducationKidHostService hostService;

    public EducationK12KidHostApiController(
            Kb<PERSON><PERSON> kbRes,
            EducationKidHostService hostService
    ) {
        this.kbRes = kbRes;
        this.hostService = hostService;
    }

    @PostMapping("/obj/searchKid")
    public KbEntity<List<KidBaseModel>> searchKid(
            @RequestParam(required = false) String queryParam,
            @RequestParam(required = false, defaultValue = "1") Integer page,
            @RequestParam(required = false, defaultValue = "20") Integer pageSize
    ) {
        return kbRes.ok(hostService.searchKid(queryParam, page, pageSize));
    }

    @PostMapping("/obj/searchKidBySchoolName")
    public KbEntity<List<KidBaseModel>> searchKidBySchoolName(
        @RequestParam String queryParam,
        @RequestParam(required = false, defaultValue = "1") Integer page,
        @RequestParam(required = false, defaultValue = "20") Integer pageSize
    ) {
        return kbRes.ok(hostService.searchKidBySchoolName(queryParam, page, pageSize));
    }

    @PostMapping("/obj/getKidHostByKidId")
    public KbEntity<KidHostModel> getKidHostByKidId(
        @RequestParam String kidId
    ) {
        return kbRes.ok(hostService.getKidHostByKidIdFromCache(kidId));
    }
}
