package com.kaiba.m.core.controller.app.cyx;

import com.kaiba.lib.base.middleware.KbRes;
import com.kaiba.lib.base.response.KbEntity;
import com.kaiba.m.core.model.appcyx.CYXAppHomeModel;
import com.kaiba.m.core.model.appcyx.CYXArticleGroupModel;
import com.kaiba.m.core.model.appcyx.CYXArticleGroupState;
import com.kaiba.m.core.service.appcyx.CYXAppHomeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 超有戏 客户端首页
 * <AUTHOR>
 * @version CYXAppHomeApiController, v0.1 2025/6/23 10:17 daopei Exp $
 **/
@Slf4j
@RestController
@RequestMapping("/api/app/home/<USER>")
public class CYXAppHomeApiController {

    private final KbRes kbRes;
    private final CYXAppHomeService cyxAppHomeService;

    public CYXAppHomeApiController(
            KbRes kbRes,
            CYXAppHomeService cyxAppHomeService
    ) {
        this.kbRes = kbRes;
        this.cyxAppHomeService = cyxAppHomeService;
    }


    @PostMapping("/getHome")
    public KbEntity<CYXAppHomeModel> getHome() {
        return kbRes.ok(cyxAppHomeService.getHomeFromCache());
    }

    @PostMapping("/getGroupList")
    public KbEntity<List<CYXArticleGroupModel>> getGroupList(
            @RequestParam String groupType
    ) {
        return kbRes.ok(cyxAppHomeService.getGroupList(CYXArticleGroupState.SHOW.name(), groupType, 1, 100));
    }
}
