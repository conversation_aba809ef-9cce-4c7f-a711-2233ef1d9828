package com.kaiba.m.core.controller.app.widgeticongrid;

import com.kaiba.lib.base.constant.appwidget.WidgetItemState;
import com.kaiba.lib.base.constant.appwidget.WidgetOrder;
import com.kaiba.lib.base.domain.appwidget.icongrid.IconGridDataCreateModel;
import com.kaiba.lib.base.domain.appwidget.icongrid.IconGridDataModel;
import com.kaiba.lib.base.domain.appwidget.icongrid.IconGridDataUpdateModel;
import com.kaiba.lib.base.domain.appwidget.icongrid.IconGridIdxUpdateModel;
import com.kaiba.lib.base.domain.appwidget.icongrid.IconGridInstanceModel;
import com.kaiba.lib.base.middleware.KbRes;
import com.kaiba.lib.base.response.KbCode;
import com.kaiba.lib.base.response.KbEntity;
import com.kaiba.lib.base.response.KbException;
import com.kaiba.lib.base.service.IAppIconGridService;
import com.kaiba.m.core.domain.appwidget.icongrid.IconGridInstance;
import com.kaiba.m.core.service.appcomponent.icongrid.AppIconGridCacheModel;
import com.kaiba.m.core.service.appcomponent.icongrid.AppIconGridCacheService;
import com.kaiba.m.core.service.appcomponent.icongrid.AppIconGridModelHelper;
import com.kaiba.m.core.service.appcomponent.icongrid.AppIconGridService;
import com.kaiba.m.core.util.PageUtils;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Sort;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @version AppIconGridServiceController, v0.1 2023/7/17 17:00 daopei Exp $
 **/
@RestController
@Slf4j
@RequiredArgsConstructor
public class AppIconGridServiceController implements IAppIconGridService {

    @NonNull
    private KbRes kbRes;
    @NonNull
    private AppIconGridService appIconGridService;
    @NonNull
    private AppIconGridCacheService appIconGridCacheService;


    @Override
    public KbEntity<IconGridInstanceModel> createInstance(IconGridInstanceModel model) {
        return kbRes.ok(AppIconGridModelHelper.instance2Model(appIconGridService.createInstance(model)));
    }

    @Override
    public KbEntity<Void> updateInstance(IconGridInstanceModel model) {
        appIconGridService.updateInstance(model);
        appIconGridCacheService.invalidate(model.getId());
        return kbRes.ok();
    }

    @Override
    public KbEntity<Void> deleteInstance(String instanceId) {
        if (instanceId == null) {
            throw new KbException(KbCode.REQUEST_PARAM_MISSING);
        }
        appIconGridService.deleteInstance(instanceId);
        appIconGridCacheService.invalidate(instanceId);
        return kbRes.ok();
    }

    @Override
    public KbEntity<IconGridInstanceModel> getInstance(Integer siteId, String instanceId, String instanceKey) {
        if (instanceId == null && instanceKey == null) {
            throw new KbException(KbCode.REQUEST_PARAM_MISSING);
        }
        return appIconGridService.getInstance(siteId, instanceId, instanceKey)
                .map(AppIconGridModelHelper::instance2Model)
                .map(kbRes::ok)
                .orElseThrow(() -> new KbException(KbCode.RESOURCE_NOT_FOUND).r("实例数据未找到").li());
    }

    @Override
    public KbEntity<List<IconGridInstanceModel>> getInstanceList(Integer siteId, Integer backendFilterType, Integer page, Integer pageSize) {
        Page<IconGridInstance> pageResult = appIconGridService.getInstanceList(siteId, backendFilterType, page, pageSize);
        return kbRes.ok(pageResult.map(AppIconGridModelHelper::instance2Model));
    }

    @Override
    public KbEntity<IconGridDataModel> createData(IconGridDataCreateModel model) {
        return kbRes.ok(
                AppIconGridModelHelper.data2Model(appIconGridService.createData(model)));
    }

    @Override
    public KbEntity<IconGridDataModel> updateData(IconGridDataUpdateModel model) {
        return kbRes.ok(
                AppIconGridModelHelper.data2Model(appIconGridService.updateData(model)));
    }

    @Override
    public KbEntity<Void> deleteData(String dataId) {
        appIconGridService.deleteData(dataId);
        return kbRes.ok();
    }

    @Override
    public KbEntity<Void> updateIdx(IconGridIdxUpdateModel idxUpdateModel) {
        appIconGridService.updateIdx(idxUpdateModel);
        return kbRes.ok();
    }

    @Override
    public KbEntity<IconGridDataModel> updateState(String dataId, Integer state) {
        return kbRes.ok(
                AppIconGridModelHelper.data2Model(
                        appIconGridService.updateState(dataId, WidgetItemState.resolveByValue(state).get())));
    }

    @Override
    public KbEntity<IconGridDataModel> updateScheduleTime(String dataId, Long scheduledStartTime, Long scheduledEndTime) {
        return kbRes.ok(
                AppIconGridModelHelper.data2Model(
                        appIconGridService.updateScheduleTime(dataId, scheduledStartTime, scheduledEndTime))
        );
    }

    @Override
    public KbEntity<List<IconGridDataModel>> getByInstance(Integer siteId, String instanceId, String instanceKey, Integer state, WidgetOrder order, Integer page, Integer pageSize) {
        return kbRes.ok(
                appIconGridService.getByInstance(siteId, instanceId, instanceKey, state,
                        PageUtils.ofDefault(page, pageSize, order == null ? null : order.getSort()))
                        .map(AppIconGridModelHelper::data2Model));
    }

    @Override
    public KbEntity<IconGridInstanceModel> getCachedInstance(Integer siteId, String instanceId, String instanceKey) {
        AppIconGridCacheModel appIconGridCacheModel = appIconGridCacheService.getInstanceFromCache(siteId, instanceId, instanceKey);
        return kbRes.ok(appIconGridCacheModel.getInstance());
    }


}
