package com.kaiba.m.core.controller.user;

import com.kaiba.lib.base.constant.KbHeader;
import com.kaiba.lib.base.middleware.KbRes;
import com.kaiba.lib.base.response.KbEntity;
import com.kaiba.m.core.model.user.UserAuthModel;
import com.kaiba.m.core.service.user.auth.UserAuthService;

import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2024/06/04 14:20
 **/
@Slf4j
@RestController
@RequestMapping("/user/auth/admin")
public class UserAuthAdminController {

    private final KbRes kbRes;
    private final UserAuthService userAuthService;

    public UserAuthAdminController(KbRes kbRes, UserAuthService userAuthService) {
        this.kbRes = kbRes;
        this.userAuthService = userAuthService;
    }

    @ApiOperation("认证用户")
    @PostMapping(path = "/authUser")
    public KbEntity<UserAuthModel> authUser(
            @RequestHeader(value = KbHeader.KB_USER_ID) Integer userId,
            @RequestParam Integer targetUserId,
            @RequestParam String userName,
            @RequestParam String content
    ) {
        userAuthService.authUser(targetUserId, userId, userName, content);
        return kbRes.ok();
    }

    @ApiOperation("取消认证")
    @PostMapping(path = "/cancelAuth")
    public KbEntity<UserAuthModel> cancelAuth(
            @RequestHeader(value = KbHeader.KB_USER_ID) Integer userId,
            @RequestParam Integer targetUserId
    ) {
        log.info("user auth canceled by : {}, target user id: {}", userId, targetUserId);
        userAuthService.cancelAuth(targetUserId);
        return kbRes.ok();
    }

    @ApiOperation("获取认证用户列表")
    @PostMapping(path = "/getAuthUserList")
    public KbEntity<List<UserAuthModel>> getAuthUserList(
            @RequestHeader(value = KbHeader.KB_SITE_ID) Integer siteId,
            @RequestParam(required = false, defaultValue = "1") Integer page,
            @RequestParam(required = false, defaultValue = "20") Integer pageSize
    ) {
        return kbRes.ok(userAuthService.getUserAuthList(siteId, page, pageSize));
    }
}
