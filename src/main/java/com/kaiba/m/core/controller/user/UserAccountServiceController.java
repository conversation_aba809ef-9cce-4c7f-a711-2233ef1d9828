package com.kaiba.m.core.controller.user;

import cn.binarywang.wx.miniapp.api.WxMaService;
import cn.binarywang.wx.miniapp.bean.WxMaJscode2SessionResult;
import cn.binarywang.wx.miniapp.bean.WxMaPhoneNumberInfo;
import cn.binarywang.wx.miniapp.util.WxMaConfigHolder;
import com.kaiba.lib.base.assembler.Mapper;
import com.kaiba.lib.base.constant.sms.SmsCodeMark;
import com.kaiba.lib.base.constant.user.LoginCMSResultCode;
import com.kaiba.lib.base.constant.user.UserLoginType;
import com.kaiba.lib.base.constant.wx.WXEntity;
import com.kaiba.lib.base.constant.wx.WXEntityEnum;
import com.kaiba.lib.base.domain.tecent.QQUserInfo;
import com.kaiba.lib.base.domain.tecent.WechatAccessToken;
import com.kaiba.lib.base.domain.tecent.WechatCodeSession;
import com.kaiba.lib.base.domain.tecent.WechatUserInfo;
import com.kaiba.lib.base.domain.user.*;
import com.kaiba.lib.base.middleware.KbRes;
import com.kaiba.lib.base.response.KbCode;
import com.kaiba.lib.base.response.KbEntity;
import com.kaiba.lib.base.response.KbException;
import com.kaiba.lib.base.service.IAuthService;
import com.kaiba.lib.base.service.ISmsService;
import com.kaiba.lib.base.service.IUserAccountService;
import com.kaiba.lib.base.util.GsonUtils;
import com.kaiba.lib.base.util.ServletRequestUtils;
import com.kaiba.lib.base.util.StringUtils;
import com.kaiba.m.core.domain.user.User;
import com.kaiba.m.core.domain.user.UserLogoff;
import com.kaiba.m.core.service.user.AliAcsChecker;
import com.kaiba.m.core.service.user.UserBasicCacheService;
import com.kaiba.m.core.service.user.UserLoginCacheService;
import com.kaiba.m.core.service.user.UserService;
import com.kaiba.m.core.service.wx.WxOpenUserService;
import com.kaiba.m.core.util.JsonUtils;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxErrorException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.web.client.RestTemplateBuilder;
import org.springframework.http.converter.StringHttpMessageConverter;
import org.springframework.util.Base64Utils;
import org.springframework.util.DigestUtils;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Optional;

/**
 * author: lyux
 * date: 18-10-3
 */
@Slf4j
@RestController
public class UserAccountServiceController implements IUserAccountService {

    @Value("${wx.open.app_id}")
    private String wxOpenAppId;
    @Value("${wx.open.secret}")
    private String wxOpenSecret;
    private static final String initPsd = "asd123";

    private final KbRes kbRes;
    private final UserService userService;
    private final UserBasicCacheService userBasicCacheService;
    private final UserLoginCacheService userLoginCacheService;
    private final IAuthService authService;
    private final RestTemplate restTemplate;
    private final ISmsService smsService;
    private final AliAcsChecker aliAcsChecker;

    private final WxMaService wxMaService;
    private final WxOpenUserService wxOpenUserService;

    @Autowired
    public UserAccountServiceController(
            KbRes kbRes,
            UserService userService,
            UserBasicCacheService userBasicCacheService,
            UserLoginCacheService userLoginCacheService,
            IAuthService authService,
            ISmsService smsService,
            AliAcsChecker aliAcsChecker,
            WxMaService wxMaService,
            WxOpenUserService wxOpenUserService
    ) {
        this.kbRes = kbRes;
        this.userService = userService;
        this.userBasicCacheService = userBasicCacheService;
        this.userLoginCacheService = userLoginCacheService;
        this.authService = authService;
        this.smsService = smsService;
        this.aliAcsChecker = aliAcsChecker;
        this.wxMaService = wxMaService;
        this.wxOpenUserService = wxOpenUserService;

        StringHttpMessageConverter converter = new StringHttpMessageConverter(StandardCharsets.UTF_8);
        this.restTemplate = new RestTemplateBuilder().additionalMessageConverters(converter).build();
    }

    @Override
    public KbEntity<UserModel> getUserBasicById(Integer userId) {
        return userService
                .getDetail(userId)
                .map(UserAccountServiceController::user2basic)
                .map(kbRes::ok)
                .orElse(kbRes.err(KbCode.USER_NOT_EXISTS, "user not exists: " + userId));
    }

    @Override
    public KbEntity<UserModel> getUserBasicByLoginName(String account) {
        return userService
                .getByLoginName(account)
                .map(UserAccountServiceController::user2basic)
                .map(kbRes::ok)
                .orElse(kbRes.err(KbCode.USER_NOT_EXISTS, "user not exists: " + account));
    }

    @Override
    public KbEntity<UserModel> getUserBasicByMobile(String mobile) {
        return userService
                .getByMobile(mobile)
                .map(UserAccountServiceController::user2basic)
                .map(kbRes::ok)
                .orElse(kbRes.err(KbCode.USER_NOT_EXISTS, "user not exists: " + mobile));
    }

    @Override
    public KbEntity<LoginUserModel> getSelfAccountInfo(Integer userId) {
        return userService.getDetail(userId)
                .map(user -> userService.login2model(user, null))
                .map(kbRes::ok)
                .orElse(kbRes.err(KbCode.USER_NOT_EXISTS));
    }

    @Override
    public KbEntity<UserModel> createByAccount(
            String account, String passwordMD5, Integer siteId, String mobile, String name) {
        User newUser = new User();
        newUser.setSiteId(siteId);
        newUser.setUserName(name);
        newUser.setLoginName(account);
        if (StringUtils.isValidMobile(mobile)) {
            newUser.setMobile(mobile);
        }
        if (StringUtils.isEmpty(passwordMD5)){
            newUser.setPassword(DigestUtils.md5DigestAsHex(initPsd.getBytes()));
            newUser.setSetpass(1);
        } else {
            newUser.setPassword(passwordMD5);
        }
        User user = userService.createUser(newUser);
        UserModel model = Mapper.map(user, UserModel.class);
        model.setUserId(user.getId());
        model.setPassword(null);
        return kbRes.ok(model);
    }

    @Override
    public KbEntity<Void> changePasswordByAccount(String account, String passwordMD5) {
        User user = userService.getByLoginName(account).orElseThrow(() -> new KbException(KbCode.USER_NOT_EXISTS).li());
        userService.updatePassword(user.getId(), passwordMD5);
        return kbRes.ok();
    }

    @Override
    public KbEntity<LoginUserModel> loginByPassword(
            String account, String passwordMd5,
            String deviceToken, String deviceNumber) {
        int nextTrySeconds = userLoginCacheService.getNextLoginTryDurationInSecond(account);
        if (nextTrySeconds > 0){
            String hit = "密码尝试次数过多,请在 " + (int)Math.ceil(nextTrySeconds*1.0/60) +" 分钟后重试。";
            return kbRes.err(KbCode.USER_LOGIN_FAIL, "failed too many times", hit);
        }
        Optional<User> optional = userService.getByLoginNameOrMobile(account);
        if (optional.isPresent()) {
            User user = optional.get();
            if (user.getPassword().equals(passwordMd5)){
                return kbRes.ok(userService.doLogin(user, UserLoginType.PASSWORD));
            } else {
                userService.loginFailed(user.getId());
                userLoginCacheService.setLoginFailedAccount(account);
            }
        }
        return kbRes.err(KbCode.USER_LOGIN_WITH_PASSWORD_FAIL);
    }

    @Override
    public KbEntity<LoginUserModel> loginByMobile(String mobile) {
        return userService.getByMobile(mobile)
                .map(user -> userService.doLogin(user, UserLoginType.MOBILE))
                .map(kbRes::ok)
                .orElse(kbRes.err(KbCode.USER_UNREGISTER_CREDENCE));
    }

    @Override
    public KbEntity<LoginUserModel> mobileRegister(
            String mobile, String password, Integer origin,
            Integer siteId, Integer source, String deviceToken, String deviceNumber
    ) {
        User newUser = new User();
        newUser.setMobile(mobile);
        if (StringUtils.isEmpty(password)){
            newUser.setPassword(DigestUtils.md5DigestAsHex(initPsd.getBytes()));
            newUser.setSetpass(1);
        } else {
            newUser.setPassword(password);
        }
        newUser.setOrigin(origin);
        newUser.setSource(source);
        newUser.setSiteId(siteId);
        userService.createUser(newUser);
        LoginUserModel loginUserModel = userService.doLogin(newUser, UserLoginType.MOBILE);
        loginUserModel.setIsNewUser(1);
        return kbRes.ok(loginUserModel);
    }

    @Override
    public KbEntity<LoginUserModel> fillUserInfo(
            Integer userId, String userName, Integer sex, String birthday,
            Integer cityCode, String avatar, String series, Integer origin
    ) {
        User user = new User();
        user.setId(userId);
        user.setUserName(userName);
        user.setLoginName(userName);
        user.setSex(sex);
        user.setBirthday(birthday);
        user.setCityCode(cityCode);
        user.setAvatar(avatar);
        user.setSeries(series);
        user.setOrigin(origin);
        User updatedUser = userService.updateUser(user);
        if (updatedUser != null) {
            userBasicCacheService.invalidateUser(userId);
        }
        return kbRes.ok(userService.doLogin(updatedUser, UserLoginType.REGISTER_UPDATE));
    }

    @Override
    public KbEntity<LoginUserModel> loginByThirdPartyWX(
            String accessToken, String openId, String deviceToken, String deviceNumber) {
        WechatUserInfo wechatUserInfo = getWXUserInfo(accessToken, openId);
        if (wechatUserInfo == null || StringUtils.isEmpty(wechatUserInfo.getUnionid())) {
            return kbRes.err(KbCode.USER_LOGIN_FAIL);
        }

        /*
         * 因 2023-12-28 开吧 APP 更换微信主体, 导致用户绑定的微信号 openId 不变但 unionId 发生变化. 因此临时更改微信登录逻辑:
         * * 先尝试使用 unionId 找用户, 若可以找到则登录; 若找不到则进行下一步.
         * * 再尝试使用 openId 找用户, 若可以找到则登录, 并更新该用户的 unionId; 若找不到则提示账号不存在.
         * TODO:
         * 因此建议该逻辑运行一段时间后, 改为发现 unionId 不一致, 则提醒用户使用手机验证码进行验证, 若通过再继续登录动作, 并更新 unionId.
         */
        Optional<User> unionIdUserOp = userService.getByWXUnionId(wechatUserInfo.getUnionid());
        if(unionIdUserOp.isPresent()) {
            log.info("loginByThirdPartyWX, find user by unionId, login");
            return unionIdUserOp
                    .map(user -> userService.doLogin(user, UserLoginType.WXID))
                    .map(kbRes::ok)
                    .orElse(kbRes.err(KbCode.USER_UNREGISTER_CREDENCE));
        }

        Optional<User> openIdUserOp = userService.getByWXOpenId(openId);
        if (openIdUserOp.isPresent()) {
            log.info("loginByThirdPartyWX, find user by openId, update unionId then login");
            return openIdUserOp
                    .map(user -> {
                        LoginUserModel model = userService.doLogin(user, UserLoginType.WXID);
                        userService.updateWX(model.getUserId(), openId, wechatUserInfo.getUnionid(), "unionId 不一致时更新");
                        return model;
                    })
                    .map(kbRes::ok)
                    .orElse(kbRes.err(KbCode.USER_UNREGISTER_CREDENCE));
        } else {
            return kbRes.err(KbCode.USER_UNREGISTER_CREDENCE);
        }
    }

    @Override
    public KbEntity<LoginUserModel> wxRegister(
            String accessToken, String openId, String mobile, String avatar,
            Integer siteId, Integer source, String deviceToken, String deviceNumber) {
        WechatUserInfo wxUserInfo = getWXUserInfo(accessToken, openId);
        if (wxUserInfo == null || StringUtils.isEmpty(wxUserInfo.getUnionid())) {
            return kbRes.err(KbCode.USER_WRONG_WX_CREDENCE);
        }
        User user = doWXRegister(
                wxUserInfo.getOpenid(), wxUserInfo.getUnionid(),
                wxUserInfo.getNickname(), wxUserInfo.getSex(),
                mobile, avatar, siteId, source);
        LoginUserModel model = userService.doLogin(user, UserLoginType.WXID);
        model.setIsNewUser(1);
        return kbRes.ok(model);
    }

    @Override
    public KbEntity<LoginUserModel> wxMiniProgramLoginV2(Integer endpoint, String jsCode) {
        WXEntity wx = WXEntityEnum.resolveByEndpoint(endpoint)
                .orElseThrow(() -> new KbException(KbCode.ILLEGAL_STATE, "unknown wx mini program for endpoint: " + endpoint));
        String codeUrl = "https://api.weixin.qq.com/sns/jscode2session" +
                "?appid=" + wx.getAppId() +
                "&secret=" + wx.getSecret() +
                "&js_code=" + jsCode +
                "&grant_type=authorization_code";
        String response = restTemplate.getForObject(codeUrl, String.class);
        WechatCodeSession session = JsonUtils.toModelIgnoreError(response, WechatCodeSession.class);
        if (session == null || StringUtils.isEmpty(session.getSession_key())) {
            log.info("wxMiniProgramLogin fail, session invalid: " + response);
            return kbRes.err(KbCode.USER_LOGIN_FAIL, "wechat session null");
        }
        Optional<User> unionIdUserOp = userService.getByWXUnionId(session.getUnionid());
        if(unionIdUserOp.isPresent()) {
            log.info("login WX MiniProgram, find user by unionId, login");
            return unionIdUserOp
                    .map(user -> userService.doLogin(user, UserLoginType.WX_MINI))
                    .map(user -> {
                        userService.recordWXOpenId(user.getUserId(), wx, session.getOpenid(), true);
                        return user;
                    })
                    .map(kbRes::ok)
                    .orElse(kbRes.err(KbCode.USER_UNREGISTER_CREDENCE));
        }

        Optional<User> openIdUserOp = userService.getByWXOpenIdFromUserAccWX(session.getOpenid(), wx.getName());
        if (openIdUserOp.isPresent()) {
            log.info("login WX MiniProgram, find user by openId, update unionId then login");
            return openIdUserOp
                    .map(user -> {
                        LoginUserModel model = userService.doLogin(user, UserLoginType.WX_MINI);
                        //仅更新unionId
                        userService.updateWX(model.getUserId(), user.getWxId(), session.getUnionid(), "unionId 不一致时更新,仅更新了unionId");

                        return model;
                    })
                    .map(kbRes::ok)
                    .orElse(kbRes.err(KbCode.USER_UNREGISTER_CREDENCE));
        } else {
            return kbRes.err(KbCode.USER_UNREGISTER_CREDENCE);
        }
    }

    @Override
    public KbEntity<LoginUserModel> wxMiniProgramLogin(
            Integer endPoint, String jsCode, String iv, String encryptedData) {
        WXEntity wx = WXEntityEnum.resolveByEndpoint(endPoint)
                .orElseThrow(() -> new KbException(KbCode.ILLEGAL_STATE, "unknown wx mini program for endpoint: " + endPoint));
        String codeUrl = "https://api.weixin.qq.com/sns/jscode2session" +
                "?appid=" + wx.getAppId() +
                "&secret=" + wx.getSecret() +
                "&js_code=" + jsCode +
                "&grant_type=authorization_code";
        String response = restTemplate.getForObject(codeUrl, String.class);
        WechatCodeSession session = JsonUtils.toModelIgnoreError(response, WechatCodeSession.class);
        if (session == null || StringUtils.isEmpty(session.getSession_key())) {
            log.info("wxMiniProgramLogin fail, session invalid: " + response);
            return kbRes.err(KbCode.USER_LOGIN_FAIL, "wechat session null");
        }

        // 未授权用户无法直接获取unionId，需要解密获取
        if (StringUtils.isEmpty(session.getUnionid())) {
            String decrypt = decryptWxMiniSession(encryptedData, iv, session.getSession_key());
            //解密失败
            if (StringUtils.isEmpty(decrypt)) {
                log.info("wxMiniProgramLogin fail, get unionId, decrypted data empty," +
                        " encryptedData = " + encryptedData + "," +
                        " iv = " + iv + "," +
                        " session = " + session.getSession_key());
                return kbRes.err(KbCode.USER_LOGIN_FAIL, "wechat session decrypt empty");
            }

            WXUserInfoModel infoModel = JsonUtils.toModelIgnoreError(decrypt, WXUserInfoModel.class);
            //解密后的对象也没有 unionId
            if (infoModel == null
                    || StringUtils.isEmpty(infoModel.getUnionId())
                    || !session.getOpenid().equals(infoModel.getOpenId())) {
                log.info("wxMiniProgramLogin fail, get unionId, unionId empty," +
                        " encryptedData = " + encryptedData + "," +
                        " iv = " + iv + "," +
                        " session = " + session.getSession_key());
                return kbRes.err(KbCode.USER_LOGIN_FAIL, "wechat session WXUserInfoModel wrong");
            }

            session.setUnionid(infoModel.getUnionId());
        }
        Optional<User> unionIdUserOp = userService.getByWXUnionId(session.getUnionid());
        if(unionIdUserOp.isPresent()) {
            log.info("login WX MiniProgram, find user by unionId, login");
            return unionIdUserOp
                    .map(user -> userService.doLogin(user, UserLoginType.WX_MINI))
                    .map(user -> {
                        userService.recordWXOpenId(user.getUserId(), wx, session.getOpenid(), true);
                        return user;
                    })
                    .map(kbRes::ok)
                    .orElse(kbRes.err(KbCode.USER_UNREGISTER_CREDENCE));
        }

        Optional<User> openIdUserOp = userService.getByWXOpenIdFromUserAccWX(session.getOpenid(), wx.getName());
        if (openIdUserOp.isPresent()) {
            log.info("login WX MiniProgram, find user by openId, update unionId then login");
            return openIdUserOp
                    .map(user -> {
                        LoginUserModel model = userService.doLogin(user, UserLoginType.WX_MINI);
                        //仅更新unionId
                        userService.updateWX(model.getUserId(), user.getWxId(), session.getUnionid(), "unionId 不一致时更新,仅更新了unionId");

                        return model;
                    })
                    .map(kbRes::ok)
                    .orElse(kbRes.err(KbCode.USER_UNREGISTER_CREDENCE));
        } else {
            return kbRes.err(KbCode.USER_UNREGISTER_CREDENCE);
        }
    }

    @Override
    public KbEntity<LoginUserModel> wxMiniProgramRegister(
            Integer endPoint, String jsCode,
            String nikeName, Integer sex, String mobile, Integer siteId, Integer source,
            String iv, String encryptedData
    ) {
        WXEntity wx = WXEntityEnum.resolveByEndpoint(endPoint)
                .orElseThrow(() -> new KbException(KbCode.ILLEGAL_STATE, "unknown wx mini program for endpoint: " + endPoint));
        String codeUrl = "https://api.weixin.qq.com/sns/jscode2session" +
                "?appid=" + wx.getAppId() +
                "&secret=" + wx.getSecret() +
                "&js_code=" + jsCode +
                "&grant_type=authorization_code";
        String codeRes = restTemplate.getForObject(codeUrl, String.class);
        WechatCodeSession session = JsonUtils.toModelIgnoreError(codeRes, WechatCodeSession.class);
        if (session == null || StringUtils.isEmpty(session.getSession_key())) {
            log.info("wxMiniProgramRegister fail, session key empty :" + codeRes);
            return kbRes.err(KbCode.USER_LOGIN_FAIL, "wechat register session null");
        }
        if (StringUtils.isEmpty(session.getUnionid())) {
            // 老版本小程序可能不传这两个参数
            if (encryptedData == null || iv == null) {
                log.info("wxMiniProgramRegister fail, no encryptedData or iv. " +
                        ServletRequestUtils.getCurrentRequestInfo());
                return kbRes.err(KbCode.USER_LOGIN_FAIL, "wechat session empty, encryptedData empty");
            }

            String decrypt = decryptWxMiniSession(encryptedData, iv, session.getSession_key());
            //解密失败
            if (StringUtils.isEmpty(decrypt)) {
                log.info("wxMiniProgramRegister fail, decrypt data empty. " +
                        "encryptedData = " + encryptedData + " " +
                        "iv = " + iv + " " +
                        "session = " + session.getSession_key());
                return kbRes.err(KbCode.USER_LOGIN_FAIL, "wechat session decrypt empty");
            }

            WXUserInfoModel infoModel = JsonUtils.toModelIgnoreError(decrypt, WXUserInfoModel.class);
            //解密后的对象也没有 unionId
            if (infoModel == null
                    || StringUtils.isEmpty(infoModel.getUnionId())
                    || !session.getOpenid().equals(infoModel.getOpenId())) {
                log.info("wxMiniProgramRegister fail, decrypt data without unionId. " +
                        "encryptedData = " + encryptedData + " " +
                        "iv = " + iv + " " +
                        "session = " + session.getSession_key() + " " +
                        "decryptedData = " + decrypt);
                return kbRes.err(KbCode.USER_LOGIN_FAIL, "wechat session WXUserInfoModel wrong");
            }

            session.setUnionid(infoModel.getUnionId());
        }

        User user = doWXRegister(
                session.getOpenid(), session.getUnionid(), nikeName, sex, mobile, "", siteId, source);
        userService.recordWXOpenId(user.getId(), wx, session.getOpenid(), false);
        LoginUserModel model = userService.doLogin(user, UserLoginType.WX_MINI);
        return kbRes.ok(model);
    }

    @Override
    public KbEntity<LoginUserModel> wxMiniProgramRegisterByMobileCode(
            Integer endPoint, String jsCode, String mobileCode,
            Integer siteId,  Integer source, String sourceOpenId, String sourceUnionId
    ) {
        WXEntity wx = WXEntityEnum.resolveByEndpoint(endPoint)
                .orElseThrow(() -> new KbException(KbCode.ILLEGAL_STATE, "unknown wx mini program for endpoint: " + endPoint));
        //get wx session
        WxMaJscode2SessionResult session = getWxMaSession(wx.getAppId(), jsCode);
        //get wx mobile
        String mobile = getWxPhoneNum(wx.getAppId(), mobileCode);

        User user = doWXRegister(
                session.getOpenid(), session.getUnionid(), null, null, mobile, "", siteId, source);
        wxOpenUserService.addOpenUser(siteId, sourceOpenId, sourceUnionId, user.getId());
        userService.recordWXOpenId(user.getId(), wx, session.getOpenid(), false);
        LoginUserModel model = userService.doLogin(user, UserLoginType.WX_MINI);
        return kbRes.ok(model);
    }
    @Override
    public KbEntity<WebAuthResult> wxWebLogin(String code){
        //log.info("code:"+code);
        WechatAccessToken token = getWXAccessTokenByCode(code);
        if (token.getErrcode() != null) {
            return kbRes.err(KbCode.USER_LOGIN_FAIL, "", token.getErrmsg());
        } else if (token.getIs_snapshotuser() != null && token.getIs_snapshotuser() == 1) {
            // 虚拟用户
            WebAuthResult authResult = new WebAuthResult();
            WebAuthResult.AuthInfo info = new WebAuthResult.AuthInfo();
            info.setAccessToken(token.getAccess_token());
            info.setOpenId(token.getOpenid());
            info.setIsSnapshotUser(token.getIs_snapshotuser());
            authResult.setAuthInfo(info);
            KbEntity<WebAuthResult> entity = kbRes.err(KbCode.USER_WX_SNAPSHOT);
            entity.setData(authResult);
            return entity;
        } else {
            String unionId = token.getUnionid();
            if (StringUtils.isEmpty(unionId)){
                WechatUserInfo info = getWXUserInfo(token.getAccess_token(),token.getOpenid());
                unionId = info.getUnionid();
            }
            if (StringUtils.isEmpty(unionId)){
                return kbRes.err(KbCode.USER_LOGIN_FAIL,"","微信授权失败");
            }

            return userService.getByWXUnionId(unionId)
                    .map(user -> userService.doLogin(user, UserLoginType.WX_WEB))
                    .map(t->{
                        WebAuthResult authResult = new WebAuthResult();
                        authResult.setLoginUserModel(t);
                        return authResult;
                    })
                    .map(kbRes::ok)
                    .orElseGet(()->{
                        WebAuthResult authResult = new WebAuthResult();
                        WebAuthResult.AuthInfo info = new WebAuthResult.AuthInfo();
                        info.setAccessToken(token.getAccess_token());
                        info.setOpenId(token.getOpenid());
                        authResult.setAuthInfo(info);

                        KbEntity<WebAuthResult> ret = new KbEntity<>(KbCode.USER_UNREGISTER_CREDENCE);
                        ret.setData(authResult);
                        return ret;
                    });
        }
    }

    @Override
    public KbEntity<WebAuthResult> wxWebRegister(
            String accessToken, String openId, String mobile, Integer siteId, Integer source) {
        WechatUserInfo wechatUserInfo = getWXUserInfo(accessToken, openId);
        if (StringUtils.isEmpty(wechatUserInfo.getErrcode())){
            User user = doWXRegister(
                    openId, wechatUserInfo.getUnionid(),
                    wechatUserInfo.getNickname(), wechatUserInfo.getSex(),
                    mobile, "", siteId,source);
            LoginUserModel model = userService.doLogin(user, UserLoginType.WX_WEB);

            WebAuthResult authResult = new WebAuthResult();
            authResult.setLoginUserModel(model);
            WebAuthResult.AuthInfo info = new WebAuthResult.AuthInfo();
            info.setAvatar(wechatUserInfo.getHeadimgurl());
            info.setName(wechatUserInfo.getNickname());

            return kbRes.ok(authResult);
        }else{
            return kbRes.err(KbCode.USER_LOGIN_FAIL,"login fail: " + mobile, wechatUserInfo.getErrmsg());
        }
    }

    private User doWXRegister(
            String openId, String unionId,
            String nikeName, Integer sex, String mobile, String avatar,
            Integer siteId, Integer source
    ) {
        if (!StringUtils.isValidMobile(mobile)) {
            throw new KbException(KbCode.REQUEST_PARAM_INVALID).setReadableMessage("请填写有效的电话号码").li();
        }
        Optional<User> wxOptional = userService.getByWXUnionId(unionId);
        Optional<User> mobileOptional = userService.getByMobile(mobile);
        User user;
        if (wxOptional.isPresent() && mobileOptional.isPresent()) {
            user = wxOptional.get();
        } else if (wxOptional.isPresent()) {
            user = wxOptional.get();
            user.setMobile(mobile);
            userService.updateMobile(user.getId(), mobile);
        } else if (mobileOptional.isPresent()) {
            user = mobileOptional.get();
            user.setWxId(openId);
            user.setUnionId(unionId);
            userService.updateWX(user.getId(), openId, unionId, "用户注册");
        } else {
            user = new User();
            user.setWxId(openId);
            user.setUnionId(unionId);
            user.setMobile(mobile);
            user.setPassword(DigestUtils.md5DigestAsHex(initPsd.getBytes()));
            user.setSetpass(1);
            user.setSiteId(siteId);
            user.setSource(source);
            user.setAvatar(avatar);
            user.setSex(sex);

            if (!StringUtils.isEmpty(nikeName) && !userService.exitByUserNameOrLoginName(nikeName, nikeName)) {
                user.setUserName(nikeName);
                user.setLoginName(nikeName);
            }
            userService.createUser(user);
        }
        return user;
    }

    @Override
    public KbEntity<LoginUserModel> loginByThirdPartyQQ(
            String accessToken, String openId, String deviceToken, String deviceNumber) {
        if (StringUtils.isEmpty(openId) || StringUtils.isEmpty(accessToken)) {
            return kbRes.err(KbCode.USER_LOGIN_FAIL);
        }
        return userService.getByQQOpenId(openId)
                .map(user -> userService.doLogin(user, UserLoginType.QQID))
                .map(kbRes::ok)
                .orElse(kbRes.err(KbCode.USER_UNREGISTER_CREDENCE));
    }

    @Override
    public KbEntity<LoginUserModel> qqRegister(
            String accessToken, String openId, String mobile, String avatar,
            Integer siteId, Integer source, String deviceToken, String deviceNumber
    ) {
        if (!StringUtils.isValidMobile(mobile)) {
            return kbRes.err(KbCode.REQUEST_PARAM_INVALID,
                    "mobile invalid", "请填写有效的电话号码");
        }
        QQUserInfo qqUserInfo = getQQUserInfo(accessToken, openId);
        if (qqUserInfo == null || !qqUserInfo.getRet().equals(0)) {
            return kbRes.err(KbCode.USER_WRONG_QQ_CREDENCE);
        }
        Optional<User> qqOptional = userService.getByQQOpenId(openId);
        Optional<User> mobileOptional = userService.getByMobile(mobile);
        User user;
        if (qqOptional.isPresent() && mobileOptional.isPresent()) {
            user = qqOptional.get();
        } else if (qqOptional.isPresent()) {
            user = qqOptional.get();
            user.setMobile(mobile);
            userService.updateMobile(user.getId(), mobile);
        } else if (mobileOptional.isPresent()) {
            user = mobileOptional.get();
            user.setQqId(openId);
            userService.updateQQ(user.getId(), openId);
        } else {
            user = new User();
            user.setQqId(openId);
            user.setMobile(mobile);
            user.setPassword(DigestUtils.md5DigestAsHex(initPsd.getBytes()));
            user.setSetpass(1);
            user.setSiteId(siteId);
            user.setSource(source);
            user.setAvatar(avatar);
            user.setSex("女".equals(qqUserInfo.getGender()) ? 2 : 1);

            String userName = qqUserInfo.getNickname();
            if (!StringUtils.isEmpty(userName) && !userService.exitByUserNameOrLoginName(userName, userName)) {
                user.setUserName(userName);
                user.setLoginName(userName);
            }
            userService.createUser(user);
        }

        LoginUserModel loginUserModel = userService.doLogin(user, UserLoginType.QQID);
        loginUserModel.setIsNewUser(1);
        return kbRes.ok(loginUserModel);
    }

    @Override
    public KbEntity<LoginUserModel> autoLogin(Integer userId, String passwordMd5) {
        return userService.getDetail(userId)
                .filter(user -> passwordMd5.equals(user.getPassword()))
                .map(user -> userService.doLogin(user, UserLoginType.AUTO))
                .map(kbRes::ok)
                .orElse(kbRes.err(KbCode.USER_LOGIN_WITH_PASSWORD_FAIL));
    }

    @Override
    public KbEntity<LoginUserModel> videoLogin(String userName, String password) {
        User user = userService.getByLoginNameOrMobile(userName).orElseThrow(
                () -> new KbException(KbCode.USER_LOGIN_WITH_PASSWORD_FAIL).li());
        if (!user.getPassword().equals(password)) {
            throw new KbException(KbCode.USER_LOGIN_WITH_PASSWORD_FAIL).li();
        }
        String permission = "site." + user.getSiteId() + ".program.video.login";
        if (authService.hasPermission(user.getId(), permission, "1").getCode() != 0) {
            throw new KbException(KbCode.AUTH_NO_PERMISSION, "你没有登录权限").li();
        }
        LoginUserModel loginUserModel = new LoginUserModel();
        loginUserModel.setUserId(user.getId());
        loginUserModel.setUserName(user.getUserName());
        loginUserModel.setSex(user.getSex());
        loginUserModel.setSiteId(user.getSiteId());
        loginUserModel.setSource(user.getSource());
        loginUserModel.setToken(userService.getUserToken(user, true));

        return kbRes.ok(loginUserModel);
    }

    @Override
    public KbEntity<Void> logout(Integer userId, String token) {
        userService.doLogout(userId);
        return kbRes.ok();
    }

    @Override
    public KbEntity<Void> initialPassword(Integer userId, String passwordMd5) {
        if (null == userId) {
            return kbRes.err(KbCode.REQUEST_PARAM_INVALID, "userId null");
        }
        if (null == passwordMd5) {
            return kbRes.err(KbCode.REQUEST_PARAM_INVALID, "passwordMd5 null");
        }
        if (userService.setPassword(userId, passwordMd5).isPresent()) {
            return kbRes.ok();
        } else {
            return kbRes.err(KbCode.USER_PSD_SETTED);
        }
    }

    @Override
    public KbEntity<String> updateUserPasswordByMobile(String mobile, String passwordMd5) {
        if (!StringUtils.isValidMobile(mobile)) {
            return kbRes.err(KbCode.REQUEST_PARAM_INVALID, "mobile invalid");
        }
        if (null == passwordMd5) {
            return kbRes.err(KbCode.REQUEST_PARAM_INVALID, "passwordMd5 null");
        }
        User user = userService.getBasicByMobile(mobile).orElseThrow(() -> new KbException(KbCode.USER_NOT_EXISTS).li());
        userService.updatePassword(user.getId(), passwordMd5);
        return kbRes.ok();
    }

    @Override
    public KbEntity<String> updateUserPassword(Integer userId, String oldPsd, String newPsd) {
        User user = userService.getDetail(userId).orElseThrow(() -> new KbException(KbCode.USER_NOT_EXISTS));
        if (!user.getPassword().equals(oldPsd)) {
            return kbRes.err(KbCode.USER_LOGIN_WITH_PASSWORD_FAIL);
        }
        userService.updatePassword(userId, newPsd);
        return kbRes.ok();
    }

    @Override
    public KbEntity<String> resetUserAvatar(Integer userId) {
        User oldUser = userService.getBasic(userId).orElseThrow(() -> new KbException(KbCode.USER_NOT_EXISTS));
        User user = userService.updateAvatar(userId, oldUser.getSex());
        return kbRes.ok(user.getAvatar());
    }

    @Override
    public KbEntity<String> resetUserName(Integer userId) {
        userService.getBasic(userId).orElseThrow(() -> new KbException(KbCode.USER_NOT_EXISTS));
        User user = userService.updateUserName(userId, "吧友_" + userId);
        return kbRes.ok(user.getUserName());
    }

    @Override
    public KbEntity<String> resetUserPassword(Integer userId, String passwordMd5) {
        userService.getBasic(userId).orElseThrow(() -> new KbException(KbCode.USER_NOT_EXISTS));
        userService.updatePassword(userId, passwordMd5);
        return kbRes.ok();
    }

    @Override
    public KbEntity<Object> checkBind(Integer userId) {
        User user = userService.getDetail(userId).orElseThrow(() -> new KbException(KbCode.USER_NOT_EXISTS).li());
        HashMap<String, Integer> result = new HashMap<>();
        result.put("mobile", StringUtils.isEmpty(user.getMobile()) ? 0 : 1);
        result.put("qq", StringUtils.isEmpty(user.getQqId()) ? 0 : 1);
        result.put("wx", StringUtils.isEmpty(user.getUnionId()) ? 0 : 1);
        return kbRes.ok(result);
    }

    @Override
    public KbEntity<Void> bindQQ(Integer userId, String accessToken, String openId) {
        QQUserInfo qqUserInfo = getQQUserInfo(accessToken, openId);
        if (qqUserInfo == null || !qqUserInfo.getRet().equals(0)) {
            return kbRes.err(KbCode.USER_WRONG_QQ_CREDENCE);
        }
        Optional<User> optional = userService.getByQQOpenId(openId);
        if (optional.isPresent()) {
            return kbRes.err(KbCode.USER_QQ_ALREADY_EXISTS);
        }
        userService.updateQQ(userId, openId);
        return kbRes.ok();
    }

    @Override
    public KbEntity<Void> unbindThird(Integer userId, Integer type) {
        if (type == 1) {
            userService.updateQQ(userId, "");
        } else if (type == 2) {
            userService.updateWX(userId, "", "", "解绑三方账号");
        }
        return kbRes.ok();
    }

    @Override
    public KbEntity<Void> bindWX(Integer userId, String accessToken, String openId) {
        WechatUserInfo wxUserInfo = getWXUserInfo(accessToken, openId);
        if (wxUserInfo == null || StringUtils.isEmpty(wxUserInfo.getUnionid())) {
            return kbRes.err(KbCode.USER_WRONG_WX_CREDENCE);
        }
        Optional<User> optional = userService.getByWXUnionId(wxUserInfo.getUnionid());
        if (optional.isPresent()) {
            return kbRes.err(KbCode.USER_WX_ALREADY_EXISTS);
        }
        userService.updateWX(userId, openId, wxUserInfo.getUnionid(), "绑定三方账号");
        return kbRes.ok();
    }

    @Override
    public KbEntity<Void> bindMobile(Integer userId, String mobile) {
        if (null == userId) {
            return kbRes.err(KbCode.REQUEST_PARAM_INVALID, "userId null");
        }
        if (!StringUtils.isValidMobile(mobile)) {
            return kbRes.err(KbCode.REQUEST_PARAM_INVALID, "mobile invalid");
        }
        if (userService.getBasicByMobile(mobile).isPresent()) {
            return kbRes.err(KbCode.USER_MOBILE_ALREADY_EXISTS);
        }
        userService.updateMobile(userId, mobile);
        return kbRes.ok();
    }

    @Override
    public KbEntity<Void> logoff(Integer userId) {
        if (null == userId) {
            return kbRes.err(KbCode.REQUEST_PARAM_INVALID, "userId null");
        }
        userService.userLogoff(userId);
        return kbRes.ok();
    }

    @Override
    public KbEntity<Void> cancelLogoff(Integer userId) {
        if (null == userId) {
            return kbRes.err(KbCode.REQUEST_PARAM_INVALID, "userId null");
        }
        userService.cancelLogoff(userId);
        return kbRes.ok();
    }

    @Override
    public KbEntity<List<String>> getLoginType(Integer userId) {
        if (null == userId) {
            return kbRes.err(KbCode.REQUEST_PARAM_INVALID, "userId null");
        }
        List<String> typeList = new LinkedList<>();
        User user = userService.getDetail(userId).orElseThrow(() -> new KbException(KbCode.USER_NOT_EXISTS).li());
        if (!StringUtils.isEmpty(user.getMobile())) {
            typeList.add(UserLoginType.MOBILE.name().toLowerCase());
        }
        if (!StringUtils.isEmpty(user.getWxId()) || !StringUtils.isEmpty(user.getUnionId())) {
            typeList.add(UserLoginType.WXID.name().toLowerCase());
        }
        if (!StringUtils.isEmpty(user.getQqId())) {
            typeList.add(UserLoginType.QQID.name().toLowerCase());
        }
        typeList.add(UserLoginType.PASSWORD.name().toLowerCase());
        return kbRes.ok(typeList);
    }

    @Override
    public KbEntity<UserLogoffModel> logoffInfo(Integer userId) {
        if (null == userId) {
            return kbRes.err(KbCode.REQUEST_PARAM_INVALID, "userId null");
        }
        UserLogoff logoff = userService.getLogoffDetail(userId).orElseThrow(() -> new KbException(KbCode.USER_NOT_EXISTS).li());
        return kbRes.ok(logoff2Model(logoff));
    }

    @Override
    public KbEntity<Boolean> verifyLogoff(Integer userId, String type, String certification) {
        User user = userService.getDetail(userId).orElseThrow(() -> new KbException(KbCode.USER_NOT_EXISTS).li());
        boolean passed = false;
        if (type.equals(UserLoginType.WXID.name().toLowerCase()) && certification.equals(user.getUnionId())) {
            passed = true;
        } else if (type.equals(UserLoginType.QQID.name().toLowerCase()) && certification.equals(user.getQqId())) {
            passed = true;
        } else if (type.equals(UserLoginType.PASSWORD.name().toLowerCase()) && certification.equals(user.getPassword())) {
            passed = true;
        }
        return kbRes.ok(passed);
    }

    @Override
    public KbEntity<List<UserLogoffModel>> getLogoffList(Integer page, Integer pageSize) {
        return kbRes.ok(userService.getLogoffPage(page, pageSize).map(UserAccountServiceController::logoff2Model));
    }

    @Override
    public KbEntity<String> updateExpertDeviceToken(Integer userId, String deviceToken) {
        if (null == userId) {
            return kbRes.err(KbCode.REQUEST_PARAM_INVALID, "userId null");
        }
        if (StringUtils.isEmpty(deviceToken)) {
            return kbRes.err(KbCode.REQUEST_PARAM_INVALID, "deviceToken null");
        }
        userService.updateExpertDeviceToken(userId, deviceToken);
        return kbRes.ok();
    }

    @Override
    public KbEntity<LoginUserModel> loginCms(Integer userId, String account, String passwordMd5) {
        int nextTrySeconds = userLoginCacheService.getNextLoginTryDurationInSecond(account);
        if (nextTrySeconds > 0){
            String hit = "密码尝试次数过多,请在 " + (int)Math.ceil(nextTrySeconds*1.0/60) +" 分钟后重试。";
            return kbRes.err(KbCode.USER_LOGIN_FAIL, "failed too many times", hit);
        }

        User user = userService.getByLoginNameOrMobile(account)
                .orElseThrow(() -> new KbException(KbCode.USER_LOGIN_FAIL).r("账号或密码错误").li());
        if (!user.getPassword().equals(passwordMd5)) {
            return kbRes.err(KbCode.USER_LOGIN_FAIL, "login fail", "账号或者密码错误");
        }
        if (userId != null && !userId.equals(user.getId())) {
            return kbRes.err(KbCode.USER_LOGIN_FAIL, "login fail", "账号匹配错误");
        }
        return kbRes.ok(userService.login2model(user, userService.getUserToken(user, false)));
    }

    @Override
    public KbEntity<LoginUserModel> loginCms1(Integer userId, String account, String password) {
        if(!WeakPwdUtils.isPasswordOk(password)) {
            return kbRes.err(KbCode.USER_LOGIN_UNQUALIFIED_PASSWORD, null, "密码应为8-16位，必须同时包含大写字母、小写字母、数字和特殊字符~!@#$%^&*_-+=:;.");
        }

        return loginCms(userId, account, password);
    }

    @Override
    public KbEntity<LoginCMSResult> loginCmsCheckAcs(
            Integer userId, String account, String passwordMd5, String awcs) {
        LoginCMSResult acsResult = aliAcsChecker.checkAcs(awcs);
        if (!acsResult.isOK()) {
            return kbRes.ok(acsResult);
        }
        return loginCms(userId, account, passwordMd5, acsResult.getAliACSCode());
    }

    @Override
    public KbEntity<LoginCMSResult> loginCmsCheckSig(
            Integer userId, String account, String passwordMd5, String awcs) {
        LoginCMSResult acsResult = aliAcsChecker.checkSig(awcs);
        if (!acsResult.isOK()) {
            return kbRes.ok(acsResult);
        }
        return loginCms(userId, account, passwordMd5, acsResult.getAliACSCode());
    }

    @Override
    public KbEntity<LoginUserModel> loginCmsByMobile(Integer userId, String mobile, String vcode) {
        if (!smsService.checkSmsCode(mobile, vcode, SmsCodeMark.LOGIN.getMark()).dataOrThrow()) {
            return kbRes.err(KbCode.USER_WRONG_VCODE);
        }

        return userService.getByLoginNameOrMobile(mobile)
            .filter(user -> userId == null || userId.equals(user.getId())) // 如果指定了 userId, 验证是否一致
            .map(user -> userService.login2model(user, userService.getUserToken(user, false)))
            .map(kbRes::ok)
            .orElse(kbRes.err(KbCode.USER_LOGIN_FAIL));
    }

    @Override
    // @KbCheckSignature(type = KbSignType.LOGIN)
    public KbEntity<String> loginCmsSendSMSCode(String mobile, String awcs) {
        if (StringUtils.isEmpty(awcs)) {
            return kbRes.err(KbCode.REQUEST_FAIL, null, "缺少检验参数");
        }
        LoginCMSResult result = aliAcsChecker.checkSig(awcs);
        if (result.isOK()) {
            smsService.sendSmsCode(mobile, 300, SmsCodeMark.LOGIN.getMark()).dataOrThrow();
            return kbRes.ok("验签通过，短信已下发");
        } else {
            return kbRes.err(KbCode.REQUEST_FAIL, null, result.getMessage());
        }
    }

    // ----------------------------------------------------------------

    private KbEntity<LoginCMSResult> loginCms(
            Integer userId, String account, String passwordMd5, Integer acsCode) {
        int nextTrySeconds = userLoginCacheService.getNextLoginTryDurationInSecond(account);
        if (nextTrySeconds > 0){
            String msg = "密码尝试次数过多,请在 " + (int)Math.ceil(nextTrySeconds*1.0/60) +" 分钟后重试。";
            return kbRes.ok(new LoginCMSResult(LoginCMSResultCode.RETRY_EXCEED_LIMIT, msg));
        }
        Optional<User> userOp = userService.getByLoginNameOrMobile(account);
        if (!userOp.isPresent()) {
            return kbRes.ok(new LoginCMSResult(LoginCMSResultCode.ACCOUNT_NOT_EXISTS));
        }
        User user = userOp.get();
        if (!user.getPassword().equals(passwordMd5)) {
            return kbRes.ok(new LoginCMSResult(LoginCMSResultCode.WRONG_PASSWORD));
        }
        if (userId != null && !userId.equals(user.getId())) {
            return kbRes.ok(new LoginCMSResult(LoginCMSResultCode.NOT_OWNER));
        }
        try {
            return kbRes.ok(userService.getUserToken(user, false))
                    .map(token -> userService.login2model(user, token))
                    .map(LoginCMSResult::new)
                    .peek(result -> result.setAliACSCode(acsCode));
        } catch (Exception e) {
            return kbRes.ok(new LoginCMSResult(LoginCMSResultCode.FAIL));
        }
    }

    private QQUserInfo getQQUserInfo(String accessToken, String openId) {
        String infoUrl = "https://graph.qq.com/user/get_user_info" +
                "?access_token=" + accessToken +
                "&openid=" + openId +
                "&lang=zh_CN&oauth_consumer_key=1104728527";
        String infoResponse = restTemplate.getForObject(infoUrl, String.class);
        log.debug("response from qq: " + infoResponse);
        return JsonUtils.getGson().fromJson(infoResponse, QQUserInfo.class);
    }

    private WechatUserInfo getWXUserInfo(String accessToken, String openId) {
        String infoUrl = "https://api.weixin.qq.com/sns/userinfo" +
                "?access_token=" + accessToken +
                "&openid=" + openId +
                "&lang=zh_CN";
        String infoResponse = restTemplate.getForObject(infoUrl, String.class);
        log.debug("response from wx: " + infoResponse);
        return JsonUtils.getGson().fromJson(infoResponse, WechatUserInfo.class);
    }

    private WechatAccessToken getWXAccessTokenByCode(String code){
        log.debug("wx web login code: "+code);
        String tokenUrl = "https://api.weixin.qq.com/sns/oauth2/access_token" +
                "?appid=" + wxOpenAppId +
                "&secret=" + wxOpenSecret +
                "&code=" + code +
                "&grant_type=authorization_code";
        String infoResponse = restTemplate.getForObject(tokenUrl, String.class);
        log.debug("response access_token from wx: " + infoResponse);
        return JsonUtils.getGson().fromJson(infoResponse, WechatAccessToken.class);
    }

    private WxMaJscode2SessionResult getWxMaSession(String appId, String jsCode) {
        try {
            if (!wxMaService.switchover(appId)) {
                throw new KbException(KbCode.ILLEGAL_ARGUMENT).r("未找到对应微信配置");
            }
            return wxMaService.getUserService().getSessionInfo(jsCode);
        } catch (WxErrorException e) {
            throw new KbException(KbCode.USER_LOGIN_FAIL).r("微信获取登陆会话失败");
        } finally {
            WxMaConfigHolder.remove();
        }
    }

    private String getWxPhoneNum(String appId, String code) {
        if (!wxMaService.switchover(appId)) {
            throw new KbException(KbCode.ILLEGAL_ARGUMENT).r("未找到对应微信配置");
        }
        try {
            WxMaPhoneNumberInfo phoneNoInfo = wxMaService.getUserService().getPhoneNoInfo(code);
            log.info("wx ma phoneNum get : appId:{}, code:{}, result:{}", appId, code, GsonUtils.getGson().toJson(phoneNoInfo));
            return phoneNoInfo.getPurePhoneNumber();
        } catch (WxErrorException e) {
            throw new KbException(KbCode.REQUEST_FAIL).r("微信获取手机号失败");
        } finally {
            WxMaConfigHolder.remove();
        }
    }

    private static UserLogoffModel logoff2Model(UserLogoff logoff) {
        UserLogoffModel model = new UserLogoffModel();
        model.setId(logoff.getId());
        model.setUserId(logoff.getUserId());
        model.setUserName(logoff.getUserName());
        model.setMobile(logoff.getMobile());
        model.setStatus(logoff.getStatus());
        model.setEndTime(logoff.getEndTime());
        model.setCreateTime(logoff.getCreateTime());
        return model;
    }

    private static String decryptWxMiniSession(String encryptedData, String iv, String session) {
        byte[] encrypt = Base64Utils.decodeFromString(encryptedData);
        byte[] ivByte = Base64Utils.decodeFromString(iv);
        byte[] seed = Base64Utils.decodeFromString(session);
        try {
            SecretKeySpec keySpec = new SecretKeySpec(seed, "AES");
            IvParameterSpec ivSpec = new IvParameterSpec(ivByte);
            Cipher cipher = Cipher.getInstance("AES/CBC/PKCS5Padding");
            cipher.init(Cipher.DECRYPT_MODE, keySpec, ivSpec);
            byte[] result = cipher.doFinal(encrypt);
            return new String(result);
        } catch (Exception e) {
            log.error(e.getMessage());
            return "";
        }
    }

    private static UserModel user2basic(User user) {
        UserModel model = new UserModel();
        model.setUserId(user.getId());
        model.setUserName(user.getUserName());
        model.setLoginName(user.getLoginName());
        model.setAvatar(user.getAvatar());
        model.setSex(user.getSex());
        model.setSiteId(user.getSiteId());
        return model;
    }

    @NoArgsConstructor
    @Data
    private static class WXUserInfoModel {
        private String openId;
        private String nickName;
        private Integer gender;
        private String avatarUrl;
        private String unionId;
    }

}
