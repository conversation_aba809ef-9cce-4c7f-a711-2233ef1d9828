package com.kaiba.m.core.controller.news.article;

import com.google.common.collect.Lists;
import com.kaiba.lib.base.constant.news.article.NewsState;
import com.kaiba.lib.base.domain.news.article.ArticleQueryModel;
import com.kaiba.lib.base.response.KbCode;
import com.kaiba.lib.base.response.KbException;
import com.kaiba.m.core.controller.news.article.convert.mapping.NewsArticleMapper;
import com.kaiba.m.core.domain.news.article.NewsArticle;
import com.kaiba.m.core.domain.news.legacy.News;
import com.kaiba.m.core.domain.news.pool.bygroup.IdsGroup;
import com.kaiba.m.core.repository.news.article.NewsArticleRepository;
import com.kaiba.m.core.service.news.article.NewsArticleService;
import com.kaiba.m.core.service.news.legacy.NewsService;
import com.kaiba.m.core.service.news.pool.bygroup.NewsGroupService;
import io.swagger.annotations.Api;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * Description: 新老资讯同步
 * Author: ZM227
 * Date: 2024/9/14 19:48
 */
@Slf4j
@RestController
@Api(tags = "新老资讯同步")
public class NewsArticleSyncController {

    @Resource
    private NewsService newsService;
    @Resource
    private NewsArticleMapper newsArticleMapper;
    @Resource
    private NewsArticleService newsArticleService;
    @Resource
    private NewsGroupService newsGroupService;
    @Resource
    private NewsArticleRepository articleRepository;

    @PostMapping("/article/obj/syncNewsArticleFromOldNews")
    public void syncNewsArticleFromOldNews(@RequestParam String startId,
        @RequestParam(required = false) String uniqueId) {
        if (StringUtils.isNotBlank(uniqueId)) {
            Optional<News> newsOptional = newsService.getNewsById(uniqueId);
            if (newsOptional.isPresent()) {
                News news = newsOptional.get();
                if (!newsArticleService.getArticleById(news.getId()).isPresent()) {
                    NewsArticle created = newsArticleService.transferCreateArticleFromOld(
                        newsArticleMapper.oldToNewsArticleMapping(news));
                    // 处理文章分组
                    if (NewsState.ONLINE.name().equals(created.getState())) {
                        Set<String> groupSet = created.getGroups();
                        if (CollectionUtils.isNotEmpty(groupSet)) {
                            for (String s : groupSet) {
                                if (StringUtils.isBlank(s)) {
                                    continue;
                                }
                                IdsGroup idsGroup = newsGroupService.getGroup(null, s).orElseThrow(
                                    () -> new KbException(KbCode.RESOURCE_NOT_FOUND).li()
                                        .r("分组不存在"));
                                newsGroupService.upsertArticleToGroup(created, idsGroup, null);
                            }
                        }
                    }
                }
            }
        } else {
            List<News> current;
            do {
                current = newsService.getListByIdGtOrderAndSiteIdByIdAsc(startId, 9, 50);
                for (News news : current) {
                    try {
                        startId = news.getId();
                        if (newsArticleService.getArticleById(news.getId()).isPresent()) {
                            log.info(
                                "news exist : " + news.getTitle() + "&news id : " + news.getId());
                            continue;
                        }
                        NewsArticle created = newsArticleMapper.oldToNewsArticleMapping(news);
                        created = newsArticleService.transferCreateArticleFromOld(created);
                        // 处理文章分组
                        if (NewsState.ONLINE.name().equals(created.getState())) {
                            Set<String> groupSet = created.getGroups();
                            if (CollectionUtils.isNotEmpty(groupSet)) {
                                for (String s : groupSet) {
                                    if (StringUtils.isBlank(s)) {
                                        continue;
                                    }
                                    IdsGroup idsGroup = newsGroupService.getGroup(null, s)
                                        .orElseThrow(
                                            () -> new KbException(KbCode.RESOURCE_NOT_FOUND).li()
                                                .r("分组不存在"));
                                    newsGroupService.upsertArticleToGroup(created, idsGroup, null);
                                }
                            }
                        }
                    } catch (Exception e) {
                        log.error("errorMsg : " + e.getMessage() + "@newsId : " + news.getId());
                    }
                }
            } while (CollectionUtils.isNotEmpty(current));
        }
    }

    /**
     * 把时政专题的createTime和updateTime更新
     *
     * @param groupIds 时政专题分组id数组
     */
    @PostMapping("/article/obj/fixPolicyNewsTime")
    public void fixPolicyNewsTime(@RequestParam String groupIds) {
        String[] groupStr = groupIds.split(",");
        List<String> groupIdList = Lists.newArrayList(groupStr);
        if (CollectionUtils.isEmpty(groupIdList)) {
            return;
        }
        for (String groupId : groupIdList) {
            Optional<IdsGroup> groupOptional = newsGroupService.getGroup(groupId, null);
            if (!groupOptional.isPresent()) {
                continue;
            }
            Page<NewsArticle> articlePage;
            int startPage = 1;
            do {
                ArticleQueryModel queryModel = new ArticleQueryModel();
                queryModel.setPage(startPage++);
                queryModel.setPageSize(100);
                queryModel.setGroup(groupOptional.get().getKey());
                articlePage = newsArticleService.getArticlePageByQuery(queryModel);
                for (NewsArticle newsArticle : articlePage.getContent()) {
                    if (newsArticle.getAttr().containsKey("syncCreateTime")) {
                        newsArticle.setCreateTime(
                            NumberUtils.toLong(newsArticle.getAttr().get("syncCreateTime")));
                    }
                    if (newsArticle.getAttr().containsKey("syncUpdateTime")) {
                        newsArticle.setUpdateTime(
                            NumberUtils.toLong(newsArticle.getAttr().get("syncUpdateTime")));
                        newsArticle.setReleaseTime(
                            NumberUtils.toLong(newsArticle.getAttr().get("syncUpdateTime")));
                        newsArticle.setOnlineTime(
                            NumberUtils.toLong(newsArticle.getAttr().get("syncUpdateTime")));
                        newsArticle.setSignTime(
                            NumberUtils.toLong(newsArticle.getAttr().get("syncUpdateTime")));
                    }
                    articleRepository.updateTime(newsArticle.getId(), newsArticle.getCreateTime(),
                        newsArticle.getUpdateTime());
                }
            } while (!(articlePage.isEmpty() || articlePage.isLast()));
        }
    }
}