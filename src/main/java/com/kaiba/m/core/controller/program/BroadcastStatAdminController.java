
package com.kaiba.m.core.controller.program;

import com.kaiba.lib.base.constant.KbHeader;
import com.kaiba.lib.base.middleware.KbRes;
import com.kaiba.lib.base.response.KbEntity;
import com.kaiba.m.core.domain.program.BroadcastStat;
import com.kaiba.m.core.service.program.BroadcastStatService;
import com.kaiba.m.core.model.program.BroadcastStatIdCountByUserIdModel;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@Slf4j
@RestController
@RequestMapping("/admin/program/broadcastStat")
public class BroadcastStatAdminController {

    private final KbRes kbRes;
    private final BroadcastStatService broadcastStatService;

    public BroadcastStatAdminController(
            KbRes kbRes,
            BroadcastStatService broadcastStatService
    ) {
        this.kbRes = kbRes;
        this.broadcastStatService = broadcastStatService;
    }

    @ApiOperation(value = "获取按用户分组的待播统计列表")
    @PostMapping(path = "/getIdCountListByUserId")
    public KbEntity<List<BroadcastStatIdCountByUserIdModel>> getIdCountListByUserId(
            @RequestHeader(KbHeader.KB_SITE_ID) Integer siteId,
            @RequestParam Long startCreateTime,
            @RequestParam Long endCreateTime
    ) {
        return kbRes.ok(broadcastStatService.getIdCountListByUserId(siteId, startCreateTime, endCreateTime).getMappedResults());
    }

    @ApiOperation(value = "获取待播统计列表")
    @PostMapping(path = "/getList")
    public KbEntity<List<BroadcastStat>> getList(
            @RequestHeader(KbHeader.KB_SITE_ID) Integer siteId,
            @RequestParam Long startCreateTime,
            @RequestParam Long endCreateTime,
            @RequestParam(required = false) Integer userId,
            @RequestParam(required = false, defaultValue = "1") Integer page,
            @RequestParam(required = false, defaultValue = "15") Integer pageSize
    ) {
        return kbRes.ok(broadcastStatService.getList(siteId, userId, startCreateTime, endCreateTime, page, pageSize));
    }

    @ApiOperation("添加到待播统计")
    @PostMapping("/create")
    public KbEntity<BroadcastStat> create(
            @RequestHeader(KbHeader.KB_USER_ID) Integer userId,
            @RequestHeader(KbHeader.KB_SITE_ID) Integer siteId,
            @RequestParam String noteId,
            @RequestParam String threadId,
            @RequestParam String boardThreadId
    ) {
        return kbRes.ok(broadcastStatService.create(userId, siteId, noteId, threadId, boardThreadId));
    }
}
