package com.kaiba.m.core.controller.thirdparty.hoolo;

import com.kaiba.lib.base.annotation.api.KbCheckSignature;
import com.kaiba.lib.base.assembler.Mapper;
import com.kaiba.lib.base.constant.KbSignType;
import com.kaiba.lib.base.constant.common.KbImageFormat;
import com.kaiba.lib.base.domain.appwidget.banner.BannerImageModel;
import com.kaiba.lib.base.middleware.KbRes;
import com.kaiba.lib.base.response.KbCode;
import com.kaiba.lib.base.response.KbEntity;
import com.kaiba.lib.base.util.appaction.AppActionUtils;
import com.kaiba.m.core.service.appcomponent.banner.AppBannerCacheModel;
import com.kaiba.m.core.service.appcomponent.banner.AppBannerCacheService;
import io.swagger.annotations.ApiOperation;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * author: lyux
 * date: 2024-10-24
 *
 * 对接葫芦网 PC 端首页.
 */
@Slf4j
@Validated
@RestController
@RequestMapping("/hoolo/homepage")
public class HooloHomePageController {

    private static final String BANNER_INSTANCE_KEY = "headline_page_primary_banner";

    private final KbRes kbApiRes;
    private final AppBannerCacheService cacheService;

    public HooloHomePageController(
            @Qualifier("kbApiRes") KbRes kbApiRes,
            AppBannerCacheService cacheService
    ) {
        this.kbApiRes = kbApiRes;
        this.cacheService = cacheService;
    }

    @ApiOperation("获取开吧时政首页顶部轮播图列表接口")
    @KbCheckSignature(type = KbSignType.THIRD_PARTY)
    @PostMapping(path = "/getHeadlineBannerList")
    public KbEntity<List<Banner>> getBannerList() {
        AppBannerCacheModel cache = cacheService
                .getCacheByInstance(9, null, BANNER_INSTANCE_KEY).orElse(null);
        if (cache == null) {
            log.info("hoolo home page banner instance cache not found");
            return kbApiRes.msg(KbCode.RESOURCE_NOT_FOUND, "数据跑丢了(1)");
        }
        if (cache.isValid()) {
            if (cache.getOnlineBannerList() == null || cache.getOnlineBannerList().isEmpty()) {
                return kbApiRes.ok(convertBannerList(cache.getDefaultBannerList()));
            } else {
                return kbApiRes.ok(convertBannerList(cache.getOnlineBannerList()));
            }
        } else {
            log.info("hoolo home page banner instance cache invalid");
            return kbApiRes.msg(KbCode.RESOURCE_NOT_FOUND, "数据跑丢了(2)");
        }
    }

    private List<Banner> convertBannerList(List<BannerImageModel> models) {
        return models.stream()
                .map(model -> {
                    String url = AppActionUtils.getPageUrlByAction(model.getAction(), model.getActionParams(), true);
                    if (url == null) {
                        return null;
                    } else  {
                        Banner banner = Mapper.map(model, Banner.class);
                        banner.link = url;
                        return banner;
                    }
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    @Data
    @NoArgsConstructor
    public static class Banner {

        /** 跳转链接 */
        private String link;

        /** 图片 url. */
        private String url;

        /** 图片格式. {@link KbImageFormat} */
        private String format;

        /** 标题. 可为空. */
        private String title;

        /** 副标题. 可为空. */
        private String subtitle;

        /** 内容是否可播放, 用于前端展示播放按钮 */
        private Boolean isPlayable;

        /** 内容是否在直播, 用于前端展示正在直播动画 */
        private Boolean isLive;

        /** 自动翻页的间隔时间, 单位秒. */
        private Integer autoplay;

    }

}
