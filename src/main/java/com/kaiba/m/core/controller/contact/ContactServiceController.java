package com.kaiba.m.core.controller.contact;

import com.kaiba.lib.base.assembler.Mapper;
import com.kaiba.lib.base.constant.contact.ContactField;
import com.kaiba.lib.base.constant.contact.ContactFixedGroup;
import com.kaiba.lib.base.constant.contact.ContactState;
import com.kaiba.lib.base.domain.AttrUpdater;
import com.kaiba.lib.base.domain.contact.ContactGroupModel;
import com.kaiba.lib.base.domain.contact.ContactModel;
import com.kaiba.lib.base.domain.contact.ContactModifyModel;
import com.kaiba.lib.base.domain.contact.ContactRelationModel;
import com.kaiba.lib.base.middleware.KbRes;
import com.kaiba.lib.base.response.KbCode;
import com.kaiba.lib.base.response.KbEntity;
import com.kaiba.lib.base.response.KbException;
import com.kaiba.lib.base.service.IContactService;
import com.kaiba.lib.base.service.IUserService;
import com.kaiba.m.core.domain.contact.Contact;
import com.kaiba.m.core.domain.contact.ContactGroup;
import com.kaiba.m.core.domain.contact.ContactRelation;
import com.kaiba.m.core.service.contact.ContactService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.*;
import java.util.stream.Collectors;

/**
 * author: lyux
 * date: 2021-12-10
 */
@Slf4j
@RestController
public class ContactServiceController implements IContactService {

    private final KbRes kbRes;
    private final ContactService contactService;
    private final IUserService userService;

    public ContactServiceController(
            KbRes kbRes,
            ContactService contactService,
            IUserService userService) {
        this.kbRes = kbRes;
        this.contactService = contactService;
        this.userService = userService;
    }

    @Override
    public KbEntity<List<ContactModel>> getUserTemplateAddressList(
            Integer userId, Integer[] state, Integer page, Integer pageSize) {
        return getContactListByUserId(userId, ContactFixedGroup.USER_ADDRESS_TEMPLATE.getKey(), state, page, pageSize);
    }

    @Override
    public KbEntity<ContactModel> getUserTemplateAddress(Integer userId, String contactId) {
        return contactService
                .getContactById(contactId)
                .filter(contact -> userId.equals(contact.getUserId()))
                .map(ContactServiceController::contact2model)
                .map(kbRes::ok)
                .orElse(kbRes.err(KbCode.RESOURCE_NOT_FOUND));
    }

    @Override
    public KbEntity<ContactModel> getDefaultUserTemplateAddress(Integer userId) {
        return contactService
                .getFirstContactByUserIdAndGroup(userId, ContactFixedGroup.USER_ADDRESS_TEMPLATE.getKey())
                .filter(contact -> contact.getIsDefault() != null && contact.getIsDefault())
                .map(ContactServiceController::contact2model)
                .map(kbRes::ok)
                .orElse(kbRes.err(KbCode.RESOURCE_NOT_FOUND));
    }

    @Override
    public KbEntity<ContactModel> getFirstUserTemplateAddress(Integer userId) {
        return contactService
                .getFirstContactByUserIdAndGroup(userId, ContactFixedGroup.USER_ADDRESS_TEMPLATE.getKey())
                .map(ContactServiceController::contact2model)
                .map(kbRes::ok)
                .orElse(kbRes.err(KbCode.RESOURCE_NOT_FOUND));
    }

    @Override
    public KbEntity<Void> setUserTemplateAddressAsDefault(String contactId) {
        return updateContactAsDefault(contactId);
    }

    @Override
    public KbEntity<ContactModel> createUserTemplateAddress(ContactModifyModel address) {
        address.setGroupKey(ContactFixedGroup.USER_ADDRESS_TEMPLATE.getKey());
        if (address.getState() == null) {
            address.setState(ContactState.ENABLED.getValue());
        }
        return createContact(address);
    }

    @Override
    public KbEntity<ContactModel> createUserImplAddressFromTemplate(Integer userId, String contactId) {
        return createContactFromContact(
                userId, contactId, ContactFixedGroup.USER_ADDRESS_IMPL.getKey(), null, true);
    }

    @Override
    public KbEntity<ContactModel> createUserPhoneContactWithVCode(
            Integer userId, String phone, String vcode, String groupKey, String remark, Boolean createOnExist) {
        if (createOnExist != null && !createOnExist) {
            Optional<Contact> op = contactService
                    .getFirstUserPhoneContact(userId, groupKey, phone, ContactState.ENABLED);
            if (op.isPresent()) {
                return kbRes.ok(contact2model(op.get()));
            }
        }
        ContactModifyModel createModel = new ContactModifyModel();
        createModel.setGroupKey(groupKey);
        createModel.setUserId(userId);
        createModel.setPhone(phone);
        createModel.setRemark(remark);
        createModel.setVcode(vcode);
        return createContact(createModel);
    }

    @Override
    public KbEntity<ContactModel> createUserPhoneContact(
            Integer userId, String phone, String groupKey, String remark, Boolean createOnExist) {
        return createUserPhoneContactWithVCode(userId, phone, null, groupKey, remark, createOnExist);
    }

    @Override
    public KbEntity<ContactModel> createUserPhoneHistory(Integer userId, String phone) {
        return createUserPhoneContact(
                userId, phone, ContactFixedGroup.USER_PHONE_HISTORY.getKey(), null, true);
    }

    @Override
    public KbEntity<ContactModel> createUserPhoneContactFromUser(
            Integer userId, String groupKey, String remark, Boolean createOnExist) {
        return userService.getMobileById(userId).data()
                .map(phone -> createUserPhoneContact(userId, phone, groupKey, remark, createOnExist))
                .orElse(kbRes.err(KbCode.USER_NOT_BIND_MOBILE));
    }

    @Override
    public KbEntity<ContactModel> createUserPhoneContactFromContact(
            Integer userId, String contactId, String groupKey, String remark) {
        return createContactFromContact(
                userId, contactId, groupKey, remark, false);
    }

    @Override
    public KbEntity<List<String>> getUserPhoneCandidateList(Integer userId) {
        Set<String> phoneFromAddrTemplate = contactService
                .getContactPageByUserId(
                        userId,
                        ContactFixedGroup.USER_ADDRESS_TEMPLATE.getKey(),
                        ContactState.STATE_ARR_ACTIVE,
                        1, 20)
                .getContent().stream()
                .map(Contact::getPhone)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());
        List<String> returnList = new ArrayList<>(phoneFromAddrTemplate.size() + 1);
        returnList.addAll(phoneFromAddrTemplate);
        userService.getMobileById(userId).data().ifPresent(returnList::add);
        return kbRes.ok(returnList);
    }

    @Override
    public KbEntity<ContactModel> createContact(ContactModifyModel createModel) {
        ContactGroup group = contactService.getGroupFromCacheByKey(createModel.getGroupKey())
                .orElseThrow(() -> new KbException(KbCode.RESOURCE_NOT_FOUND, "group not exists").li());
        boolean setAsDefault = group.isAllowDefault()
                && createModel.getIsDefault() != null && createModel.getIsDefault();
        createModel.setIsDefault(null);
        Contact tobeCreated = Mapper.map(createModel, Contact.class);

        if (createModel.getEmergencyContactId() == null && createModel.getEmergencyContactPhone() != null) {
            // 创建紧急联系人
            String emergencyContactId = contactService.createEmergencyContact(
                    createModel.getEmergencyContactName(), createModel.getEmergencyContactPhone()).getId();
            tobeCreated.setEmergencyContactId(emergencyContactId);
        }

        Contact contact;
        try {
            contact = contactService.createContact(tobeCreated, group, createModel.getVcode());
        } catch (KbException e) {
            if (createModel.getIsSaveDraftOnInvalid() != null
                    && createModel.getIsSaveDraftOnInvalid()
                    && e.getCode() == KbCode.ILLEGAL_ARGUMENT) {
                // 因创建参数问题导致创建失败, 尝试保存为草稿
                tobeCreated.setState(ContactState.DRAFT.getValue());
                contact = contactService.createContact(tobeCreated, null);
            } else {
                throw e;
            }
        }

        if (setAsDefault) {
            // 先创建, 再处理"是否默认"参数
            contactService.updateContactIsDefault(contact, true, false);
        }

        // 建立关系
        if (contact != null && contact.getId() != null
                && createModel.getSubjectRelations() != null
                && createModel.getSubjectRelations().size() != 0) {
            String contactId = contact.getId();
            List<ContactRelationModel> relations = createModel.getSubjectRelations().stream()
                    .map(subjectRelation -> {
                        ContactRelationModel m = new ContactRelationModel();
                        m.setContactId(contactId);
                        m.setSubjectContactId(subjectRelation.getContactId());
                        m.setRelation(subjectRelation.getRelation());
                        return m;
                    })
                    .collect(Collectors.toList());
            contactService.upsertRelationList(relations);
        }
        return kbRes.ok(contact2model(contact));
    }

    @Override
    public KbEntity<ContactModel> createContactFromContact(
            Integer userId, String contactId, String groupKey, String remark, Boolean refreshTemplate) {
        if (groupKey == null) {
            return kbRes.err(KbCode.REQUEST_PARAM_INVALID,
                    "groupKey null", "请指定分组信息");
        }
        Contact contact = contactService.getContactById(contactId)
                .orElseThrow(() -> new KbException(KbCode.RESOURCE_NOT_FOUND));
        if (userId != null && !userId.equals(contact.getUserId())) {
            return kbRes.err(KbCode.REQUEST_PARAM_INVALID,
                    "userId not the same", "指定模板不属于该用户");
        }
        if (refreshTemplate == null) {
            refreshTemplate = false;
        }
        ContactModifyModel model = Mapper.map(contact, ContactModifyModel.class);
        model.setState(ContactState.ENABLED.getValue());
        model.setIsDefault(null);
        model.setGroupKey(groupKey);
        KbEntity<ContactModel> entity = createContact(model);
        if (refreshTemplate) {
            refreshContact(contactId); // 提升最近使用的模板地址的列表排序
        }
        return entity;
    }

    @Override
    public KbEntity<ContactModel> updateContactData(ContactModifyModel modifyModel) {
        Contact contact = contactService.getContactById(modifyModel.getId())
                .orElseThrow(() -> new KbException(KbCode.RESOURCE_NOT_FOUND));
        modifyModel.setGroupKey(null); // 创建后不允许修改 groupKey
        modifyModel.setState(null); // 不应该在这个接口中改变状态
        boolean modifyIsDefault = modifyModel.getIsDefault() != null && modifyModel.getIsDefault();
        boolean attrNotNull = contact.getAttr() != null;
        modifyModel.setIsDefault(null);
        Mapper.map(modifyModel, contact);
        if (attrNotNull && (modifyModel.getAttrReplace() == null || !modifyModel.getAttrReplace())) {
            contact.getAttr().putAll(modifyModel.getAttr());
        } else {
            contact.setAttr(modifyModel.getAttr());
        }
        Contact modified = contactService.updateContact(contact, modifyModel.getVcode());
        if (modifyIsDefault) {
            // 先修改, 再处理"是否默认"参数
            contactService.updateContactIsDefault(modified, true, false);
        }
        // 建立关系
        if (contact.getId() != null
                && modifyModel.getSubjectRelations() != null
                && modifyModel.getSubjectRelations().size() != 0) {
            List<ContactRelationModel> relations = modifyModel.getSubjectRelations().stream()
                    .map(subjectRelation -> {
                        ContactRelationModel m = new ContactRelationModel();
                        m.setContactId(contact.getId());
                        m.setSubjectContactId(subjectRelation.getContactId());
                        m.setRelation(subjectRelation.getRelation());
                        return m;
                    })
                    .collect(Collectors.toList());
            contactService.upsertRelationList(relations);
        }
        return kbRes.ok(contact2model(modified));
    }

    @Override
    public KbEntity<Void> updateContactState(String contactId, Integer state) {
        ContactState s = ContactState.resolveValue(state).orElseThrow(() ->
                new KbException(KbCode.REQUEST_PARAM_INVALID, "unknown state: " + state).r("错误的状态值").li());
        contactService.updateContactState(contactId, s);
        return kbRes.ok();
    }

    @Override
    public KbEntity<Void> updateContactAsDefault(String contactId) {
        Contact contact = contactService.getContactById(contactId)
                .orElseThrow(() -> new KbException(KbCode.RESOURCE_NOT_FOUND).li());
        contactService.updateContactIsDefault(contact, true, true);
        return kbRes.ok();
    }

    @Override
    public KbEntity<ContactModel> updateContactAttr(AttrUpdater attrUpdater) {
        if (attrUpdater.isAttrEmpty()) {
            return kbRes.err(KbCode.REQUEST_PARAM_INVALID, "nothing to update");
        }
        Contact contact = contactService.updateContactAttr(
                attrUpdater.getId(), attrUpdater.getAttr(), attrUpdater.isReplaceAttr());
        return kbRes.ok(contact2model(contact));
    }

    @Override
    public KbEntity<ContactModel> updateContactAttrKeyValue(String contactId, String key, String value) {
        Contact contact = contactService.updateContactAttrKeyValue(contactId, key, value);
        return kbRes.ok(contact2model(contact));
    }

    @Override
    public KbEntity<Void> cancelContactAsDefault(String contactId) {
        Contact contact = contactService.getContactById(contactId)
                .orElseThrow(() -> new KbException(KbCode.RESOURCE_NOT_FOUND).li());
        contactService.updateContactIsDefault(contact, false, true);
        return kbRes.ok();
    }

    @Override
    public KbEntity<Void> refreshContact(String contactId) {
        contactService.refreshContact(contactId);
        return kbRes.ok();
    }

    @Override
    public KbEntity<Void> deleteContact(String contactId) {
        contactService.deleteContact(contactId);
        return kbRes.ok();
    }

    @Override
    public KbEntity<ContactModel> getContactById(String contactId) {
        return contactService
                .getContactById(contactId)
                .map(ContactServiceController::contact2model)
                .map(kbRes::ok)
                .orElse(kbRes.err(KbCode.RESOURCE_NOT_FOUND));
    }

    @Override
    public KbEntity<List<ContactModel>> getContactListByIdIn(String[] contactIds) {
        return kbRes.ok(contactService
                .getContactListByIdIn(Arrays.asList(contactIds)).stream()
                .map(ContactServiceController::contact2model)
                .collect(Collectors.toList()));
    }

    @Override
    public KbEntity<List<ContactModel>> getContactListByUserId(
            Integer userId, String groupKey, Integer[] state, Integer page, Integer pageSize) {
        return kbRes.ok(contactService
                .getContactPageByUserId(userId, groupKey, state, page, pageSize)
                .map(ContactServiceController::contact2model));
    }

    @Override
    public KbEntity<List<ContactModel>> getContactListByPhone(
            String phone, String groupKey, Integer[] state, Integer page, Integer pageSize) {
        return kbRes.ok(contactService
                .getContactPageByPhone(phone, groupKey, state, page, pageSize)
                .map(ContactServiceController::contact2model));
    }

    @Override
    public KbEntity<List<ContactModel>> getContactListByGroup(
            String groupKey, Integer[] state, Integer page, Integer pageSize) {
        return kbRes.ok(contactService
                .getContactPageByGroup(groupKey, state, page, pageSize)
                .map(ContactServiceController::contact2model));
    }

    @Override
    public KbEntity<Boolean> existsByContactGroupAndUserId(Integer userId, String groupKey) {
        return kbRes.ok(contactService.isContactExistsByUserIdAndGroupKey(userId, groupKey));
    }

    @Override
    public KbEntity<ContactRelationModel> getRelation(
            String subjectContactId, String contactId) {
        return contactService.getRelation(subjectContactId, contactId)
                .map(ContactServiceController::relation2model)
                .map(kbRes::ok)
                .orElse(kbRes.ok());
    }

    @Override
    public KbEntity<List<ContactRelationModel>> getRelationListByContact(
            String contactId, String[] subjectContactIds, Integer page, Integer pageSize) {
        if (subjectContactIds == null || subjectContactIds.length == 0) {
            return kbRes.ok(contactService
                    .getRelationPageByContact(contactId, page, pageSize)
                    .map(ContactServiceController::relation2model));
        } else {
            return kbRes.ok(contactService
                    .getRelationListByContact(contactId, Arrays.asList(subjectContactIds)).stream()
                    .map(ContactServiceController::relation2model)
                    .collect(Collectors.toList()));
        }
    }

    @Override
    public KbEntity<List<ContactModel>> getContactListWithContactRelation(
            String contactId, String[] subjectContactIds) {
        List<ContactRelation> relations =
                contactService.getRelationListByContact(contactId, Arrays.asList(subjectContactIds));
        return kbRes.ok(getContactListWithRelation(contactId, subjectContactIds, relations));
    }

    @Override
    public KbEntity<List<ContactRelationModel>> getRelationListBySubjectContact(
            String subjectContactId, String[] contactIds, Integer page, Integer pageSize) {
        if (contactIds == null || contactIds.length == 0) {
            return kbRes.ok(contactService
                    .getRelationPageBySubjectContact(subjectContactId, page, pageSize)
                    .map(ContactServiceController::relation2model));
        } else {
            return kbRes.ok(contactService
                    .getRelationListBySubjectContact(subjectContactId, Arrays.asList(contactIds)).stream()
                    .map(ContactServiceController::relation2model)
                    .collect(Collectors.toList()));
        }
    }

    @Override
    public KbEntity<List<ContactModel>> getContactListWithSubjectRelation(String subjectContactId, String[] contactIds) {
        List<ContactRelation> relations =
                contactService.getRelationListBySubjectContact(subjectContactId, Arrays.asList(contactIds));
        return kbRes.ok(getContactListWithRelation(subjectContactId, contactIds, relations));
    }

    @Override
    public KbEntity<ContactRelationModel> setRelation(String subjectContactId, String contactId, String relation) {
        ContactRelation cr = contactService.upsertRelation(subjectContactId, contactId, relation);
        return kbRes.ok(relation2model(cr));
    }

    @Override
    public KbEntity<List<ContactRelationModel>> setRelationListByBody(List<ContactRelationModel> relations) {
        return kbRes.ok(contactService
                .upsertRelationList(relations).stream()
                .map(ContactServiceController::relation2model)
                .collect(Collectors.toList()));
    }

    @Override
    public KbEntity<Void> removeRelation(String relationId) {
        contactService.removeRelation(relationId);
        return kbRes.ok();
    }

    @Override
    public KbEntity<Void> removeRelationByContact(String subjectContactId, String contactId) {
        contactService.removeRelationByContact(subjectContactId, contactId);
        return kbRes.ok();
    }

    @Override
    public KbEntity<List<ContactGroupModel>> getContactGroupList(
            Long[] constraint, Integer page, Integer pageSize) {
        return kbRes.ok(contactService.getGroupPage(constraint, page, pageSize)
                .map(ContactServiceController::group2model));
    }

    @Override
    public KbEntity<ContactGroupModel> getContactGroup(String groupId, String groupKey) {
        if (groupId == null && groupKey == null) {
            return kbRes.err(KbCode.REQUEST_PARAM_INVALID,
                    "id and key null", "id 和 key 应至少提供一个");
        } else {
            return contactService.getGroup(groupId, groupKey)
                    .map(ContactServiceController::group2model)
                    .map(kbRes::ok)
                    .orElse(kbRes.err(KbCode.RESOURCE_NOT_FOUND));
        }
    }

    @Override
    public KbEntity<ContactGroupModel> createContactGroup(
            String groupKey, Long constraint, Boolean mutable,
            Boolean allowDefault, Integer limitPerUser, String description) {
        ContactGroup group = new ContactGroup();
        group.setKey(groupKey);
        group.setConstraint(constraint);
        group.setMutable(mutable);
        group.setAllowDefault(allowDefault);
        group.setLimitPerUser(limitPerUser);
        group.setDescription(description);
        return kbRes.ok(group2model(contactService.createGroup(group)));
    }

    @Override
    public KbEntity<ContactGroupModel> updateContactGroupConstraint(
            String groupKey, Long constraint, Boolean scanContacts) {
        ContactGroup group = contactService.updateGroupConstraint(groupKey, constraint);
        if (scanContacts != null && scanContacts) {
            contactService.scanContactByGroupConstraintAsync(group);
        }
        return kbRes.ok(group2model(group));
    }

    @Override
    public KbEntity<ContactGroupModel> updateContactGroupMutable(String groupKey, Boolean mutable) {
        ContactGroup group = contactService.updateMutableByKey(groupKey, mutable);
        return kbRes.ok(group2model(group));
    }

    @Override
    public KbEntity<Void> createOrUpdateFixedGroups(String secret) {
        if ("4676184ce522ae4ff1632bfff70872f6".equals(secret)) {
            contactService.updateFixedGroups();
        }
        return kbRes.ok();
    }

    @RequestMapping(path = "/contact/generateConstraint", method = RequestMethod.GET)
    KbEntity<Long> generateConstraint(
            @RequestParam(required = false, defaultValue = "false") Boolean requireUserId,
            @RequestParam(required = false, defaultValue = "false") Boolean requireName,
            @RequestParam(required = false, defaultValue = "false") Boolean requireGender,
            @RequestParam(required = false, defaultValue = "false") Boolean requirePhone,
            @RequestParam(required = false, defaultValue = "false") Boolean requirePhoneWithVCode,
            @RequestParam(required = false, defaultValue = "false") Boolean requireTels,
            @RequestParam(required = false, defaultValue = "false") Boolean requireEmail,
            @RequestParam(required = false, defaultValue = "false") Boolean requireEmailWithVCode,
            @RequestParam(required = false, defaultValue = "false") Boolean requireIDCard,
            @RequestParam(required = false, defaultValue = "false") Boolean requireEmergencyContact,
            @RequestParam(required = false, defaultValue = "false") Boolean requireAddress,
            @RequestParam(required = false, defaultValue = "false") Boolean requireAddressCoordinate,
            @RequestParam(required = false, defaultValue = "false") Boolean requireAddressCode,
            @RequestParam(required = false, defaultValue = "false") Boolean requireAddressDistrict
    ) {
        long constraint = 0L;
        constraint |= (requireUserId ? ContactField.USER_ID.getValue() : 0L);
        constraint |= (requireName ? ContactField.NAME.getValue() : 0L);
        constraint |= (requireGender ? ContactField.GENDER.getValue() : 0L);
        constraint |= (requirePhone ? ContactField.PHONE.getValue() : 0L);
        constraint |= (requirePhoneWithVCode ? ContactField.PHONE_WITH_VCODE.getValue() : 0L);
        constraint |= (requireTels ? ContactField.TELS.getValue() : 0L);
        constraint |= (requireEmail ? ContactField.EMAIL.getValue() : 0L);
        constraint |= (requireEmailWithVCode ? ContactField.EMAIL_WITH_VCODE.getValue() : 0L);
        constraint |= (requireIDCard ? ContactField.ID_CARD.getValue() : 0L);
        constraint |= (requireEmergencyContact ? ContactField.EMERGENCY_CONTACT.getValue() : 0L);
        constraint |= (requireAddress ? ContactField.ADDRESS.getValue() : 0L);
        constraint |= (requireAddressCoordinate ? ContactField.ADDRESS_COORDINATE.getValue() : 0L);
        constraint |= (requireAddressCode ? ContactField.ADDRESS_CODE.getValue() : 0L);
        constraint |= (requireAddressDistrict ? ContactField.ADDRESS_DISTRICT.getValue() : 0L);
        return kbRes.ok(constraint);
    }

    @RequestMapping(path = "/contact/parseConstraint", method = RequestMethod.GET)
    KbEntity<Map<String, String>> parseConstraint(
            @RequestParam() Long constraint
    ) {
        Map<String, String> result = new HashMap<>();
        for (ContactField field : ContactField.values()) {
            if (field.isOn(constraint)) {
                result.put(field.name(), "on");
            }
        }
        return kbRes.ok(result);
    }

    // ------------------------------------------------------

    private List<ContactModel> getContactListWithRelation(String id, String[] ids, List<ContactRelation> relationList) {
        int size = ids == null ? 1 : ids.length + 1;
        List<String> list = new ArrayList<>(size);
        if (id != null) {
            list.add(id);
        }
        if (ids != null && ids.length != 0) {
            Set<String> set = new HashSet<>(ids.length);
            for (String subjectContactId : ids) {
                if (id != null && !set.contains(id)) {
                    set.add(subjectContactId);
                }
            }
            list.addAll(set);
        }
        if (list.size() == 0) {
            return Collections.emptyList();
        }

        List<ContactRelationModel> relations;
        Map<String, ContactRelationModel> relationMap;
        if (relationList == null || relationList.size() == 0) {
            relations = null;
            relationMap = Collections.emptyMap();
        } else {
            relations = new ArrayList<>(relationList.size());
            relationMap = new HashMap<>(relationList.size());
            for (ContactRelation r : relationList) {
                ContactRelationModel m = relation2model(r);
                relationMap.put(m.getSubjectContactId(), m);
                relations.add(m);
            }
        }

        return contactService.getContactListByIdIn(list).stream()
                .map(ContactServiceController::contact2model)
                .peek(c -> {
                    if (c.getId().equals(id)) {
                        c.setRelations(relations);
                    } else {
                        ContactRelationModel relation = relationMap.get(c.getId());
                        if (relation != null) {
                            c.setRelationRemark(relation.getRelation());
                        }
                    }
                })
                .collect(Collectors.toList());
    }

    private static ContactModel contact2model(Contact contact) {
        return Mapper.map(contact, ContactModel.class);
    }

    private static ContactGroupModel group2model(ContactGroup group) {
        return Mapper.map(group, ContactGroupModel.class);
    }

    private static ContactRelationModel relation2model(ContactRelation relation) {
        return Mapper.map(relation, ContactRelationModel.class);
    }

}
