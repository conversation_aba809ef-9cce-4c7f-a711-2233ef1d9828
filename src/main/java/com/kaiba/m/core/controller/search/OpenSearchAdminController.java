package com.kaiba.m.core.controller.search;

import com.kaiba.lib.base.assembler.Mapper;
import com.kaiba.lib.base.constant.KbHeader;
import com.kaiba.lib.base.constant.opensearch.SearchModule;
import com.kaiba.lib.base.domain.note.NoteModel;
import com.kaiba.lib.base.domain.opensearch.SearchModuleModel;
import com.kaiba.lib.base.domain.opensearch.SearchTopModel;
import com.kaiba.lib.base.middleware.KbRes;
import com.kaiba.lib.base.response.KbCode;
import com.kaiba.lib.base.response.KbEntity;
import com.kaiba.lib.base.service.INoteService;
import com.kaiba.lib.base.service.IOpenSearchService;
import com.kaiba.m.core.domain.opensearch.SearchTop;
import com.kaiba.m.core.service.opensearch.OpenSearchDataService;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.Collections;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2023/08/22 17:12
 */
@Slf4j
@RestController()
@RequestMapping("/backend/openSearch/search")
public class OpenSearchAdminController {

    private final KbRes kbRes;
    private final IOpenSearchService searchService;
    private final INoteService noteService;
    private final OpenSearchDataService dataService;

    public OpenSearchAdminController(
            KbRes kbRes,
            IOpenSearchService searchService,
            INoteService noteService,
            OpenSearchDataService dataService
    ) {
        this.kbRes = kbRes;
        this.searchService = searchService;
        this.noteService = noteService;
        this.dataService = dataService;
    }

    @ApiOperation("添加搜索置顶内容")
    @PostMapping(path = "/addNewsToTopList")
    public KbEntity<SearchTopModel> addToTopList(
            @RequestHeader(name = KbHeader.KB_USER_ID) Integer userId,
            @RequestBody SearchTopModel newsModel
    ) {
        newsModel.setSearchType(SearchModule.TOP.getValue());
        return kbRes.ok(entity2Model(dataService.addToTopList(userId, newsModel)));
    }

    @ApiOperation("修改搜索置顶内容")
    @PostMapping(path = "/editTopById")
    public KbEntity<SearchTopModel> editTopById(
            @RequestHeader(name = KbHeader.KB_USER_ID) Integer userId,
            @RequestBody SearchTopModel newsModel
    ) {
        newsModel.setSearchType(SearchModule.TOP.getValue());
        return kbRes.ok(entity2Model(dataService.edit(userId, newsModel)));
    }

    @ApiOperation("修改搜索置顶状态")
    @PostMapping(path = "/editTopStateAsOnline")
    public KbEntity<SearchTopModel> editTopStateAsOnline(
            @RequestHeader(name = KbHeader.KB_USER_ID) Integer userId,
            @RequestParam String id
    ) {
        log.info("edit top state 2 online, by user: {}", userId);
        return kbRes.ok(entity2Model(dataService.editTopStateAsOnline(id)));
    }

    @ApiOperation("修改搜索置顶状态")
    @PostMapping(path = "/editTopStateAsOffline")
    public KbEntity<SearchTopModel> editTopStateAsOffline(
            @RequestHeader(name = KbHeader.KB_USER_ID) Integer userId,
            @RequestParam String id
    ) {
        log.info("edit top state 2 offline, by user: {}", userId);
        return kbRes.ok(entity2Model(dataService.editTopStateAsOffline(id)));
    }

    @ApiOperation("获取搜索置顶列表")
    @PostMapping(path = "/getSearchTopList")
    public KbEntity<List<SearchTopModel>> getSearchTopList(
            @RequestParam(required = false) String queryParams,
            @RequestParam(required = false) Integer state,
            @RequestParam(required = false, defaultValue = "1") Integer page,
            @RequestParam(required = false, defaultValue = "20") Integer pageSize
    ) {
        return kbRes.ok(dataService.getSearchTopList(queryParams, state, page, pageSize).map(this::entity2Model));
    }

    @ApiOperation("搜索帖子")
    @PostMapping(path = "/searchNote")
    public KbEntity<List<NoteModel>> searchNote(
            @RequestParam(required = false) Integer siteId,
            @RequestParam(required = false) String queryParam,
            @RequestParam(required = false) String threadId,
            @RequestParam(required = false) Integer area,
            @RequestParam(required = false) Integer userId,
            @RequestParam(required = false) Long startTime,
            @RequestParam(required = false) Long endTime,
            @RequestParam(required = false) Boolean containImage,
            @RequestParam(required = false) Boolean containAudio,
            @RequestParam(required = false) Boolean containVideo,
            @RequestParam(required = false) Boolean containRemark,
            @RequestParam(required = false, defaultValue = "1") Integer page,
            @RequestParam(required = false, defaultValue = "20") Integer pageSize
    ) {
        if (siteId == null && threadId == null) {
            return kbRes.err(KbCode.ILLEGAL_ARGUMENT, null, "必须传入电台id或板块id");
        }
        KbEntity<List<SearchModuleModel>> searchEntity = searchService.searchNote(
                siteId, queryParam, threadId, area, userId, startTime, endTime, true,
                containImage, containAudio, containVideo, containRemark, page, pageSize);
        List<SearchModuleModel> models = searchEntity.data().orElse(Collections.emptyList());
        if(!models.isEmpty()) {
            String[] noteIds = models.stream().map(t -> t.getId().toString()).toArray(String[]::new);
            KbEntity<List<NoteModel>> entity = noteService.getNoteListIn(
                noteIds, userId, null, 154618828287L);
            Collections.reverse(entity.getData());
            entity.setTotalCount(searchEntity.getTotalCount());
            entity.setTotalPage(searchEntity.getTotalPage());
            return entity;
        }
        return kbRes.ok(Collections.emptyList());
    }

    @ApiOperation("根据id查找搜索引擎内容")
    @PostMapping(path = "/searchById")
    public KbEntity<SearchModuleModel> searchById(
            @RequestParam Integer type,
            @RequestParam String id
    ) {
        return searchService.searchById(type, id);
    }

    // ------------------------------------------------------------------------

    private SearchTopModel entity2Model(SearchTop top)  {
        SearchTopModel model = Mapper.map(top, SearchTopModel.class);
        model.setStartTime(Optional.ofNullable(top.getStartTime())
            .map(time -> time / 1000).orElse(null));
        model.setEndTime(Optional.ofNullable(top.getEndTime())
            .map(time -> time / 1000).orElse(null));
        model.setTypeName(top.getTypeName());
        return model;
    }
}
