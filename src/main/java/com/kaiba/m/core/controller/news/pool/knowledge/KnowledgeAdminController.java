package com.kaiba.m.core.controller.news.pool.knowledge;

import com.kaiba.lib.base.constant.KbHeader;
import com.kaiba.lib.base.domain.news.pool.knowledge.BasedAnswerCreateModel;
import com.kaiba.lib.base.domain.news.pool.knowledge.BasedAnswerModel;
import com.kaiba.lib.base.domain.news.pool.knowledge.KnowledgeCategoryModel;
import com.kaiba.lib.base.middleware.KbRes;
import com.kaiba.lib.base.response.KbEntity;
import com.kaiba.lib.base.response.KbException;
import com.kaiba.m.core.controller.news.pool.knowledge.mapper.KnowledgeConMapping;
import com.kaiba.m.core.domain.knowledge.KnowledgeBase;
import com.kaiba.m.core.service.news.pool.knowledge.IBasedAnswerService;
import com.kaiba.m.core.service.news.pool.knowledge.IKnowledgeBaseService;
import com.kaiba.m.core.service.news.pool.knowledge.IKnowledgeCategoryService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.util.List;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * Description: 知识库后台管理Controller
 * Author: ZM227
 * Date: 2025/1/6 14:25
 */
@Slf4j
@RestController
@RequestMapping("/admin/knowledge/modify")
@Api(tags = "知识库后台管理")
public class KnowledgeAdminController {

    @Resource
    private KbRes kbRes;
    @Resource
    private KnowledgeConMapping knowledgeConMapping;
    @Resource
    private IBasedAnswerService answerService;
    @Resource
    private IKnowledgeCategoryService categoryService;
    @Resource
    private IKnowledgeBaseService knowledgeBaseService;

    /**************** 知识点相关 ****************/
    @ApiOperation(value = "创建知识库实例", notes = "创建知识库实例")
    @PostMapping("/usr/createKnowledgeBase")
    public KbEntity<String> createKnowledgeBase(@RequestHeader(KbHeader.KB_USER_ID) Integer userId,
        @RequestBody() KnowledgeBase knowledgeBase) {
        try {
            return kbRes.ok(knowledgeBaseService.createKnowledgeBase(knowledgeBase));
        } catch (KbException e) {
            return kbRes.err(e.getCode(), e.getMessage(), e.getReadableMessage());
        }
    }

    /**************** 知识点相关 ****************/
//    @ApiOperation(value = "创建知识点记录", notes = "创建知识点记录")
//    @PostMapping("/usr/createKnowledge")
//    public KbEntity<String> createKnowledge(@RequestHeader(KbHeader.KB_USER_ID) Integer userId,
//        @RequestBody() KnowledgeCreateModel createModel) {
//        try {
//            if (StringUtils.isBlank(createModel.getArticleId()) && Objects.nonNull(
//                createModel.getArticleCreateModel())) {
//                // 集成Article创建
//                NewsArticle article = Mapper.map(createModel.getArticleCreateModel(),
//                    NewsArticle.class);
//                NewsArticle created = articleService.createArticle(userId, article);
//                createModel.setArticleId(created.getId());
//            }
//            createModel.setCreatorId(userId);
//            return kbRes.ok(knowledgeService.addKnowledge(createModel));
//        } catch (KbException e) {
//            return kbRes.err(e.getCode(), e.getMessage(), e.getReadableMessage());
//        }
//    }
//
//    @ApiOperation(value = "更新知识点记录", notes = "更新知识点记录")
//    @PostMapping("/usr/updateKnowledge")
//    public KbEntity<Boolean> updateKnowledge(@RequestHeader(KbHeader.KB_USER_ID) Integer userId,
//        @RequestBody() KnowledgeModel knowledgeModel) {
//        try {
//            knowledgeModel.setModifierId(userId);
//            if (StringUtils.isBlank(knowledgeModel.getKnowledgeId())) {
//                throw new KbException(KbCode.RESOURCE_NOT_FOUND, "knowledge not exist").r(
//                    "知识条目不存在");
//            }
//            return kbRes.ok(knowledgeService.updateKnowledge(knowledgeModel));
//        } catch (KbException e) {
//            return kbRes.err(e.getCode(), e.getMessage(), e.getReadableMessage());
//        }
//    }

    /**************** 知识库常用答案相关 ****************/
    @ApiOperation(value = "创建知识库常用答案", notes = "创建知识库常用答案")
    @PostMapping("/usr/createBasedAnswer")
    public KbEntity<BasedAnswerModel> createBasedAnswer(
        @RequestHeader(KbHeader.KB_USER_ID) Integer userId,
        @RequestBody BasedAnswerCreateModel model) {
        try {
            model.setCreatorId(userId);
            return kbRes.ok(
                knowledgeConMapping.answerDomainToModel(answerService.createBasedAnswer(model)));
        } catch (KbException e) {
            return kbRes.err(e.getCode(), e.getMessage(), e.getReadableMessage());
        }
    }

    @ApiOperation(value = "更新知识库常用答案", notes = "更新知识库常用答案")
    @PostMapping("/usr/updateBasedAnswer")
    public KbEntity<BasedAnswerModel> updateBasedAnswer(
        @RequestHeader(KbHeader.KB_USER_ID) Integer userId,
        @RequestBody BasedAnswerModel model) {
        try {
            model.setModifierId(userId);
            return kbRes.ok(
                knowledgeConMapping.answerDomainToModel(answerService.updateBasedAnswer(model)));
        } catch (KbException e) {
            return kbRes.err(e.getCode(), e.getMessage(), e.getReadableMessage());
        }
    }

    @ApiOperation(value = "修改知识库常用答案状态", notes = "修改知识库常用答案状态")
    @PostMapping("/usr/modifyBasedAnswerStatus")
    public KbEntity<BasedAnswerModel> modifyBasedAnswerStatus(
        @RequestHeader(KbHeader.KB_USER_ID) Integer userId,
        @RequestParam String answerId, @RequestParam Boolean isValid) {
        try {
            return kbRes.ok(knowledgeConMapping.answerDomainToModel(
                answerService.changeStatus(answerId, isValid)));
        } catch (KbException e) {
            return kbRes.err(e.getCode(), e.getMessage(), e.getReadableMessage());
        }
    }

    @ApiOperation(value = "修改知识库常用答案关键字", notes = "修改知识库常用答案关键字")
    @PostMapping("/usr/modifyBasedAnswerTags")
    public KbEntity<BasedAnswerModel> modifyBasedAnswerTags(
        @RequestHeader(KbHeader.KB_USER_ID) Integer userId,
        @RequestParam String answerId,
        @RequestParam(required = false) List<String> plusTags,
        @RequestParam(required = false) List<String> removeTags) {
        try {
            return kbRes.ok(knowledgeConMapping.answerDomainToModel(
                answerService.modifyTags(answerId, plusTags, removeTags)));
        } catch (KbException e) {
            return kbRes.err(e.getCode(), e.getMessage(), e.getReadableMessage());
        }
    }

    /**************** 知识分类相关 ****************/
    @ApiOperation(value = "创建知识库分类", notes = "创建知识库分类")
    @PostMapping("/usr/createCategory")
    public KbEntity<String> createCategory(
        @RequestHeader(KbHeader.KB_USER_ID) Integer userId,
        @RequestBody KnowledgeCategoryModel model) {
        try {
            return kbRes.ok(categoryService.createCategory(model));
        } catch (KbException e) {
            return kbRes.err(e.getCode(), e.getMessage(), e.getReadableMessage());
        }
    }

    @ApiOperation(value = "更新知识库分类", notes = "更新知识库分类")
    @PostMapping("/usr/updateCategory")
    public KbEntity<KnowledgeCategoryModel> updateCategory(
        @RequestHeader(KbHeader.KB_USER_ID) Integer userId,
        @RequestBody KnowledgeCategoryModel model) {
        try {
            return kbRes.ok(
                knowledgeConMapping.categoryDomainToModel(categoryService.updateCategory(model)));
        } catch (KbException e) {
            return kbRes.err(e.getCode(), e.getMessage(), e.getReadableMessage());
        }
    }

    @ApiOperation(value = "删除知识库分类", notes = "删除知识库分类")
    @PostMapping("/usr/deleteCategory")
    public KbEntity<KnowledgeCategoryModel> deleteCategory(
        @RequestHeader(KbHeader.KB_USER_ID) Integer userId,
        @RequestParam String categoryId) {
        try {
            KnowledgeCategoryModel model = new KnowledgeCategoryModel();
            model.setCategoryId(categoryId);
            model.setStatus(0);
            return kbRes.ok(
                knowledgeConMapping.categoryDomainToModel(categoryService.updateCategory(model)));
        } catch (KbException e) {
            return kbRes.err(e.getCode(), e.getMessage(), e.getReadableMessage());
        }
    }

}
