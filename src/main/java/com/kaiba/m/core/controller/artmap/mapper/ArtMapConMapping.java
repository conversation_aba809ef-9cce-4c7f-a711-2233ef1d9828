package com.kaiba.m.core.controller.artmap.mapper;

import com.kaiba.lib.base.domain.artmap.VenuesModel;
import com.kaiba.lib.base.domain.artmap.VenuesQueryModel;
import com.kaiba.m.core.domain.artmap.Question;
import com.kaiba.m.core.domain.artmap.Recommend;
import com.kaiba.m.core.domain.artmap.Venues;
import com.kaiba.m.core.model.artmap.QuestionModel;
import com.kaiba.m.core.model.artmap.RecommendModel;
import com.kaiba.m.core.model.artmap.dto.VenuesQueryDTO;
import com.kaiba.m.core.util.PageUtils;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.util.List;
import java.util.Objects;
import org.mapstruct.AfterMapping;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.Mappings;
import org.mapstruct.NullValueCheckStrategy;
import org.springframework.data.domain.Sort;
import org.springframework.data.domain.Sort.Direction;
import org.springframework.data.mongodb.core.geo.GeoJsonPoint;

/**
 * Description: 艺术地图Controller层映射器
 * Author: ZM227
 * Date: 2025/6/3 16:56
 */
@Mapper(componentModel = "spring",
    nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
    imports = {
        PageUtils.class,
        Sort.class,
        Direction.class,
        LocalTime.class,
        Instant.class,
        ZoneId.class,
        LocalDateTime.class
    }
)
public interface ArtMapConMapping {

    Venues venuesModelToDomain(VenuesModel venuesModel);

    @AfterMapping
    default void venuesModelToDomain(@MappingTarget Venues venues,
        VenuesModel venuesModel) {
        if (Objects.nonNull(venuesModel.getLongitude()) && Objects.nonNull(
            venuesModel.getLatitude())) {
            venues.setLocation(
                new GeoJsonPoint(venuesModel.getLongitude(), venuesModel.getLatitude()));
        }
    }

    VenuesModel venuesDomainToModel(Venues venues);

    @AfterMapping
    default void venuesDomainToModel(@MappingTarget VenuesModel venuesModel,
        Venues venues) {
        if (Objects.nonNull(venues.getLocation())) {
            venuesModel.setLongitude(venues.getLocation().getX());
            venuesModel.setLatitude(venues.getLocation().getY());
        }
    }

    List<VenuesModel> venuesDomainToModelList(List<Venues> venues);

    @Mappings({
        @Mapping(target = "pageable", expression = "java(PageUtils.ofDefault(query.getPage(), query.getPageSize(), Sort.by(Direction.DESC, \"createTime\")))"),
    })
    VenuesQueryDTO venuesModelToQueryDTO(VenuesQueryModel query);

    Recommend recommendModelToDomain(RecommendModel recommendModel);

    RecommendModel recommendDomainToModel(Recommend recommend);

    @Mappings({
        @Mapping(target = "validStartTime", expression = "java(LocalDateTime"
            + ".of(Instant.ofEpochMilli(questionModel.getValidStartTime())"
            + ".atZone(ZoneId.systemDefault()).toLocalDate(), LocalTime.MIN)"
            + ".atZone(ZoneId.systemDefault()).toInstant().toEpochMilli())"),
        @Mapping(target = "validEndTime", expression = "java(LocalDateTime"
            + ".of(Instant.ofEpochMilli(questionModel.getValidEndTime())"
            + ".atZone(ZoneId.systemDefault()).toLocalDate(), LocalTime.MAX)"
            + ".atZone(ZoneId.systemDefault()).toInstant().toEpochMilli())")
    })
    Question questionModelToDomain(QuestionModel questionModel);

    QuestionModel questionDomainToModel(Question question);
}
