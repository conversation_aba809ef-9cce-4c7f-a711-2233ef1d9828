package com.kaiba.m.core.controller.thirdparty.hcrtreview.service;

import com.kaiba.lib.base.domain.note.NoteCommentModel;
import com.kaiba.lib.base.service.INoteService;
import com.kaiba.m.core.controller.thirdparty.hcrtreview.constant.KbBizType;
import com.kaiba.m.core.controller.thirdparty.hcrtreview.constant.KbHCRTReviewConst;
import com.kaiba.m.core.controller.thirdparty.hcrtreview.constant.KbItemType;
import com.kaiba.m.core.controller.thirdparty.hcrtreview.model.KbContent;
import com.kaiba.m.core.controller.thirdparty.hcrtreview.model.KbResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * author: lyux
 * date: 2024-10-24
 */
@Slf4j
@Component
public class KbProviderForComment implements IKbProvider {

    private final INoteService noteService;

    public KbProviderForComment(INoteService noteService) {
        this.noteService = noteService;
    }

    @Override
    public KbBizType getBiz() {
        return KbBizType.COMMENT;
    }

    @Override
    public KbResult getContentList(int page, int pageSize, long until) {
        int max = 1_000_000 / pageSize;
        for (int i = page; i < max; i ++) {
            KbResult result = requestContentList(i, pageSize, until);
            if (result.getContents().isEmpty() && result.getHasNext()) {
                if (KbHCRTReviewConst.DEBUG) {
                    log.info("HCRTReview, get comment, result content empty, yet hasNext is true, continue loop");
                }
            } else {
                if (KbHCRTReviewConst.DEBUG) {
                    log.info(result.toString());
                }
                return result;
            }
        }

        return KbResult.asLast(KbBizType.COMMENT);
    }

    private KbResult requestContentList(int page, int pageSize, long until) {
        List<NoteCommentModel> commentList = noteService.getCommentList(null, page, pageSize)
                .data().orElse(Collections.emptyList());
        KbResult result = new KbResult(KbBizType.COMMENT);
        if (commentList.isEmpty()) {
            result.setContents(Collections.emptyList());
            result.setHasNext(false);
        } else {
            boolean hasNext = true;
            List<KbContent> contentList = new ArrayList<>(commentList.size());
            for (NoteCommentModel comment : commentList) {
                if (comment.getCreateTime() == null || comment.getCreateTime() < until / 1000) {
                    hasNext = false;
                    continue;
                }
                KbContent content = new KbContent(KbBizType.COMMENT, comment.getId());
                if (comment.getContent() != null) {
                    content.add(KbItemType.TEXT, comment.getContent(), "text", "帖子正文");
                }
                if (hasNext && comment.getCreateTime() != null && comment.getCreateTime() < until / 1000) {
                    hasNext = false;
                }
                if (content.notEmpty()) {
                    contentList.add(content);
                }
            }
            result.setContents(contentList);
            if (hasNext) {
                result.setNextPage(page + 1);
                result.setHasNext(true);
            } else {
                result.setHasNext(false);
            }
        }
        return result;
    }

}
