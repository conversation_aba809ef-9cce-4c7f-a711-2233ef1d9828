package com.kaiba.m.core.controller.app.homeframe;

import com.google.common.reflect.TypeToken;
import com.kaiba.lib.base.annotation.apiparam.KbSiteId;
import com.kaiba.lib.base.middleware.KbRes;
import com.kaiba.lib.base.response.KbEntity;
import com.kaiba.m.core.model.app.AppOldServiceFrameModel;
import com.kaiba.m.core.util.JsonUtils;
import io.swagger.annotations.ApiOperation;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version AppOldServiceFrameApiController, v0.1 2023/8/7 16:14 daopei Exp $
 **/
@Slf4j
@RestController
@RequestMapping("/api/appOldServiceFrame")
@Validated
@RequiredArgsConstructor
public class AppOldServiceFrameApiController {


    @NonNull
    @Qualifier("kbApiRes")
    private KbRes kbRes;

    private static List<AppOldServiceFrameModel> defaultModel = buildDefault();

    @ApiOperation("查询老版本ServiceFrame数据")
    @PostMapping("/getBySiteId")
    public KbEntity<List<AppOldServiceFrameModel>> getBySiteId(
            @RequestParam @KbSiteId Integer siteId
    ) {
        return kbRes.ok(buildBySiteId(siteId));
    }


    private static List<AppOldServiceFrameModel> buildDefault() {
        List<AppOldServiceFrameModel> result = new ArrayList<>();
        AppOldServiceFrameModel model = new AppOldServiceFrameModel();
        model.setTitle("查限行");
        model.setSubTitle("杭州错峰出行查询地图 一键查询 一眼秒懂");
        model.setIcon("http://static.kaiba315.com.cn/E3F062828897F37FF824666BBECBA88D");
        model.setAction("pageWeb");
        Map<String, Object> actionParams = new HashMap<>();
        actionParams.put("title","杭州错峰出行查询地图");
        actionParams.put("url","https://api.kaiba315.com.cn/p/qG9T9r");
        model.setActionParams(actionParams);
        result.add(model);

        model = new AppOldServiceFrameModel();
        model.setTitle("找车位");
        model.setSubTitle("杭州3000+停车场实时空位");
        model.setIcon("http://static.kaiba315.com.cn/C32C81C641C5F20D7DCE1E755AC9D875");
        model.setAction("pageWeb");
        actionParams = new HashMap<>();
        actionParams.put("title","找车位");
        actionParams.put("url","https://page.kaiba315.com.cn/park/index");
        model.setActionParams(actionParams);
        result.add(model);

        model = new AppOldServiceFrameModel();
        model.setTitle("摇号查询");
        model.setSubTitle("中签信息 延期提醒一手掌握  丰厚礼品免费拿");
        model.setIcon("http://static.kaiba315.com.cn/5A9711267F37571214A7AB57EFF036F9");
        model.setAction("pageWeb");
        actionParams = new HashMap<>();
        actionParams.put("requireLogin",true);
        actionParams.put("decorateUrl",true);
        actionParams.put("title","摇号宠粉日");
        actionParams.put("url","https://page.kaiba315.com.cn/yaohao/list");
        model.setActionParams(actionParams);
        result.add(model);

        model = new AppOldServiceFrameModel();
        model.setTitle("汽车问答");
        model.setSubTitle("汽修专家在线解答");
        model.setIcon("http://static.kaiba315.com.cn/FB049FBEB244B27D692AD34DD767046C");
        model.setAction("pageIssue");
        result.add(model);

        model = new AppOldServiceFrameModel();
        model.setTitle("汽车维权");
        model.setSubTitle("直通厂家一键在线解决");
        model.setIcon("http://static.kaiba315.com.cn/EE7307D75750F119AD615E684D4F83B4");
        model.setAction("pageSafeguard");
        result.add(model);

        model = new AppOldServiceFrameModel();
        model.setTitle("圈子");
        model.setSubTitle("同城车友圈, 大家帮大家");
        model.setIcon("http://static.kaiba315.com.cn/27DCA35F31FECF3ADC217CFD5CF60EA5");
        model.setAction("pageSubCircle");
        result.add(model);

        model = new AppOldServiceFrameModel();
        model.setTitle("杭帮侠");
        model.setSubTitle("");
        model.setIcon("http://static.kaiba315.com.cn/B41F11AF10397CAC0086DCA32457638B");
        model.setAction("pageHangbangxia");
        actionParams = new HashMap<>();
        actionParams.put("title","杭帮侠");
        actionParams.put("siteId","9");
        actionParams.put("url","https://page.kaiba315.com.cn/note/index?threadId\\u003d5d149db386156300016f5ddb");
        model.setActionParams(actionParams);
        result.add(model);

        model = new AppOldServiceFrameModel();
        model.setTitle("精彩文广");
        model.setSubTitle("一手掌握杭城资讯");
        model.setIcon("http://static.kaiba315.com.cn/7633B1FC5655C1019A7BAA489FC889C6");
        model.setAction("pageWeb");
        actionParams = new HashMap<>();
        actionParams.put("title","精彩文广");
        actionParams.put("url","https://page.kaiba315.com.cn/pages/hoolo/content/list");
        model.setActionParams(actionParams);
        result.add(model);

        return result;
    }

    private List<AppOldServiceFrameModel> buildBySiteId(Integer siteId) {
        if (siteId == 14) {
            return JsonUtils.toModel(site_14,new TypeToken<List<AppOldServiceFrameModel>>(){}.getType());
        }
        if (siteId == 96) {
            return JsonUtils.toModel(site_96,new TypeToken<List<AppOldServiceFrameModel>>(){}.getType());
        }
        return defaultModel;
    }

    private final static String site_14 = "[\n" +
            "    {\n" +
            "        \"title\":\"路况\",\n" +
            "        \"subTitle\":\"\",\n" +
            "        \"icon\":\"http://static.kaiba315.com.cn/76EADCE129B47C65D3CA7A0EC462E3A2\",\n" +
            "        \"action\":\"pageRoadCondition\",\n" +
            "        \"actionParams\":{\n" +
            "            \"siteId\":\"14\"\n" +
            "        }\n" +
            "    },\n" +
            "    {\n" +
            "        \"title\":\"高速路况\",\n" +
            "        \"subTitle\":\"\",\n" +
            "        \"icon\":\"http://static.kaiba315.com.cn/7F9E1BA460E6C747379F1A4BB54C6950\",\n" +
            "        \"action\":\"pageWeb\",\n" +
            "        \"actionParams\":{\n" +
            "            \"requireLogin\":\"true\",\n" +
            "            \"decorateUrl\":\"true\",\n" +
            "            \"title\":\"高速路况\",\n" +
            "            \"url\":\"https://xcx.91kuaijian.com/api/road/freeway?from=singlemessage&isappinstalled=0\"\n" +
            "        }\n" +
            "    },\n" +
            "    {\n" +
            "        \"title\":\"民航服务\",\n" +
            "        \"subTitle\":\"\",\n" +
            "        \"icon\":\"http://static.kaiba315.com.cn/BEFC3BBF9802ACCB7E2EA5BB2BC496CB\",\n" +
            "        \"action\":\"pageWeb\",\n" +
            "        \"actionParams\":{\n" +
            "            \"requireLogin\":\"true\",\n" +
            "            \"decorateUrl\":\"true\",\n" +
            "            \"title\":\"民航服务\",\n" +
            "            \"url\":\"http://page.kaiba315.com.cn/app_page/News/indexForSure.html?id=5df774add6e2d084488bc264\"\n" +
            "        }\n" +
            "    },\n" +
            "    {\n" +
            "        \"title\":\"汽车时刻表\",\n" +
            "        \"subTitle\":\"\",\n" +
            "        \"icon\":\"http://static.kaiba315.com.cn/7D23C10A2A50978F57D07338FF9DAA8A\",\n" +
            "        \"action\":\"pageWeb\",\n" +
            "        \"actionParams\":{\n" +
            "            \"requireLogin\":\"true\",\n" +
            "            \"decorateUrl\":\"true\",\n" +
            "            \"title\":\"汽车时刻表\",\n" +
            "            \"url\":\"http://hd.kaiba315.com.cn/Carport/\"\n" +
            "        }\n" +
            "    },\n" +
            "    {\n" +
            "        \"title\":\"违章查询\",\n" +
            "        \"subTitle\":\"\",\n" +
            "        \"icon\":\"http://static.kaiba315.com.cn/A88BDF20B6A7C2AC9A05ED12ED5C0F06\",\n" +
            "        \"action\":\"pageWeb\",\n" +
            "        \"actionParams\":{\n" +
            "            \"requireLogin\":\"true\",\n" +
            "            \"decorateUrl\":\"true\",\n" +
            "            \"title\":\"违章查询\",\n" +
            "            \"url\":\"https://sd.122.gov.cn/views/inquiry.html\"\n" +
            "        }\n" +
            "    },\n" +
            "    {\n" +
            "        \"title\":\"口令红包\",\n" +
            "        \"subTitle\":\"\",\n" +
            "        \"icon\":\"http://static.kaiba315.com.cn/A52263BCF7941CF88FDA9A5CB9E37A83\",\n" +
            "        \"action\":\"pageRedPacket\",\n" +
            "        \"actionParams\":{\n" +
            "            \"siteId\":\"14\",\n" +
            "            \"url\":\"http://api.kaiba315.com.cn/passred/index\",\n" +
            "            \"title\":\"口令红包\",\n" +
            "            \"requireLogin\":\"true\",\n" +
            "            \"decorateUrl\":\"true\"\n" +
            "        }\n" +
            "    }\n" +
            "]";


    private final static String site_96 = "[\n" +
            "    {\n" +
            "        \"title\":\"抢红包\",\n" +
            "        \"subTitle\":\"\",\n" +
            "        \"icon\":\"http://static.kaiba315.com.cn/8D41CDE8FF3E853DE028547543E8B317\",\n" +
            "        \"action\":\"pageWeb\",\n" +
            "        \"actionParams\":{\n" +
            "            \"requireLogin\":\"true\",\n" +
            "            \"decorateUrl\":\"true\",\n" +
            "            \"title\":\"抢红包\",\n" +
            "            \"url\":\"https://page.kaiba315.com.cn/app_page/Red/redList.html\"\n" +
            "        }\n" +
            "    },\n" +
            "    {\n" +
            "        \"title\":\"违章查询\",\n" +
            "        \"subTitle\":\"\",\n" +
            "        \"icon\":\"http://static.kaiba315.com.cn/114D5BAC4B958809CDD0508041A961CB\",\n" +
            "        \"action\":\"pageWeb\",\n" +
            "        \"actionParams\":{\n" +
            "            \"requireLogin\":\"false\",\n" +
            "            \"decorateUrl\":\"false\",\n" +
            "            \"title\":\"违章查询\",\n" +
            "            \"url\":\"http://m.hbgajg.com/\"\n" +
            "        }\n" +
            "    },\n" +
            "    {\n" +
            "        \"title\":\"饮食新男女\",\n" +
            "        \"subTitle\":\"\",\n" +
            "        \"icon\":\"http://static.kaiba315.com.cn/21AD6BF6DCC8EB142F17AA4D478272CD\",\n" +
            "        \"action\":\"pageWeb\",\n" +
            "        \"actionParams\":{\n" +
            "            \"requireLogin\":\"true\",\n" +
            "            \"decorateUrl\":\"true\",\n" +
            "            \"title\":\"饮食新男女\",\n" +
            "            \"url\":\"http://api.kaiba315.com.cn/Delicious/indexs\"\n" +
            "        }\n" +
            "    },\n" +
            "    {\n" +
            "        \"title\":\"积分商城\",\n" +
            "        \"subTitle\":\"\",\n" +
            "        \"icon\":\"http://static.kaiba315.com.cn/BE36849CFF6DC134C612FE392E8456D9\",\n" +
            "        \"action\":\"pageWeb\",\n" +
            "        \"actionParams\":{\n" +
            "            \"requireLogin\":\"true\",\n" +
            "            \"decorateUrl\":\"true\",\n" +
            "            \"title\":\"积分商城\",\n" +
            "            \"url\":\"http://api.kaiba315.com.cn/Scoreshop\"\n" +
            "        }\n" +
            "    },\n" +
            "    {\n" +
            "        \"title\":\"电台活动\",\n" +
            "        \"subTitle\":\"\",\n" +
            "        \"icon\":\"http://static.kaiba315.com.cn/1878357F594969A783BEDE709921554A\",\n" +
            "        \"action\":\"pageWeb\",\n" +
            "        \"actionParams\":{\n" +
            "            \"requireLogin\":\"true\",\n" +
            "            \"decorateUrl\":\"true\",\n" +
            "            \"title\":\"电台活动\",\n" +
            "            \"url\":\"http://api.kaiba315.com.cn/Activitys/indexs\"\n" +
            "        }\n" +
            "    }\n" +
            "]";

}
