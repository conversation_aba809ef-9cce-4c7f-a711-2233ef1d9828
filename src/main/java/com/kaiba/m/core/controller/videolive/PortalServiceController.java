package com.kaiba.m.core.controller.videolive;

import com.google.gson.reflect.TypeToken;
import com.kaiba.lib.base.assembler.Mapper;
import com.kaiba.lib.base.constant.KbProperties;
import com.kaiba.lib.base.constant.videolive.PortalState;
import com.kaiba.lib.base.constant.videolive.VideoLiveState;
import com.kaiba.lib.base.domain.user.UserModel;
import com.kaiba.lib.base.domain.videolive.PortalCreateParams;
import com.kaiba.lib.base.domain.videolive.PortalEditParams;
import com.kaiba.lib.base.domain.videolive.PortalModel;
import com.kaiba.lib.base.middleware.KbRes;
import com.kaiba.lib.base.response.KbCode;
import com.kaiba.lib.base.response.KbEntity;
import com.kaiba.lib.base.response.KbException;
import com.kaiba.lib.base.service.IPortalService;
import com.kaiba.lib.base.service.IUserService;
import com.kaiba.lib.base.util.GsonUtils;
import com.kaiba.m.core.domain.videolive.Portal;
import com.kaiba.m.core.service.videolive.VideoLiveMessageService;
import com.kaiba.m.core.service.videolive.PortalService;
import com.kaiba.m.core.util.JsonUtils;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.web.bind.annotation.ModelAttribute;
import org.springframework.web.bind.annotation.RestController;

import java.time.Duration;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

import javax.validation.Valid;

/**
 * <AUTHOR>
 * @date 2020/6/4
 */
@RestController
public class PortalServiceController implements IPortalService {

    public static final String PORTAL_ACTIVE_PREFIX = "java_active_portal_videolive_id_";
    public static final String PORTAL_PREFIX = "java_video_live_portal_";
    public static final String PORTAL_REGISTER_PREFIX = "java_video_live_portal_click_count_";
    private static final String ACTIVE_PORTAL_LIST_PLACE_HOLDER = "default_active_portal_list_placeholder";

    private final KbRes kbRes;
    private final PortalService portalService;
    private final IUserService userService;
    private final StringRedisTemplate stringRedisTemplate;
    private final VideoLiveMessageService liveMessageService;

    public PortalServiceController(
        KbRes kbRes,
        PortalService portalService,
        IUserService userService,
        StringRedisTemplate stringRedisTemplate,

        VideoLiveMessageService liveMessageService) {
        this.kbRes = kbRes;
        this.portalService = portalService;
        this.userService = userService;
        this.stringRedisTemplate = stringRedisTemplate;
        this.liveMessageService = liveMessageService;
    }

    @Override
    public KbEntity<PortalModel> create(@Valid @ModelAttribute PortalCreateParams params) {
        Portal portal = Mapper.map(params, Portal.class);
        if (params.getActionParams() != null) {
            Map<String, Object> ap = GsonUtils.getGson().fromJson(
                    params.getActionParams(), new TypeToken<Map<String, Object>>() {}.getType());
            portal.setActionParams(ap);
        }
        return kbRes.ok(portal2model(portalService.create(portal)));
    }

    @Override
    public KbEntity<PortalModel> edit(@Valid @ModelAttribute PortalEditParams params) {
        Portal portal = portalService.detail(params.getPortalId()).orElseThrow(() ->
            new KbException(KbCode.RESOURCE_NOT_FOUND, "videolive portal not exists: " + params.getPortalId()).li());
        if (PortalState.ONLINE.getValue() == portal.getState()) {
            return kbRes.err(KbCode.ILLEGAL_STATE, "正在线上的链接不能修改");
        }
        portal.setTitle(params.getTitle());
        portal.setImage(params.getImage());
        portal.setDuration(params.getDuration());
        portal.setAction(params.getAction());
        portal.setActionParams(params.getActionParams() == null ? null :
            GsonUtils.getGson().fromJson(params.getActionParams(), new TypeToken<Map<String, Object>>() {}.getType()));
        return kbRes.ok(portal2model(portalService.save(portal)));
    }

    @Override
    public KbEntity<PortalModel> detail(String portalId) {
        return portalService.detail(portalId)
                .map(portal -> {
                    Object countObj = stringRedisTemplate.opsForHash().get(videolivePortalKey(portal.getVideoliveId()), portalRegisterKey(portal.getId()));
                    PortalModel model = portal2model(portal);
                    model.setClickCount(countObj == null ? 0 : Long.parseLong(countObj.toString()));
                    model.setCreator(userService.getBasic(portal.getCreator()).getData());
                    return model;
                })
                .map(kbRes::ok)
                .orElse(kbRes.err(KbCode.RESOURCE_NOT_FOUND));
    }

    @Override
    public KbEntity<List<PortalModel>> getPortalListByStateIn(String videoliveId, Integer[] states) {
        List<Portal> portalList = portalService.getPortalList(videoliveId, states);
        Set<Integer> userIds = portalList.stream().map(Portal::getCreator).collect(Collectors.toSet());
        Map<Integer, UserModel> userMap = userService.getBasicMapByIds(userIds).data().orElse(Collections.emptyMap());
        Map<Object, Object> entries = stringRedisTemplate.opsForHash().entries(videolivePortalKey(videoliveId));
        return kbRes.ok(portalService.getPortalList(videoliveId, states)
                .stream()
                .map(portal -> {
                    Object countObj = entries.get(portalRegisterKey(portal.getId()));
                    PortalModel model = portal2model(portal);
                    model.setCreator(userMap.get(portal.getCreator()));
                    model.setClickCount(countObj == null ? 0 : Long.parseLong(countObj.toString()));
                    return model;
                })
                .collect(Collectors.toList())
        );
    }

    @Override
    public KbEntity<List<PortalModel>> getActivePortalList(String videoliveId, Boolean allowCache) {
        Map<Object, Object> entries = getPortalClickMap(videoliveId);
        if (Boolean.TRUE.equals(allowCache)) {
            return kbRes.ok(getActivePortalListFromCache(videoliveId)
                .map(list -> {
                    list.stream().filter(t -> notPlaceHolder(t.getId())).forEach(portal -> {
                        String registerKey = PORTAL_REGISTER_PREFIX + portal.getId();
                        Object count = entries.getOrDefault(registerKey, "0");
                        portal.setClickCount(Long.parseLong(count.toString()));
                    });
                    return list;
                })
                .orElseGet(() -> {
                    List<Portal> dbData = getActivePortalListFromDB(videoliveId);
                    List<PortalModel> portalModels = convertAndSetClickCount(dbData, entries);
                    cachePortalList(videoliveId, portalModels);
                    return portalModels;
                }));
        } else {
            List<Portal> portalList = getActivePortalListFromDB(videoliveId);
            return kbRes.ok(convertAndSetClickCount(portalList, entries));
        }
    }

    @Override
    public KbEntity<Void> sign(String portalId) {
        portalService.save(changeState(portalId, PortalState.SIGNED));
        return kbRes.ok();
    }

    @Override
    public KbEntity<Void> start(String portalId) {
        Portal portal = changeState(portalId, PortalState.ONLINE);
        Long currentTime = System.currentTimeMillis() / 1000;
        portal.setStartTime(currentTime);
        portal.setEndTime(currentTime + portal.getDuration());
        portalService.save(portal);
        expireActiveVideoliveList(portal.getVideoliveId());
        liveMessageService.callLink(portal.getVideoliveId(), 8);
        return kbRes.ok();
    }

    @Override
    public KbEntity<Void> end(String portalId) {
        Portal portal = changeState(portalId, PortalState.ENDED);
        portalService.save(portal);
        expireActiveVideoliveList(portal.getVideoliveId());
        liveMessageService.callLink(portal.getVideoliveId(), 8);
        return kbRes.ok();
    }

    @Override
    public KbEntity<Void> changPortalState(String portalId, Integer state) {
        PortalState ps = PortalState.valueOf(state).orElseThrow(() ->
            new KbException(KbCode.ILLEGAL_STATE).setReadableMessage("未知的状态").li());
        Portal portal = changeState(portalId, ps);
        portalService.save(portal);
        expireActiveVideoliveList(portal.getVideoliveId());
        if(PortalState.SEALED.getValue() == state) {
            liveMessageService.callLink(portal.getVideoliveId(), 8);
        }
        return kbRes.ok();
    }

    @Override
    public KbEntity<Long> register(String videoliveId, String portalId) {
        return kbRes.ok(stringRedisTemplate.opsForHash().increment(videolivePortalKey(videoliveId), portalRegisterKey(portalId), 1));
    }

    // --------------------

    private static PortalModel portal2model(Portal portal) {
        PortalModel model = new PortalModel();
        model.setId(portal.getId());
        model.setTitle(portal.getTitle());
        model.setLiveId(portal.getVideoliveId());
        model.setImage(portal.getImage());
        model.setAction(portal.getAction());
        model.setActionParams(portal.getActionParams());
        model.setScheduledStartTime(portal.getStartTime());
        model.setScheduledEndTime(portal.getEndTime());
        model.setCreateTime(portal.getCreateTime());
        model.setState(portal.getState());
        model.setDuration(portal.getDuration());
        return model;
    }

    private Portal changeState(String portalId, PortalState targetState) {
        Portal portal = portalService.detail(portalId).orElseThrow(() ->
            new KbException(KbCode.RESOURCE_NOT_FOUND, "videolive portal not exists: " + portalId).li());
        PortalState currentState = PortalState.valueOf(portal.getState()).orElseThrow(() ->
            new KbException(KbCode.ILLEGAL_STATE).setReadableMessage("未知的状态").li());
        if (!currentState.isStateChangeAllowed(targetState)) {
            throw new KbException(KbCode.ILLEGAL_STATE);
        }
        int newState = map2VideoState(targetState).getValue();
        liveMessageService.changeState(portal.getVideoliveId(), KbProperties.ADMIN_USER_ID, newState);
        portal.setState(targetState.getValue());
        return portal;
    }

    private List<Portal> getActivePortalListFromDB(String videoLiveId) {
        List<Portal> portalList = portalService.getPortalList(videoLiveId, new Integer[]{PortalState.ONLINE.getValue()});
        if (portalList.isEmpty()) {
            Portal portal = new Portal();
            portal.setId(ACTIVE_PORTAL_LIST_PLACE_HOLDER);
            portalList = Collections.singletonList(portal);
        }
        return portalList;
    }

    private void cachePortalList(String videoLiveId, List<PortalModel> list) {
        String cacheKey = PORTAL_ACTIVE_PREFIX + videoLiveId;
        String json = JsonUtils.getGson().toJson(list);
        stringRedisTemplate.opsForValue().set(cacheKey, json, Duration.ofMinutes(5));
    }

    private Optional<List<PortalModel>> getActivePortalListFromCache(String videoLiveId) {
        String cacheKey = PORTAL_ACTIVE_PREFIX + videoLiveId;
        String json = stringRedisTemplate.opsForValue().get(cacheKey);
        return Optional.ofNullable(json).map(t ->
            JsonUtils.getGson().fromJson(t, new TypeToken<List<PortalModel>>() {}.getType()));
    }

    private Map<Object, Object> getPortalClickMap(String videoLiveId) {
        String cacheKey = PORTAL_PREFIX + videoLiveId;
        return stringRedisTemplate.opsForHash().entries(cacheKey);
    }

    private static boolean notPlaceHolder(String portalId) {
        return !ACTIVE_PORTAL_LIST_PLACE_HOLDER.equals(portalId);
    }

    private static List<PortalModel> convertAndSetClickCount(List<Portal> list, Map<Object, Object> entries) {
        return list.stream().filter(portal -> notPlaceHolder(portal.getId()))
            .map(portal -> {
                PortalModel portalModel = portal2model(portal);
                String registerKey = PORTAL_REGISTER_PREFIX + portal.getId();
                Object count = entries.getOrDefault(registerKey, "0");
                portalModel.setClickCount(Long.parseLong(count.toString()));
                return portalModel;
            }).collect(Collectors.toList());
    }

    private void expireActiveVideoliveList(String videoliveId) {
        stringRedisTemplate.delete(portalActiveKey(videoliveId));
    }

    private static String portalActiveKey(String videoliveId) {
        return PORTAL_ACTIVE_PREFIX + videoliveId;
    }

    private static String videolivePortalKey(String videoliveId) {
        return PORTAL_PREFIX + videoliveId;
    }

    private static String portalRegisterKey(String portalId) {
        return PORTAL_REGISTER_PREFIX + portalId;
    }

    private static VideoLiveState map2VideoState(PortalState targetState) {
        switch (targetState) {
            case INIT:
                return VideoLiveState.INIT;
            case SIGNED:
                return VideoLiveState.SIGNED;
            case ONLINE:
                return VideoLiveState.ONLINE;
            case ENDED:
                return VideoLiveState.ENDED;
            case SEALED:
                return VideoLiveState.SEALED;
            default:
                throw new KbException(KbCode.ILLEGAL_STATE);
        }
    }
}
