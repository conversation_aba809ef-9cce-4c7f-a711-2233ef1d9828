package com.kaiba.m.core.controller.enroll;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.kaiba.lib.base.assembler.Mapper;
import com.kaiba.lib.base.constant.KbHeader;
import com.kaiba.lib.base.constant.enroll.EnrollMarkType;
import com.kaiba.lib.base.constant.enroll.EnrollModuleType;
import com.kaiba.lib.base.constant.enroll.EnrollState;
import com.kaiba.lib.base.domain.enroll.*;
import com.kaiba.lib.base.middleware.KbRes;
import com.kaiba.lib.base.response.KbCode;
import com.kaiba.lib.base.response.KbEntity;
import com.kaiba.lib.base.response.KbException;
import com.kaiba.lib.base.service.IEnrollService;
import com.kaiba.m.core.domain.enroll.Enroll;
import com.kaiba.m.core.domain.enroll.EnrollData;
import com.kaiba.m.core.domain.enroll.EnrollModule;
import com.kaiba.m.core.repository.enroll.EnrollDataRepository;
import com.kaiba.m.core.service.enroll.EnrollService;
import com.kaiba.m.core.util.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2019/12/09
 */

@Slf4j
@RestController
public class EnrollServiceController implements IEnrollService {

    private final EnrollService enrollService;
    private final KbRes kbRes;

    public EnrollServiceController(
            KbRes kbRes,
            EnrollService enrollService
    ) {
        this.kbRes = kbRes;
        this.enrollService = enrollService;
    }

    @Override
    @RequestMapping(value = "/createEnrollByBody", method = RequestMethod.POST, consumes = "application/json;charset=UTF-8")
    public KbEntity<EnrollModel> createEnrollByBody(@RequestBody EnrollModel data) {
        if (null != data.getId()) {
            return kbRes.err(KbCode.REQUEST_PARAM_INVALID, "id must be null");
        }
        return kbRes.ok(enroll2model(enrollService.createEnroll(data)));
    }

    @Override
    @RequestMapping(value = "/updateEnrollByBody", method = RequestMethod.POST, consumes = "application/json;charset=UTF-8")
    public KbEntity<EnrollModel> updateEnrollByBody(@RequestBody EnrollModel data) {
        if (null == data.getId()) {
            return kbRes.err(KbCode.REQUEST_PARAM_INVALID, "id null");
        }
        return kbRes.ok(enroll2model(enrollService.updateEnroll(data)));
    }

    @Override
    public KbEntity<EnrollModel> updateEnrollState(String enrollId, Integer state, Integer userId) {
        return kbRes.ok(enroll2model(enrollService.updateEnrollState(enrollId, state)));
    }

    @Override
    public KbEntity<String> deleteEnroll(String enrollId, Integer userId) {
        enrollService.deleteEnroll(enrollId);
        return kbRes.ok();
    }

    @Override
    @RequestMapping(value = "/addEnrollDataByBody", method = RequestMethod.POST, consumes = "application/json;charset=UTF-8")
    public KbEntity<List<EnrollDataModel>> addEnrollDataByBody(
            @RequestBody EnrollDataUpdateModel data
    ) {
        KbEntity<List<EnrollDataModel>> enrollDataModelKbEntity = new KbEntity<>();
        Enroll enroll = enrollService.findById(data.getEnrollId()).orElseThrow(
                () -> new KbException(KbCode.ENROLL_NOT_EXIST, "enroll not exist: " + data.getEnrollId()).li());
        long currentTime = System.currentTimeMillis() / 1000;
        if (currentTime > enroll.getEndTime()) {
            return kbRes.on().setReadableMessage("报名已截止!").create();
        }
        if (currentTime < enroll.getStartTime()) {
            return kbRes.msg("报名未开始.");
        }
        if (enroll.getState() == EnrollState.INIT.getValue()) {
            return kbRes.on().setReadableMessage("报名尚未启动!").create();
        }
        EnrollMarkType markType = EnrollMarkType.valueOf(enroll.getMarkType()).orElseThrow(
                () -> new KbException(KbCode.ENROLL_NOT_EXIST, "enroll mark type not exist: " + enroll).li());
        if (data.getDataList() != null && data.getDataList().size() != 0) {
            List<EnrollData> enrollDataList = data.getDataList().stream().map(EnrollServiceController::model2enrollData)
                    .collect(Collectors.toList());
            if (enrollService.existsByEnrollIdAndMark(data.getEnrollId(), enrollService.generateMark(markType, enrollDataList))) {
                return kbRes.on().setReadableMessage("您已报名!").create();
            }
            enrollDataModelKbEntity = kbRes.ok(enrollService.createEnrollData(markType, enrollDataList).stream()
                    .map(enrollData -> {
                        EnrollDataModel enrollDataModel = enrollData2model(enrollData);
                        if (enrollService.getEnrollModuleById(enrollData.getModuleId()).isPresent()) {
                            enrollDataModel.setTitle(enrollService.getEnrollModuleById(enrollData.getModuleId()).get().getTitle());
                        }
                        return enrollDataModel;
                    })
                    .collect(Collectors.toList()));
            enrollDataModelKbEntity.setMsg("报名成功!");
        }
        return enrollDataModelKbEntity;
    }

    @Override
    @RequestMapping(value = "/updateEnrollDataByBody", method = RequestMethod.POST, consumes = "application/json;charset=UTF-8")
    public KbEntity<List<EnrollDataModel>> updateEnrollDataByBody(
            @RequestBody EnrollDataUpdateModel data
    ) {
        KbEntity<List<EnrollDataModel>> enrollDataModelKbEntity = new KbEntity<>();
        Enroll enroll = enrollService.findById(data.getEnrollId()).orElseThrow(
                () -> new KbException(KbCode.ENROLL_NOT_EXIST, "enroll not exist: " + data.getEnrollId()).li());
        long currentTime = System.currentTimeMillis() / 1000;
        if (currentTime > enroll.getEndTime()) {
            return kbRes.on().setReadableMessage("报名已截止!").create();
        }
        if (currentTime < enroll.getStartTime()) {
            return kbRes.msg("报名未开始.");
        }

        if (data.getDataList() != null && data.getDataList().size() != 0) {
            List<EnrollData> enrollDataList = data.getDataList().stream().map(EnrollServiceController::model2enrollData)
                    .collect(Collectors.toList());
            enrollDataModelKbEntity = kbRes.ok(enrollService.updateEnrollData(EnrollMarkType.valueOf(enroll.getMarkType())
                    .orElseThrow(() -> new KbException(KbCode.ENROLL_NOT_EXIST, "enroll mark type not exist: " + enroll).li()), enrollDataList).stream()
                    .map(enrollData -> {
                        EnrollDataModel enrollDataModel = enrollData2model(enrollData);
                        if (enrollService.getEnrollModuleById(enrollData.getModuleId()).isPresent()) {
                            enrollDataModel.setTitle(enrollService.getEnrollModuleById(enrollData.getModuleId()).get().getTitle());
                        }
                        return enrollDataModel;
                    })
                    .collect(Collectors.toList()));
            enrollDataModelKbEntity.setMsg("编辑成功!");
        }
        return enrollDataModelKbEntity;
    }

    @Override
    public KbEntity<List<EnrollModel>> getEnrollList(Integer siteId, Integer state, Integer type, String title, Integer page, Integer pageSize) {
        return kbRes.ok(enrollService
                .getEnrollList(siteId, state, type, title, page, pageSize)
                .map(this::enroll2model));
    }

    @Override
    public KbEntity<EnrollModel> getEnrollById(String id, Integer userId) {
        Enroll enroll = enrollService.findById(id).orElseThrow(
                () -> new KbException(KbCode.ENROLL_NOT_EXIST, "enroll not exist: " + id).li());
        if (userId == null) {
            return kbRes.ok(enroll2model(enroll));
        } else {
            List<EnrollModuleDataAggr> aggr = enrollService.getAggrListByUserIdAndEnrollId(id, userId);
            if (aggr.size() == 0) {
                return kbRes.ok(enroll2model(enroll));
            } else {
                return kbRes.ok(enroll2Model(enroll, aggr));
            }
        }
    }

    @Override
    public KbEntity<List<EnrollModuleModel>> getEnrollModuleListByEnrollId(String enrollId) {
        return kbRes.ok(enrollService
                .getEnrollModuleListByEnrollId(enrollId)
                .stream()
                .map(this::enrollModule2model)
                .collect(Collectors.toList()));
    }

    @Override
    public KbEntity<List<EnrollModuleDataAggr>> getEnrollDataAggrListByEnrollIdOrUserId(String enrollId, Integer userId) {
        return kbRes.ok(enrollService.getAggrListByUserIdAndEnrollId(enrollId, userId));
    }

    @Override
    public KbEntity<List<EnrollDataModel>> getEnrollDataListByEnrollId(String enrollId, String moduleId, String mark,
                                                                       Integer userId, Integer page, Integer pageSize) {
        return kbRes.ok(enrollService
                .getEnrollDataList(enrollId, moduleId, mark, userId, page, pageSize)
                .stream()
                .map(this::enrollData2model)
                .collect(Collectors.toList()));
    }

    @Override
    public KbEntity<List<EnrollModuleDataAggr>> getEnrollModuleDataAggrByEnrollId(String enrollId, Integer createId,
                                                                                  Integer page, Integer pageSize) {
        EnrollDataRepository.EnrollModuleDataAggrWrapper enrollModuleDataAggrWrapper =
                enrollService.getAggrListByEnrollIdAndUserId(enrollId, createId, page, pageSize);
        KbEntity<List<EnrollModuleDataAggr>> list = kbRes.ok(enrollModuleDataAggrWrapper.aggrs);
        list.setTotalCount((long) enrollModuleDataAggrWrapper.total);
        return list;
    }

    @Override
    public KbEntity<List<EnrollDataModel>> getEnrollDataModelListByEnrollIdAndMark(String enrollId, String mark) {
        return kbRes.ok(enrollService.getByEnrollIdAndMark(enrollId, mark).stream().map(enrollData -> {
            EnrollDataModel enrollDataModel = enrollData2model(enrollData);
            if (enrollService.getEnrollModuleById(enrollData.getModuleId()).isPresent()) {
                enrollDataModel.setTitle(enrollService.getEnrollModuleById(enrollData.getModuleId()).get().getTitle());
            }
            return enrollDataModel;
        }).collect(Collectors.toList()));
    }

    @Override
    public KbEntity<EnrollModuleModel> getEnrollModuleModelById(String id) {
        return kbRes.ok(enrollModule2model(enrollService.getEnrollModuleById(id)
                .orElseThrow(KbException.supplier(KbCode.ENROLL_MODULE_NOT_EXIST))));
    }

    @Override
    public KbEntity<Boolean> isEnrolled(Integer userId, String id) {
        if (userId == null) {
            return kbRes.ok(false);
        }
        return kbRes.ok(enrollService.isEnrolled(userId, id));
    }

    private EnrollModel enroll2model(Enroll enroll) {
        EnrollModel model = Mapper.map(enroll, EnrollModel.class);
        model.setEnrollModules(enrollService.getEnrollModuleListByEnrollId(enroll.getId())
                .stream()
                .map(this::enrollModule2model)
                .collect(Collectors.toList()));
        return model;
    }

    private EnrollModel enroll2Model(Enroll enroll, List<EnrollModuleDataAggr> enrollModuleDataAggrs) {
        EnrollModel model = Mapper.map(enroll, EnrollModel.class);
        if (enrollModuleDataAggrs != null) {
            model.setEnrollModuleDataAggrs(enrollModuleDataAggrs);
        }
        model.setEnrollModules(enrollService.getEnrollModuleListByEnrollId(enroll.getId())
                .stream()
                .map(this::enrollModule2model)
                .collect(Collectors.toList()));
        return model;
    }

    private EnrollModuleModel enrollModule2model(EnrollModule enrollModule) {
        return Mapper.map(enrollModule, EnrollModuleModel.class);
    }

    private EnrollDataModel enrollData2model(EnrollData enrollData) {
        int type = enrollData.getType();
        EnrollDataModel model = Mapper.map(enrollData, EnrollDataModel.class);
        EnrollModuleType enrollModuleType = EnrollModuleType.valueOf(type).orElseThrow(() ->
                new KbException(KbCode.ILLEGAL_ARGUMENT, "wrong type: " + type).li());
        switch (enrollModuleType) {
            case SINGLE_TEXT:
            case MOBILE:
            case EMAIL:
            case CAR_NO:
            case ID_CARD:
            case MULTI_TEXT:
                if (enrollData.getText() != null) {
                    model.setData(enrollData.getText());
                }
                break;
            case AUDIO:
                if (enrollData.getAudio() != null) {
                    model.setData(enrollData.getAudio());
                }
                break;
            case VIDEO:
                if (enrollData.getVideo() != null) {
                    model.setData(enrollData.getVideo());
                }
                break;
            case IMAGE:
                if (enrollData.getImages() != null) {
                    model.setData(enrollData.getImages());
                }
                break;
            case CHECK_BOX:
                if (enrollData.getChoice() != null) {
                    model.setData(enrollData.getChoice());
                }
                break;
            case RATE:
                if (enrollData.getRating() != null) {
                    model.setData(enrollData.getRating());
                }
                break;
        }
        return model;
    }

    @SuppressWarnings("unchecked")
    private static EnrollData model2enrollData(EnrollDataModel enrollDataModel) {
        int type = enrollDataModel.getType();
        EnrollData enrollData = Mapper.map(enrollDataModel, EnrollData.class);
        EnrollModuleType enrollModuleType = EnrollModuleType.valueOf(type).orElseThrow(() ->
                new KbException(KbCode.ILLEGAL_ARGUMENT, "wrong type: " + type).li());
        Gson gson = JsonUtils.getGson();
        switch (enrollModuleType) {
            case SINGLE_TEXT:
            case MOBILE:
            case EMAIL:
            case CAR_NO:
            case ID_CARD:
            case MULTI_TEXT:
                if (null != enrollData.getData()) {
                    enrollData.setText(enrollData.getData().toString());
                }
                break;
            case AUDIO:
                if (null != enrollData.getData()) {
                    enrollData.setAudio(JsonUtils.toAudio(gson.toJson(enrollData.getData())));
                }
                break;
            case VIDEO:
                if (null != enrollData.getData()) {
                    enrollData.setVideo(JsonUtils.toVideo(gson.toJson(enrollData.getData())));
                }
                break;
            case IMAGE:
                if (null != enrollData.getData()) {
                    enrollData.setImages(JsonUtils.toImageList(gson.toJson(enrollData.getData())));
                }
                break;
            case CHECK_BOX:
                if (null != enrollData.getData()) {
                    String str = enrollData.getData().toString().trim();
                    if (str.startsWith("[")) {
                        enrollData.setChoice(gson.fromJson(
                                gson.toJson(enrollData.getData()), new TypeToken<List<Integer>>() {}.getType()));
                    } else {
                        enrollData.setChoice(Collections.singletonList(Integer.valueOf(enrollData.getData().toString())));
                    }
                }
                break;
            case RATE:
                if (null != enrollData.getData()) {
                    enrollData.setRating(Integer.valueOf(enrollData.getData().toString()));
                }
                break;
        }
        return enrollData;
    }
}
