package com.kaiba.m.core.controller.program;

import com.google.gson.reflect.TypeToken;
import com.kaiba.lib.base.constant.KbHeader;
import com.kaiba.lib.base.domain.vote.VoteModel;
import com.kaiba.lib.base.middleware.KbRes;
import com.kaiba.lib.base.response.KbEntity;
import com.kaiba.lib.base.service.IVoteService;
import com.kaiba.m.core.util.JsonUtils;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/10/16
 */
@Validated
@RestController
public class VoteApiController {

    private final KbRes kbApiRes;
    private final IVoteService voteService;

    public VoteApiController(@Qualifier("kbApiRes") KbRes kbApiRes, IVoteService voteService) {
        this.voteService = voteService;
        this.kbApiRes = kbApiRes;
    }

    @PostMapping("/n/vote/vote")
    @ApiOperation("投票")
    public KbEntity<String> vote(
            @RequestHeader(KbHeader.KB_USER_ID) Integer userId,
            @RequestParam() String voteId,
            @RequestParam() String options

    ) {
        List<String> optionsList = JsonUtils.getGson().fromJson(options, new TypeToken<List<String>>() {}.getType());
        voteService.vote(userId, voteId, optionsList.toArray(new String[0]));
        return kbApiRes.ok();
    }

    @PostMapping("/n/vote/getVoteById")
    public KbEntity<VoteModel> getVoteById(
            @RequestHeader(name = KbHeader.KB_USER_ID, required = false) Integer userId,
            @RequestParam() String voteId
    ) {
        return kbApiRes.asIs(voteService.getVoteById(userId, voteId).peek(vote -> {
            if (vote.getMultiVote() != null && (vote.getMultiVote() == 0 || vote.getMultiVote() > 1)) {
                vote.setTitle(vote.getTitle() + "（多选）");
            }
        }));
    }

}
