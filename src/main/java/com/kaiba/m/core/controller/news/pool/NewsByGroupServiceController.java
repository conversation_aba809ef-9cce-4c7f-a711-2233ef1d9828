package com.kaiba.m.core.controller.news.pool;

import com.kaiba.lib.base.assembler.Mapper;
import com.kaiba.lib.base.constant.news.article.*;
import com.kaiba.lib.base.domain.common.ShareModel;
import com.kaiba.lib.base.domain.news.article.ArticleModel;
import com.kaiba.lib.base.domain.news.pool.bygroup.*;
import com.kaiba.lib.base.domain.news.pool.topic.*;
import com.kaiba.lib.base.lang.collections.KbColUtils;
import com.kaiba.lib.base.middleware.KbRes;
import com.kaiba.lib.base.response.KbCode;
import com.kaiba.lib.base.response.KbEntity;
import com.kaiba.lib.base.response.KbException;
import com.kaiba.lib.base.service.INeoNewsByGroupService;
import com.kaiba.lib.base.service.ISensorsService;
import com.kaiba.m.core.domain.news.article.NewsArticle;
import com.kaiba.m.core.domain.news.pool.bygroup.*;
import com.kaiba.m.core.domain.news.pool.topic.TopicLayout;
import com.kaiba.m.core.service.news.article.NewsArticleCacheService;
import com.kaiba.m.core.service.news.article.NewsArticleModelHelper;
import com.kaiba.m.core.service.news.article.NewsArticleService;
import com.kaiba.m.core.service.news.biz.fetcher.GroupFetcherService;
import com.kaiba.m.core.service.news.pool.bygroup.NewsGroupSelectorService;
import com.kaiba.m.core.service.news.pool.bygroup.NewsGroupService;
import com.kaiba.m.core.service.news.pool.bygroup.NewsGroupTopicApiService;
import com.kaiba.m.core.service.news.pool.bygroup.NewsGroupTopicService;
import com.kaiba.m.core.service.news.pool.topic.NewsTopicLayoutModelHelper;
import com.kaiba.m.core.service.news.pool.topic.NewsTopicLayoutService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.*;
import java.util.stream.Collectors;

/**
 * author: lyux
 * date: 2023-08-01
 */
@Slf4j
@RestController
public class NewsByGroupServiceController implements INeoNewsByGroupService {

    private final KbRes kbRes;
    private final NewsGroupService groupService;
    private final NewsArticleService articleService;
    private final NewsTopicLayoutService layoutService;
    private final NewsGroupTopicService topicService;
    private final NewsGroupTopicApiService topicApiService;
    private final GroupFetcherService fetcherService;
    private final ISensorsService sensorsService;
    private final NewsArticleCacheService cacheService;
    private final NewsGroupSelectorService selectorService;

    public NewsByGroupServiceController(
            KbRes kbRes,
            NewsGroupService groupService,
            NewsArticleService articleService,
            NewsTopicLayoutService layoutService,
            NewsGroupTopicService topicService,
            NewsGroupTopicApiService topicApiService,
            GroupFetcherService fetcherService,
            ISensorsService sensorsService,
            NewsArticleCacheService cacheService,
            NewsGroupSelectorService selectorService
    ) {
        this.kbRes = kbRes;
        this.groupService = groupService;
        this.articleService = articleService;
        this.layoutService = layoutService;
        this.topicService = topicService;
        this.topicApiService = topicApiService;
        this.fetcherService = fetcherService;
        this.sensorsService = sensorsService;
        this.cacheService = cacheService;
        this.selectorService = selectorService;
    }

    @Override
    public KbEntity<GroupModel> createGroup(
            String groupKey, Integer siteId, String channelKey,
            String name, String desc, String renderer, String style, Integer max) {
        IdsGroup group = new IdsGroup();
        group.setKey(groupKey);
        group.setSiteId(siteId);
        group.setChannelKey(channelKey);
        group.setName(name);
        group.setDesc(desc);
        group.setRenderer(renderer);
        group.setStyle(style);
        group.setMax(max);
        IdsGroup created = groupService.createGroup(group);
        return kbRes.ok(Mapper.map(created, GroupModel.class));
    }

    @Override
    public KbEntity<GroupModel> createGroupByBody(GroupCreateModel model) {
        IdsGroup group = Mapper.map(model, IdsGroup.class);
        IdsGroup created = groupService.createGroup(group);
        return kbRes.ok(Mapper.map(created, GroupModel.class));
    }

    @Override
    public KbEntity<GroupModel> updateGroupChannel(String groupId, String channelKey) {
        IdsGroup group = groupService.updateGroupChannelKey(groupId, channelKey);
        return kbRes.ok(Mapper.map(group, GroupModel.class));
    }

    @Override
    public KbEntity<GroupModel> updateGroupInfo(String groupId, String name, String desc) {
        IdsGroup group = groupService.updateGroupInfo(groupId, name, desc);
        return kbRes.ok(Mapper.map(group, GroupModel.class));
    }

    @Override
    public KbEntity<GroupModel> updateGroupRenderer(String groupId, String renderer) {
        IdsGroup group = groupService.updateGroupRenderer(groupId, renderer);
        return kbRes.ok(Mapper.map(group, GroupModel.class));
    }

    @Override
    public KbEntity<GroupModel> updateGroupMax(String groupId, Integer max) {
        IdsGroup group = groupService.updateGroupMax(groupId, max);
        return kbRes.ok(Mapper.map(group, GroupModel.class));
    }

    @Override
    public KbEntity<GroupModel> updateGroupStyle(String groupId, String style) {
        IdsGroup group = groupService.updateGroupStyle(groupId, style);
        return kbRes.ok(Mapper.map(group, GroupModel.class));
    }

    @Override
    public KbEntity<GroupModel> updateGroupCacheConfig(String groupId, String cacheMode, Integer cacheCount) {
        IdsGroup group = groupService.updateGroupCacheConfig(groupId, cacheMode, cacheCount);
        return kbRes.ok(Mapper.map(group, GroupModel.class));
    }

    @Override
    public KbEntity<Void> updateGroupOnCreateDisplayConfig(DisplayConfigUpdater updater) {
        DisplayConfig config = Mapper.map(updater, DisplayConfig.class);
        groupService.updateGroupOnCreateDisplayConfig(updater.getGroupId(), config);
        return kbRes.ok();
    }

    @Override
    public KbEntity<Void> updateGroupOnAbsentDisplayConfig(DisplayConfigUpdater updater) {
        DisplayConfig config = Mapper.map(updater, DisplayConfig.class);
        groupService.updateGroupOnAbsentDisplayConfig(updater.getGroupId(), config);
        return kbRes.ok();
    }

    @Override
    public KbEntity<Void> updateGroupOnFetchDisplayConfig(DisplayConfigUpdater updater) {
        DisplayConfig config = Mapper.map(updater, DisplayConfig.class);
        groupService.updateGroupOnFetchDisplayConfig(updater.getGroupId(), config);
        return kbRes.ok();
    }

    @Override
    public KbEntity<GroupModel> updateGroupKey(String groupId, String groupKey) {
        IdsGroup group = groupService.updateGroupKey(groupId, groupKey);
        return kbRes.ok(Mapper.map(group, GroupModel.class));
    }

    @Override
    public KbEntity<GroupModel> updateGroupSort(String groupId, String sortMode, String timeType) {
        GroupSortMode mode = GroupSortMode.resolveByName(sortMode)
                .orElseThrow(() -> new KbException(KbCode.REQUEST_PARAM_INVALID).r("未知的排序模式").li());
        ArticleTimeType sortTime = ArticleTimeType.resolveByName(timeType)
                .orElseThrow(() -> new KbException(KbCode.REQUEST_PARAM_INVALID).r("未知的时间类型").li());
        if (sortTime == ArticleTimeType.HIDE_TIME) {
            return kbRes.msg(KbCode.REQUEST_PARAM_INVALID, "该类型不可用作排序");
        }
        IdsGroup group = groupService.updateGroupSortTime(groupId, mode, sortTime);
        return kbRes.ok(Mapper.map(group, GroupModel.class));
    }

    @Override
    public KbEntity<GroupModel> updateGroupDisplayTimeType(String groupId, String timeType, String timeFormat) {
        return kbRes.err(KbCode.NOT_SUPPORT_ANY_MORE);
    }

    @Override
    public KbEntity<Void> deleteGroup(String groupId) {
        groupService.deleteGroupById(groupId);
        return kbRes.ok();
    }

    @Override
    public KbEntity<GroupModel> getGroup(String groupId, String groupKey) {
        return groupService.getGroup(groupId, groupKey)
                .map(group -> Mapper.map(group, GroupModel.class))
                .map(kbRes::ok)
                .orElseGet(() -> kbRes.err(KbCode.RESOURCE_NOT_FOUND));
    }

    @Override
    public KbEntity<List<GroupModel>> getGroupList(
            Integer siteId, String channelKey, Integer page, Integer pageSize) {
        if (channelKey != null) {
            return kbRes.ok(groupService.getGroupPageByChannel(channelKey, page, pageSize)
                    .map(group -> Mapper.map(group, GroupModel.class)));
        } else if (siteId != null) {
            return kbRes.ok(groupService.getGroupPageBySite(siteId, page, pageSize)
                    .map(group -> Mapper.map(group, GroupModel.class)));
         } else {
            return kbRes.ok(groupService.getGroupPage(page, pageSize)
                    .map(group -> Mapper.map(group, GroupModel.class)));
        }
    }

    @Override
    public KbEntity<List<GroupModel>> getGroupListByIdIn(String[] groupIds) {
        if (groupIds == null || groupIds.length == 0) {
            return kbRes.ok();
        } else {
            List<String> ids = Arrays.asList(groupIds);
            return kbRes.ok(groupService.getGroupListByIdIn(ids).stream()
                    .map(group -> Mapper.map(group, GroupModel.class))
                    .collect(Collectors.toList()));
        }
    }

    @Override
    public KbEntity<List<GroupModel>> getGroupListByKeyIn(String[] groupKeys) {
        if (groupKeys == null || groupKeys.length == 0) {
            return kbRes.ok();
        } else {
            List<String> keys = Arrays.asList(groupKeys);
            return kbRes.ok(groupService.getGroupListByKeyIn(keys).stream()
                    .map(group -> Mapper.map(group, GroupModel.class))
                    .collect(Collectors.toList()));
        }
    }

    @Override
    public KbEntity<List<GroupModel>> searchGroupInstanceListByName(
            Integer siteId, String channelKey, String groupName, Integer page, Integer pageSize) {
        return kbRes.ok(groupService.searchGroupPageByName(siteId, channelKey, groupName, page, pageSize)
                .map(group -> Mapper.map(group, GroupModel.class)));
    }

    @Override
    public KbEntity<Void> addArticleToGroup(String articleId, String groupKey, Long idx) {
        NewsArticle article = articleService.getArticleById(articleId).orElseThrow(
                () -> new KbException(KbCode.RESOURCE_NOT_FOUND, "article not found: " + articleId).r("文章不存在").li());
        IdsGroup group = groupService.getGroup(null, groupKey).orElseThrow(
                () -> new KbException(KbCode.RESOURCE_NOT_FOUND, "group not found: " + groupKey).r("分组不存在").li());
        groupService.upsertArticleToGroup(article, group, idx);
        return kbRes.ok();
    }

    @Override
    public KbEntity<Void> removeArticleFromGroup(String articleId, String groupKey) {
        groupService.removeArticleFromGroup(articleId, groupKey);
        return kbRes.ok();
    }

    @Override
    public KbEntity<Void> updateArticleGroups(String articleId, List<String> groupKeys) {
        NewsArticle article = articleService.getArticleById(articleId).orElseThrow(
                () -> new KbException(KbCode.RESOURCE_NOT_FOUND, "article not found: " + articleId).r("文章不存在").li());
        List<IdsGroupArticle> list = groupService
                .getGroupArticlePageByArticle(articleId, 1, 100)
                .getContent();
        List<String> listForRemove = new LinkedList<>();
        List<String> listForAdd = new LinkedList<>(groupKeys);
        for (String addGroup : groupKeys) {
            for (IdsGroupArticle ga : list) {
                if (addGroup.equals(ga.getGroup())) {
                    listForAdd.remove(addGroup);
                } else {
                    listForRemove.add(addGroup);
                }
            }
        }
        if (!listForRemove.isEmpty()) {
            groupService.removeArticleFromMultiGroup(articleId, groupKeys);
        }
        if (!listForAdd.isEmpty()) {
            Map<String, IdsGroup> gaMap = groupService.getGroupMapByKeyIn(listForAdd);
            for (String groupKey : listForAdd) {
                IdsGroup group = gaMap.get(groupKey);
                if (group != null) {
                    groupService.upsertArticleToGroup(article, group, null);
                }
            }
        }
        return kbRes.ok();
    }

    @Override
    public KbEntity<Void> setArticleAsTop(String articleId, String groupKey, Long idx) {
        if (idx == null) {
            idx = 0L;
        }
        groupService.updateIdx(articleId, groupKey, idx);
        return kbRes.ok();
    }

    @Override
    public KbEntity<Void> cancelArticleAsTop(String articleId, String groupKey) {
        groupService.updateIdx(articleId, groupKey, null);
        return kbRes.ok();
    }

    @Override
    public KbEntity<Void> bulkUpdateArticleOrder(UpdateOrderModel model) {
        groupService.bulkUpdateIdx(model);
        return kbRes.ok();
    }

    @Override
    public KbEntity<Void> setArticleBatchAndIdx(String articleId, String groupKey, Long batch, Long batchIdx) {
        groupService.updateBatch(articleId, groupKey, batch, batchIdx);
        return kbRes.ok();
    }

    @Override
    public KbEntity<Void> bulkRefreshArticleOrder(String secret, String groupKey) {
        if ("2815d11dfabcf7959defc1aa5ed778d9".equals(secret)) {
            groupService.bulkRefreshGroupArticleOrder(groupKey);
            return kbRes.ok();
        } else {
            return kbRes.err(KbCode.AUTH_FAIL);
        }
    }

    @Override
    public KbEntity<GroupPruneResult> pruneObsoletesByGroup(String secret, String groupKey) {
        if ("cdf5c3fd1749e35cc0bf43752681622e".equals(secret)) {
            return kbRes.ok(groupService.pruneObsoletesByGroup(groupKey));
        } else {
            return kbRes.err(KbCode.AUTH_FAIL);
        }
    }

    @Override
    public KbEntity<GroupPruneResult> pruneObsoletesAll(String secret) {
        if ("ab94d6873332cc36ab3ce8d79c87fd1e".equals(secret)) {
            return kbRes.ok(groupService.pruneObsoletesAll());
        } else {
            return kbRes.err(KbCode.AUTH_FAIL);
        }
    }

    @Override
    public KbEntity<List<ArticleModel>> getArticleListByGroup(
            String groupKey, Long idxMin, Long batch, Integer page, Integer pageSize,
            Boolean withContent, Boolean withStat, Boolean withPushCount
    ) {
        IdsGroup group = groupService.getGroup(null, groupKey).orElseThrow(() ->
                new KbException(KbCode.RESOURCE_NOT_FOUND, "group not found: " + groupKey).r("分组不存在").li());
        Page<IdsGroupArticle> igaPage = groupService.getGroupArticlePageByGroup(group, idxMin, batch, page, pageSize);
        List<String> idList = igaPage.stream().map(IdsGroupArticle::getArticleId).collect(Collectors.toList());
        Map<String, NewsArticle> articleMap = articleService.getArticleMapByIdIn(idList);
        List<ArticleModel> articleList = igaPage.getContent().stream().map(iga -> {
            NewsArticle article = articleMap.get(iga.getArticleId());
            if (article == null) {
                return null;
            } else {
                ArticleModel model = Mapper.map(article, ArticleModel.class);
                model.setIdx(iga.getIdx());
                model.setSeq(iga.getSeq());
                model.setBatch(iga.getBatch());
                model.setBatchIdx(iga.getBatchIdx());
                model.setBatchSeq(iga.getBatchSeq());
                if (withContent == null || !withContent) {
                    model.setContent(null);
                }
                if (withStat) {
                    model.setStat(NewsArticleModelHelper.createArticleStat(model, sensorsService));
                }
                if (withPushCount) {
                    NewsArticleModelHelper.attachPushStat(model, cacheService.getPushCount(article.getId()));
                }
                return model;
            }
        }).filter(Objects::nonNull).collect(Collectors.toList());
        return kbRes.ok(articleList).setPageInfo(igaPage);
    }

    @Override
    public KbEntity<TopicLayoutModel> createLayout(TopicLayoutCreateModel model) {
        TopicLayout layout = layoutService.createInstance(model);
        return kbRes.ok(NewsTopicLayoutModelHelper.layout2model(layout));
    }

    @Override
    public KbEntity<TopicLayoutModel> updateLayout(TopicLayoutUpdateModel model) {
        if (model.getId() == null) {
            return kbRes.err(KbCode.REQUEST_PARAM_MISSING, "missing id", "请提供布局标识");
        }
        TopicLayout layout = Mapper.map(model, TopicLayout.class);
        TopicLayout updated = layoutService.updateLayout(layout);
        return kbRes.ok(NewsTopicLayoutModelHelper.layout2model(updated));
    }

    @Override
    public KbEntity<TopicLayoutModel> initLayoutBanner(String layoutId) {
        TopicLayout layout = layoutService.initAndUpdateBanner(layoutId);
        return kbRes.ok(NewsTopicLayoutModelHelper.layout2model(layout));
    }

    @Override
    public KbEntity<TopicLayoutModel> updateLayoutBannerEnabled(String layoutId, Boolean enabled) {
        TopicLayout layout = layoutService.updateBannerEnabled(layoutId, enabled);
        return kbRes.ok(NewsTopicLayoutModelHelper.layout2model(layout));
    }

    @Override
    public KbEntity<TopicLayoutModel> initLayoutBelt(String layoutId) {
        TopicLayout layout = layoutService.initAndUpdateBelt(layoutId);
        return kbRes.ok(NewsTopicLayoutModelHelper.layout2model(layout));
    }

    @Override
    public KbEntity<TopicLayoutModel> updateLayoutBeltEnabled(String layoutId, Boolean enabled) {
        TopicLayout layout = layoutService.updateBeltEnabled(layoutId, enabled);
        return kbRes.ok(NewsTopicLayoutModelHelper.layout2model(layout));
    }

    @Override
    public KbEntity<TopicLayoutModel> initLayoutIconGrid(String layoutId) {
        TopicLayout layout = layoutService.initAndUpdateIconGrid(layoutId);
        return kbRes.ok(NewsTopicLayoutModelHelper.layout2model(layout));
    }

    @Override
    public KbEntity<TopicLayoutModel> updateLayoutIconGridEnabled(String layoutId, Boolean enabled) {
        TopicLayout layout = layoutService.updateIconGridEnabled(layoutId, enabled);
        return kbRes.ok(NewsTopicLayoutModelHelper.layout2model(layout));
    }

    @Override
    public KbEntity<TopicLayoutModel> updateLayoutVoteEnabled(String layoutId, Boolean enabled) {
        TopicLayout layout = layoutService.updateVoteEnabled(layoutId, enabled);
        return kbRes.ok(NewsTopicLayoutModelHelper.layout2model(layout));
    }

    @Override
    public KbEntity<TopicLayoutModel> updateLayoutContent(String layoutId, String content, String contentType) {
        if (contentType != null && content == null) {
            return kbRes.err(KbCode.REQUEST_PARAM_MISSING, "missing content", "请填写内容");
        }
        if (contentType == null && content != null) {
            return kbRes.err(KbCode.REQUEST_PARAM_MISSING, "missing content type", "请填写内容类型");
        }
        NewsContentType.resolveByName(contentType).orElseThrow(
                () -> new KbException(KbCode.ILLEGAL_ARGUMENT, "unknown type").r("未知的内容类型").li());
        TopicLayout layout = layoutService.updateContent(layoutId, content, contentType);
        return kbRes.ok(NewsTopicLayoutModelHelper.layout2model(layout));
    }

    @Override
    public KbEntity<TopicLayoutModel> updateLayoutVideo(TopicLayoutUpdateVideoModel model) {
        TopicLayout layout = layoutService.updateVideoInfo(model);
        return kbRes.ok(NewsTopicLayoutModelHelper.layout2model(layout));
    }

    @Override
    public KbEntity<TopicLayoutModel> updateLayoutFloat(String layoutId, TopicFloat floatIcon) {
        TopicLayout layout = layoutService.updateFloatIcon(layoutId, floatIcon);
        return kbRes.ok(NewsTopicLayoutModelHelper.layout2model(layout));
    }

    @Override
    public KbEntity<TopicLayoutModel> getLayoutById(String layoutId) {
        return layoutService.getLayoutById(layoutId)
                .map(NewsTopicLayoutModelHelper::layout2model)
                .map(kbRes::ok)
                .orElseGet(() -> kbRes.err(KbCode.RESOURCE_NOT_FOUND));
    }

    @Override
    public KbEntity<List<TopicLayoutModel>> getLayoutList(Integer page, Integer pageSize) {
        return kbRes.ok(layoutService.getLayoutPage(page, pageSize)
                .map(NewsTopicLayoutModelHelper::layout2model));
    }

    @Override
    public KbEntity<GroupTopicModel> createTopic(Integer siteId, GroupTopicCreateModel model) {
        model.setSiteId(siteId);
        GroupTopic instance = topicService.createInstance(model);
        return kbRes.ok(NewsGroupTopicApiService.topic2model(instance));
    }

    @Override
    public KbEntity<GroupTopicModel> updateTopic(GroupTopicUpdateModel model) {
        if (model.getId() == null) {
            return kbRes.err(KbCode.REQUEST_PARAM_MISSING, "missing id", "请提供标识");
        }
        GroupTopic topic = Mapper.map(model, GroupTopic.class);
        LinkedList<String> groupKeys = new LinkedList<>();
        //基础tab处理
        if (model.getTabs() != null) {
            if (model.getTabs().isEmpty()) {
                topic.setTabs(model.getTabs());
            } else {
                verifyTabs(model.getTabs(), true);
                collectTabGroupKeys(model.getTabs(), groupKeys);
            }
        }
        //头部tab处理
        if (model.getHeadTabs() != null) {
            if (model.getHeadTabs().isEmpty()) {
                topic.setHeadTabs(model.getHeadTabs());
            } else {
                verifyTabs(model.getHeadTabs(), true);
                collectTabGroupKeys(model.getHeadTabs(), groupKeys);
            }
        }
        if (!groupKeys.isEmpty()) {
            topic.setGroupKeys(KbColUtils.ListOpt.upsertLinkedList(groupKeys, topic.getGroupKeys()));
        }
        GroupTopic updated = topicService.updateInstance(topic, "更新数据");
        topicApiService.invalidate(updated.getId());
        return kbRes.ok(NewsGroupTopicApiService.topic2model(updated));
    }

    @Override
    public KbEntity<Void> addTabToTopic(
            String topicId, String tabName, String tabType, String displayStyleJson, String tabKey, Boolean init) {
        GroupTopic topic = topicService.getTopicById(topicId).orElseThrow(
                () -> new KbException(KbCode.RESOURCE_NOT_FOUND).r("话题不存在").li());
        boolean initByKey = init != null && init;
        GroupTopicTab tab = new GroupTopicTab();
        tab.setType(tabType);
        tab.setName(tabName);
        tab.setKey(tabKey);
        tab.setDisplayStyleJson(displayStyleJson);
        topicService.addTab(topic, tab, initByKey);
        topicApiService.invalidate(topicId);
        return kbRes.ok();
    }

    @Override
    public KbEntity<Void> updateTopicTab(
            String topicId, String tabType, String tabKey, String displayStyleJson, String name, String state, Boolean selected) {
        GroupTopic topic = topicService.getTopicById(topicId).orElseThrow(
                () -> new KbException(KbCode.RESOURCE_NOT_FOUND).r("话题不存在").li());
        if (topic.getTabs() == null || topic.getTabs().isEmpty()) {
            return kbRes.msg(KbCode.ILLEGAL_STATE, "该话题不存在 tab");
        }
        boolean isTabSelected = selected != null && selected;
        for (GroupTopicTab tab : topic.getTabs()) {
            if (tabType.equals(tab.getType()) && tabKey.equals(tab.getKey())) {
                if (name != null) {
                    tab.setName(name);
                }
                if (state != null) {
                    tab.setState(state);
                }
                tab.setDisplayStyleJson(displayStyleJson);
                tab.setSelected(isTabSelected ? true : null);
            } else {
                if (isTabSelected) {
                    tab.setSelected(null);
                }
            }
        }
        topicService.updateInstance(topic, "更新 tab 数据: " + tabKey);
        topicApiService.invalidate(topicId);
        return kbRes.ok();
    }

    @Override
    public KbEntity<Void> removeTopicTab(
            String topicId, String tabType, String tabKey) {
        GroupTopic topic = topicService.getTopicById(topicId).orElseThrow(
                () -> new KbException(KbCode.RESOURCE_NOT_FOUND).r("话题不存在").li());
        if (topic.getTabs() == null || topic.getTabs().isEmpty()) {
            return kbRes.msg(KbCode.ILLEGAL_STATE, "该话题不存在 tab");
        }
        String tabName = null;
        for (int i = topic.getTabs().size() - 1; i >= 0; i --) {
            GroupTopicTab tab = topic.getTabs().get(i);
            if (tab.getType().equals(tabType) && tab.getKey().equals(tabKey)) {
                tabName = tab.getName();
                topic.getTabs().remove(i);
                break;
            }
        }
        if (tabName != null) {
            topicService.updateInstance(topic, "移除 tab: " + tabName);
            topicApiService.invalidate(topicId);
        }
        return kbRes.ok();
    }

    @Override
    public KbEntity<Void> addHeadTabToTopic(String topicId, String tabName, String tabType, String displayStyleJson, String tabKey, Boolean init) {
        GroupTopic topic = topicService.getTopicById(topicId).orElseThrow(
                () -> new KbException(KbCode.RESOURCE_NOT_FOUND).r("话题不存在").li());
        boolean initByKey = init != null && init;
        GroupTopicTab tab = new GroupTopicTab();
        tab.setType(tabType);
        tab.setName(tabName);
        tab.setKey(tabKey);
        tab.setDisplayStyleJson(displayStyleJson);
        topicService.addHeadTab(topic, tab, initByKey);
        topicApiService.invalidate(topicId);
        return kbRes.ok();
    }

    @Override
    public KbEntity<GroupTopicModel> updateTopicLayoutId(String topicId, String layoutId) {
        GroupTopic updated = topicService.updateLayoutId(topicId, layoutId);
        topicApiService.invalidate(topicId);
        return kbRes.ok(NewsGroupTopicApiService.topic2model(updated));
    }

    @Override
    public KbEntity<GroupTopicModel> updateTopicShare(
            String topicId, String title, String content, String image, String url) {
        ShareModel share = new ShareModel();
        share.setTitle(title);
        share.setContent(content);
        share.setImageUrl(image);
        share.setUrl(url);
        ShareModel.verifyAsWeb(share).validOrThrow();
        GroupTopic updated = topicService.updateShare(topicId, share);
        return kbRes.ok(NewsGroupTopicApiService.topic2model(updated));
    }

    @Override
    public KbEntity<GroupTopicModel> updateTopicState(String topicId, String state) {
        if (!GroupTopicState.resolveByName(state).isPresent()) {
            return kbRes.err(KbCode.REQUEST_PARAM_INVALID, "unknown state", "状态值错误");
        }
        GroupTopic updated = topicService.updateState(topicId, state);
        topicApiService.invalidate(topicId);
        return kbRes.ok(NewsGroupTopicApiService.topic2model(updated));
    }

    @Override
    public KbEntity<GroupTopicModel> updateTopicKey(String topicId, String key) {
        GroupTopic updated = topicService.updateKey(topicId, key);
        return kbRes.ok(NewsGroupTopicApiService.topic2model(updated));
    }

    @Override
    public KbEntity<GroupTopicModel> updateTopicChannel(String topicId, String channelKey) {
        GroupTopic updated = topicService.updateChannel(topicId, channelKey);
        return kbRes.ok(NewsGroupTopicApiService.topic2model(updated));
    }

    @Override
    public KbEntity<GroupTopicModel> updateTopicTabsTitle(String topicId, String tabsTitle) {
        GroupTopic updated = topicService.updateTabsTitle(topicId, tabsTitle);
        topicApiService.invalidate(topicId);
        return kbRes.ok(NewsGroupTopicApiService.topic2model(updated));
    }

    @Override
    public KbEntity<GroupTopicModel> updateTopicType(String topicId, String type) {
        GroupTopic updated = topicService.updateType(topicId, type);
        topicApiService.invalidate(topicId);
        return kbRes.ok(NewsGroupTopicApiService.topic2model(updated));
    }

    @Override
    public KbEntity<GroupTopicModel> updateTopicShareFloat(String topicId, TopicFloat shareFloat) {
        GroupTopic topic = topicService.updateShareFloat(topicId, shareFloat);
        topicApiService.invalidate(topicId);
        return kbRes.ok(NewsGroupTopicApiService.topic2model(topic));
    }

    @Override
    public KbEntity<GroupTopicModel> updateTopicTabNavStyle(String topicId, String tabNavStyle) {
        GroupTopic updated = topicService.updateTabNavStyle(topicId, tabNavStyle);
        topicApiService.invalidate(topicId);
        return kbRes.ok(NewsGroupTopicApiService.topic2model(updated));
    }

    @Override
    public KbEntity<GroupTopicModel> getTopicById(String topicId) {
        return topicService.getTopicById(topicId)
                .map(NewsGroupTopicApiService::topic2model)
                .map(kbRes::ok)
                .orElseGet(() -> kbRes.err(KbCode.RESOURCE_NOT_FOUND));
    }

    @Override
    public KbEntity<GroupTopicModel> getTopicByKey(String topicKey) {
        return topicService.getTopicByKey(topicKey)
                .map(NewsGroupTopicApiService::topic2model)
                .map(kbRes::ok)
                .orElseGet(() -> kbRes.err(KbCode.RESOURCE_NOT_FOUND));
    }

    @Override
    public KbEntity<ShareModel> getTopicShareById(String topicId) {
        return topicService.getTopicById(topicId)
                .map(GroupTopic::getShare)
                .map(kbRes::ok)
                .orElseGet(() -> kbRes.err(KbCode.RESOURCE_NOT_FOUND));
    }

    @Override
    public KbEntity<List<GroupTopicModel>> getTopicList(String channelKey, Integer page, Integer pageSize) {
        return kbRes.ok(topicService.getTopicPage(channelKey, page, pageSize)
                .map(NewsGroupTopicApiService::topic2model));
    }

    @Override
    public KbEntity<GroupTopicModel> getTopicByGroupKeyAsFirst(String groupKey) {
        return topicService.getTopicByGroupAsFirst(groupKey)
                .map(NewsGroupTopicApiService::topic2model)
                .map(kbRes::ok)
                .orElseGet(() -> kbRes.err(KbCode.RESOURCE_NOT_FOUND));
    }

    @Override
    public KbEntity<List<GroupTopicModel>> getTopicByQuery(GroupTopicQuery query) {
        return kbRes.ok(topicService.getTopicPageByQuery(query)
                .map(NewsGroupTopicApiService::topic2model));
    }

    @Override
    public KbEntity<List<GroupTopicModel>> getTopicListByGroupKey(
            String groupKey, Integer page, Integer pageSize) {
        return kbRes.ok(topicService.getTopicListByGroup(groupKey, page, pageSize).stream()
                .map(NewsGroupTopicApiService::topic2model)
                .collect(Collectors.toList()));
    }

    @Override
    public KbEntity<GroupSelectorModel> createSelectorInstance(String selector, String name) {
        GroupSelector created = selectorService.createSelector(selector, name);
        return kbRes.ok(Mapper.map(created, GroupSelectorModel.class));
    }

    @Override
    public KbEntity<GroupSelectorModel> updateSelectorInstance(String selector, String name) {
        GroupSelector updated = selectorService.updateSelector(selector, name);
        return kbRes.ok(Mapper.map(updated, GroupSelectorModel.class));
    }

    @Override
    public KbEntity<GroupSelectorMenuModel> createSelectorMenu(
            String selector, String groupKey, String name, String desc) {
        groupService.getGroup(null, groupKey).orElseThrow(() ->
                new KbException(KbCode.RESOURCE_NOT_FOUND, "group not found").r("分组不存在").li());
        if (selectorService.getMenuBySelectorAndGroup(selector, groupKey).isPresent()) {
            return kbRes.msg(KbCode.RESOURCE_ALREADY_EXIST, "分组在该选择器中已存在");
        }
        GroupSelectorMenu created = selectorService.createMenu(selector, groupKey, name, desc);
        return kbRes.ok(Mapper.map(created, GroupSelectorMenuModel.class));
    }

    @Override
    public KbEntity<GroupSelectorMenuModel> updateSelectorMenu(
            String menuId, String name, String desc) {
        GroupSelectorMenu updated = selectorService.updateMenu(menuId, name, desc);
        return kbRes.ok(Mapper.map(updated, GroupSelectorMenuModel.class));
    }

    @Override
    public KbEntity<Void> updateSelectorMenuIdx(String menuId, Long idx) {
        selectorService.updateMenuIdx(menuId, idx);
        return kbRes.ok();
    }

    @Override
    public KbEntity<Void> bulkUpdateSelectorMenuIdx(GroupSelectorOrderUpdater updater) {
        selectorService.bulkUpdateMenuIdx(updater);
        return kbRes.ok();
    }

    @Override
    public KbEntity<Void> removeSelectorMenu(String menuId) {
        selectorService.deleteMenu(menuId);
        return kbRes.ok();
    }

    @Override
    public KbEntity<List<GroupSelectorModel>> getSelectorList(
            String keyword, Integer page, Integer pageSize) {
        return kbRes.ok(selectorService.getSelectorPage(keyword, page, pageSize)
                .map(selector -> Mapper.map(selector, GroupSelectorModel.class)));
    }

    @Override
    public KbEntity<GroupSelectorModel> getSelector(String selector) {
        return selectorService.getSelectorByKey(selector)
                .map(s -> Mapper.map(s, GroupSelectorModel.class))
                .map(kbRes::ok)
                .orElseGet(() -> kbRes.err(KbCode.RESOURCE_NOT_FOUND));
    }

    @Override
    public KbEntity<List<GroupSelectorMenuModel>> getSelectorMenuList(
            String selector, Integer page, Integer pageSize) {
        Page<GroupSelectorMenu> menuPage = selectorService.getMenuListBySelector(selector, page, pageSize);
        Set<String> groupKeys = menuPage.getContent().stream()
                .map(GroupSelectorMenu::getGroup).collect(Collectors.toSet());
        Map<String, IdsGroup> groupMap = groupService.getGroupMapByKeyIn(groupKeys);
        return kbRes.ok(menuPage.map(menu -> {
            GroupSelectorMenuModel model = Mapper.map(menu, GroupSelectorMenuModel.class);
            model.setGroupModel(Mapper.map(groupMap.get(menu.getGroup()), GroupModel.class));
            return model;
        }));
    }

    @Override
    public KbEntity<GroupSelectorMenuModel> getSelectorMenuByGroup(String selector, String groupKey) {
        return selectorService.getMenuBySelectorAndGroup(selector, groupKey)
                .map(menu -> {
                    GroupSelectorMenuModel model = Mapper.map(menu, GroupSelectorMenuModel.class);
                    groupService.getGroup(null, menu.getGroup()).ifPresent(
                            group -> model.setGroupModel(Mapper.map(group, GroupModel.class)));
                    return model;
                })
                .map(kbRes::ok)
                .orElseGet(() -> kbRes.err(KbCode.RESOURCE_NOT_FOUND));
    }

    @Override
    public KbEntity<GroupSelectorMenuModel> getSelectorMenuById(String menuId) {
        return selectorService.getMenuById(menuId)
                .map(menu -> {
                    GroupSelectorMenuModel model = Mapper.map(menu, GroupSelectorMenuModel.class);
                    groupService.getGroup(null, menu.getGroup()).ifPresent(
                            group -> model.setGroupModel(Mapper.map(group, GroupModel.class)));
                    return model;
                })
                .map(kbRes::ok)
                .orElseGet(() -> kbRes.err(KbCode.RESOURCE_NOT_FOUND));
    }

    // ----------------------------------------------------

    private void verifyTabs(List<GroupTopicTab> tabs, boolean supportTabContainer) {
        if (tabs == null) {
            return;
        }
        for (GroupTopicTab tab : tabs) {
            if (tab.getType() == null || tab.getKey() == null || tab.getName() == null) {
                throw new KbException(KbCode.REQUEST_PARAM_INVALID, "missing tab info").r("tab 信息错误").li();
            }
            if (tab.getType().equals(GroupTabType.TAB_CONTAINER.name())) {
                if (!supportTabContainer) {
                    throw new KbException(KbCode.REQUEST_PARAM_INVALID, "missing tab container tabs").r("tab 容器参数错误,不支持更多容器嵌套").li();
                }
            }

            verifyTabs(tab.getTabs(), false);
        }
    }

    private void collectTabGroupKeys(List<GroupTopicTab> tabs, List<String> groupKeys) {
        for (GroupTopicTab tab : tabs) {
            if (tab.getType().equals(GroupTabType.ARTICLE_GROUP.name())) {
                groupKeys.add(tab.getKey());
            }
            if (tab.getTabs() != null && ! tab.getTabs().isEmpty()) {
                collectTabGroupKeys(tab.getTabs(), groupKeys);
            }
        }
    }

    // ----------------------------------------------------

    @PostMapping("/usr/fetchGroupArticleFromGroups")
    public KbEntity<List<ArticleModel>> fetchGroupArticleFromGroups(
            @RequestParam() String secret,
            @RequestParam() String configKey,
            @RequestParam() Long st,
            @RequestParam() Long et
    ) {
        if (!"fc9435678c3f30d95216cacc6dd8c70f".equals(secret)) {
            return kbRes.err(KbCode.AUTH_FAIL);
        }
        if (et < st) {
            return kbRes.msg("终止时间小于起始时间");
        } else if (et - st > 7 * 86400_000L) {
            return kbRes.msg("时间跨度太长");
        }
        return kbRes.ok(fetcherService.fetchArticleByConfigKey(configKey, st, et));
    }

}
