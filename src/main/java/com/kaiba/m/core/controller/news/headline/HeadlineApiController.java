package com.kaiba.m.core.controller.news.headline;

import com.kaiba.lib.base.assembler.Mapper;
import com.kaiba.lib.base.constant.news.article.NewsContentType;
import com.kaiba.lib.base.domain.common.ShareModel;
import com.kaiba.lib.base.domain.news.article.ArticleModel;
import com.kaiba.lib.base.domain.news.pool.ArticleListModel;
import com.kaiba.lib.base.lang.collections.BuilderMap;
import com.kaiba.lib.base.middleware.KbRes;
import com.kaiba.lib.base.response.KbCode;
import com.kaiba.lib.base.response.KbEntity;
import com.kaiba.lib.base.util.appaction.AppActionType;
import com.kaiba.m.core.domain.news.pool.bygroup.IdsGroup;
import com.kaiba.m.core.service.news.article.NewsArticleCacheService;
import com.kaiba.m.core.service.news.pool.bygroup.NewsGroupApiService;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.stream.Collectors;

/**
 * author: lyux
 * date: 2023-07-28
 */
@Slf4j
@RestController
@RequestMapping("/api/headline")
public class HeadlineApiController {

    private final KbRes kbRes;
    private final NewsArticleCacheService articleCacheService;
    private final NewsGroupApiService idsByGroupApiService;
    private final String shareUrl;

    public HeadlineApiController(
            @Qualifier("kbApiRes") KbRes kbRes,
            @Value("${kb.host.page}") String pageHost,
            NewsArticleCacheService articleCacheService,
            NewsGroupApiService idsByGroupApiService
    ) {
        this.kbRes = kbRes;
        this.articleCacheService = articleCacheService;
        this.idsByGroupApiService = idsByGroupApiService;
        this.shareUrl = "https://" + pageHost + "/headline?siteId=";
    }

    @PostMapping(path = "/getArticleList")
    public KbEntity<ArticleListModel> getArticleList(
            @RequestParam() Integer siteId,
            @RequestParam(required = false) String lastId,
            @RequestParam(required = false) String lastMark,
            @RequestParam(required = false, defaultValue = "1") Integer page,
            @RequestParam(required = false, defaultValue = "20") Integer pageSize
    ) {
        String groupKey = getGroupKey(siteId);
        IdsGroup group = idsByGroupApiService.getGroupByKeyOrThrow(groupKey);
        ArticleListModel cached = idsByGroupApiService.getArticleList(group, lastMark, lastId, page, pageSize);
        ArticleListModel model = Mapper.map(cached, ArticleListModel.class);
        model.setShare(buildArticleShare(siteId));
        return kbRes.ok(model);
    }

    @ApiOperation("首页要闻词条滚动器")
    @PostMapping(path = "/getTickerArticleList")
    public KbEntity<List<ArticleModel>> getTickerArticleList(
            @RequestParam() Integer siteId
    ) {
        String groupKey = getGroupKey(siteId);
        IdsGroup group = idsByGroupApiService.getGroupByKeyOrThrow(groupKey);
        ArticleListModel cached = idsByGroupApiService
                .getArticleList(group, null, null, 1, 15);
        List<ArticleModel> articles = cached.getArticles().stream()
                .filter(article -> article.getIdx() != null)
                .map(article -> {
                    ArticleModel m = new ArticleModel();
                    m.setId(article.getId());
                    m.setTitle(article.getTitle());
                    m.setContentType(article.getContentType());
                    m.setAction(AppActionType.PAGE_HOMEPAGE_IMPORTANT_NEWS_DETAIL.getAction());
                    m.setActionParams(new BuilderMap<String, Object>()
                            .putValue("id", m.getId())
                            .putValue("cascade", true)
                    );
                    return m;
                })
                .collect(Collectors.toList());
        return kbRes.ok(articles);
    }

    @PostMapping("/getArticleDetail")
    public KbEntity<ArticleModel> getArticleDetail(
            @RequestParam() String articleId
    ) {
        return articleCacheService.getSummaryById(articleId)
                .map(article -> {
                    if (NewsContentType.HTML.name().equals(article.getContentType())
                            || NewsContentType.HTML_NEO.name().equals(article.getContentType())
                            || NewsContentType.PLAIN_TEXT.name().equals(article.getContentType())) {
                        article.setContent(articleCacheService.getContentById(articleId));
                    }
                    return article;
                })
                .map(kbRes::ok)
                .orElseGet(() -> kbRes.err(KbCode.RESOURCE_NOT_FOUND));
    }

    private static String getGroupKey(Integer siteId) {
        return "headline." + siteId;
    }

    private ShareModel buildArticleShare(Integer siteId) {
        ShareModel shareModel = new ShareModel();
        shareModel.setTitle("首页要闻");
        shareModel.setContent("点击查看更多");
        shareModel.setImageUrl("https://static.kaiba315.com.cn/kaiba-logo.png");
        shareModel.setUrl(shareUrl + siteId);
        return shareModel;
    }

}
