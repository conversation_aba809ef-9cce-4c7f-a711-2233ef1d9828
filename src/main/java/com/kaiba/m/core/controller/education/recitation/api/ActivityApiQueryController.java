package com.kaiba.m.core.controller.education.recitation.api;

import com.kaiba.lib.base.constant.KbHeader;
import com.kaiba.lib.base.middleware.KbRes;
import com.kaiba.lib.base.response.KbEntity;
import com.kaiba.m.core.controller.education.recitation.mapper.RecitationConMapping;
import com.kaiba.m.core.domain.education.recitation.Activity;
import com.kaiba.m.core.domain.education.recitation.enums.ActivityTypeEnum;
import com.kaiba.m.core.model.education.recitation.ActivityModel;
import com.kaiba.m.core.service.education.recitation.ActivityQueryService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.util.List;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * Description: 朗诵团活动Controller
 * Author: ZM227
 * Date: 2024/8/1 17:29
 */
@Slf4j
@RestController
@RequestMapping("/api/recitation/ifu/activity/query")
@Api(tags = "活动查询")
public class ActivityApiQueryController {

    @Resource
    private KbRes kbRes;
    @Resource
    private ActivityQueryService activityQueryService;
    @Resource
    private RecitationConMapping recitationConMapping;

    /**
     * 查询当前有效的活动
     *
     * @param userId       当前操作开吧用户Id
     * @param activityCode 活动Code
     * @param activityType 活动类型
     * @return 活动数据
     */
    @PostMapping("/queryActiveActivity")
    @ApiOperation(value = "查询当前有效活动")
    public KbEntity<ActivityModel> queryActiveActivity(
        @RequestHeader(name = KbHeader.KB_USER_ID, required = false) Integer userId,
        @RequestParam(required = false) String activityCode, @RequestParam Integer activityType) {
        Long current = System.currentTimeMillis();
        List<Activity> activityList = activityQueryService.findExistActivity(activityCode, current,
            current, ActivityTypeEnum.forCode(activityType));
        if (CollectionUtils.isEmpty(activityList)) {
            return kbRes.ok();
        }
        return kbRes.ok(recitationConMapping.domainToModel(activityList.get(0)));
    }
}
