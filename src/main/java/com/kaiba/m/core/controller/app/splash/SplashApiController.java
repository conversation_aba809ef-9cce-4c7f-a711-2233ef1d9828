package com.kaiba.m.core.controller.app.splash;

import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.LoadingCache;
import com.kaiba.lib.base.annotation.apiparam.KbSiteId;
import com.kaiba.lib.base.constant.splash.SplashState;
import com.kaiba.lib.base.domain.splash.SplashModel;
import com.kaiba.lib.base.middleware.KbRes;
import com.kaiba.lib.base.response.KbEntity;
import com.kaiba.lib.base.service.ISplashService;
import io.swagger.annotations.ApiOperation;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.time.Duration;
import java.util.List;

/**
 * author: lyux
 * date: 2023-07-24
 */
@Slf4j
@RestController
@RequestMapping("/api/splash")
@Validated
public class SplashApiController {

    private static final int SPLASH_LIST_SIZE = 20;

    private final KbRes kbRes;
    private final ISplashService splashService;

    private final LoadingCache<Integer, SplashInfoModel> splashInfoCache = Caffeine.newBuilder()
            .expireAfterWrite(Duration.ofMinutes(1))
            .build(this::getSplashInfoModelBySiteId);

    public SplashApiController(
            KbRes kbRes,
            ISplashService splashService
    ) {
        this.kbRes = kbRes;
        this.splashService = splashService;
    }

    @PostMapping("/getSplashListBySiteId")
    @ApiOperation("根据siteId获取未开始和正在进行中的开屏广告列表")
    public KbEntity<SplashInfoModel> getSplashListBySiteId(
            @RequestParam() @KbSiteId Integer siteId
    ) {
        return kbRes.ok(splashInfoCache.get(siteId));
    }

    private SplashInfoModel getSplashInfoModelBySiteId(Integer siteId) {
        Integer[] states = new Integer[] { SplashState.ACTIVE.getValue() };
        List<SplashModel> list = splashService.getSplashListByEndTimeGT(
                siteId, states,System.currentTimeMillis() / 1000,1, SPLASH_LIST_SIZE,false).getData();
        SplashInfoModel model = new SplashInfoModel();
        model.splashModelList = list;
        model.showStartupVideo = true;
        return model;
    }

    @Data
    @NoArgsConstructor
    public static class SplashInfoModel {
        private List<SplashModel> splashModelList;
        private Boolean showStartupVideo;
    }

}
