package com.kaiba.m.core.domain.hoolo;

import java.util.Date;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * Description: 葫芦网内容模型
 * Author: ZM227
 * Date: 2024/9/11 13:58
 */
@Data
@NoArgsConstructor
@ToString
@Document(collection = "k_hoolo_news")
public class HooloNews {

    /**
     * 主键
     */
    @Id
    private String id;

    /**
     * 新闻UK
     */
    @Indexed
    private String oriId;

    /**
     * 新闻UK
     */
    private String articleId;

    /**
     * 作者
     */
    private String author;

    /**
     * 简介
     */
    private String brief;

    /**
     * 新闻关键字
     */
    private String keyWords;

    /**
     * 标题
     */
    private String title;

    /**
     * 子标题
     */
    private String subTitle;

    /**
     * 来源频道
     */
    private String source;

    /**
     * 码率
     */
    private String bitRate;

    /**
     * 分类ID
     */
    private Integer categoryId;

    /**
     * 状态
     * 1-有效
     */
    private Integer status;

    /**
     * 时长（秒）
     */
    private Double duration;

    /**
     * 格式化后时长
     */
    private String durationFormat;

    /**
     * 封面图
     */
    private String indexPic;

    /**
     * 视频链接
     */
    private String videoUrl;

    /**
     * 封面类型
     */
    private Integer coverType;

    /**
     * 来源类型
     */
    private String sourceType;

    /**
     * 拓展oriId
     */
    private String extendOriId;

    /**
     * 拓展id
     */
    private String extendId;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 发布时间
     */
    private Date publishTime;

    /**
     * 葫芦网原始数据
     */
    private Object oriContent;

}
