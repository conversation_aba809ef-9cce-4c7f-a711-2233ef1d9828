package com.kaiba.m.core.domain.artmap;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * Description: 艺术地图每日推荐记录
 * Author: ZM227
 * Date: 2025/6/6 15:12
 */
@Data
@NoArgsConstructor
@ToString
@Document(collection = "k_art_map_recommend")
public class Recommend {

    /**
     * 主键
     */
    @Id
    private String id;

    /**
     * 推荐编码
     */
    @Indexed
    private String recommendCode;

    /**
     * 推荐标题
     */
    private String title;

    /**
     * 推荐开始时间
     */
    private Long startTime;

    /**
     * 推荐结束时间
     */
    private Long endTime;

    /**
     * 推荐分组编码
     */
    private String groupKey;

    /**
     * 分组名称
     */
    private String groupName;

    /**
     * 推荐状态 0-下架 1-上架
     */
    private Integer status;

    /**
     * 创建时间
     */
    private Long createTime;

    /**
     * 更新时间
     */
    private Long updateTime;

}
