package com.kaiba.m.core.domain.program;

import com.kaiba.lib.base.constant.program.RebroadcastType;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.List;

/**
 * author wangsj
 * date 2020-09-01
 *
 * 排班实例:
 * 具体的某天的某个时间段的节目排班. 比如 2020-01-01 中午 12:00-1:00 的 [我的汽车有话说].
 */
@Data
@ToString
@NoArgsConstructor
@Document(collection = "k_schedule_instance")
public class ScheduleInstance {

    @Id
    private String id;

    /** 节目. {@link Program} */
    @Indexed
    private String programId;

    /** 电台 id */
    @Indexed
    private Integer siteId;

    /** 开始时间, 时间戳. 单位秒. */
    @Indexed
    private Integer start;

    /** 结束时间, 时间戳. 单位秒. */
    private Integer end;

    /** 主持人列表. {@link Emcee} */
    private List<String> emceeId;

    /** 是否由模板生成 */
    private Boolean isDefault;

    /** true-为空白档 */
    private Boolean isEmpty;

    /** 是否是重播 */
    private Boolean rebroadcast;

    /** 重播对应的相应的实例标识 {@link ScheduleInstance#getId()} */
    private String rebroadcastId;

    /** 重播关联到相关节目的策略. {@link RebroadcastType} */
    private String rebroadcastType;

    /** 帖子板块 id */
    private String threadId;

    /** 直播管理后台用, 待播帖子板块 id */
    private String boardThreadId;

    /** 直播管理后台用, 已播帖子板块 id */
    private String broadcastThreadId;

    /** 直播管理后台用, 打赏帖子板块 id */
    private String rewardThreadId;

    /** 点赞数 */
    private Integer praise;

    /** 浏览数 */
    private Integer view;

    /** 创建者 */
    private Integer createUser;

    /** 创建时间, 单位秒 */
    private Long createTime;

    /** 更新者用户 id */
    private Integer updateUser;

    /** 更新时间, 单位秒 */
    private Long updateTime;

}
