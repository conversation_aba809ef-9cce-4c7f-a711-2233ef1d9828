package com.kaiba.m.core.domain.news.pool.topic;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * author: lyux
 * date: 2024-08-28
 *
 * 话题布局中的视频直播排期.
 * TODO: 需要一个时间规则, 以便指定一批时间
 *
 */
@Data
@ToString
@NoArgsConstructor
@Document(collection = "k_news_topic_video_scheduler")
public class TopicVideoScheduler {

    @Id
    private String id;

    /** 话题布局标识. {@link TopicVideoScheduler} */
    @Indexed
    private String layoutId;

    /** 更新时间, 单位毫秒. */
    private Long updateTime;

    /** 创建时间, 单位毫秒. */
    private Long createTime;

}
