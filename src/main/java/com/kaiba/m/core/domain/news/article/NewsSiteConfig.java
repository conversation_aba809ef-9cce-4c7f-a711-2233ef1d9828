package com.kaiba.m.core.domain.news.article;

import com.kaiba.lib.base.domain.site.SiteModel;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * author: lyux
 * date: 2023-07-26
 */
@Data
@ToString
@NoArgsConstructor
@Document(collection = "k_news_site_config")
public class NewsSiteConfig {

    @Id
    private String id;

    @Indexed(unique = true)
    private Integer siteId;

    /** 冗余字段, {@link SiteModel#getName()}. 不会跟随 site 表变更而变更, 仅为查找数据库时方便. */
    private String siteName;

    /** 默认的组织名称. 会被预填到 {@link NewsArticle#getOrgName()} */
    private String orgName;

    /** 资讯评论总板 */
    private String threadId;

    /** 更新时间, 单位毫秒. */
    private Long updateTime;

    /** 创建时间, 单位毫秒. */
    private Long createTime;

}
