package com.kaiba.m.core.domain.appversion;

import com.kaiba.lib.base.constant.appversion.AppUpdatePolicy;
import com.kaiba.lib.base.constant.appversion.AppVersionState;
import java.util.List;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

import java.time.LocalDateTime;

/**
 * author: lyux
 * date: 2022-11-11
 *
 * 开吧约定正式版本号为点分 3 位, 测试版本号允许出现点分第 4 位. 举例:
 * 正式版本号: 6.58.3 ; 测试版本号: 6.58.1.2
 *
 * 版本号有如下规则:
 * * 将字符串形式的版本号转换为数字版本号时, 规则为字符串中的每个点分位站数字中的两位.
 * * 将数字版本号转换为字符串形式的版本号时, 规则为数字每两位代表一个点分位.
 * * 字符版本号不补零.
 * * 点分首位版本号不可为零, 点分其余位允许为零.
 *
 * 应用版本表中, packageId 和 code 的组合唯一.
 */
@Data
@ToString
@NoArgsConstructor
@Document("k_app_version")
public class AppVersion {

    /** 逻辑主键 */
    @Id
    private String id;

    /**
     * 应用标识. 用于唯一的区分一个具体 APP 应用. 创建后不可修改.
     * 若是 Android 端则是 package name, 如 com.hz.czfw.app
     * 若是 iOS 端则是 bundle id, 如 cn.czfw.HangZhou
     * 若是 ohos鸿蒙端则是 com.kaiba.app
     */
    @Indexed
    private String packageId;

    /** 版本号, 如 65803. 创建后不可修改. */
    @Indexed
    private Integer code;

    /** 版本名, 如 6.58.3. 必须和 {@link #code} 相匹配, 创建后不可修改. */
    private String name;

    /** 版本状态. {@link AppVersionState} */
    private Integer state;

    /** Android / iOS / ohos. 创建后不可修改. */
    private String device;

    /** 版本信息展示页面地址 */
    private String displayUrl;

    /** 版本包下载地址 */
    private String packageUrl;

    /** 面向用户的低版本向本版本升级时的提示语. */
    private String content;

    /** 面向内部的版本描述, 可具体描述版本解决了哪些问题, 发现了哪些问题. */
    private String description;

    /**
     * 当升级提示风格为 {@link AppUpdatePolicy#RECOMMEND} 时, 此为是否再次提示用户的标识.
     * 当客户端检测到新的标识和已经显示过升级提示的版本标识不同时, 则再次弹出升级提示框.
     */
    private String mark;

    /**
     * 客户端打包盐值, 用于签名算法.
     * 使用数组是为了防止意外情况下同版本客户端包内预置盐值不同. 签名时会按数组顺序逐个匹配.
     */
    private List<String> salt;

    /** 更新时间, ms */
    private Long updateTime;

    /** 创建时间, ms */
    private Long createTime;

}
