package com.kaiba.m.core.domain.news.article;

import com.kaiba.lib.base.constant.news.NewsChannel;
import com.kaiba.lib.base.constant.news.article.*;
import com.kaiba.lib.base.domain.IActionGetterSetter;
import com.kaiba.lib.base.domain.common.Image;
import com.kaiba.lib.base.domain.common.ShareModel;
import com.kaiba.lib.base.domain.news.QuickReplyCategoryModel;
import com.kaiba.lib.base.domain.news.article.ArticleAudio;
import com.kaiba.lib.base.domain.news.article.ArticleVideo;
import com.kaiba.lib.base.domain.news.article.NewsModuleConfigModel;
import com.kaiba.lib.base.domain.news.pool.bygroup.GroupModel;
import com.kaiba.lib.base.lang.dsl.timedisplay.DisplayTimeFormatter;
import com.kaiba.lib.base.response.KbCode;
import com.kaiba.lib.base.response.KbException;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.IndexDirection;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * author: lyux
 * date: 2023-07-26
 *
 * 新资讯文章池
 */
@Data
@ToString
@NoArgsConstructor
@Document(collection = "k_news_article")
public class NewsArticle implements IActionGetterSetter {

    @Id
    private String id;

    /** 文章创建者 */
    @Indexed
    private Integer creator;

    /** 电台 id */
    @Indexed
    private Integer siteId;

    /** 内容所属频率频道. {@link NewsChannel} */
    @Indexed
    private String channelKey;

    /** 频率频道的下属部门标识, 目前仅用于考核数据统计. */
    private String departKey;

    /** 所属分组. {@link GroupModel#getKey()} */
    @Indexed
    private Set<String> groups;

    /** 所属模块列表. {@link NewsModuleConfigModel#getModule()}. */
    @Indexed
    private Set<String> moduleIndex;

    /** 文章状态 {@link NewsState} */
    private String state;

    /** 业务类型. {@link NewsBusinessType}. 可不填. */
    private String businessType;

    /** 详情页以何种形式渲染. {@link NewsRenderer} */
    private String renderer;

    /** 浏览样式设置, 默认为 {@link DisplayViewStyle#EYE}. */
    private String viewStyle;

    /** 互动按钮设置, 默认为 {@link DisplayReactStyle#LIKE}. */
    private String reactStyle;

    /** 评论功能配置, 默认为 {@link DisplayReplyStyle#FREE} */
    private String replyStyle;

    /** 客户端展示时间时, 要使用哪个时间类型. 默认为 {@link ArticleTimeType#HIDE_TIME}. */
    private String displayTimeBy;

    /** 客户端展示时间格式. {@link DisplayTimeFormatter} */
    private String displayTimeFormat;

    /**
     * 前端展示时间, 单位毫秒. 默认为空, 仅在特殊情况下才需要配置该字段.
     * * 默认为空, 表示遵循 {@link #displayTimeBy} 规则, 包括 默认/递补/覆盖 规则.
     * * 如果不为空, 则前端时间优先展示文章自身配置的时间.
     */
    private Long displayTime;

    /** 分享配置 */
    private ShareModel share;

    /** 评论板块 id. 第一个评论发起时动态创建. */
    private String threadId;

    /** 快速回复类别. quick reply type. {@link QuickReplyCategoryModel} */
    private String qrCategoryId;

    // ---------------------------------------------------------
    // 内容字段

    /** 标题 */
    private String title;

    /** 副标题 */
    private String subtitle;

    /** 封面 */
    private List<Image> covers;

    /** 内容字段类型, {@link NewsContentType}, 用来指明 {@link #content} 字段的格式. */
    private String contentType;

    /** 正文内容 */
    private String content;

    /** 正文视频 */
    private ArticleVideo video;

    /** 正文音频 */
    private ArticleAudio audio;

    /** 视频文件处理状态. {@link ArticleVideoState}. */
    private String videoState;

    /** 投票实例 id */
    private String voteId;

    /** 如果 {@link #renderer} 为 {@link NewsRenderer#NONE}, 此为跳转页面. */
    private String action;

    /** 如果 {@link #renderer} 为 {@link NewsRenderer#NONE}, 此为跳转页面参数. */
    private Map<String, Object> actionParams;

    /** 以 key-value 存储的属性值 */
    private Map<String, String> attr;

    // ---------------------------------------------------------
    // 创作者信息

    /** 文章协作者 */
    private Set<Integer> authors;

    /** 签发者 */
    private Integer issuer;

    /** 来源信息 */
    private String origin;

    /** 组织名称, 未填写则默认为电台名 **/
    private String orgName;

    /** 作者名, 默认次序为 authorName -> {@link #authors} -> {@link #creator} */
    private String authorName;

    /** 主编, 未填写则默认为 {@link #issuer} */
    private String chiefEditor;

    /** 是否原创 */
    private Boolean isOriginal;

    // ---------------------------------------------------------
    // 辅助字段

    /** 省宣: 稿件链接 */
    private String asTmuLink;

    /** 省宣: 预定的自动投递时间. 单位毫秒. */
    private Long asTmuSendTime;

    /** 省宣: 是否已经递送为省宣稿件. */
    private Boolean asTmu;

    /** 签发时间, 单位毫秒. */
    @Indexed(direction = IndexDirection.DESCENDING)
    private Long signTime;

    /** 上线时间, 单位毫秒. */
    @Indexed(direction = IndexDirection.DESCENDING)
    private Long onlineTime;

    /** 创建时间, 单位毫秒. */
    @Indexed(direction = IndexDirection.DESCENDING)
    private Long createTime;

    /** 更新时间, 单位毫秒. */
    private Long updateTime;

    /** 归档时间, 单位毫秒. */
    private Long archiveTime;

    /** 如果文章代表一个节目, 此为播出开始时间, 单位毫秒. */
    @Indexed(direction = IndexDirection.DESCENDING)
    private Long releaseTime;

    /** 如果文章代表一个节目, 此为播出结束时间, 单位毫秒. */
    private Long releaseEndTime;

    /** 省宣稿件投递时间, 单位毫秒. */
    private Long tmuSendTime;

    // ---------------------------------------------------------
    // 辅助字段

    public Long obtainTimeByType(ArticleTimeType type) {
        switch (type) {
            case CREATE_TIME:
                return createTime;
            case UPDATE_TIME:
                return updateTime;
            case SIGN_TIME:
                return signTime;
            case ONLINE_TIME:
                return onlineTime;
            case ARCHIVE_TIME:
                return archiveTime;
            case RELEASE_TIME:
                return releaseTime;
            default:
                throw new KbException(KbCode.ILLEGAL_ARGUMENT, "unknown time type: " + type);
        }
    }

}
