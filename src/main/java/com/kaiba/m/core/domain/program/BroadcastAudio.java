package com.kaiba.m.core.domain.program;

import com.kaiba.lib.base.domain.common.AppVersionRange;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.List;

@Data
@NoArgsConstructor
@Document(collection = "k_broadcast_audio")
public class BroadcastAudio {
    private String id;

    private Integer siteId;

    private String name;

    /** 流地址 */
    private String url;

    /** {@link com.kaiba.lib.base.constant.program.BroadcastAudioChannel} **/
    private String channel;

    private String channelId;

    /** 是否重播 */
    private Boolean rebroadcast;

    /** 是否默认 */
    private Boolean isDefault;

    /** 禁止使用版本 */
    private List<AppVersionRange> exclusiveVersion;

    /** {@link com.kaiba.lib.base.constant.program.BroadcastAudioPlayer} **/
    private String iOSPlayer;

    /** {@link com.kaiba.lib.base.constant.program.BroadcastAudioPlayer} **/
    private String androidPlayer;
}
