package com.kaiba.m.core.domain.issue;

import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.List;

/**
 * author: lyux
 * date: 18-9-11
 */
@Data
@NoArgsConstructor
@Document(collection = "issue_expert")
public class IssueExpert {

    @Id
    private Integer id;

    private String userName;

    private String avatar;

    private String mobile;

    private Integer channelId;
    
    private Integer sex;

    private Integer level;

    private Integer rank;

    private Integer source;

    private String company;

    private String description;

    private List<IssuePayLevel> payLevels;

    private List<IssuePayLevel> phonePayLevels;

    private List<String> specialBrand;

    private List<String> specialArea;

    private List<String> certificate;

    private List<String> idCard;

    private String idCardNumber;

    private Boolean recommend;

    private Long createTime;

    private Long loginTime;

    private Double satisfactionRatio;

    private Double speedRatio;

    private Double qualityRatio;

    private Integer answerCount;

    private Integer praiseCount;

    private Integer followerCount;

    private Float income;

}
