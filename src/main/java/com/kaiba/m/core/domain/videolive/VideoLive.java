package com.kaiba.m.core.domain.videolive;

import com.kaiba.lib.base.domain.common.Image;
import com.kaiba.lib.base.domain.common.Video;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/5/27
 */
@Data
@ToString
@NoArgsConstructor
@Document(collection = "k_videolive")
public class VideoLive {

    /**
     * 主键
     */
    @Id
    private String id;

    /**
     * 电台 id
     */
    @Indexed
    private Integer siteId;

    /**
     * 地市编码
     */
    private Integer source;

    /**
     * 直播对应的聊天室 id
     */
    private String chatId;

    /**
     * 视频直播流地址,返回给前端直接使用,同时为管理平台设置拉流地址的字段
     */
    private String videoUrl;

    @Indexed
    private Integer state;

    /**
     * 主持人列表
     */
    @Indexed
    private List<Integer> emcees;

    /**
     * 标题
     */
    private String title;

    /**
     * 封面图, 节目预热时显示
     */
    private Image warmUpCover;

    /**
     * 封面图, 节目开始后显示
     */
    private Image startedCover;

    /**
     * 预热视频
     */
    private Video warmUpVideo;

    /**
     * 直播间背景图
     */
    private String background;

    /**
     * 直播间预热状态描述
     */
    private String description;

    /**
     * 主播预设的开始时间, 仅作为前端页面展示使用, 不参与任何逻辑. 单位秒.
     */
    private Long presetStartTime;

    /**
     * 主播预设的结束时间, 仅作为前端页面展示使用, 不参与任何逻辑. 单位秒.
     */
    private Long presetEndTime;

    /**
     * 直播创建时间
     */
    @Indexed
    private Long createTime;

    /**
     * 直播开始的时间点. 由系统在直播状态变为开始时设定. 单位秒.
     */
    private Long startTime;

    /**
     * 直播结束的时间点. 由系统在直播状态变为结束时设定. 单位秒.
     */
    private Long endTime;

    /**
     * 点击开始直播的时间
     */
    private Long realStartTime;

    /**
     * 点击结束直播的时间
     */
    private Long realEndTime;

    /**
     * http拉流地址, 通过云信获取
     */
    private String httpPullUrl;

    /**
     * hls拉流地址, 通过云信获取
     */
    private String hlsPullUrl;

    /**
     * rtmp拉流地址, 通过云信获取
     */
    private String rtmpPullUrl;

    /**
     * 推流地址
     */
    private String pushUrl;

    private Integer creator;

    /**
     * 0为竖屏，1为横屏，不传默认竖屏
     */
    private Integer liveStreamingOrientation;

    /** 是否横屏播放 默认0 */
    private Integer landscape;

    /** 是否隐藏弹幕区 默认0 不隐藏 */
    private Integer hideComment;

    /** 是否隐藏点赞按钮 默认0 不隐藏 */
    private Integer hidePraise;

    private Integer lastOperator;

    /** 是否前端展示 */
    private Boolean isDisplay;

    /**
     * 直播间直播时浮动弹窗描述
     */
    private String floatText;

    /** 虚拟计数规则 (技术是无罪的!), 真实浏览数会经此计算得到展示浏览数. 为空则表示不启用虚拟数. */
    private String vcExpression;

    /** 虚拟计数规则说明, 会伴随 {@link #vcExpression} 生成. */
    private String vcDescription;

    /** 内容所属频率频道 */
    private String channelKey;
}
