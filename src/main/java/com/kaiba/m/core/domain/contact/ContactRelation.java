package com.kaiba.m.core.domain.contact;

import com.kaiba.lib.base.domain.contact.ContactModel;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * author: lyux
 * date: 19-11-20
 *
 * 联系人之间的关系.
 * {@link #subjectContactId} 是 {@link #contactId} 的 {@link #relation}.
 * 举例, 张三是小明的舅舅. 那么:
 * {@link #subjectContactId} 为张三
 * {@link #contactId} 为小明
 * {@link #relation} 取值 "舅舅"
 */
@Data
@ToString
@NoArgsConstructor
@Document(collection = "k_contact_relation")
public class ContactRelation {

    @Id
    private String id;

    /** 联系人 ID, 主语. {@link ContactModel} */
    @Indexed
    private String subjectContactId;

    /** 关系名称 */
    private String relation;

    /** 联系人 ID, 谓语. {@link ContactModel} */
    @Indexed
    private String contactId;

    /** 创建时间, 单位毫秒 */
    private Long createTime;

}
