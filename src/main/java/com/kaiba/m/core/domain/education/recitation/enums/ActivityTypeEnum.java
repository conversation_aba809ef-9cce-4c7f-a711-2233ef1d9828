package com.kaiba.m.core.domain.education.recitation.enums;

import java.util.Arrays;
import java.util.Objects;
import lombok.Getter;

/**
 * Description: 活动类型枚举
 * Author: ZM227
 * Date: 2024/8/8 17:56
 */
@Getter
public enum ActivityTypeEnum {

    SOUND_JOURNEY(1, "声音环游记"),
    RECITATION_CLUB(2, "朗诵团");

    private final Integer code;
    private final String desc;

    ActivityTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static ActivityTypeEnum forCode(Integer code) {
        if (Objects.isNull(code)) {
            return null;
        }
        return Arrays.stream(ActivityTypeEnum.values())
            .filter(type -> Objects.equals(type.getCode(), code)).findFirst().orElse(SOUND_JOURNEY);
    }
}
