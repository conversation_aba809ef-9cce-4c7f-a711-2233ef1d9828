package com.kaiba.m.core.domain.contact;

import com.kaiba.lib.base.constant.contact.ContactFixedGroup;
import com.kaiba.lib.base.constant.contact.ContactState;
import com.kaiba.lib.base.domain.contact.IContact;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.List;
import java.util.Map;

/**
 * author: lyux
 * date: 19-11-20
 */
@Data
@ToString
@NoArgsConstructor
@Document(collection = "k_contact")
public class Contact implements IContact {

    @Id
    private String id;

    /** 组标识. {@link ContactGroup#getKey()} */
    @Indexed
    private String groupKey;

    /** 状态. {@link ContactState}. 会影响排序字段 {@link #weight} 的生成. */
    private Integer state;

    /** 用户 id. 非用户地址时可为空. */
    @Indexed
    private Integer userId;

    /** 联系人姓名 */
    private String name;

    /** 性别. {@link com.kaiba.lib.base.constant.user.UserGender} */
    private Integer gender;

    /** 手机号码 */
    @Indexed
    private String phone;

    /** 联系电话. 并不限制是手机还是座机. */
    private List<String> tels;

    /** 详细地址 */
    private String address;

    /** 地区码. 如 330101 */
    private String code;

    /** 省, 必须和 {@link #code} 一致, 如: 浙江省 */
    private String province;

    /** 市, 必须和 {@link #code} 一致, 如: 杭州市 */
    private String city;

    /** 区县, 必须和 {@link #code} 一致, 如: 上城区 */
    private String county;

    /** 街道, 如望江街道 */
    private String district;

    /** 电邮 */
    private String email;

    /** 身份证号 */
    private String idCard;

    /** 紧急联系人. {@link ContactFixedGroup#EMERGENCY_CONTACT} */
    private String emergencyContactId;

    /** 备注 */
    private String remark;

    /** 经度 */
    private Double longitude;

    /** 纬度 */
    private Double latitude;

    /** 排序依据, 根据规则策略计算得到, 策略选择详见代码. 默认列表按此字段降序排列. */
    @Indexed
    private Long weight;

    /**
     * 是否默认联系人. 该字段是排序规则 {@link #weight} 依赖的基础数据之一.
     * 默认联系人在同一个 userId, groupKey 下只能存在一个.
     * 只有 {@link ContactGroup#getAllowDefault()} 为 true 的分组下才允许设置默认联系人.
     */
    private Boolean isDefault;

    /** 额外的键值对信息 */
    private Map<String, String> attr;

    /** 刷新时间, 单位毫秒. 用作排序规则 {@link #weight} 依赖的基础数据之一. */
    private Long refreshTime;

    /** 更新时间, 单位毫秒 */
    private Long updateTime;

    /** 创建时间, 单位毫秒 */
    private Long createTime;

}
