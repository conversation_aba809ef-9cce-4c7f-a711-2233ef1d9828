package com.kaiba.m.core.domain.note;

import com.kaiba.lib.base.domain.note.NoteLink;
import com.kaiba.lib.base.util.StringUtils;
import com.kaiba.lib.base.util.appaction.AppActionType;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.Collections;
import java.util.List;

@Data
@NoArgsConstructor
@Document(collection = "k_note_comment")
public class NoteComment {

    /** 主键 */
    @Id
    private String id;

    /** 帖子 id */
    @Indexed
    private String noteId;

    /** 电台 id */
    @Indexed
    private Integer siteId;

    /** 发帖用户 id */
    @Indexed
    private Integer userId;

    /** 所属板块列表 */
    @Indexed
    private List<String> threads;

    /** 对谁说 */
    private Integer toUserId;

    /** 对哪个回复进行回复 */
    private String toCommentId;

    /** 回复内容 */
    private String content;

    /** {@link com.kaiba.lib.base.constant.note.NoteState} */
    private Integer state;

    /** 发回复时用户坐标-经度 */
    private Double longitude;

    /** 发回复时用户坐标-纬度 */
    private Double latitude;

    /** 逆地理信息得到的街道名称 */
    private String street;

    /** 是否已经被标记为删除. 一般由用户删除会开启此标志. */
    @Deprecated
    private Boolean isSoftDeleted;

    /** 创建时间, 单位秒.  */
    @Indexed
    private Long createTime;

    /** 创建时间, 单位毫秒. */
    private Long createTimeMS;

    /** 自动回复链接 */
    private NoteLink link;

    public void genReplyLink(String url) {
        if (!StringUtils.isEmpty(url)) {
            link = new NoteLink();
            link.setTitle("了解更多");
            link.setIcon("note_comment_link_0924.png");
            link.setAction(AppActionType.PAGE_WEB.getAction());
            link.setActionParams(Collections.singletonMap("url", url));
        }
    }
}
