package com.kaiba.m.core.domain.user;

import com.kaiba.lib.base.domain.user.IKbAddress;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * author: lyux
 * date: 19-11-20
 */
@Data
@ToString
@NoArgsConstructor
@Document(collection = "k_user_address")
public class UserAddress implements IKbAddress {

    @Id
    private String id;

    /** 用户 id */
    @Indexed
    private Integer userId;

    /** 真实的收件人姓名 */
    private String name;

    /** 收件人电话 */
    private String tel;

    /** 收件人地址 */
    private String address;

    /** 地区码. 如 330101 */
    private Integer code;

    /** 省, 必须和 {@link #code} 一致, 如: 浙江省 */
    private String province;

    /** 市, 必须和 {@link #code} 一致, 如: 杭州市 */
    private String city;

    /** 区县, 必须和 {@link #code} 一致, 如: 上城区 */
    private String county;

    /** 是否用户的默认地址 */
    private Boolean isDefault;

    /** 更新时间, 单位秒 */
    private Long updateTime;

    /** 创建时间, 单位秒 */
    private Long createTime;

}
