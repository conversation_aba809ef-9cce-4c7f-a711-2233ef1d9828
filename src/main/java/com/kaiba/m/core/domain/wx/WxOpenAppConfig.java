package com.kaiba.m.core.domain.wx;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * <AUTHOR>
 * @version WxConfig, v0.1 2024/4/17 16:00 daopei Exp $
 **/
@Data
@ToString
@NoArgsConstructor
@Document(collection = "k_wx_open_app_config")
public class WxOpenAppConfig {

    /** siteId */
    @Id
    private Integer id;
    /** 微信appId */
    private String appId;
    /** 代理用户ID: 使用此用户来代理微信用户进行站内操作, 包含业务:节目互动发帖 */
    private Integer proxyUserId;
    /** 公众号是否绑定了开吧开放平台,未绑定则UnionId不互通 */
    private Boolean kbBind;
    /** 创建时间 */
    private Long createTime;
    /** 修改时间 */
    private Long updateTime;

}
