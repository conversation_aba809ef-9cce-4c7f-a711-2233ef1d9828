package com.kaiba.m.core.domain.da.eventtrack;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.Set;

/**
 * author: lyux
 * date: 2025-03-07
 *
 * 标准稿件/维表.
 */
@Data
@ToString
@NoArgsConstructor
@Document(collection = "k_event_track_doc_module")
public class ETDocModule {

    @Id
    private String id;

    /** 模块. */
    @Indexed(unique = true)
    private String module;

    /** 模块分类. */
    private Set<String> category;

    /** 模块名称. */
    private String name;

    /** 创建时间 */
    private Long createTime;

    /** 修改时间 */
    private Long updateTime;

}
