package com.kaiba.m.core.domain.da.eventtrack;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * 标准稿件推送游标, 每次取最近
 * <AUTHOR>
 * @version ETDocPushCursor, v0.1 2025/4/11 15:07 daopei Exp $
 **/
@Data
@ToString
@NoArgsConstructor
@Document(collection = "k_event_track_doc_push_cursor")
public class ETDocPushCursor {

    @Id
    private String id;

    /** 同步表标识. [dim_doc_info] */
    @Indexed(unique = true)
    private String mark;

    /** 时间基准线. 上次同步使用的时间上限 */
    private Long timestamp;

    /** 创建时间 */
    private Long createTime;

    /** 修改时间 */
    private Long updateTime;
}
