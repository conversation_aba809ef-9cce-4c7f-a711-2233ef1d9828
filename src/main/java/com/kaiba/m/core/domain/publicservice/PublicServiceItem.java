package com.kaiba.m.core.domain.publicservice;

import com.kaiba.lib.base.constant.publicservice.ServiceState;
import com.kaiba.lib.base.domain.common.Image;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.CompoundIndex;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/01/05 14:18
 */
@Data
@ToString
@NoArgsConstructor
@Document(collection = "k_public_service_item")
@CompoundIndex(name = "uni_key_name", def = "{'instanceKey': 1, 'name': 1}", unique = true)
public class PublicServiceItem {

    @Id
    private String id;

    /** 电台id */
    private Integer siteId;

    /** 实例 key */
    private String instanceKey;

    /** 添加服务的操作人id */
    private Integer userId;

    /** 服务名称 */
    private String name;

    /** 服务描述 */
    private String desc;

    /** 状态 {@link ServiceState} */
    private Integer state;

    /** 图标 */
    private Image icon;

    /** action */
    private String action;

    /** actionParams */
    private Map<String, Object> actionParams;

    /** 创建时间 ms */
    private Long createTime;

    /** 更新时间 ms */
    private Long updateTime;

}
