package com.kaiba.m.core.domain.da.sensors.kanban;

import com.kaiba.lib.base.domain.da.sensors.kanban.KanbanConfigModel;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.List;

/**
 * <AUTHOR>
 * @version KanbanConfigGroup, v0.1 2024/3/18 11:29 daopei Exp $
 **/
@Data
@ToString
@NoArgsConstructor
@Document(collection = "k_sensors_kanban_config_group")
public class KanbanConfigGroup {


    @Id
    private String id;

    /**
     * 分组名称
     **/
    private String name;

    /**
     * 分组标识
     **/
    @Indexed(unique = true)
    private String key;

    /**
     * 包含表格列表 {@link KanbanConfigModel}
     **/
    private List<String> configIds;

    /**
     * 创建时间
     **/
    private Long createTime;

    /**
     * 修改时间
     **/
    private Long updateTime;
}
