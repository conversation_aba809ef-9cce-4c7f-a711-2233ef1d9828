package com.kaiba.m.core.domain.news.pool.bytag;

import com.kaiba.m.core.domain.news.article.NewsArticle;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * author: lyux
 * date: 2023-07-27
 *
 * [文章 id 聚合策略] 多实例标签: 使用 "标签组" 和 "标签" 两级对文章进行分组
 * 标签和资讯关联表. 除排序字段 idx 外, 所有字段创建后均不可修改.
 */
@Data
@ToString
@NoArgsConstructor
@Document(collection = "k_news_ids_tag_article")
public class IdsTagArticle {

    @Id
    private String id;

    /** {@link IdsTag#getId()} */
    @Indexed
    private String tagId;

    /**
     * 标签类型. {@link IdsTag#getGroup()} 的冗余字段.
     * 因标签类型创建后不可修改, 所以可认为该字段是固定的.
     */
    private String tagGroup;

    /**
     * 标签名称. {@link IdsTag#getName()} 的冗余字段.
     * 因标签名称创建后不可修改, 所以可以认为该字段始终和标签名称一致.
     */
    private String tagName;

    /** {@link NewsArticle#getId()} */
    @Indexed
    private String articleId;

    /** 查询时以此字段降序排列. */
    @Indexed
    private Long seq;

    /**
     * 人工指定的排序依据, 降序, 最大值 999, 允许重复.
     * 最终会根据算法生成 {@link #seq}. 因此本字段仅做记录, 实际排序应使用 {@link #seq} 字段
     */
    @Indexed(sparse = true)
    private Long idx;

    /** 创建时间, 单位毫秒. */
    private Long createTime;

}
