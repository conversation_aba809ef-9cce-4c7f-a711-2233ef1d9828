package com.kaiba.m.core.domain.appwidget.textticker;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.CompoundIndex;
import org.springframework.data.mongodb.core.index.CompoundIndexes;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * <AUTHOR>
 * @version TextTickerInstance, v0.1 2023/7/17 16:43 daopei Exp $
 **/
@Data
@ToString
@NoArgsConstructor
@Document("k_app_text_ticker_instance")
@CompoundIndexes({
        @CompoundIndex(unique = true, def = "{ siteId: 1, key: 1}", name = "uniq_key_site"),
})
public class TextTickerInstance {

    /**
     * 主键
     */
    @Id
    private String id;

    /**
     * 逻辑主键.
     */
    private String key;

    /**
     * 电台标识
     */
    private Integer siteId;

    /**
     * 自动翻页的间隔时间, 单位毫秒. 非正值表示不会自动翻页.
     */
    private Integer autoplay;

    /**
     * 最大显示行数
     */
    private Integer maxLine;

    /**
     * 简短名称, 只显示在管理后台
     */
    private String name;

    /**
     * 介绍, 只显示在管理后台. 非必填.
     */
    private String description;

    /**
     * 更新时间, 单位毫秒
     */
    private Long updateTime;

    /**
     * 创建时间, 单位毫秒
     */
    private Long createTime;

}
