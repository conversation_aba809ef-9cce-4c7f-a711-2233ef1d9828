package com.kaiba.m.core.domain.thirdparty;

import com.kaiba.lib.base.constant.accounting.BusinessType;
import com.kaiba.lib.base.constant.thirdparty.TPTransactionOrderCancelReason;
import com.kaiba.lib.base.constant.thirdparty.TPTransactionOrderState;
import com.kaiba.lib.base.domain.common.KbCallback;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.IndexDirection;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.List;

/**
 * author: lyux
 * date: 19-10-23
 *
 * 抽奖实例.
 */
@Data
@ToString
@NoArgsConstructor
@Document(collection = "k_third_party_transaction_order")
public class TPTransactionOrder {

    /** 主键, 支付订单 id. */
    @Id
    private String id;

    /** 三方在我方系统中的唯一识. {@link ThirdPartyAccess} */
    @Indexed
    private String accessId;

    /** 交易相关的用户 id. */
    @Indexed
    private Integer userId;

    /** 用户手机号. 一旦产生客诉等情况, 方便联系用户. */
    private String userMobile;

    /** 订单状态. {@link TPTransactionOrderState} */
    @Indexed
    private Integer state;

    /** 资金系统的业务类型. {@link BusinessType}. */
    private String businessType;

    /** 资金系统的业务 id. */
    @Indexed
    private String businessId;

    /** 资金系统内对应的交易 id */
    @Indexed
    private String transactionId;

    /** 交易金额, 单位元, 两位小数. */
    private String transactionAmount;

    /** 引用 id. */
    @Indexed
    private String refId1;

    /** 引用 id. */
    @Indexed
    private String refId2;

    /** 引用 id. */
    @Indexed
    private String refId3;

    /** 交易成功后要回调的 url. */
    private List<KbCallback> callbackList;

    /** 调起开吧支付页面, 支付成功后将跳转至该页面. */
    private String returnUrl;

    /** 交易备注 */
    private String comment;

    /** 取消订单的操作者 id */
    private Integer cancelUserId;

    /** 取消订单的原因编号. {@link TPTransactionOrderCancelReason} */
    private Integer cancelReasonType;

    /** 取消订单的具体原因 */
    private String cancelReason;

    /** 支付超时时间点, 单位秒. 超过此时间则订单转为超时状态. 若为空则没有超时时间. */
    @Indexed(direction = IndexDirection.DESCENDING)
    private Long payExpireTime;

    /** 状态更新时间, 单位秒. */
    private Long updateTime;

    /** 创建时间, 单位秒. 设置后不可修改. */
    private Long createTime;

    /** 用于校验更新操作的一致性, 即乐观锁实现 */
    private Long v;

    // -----------------------------------------------------------------------

}
