package com.kaiba.m.core.domain.banner;

import com.kaiba.lib.base.constant.banner.BannerState;
import com.kaiba.lib.base.domain.common.Image;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;
import java.util.Map;

@Data
@NoArgsConstructor
@Document(collection = "k_banner")
public class Banner {

    /** 主键 */
    @Id
    private String id;

    /** 电台标识 */
    @Indexed
    private Integer siteId;

    /** 属于哪个模块 */
    @Indexed
    private String module;

    /** 标题 */
    private String title;

    /** 内容 */
    private String content;

    /** 状态 参考 {@link BannerState} */
    private Integer state;
    /**
     * 由原始 url 经过 短链算法 得到的 id
     */
    private String mapperKey;

    /** 原始url */
    private String originalUrl;

    /** 展示时长 */
    private Integer autoplayDuration;

    /** 跳转类型. 参考 {@link com.kaiba.lib.base.util.appaction.AppActionType} */
    private String action;

    /** 跳转参数 */
    private Map<String, Object> actionParams;

    /** 所占屏位 */
    private Integer homeOrder;

    /** banner 图片 */
    private Image banner;

    /** 刘海屏 banner 图片 */
    private Image bannerNotch;

    /** 开始时间 单位毫秒*/
    private Long startTime;

    /** 结束时间 单位毫秒*/
    private Long endTime;

    /** 创建时间 单位毫秒*/
    private Long createTime;

    /** 创建者标识 */
    private Integer createUserId;

    /** 更新时间 单位毫秒*/
    private Long updateTime;

    /** 更新者标识 */
    private Integer updateUserId;

    /** 额外属性 */
    private Map<String, String> attrs;
}
