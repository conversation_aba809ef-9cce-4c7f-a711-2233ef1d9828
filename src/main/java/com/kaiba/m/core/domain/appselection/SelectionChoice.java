package com.kaiba.m.core.domain.appselection;

import com.kaiba.lib.base.constant.apphome.AppHomeSelectionStyle;
import com.kaiba.lib.base.constant.apphome.AppHomeSelectionTarget;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * author: lyux
 * date: 2023-07-27
 *
 * target 和 content 的关系表
 */
@Data
@ToString
@NoArgsConstructor
@Document(collection = "k_app_selection_choice")
public class SelectionChoice {

    @Id
    private String id;

    /** 电台 id. */
    @Indexed
    private Integer siteId;

    /** {@link AppHomeSelectionTarget} */
    @Indexed
    private String target;

    /** 展示样式. {@link AppHomeSelectionStyle} */
    private Integer style;

    /** {@link SelectionContent#getId()} */
    @Indexed
    private String contentId;

    /** 来源角标. 默认为空, 为空则会根据 {@link SelectionContent#getOrigin()} 赋值. */
    private String originName;

    /** 查询时以此字段降序排列. */
    @Indexed
    private Long seq;

    /**
     * 人工指定的排序依据, 降序, 最大值 999.
     * 最终会根据算法生成 {@link #seq}. 因此本字段仅做记录, 实际排序应使用 {@link #seq} 字段
     */
    @Indexed(sparse = true)
    private Long idx;

    /** 预约下线时间, 单位毫秒. */
    private Long scheduleEndTime;

    /** 预约置空 {@link #idx} 字段的时间, 单位毫秒. 置空 idx 表示取消置顶. */
    private Long scheduleDelIdxTime;

    /** 被选入的时间, 单位毫秒. */
    private Long selectTime;

    /** 创建时间, 单位毫秒. */
    private Long createTime;

}
