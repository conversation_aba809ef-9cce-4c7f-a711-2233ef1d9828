package com.kaiba.m.core.domain.artmap;

import com.kaiba.m.core.model.artmap.dto.VenuesCoverImageDTO;
import java.util.List;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.geo.GeoJsonPoint;
import org.springframework.data.mongodb.core.index.CompoundIndex;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * Description: 艺术地图场馆记录
 * Author: ZM227
 * Date: 2025/5/30 13:43
 */
@Data
@NoArgsConstructor
@ToString
@Document(collection = "k_art_map_venues")
@CompoundIndex(name = "coordinate_idx", def = "{'xAxis': 1, 'yAxis': 1}")
public class Venues {

    /**
     * 主键
     */
    @Id
    private String id;

    /**
     * 场馆编码
     */
    @Indexed
    private String venuesCode;

    /**
     * 场馆名称
     */
    private String name;

    /**
     * 场馆图片列表
     */
    private List<String> pictures;

    /**
     * 场馆地图贴图
     */
    private VenuesCoverImageDTO mapPicture;

    /**
     * 场馆联系电话
     */
    private String contactPhone;

    /**
     * 场馆地址
     */
    private String address;

    /**
     * 场馆描述
     */
    private String description;

    /**
     * 场馆状态 0-下线 1-上线
     */
    private Integer status;

    /**
     * 场馆类型
     * 1-剧场、2-体育场馆、3-景点、4-演艺新空间、5-博物馆、6-酒吧
     */
    private Integer venuesType;

    /**
     * 场馆经纬度
     */
    private GeoJsonPoint location;

    /**
     * 场馆x坐标
     */
    private Integer xAxis;

    /**
     * 场馆y坐标
     */
    private Integer yAxis;

    /**
     * 建议游玩时长
     */
    private String suggestTime;

    /**
     * 场馆开放时间
     */
    private String openingHours;

    /**
     * 剧院Id
     */
    private List<String> theaterIds;

    /**
     * 场馆排序字段
     */
    @Indexed
    private Integer seq = 999999;

    /**
     * 创建时间
     */
    private Long createTime;

    /**
     * 更新时间
     */
    private Long updateTime;


}
