package com.kaiba.m.core.domain.workorder;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * author: lyux
 * date: 2024-03-28
 *
 * 工单系统, 标签与案件的关联表
 */
@Data
@ToString
@NoArgsConstructor
@Document(collection = "k_work_order_tagged_case")
public class WOTaggedCase {

    @Id
    private String id;

    /** 案件 id. {@link WOCase#getId()}. */
    @Indexed
    private String caseId;

    /** {@link WOTag#getCode()} */
    @Indexed
    private Long tag;

    /** 直属父级标签. 冗余字段. */
    private Long parent;

    /** 排序依据, 查询时以此字段降序排列. 建议仅在同一个标签下进行排序. */
    @Indexed
    private Long seq;

    /**
     * 指定的排序依据, 降序, 最大值 999.
     * 最终会根据算法生成 {@link #seq}. 因此本字段仅做记录, 实际排序应使用 {@link #seq} 字段.
     */
    private Long idx;

    /** 创建者, 程序逻辑中打标签时可为空. 仅用做审计, 不参与逻辑. */
    private Integer userId;

    /** 更新时间, 单位毫秒. */
    private Long updateTime;

    /** 创建时间, 单位毫秒. */
    private Long createTime;

}
