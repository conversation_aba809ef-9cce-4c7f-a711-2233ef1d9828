package com.kaiba.m.core.domain.news.videoclip;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2023/07/12 17:56
 **/
public final class VideoClipFuncFlag {

    private static final long SHOW_LIKE = 1;

    private final long flag;

    public static Builder on() {
        return Builder.on();
    }

    public static Builder parse(long flag) {
        return Builder.parse(flag);
    }

    private VideoClipFuncFlag(long flag) {
        this.flag = flag;
    }

    @Data
    public static class Builder {
        private long flag = 0;
        private int showLike = 0;

        public static Builder on() {
            return new Builder();
        }

        public static Builder parse(long flag) {
            return new Builder(flag);
        }

        private Builder() {}

        private Builder(long flag) {
            this.flag = flag;
            this.showLike = (flag & SHOW_LIKE) == SHOW_LIKE ? 1 : 0;
        }

        public Builder setShowLike(int showLike) {
            this.showLike = showLike;
            return this;
        }

        public Long create() {
            if (showLike == 1) {
                flag = flag | SHOW_LIKE;
            }
            return flag;
        }
    }
}
