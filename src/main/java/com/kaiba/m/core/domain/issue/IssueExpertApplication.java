package com.kaiba.m.core.domain.issue;

import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.List;

/**
 * author: lyux
 * date: 18-9-11
 */
@Data
@NoArgsConstructor
@Document(collection = "issue_expert_application")
public class IssueExpertApplication {

    @Id
    private String id;

    @Indexed
    private Integer state;

    private Integer operatorId;

    private String refuseReason;

    @Indexed
    private Integer userId;

    private String userName;

    private String avatar;

    @Indexed
    private String mobile;

    private Integer sex;

    private Integer source;

    private Integer channelId;

    private String company;

    private String description;

    private List<String> specialBrand;

    private List<String> specialArea;

    private List<String> certificate;

    private List<String>  idCard;

    private String idCardNumber;

    private Long createTime;

    private Long updateTime;

}
