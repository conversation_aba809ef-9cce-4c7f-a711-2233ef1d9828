package com.kaiba.m.core.domain.education.recitation.enums;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import lombok.Getter;
import org.apache.commons.collections4.CollectionUtils;

/**
 * Description: 成员类型
 * Author: ZM227
 * Date: 2024/8/2 10:50
 */
@Getter
public enum MemberTypeEnum {

    MEMBER(1, "团员"),
    NON_MEMBER(0, "非团员");

    private final Integer code;
    private final String desc;

    MemberTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static MemberTypeEnum forCode(Integer code) {
        if (Objects.isNull(code)) {
            return null;
        }
        return Arrays.stream(MemberTypeEnum.values())
            .filter(type -> Objects.equals(type.getCode(), code)).findFirst().orElse(NON_MEMBER);
    }

    public static List<MemberTypeEnum> forListCode(List<Integer> codeList) {
        if (CollectionUtils.isEmpty(codeList)) {
            return null;
        }
        return codeList.stream().map(MemberTypeEnum::forCode).collect(Collectors.toList());
    }

}
