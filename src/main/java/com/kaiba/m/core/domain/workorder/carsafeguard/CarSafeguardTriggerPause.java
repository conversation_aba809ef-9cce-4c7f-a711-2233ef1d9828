package com.kaiba.m.core.domain.workorder.carsafeguard;

import com.kaiba.m.core.domain.workorder.WOCallbackTrigger;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * 汽车维权定时器暂停记录表
 * <AUTHOR>
 * @version CarSafeguardTriggerPause, v0.1 2024/7/19 11:05 daopei Exp $
 **/
@Data
@ToString
@NoArgsConstructor
@Document(collection = "k_work_order_car_safeguard_trigger_pause")
public class CarSafeguardTriggerPause {

    @Id
    private String id;

    /** 案件ID. WOCase */
    @Indexed
    private String caseId;

    /** 暂停后剩余时间. 毫秒数 */
    private Long pauseLessMils;

    /** 暂停操作用户ID */
    private Integer pauseUserId;

    /** 创建时间 */
    private Long createTime;

    /** 暂停前定时器快照暂存 */
    private WOCallbackTrigger trigger;

}
