package com.kaiba.m.core.domain.playback;

import com.kaiba.lib.base.constant.playback.PlaybackAlbumType;
import com.kaiba.lib.base.domain.program.EmceeModel;
import com.kaiba.lib.base.domain.program.ProgramModel;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * <AUTHOR>
 * @date 2019/7/31
 */
@Data
@NoArgsConstructor
@Document(collection = "k_playback_album")
public class PlaybackAlbum {

    /** 主键 */
    @Id
    private String id;

    /** 电台 id */
    @Indexed
    private Integer siteId;

    /** 专辑类型. {@link PlaybackAlbumType} */
    @Indexed
    private Integer type;

    /** 关联 id. 根据 type 不同, 可能是 {@link EmceeModel#getId()}, 也可能是 {@link ProgramModel#getId()}. */
    @Indexed
    private String referenceId;

    /** 该专辑下所有音频的播放总次数 */
    private Integer playCount;

    /** 该专辑下所有音频的播放每个月的播放总次数 */
    private Integer monthPlayCount;

    /** 专辑访问量 */
    private Integer viewCount;

    /** 该专辑下回播音频数量 */
    private Integer playbackCount;

    /** 标题 */
    private String title;

    /** 副标题 */
    private String subTitle;

    /** 封面图 url */
    private String cover;

    /** 创建者用户 id */
    private Integer creator;

    /** 创建时间 */
    private Long createTime;

    /** 内容是否可以被第三方电台合作伙伴分享 */
    private Boolean isShare;
}
