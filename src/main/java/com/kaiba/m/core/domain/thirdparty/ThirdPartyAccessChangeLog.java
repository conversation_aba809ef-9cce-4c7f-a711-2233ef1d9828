package com.kaiba.m.core.domain.thirdparty;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * author: lyux
 * date: 2020-09-02
 */
@Data
@ToString
@NoArgsConstructor
@Document(collection = "k_third_party_access_change_log")
public class ThirdPartyAccessChangeLog {

    @Id
    private String id;

    /** {@link ThirdPartyAccess#getId()} */
    @Indexed
    private String accessId;

    /** 操作者 id */
    private Integer operatorId;

    /** 改变前的摘要算法盐值 */
    private String salt;

    /** 改变前的令牌. */
    private String token;

    /** 改变前的鉴权私钥. */
    private String privateKey;

    /** 改变前的鉴权公钥. */
    private String publicKey;

    /** 创建时间, 单位毫秒 */
    private Long createTime;

}
