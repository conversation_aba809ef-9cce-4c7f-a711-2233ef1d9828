package com.kaiba.m.core.domain.playback;

import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/7/31
 */
@Data
@NoArgsConstructor
@Document(collection = "k_playback")
public class Playback {
    /** 主键 */
    @Id
    private String id;

    /** 所属专辑 id */
    @Indexed
    @Deprecated
    private String albumId;

    /** 所属专辑 id 数组*/
    @Indexed
    private List<String> albumIds;

    /** 电台 id */
    private Integer siteId;

    /** 标题 */
    private String title;

    /** 副标题 */
    private String subTitle;

    /** 封面 */
    private String cover;

    private Long createTime;

    /** 本期主持人 */
    private List<String> emcees;

    /** 音频 url */
    private String audioUrl;

    /** 音频时长, 单位秒 */
    private Long audioDuration;

    /** 音频大小, 单位 B */
    private Long audioSize;

    /** 对应的帖子板块 id */
    private String threadId;

    /** 对应节目的播出时间 */
    @Indexed
    private Long programTime;

    /** 播放次数 */
    private Integer playCount;

    /** 内容是否可以被第三方电台合作伙伴分享 */
    private Boolean isShare;

}
