package com.kaiba.m.core.domain.opensearch;

import com.kaiba.lib.base.constant.opensearch.SearchTopState;
import com.kaiba.lib.base.domain.common.Image;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/08/24 17:32
 */
@Data
@NoArgsConstructor
@Document(collection = "k_open_search_top")
public class SearchTop {

    @Id
    private String id;

    /** 电台id */
    private Integer siteId;

    /** 操作用户id */
    private Integer userId;

    /** 置顶数据状态 {@link SearchTopState } */
    private Integer state;

    /** 搜索模块 */
    private Integer searchType;

    /** 标题 */
    @Indexed
    private String title;

    /** 角标名称 */
    private String typeName;

    /** 搜索关键词 */
    @Indexed
    private List<String> keywords;

    /** 搜索框显示文案 */
    private String copywriting;

    /** 图片 */
    private Image image;

    /** 生效时间 ms 可为空 */
    @Indexed
    private Long startTime;

    /** 失效时间 ms 可为空 */
    @Indexed
    private Long endTime;

    /** 指定消息点击时的跳转页面. */
    private String action;

    /** 指定消息点击时的跳转页面, 参数. */
    private Map<String, Object> actionParams;

    /** 创建时间 ms */
    @Indexed
    private Long createTime;

    /** 更新时间 ms */
    @Indexed
    private Long updateTime;

}
