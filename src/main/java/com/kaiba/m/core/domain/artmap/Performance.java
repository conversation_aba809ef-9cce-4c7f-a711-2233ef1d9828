package com.kaiba.m.core.domain.artmap;

import com.kaiba.lib.base.domain.common.ActionLink;
import java.time.Instant;
import java.util.List;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * Description: 艺术地图演出记录
 * Author: ZM227
 * Date: 2025/6/11 14:04
 */
@Data
@NoArgsConstructor
@ToString
@Document(collection = "k_art_map_performance")
public class Performance {

    /**
     * 主键
     */
    @Id
    private String id;

    /**
     * 演艺演出编码
     */
    @Indexed
    private String performanceCode;

    /**
     * 演出类型
     * 1：演艺演出 2：文章帖子
     */
    private Integer performanceType;

    /**
     * 业务方标识code
     */
    private String bizCode;

    /**
     * 演出日期列表
     * 格式为 yyyy-MM-dd
     */
    @Indexed
    private List<Instant> performanceDates;

    /**
     * 演出标题
     */
    private String title;

    /**
     * 封面图片
     */
    private String coverUrl;

    /**
     * 状态
     * (0, "无效")
     * (1, "有效")
     */
    private Integer status;

    /**
     * 最低价
     */
    private Long priceLowest;

    /**
     * 最高价
     */
    private Long priceHighest;

    /**
     * 售卖开始时间
     */
    private Long saleStartTime;

    /**
     * 跳转链接
     */
    private ActionLink actionLink;

    /**
     * 创建时间
     */
    private Long createTime;

    /**
     * 更新时间
     */
    private Long updateTime;
}
