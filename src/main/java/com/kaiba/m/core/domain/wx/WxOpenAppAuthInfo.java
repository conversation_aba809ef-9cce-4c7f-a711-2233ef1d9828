package com.kaiba.m.core.domain.wx;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.List;

/**
 * 初始数据由wx_third_party_auth_info表迁移而来
 * <AUTHOR>
 * @version WxOpenAppAuthInfo, v0.1 2024/4/19 09:59 daopei Exp $
 **/
@Data
@ToString
@NoArgsConstructor
@Document(collection = "k_wx_open_app_auth_info")
public class WxOpenAppAuthInfo {

    /** 微信开发者ID,APPID */
    @Id
    private String id;
    /** 接口调用令牌（在授权的公众号/小程序具备 API 权限时，才有此返回值） */
    private String authorizerAccessToken;
    /** 刷新令牌（在授权的公众号具备API权限时，才有此返回值），
     * 刷新令牌主要用于第三方平台获取和刷新已授权用户的 authorizer_access_token。
     * 一旦丢失，只能让用户重新授权，才能再次拿到新的刷新令牌。
     * 用户重新授权后，之前的刷新令牌会失效 */
    private String authorizerRefreshToken;
    /** authorizer_access_token 的有效期（在授权的公众号/小程序具备API权限时，才有此返回值），单位：秒 */
    private Integer expiresIn;
    /** 授权给开发者的权限集列表 */
    private List<Integer> funcInfos;
    /** 创建时间 */
    private Long createTime;
    /** 修改时间 */
    private Long updateTime;
}
