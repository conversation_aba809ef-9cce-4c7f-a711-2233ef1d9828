package com.kaiba.m.core.domain.workorder;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * author: lyux
 * date: 2024-01-18
 *
 * 工单系统, 事件
 */
@Data
@ToString
@NoArgsConstructor
@Document(collection = "k_work_order_callback_trigger")
public class WOCallbackTrigger {

    @Id
    private String id;

    /** 业务标识 */
    private String biz;

    /** 案件 ID. {@link WOCase} */
    private String caseId;

    /** 事件 id. {@link WOEvent} */
    private String eventId;

    /** 回调标识. */
    private String mark;

    /** 拼接后的回调标识 */
    @Indexed
    private String fullMark;

    /** 启动时间, 时间戳, 单位毫秒. */
    @Indexed
    private Long fireTime;

    /** 失效时间, 时间戳, 单位毫秒. */
    @Indexed
    private Long expireTime;

    /** 更新时间, 单位毫秒. */
    private Long updateTime;

    // ------------------------------------------------

    public boolean isMatchMark(String caseId, String mark) {
        return this.caseId.equals(caseId) && this.mark.equals(mark);
    }

    public static String generateFullMark(String caseId, String mark) {
        return caseId + "-" + mark;
    }

}
