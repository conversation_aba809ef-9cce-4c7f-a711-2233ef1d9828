package com.kaiba.m.core.domain.user;

import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * author: lyux
 * date: 18-12-7
 */
@Data
@NoArgsConstructor
@Document(collection = "user_acc_wx")
public class UserAccWX {

    @Id
    private String id;

    @Indexed
    private Integer userId;

    /** {@link com.kaiba.lib.base.constant.wx.WXEntityEnum} */
    private String entity;

    @Indexed(unique = true)
    private String openId;

    /** 单位毫秒 */
    private Long createTime;

}
