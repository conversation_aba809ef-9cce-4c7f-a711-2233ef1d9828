package com.kaiba.m.core.domain.issue;

import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * author: lyux
 * date: 18-9-25
 */
@Data
@NoArgsConstructor
@Document(collection = "issue_lawyer_followee")
public class IssueLawyerFollowee {

    @Id
    private String id;

    @Indexed
    private Integer lawyerId;

    @Indexed
    private Integer userId;

    private Long createTime;

}
