package com.kaiba.m.core.domain.enroll;

import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;
/**
 * <AUTHOR>
 * @date 2019/12/09
 * 报名实例
 */
@Data
@NoArgsConstructor
@Document(collection = "k_enroll")
public class Enroll {

    /** 主键 */
    @Id
    private String id;

    /** 电台标识 */
    @Indexed
    private Integer siteId;

    /** 类型  {@link com.kaiba.lib.base.constant.enroll.EnrollType}*/
    @Indexed
    private Integer type;

    /** 报名的状态. {@link com.kaiba.lib.base.constant.enroll.EnrollState} */
    @Indexed
    private Integer state;

    /** 报名的状态. {@link com.kaiba.lib.base.constant.enroll.EnrollMarkType} */
    private Integer markType;

    /** 标题 */
    private String title;

    /** 开始时间 */
    private Long startTime;

    /** 结束时间 */
    private Long endTime;

    /** 创建时间 */
    private Long createTime;

    /** 更新时间 */
    private Long updateTime;

    /** 活动介绍 */
    private String introduce;

    /** 背景 - 颜色代码 */
    private String backgroundColor;

    /** 背景 - 图片key */
    private String backgroundImageKey;

    /** 文字颜色 */
    private String textColor;

    /** 活动封面 */
    private String cover;

    /** 创建用户编号 */
    private Integer creatorId;

    /** 报名的协议名 */
    private String preamble;

    /** 报名的协议内容 */
    private String agreement;

}
