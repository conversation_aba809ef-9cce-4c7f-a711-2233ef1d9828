package com.kaiba.m.core.domain.issue;

import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * author: lyux
 * date: 18-9-11
 */
@Data
@NoArgsConstructor
@Document(collection = "issue_expert_comment")
public class IssueExpertComment {

    @Id
    private String id;

    @Indexed
    private String issueId;

    private Integer userId;

    @Indexed
    private Integer expertId;

    private Integer type;

    private Integer satisfactionRatio;

    private Integer speedRatio;

    private Integer qualityRatio;

    private String content;

    private Long createTime;
}
