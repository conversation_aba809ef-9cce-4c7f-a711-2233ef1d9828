package com.kaiba.m.core.domain.program.adrush;

import com.kaiba.lib.base.constant.rush.RushState;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * 节目 - 广告抽奖展示关联表
 *
 * <AUTHOR>
 * @date 2021/02/04
 */
@Data
@NoArgsConstructor
@Document(collection = "k_program_ad_rush_active_time")
public class ActiveTime {

    @Id
    private String id;

    @Indexed
    private Integer siteId;

    @Indexed
    private String adRushId;

    @Indexed
    private String rushId;

    /**
     * 抽奖状态. 此为冗余字段, 以方便查询. {@link RushState}
     */
    @Indexed
    private Integer rushState;

    /**
     * 广告- 时间段范围或者节目 - 展示类型 {@link com.kaiba.lib.base.constant.program.ActiveTimeType}
     */
    @Indexed
    private Integer type;

    @Indexed
    private String scheduleId;

    private String programName;

    @Indexed
    private Long startTime;

    @Indexed
    private Long endTime;

    private Long createTime;

    private Long updateTime;
}
