package com.kaiba.m.core.domain.publicservice;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * <AUTHOR>
 * @date 2024/01/05 14:18
 */
@Data
@ToString
@NoArgsConstructor
@Document(collection = "k_public_service_instance")
public class PublicServiceInstance {

    @Id
    private String id;

    /** 电台id */
    private Integer siteId;

    /** 服务实例 key */
    @Indexed(unique = true)
    private String instanceKey;

    /** 服务实例下对应的 banner key */
    private String bannerKey;

    /** 实例描述 */
    private String desc;

    /** 创建用户 id */
    private Integer userId;

    /** 创建时间 ms */
    private Long createTime;

}
