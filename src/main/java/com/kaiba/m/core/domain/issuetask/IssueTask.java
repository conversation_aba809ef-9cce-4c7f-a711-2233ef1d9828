package com.kaiba.m.core.domain.issuetask;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * author: lyux
 * date: 18-9-18
 */
@Data
@ToString
@NoArgsConstructor
@Document(collection = "issue_task")
public class IssueTask {

    @Id
    private String id;

    @Indexed
    private String issueId;

    private Integer userId;

    @Indexed
    private Integer expertId;

    /** {@link com.kaiba.lib.base.constant.issue.IssueTaskState} */
    @Indexed
    private Integer state;

    /** {@link com.kaiba.lib.base.constant.issue.IssueTaskType} */
    private Integer type;

    private Integer refuseReasonId;

    private String refuseReasonContent;

    private Long createTime; // in millisecond

    @Indexed
    private Long updateTime; // in millisecond

}
