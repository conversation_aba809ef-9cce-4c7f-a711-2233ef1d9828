package com.kaiba.m.core.domain.apphome;

import com.kaiba.lib.base.constant.applego.AppComponentState;
import com.kaiba.lib.base.domain.apphome.AppHomeFrameTab;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * author: lyux
 * date: 2023-07-05
 *
 * flutter 客户端首页底部导航栏
 */
@Data
@ToString
@NoArgsConstructor
@Document("k_app_tab_home_frame")
public class AppHomeFrame {

    /** 物理主键 */
    @Id
    private String id;

    /** 电台 id. */
    @Indexed
    private Integer siteId;

    /** 状态 {@link AppComponentState} */
    private Integer state;

    /** 电台logo,默认规则 ${site.logo},为空则不显示 */
    private String siteLogo; // ${logo} 或者 https://static.kaiba315.com.cn/xxxxx

    /** 电台名称,默认规则FM+${site.fm},为空则不显示 */
    private String siteName; // 宁波 Nice 或者 FM + ${fm}

    /** 背景图片. */
    private String headBkgImage;

    /** 背景颜色. 格式举例: #fff0f0f0 . 如果同时存在背景图片, 则此项设置无效. */
    private String headBkgColor;

    /** 背景图片. */
    private String footBkgImage;

    /** 背景颜色, 以#开头接6或8位16进制数, 颜色位在最前或可省略. 举例: #fff0f0f0 或 #f6f6f6. 如果同时存在背景图片, 则此项设置无效. */
    private String footBkgColor;

    /** 搜索框默认词条 */
    private String searchHint;

    /** 底部导航栏 tab1. 必填. */
    private AppHomeFrameTab tab1;

    /** 底部导航栏 tab2. 必填. */
    private AppHomeFrameTab tab2;

    /** 底部导航栏 tab3. 必填. */
    private AppHomeFrameTab tab3;

    /** 底部导航栏 tab4. 必填. */
    private AppHomeFrameTab tab4;

    /** 底部导航栏 tab5. 非必填. */
    private AppHomeFrameTab tab5;

    /** 底部导航栏 tab6. 非必填. */
    private AppHomeFrameTab tab6;

    /** 创建时间, 单位毫秒 */
    private Long createTime;

    /** 修改时间, 单位毫秒 */
    private Long updateTime;

    /** 上线时间, 单位毫秒 */
    private Long onlineTime;
}
