package com.kaiba.m.core.domain.message;

/**
 * author: lyux
 * date: 18-9-28
 */
public interface IMessage {

    String getId();

    void setId(String id);

    Integer getUserId();

    void setUserId(Integer userId);

    Integer getType();

    void setType(Integer type);

    Integer getModule();

    void setModule(Integer module);

    String getReferenceId();

    void setReferenceId(String referenceId);

    String getTitle();

    void setTitle(String title);

    String getSubTitle();

    void setSubTitle(String subTitle);

    String getExtra();

    void setExtra(String extra);

    Long getCreateTime();

    void setCreateTime(Long createTime);

}
