package com.kaiba.m.core.domain.da.eventtrack;

import com.kaiba.lib.base.annotation.data.KbRefreshMongoIndex;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.CompoundIndex;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * author: lyux
 * date: 2025-03-07
 *
 * 标准稿件/维表.
 */
@Data
@ToString
@NoArgsConstructor
@KbRefreshMongoIndex("2025-04-20")
@Document(collection = "k_event_track_doc_depart")
@CompoundIndex(name = "depart_channel_index", def = "{'depart': 1, 'channel': 1}", unique = true)
public class ETDocDepart {

    @Id
    private String id;

    /** 部门. 部门是单位的下属组织. */
    @Indexed
    private String depart;

    /** 部门名称. */
    private String name;

    /** 所属单位. */
    @Indexed
    private String channel;

    /** 创建时间 */
    private Long createTime;

    /** 修改时间 */
    private Long updateTime;

}
