package com.kaiba.m.core.domain.note;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.List;

/**
 * author: yeqq
 * date: 2020-07-30
 *
 * 用以统计板块内的用户发帖排行
 */
@Data
@ToString
@NoArgsConstructor
public class NoteUserRanking {

    public List<UserRanking> aggrs;

    /** 聚合所得总条目数, 即指定条件下的用户数 */
    public int total;

    @Data
    @ToString
    @NoArgsConstructor
    public static class UserRanking {

        /** 用户发帖数 */
        private Integer count;

        /** 用户id */
        private Integer userId;
    }

}
