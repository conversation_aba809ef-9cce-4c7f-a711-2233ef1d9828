package com.kaiba.m.core.domain.note;

import com.kaiba.lib.base.domain.common.Audio;
import com.kaiba.lib.base.domain.common.Gallery;
import com.kaiba.lib.base.domain.common.Image;
import com.kaiba.lib.base.domain.common.Video;
import com.kaiba.lib.base.domain.note.NoteLink;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.CompoundIndex;
import org.springframework.data.mongodb.core.index.CompoundIndexes;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.List;
import java.util.Map;

/**
 * author: lyux
 * date: 19-5-21
 */
@Data
@ToString
@NoArgsConstructor
@Document(collection = "k_note")
@CompoundIndexes({
        @CompoundIndex(def = "{ threads: 1, refreshTime: -1}", name = "threads_refreshTime_desc"),
        @CompoundIndex(def = "{ threads: 1, createTime: -1}", name = "threads_createTime_desc")
})
public class Note {

    /** 主键 */
    @Id
    private String id;

    /** 电台 id */
    @Indexed
    private Integer siteId;

    /** 地市 id */
    private Integer source;

    /** 发帖者 id */
    @Indexed
    private Integer userId;

    /** 内容: 音频 */
    private Audio audio;

    /** 内容: 视频 */
    private Video video;

    /** 内容: 媒资视频 */
    private String mediaId;

    /** 内容: 图片列表 */
    private List<Image> images;

    /** 内容: 文字 */
    private String content;

    /** 图片组 */
    private Gallery gallery;

    /** 内容: 备注. 备注内容一般由运营人员添加, 且 C 端用户不可见. */
    private String remark;

    /** 发帖时用户坐标-经度 */
    private Double longitude;

    /** 发帖时用户坐标-纬度 */
    private Double latitude;

    /** 逆地理信息得到的街道名称 */
    private String street;

    /** 跳转链接列表 */
    private List<NoteLink> links;

    /** 置顶的评论 id */
    private String stickyCommentId;

    /** 帖子发布时所选择的板块 id */
    private String originThreadId;

    /** 所属板块列表 */
    @Indexed
    private List<String> threads;

    /** 被设为置顶的模块 id 列表. 该字段为冗余字段. */
    private List<String> topThreads;

    /** 被设为热议的模块 id 列表. 该字段为冗余字段. */
    private List<String> hotThreads;

    /** 关联字段, 比如 维权 Id . */
    @Indexed
    private String referenceId;

    /** {@link com.kaiba.lib.base.constant.note.NoteState} */
    private Integer state;

    /** 是否允许评论. 默认为空, 表示允许. */
    private Boolean isAllowComment;

    /** 是否允许点赞. 默认为空, 表示允许. */
    private Boolean isAllowPraise;

    /** 是否匿名发布, 即不显示发布者信息 */
    private Boolean isAnonymous;

    /** 是否已经被标记为删除. 一般由用户删除会开启此标志. */
    @Deprecated
    private Boolean isSoftDeleted;

    /** 发帖时间, 单位秒. */
    private Long createTime;

    /** 更新时间, 单位秒. 更新是指帖子数据有变更. */
    private Long updateTime;

    /** 刷新时间, 单位秒. 用以实现 "擦亮" 功能 */
    private Long refreshTime;

    /** 发帖时间, 单位毫秒. */
    private Long createTimeMS;

    /** 更新时间, 单位毫秒. 更新是指帖子数据有变更. */
    private Long updateTimeMS;

    /** 刷新时间, 单位毫秒. 用以实现 "擦亮" 功能 */
    private Long refreshTimeMS;

    /** 评论数 */
    private Long commentCount;

    /** 点赞数 */
    private Long praiseCount;

    /** 浏览数 */
    private Long viewCount;

    /** 分享数 */
    private Long shareCount;

    /** 以键值定义的额外属性 */
    private Map<String, String> extra;


    public String contentAsString() {
        if (content != null) {
            return content;
        } else if (video != null || mediaId != null) {
            return "[视频]";
        } else if (audio != null) {
            return "[音频]";
        } else if (images != null && images.size() != 0) {
            return "[图片]";
        } else {
            return "[帖子]";
        }
    }

}
