package com.kaiba.m.core.domain.note;

import com.kaiba.lib.base.util.GsonUtils;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * author: lyux
 * date: 19-5-21
 *
 * 帖子删除表. 参考帖子表 {@link Note}
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Document(collection = "k_note_delete")
public class NoteDelete extends Note {

    /** 原 note 表 id */
    private String noteId;

    /** 删除者用户 id */
    private Integer operatorId;

    /** 删除时间, 单位秒 */
    private Long deleteTime;

    /** 删除时间, 单位毫秒 */
    private Long deleteTimeMS;

    /** 删除批注 */
    private String deleteRemark;

    public static NoteDelete fromNote(Note note) {
        String json = GsonUtils.getGson().toJson(note);
        NoteDelete noteDelete = GsonUtils.getGson().fromJson(json, NoteDelete.class);
        noteDelete.setId(null);
        noteDelete.setNoteId(note.getId());
        noteDelete.setDeleteTime(System.currentTimeMillis() / 1000);
        noteDelete.setDeleteTimeMS(System.currentTimeMillis());
        return noteDelete;
    }

    public Note toNote() {
        String json = GsonUtils.getGson().toJson(this);
        Note note = GsonUtils.getGson().fromJson(json, Note.class);
        note.setId(noteId);
        return note;
    }

}
