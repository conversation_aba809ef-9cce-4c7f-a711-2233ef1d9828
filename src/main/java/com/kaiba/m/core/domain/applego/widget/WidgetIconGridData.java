package com.kaiba.m.core.domain.applego.widget;

import com.kaiba.lib.base.constant.applego.WidgetType;
import com.kaiba.lib.base.constant.common.DummyFantasy;
import com.kaiba.lib.base.domain.applego.IWidget;
import com.kaiba.lib.base.domain.appwidget.icongrid.IconGridInstanceModel;
import com.kaiba.m.core.domain.applego.LegoBlock;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * author: lyux
 * date: 2023-05-24
 *
 * 组件化控件数据 - 网格图标.
 * {@link com.kaiba.lib.base.constant.applego.WidgetType#ICON_GRID}
 */
@Data
@ToString
@NoArgsConstructor
@Document("k_app_lego_widget_icon_grid")
public class WidgetIconGridData implements IWidget {

    /** 物理主键 */
    @Id
    private String id;

    /** {@link LegoBlock#getId()} */
    @Indexed
    private String blockId;

    /** 电台 ID */
    private Integer siteId;

    /**
     * 样式枚举使用 {@link DummyFantasy}:
     *
     * {@link DummyFantasy#HYDRA} - 每行 4 个, 背景白底圆角;
     * {@link DummyFantasy#CERBERUS} - 每行 5 个, 无背景;
     * {@link DummyFantasy#BASILISK} - 每行 4 个, 无背景;
     * {@link DummyFantasy#BEHEMOTH} - 每行 3 个, 无背景;
     * {@link DummyFantasy#MEDUSA} - 每行 4 个, 图标尺寸较大, 无背景;
     * {@link DummyFantasy#CYCLOPS} - 每行 2 个, 左图标右标题, 背景白底圆角;
     *
     * {@link DummyFantasy#PIXIE} - 每行 5 个, 无背景, 支持分页滑动, 每页行数 1 - 2 - 2;
     * {@link DummyFantasy#GENIE} - 每行 5 个, 无背景, 支持分页滑动, 每页行数 1 - 3 - 3;
     * {@link DummyFantasy#TITAN} - 每行 5 个, 无背景, 支持分页滑动, 第一页一行, 其余放在第二页;
     */
    private String style;

    /** 词条数据. {@link IconGridInstanceModel#getKey()} */
    private String instanceKey;

    /** 更新时间, 单位毫秒 */
    private Long updateTime;

    /** 创建时间, 单位毫秒 */
    private Long createTime;

    @Override
    public WidgetType getType() {
        return WidgetType.ICON_GRID;
    }
}
