package com.kaiba.m.core.domain.site;

import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

/**
 * author: lyux
 * date: 18-10-23
 */
@Data
@NoArgsConstructor
@Document(collection = "zone")
public class Zone {

    @Id
    private Integer id;

    private Integer code;

    private String name;

    private String telCode;

    private String letter;

    private Integer level;

    private Integer parentCode;

    private String pinyin;

    private Integer listorder;

    @Field("is_open")
    private Integer open;

}
