package com.kaiba.m.core.domain.workorder;

import com.kaiba.lib.base.constant.workorder.WOBusiness;
import com.kaiba.lib.base.constant.workorder.WOIdentity;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.List;

/**
 * 工单关闭原因配置
 *
 * <AUTHOR>
 * @version WOCloseReason, v0.1 2024/7/26 15:54 daopei Exp $
 **/
@Data
@ToString
@NoArgsConstructor
@Document(collection = "k_work_order_close_type")
public class WOCloseType {

    @Id
    private String id;
    /**
     * 结案原因唯一标识
     */
    @Indexed(unique = true)
    private String code;
    /**
     * 结案描述
     */
    private String desc;
    /**
     * 业务类型. 公共可用的结案原因该值等于COMMON, 其他为各自业务{@link WOBusiness}
     */
    private String biz;
    /**
     * 结案原因允许使用的角色身份 {@link WOIdentity}
     */
    private List<String> identity;
    /**
     * 结案配置状态. 满足可选、不可选但可见、不可选也不可见
     */
    private String state;
    /**
     * 创建时间
     */
    private Long createTime;
    /**
     * 修改时间
     */
    private Long updateTime;
}
