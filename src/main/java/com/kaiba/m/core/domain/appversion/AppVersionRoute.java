package com.kaiba.m.core.domain.appversion;

import com.kaiba.lib.base.constant.appversion.AppUpdatePolicy;
import com.kaiba.lib.base.constant.appversion.AppVersionRouteState;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.List;

/**
 * author: lyux
 * date: 2023-04-13
 *
 * 应用版本升级路径.
 */
@Data
@ToString
@NoArgsConstructor
@Document("k_app_version_route")
public class AppVersionRoute {

    /** 逻辑主键 */
    @Id
    private String id;

    /**
     * 应用标识. 必填. 用于唯一的区分一个具体 APP 应用.
     * 若是 Android 端则是 package name, 如 com.hz.czfw.app
     * 若是 iOS 端则是 bundle id, 如 cn.czfw.HangZhou
     */
    @Indexed
    private String packageId;

    /** 匹配版本号. 必填. */
    @Indexed
    private Integer originCode;

    /** 升级目标版本号, 可为空. 为空时表示升至最新版. */
    private Integer targetCode;

    /** 状态. {@link AppVersionRouteState} */
    private Integer state;

    /** 提示用户升级的策略. {@link AppUpdatePolicy} */
    private Integer policy;

    /** 匹配规则: 电台, 分值: 1. */
    private Integer siteId;

    /** 匹配规则: 设备品牌型号, 分值: 5. 如 Meizu Pro6 / iPhone 10,1 . */
    private String deviceBrand;

    /** 匹配规则: 设备操作系统版本, 分值: 1. 如 (Android) 4.4.1 / (iOS) 12.1. 只在 {@link #deviceBrand} 存在时有效. */
    private String deviceVersion;

    /** 匹配规则: 用户 id, 分值: 10. */
    private List<Integer> userIds;

    /** 面向内部的描述. */
    private String description;

    /** 更新时间, ms */
    private Long updateTime;

    /** 创建时间, ms */
    private Long createTime;

}
