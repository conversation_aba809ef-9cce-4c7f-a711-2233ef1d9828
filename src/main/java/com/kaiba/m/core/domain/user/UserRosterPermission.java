package com.kaiba.m.core.domain.user;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.CompoundIndex;
import org.springframework.data.mongodb.core.index.CompoundIndexes;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * <AUTHOR>
 * @version UserRosterPermission, v0.1 2024/2/22 16:25 daopei Exp $
 **/
@Data
@ToString
@NoArgsConstructor
@Document(collection = "k_user_roster_permission")
@CompoundIndexes({
        @CompoundIndex(unique = true, def = "{ rosterId: 1, menuId: 1}", name = "uniq_user_menu"),
})
public class UserRosterPermission {

    @Id
    private String id;

    /**
     * 花名册ID
     **/
    private String rosterId;

    /**
     * 权限菜单ID
     **/
    private String menuId;

    /**
     *
     **/
    private Long createTime;

    /**
     *
     **/
    private Long updateTime;
}
