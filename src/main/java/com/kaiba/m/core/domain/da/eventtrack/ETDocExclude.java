package com.kaiba.m.core.domain.da.eventtrack;

import com.kaiba.lib.base.constant.KbModule;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.StringJoiner;


/**
 * 稿件剔除名单, 期望不做展示仅作数值统计
 * <AUTHOR>
 * @version ETDocExclude, v0.1 2025/4/8 10:08 daopei Exp $
 **/
@Data
@ToString
@NoArgsConstructor
@Document(collection = "k_event_track_doc_exclude")
public class ETDocExclude {

    @Id
    private String id;
    /** 业务类型. {@link KbModule}. 非空. */
    private String biz;
    /** 业务单元. 用以保持稿件定义的同构性: 同一个业务单元下的 ref 含义和层级结构总是一致的. 可为空. */
    private String unit;
    /** 对象标识1. 非空. */
    private String ref1;
    /** 对象标识2. 可为空. */
    private String ref2;
    /** 对象标识3. 可为空. */
    private String ref3;
    /** 站点 ID. 可为空. */
    private Integer siteId;
    /** 稿件标识, 拼接非空字段得到: biz-unit-ref1-ref2-ref3-siteId */
    private String docId;
    /** 稿件标题. 非必填. */
    private String title;
    /** 黑名单理由 */
    private String reason;
    /** 创建时间 */
    private Long createTime;
    /** 修改时间 */
    private Long updateTime;

    public String buildDocId() {
        StringJoiner sj = new StringJoiner("-");
        sj.add(this.getBiz());
        if (this.getUnit() != null) {
            sj.add(this.getUnit());
        }
        if (this.getRef1() != null) {
            sj.add(this.getRef1());
        }
        if (this.getRef2() != null) {
            sj.add(this.getRef2());
        }
        if (this.getRef3() != null) {
            sj.add(this.getRef3());
        }
        if (this.getSiteId() != null) {
            sj.add(String.valueOf(this.getSiteId()));
        }
        return sj.toString();
    }
}
