package com.kaiba.m.core.domain.news.pool.bygroup;

import com.kaiba.lib.base.constant.news.NewsChannel;
import com.kaiba.lib.base.constant.news.article.*;
import com.kaiba.lib.base.domain.news.pool.bygroup.DisplayConfig;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.Set;

/**
 * author: lyux
 * date: 2023-07-27
 *
 * [文章 id 聚合策略] 简单分组: 使用一个分类层级对文章进行分组.
 * 本类代表分组定义.
 */
@Data
@ToString
@NoArgsConstructor
@Document(collection = "k_news_ids_group")
public class IdsGroup {

    @Id
    private String id;

    /** 唯一标识. 创建后不可变更. 多电台情况下也应保持唯一性. */
    @Indexed(unique = true)
    private String key;

    /** 电台标识, 指示该标签类型属于哪个电台管理. */
    @Indexed
    private Integer siteId;

    /** 频道标识. {@link NewsChannel} */
    @Indexed
    private String channelKey;

    /** 名称, 仅供后台展示用. */
    private String name;

    /** 简述, 仅供后台展示用. */
    private String desc;

    /** 该分组内文章列表的样式. 默认为 {@link ArticleListStyle#PREF_RIGHT} */
    private String style;

    /** 该分组内文章的最大数目. 为空或负数则表示无限制. 默认无限制 */
    private Integer max;

    /** 排序方式, {@link GroupSortMode}*/
    private String sortMode;

    /** 排序字段生成时, 此为所参考的排序时间类型 {@link ArticleTimeType} */
    private String sortTimeBy;

    /** 客户端展示时间时, 要使用哪个时间类型 {@link ArticleTimeType} */
    @Deprecated
    private String displayTimeBy;

    /** 客户端展示时间格式 */
    @Deprecated
    private String displayTimeFormat;

    /** 推荐的渲染器, 指示管理后台编辑器样式. {@link NewsRenderer} */
    private String renderer;

    /** 该组下文章列表缓存策略. 默认为 {@link GroupCacheMode#MEDIUM}. */
    private String cacheMode;

    /** 该组下文章列表缓存的条目数. 默认为 45. */
    private Integer cacheCount;

    /**
     * 文章配置: 文章创建时, 该配置会作为默认配置写入文章配置.
     * 当希望未来创建的文章具有该配置, 且不希望影响过往文章配置时, 可使用此选项.
     */
    private DisplayConfig dcOnCreate;

    /**
     * 文章配置: 文章查询时, 如果文章并未设置该配置, 则将此配置填充给文章后再返回给前端.
     * 当希望统一设置文章配置, 但不希望影响已经单独进行的文章时, 可使用此选项.
     */
    private DisplayConfig dcOnAbsent;

    /**
     * 文章配置: 文章查询时, 统一使用该配置替换文章原有配置.
     * 当希望统一对组内文章进行配置时, 可使用此选项.
     */
    private DisplayConfig dcOnFetch;

    /** 组路由: 添加到该组的文章会同时发到哪些其他组. {@link #id} */
    private Set<String> routeGroupIds;

    /** 更新时间, 单位毫秒. */
    private Long updateTime;

    /** 创建时间, 单位毫秒 */
    private Long createTime;

}
