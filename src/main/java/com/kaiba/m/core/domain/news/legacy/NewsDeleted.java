package com.kaiba.m.core.domain.news.legacy;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.springframework.data.mongodb.core.mapping.Document;

@Data
@ToString
@NoArgsConstructor
@Document(collection = "k_news_deleted")
public class NewsDeleted extends News {

    private Long deleteTime;

    private Integer deleteUserId;

    public NewsDeleted(News news, Integer deleteUserId) {
        this.setId(news.getId());
        this.setSiteId(news.getSiteId());
        this.setTitle(news.getTitle());
        this.setDescription(news.getDescription());
        this.setType(news.getType());
        this.setUrlDecorated(news.getUrlDecorated());
        this.setCoverImg(news.getCoverImg());
        this.setCreateUser(news.getCreateUser());
        this.setCreateTime(news.getCreateTime());
        this.setLastUpdateTime(news.getLastUpdateTime());
        this.setIsSign(news.getIsSign());
        this.setSignUser(news.getSignUser());
        this.setSignTime(news.getSignTime());
        this.setPushCount(news.getPushCount());
        this.setLastPushTime(news.getLastPushTime());
        this.setPushUser(news.getPushUser());
        this.setIsReference(news.getIsReference());
        this.setUrl(news.getUrl());
        this.setContent(news.getContent());
        this.setIsTop(news.getIsTop());
        this.setRewardComment(news.getRewardComment());
        this.setOpenComment(news.getOpenComment());
        this.setOpenPraise(news.getOpenPraise());
        this.setCommentCount(news.getCommentCount());
        this.setViewCount(news.getViewCount());
        this.setPraiseCount(news.getPraiseCount());
        this.setRewardCount(news.getRewardCount());
        this.setThreadId(news.getThreadId());

        this.deleteUserId = deleteUserId;
        this.deleteTime = System.currentTimeMillis() / 1000;
    }
}
