package com.kaiba.m.core.domain.issue;

import com.kaiba.lib.base.domain.common.Image;
import com.kaiba.lib.base.domain.common.Video;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.List;

/**
 * {@link com.kaiba.lib.base.domain.issue.IssueModel}
 */
@Data
@NoArgsConstructor
@ToString
@Document(collection = "issue")
public class Issue {

    @Id
    private String id;

    @Indexed
    private Integer userId;

    @Indexed
    private Integer expertId;

    private Integer payLevel;

    private String fee;

    private Long duration;

    private String transactionId;

    private Integer source;

    @Indexed
    private Integer state;

    @Indexed
    private Integer taskType;

    private Integer cancelReason;

    private String cancelMessage;

    @Indexed
    private String carCode;

    @Indexed
    private String carBrand;

    private Integer rongHostId;

    private String rongChatId;

    private String rongChatName;

    private String content;

    private List<Image> images;

    private Video video;

    private Long messageCount;

    @Indexed
    private Long createTime;

    private Long startTime;

    private Long endTime;

    @Indexed
    private Long updateTime;

    private Double longitude;

    private Double latitude;

    @Indexed
    private Integer top;

    private Boolean fake;

}
