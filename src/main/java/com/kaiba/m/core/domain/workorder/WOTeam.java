package com.kaiba.m.core.domain.workorder;

import com.kaiba.lib.base.constant.workorder.WOBusiness;
import com.kaiba.lib.base.constant.workorder.WOIdentity;
import com.kaiba.lib.base.constant.workorder.WOTeamState;
import com.kaiba.lib.base.domain.workorder.WOConfigRole;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.List;
import java.util.Map;

/**
 * author: lyux
 * date: 2023-11-07
 *
 * 工单系统, 用户组: 参与其中的主体.
 */
@Data
@ToString
@NoArgsConstructor
@Document(collection = "k_work_order_team")
public class WOTeam {

    @Id
    private String id;

    /** 业务标识. {@link WOBusiness} */
    private String biz;

    /** 用户组标识 */
    @Indexed
    private String team;

    /** 全称, 仅做显示 */
    private String name;

    /** 简称, 仅做显示 */
    private String abbr;

    /** 描述, 仅做显示 */
    private String desc;

    /** 头像 */
    private String avatar;

    /** {@link WOIdentity} */
    private String identity;

    /** {@link WOConfigRole#getRole()} */
    private List<String> roles;

    /** {@link WOTeamState} */
    private String state;

    /** 以 key-value 存储的属性值 */
    private Map<String, String> attr;

    /** 更新时间, 单位毫秒. */
    private Long updateTime;

    /** 创建时间, 单位毫秒. */
    private Long createTime;

}
