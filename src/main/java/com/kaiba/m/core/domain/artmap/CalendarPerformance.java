package com.kaiba.m.core.domain.artmap;

import java.time.Instant;
import java.time.LocalDate;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.CompoundIndex;
import org.springframework.data.mongodb.core.index.CompoundIndexes;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * Description: 艺术地图日历维度演出记录
 * Author: ZM227
 * Date: 2025/6/16 16:27
 */
@Data
@NoArgsConstructor
@ToString
@Document(collection = "k_art_map_calendar_performance")
@CompoundIndexes({
    @CompoundIndex(name = "idx_performance_code_date", def = "{'performanceCode': 1, 'performanceDate': -1}")
})
public class CalendarPerformance {

    /**
     * 主键
     */
    @Id
    private String id;

    /**
     * 演艺演出编码
     */
    @Indexed
    private String calendarCode;

    /**
     * 演艺演出编码
     */
    @Indexed
    private String performanceCode;

    /**
     * 演出日期
     */
    @Indexed
    private Instant performanceDate;

    /**
     * 演出类型
     * 1：演艺演出 2：文章帖子
     */
    private Integer performanceType;

    /**
     * 业务方标识code
     */
    private String bizCode;

    /**
     * 演出开始时间
     */
    private Long startTime;

    /**
     * 剧场Code
     */
    private String venuesCode;

    /**
     * 剧场名称
     */
    private String theaterName;

    /**
     * 库存
     */
    private Integer inventory;

    /**
     * 状态
     * (0, "无效")
     * (1, "有效")
     */
    private Integer status;

    /**
     * 演出排序字段
     */
    @Indexed
    private Integer seq = 999999;

    /**
     * 创建时间
     */
    private Long createTime;

    /**
     * 更新时间
     */
    private Long updateTime;

}
