package com.kaiba.m.core.domain.news.channel;

import com.kaiba.lib.base.constant.news.NewsChannel;
import com.kaiba.lib.base.constant.news.article.NDisplayState;
import com.kaiba.lib.base.domain.common.Image;
import com.kaiba.lib.base.domain.common.ShareModel;
import com.kaiba.lib.base.domain.news.pool.bygroup.DisplayConfig;
import com.kaiba.m.core.domain.news.pool.bygroup.IdsGroup;
import com.kaiba.m.core.domain.news.pool.topic.TopicLayout;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * author: lyux
 * date: 2024-08-28
 *
 * 频率频道的一档栏目. 比如 "阿六头说新闻", "小区大事".
 */
@Data
@ToString
@NoArgsConstructor
@Document(collection = "k_news_channel_programme")
public class CProgramme {

    @Id
    private String id;

    /** 逻辑主键 */
    @Indexed(unique = true)
    private String key;

    /** 频道标识. {@link NewsChannel} */
    @Indexed
    private String channelKey;

    /** 电台 id */
    private Integer siteId;

    /** 状态. {@link NDisplayState} */
    private String state;

    /** 栏目关联的单期分组. {@link IdsGroup#getKey()} */
    private String group;

    /** 栏目名称 */
    private String name;

    /** 栏目简称 */
    private String abbr;

    /** 栏目封面 */
    private Image cover;

    /** 顶部布局. {@link TopicLayout#getId()} */
    private String layoutId;

    /** 分享配置 */
    private ShareModel share;

    /** 查询时以此字段降序排列. */
    @Indexed
    private Long seq;

    /**
     * 人工指定的排序依据, 降序, 最大值 999.
     * 最终会根据算法生成 {@link #seq}. 因此本字段仅做记录, 实际排序应使用 {@link #seq} 字段
     */
    @Indexed(sparse = true)
    private Long idx;

    /** 默认的每日播出开始时间, 单位毫秒, 取值范围 [0, 86_400_000]. 用来在添加单期视频时预填节目在当日的播出时间. */
    private Long startTimeInDay;

    /** 默认的每日播出结束时间, 单位毫秒, 取值范围 [0, 86_400_000]. 用来在添加单期视频时预填节目在当日的播出时间. */
    private Long endTimeInDay;

    /** 默认的单期和拆条分组的递补配置. {@link IdsGroup#getDcOnAbsent()}. */
    private DisplayConfig groupAbsentDc;

    /**
     * 葫芦网cid
     * 目前用户葫芦网资讯同步
     */
    private Integer outerId;

    /** 更新时间, 单位毫秒. */
    private Long updateTime;

    /** 创建时间, 单位毫秒. */
    private Long createTime;

}
