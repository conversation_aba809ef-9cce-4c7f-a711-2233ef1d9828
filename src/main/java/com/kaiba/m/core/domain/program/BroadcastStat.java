package com.kaiba.m.core.domain.program;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

@Data
@ToString
@NoArgsConstructor
@Document(collection = "broadcast_statistic")
public class BroadcastStat {
    @Id
    private String id;

    /** 操作帖子进入待播板块的用户ID */
    private Integer userId;

    /** 操作帖子进入待播板块时，操作用户所在的电台ID */
    private Integer siteId;

    /** 帖子ID */
    private String noteId;

    /** 用户发帖子的板块ID */
    private String threadId;

    /** 待播的板块ID */
    private String boardThreadId;

    /** 创建时间 */
    private Long createTime;
}
