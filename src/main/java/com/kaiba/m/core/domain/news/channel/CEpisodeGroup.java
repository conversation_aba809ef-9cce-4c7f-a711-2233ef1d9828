package com.kaiba.m.core.domain.news.channel;

import com.kaiba.m.core.domain.news.pool.bygroup.IdsGroup;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * author: lyux
 * date: 2024-08-28
 *
 * 栏目中单期的拆条所在分组. 比如 "新闻联播 2024年5月1日 期" 拆为 10 段视频.
 */
@Data
@ToString
@NoArgsConstructor
@Document(collection = "k_news_channel_programme_episode_group")
public class CEpisodeGroup {

    @Id
    private String id;

    /** 栏目标识. {@link CProgramme#getId()} */
    @Indexed
    private String programmeId;

    /** 单期内容 id */
    @Indexed(unique = true)
    private String articleId;

    /** 单期所在的栏目分组. {@link IdsGroup#getKey()} */
    private String episodeGroup;
    
    /** 单期关联的拆条分组. {@link IdsGroup#getKey()} */
    @Indexed
    private String clipGroup;
    
    /** 更新时间, 单位毫秒. */
    private Long updateTime;

    /** 创建时间, 单位毫秒. */
    private Long createTime;

}
