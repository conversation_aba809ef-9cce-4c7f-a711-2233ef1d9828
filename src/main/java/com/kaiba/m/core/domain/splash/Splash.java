package com.kaiba.m.core.domain.splash;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;
import java.util.Map;

/**
 * author: yeqq
 * date: 20-11-25
 */
@Data
@ToString
@NoArgsConstructor
@Document(collection = "k_splash")
public class Splash {

    @Id
    private String id;

    @Indexed
    private Integer siteId;

    @Indexed
    private Integer createUserId;

    /** 广告的状态. 参考 {@link com.kaiba.lib.base.constant.splash.SplashState}. **/
    @Indexed
    private Integer state;

    /** 广告标题. */
    private String title;

    private String action;

    private Map<String, Object> actionParams;

    private String originUrl;

    /** 广告内容. */
    private String content;

    /** 广告图片 */
    private String pic;

    /** 广告开始时间 */
    private Long startTime;

    /** 广告结束时间 */
    private Long endTime;

    /** 广告创建时间 */
    private Long createTime;

    /** 广告更新时间 */
    private Long updateTime;

    /** 广告展示时长 */
    private Integer showTime;

    /** 广告最短展示时长 */
    private Integer showMinTime;

    /** 视频链接地址 */
    private String video;

    /** 是否开始首页banner特效 */
    private Boolean isBannerOn;
}
