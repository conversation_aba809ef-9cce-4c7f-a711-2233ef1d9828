package com.kaiba.m.core.domain.contact;

import com.kaiba.lib.base.constant.contact.ContactField;
import com.kaiba.lib.base.constant.contact.ContactState;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * author: lyux
 * date: 19-11-20
 *
 * 联系方式分组.
 */
@Data
@ToString
@NoArgsConstructor
@Document(collection = "k_contact_group")
public class ContactGroup {

    @Id
    private String id;

    /** 逻辑主键 */
    @Indexed(unique = true)
    private String key;

    /** 字段约束. 由 {@link ContactField} 中的枚举值按位与得到. */
    @Indexed
    private Long constraint;

    /**
     * 该分组下的联系人是否可变. 默认为 true.
     * 如果为不可变, 则一旦该组下的联系人状态到达 {@link ContactState#ENABLED} 状态, 则其数据不再支持更改.
     */
    private Boolean mutable;

    /** 该分组下的联系人是否允许"默认"属性. 该字段默认值为 false. */
    private Boolean allowDefault;

    /** 每个用户在此 group 下最多可以创建的实例上限. 默认为 null, 表示无上限. */
    private Integer limitPerUser;

    /** 描述 */
    private String description;

    /** 更新时间, 单位毫秒 */
    private Long updateTime;

    /** 创建时间, 单位毫秒 */
    @Indexed
    private Long createTime;

    public boolean isAllowDefault() {
        return allowDefault != null && allowDefault;
    }

}
