package com.kaiba.m.core.domain.applego;

import com.kaiba.lib.base.constant.applego.AppComponentState;
import com.kaiba.lib.base.constant.applego.PageScene;
import com.kaiba.lib.base.constant.applego.PageStructure;
import com.kaiba.lib.base.constant.common.DummyFantasy;
import com.kaiba.lib.base.domain.applego.lego.LegoLayoutTab;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.CompoundIndex;
import org.springframework.data.mongodb.core.index.CompoundIndexes;
import org.springframework.data.mongodb.core.index.IndexDirection;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.List;

/**
 * author: lyux
 * date: 2023-05-24
 *
 * 布局页面. 一旦创建, 只有 state / refreshTime / description 字段允许修改.
 */
@Data
@ToString
@NoArgsConstructor
@Document("k_app_lego_page")
@CompoundIndexes({
        @CompoundIndex(def = "{ siteId: 1, scene: 1}", name = "site_scene_asc"),
})
public class LegoPage {

    /** 物理主键 */
    @Id
    private String id;

    /** 电台 ID */
    @Indexed
    private Integer siteId;

    /** 状态 {@link AppComponentState} */
    private Integer state;

    /** 场景: 主页首屏, 主页服务页 等. {@link PageScene} */
    private String scene;

    /** 页面结构. {@link PageStructure} */
    private String structure;

    /** 是否支持滚动. */
    private Boolean scrollable;

    /** 如果存在 TAB 栏, 此为 TAB 栏样式枚举. {@link DummyFantasy}. */
    private String tabStyle;

    /** 背景图片. */
    private String bkgImage;

    /** 背景颜色, 以#开头接6或8位16进制数, 颜色位在最前或可省略. 举例: #fff0f0f0 或 #f6f6f6. 如果同时存在背景图片, 则此项设置无效. */
    private String bkgColor;

    /** 如果包含顺序布局部分, 此为布局数据. {@link LegoLayout#getId()} */
    private String layoutId;

    /** 处于选中状态的 tab layout id. 默认为空, 表示选中第一个. */
    @Deprecated
    private String tabSelectedId;

    /** 如果包含 TAB 布局部分, 此为布局数据. {@link LegoLayout#getId()}. deprecated, use tabs instead. */
    @Deprecated
    private List<String> tabLayoutIds;

    /** 如果包含 TAB 布局部分, 此为布局数据. {@link LegoLayout#getId()} */
    private List<LegoLayoutTab> tabs;

    /** 描述. 仅管理后台可见. */
    private String description;

    /** 刷新时间, 单位毫秒. 刷新时间最大的条目会被选为当前活跃条目. */
    @Indexed(direction = IndexDirection.DESCENDING)
    private Long refreshTime;

    /** 创建时间, 单位毫秒 */
    private Long createTime;

}
