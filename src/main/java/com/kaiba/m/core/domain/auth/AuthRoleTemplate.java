package com.kaiba.m.core.domain.auth;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.List;

/**
 * author: lyux
 * date: 19-8-9
 */
@Data
@ToString
@NoArgsConstructor
@Document(collection = "k_auth_role_template")
public class AuthRoleTemplate {

    /** 主键 */
    @Id
    private String id;

    /** 适用范围. 参考 {@link com.kaiba.lib.base.constant.auth.AuthScope}. 一旦生成, 不可修改. */
    @Indexed
    private Integer scope;

    /** 通配格式. 模板唯一标识. 比如 site.admin.${siteId}. 一旦生成, 不可修改. */
    @Indexed(unique = true)
    private String format;

    /** 角色被授予的通配权限列表 */
    @Indexed
    private List<String> permissionFormats;

    /** 角色被授予的非通配权限列表 */
    @Indexed
    private List<String> permissions;

    /** 描述 */
    private String description;

    /** 创建时间 */
    private long createTime;

}
