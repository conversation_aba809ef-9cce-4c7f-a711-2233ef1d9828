package com.kaiba.m.core.domain.knowledge;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * Description: 知识库问答日志
 * Author: ZM227
 * Date: 2024/12/30 10:28
 */
@Data
@NoArgsConstructor
@ToString
@Document(collection = "k_knowledge_question_log")
public class KnowledgeQuestionLog {

    /**
     * 主键
     */
    @Id
    private String id;

    /**
     * 用户输入的问题
     */
    private String question;

    /**
     * 回答类型:1-知识库,2-AI
     */
    private Integer answerType;

    /**
     * 问题答案
     */
    private String answerId;

    /**
     * 关联知识点id
     */
    private String knowledgeId;

    /**
     * 提问时间
     */
    private Long questionTime;

    /**
     * 创建时间, 时间戳毫秒数
     */
    private Long createTime;

    /**
     * 更新时间, 时间戳毫秒数
     */
    private Long updateTime;

}
