package com.kaiba.m.core.domain.advertisement;

import com.kaiba.lib.base.domain.common.Image;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.List;

@Data
@NoArgsConstructor
@Document(collection = "k_news_ad")
public class NewsAd {
    @Id
    private String id;

    @Indexed
    private Integer siteId;

    private String title;

    @Indexed
    private Integer type;

    private Integer browser;

    private Integer isReference;

    private String url;

    private String content;

    private List<Image> images;

    private Long createTime;

    private Integer createUser;

    private Long lastUpdateTime;

    private Integer isSign;

    private Long signTime;

    private Integer signUser;

    private Integer viewCount;

    private Integer display;

    /** 电台名 **/
    private String siteTitle;
}
