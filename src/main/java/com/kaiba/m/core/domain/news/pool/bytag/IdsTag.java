package com.kaiba.m.core.domain.news.pool.bytag;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * author: lyux
 * date: 2023-07-27
 *
 * [文章 id 聚合策略] 多实例标签: 使用 "标签组" 和 "标签" 两级对文章进行分组
 * 资讯标签表. 所有字段在创建后均不可修改.
 */
@Data
@ToString
@NoArgsConstructor
@Document(collection = "k_news_ids_tag")
public class IdsTag {

    @Id
    private String id;

    /** {@link IdsTagGroup#getKey()} */
    @Indexed
    private String group;

    /** 标签名称 */
    @Indexed
    private String name;

    /** 创建时间, 单位毫秒 */
    private Long createTime;

}
