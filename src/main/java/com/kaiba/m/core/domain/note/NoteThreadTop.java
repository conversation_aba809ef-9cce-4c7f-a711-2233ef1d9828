package com.kaiba.m.core.domain.note;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * author: lyux
 * date: 19-5-21
 */
@Data
@ToString
@NoArgsConstructor
@Document(collection = "k_note_thread_top")
public class NoteThreadTop {

    /** 主键 */
    @Id
    private String id;

    /** 帖子 id */
    private String noteId;

    /** 要设为置顶的板块 id */
    @Indexed
    private String threadId;

    /** 刷新时间, 单位秒. 用于排序 */
    private Long refreshTime;

    /** 创建时间, 单位毫秒 */
    private Long refreshTimeMS;

}
