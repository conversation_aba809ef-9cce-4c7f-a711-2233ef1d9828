package com.kaiba.m.core.domain.kri.matcher;

import com.kaiba.lib.base.annotation.data.KbRefreshMongoIndex;
import com.kaiba.lib.base.constant.KbModule;
import com.kaiba.lib.base.constant.kri.KRIMatcherFact;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * author: lyux
 * date: 2024-12-04
 *
 * kaiba resource identifier. 开吧统一资源标识.
 * 匹配器-完全匹配.
 */
@Data
@ToString
@NoArgsConstructor
@KbRefreshMongoIndex("2025-01-10")
@Document(collection = "kri_match_by_value")
public class KRIValueMatcher {

    @Id
    private String id;

    /** 业务类型. {@link KbModule} */
    @Indexed
    private String biz;

    /** 功能单元 */
    @Indexed(sparse = true)
    private String unit;

    /** 对象标识 1, 匹配参数. */
    @Indexed(sparse = true)
    private String ref1;

    /** 对象标识 2, 匹配参数. */
    @Indexed(sparse = true)
    private String ref2;

    /** 对象标识 3, 匹配参数. */
    @Indexed(sparse = true)
    private String ref3;

    /** 业务事实类型. {@link KRIMatcherFact}. */
    @Indexed
    private String fact;

    /** 业务事实对应的数据. */
    private String data;

    /** 创建时间, 单位毫秒 */
    private Long createTime;

    /** 创建时间, 单位毫秒 */
    private Long updateTime;

}
