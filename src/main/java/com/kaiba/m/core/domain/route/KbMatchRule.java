package com.kaiba.m.core.domain.route;

import com.kaiba.lib.base.constant.route.KbRouteRuleScope;
import com.kaiba.lib.base.constant.route.KbRouteRuleState;
import com.kaiba.lib.base.domain.route.MatchRuleModel;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.http.HttpMethod;

import java.util.List;

/**
 * author: lyux
 * date: 2021-03-24
 *
 * 路由匹配规则.
 * 若配置了多种规则, 比如 {@link #ip} 和 {@link #header} 都有值, 则各规则间为 "and" 逻辑, 即必须同时满足.
 */
@Data
@ToString
@NoArgsConstructor
@Document(collection = "k_route_match_rule")
public class KbMatchRule {

    @Id
    private String id;

    /** 规则的适用范围. {@link KbRouteRuleScope}. 管理后台的相关功能会根据这个类型分别进行管理. */
    private Integer scope;

    /** 状态. {@link KbRouteRuleState}. */
    private Integer state;

    /** 名称 */
    private String name;

    /** 描述 */
    private String description;

    /** 匹配条件: 指定 ip 可访问, 为空则表示无限制. */
    private List<String> ip;

    /** 匹配条件: HTTP 方法, 为空则表示无限制. 参考 {@link HttpMethod}. */
    private List<String> method;

    /** 匹配条件: header, 为空则表示无限制. 多个条件时适用 "and" 逻辑, 即必须同时满足. */
    private List<MatchRuleModel.HeaderRule> header;

    /** 匹配条件: cookie, 为空则表示无限制. 多个条件时适用 "and" 逻辑, 即必须同时满足. */
    private List<MatchRuleModel.CookieRule> cookie;

    /** 匹配条件: Android/iOS 版本号, 为空则表示无限制. 多个条件时适用 "or" 逻辑, 即只需满足一个. */
    private List<MatchRuleModel.AppVersionRule> appVersion;

    /** 创建时间, 单位毫秒 */
    private Long createTime;

    /** 创建时间, 单位毫秒 */
    private Long updateTime;

}
