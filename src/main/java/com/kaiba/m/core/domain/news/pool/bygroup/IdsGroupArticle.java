package com.kaiba.m.core.domain.news.pool.bygroup;

import com.kaiba.lib.base.constant.news.article.ArticleTimeType;
import com.kaiba.m.core.domain.news.article.NewsArticle;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * author: lyux
 * date: 2023-07-27
 *
 * [文章 id 聚合策略] 简单分组: 使用一个分类层级对文章进行分组.
 * 本类是分组文章的关系表.
 */
@Data
@ToString
@NoArgsConstructor
@Document(collection = "k_news_ids_group_article")
public class IdsGroupArticle {

    @Id
    private String id;

    /** {@link IdsGroup#getKey()} */
    @Indexed
    private String group;

    /** {@link NewsArticle#getId()} */
    @Indexed
    private String articleId;

    /** 查询时以此字段降序排列. */
    @Indexed
    private Long seq;

    /**
     * 人工指定的排序依据, 降序, 最大值 999.
     * 最终会根据算法生成 {@link #seq}. 因此本字段仅做记录, 实际排序应使用 {@link #seq} 字段
     */
    @Indexed(sparse = true)
    private Long idx;

    /** 排序规则-批次排序: 批次 */
    @Indexed
    private Long batch;

    /** 排序规则-批次排序: 批次内的序号 */
    private Long batchIdx;

    /** 排序规则-批次排序: 查询时以此字段降序排列. */
    @Indexed
    private Long batchSeq;

    /** 用以生成排序规则的时间类型, {@link ArticleTimeType}. */
    private String timeBy;

    /** 用以生成排序规则的时间, 冗余自文章数据, 单位毫秒. */
    private Long time;

    /** 清除标记: 所关联的文章是否已 '不存在(1)' 或 '非上线状态(2)'. */
    private Integer obsolete;

    /** 创建时间, 单位毫秒. */
    private Long createTime;

}
