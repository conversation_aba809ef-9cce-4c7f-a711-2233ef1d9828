package com.kaiba.m.core.domain.appwidget.banner;

import com.kaiba.lib.base.constant.appwidget.WidgetItemState;
import com.kaiba.lib.base.constant.appwidget.WidgetItemType;
import com.kaiba.lib.base.constant.common.KbImageFormat;
import com.kaiba.lib.base.domain.IActionGetterSetter;
import com.kaiba.lib.base.domain.appwidget.banner.BannerInstanceModel;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.Map;

/**
 * author: lyux
 * date: 2023-05-31
 */
@Data
@ToString
@NoArgsConstructor
@Document("k_app_banner_image")
public class BannerImage implements IActionGetterSetter {

    /** 主键 */
    @Id
    private String id;

    /** {@link BannerInstanceModel} */
    @Indexed
    private String instanceId;

    /** {@link WidgetItemState} */
    private Integer state;

    /** {@link WidgetItemType} */
    private Integer type;

    /** 顺序/屏位. 升序排列, 若创建时未填写则默认为 100. */
    private Integer idx;

    /** 图片 url. */
    private String url;

    /** 图片格式. {@link KbImageFormat} */
    private String format;

    /** 标题. 可为空. */
    private String title;

    /** 副标题. 可为空. */
    private String subtitle;

    /** 内容是否可播放, 用于前端展示播放按钮 */
    private Boolean isPlayable;

    /** 内容是否在直播, 用于前端展示正在直播动画 */
    private Boolean isLive;

    /** 备注, 仅后台可见. 可为空. */
    private String remark;

    /** 点击时的跳转页面. */
    private String action;

    /** 点击时的跳转页面, 参数. */
    private Map<String, Object> actionParams;

    /** 额外属性 */
    private Map<String, String> attr;

    /** 自动翻页的间隔时间, 单位秒. */
    private Integer autoplay;

    /** 预设的开始时间, 单位毫秒. */
    @Indexed(sparse = true)
    private Long scheduledStartTime;

    /** 预设的结束时间, 单位毫秒. */
    @Indexed(sparse = true)
    private Long scheduledEndTime;

    /** 签发时间, 单位毫秒. */
    private Long signTime;

    /** 开始时间, 单位毫秒 */
    private Long startTime;

    /** 结束时间, 单位毫秒 */
    private Long endTime;

    /** 更新时间, 单位毫秒. */
    private Long updateTime;

    /** 创建时间, 单位毫秒 */
    private Long createTime;

}
