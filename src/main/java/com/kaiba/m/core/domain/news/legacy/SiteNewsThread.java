package com.kaiba.m.core.domain.news.legacy;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

@Data
@ToString
@NoArgsConstructor
@Document(collection = "k_site_news_thread")
public class SiteNewsThread {

    @Id
    private String id;

    private Integer siteId;

    private String threadId;

    private Integer multiply;

    private Integer base;
}
