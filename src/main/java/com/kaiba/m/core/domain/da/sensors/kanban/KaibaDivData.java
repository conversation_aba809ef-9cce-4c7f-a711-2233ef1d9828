package com.kaiba.m.core.domain.da.sensors.kanban;

import com.kaiba.lib.base.domain.da.sensors.kanban.DivConfigKaibaModel;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * <AUTHOR>
 * @version KaibaDivData, v0.1 2024/3/18 14:28 daopei Exp $
 **/
@Data
@ToString
@NoArgsConstructor
@Document(collection = "k_sensors_kanban_kaiba_div_data")
public class KaibaDivData implements DivData {

    @Id
    private String id;

    /** 根据分片策略生成的数据行名称, 如分片策略为按天, 则此为 2024-02-28 */
    private String title;

    /** 配置 id {@link DivConfigKaibaModel#getId()} */
    private String configId;

    /**
     * 注册量
     **/
    private Long register;

    /**
     * 下载量
     **/
    private Long download;


    /** 切片数据起始时间, 单位毫秒 */
    private Long st;

    /** 切片数据终止时间, 单位毫秒 */
    private Long et;

    /**
     *
     **/
    private Long createTime;
    /**
     *
     **/
    private Long updateTime;


}
