package com.kaiba.m.core.domain.da.sensors.kanban;

import com.kaiba.lib.base.domain.da.sensors.kanban.DivConfigPGCModel;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * author: lyux
 * date: 2024-02-26
 *
 * 神策统一 PGC 埋点分时数据条目
 */
@Data
@ToString
@NoArgsConstructor
@Document(collection = "k_sensors_kanban_pgc_div_data")
public class PGCDivData implements DivData{

    @Id
    private String id;

    /** 配置 id {@link DivConfigPGCModel#getId()} */
    private String configId;

    /** 根据分片策略生成的数据行名称, 如分片策略为按天, 则此为 2024-02-28 */
    private String title;

    /** 多类型统计标准 */
    private PGCDivDataBase sum;
    private PGCDivDataBase count;
    private PGCDivDataBase avg;
    private PGCDivDataBase sumDistinct;

    /** 切片数据起始时间, 单位毫秒 */
    private Long st;

    /** 切片数据终止时间, 单位毫秒 */
    private Long et;

    /** 数据生成时间, 单位毫秒. */
    private Long createTime;

}
