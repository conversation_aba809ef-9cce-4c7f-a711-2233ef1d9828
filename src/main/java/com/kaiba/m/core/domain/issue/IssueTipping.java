package com.kaiba.m.core.domain.issue;

import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * author: lyux
 * date: 18-11-1
 */
@Data
@NoArgsConstructor
@Document(collection = "issue_tipping")
public class IssueTipping {

    @Id
    private String id;

    private String issueId;

    private String transactionId;

    private Integer userId;

    private Integer expertId;

    private String amount;

    private String content;

    private Long createTime;

}
