package com.kaiba.m.core.domain.car;

import com.kaiba.lib.base.domain.common.Image;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

/**
 * author: lyux
 * date: 18-9-7
 */
@Data
@NoArgsConstructor
@Document(collection = "series")
public class Car {

    @Id
    private String id;

    private String code;

    @Field("brand_code")
    private String brandCode;

    private String name;

    @Field("series_type")
    private String typeName;

    /**
     * 8.0版本之前历史数据使用该字段
     */
    @Deprecated
    @Field("pic")
    private String image;

    @Field("sizeId")
    private Integer sizeType;

    @Field("price_low")
    private Float priceLow;

    @Field("price_top")
    private Float priceTop;

    private Integer factoryId;

    @Field("createtime")
    private Long createTime;

    /**
     * 汽车之家ID
     */
    private Integer autohomeId;
    /**
     * 车型图片.
     * 8.0版本之后使用该字段
     */
    private Image logo;

    /** 国别编号. {@link com.kaiba.lib.base.constant.car.CarRegion} */
    private Integer regionId;

    /** 产权归属. 1:国产,2:进口,3:合资,4:独资,999:未知 */
    private Integer rightId;

}
