package com.kaiba.m.core.domain.car;

import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * <AUTHOR>
 * @version CarBrandFactory, v0.1 2024/9/10 09:40 daopei Exp $
 **/
@Data
@NoArgsConstructor
@Document(collection = "brand_factory")
public class CarBrandFactory {

    @Id
    private Integer id;

    private String name;

    private String tel;

}
