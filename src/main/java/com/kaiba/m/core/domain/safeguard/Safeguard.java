package com.kaiba.m.core.domain.safeguard;

import com.kaiba.lib.base.domain.common.Image;
import com.kaiba.lib.base.domain.common.Video;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/8/6
 */
@Data
@NoArgsConstructor
@ToString
@Document(collection = "safeguard")
public class Safeguard {
    @Indexed
    private String id;

    private Integer userId;

    @Field("createtime")
    private Long createTime;

    private String content;

    private String noteId;

    private String carNo;

    private String mobile;

    private String name;

    private String brand;

    private String series;

    private String carriageNo;

    private String engineNo;

    private Integer orgId;

    private Integer dealUser;

    private Integer status;

    private Integer dealflag;

    private List<Image> pic;

    private Video video;
}
