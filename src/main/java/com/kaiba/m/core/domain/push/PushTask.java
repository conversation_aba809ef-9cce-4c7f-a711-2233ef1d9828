package com.kaiba.m.core.domain.push;

import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.Map;

@Data
@NoArgsConstructor
@Document(collection = "k_push_task")
public class PushTask {

    @Id
    private String id;

    private String title;

    private String subTitle;

    private String action;

    private Map<String, Object> actionParams;

    private String targetId;

    /** {@link com.kaiba.lib.base.constant.push.PushType} */
    private Integer type;

    /**
     * {@link com.kaiba.lib.base.constant.push.PushRange}
     * */
    private Integer range;

    /** 被推对象标识 */
    @Indexed
    private String referenceId;

    /** 极光推送消息标识. 推送成功后得到. 用以调用极光 SDK 查询推送结果 */
    private Long jPushMsgId;

    /** 个推推送消息标识. 推送成功后得到. */
    private String gtPushTaskId;

    /** 个推推送失败时结果回执 */
    private String gtPushResult;

    /** 个推推送消息标识(新渠道). 推送成功后得到. */
    private String gtPushNTaskId;

    /** 个推结果回执(新渠道) */
    private String gtPushNResult;

    /** {@link com.kaiba.lib.base.constant.KbEndpoint} */
    private Integer endpoint;

    /** {@link com.kaiba.lib.base.constant.push.PushStatus} */
    private String status;

    private Integer createUserId;

    private Long createTime;

    private Long deadLine;

    private Long start;

    private Long end;
}
