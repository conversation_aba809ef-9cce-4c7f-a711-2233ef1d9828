package com.kaiba.m.core.domain.workorder;

import com.kaiba.lib.base.constant.workorder.WOIdentity;
import com.kaiba.lib.base.domain.workorder.WOACLStringData;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.List;

/**
 * author: lyux
 * date: 2023-11-07
 *
 * 工单系统, 案件关注者. {@link WOIdentity#FOLLOWER}
 */
@Data
@ToString
@NoArgsConstructor
@Document(collection = "k_work_order_case_follower")
public class WOFollower {

    @Id
    private String id;

    /** 案件 id. {@link WOCase#getId()} */
    @Indexed
    private String caseId;

    /** 案件Biz. {@link  WOCase#getBiz()} */
    private String biz;

    /** 用户 id. */
    @Indexed
    private Integer userId;

    /** 关注者信息 */
    private List<WOACLStringData> info;

    /** 关注者留言, 非必填 */
    private String message;

    /** 创建时间, 单位毫秒. */
    private Long createTime;

}
