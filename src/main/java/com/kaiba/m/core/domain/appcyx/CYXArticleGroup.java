package com.kaiba.m.core.domain.appcyx;

import com.kaiba.lib.base.annotation.data.KbRefreshMongoIndex;
import com.kaiba.lib.base.domain.common.ActionLink;
import com.kaiba.m.core.constant.app.cyx.CYXArticleGroupType;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * 超有戏资讯分组
 * <AUTHOR>
 * @version CYXArticleGroup, v0.1 2025/6/23 11:15 daopei Exp $
 **/
@Data
@ToString
@NoArgsConstructor
@Document("k_cyx_article_group")
@KbRefreshMongoIndex("2025-07-01")
public class CYXArticleGroup {

    @Id
    private String id;

    /** 资讯分组KEY */
    private String groupKey;

    /** 分组类型。 {@link CYXArticleGroupType}*/
    @Indexed
    private String groupType;

    /** 分组标题 */
    private String title;

    /** 状态. SHOW/HIDE */
    private String state;

    /** 排序依据, 查询时以此字段降序排列. */
    @Indexed
    private Long seq;

    /**
     * 指定的排序依据, 降序, 最大值 999.
     * 最终会根据算法生成 {@link #seq}. 因此本字段仅做记录, 实际排序应使用 {@link #seq} 字段
     */
    private Long idx;

    /**
     * 排序风格.
     * 目前客户端可支持：
     * WATERFALL 瀑布流
     * HORIZONTAL 横向滑动
     *  */
    private String style;

    /** 显示更多跳转动作配置 */
    private ActionLink showMore;

    /** 创建时间 */
    private Long createTime;

    /** 修改时间 */
    private Long updateTime;
}
