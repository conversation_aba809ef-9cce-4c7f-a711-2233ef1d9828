package com.kaiba.m.core.domain.education.recitation;

import com.kaiba.m.core.domain.attribute.BasicAttribute;
import com.kaiba.m.core.domain.attribute.MemberAttr;
import com.kaiba.m.core.domain.attribute.MemberExtend;
import com.kaiba.m.core.domain.education.recitation.enums.AgeTypeEnum;
import com.kaiba.m.core.domain.education.recitation.enums.CommonStatusEnum;
import com.kaiba.m.core.domain.education.recitation.enums.ExpertTypeEnum;
import com.kaiba.m.core.domain.education.recitation.enums.GenderEnum;
import com.kaiba.m.core.domain.education.recitation.enums.MemberTypeEnum;
import java.util.List;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * Description: 朗诵团专家
 * Author: ZM227
 * Date: 2024/8/2 10:18
 */
@Data
@NoArgsConstructor
@ToString
@Document(collection = "k_recitation_expert")
public class Expert {
    /**
     * 主键
     */
    @Id
    private String id;

    /**
     * 成员code
     */
    @Indexed(unique = true)
    private String expertCode;

    /**
     * 成员姓名
     */
    private String name;

    /**
     * 成员年龄
     */
    private Integer age;

    /**
     * 联系电话
     */
    private String connectPhone;

    /**
     * 性别
     */
    private GenderEnum gender;

    /**
     * 专家类型:0-普通,1-资深
     */
    private ExpertTypeEnum expertType;

    /**
     * 分组key
     */
    private String groupKey;

    /**
     * 专家状态
     */
    private CommonStatusEnum status;

    /**
     * 专家头像
     */
    private String photo;

    /**
     * 兴趣爱好
     */
    private String hobbies;

    /**
     * 个性签名
     */
    private String signature;

    /**
     * 个人风采
     */
    private List<String> pictures;

    /**
     * 是否置顶
     */
    private Boolean pinned;

    /**
     * 排序值
     */
    private Integer sortValue;

    /**
     * 属性
     */
    private BasicAttribute<MemberAttr> attribute = new BasicAttribute<>();

    /**
     * 扩展信息
     */
    private BasicAttribute<MemberExtend> extendInfo = new BasicAttribute<>();

    /**
     * 创建时间, 时间戳毫秒数
     */
    private Long createTime;

    /**
     * 更新时间, 时间戳毫秒数
     */
    private Long updateTime;
}
