package com.kaiba.m.core.domain.issue;

import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * author: lyux
 * date: 18-9-26
 */
@Data
@NoArgsConstructor
@Document(collection = "issue_favorite")
public class IssueFavorite {

    @Id
    private String id;

    @Indexed
    private String issueId;

    @Indexed
    private Integer expertId;

    @Indexed
    private Integer userId;

    private Long createTime;

}
