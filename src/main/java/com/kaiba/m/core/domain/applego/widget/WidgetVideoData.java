package com.kaiba.m.core.domain.applego.widget;

import com.kaiba.lib.base.constant.applego.WidgetType;
import com.kaiba.lib.base.constant.appwidget.WidgetVideoFormat;
import com.kaiba.lib.base.constant.common.KbImageFormat;
import com.kaiba.lib.base.constant.common.KbImageScale;
import com.kaiba.lib.base.domain.applego.IWidget;
import com.kaiba.m.core.domain.applego.LegoBlock;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * author: lyux
 * date: 2024-09-04
 *
 * 组件化控件数据 - 视频控件.
 * {@link WidgetType#VIDEO}
 */
@Data
@ToString
@NoArgsConstructor
@Document("k_app_lego_widget_video")
public class WidgetVideoData implements IWidget {

    /** 物理主键 */
    @Id
    private String id;

    /** {@link LegoBlock#getId()} */
    @Indexed
    private String blockId;

    /** 电台 ID */
    private Integer siteId;

    /** 视频 url. */
    private String url;

    /** 宽, 必传. */
    private Integer width;

    /** 高, 必传. */
    private Integer height;

    /** 更新时间, 单位毫秒 */
    private Long updateTime;

    /** 创建时间, 单位毫秒 */
    private Long createTime;

    @Override
    public WidgetType getType() {
        return WidgetType.VIDEO;
    }
}
