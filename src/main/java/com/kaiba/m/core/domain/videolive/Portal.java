package com.kaiba.m.core.domain.videolive;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020/6/4
 */
@NoArgsConstructor
@ToString
@Data
@Document(collection = "k_videolive_portal")
public class Portal {
    /**
     * 主键
     */
    @Id
    private String id;

    /**
     * 电台 id
     */
    @Indexed
    private Integer siteId;

    /**
     * 地市编码
     */
    private Integer source;

    @Indexed
    private String videoliveId;

    private String title;

    private String image;

    private Long duration;

    private Long startTime;

    private Long endTime;

    private Long createTime;

    private Integer creator;

    /**
     * {@link com.kaiba.lib.base.constant.videolive.PortalState }
     */
    @Indexed
    private Integer state;

    @Indexed
    private String action;

    private Map<String, Object> actionParams;

}
