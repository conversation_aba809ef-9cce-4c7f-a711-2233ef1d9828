package com.kaiba.m.core.domain.program;

import com.kaiba.lib.base.domain.accounting.AccountModel;
import com.kaiba.lib.base.domain.common.Image;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.List;

/**
 * author wangsj
 * date 2020-09-01
 */
@Data
@NoArgsConstructor
@Document(collection = "k_emcee")
public class Emcee {

    @Id
    private String id;

    /** 关联的用户 id */
    @Indexed
    private Integer userId;

    /** 所属电台 id */
    @Indexed
    private Integer siteId;

    /** 主持人关联的资金系统账户 id. 用作用户打赏等情况. {@link AccountModel} */
    private Long accountId;

    /** 昵称 */
    private String nickname;

    /** 性别 */
    private Integer sex;

    /** 是否为保留主持人 */
    private Integer reserved;

    /** 头像 */
    private String avatar;

    /** 滚动轮播的主持人图片 */
    private List<Image> picture;

    /** 若使用主持人图片作为节目封面, 此为所使用的封面图 */
    private String programCover;

    /** 主持人描述 */
    private String description;

    /** 节目回播: 主持人专属专辑. {@link com.kaiba.lib.base.domain.playback.PlaybackAlbumModel} */
    private String albumId;

    /** 对应的主持人是否还活跃, 1 - 活跃, 0 - 不活跃. 比如主持人已离职等情况, 即为不活跃. */
    private Integer active;

    /** 更新时间 */
    private Long updateTime;

    /** 更新者用户 id */
    private Integer updateUser;

    /** 创建时间 */
    private Long createTime;

    /** 创建者用户 id */
    private Integer createUser;

    /** 尽快迁移至新逻辑 */
    @Deprecated
    private String mobile;

    /** 直播流相关, 尽快迁移至新逻辑 */
    @Deprecated
    private String pushStream;

    /** 直播流相关, 尽快迁移至新逻辑 */
    @Deprecated
    private String pullStream;

}
