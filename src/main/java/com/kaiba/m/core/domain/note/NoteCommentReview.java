package com.kaiba.m.core.domain.note;

import com.kaiba.lib.base.constant.note.NoteReviewState;
import com.kaiba.lib.base.constant.note.NoteState;
import com.kaiba.lib.base.util.GsonUtils;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.springframework.data.mongodb.core.mapping.Document;

@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@Document(collection = "k_note_comment_review")
public class NoteCommentReview extends NoteComment {

    /** 审核状态. {@link NoteReviewState} */
    private Integer reviewState;

    /** 审核批注 */
    private String reviewRemark;

    public NoteComment toNoteComment() {
        String json = GsonUtils.getGson().toJson(this);
        NoteComment comment = GsonUtils.getGson().fromJson(json, NoteComment.class);
        // 只允许从审核表进入评论表, 不允许从评论表进入审核表, 因此置空 id 以便帖子表自己生成主键
        comment.setId(null);
        comment.setState(NoteState.NORMAL.getValue());
        return comment;
    }

    public static NoteCommentReview fromNoteComment(NoteComment comment) {
        String json = GsonUtils.getGson().toJson(comment);
        NoteCommentReview commentReview = GsonUtils.getGson().fromJson(json, NoteCommentReview.class);
        commentReview.setId(null);
        commentReview.setReviewState(NoteReviewState.NOT_REVIEWED.getValue());
        return commentReview;
    }

}
