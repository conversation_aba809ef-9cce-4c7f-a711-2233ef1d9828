package com.kaiba.m.core.domain.da.eventtrack;

import com.kaiba.m.core.constant.da.eventtrack.ETDocSyncStrategyType;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * 业务稿件同步游标
 * <AUTHOR>
 * @version ETDocSyncCursor, v0.1 2025/4/14 11:18 daopei Exp $
 **/
@Data
@ToString
@NoArgsConstructor
@Document(collection = "k_event_track_doc_sync_cursor")
public class ETDocSyncCursor {

    @Id
    private String id;

    /** 策略类型. {@link ETDocSyncStrategyType} */
    @Indexed(unique = true)
    private String strategy;

    /** 同步数据上次结束的时间戳 */
    private Long timestamp;

    /** 创建时间 */
    private Long createTime;

    /** 修改时间 */
    private Long updateTime;
}
