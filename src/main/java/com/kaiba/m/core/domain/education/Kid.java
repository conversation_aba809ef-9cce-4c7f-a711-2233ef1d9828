package com.kaiba.m.core.domain.education;

import com.kaiba.lib.base.constant.education.SchoolPeriodType;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/03/19 16:50
 * K12 学生信息
 */
@Data
@ToString
@NoArgsConstructor
@Document(collection = "k_education_k12_kid")
public class Kid {

    @Id
    private String id;

    /** 学生性别 */
    private Integer gender;

    /** 学生出生年月日, 以年月日 20121001 形式给出 */
    private Integer birthday;

    /** 学生大名 */
    @Indexed
    private String realName;

    /** 学生小名 */
    private String nickname;

    /** 学生照片, 宽高比 6*9. */
    private String cover;

    /** 特长、兴趣爱好 */
    private String hobby;

    /** 个性签名 */
    private String signature;

    /** 标签：暂定记录学员参加过的活动 如小主播等 */
    private List<String> tags;

    /** 学生家长预留手机号. 可用于家长注册开吧后认领学生信息的场景. 不应随接口露出. */
    @Indexed
    private List<String> mobiles;

    /** 年级信息: 记录生成时的学生年龄 */
    private Integer age;

    /** 年级信息: 学生年级. {@link KidGradeRecord} */
    private Integer grade;

    /** 年级信息: 记录生成时学生所在学校. {@link School#getId()} */
    @Indexed
    private String schoolId;

    /** 年级信息: 学生义务教育阶段的学制. {@link SchoolPeriodType} */
    private String periodType;

    /** 年级信息: 学生年级信息推算的基准ID {@link KidGradeRecord#getId} */
    private String recordId;

    /** 年级信息: 学生年级信息推测时间 */
    private Long recordTime;

    /** 更新时间, 单位毫秒 */
    private Long updateTime;

    /** 创建时间, 单位毫秒 */
    private Long createTime;

}
