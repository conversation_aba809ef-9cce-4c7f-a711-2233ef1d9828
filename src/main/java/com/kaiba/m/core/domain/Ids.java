package com.kaiba.m.core.domain;

import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

/**
 * author: lyux
 * date: 18-10-24
 */
@Data
@NoArgsConstructor
@Document(collection = "ids")
public class Ids {

    @Id
    @Field("_id")
    private String id;

    private String name;

    @Field("id")
    private Long ids;

    private Long seq;

}
