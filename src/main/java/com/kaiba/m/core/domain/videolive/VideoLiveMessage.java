package com.kaiba.m.core.domain.videolive;

import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * <AUTHOR>
 * @date 2023/03/22 11:29
 **/
@Data
@Builder
@ToString
@NoArgsConstructor
@AllArgsConstructor
@Document(collection = "k_video_live_message")
public class VideoLiveMessage {

    @Id
    private String id;

    /** 对应的直播间 id */
    private String liveId;

    /** 用户 id */
    private Integer userId;

    /** 用户名 */
    private String userName;

    /** 用户头像 */
    private String avatar;

    /** 消息内容 */
    private String content;

    /** {@link com.kaiba.lib.base.constant.videolive.LiveMessageType} */
    private String msgType;

    /** 额外信息 */
    private String extra;

    /** 消息敏感词扫描是否通过 */
    private Boolean isGreen;

    /** 创建时间 ms */
    private Long createTime;

}