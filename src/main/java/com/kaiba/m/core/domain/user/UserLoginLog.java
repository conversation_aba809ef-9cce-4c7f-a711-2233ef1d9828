package com.kaiba.m.core.domain.user;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * author: lyux
 * date: 20-3-11
 */
@Data
@ToString
@NoArgsConstructor
@Document(collection = "k_user_login_log")
public class UserLoginLog {

    @Id
    private String id;

    /** 用户 id */
    @Indexed
    private Integer userId;

    /** 用户最后登录时的电台 id */
    private Integer siteId;

    /** 用户最后登录时的地市 id */
    private Integer source;

    /** 设备 id */
    private String cid;

    /** app version code, 应用版本号. 如 30201 */
    @Indexed
    private Integer vc;

    /** device type, 设备类型. 如 Android, iOS, WXTiny */
    private String dt;

    /** device brand, 设备品牌型号. 如 Meizu Pro6 / iPhone 10,1 */
    private String db;

    /** device version, 设备操作系统版本. 如 (Android) 4.4.1 / (iOS) 12.1 */
    private String dv;

    /** end point, 端类型. 如 车主端, 技师端, 节目互动小程序 ... {@link com.kaiba.lib.base.constant.KbEndpoint} */
    @Indexed
    private Integer ep;

    /** 发布渠道. iOS 固定为 AppStore. 小程序固定为 WX */
    private String cnl;

    /** 用户访问 ip */
    private String ip;

    /** 根据ip地址查询出来的结果 */
    private String ipCity;

    /** 用户最后登录时的经纬度 */
    private Double longitude;

    /** 用户最后登录时的经纬度 */
    private Double latitude;

    /** 登录类型. login type. {@link com.kaiba.lib.base.constant.user.UserLoginType} */
    private String loginType;

    /** 条目创建时间, 即用户最后登录的时间, 单位毫秒 */
    private Long createTime;

}
