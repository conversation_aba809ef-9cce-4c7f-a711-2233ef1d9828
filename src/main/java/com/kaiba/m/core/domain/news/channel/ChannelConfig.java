package com.kaiba.m.core.domain.news.channel;

import com.kaiba.lib.base.constant.news.NewsChannel;
import com.kaiba.lib.base.constant.news.NewsChannelType;
import com.kaiba.lib.base.constant.news.article.DisplayReplyStyle;
import com.kaiba.lib.base.constant.news.article.NDisplayState;
import com.kaiba.lib.base.domain.common.Image;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * author: lyux
 * date: 2024-08-28
 *
 * 频率频道/区县市 配置.
 */
@Data
@ToString
@NoArgsConstructor
@Document(collection = "k_news_channel_config")
public class ChannelConfig {

    @Id
    private String id;

    /** 频道标识. {@link NewsChannel} */
    @Indexed(unique = true)
    private String channel;

    /** 频道类型. {@link NewsChannelType} */
    private String type;

    /** 状态. {@link NDisplayState} */
    private String state;

    /** 频道名称 */
    private String name;

    /** 频道简称 */
    private String abbr;

    /** 频道图标 */
    private Image logo;

    /** 评论功能配置 {@link DisplayReplyStyle} */
    private String replyStyle;

    /** 查询时以此字段降序排列. */
    @Indexed
    private Long seq;

    /**
     * 人工指定的排序依据, 降序, 最大值 999.
     * 最终会根据算法生成 {@link #seq}. 因此本字段仅做记录, 实际排序应使用 {@link #seq} 字段
     */
    @Indexed(sparse = true)
    private Long idx;

    /** 更新时间, 单位毫秒. */
    private Long updateTime;

    /** 创建时间, 单位毫秒. */
    private Long createTime;

}
