package com.kaiba.m.core.domain.message;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * author: lyux
 * date: 18-9-28
 */
@Data
@NoArgsConstructor
@ToString
@Document(collection = "user_message_read_marker")
public class UserMessageReadMarker implements IMessageReadMarker {

    @Id
    private Integer id;

    private String lastReadId;

}
