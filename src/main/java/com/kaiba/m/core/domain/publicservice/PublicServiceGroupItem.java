package com.kaiba.m.core.domain.publicservice;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.CompoundIndex;
import org.springframework.data.mongodb.core.index.CompoundIndexes;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * <AUTHOR>
 * @date 2024/01/05 14:18
 */
@Data
@ToString
@NoArgsConstructor
@Document(collection = "k_public_service_group_item")
@CompoundIndex(name = "uni_iid_gid", def = "{'itemId': 1, 'groupId': 1}", unique = true)
public class PublicServiceGroupItem {

    @Id
    private String id;

    /** 分组id */
    private String groupId;

    /** 服务id */
    private String itemId;

    /** 服务在分组内的排序 默认0 手动后台调整 */
    private Long order;

    /** 创建时间 ms */
    private Long createTime;

    /** 更新时间 ms */
    private Long updateTime;

}
