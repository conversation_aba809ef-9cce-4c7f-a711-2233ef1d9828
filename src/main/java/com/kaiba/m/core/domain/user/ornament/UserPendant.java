package com.kaiba.m.core.domain.user.ornament;

import com.kaiba.lib.base.annotation.data.KbRefreshMongoIndex;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.CompoundIndex;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * author: lyux
 * date: 2025-04-10
 *
 * 用户徽章
 */
@Data
@ToString
@NoArgsConstructor
@Document(collection = "k_user_pendant")
@CompoundIndex(name = "user_pendant", def = "{'userId': 1, 'pendant': 1}", unique = true)
@KbRefreshMongoIndex("2025-05-15")
public class UserPendant {

    @Id
    private String id;

    /** 所属用户. */
    private Integer userId;

    /** 徽章标识. {@link PendantDef} */
    private String pendant;

    /** 过期时间, 单位毫秒. 为空表示不会过期. */
    @Indexed(sparse = true)
    private Long expireTime;

    /** 修改时间, 单位毫秒 */
    private Long updateTime;

    /** 创建时间, 单位毫秒 */
    private Long createTime;

}
