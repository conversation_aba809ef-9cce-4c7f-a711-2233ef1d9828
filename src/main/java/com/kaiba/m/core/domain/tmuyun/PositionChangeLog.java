package com.kaiba.m.core.domain.tmuyun;

import com.kaiba.lib.base.constant.tmuyun.EventPositionChangeType;
import com.kaiba.lib.base.constant.tmuyun.EventPositionState;

import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * <AUTHOR>
 * @date 2022/08/02 10:20
 **/
@Data
@ToString
@NoArgsConstructor
@Document(collection = "k_tmuyun_position_change_log")
public class PositionChangeLog {

    /** 物理主键 */
    @Id
    private String id;

    /** 事件点位 */
    private String position;

    /** 变化前的点位状态. {@link EventPositionState} */
    private Integer state;

    /** 操作者 */
    private Integer userId;

    /** 变更类型. {@link EventPositionChangeType} */
    private String change;

    /** 修改时间 */
    private Long createTime;

}