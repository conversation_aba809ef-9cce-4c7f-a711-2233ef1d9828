package com.kaiba.m.core.domain.program;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * author: duanyf
 * date: 2024-07-18
 **/
@Data
@ToString
@NoArgsConstructor
@Document(collection = "callboard_live")
public class CallboardLive {
    @Id
    private String id;

    private Integer userId;

    private Integer siteId;

    /** 导播台草稿类型 - 和主播台中待播的导播草稿类型一致 {@link CallboardLiveType} */
    private String type;

    /** 内容 */
    private String content;

    /** 是否置顶 0不置顶 */
    private Integer top;

    /** 排序 */
    private Integer order;

    /** 状态 {@link com.kaiba.m.core.constant.program.CallboardLiveStatusEnum} **/
    private Integer status;

    /** 创建时间, 单位秒. */
    private Long createTime;
}
