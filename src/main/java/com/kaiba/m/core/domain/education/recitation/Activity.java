package com.kaiba.m.core.domain.education.recitation;

import com.kaiba.m.core.domain.attribute.BasicAttribute;
import com.kaiba.m.core.domain.attribute.MemberAttr;
import com.kaiba.m.core.domain.attribute.MemberExtend;
import com.kaiba.m.core.domain.education.recitation.enums.ActivityTypeEnum;
import com.kaiba.m.core.domain.education.recitation.enums.CommonStatusEnum;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * Description: 朗诵团活动
 * Author: ZM227
 * Date: 2024/8/2 10:30
 */
@Data
@NoArgsConstructor
@ToString
@Document(collection = "k_recitation_activity")
public class Activity {

    /**
     * 主键
     */
    @Id
    private String id;

    /**
     * 活动code
     */
    @Indexed(unique = true)
    private String activityCode;

    /**
     * 活动主标题
     */
    private String title;

    /**
     * 活动子标题
     */
    private String subTitle;

    /**
     * 活动状态
     */
    private CommonStatusEnum status;

    /**
     * 报名时间起始
     */
    private Long registerStart;

    /**
     * 报名时间截止
     */
    private Long registerEnd;

    /**
     * 活动类型
     */
    private ActivityTypeEnum activityType;

    /**
     * 属性
     */
    private BasicAttribute<MemberAttr> attribute = new BasicAttribute<>();

    /**
     * 扩展信息
     */
    private BasicAttribute<MemberExtend> extendInfo = new BasicAttribute<>();

    /**
     * 创建时间, 时间戳毫秒数
     */
    private Long createTime;

    /**
     * 更新时间, 时间戳毫秒数
     */
    private Long updateTime;

}
