package com.kaiba.m.core.domain;

import com.kaiba.lib.base.response.KbCode;
import com.kaiba.lib.base.response.KbException;
import lombok.Getter;

/**
 * author: lyux
 * date: 2023-08-09
 */
@Getter
public class SeqBasedTimeFirstOrder {

    private static final transient long TIME_BASE = 1685548800000L; // 2023-06-01
    private static final transient long TIME_POWER = 1000;

    private final String id;

    private final Long idx;

    private final long seq;

    public SeqBasedTimeFirstOrder(String id, Long idx, long time) {
        this.id = id;
        this.idx = idx;
        this.seq = calculateSeq(time, idx);
    }

    public static long calculateSeq(long time, Long idx) {
        long t = time - TIME_BASE;
        if (idx == null) {
            return t * TIME_POWER;
        } else {
            if (idx >= TIME_POWER || idx < 0) {
                throw new KbException(KbCode.ILLEGAL_ARGUMENT, "idx shoud be [0," + TIME_POWER + ")");
            }
            return idx + t * TIME_POWER;
        }
    }

}
