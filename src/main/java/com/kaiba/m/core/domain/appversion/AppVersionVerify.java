package com.kaiba.m.core.domain.appversion;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.CompoundIndex;
import org.springframework.data.mongodb.core.index.CompoundIndexes;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * author: duanyf
 * date: 2024-06-25
 **/
@Data
@ToString
@NoArgsConstructor
@Document(collection = "k_app_version_verify")
@CompoundIndexes({
    @CompoundIndex(unique = true, name = "uniq_endpoint_market_versioncode", def = "{ endPoint: 1, market: 1, versionCode: -1 }"),
})
public class AppVersionVerify {

    @Id
    private String id;

    /** 端点 {@link com.kaiba.lib.base.constant.KbEndpoint} */
    private Integer endPoint;

    /** 应用市场名 */
    private String market;

    /** 应用版本号 */
    private Integer versionCode;

    /** 启用状态，0关闭 1开启 */
    private Integer state;

    /** 更新时间, 单位毫秒. */
    private Long updateTime;

    /** 创建时间, 单位毫秒. */
    private Long createTime;

}
