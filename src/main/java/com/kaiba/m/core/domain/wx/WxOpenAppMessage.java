package com.kaiba.m.core.domain.wx;

import com.kaiba.lib.base.domain.common.Audio;
import com.kaiba.lib.base.domain.common.Image;
import com.kaiba.lib.base.domain.common.Video;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * <AUTHOR>
 * @version WxGzhMessage, v0.1 2024/4/17 15:03 daopei Exp $
 **/
@Data
@ToString
@NoArgsConstructor
@Document(collection = "k_wx_open_app_message")
public class WxOpenAppMessage {

    /** 微信消息ID, wxData#MsgId */
    @Id
    private String id;
    /** 电台ID */
    @Indexed
    private Integer siteId;
    /** 文字内容 */
    private String content;
    /** 音频 qiniu oss key */
    private Audio audio;
    /** 视频 qiniu oss key */
    private Video video;
    /** 图片 qiniu oss key */
    private Image pic;
    /** 微信用户openId, wxData#FromUserName*/
    @Indexed
    private String wxFromOpenId;
    /** 微信消息类型: text,voice,image,video,location,link */
    @Indexed
    private String wxMsgType;
    /** 接收消息主体用户名 wxData#ToUserName */
    private String wxToWxId;
    /** 微信消息内容 */
    private String wxData;
    /** 创建时间 */
    @Indexed
    private Long createTime;
    /** 更新时间 */
    private Long updateTime;
    /** 状态, 1:有效, 0:删除 */
    private Integer state;
    
    /** 当作为帖子发布时回写 */
    private String noteId;
    /** 审核贴ID */
    private String noteReviewId;
}
