package com.kaiba.m.core.domain.workorder.carsafeguard;

import com.kaiba.lib.base.domain.common.Image;
import com.kaiba.lib.base.domain.common.Video;
import com.kaiba.lib.base.domain.workorder.WOACLStringData;
import com.kaiba.m.core.service.workorder.carsafeguard.CarSafeguardBizType;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.List;

/**
 * <AUTHOR>
 * @version CarSafeguardCaseContent, v0.1 2024/7/11 13:59 daopei Exp $
 **/
@Data
@ToString
@NoArgsConstructor
@Document(collection = "k_work_order_car_safeguard_ride_hailing_case")
public class CarSafeguardRideHailingCase {

    @Id
    private String id;
    /**
     * 发布者 id. 注意: 发布者未必就是案件提问者. 在后台由管理员录入的问题, 发布者即管理员.
     */
    private Integer userId;
    /**
     * 内容类型{@link CarSafeguardBizType}
     */
    private String contentType;
    /**
     * 问题描述:维权问题的文字内容描述
     */
    private String content;
    /**
     * 内容: 图片列表
     */
    private List<Image> images;
    /**
     * 内容: 视频
     */
    private Video video;
    /**
     * 内容: 用户个人信息列表
     */
    private List<WOACLStringData> personal;
    /**
     * 初始投诉对象. 系统中暂无录入的商家.
     * 该属性在未指定投诉商家的时候支持填入一个商家名称的文本.
     */
    private String originResolverLack;
    /**
     * 地区编码:杭州330100
     */
    private String source;
    /**
     * 创建时间
     */
    private Long createTime;
    /**
     * 修改时间
     */
    private Long updateTime;
    /**
     * 约车时间
     */
    private Long rideHailingTime;
    /**
     * 网约车车牌号
     */
    private String rideHailingCarNo;
    /**
     * 用户约车手机号
     */
    private String rideHailingUserPhone;

}
