package com.kaiba.m.core.domain.car;

import com.kaiba.lib.base.domain.common.Image;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

/**
 * author: lyux
 * date: 18-9-18
 */
@Data
@NoArgsConstructor
@Document(collection = "brand")
public class CarBrand {

    @Id
    private String id;

    private String name;

    private String code;

    private String letter;

    @Field("listorder")
    private Integer listOrder;

    @Field("createtime")
    private Long createTime;

    /**
     * 汽车之家ID
     */
    private Integer autohomeId;
    /**
     * 品牌图片.
     * 历史存量数据以及客户端默认解析规则 url:brand_${code}
     * */
    private Image logo;
}
