package com.kaiba.m.core.domain.actmix;

import com.kaiba.lib.base.domain.common.Image;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.Map;

/**
 * author: lyux
 * date: 2022-11-04
 */
@Data
@ToString
@NoArgsConstructor
@Document(collection = "k_act_mix_banner")
public class ActMixBanner {

    @Id
    private String id;

    private String referenceId;

    private Integer siteId;

    private String title;

    /**
     * {@link com.kaiba.lib.base.constant.actmix.BannerState}
     */
    private Integer state;

    /** 屏位顺序. */
    private Integer order;

    /** 屏位自动切换时的展示时间. 单位毫秒. */
    private Integer duration;

    /**
     * ACT_MIX - 1
     * OTHER - 2
     */
    private Integer origin;

    /** 封面. 尺寸固定. */
    private Image cover;

    /** 跳转类型. 参考 {@link com.kaiba.lib.base.util.appaction.AppActionType} */
    private String action;

    /** 跳转参数 */
    private Map<String, Object> actionParams;

    /** banner各种配置 */
    private Map<String, Object> configMap;

    /** 预约开始时间, 单位毫秒. 可为空. 有值则在指定时刻迁移状态, SIGNED -> SHOW. */
    private Long scheduleStartTime;

    /** 预约结束时间, 单位毫秒. 可为空. 有值则在指定时刻迁移状态, SIGNED/SHOW -> ARCHIVED. */
    private Long scheduleEndTime;

    /** 开始展示时间, 在状态迁移至 SHOW 时赋值, 单位毫秒. 仅做审计用, 不参与逻辑. */
    private Long startTime;

    /** 结束展示时间, 在状态迁移至 ARCHIVED 时赋值, 单位毫秒. 仅做审计用, 不参与逻辑. */
    private Long endTime;

    /** 更新时间, 单位毫秒. */
    private Long updateTime;

    /** 创建时间, 单位毫秒. */
    private Long createTime;

}
