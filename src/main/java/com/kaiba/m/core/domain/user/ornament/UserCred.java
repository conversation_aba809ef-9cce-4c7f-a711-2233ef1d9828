package com.kaiba.m.core.domain.user.ornament;

import com.kaiba.lib.base.annotation.data.KbRefreshMongoIndex;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.CompoundIndex;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * author: lyux
 * date: 2025-04-10
 *
 * 用户头冠
 */
@Data
@ToString
@NoArgsConstructor
@Document(collection = "k_user_cred")
@CompoundIndex(name = "user_cred", def = "{'userId': 1, 'cred': 1}", unique = true)
@KbRefreshMongoIndex("2025-05-15")
public class UserCred {

    @Id
    private String id;

    /** 所属用户. */
    private Integer userId;

    /** 认证标识. {@link CredDef} */
    private String cred;

    /** 过期时间, 单位毫秒. 为空表示不会过期. */
    @Indexed(sparse = true)
    private Long expireTime;

    /** 修改时间, 单位毫秒 */
    private Long updateTime;

    /** 创建时间, 单位毫秒 */
    private Long createTime;

}
