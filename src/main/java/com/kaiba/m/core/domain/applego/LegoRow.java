package com.kaiba.m.core.domain.applego;

import com.kaiba.lib.base.constant.applego.RowGravity;
import com.kaiba.lib.base.constant.common.KbAlign;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.List;

/**
 * author: lyux
 * date: 2023-05-24
 *
 * 布局行. 一旦创建, 不许修改.
 * 组件化布局一行. 所有 宽/高/边距 单位均为相对单位, 1 单位高度表示屏幕宽度的 1/1440 .
 */
@Data
@ToString
@NoArgsConstructor
@Document("k_app_lego_row")
public class LegoRow {

    /** 物理主键 */
    @Id
    private String id;

    /** 行高, 正值表示指定行高, 负值表示该行高度无限 (即支持无限下拉加载), null 表示客户端自适应. */
    private Integer height;

    /** 行内控件的横向排布方式. {@link RowGravity}. 默认为均匀排布 {@link RowGravity#EVEN}. */
    private String gravityHorizontal;

    /**
     * 行内控件的纵向排布方式. 支持三种类型:
     * {@link KbAlign#TOP}, {@link KbAlign#CENTER}, {@link KbAlign#BOTTOM}.
     * 默认为 {@link KbAlign#CENTER}.
     */
    private String gravityVertical;

    /** 外边距 top */
    private Integer marginTop;

    /** 外边距 bottom */
    private Integer marginBottom;

    /** 外边距 left */
    private Integer marginLeft;

    /** 外边距 right */
    private Integer marginRight;

    /** 内边距 top */
    private Integer paddingTop;

    /** 内边距 bottom */
    private Integer paddingBottom;

    /** 内边距 left */
    private Integer paddingLeft;

    /** 内边距 right */
    private Integer paddingRight;

    /** 背景图片. */
    private String bkgImage;

    /** 背景颜色, 以#开头接6或8位16进制数, 颜色位在最前或可省略. 举例: #fff0f0f0 或 #f6f6f6. 如果同时存在背景图片, 则此项设置无效. */
    private String bkgColor;

    /** 行内控件顺序列表. {@link LegoBlock} */
    private List<String> blockIds;

    /** 创建时间, 单位毫秒 */
    private Long createTime;

}
