package com.kaiba.m.core.domain.media;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * <AUTHOR>
 * @version AliyunMediaTag, v0.1 2024/1/9 16:07 daopei Exp $
 **/
@Data
@ToString
@NoArgsConstructor
@Document(collection = "k_aliyun_media_tag")
public class AliyunMediaTag {

    @Id
    private String id;
    /** 阿里云分类ID */
    private String tagName;
    /** 0:删除, 1:有效 */
    private Integer state;
    /** 创建时间 */
    private Long createTime;
    /** 修改时间 */
    private Long updateTime;
    /** 创建人 */
    private Integer creatorId;
}
