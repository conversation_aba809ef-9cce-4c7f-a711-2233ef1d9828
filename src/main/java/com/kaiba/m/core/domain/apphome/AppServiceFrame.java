package com.kaiba.m.core.domain.apphome;

import com.kaiba.lib.base.constant.applego.AppComponentState;
import com.kaiba.lib.base.domain.appwidget.banner.BannerInstanceModel;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * author: lyux
 * date: 2023-07-05
 *
 * flutter 客户端首页底部导航栏
 */
@Data
@ToString
@NoArgsConstructor
@Document("k_app_tab_service_frame")
public class AppServiceFrame {

    /** 物理主键 */
    @Id
    private String id;

    /** 电台 id. */
    @Indexed
    private Integer siteId;

    /** 状态 {@link AppComponentState} */
    private Integer state;

    /** 标题 */
    private String title;

    /** 背景图片. */
    private String headBkgImage;

    /** banner 数据源. {@link BannerInstanceModel} */
    private String bannerInstanceKey;

    /** 是否显示最近使用. 客户端 7.6.0 版本后不再使用该字段. */
    @Deprecated
    private Boolean showRecentUsed;

    /** 创建时间, 单位毫秒 */
    private Long createTime;

    /** 更新时间, 单位毫秒 */
    private Long updateTime;

    /** 上线时间, 单位毫秒 */
    private Long onlineTime;

}
