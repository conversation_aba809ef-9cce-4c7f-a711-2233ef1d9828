package com.kaiba.m.core.domain.user.ornament;

import com.kaiba.lib.base.annotation.data.KbRefreshMongoIndex;
import com.kaiba.lib.base.constant.user.UserOrnamentState;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * author: lyux
 * date: 2025-04-10
 *
 * 认证枚举
 */
@Data
@ToString
@NoArgsConstructor
@Document(collection = "k_user_cred_definition")
@KbRefreshMongoIndex("2025-05-15")
public class CredDef {

    @Id
    private String id;

    /** 认证类型 */
    @Indexed(unique = true)
    private String cred;

    /** 状态. {@link UserOrnamentState} */
    private String state;

    /** 认证名称 */
    private String name;

    /** 认证描述 */
    private String desc;

    /** 头冠, 可为空. {@link CrestDef#getCrest()}. */
    private String crest;

    /** 徽章, 可为空. {@link PendantDef#getPendant()}. */
    private String pendant;

    /** V标, 可为空. 必须为方形 png */
    private String badge;

    /** 名字颜色, 可为空. 以#开头接6位16进制数. 举例: #f6f6f6. */
    private String nameColor;

    /** 授予用户时的过期时间, 相对时间表达式 */
    private String initExpireRTE;

    /** 修改时间, 单位毫秒 */
    private Long updateTime;

    /** 创建时间, 单位毫秒 */
    private Long createTime;

}
