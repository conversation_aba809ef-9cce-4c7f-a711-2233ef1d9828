package com.kaiba.m.core.domain.user;

import com.kaiba.lib.base.domain.common.Image;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.List;

@Data
@ToString
@NoArgsConstructor
@Document(collection = "k_user_update_log")
public class UserUpdateLog {

    @Id
    private String id;

    @Indexed
    private Integer userId;

    @Indexed
    private String userName;

    @Indexed
    private String mobile;

    private Long createTime;

    private Integer creatorId;

    private Object preUser;
}
