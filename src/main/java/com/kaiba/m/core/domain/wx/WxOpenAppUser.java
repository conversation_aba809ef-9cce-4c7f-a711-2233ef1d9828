package com.kaiba.m.core.domain.wx;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.CompoundIndex;
import org.springframework.data.mongodb.core.index.CompoundIndexes;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * 用于解决接入三方平台管理的微信应用无法集成在一个开放平台内而导致的unionId不互通问题
 * 本地记录各个电台公众号用户所关联的userId,以达到用户互通
 * <AUTHOR>
 * @version WxOpenAppUser, v0.1 2024/6/18 17:02 daopei Exp $
 **/
@Data
@ToString
@NoArgsConstructor
@Document(collection = "k_wx_open_app_user")
public class WxOpenAppUser {

    @Id
    private String id;
    /** 电台ID */
    @Indexed
    private Integer siteId;
    /** wx openId */
    @Indexed(unique = true)
    private String openId;
    /** wx unionId 冗余属性,可能会为空*/
    private String unionId;
    /** 关联成功的userId */
    private Integer userId;

    private Long createTime;

    private Long updateTime;
}
