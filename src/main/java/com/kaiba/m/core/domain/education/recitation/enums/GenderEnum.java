package com.kaiba.m.core.domain.education.recitation.enums;

import java.util.Arrays;
import java.util.Objects;
import lombok.Getter;

/**
 * Description: 性别枚举
 * Author: ZM227
 * Date: 2024/8/6 13:38
 */
@Getter
public enum GenderEnum {

    MALE(1, "男"),
    FEMALE(2, "女");

    private final Integer code;
    private final String desc;

    GenderEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static GenderEnum forCode(Integer code) {
        if (Objects.isNull(code)) {
            return null;
        }
        return Arrays.stream(GenderEnum.values())
            .filter(type -> Objects.equals(type.getCode(), code)).findFirst().orElse(MALE);
    }
}
