package com.kaiba.m.core.domain.site;

import com.kaiba.lib.base.domain.site.SiteModel;
import com.kaiba.m.core.domain.news.legacy.SiteNewsThread;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;


/**
 * author: lyux
 * date: 18-7-16
 */
@Data
@NoArgsConstructor
@Document(collection = "site")
public class Site {

    @Id
    private Integer id;

    private String name;

    private Integer source;

    private String db;

    @Field("fm_name")
    private String fmName;

    @Field("fm_channel")
    private String fmChannel;

    private String freeway;

    private SiteModel.Location gps;

    @Field("is_open")
    private Integer open;

    private String telephone;

    @Field("director_tel")
    private String directorTel;

    private String logo;

    @Field("wx_miniprogram_open")
    private Integer wxMiniOpen;

    private String aliStreamHost;

    private Integer recommend;

    @Field("broad_url")
    private String broadUrl;

    //此处只做显示用，因为界面暂时找不到合适的放置，
    /** {@link SiteNewsThread#getMultiply()} */
    @Field("announce_hits")
    private Integer announceHit;

    @Field("createuser")
    private Integer createUser;

    @Field("createtime")
    private Long createTime;
}
