package com.kaiba.m.core.domain.playback;

import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

@Data
@NoArgsConstructor
@Document(collection = "k_playback_thread")
public class PlaybackMainThread {

    /** 主键 */
    @Id
    private String id;

    /** 电台 id */
    @Indexed(unique = true)
    private Integer siteId;

    /** 板块 id */
    @Indexed
    private String threadId;

    /** 创建者 id */
    private Integer creatorId;

    /** 创建时间 */
    private Long createTime;

}
