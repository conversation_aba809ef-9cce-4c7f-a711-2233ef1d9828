package com.kaiba.m.core.domain.tmuyun;

import com.kaiba.lib.base.domain.tmuyun.tmuyun.ArticleApiModel;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * <AUTHOR>
 * @date 2022/07/25 10:26
 **/
@Data
@ToString
@NoArgsConstructor
@Document(collection = "k_tmuyun_article_send_record")
public class ArticleSendRecord {

    @Id
    private String id;

    /** 原稿件id */
    @Indexed
    private String refId;

    /** 开吧内部的稿件类别. {@link  com.kaiba.lib.base.constant.tmuyun.TmuyunRefType} */
    private String type;

    /** 向省宣发送的稿件数据快照 */
    private ArticleApiModel article;

    /** {@link com.kaiba.lib.base.constant.tmuyun.TmuyunSendResult}*/
    private Integer sendResult;

    /** 调用天目稿件接口的操作类型, 由天目定义. {@link com.kaiba.lib.base.constant.tmuyun.TmuyunStatus} */
    private Integer tmuApiOperation;

    /** 最后一次调用天目发送稿件 api 的返回信息 */
    private String tmuApiMsg;

    /** 最后一次调用天目发送稿件 api 的返回信息 */
    private Integer tmuApiCode;

    /** 稿件递送给省宣的时间戳. 单位毫秒. */
    private Long tmuPubTime;

    /** 稿件从省宣撤稿时间戳. 单位毫秒. */
    private Long tmuRecallTime;

    /** 重试次数 */
    private Integer retryCount;

    /** 最后一次重试时间, 单位毫秒 */
    private Long lastRetryTime;

    /** 单位毫秒 */
    private Long createTime;

    /** 单位毫秒 */
    private Long updateTime;

    /** 稿件修改后的快照引用id */
    private String snapshotId;
}
