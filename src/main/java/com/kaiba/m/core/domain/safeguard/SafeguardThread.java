package com.kaiba.m.core.domain.safeguard;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * <AUTHOR>
 * @date 2019/8/5
 */
@Data
@NoArgsConstructor
@ToString
@Document(collection = "k_note_safeguard_thread")
public class SafeguardThread {
    /**
     * 主键
     */
    @Id
    private String id;

    /**
     * 板块 id
     */
    @Indexed
    private String threadId;

    /**
     * 创建者 id
     */
    private Integer creatorId;

    /**
     * 创建时间
     */
    private Long createTime;
}
