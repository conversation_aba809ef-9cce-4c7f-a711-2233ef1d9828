package com.kaiba.m.core.domain.wx;

import com.kaiba.lib.base.domain.common.Audio;
import com.kaiba.lib.base.domain.common.Image;
import com.kaiba.lib.base.domain.common.Video;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * php写入的微信消息
 * todo 迁移完后删除
 * <AUTHOR>
 * @version WxGzhMessage, v0.1 2024/4/26 16:50 daopei Exp $
 **/
@Data
@ToString
@NoArgsConstructor
@Document(collection = "wx_gzh_message")
public class WxGzhMessage {

    @Id
    private String id;
    /** 电台ID */
    private Integer siteId;
    /** 音频 qiniu oss key */
    private String audio;
    /** 视频 qiniu oss key */
    private String video;
    /** 图片 qiniu oss key */
    private String pic;
    /** 微信用户openId, wxData#FromUserName*/
    private String wx_from_openId;
    /** 微信消息类型: text,voice,image,video,location,link */
    private String wx_msg_Type;
    /** 接收消息主体用户名 wxData#ToUserName */
    private String wx_to_wxId;
    /** 微信消息内容 */
    private String wx_data;
    /** 创建时间 */
    private Long createTime;
}
