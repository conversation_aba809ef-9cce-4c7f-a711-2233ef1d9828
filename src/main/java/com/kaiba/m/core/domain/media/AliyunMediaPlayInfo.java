package com.kaiba.m.core.domain.media;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * <AUTHOR>
 * @version AliyunMediaPlayInfo, v0.1 2024/1/10 14:04 daopei Exp $
 **/
@Data
@ToString
@NoArgsConstructor
@Document(collection = "k_aliyun_media_play_info")
public class AliyunMediaPlayInfo {

    @Id
    private String id;
    /** 本系统领域内主体关联ID */
    @Indexed
    private String refId;
    // ------- 阿里云属性----------
    /** 阿里云 媒体资源ID*/
    private String mediaId;
    /** 阿里云创建时间 */
    private Long creationTime;
    /** 阿里云状态: Normal:正常,Invisible:不可见 */
    private String status;
    /** 阿里云 媒体流高度。单位：px */
    private Long height;
    /** 阿里云 媒体流宽度。单位：px */
    private Long width;
    /** 阿里云 媒体流格式 */
    private String format;
    /** 阿里云 视频流清晰度定义。取值：
     FD：流畅。
     LD：标清。
     SD：高清。
     HD：超清
     */
    private String definition;
    /** 阿里云 媒体流长度。单位：秒 */
    private String duration;
    /** 阿里云 媒体流转码的作业ID。作为媒体流的唯一标识 */
    private String jobId;
    /** 阿里云 媒体流大小。单位：Byte */
    private Long size;
    /** 阿里云 文件播放地址 */
    private String fileUrl;
}
