package com.kaiba.m.core.domain.activityhub;

import com.kaiba.lib.base.domain.common.Image;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.List;

/**
 * author: lyux
 * date: 2023-11-22
 *
 * 活动页面表.
 */
@Data
@ToString
@NoArgsConstructor
@Document(collection = "k_activity_hub_page")
public class KbActivityPage {

    @Id
    private String id;

    /** {@link KbActivity} */
    @Indexed
    private String activityId;

    /** 页面标识，埋点稿件数据使用 */
    private String pageKey;

    /** 页面全称, 前端展示标题. */
    private String name;

    /** 页面简称. 尽量简短, 可能会用作管理后台活动菜单页. */
    private String abbr;

    /** 页面描述. 非必填 */
    private String desc;

    /** 页面地址. */
    @Indexed
    private String url;

    /** 页面地址中的 host 部分. 此为 url 的冗余字段, 仅为方便查询用. */
    private String urlHost;

    /** 页面地址中的 path 部分. 此为 url 的冗余字段, 仅为方便查询用. */
    @Indexed
    private String urlPath;

    /** 页面地址中的 query 部分. 此为 url 的冗余字段, 仅为方便查询用. */
    private String urlQuery;

    /** 是否需要登录. 可能用于管理后台预览逻辑. */
    private Boolean requireLogin;

    /** 页面地址, 用作统计等用途的逻辑匹配. */
    private List<Image> screenshots;

    /** 查询时以此字段降序排列. */
    @Indexed
    private Long seq;

    /**
     * 人工指定的排序依据, 降序, 最大值 999.
     * 最终会根据算法生成 {@link #seq}. 因此本字段仅做记录, 实际排序应使用 {@link #seq} 字段.
     */
    private Long idx;

    /** 更新时间, 单位毫秒 */
    private Long updateTime;

    /** 创建时间, 单位毫秒 */
    private Long createTime;

}
