package com.kaiba.m.core.domain.apphome;

import com.kaiba.lib.base.constant.apphome.AppHomeTabBubbleState;
import com.kaiba.lib.base.constant.apphome.AppHomeTabType;
import com.kaiba.lib.base.rte.RelativeTimeExpression;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * author: lyux
 * date: 2023-10-09
 */
@Data
@ToString
@NoArgsConstructor
@Document("k_app_tab_home_frame_bubble")
public class AppTabBubble {

    /** 气泡 ID */
    @Id
    private String id;

    /** 电台 id. */
    @Indexed
    private Integer siteId;

    /** {@link AppHomeTabType} */
    @Indexed
    private String tab;

    /** {@link AppHomeTabBubbleState} */
    @Indexed
    private String state;

    /** 气泡文本内容 */
    private String text;

    /** 被点击后, 再次出现的时间. 相对时间表达式. {@link RelativeTimeExpression}. 为空表示只显示一次. */
    private String reappearRTE;

    /** 开始时间, 单位毫秒. 必须同时满足状态为上线, 且时间在 start-end 范围内, 才会在客户端显示. */
    private Long startTime;

    /** 结束时间, 单位毫秒. 必须同时满足状态为上线, 且时间在 start-end 范围内, 才会在客户端显示. */
    private Long endTime;

    /** 更新时间, 单位毫秒 */
    private Long updateTime;

    /** 创建时间, 单位毫秒 */
    private Long createTime;

}
