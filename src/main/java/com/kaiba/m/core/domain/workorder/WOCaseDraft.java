package com.kaiba.m.core.domain.workorder;

import com.kaiba.lib.base.constant.workorder.WOBusiness;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * 工单案件草稿箱
 * <AUTHOR>
 * @version WOCaseDraft, v0.1 2024/8/6 11:34 daopei Exp $
 **/
@Data
@ToString
@NoArgsConstructor
@Document(collection = "k_work_order_case_draft")
public class WOCaseDraft {

    @Id
    private String id;
    /** 业务类型. {@link WOBusiness} */
    private String biz;
    /** 草稿内容. json结构字符串,具体形式由前端定义和解析使用 */
    private String content;
    /** 创建人 */
    private Integer operator;
    /** 创建时间 */
    private Long createTime;
    /** 修改时间 */
    private Long updateTime;
}
