package com.kaiba.m.core.domain.workorder.carsafeguard;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * 汽车维权定时器暂停记录表历史表,暂停结束后由[CarSafeguardTriggerPause]迁移入此表
 * <AUTHOR>
 * @version CarSafeguardTriggerPause, v0.1 2024/7/19 11:05 daopei Exp $
 **/
@Data
@ToString
@NoArgsConstructor
@Document(collection = "k_work_order_car_safeguard_trigger_pause_his")
public class CarSafeguardTriggerPauseHis {

    @Id
    private String id;

    /** 案件ID. WOCase */
    @Indexed
    private String caseId;

    /** 案件对应内容ID.  */
    private String contentId;

    /** 暂停后剩余时间. 毫秒数 */
    private Long pauseLessMils;

    /** 暂停操作用户ID */
    private Integer pauseUserId;

    /** 启动操作用户ID */
    private Integer resumeUserId;

    /** 暂停时间 */
    private Long pauseTime;
    
    /** 创建时间 */
    private Long createTime;


}
