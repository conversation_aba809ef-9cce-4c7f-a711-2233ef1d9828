package com.kaiba.m.core.domain.news.legacy;

import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * <AUTHOR>
 * @date 2023/03/28 16:22
 **/
@Data
@ToString
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Document(collection = "k_news_rush")
public class NewsRush {

    @Id
    private String id;

    /** 对应的资讯 id */
    private String newsId;

    /** 对应的抽奖 id */
    private String rushId;

    /** 标题 */
    private String title;

    /** 封面 */
    private String cover;

    /** 操作用户 id */
    private Integer userId;

    /** 对应的抽奖开始时间 ms */
    private Long startTime;

    /** 对应的抽奖结束时间 ms */
    private Long endTime;

    /** 创建时间 ms */
    private Long createTime;

    /** 更新时间 ms */
    private Long updateTime;

}
