package com.kaiba.m.core.domain.replydict;

import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * <AUTHOR>
 * @date 2020/8/3
 */
@Data
@NoArgsConstructor
@Document(collection = "k_reply_key")
public class ReplyKey {

    @Id
    private String id;

    @Indexed
    private String key;

    @Indexed
    private Integer siteId;

    @Indexed
    private String groupId;

    private Integer createUserId;

    private Long createTime;
}
