package com.kaiba.m.core.domain.news.pool.topic;

import com.kaiba.lib.base.constant.common.DummyFantasy;
import com.kaiba.lib.base.constant.news.article.LayoutFoldPolicy;
import com.kaiba.lib.base.constant.news.article.NewsContentType;
import com.kaiba.lib.base.domain.common.Image;
import com.kaiba.lib.base.domain.news.pool.topic.TopicFloat;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.List;

/**
 * author: lyux
 * date: 2024-08-30
 *
 * 统一顶部布局.
 */
@Data
@ToString
@NoArgsConstructor
@Document(collection = "k_news_topic_layout")
public class TopicLayout {

    @Id
    private String id;

    /** 电台 id */
    private Integer siteId;

    /** {@link DummyFantasy} */
    private String style;

    /** 字体颜色. 以#开头接6位16进制数. 举例: #f6f6f6.  */
    private String fontColor;

    /** 背景色, 以#开头接6位16进制数. 举例: #f6f6f6. 背景图总是绘制在背景色之上. */
    private String bkgColor;

    /** 背景图. 背景图总是绘制在背景色之上. */
    private Image bkgImage;

    /** 头图 */
    private Image headerImage;

    /**
     * 头图样式 {@link DummyFantasy}:
     * * {@link DummyFantasy#CENTAUR} 头图和直播互斥, 直播优先. 默认样式.
     * * {@link DummyFantasy#WYVERN} 头图和直播可以同时存在, 头图在上.
     * * {@link DummyFantasy#BASILISK} 头图和直播可以同时存在, 直播在上.
     */
    private String headerStyle;

    /** 视频流地址 */
    private String videoUrl;

    /** 视频封面 */
    private Image videoCover;

    /** 是否直播. 默认为否. */
    private Boolean videoLive;

    /** 是否为纯音频. 默认为否. */
    private Boolean audioOnly;

    /** 视频流格式 */
    private String videoFormat;

    /** 视频开始展示的时间, 时间戳, 单位毫秒 */
    private Long videoStartTime;

    /** 视频结束展示的时间, 时间戳, 单位毫秒 */
    private Long videoEndTime;

    /** 视频是否启用 */
    private Boolean videoEnabled;

    /** 门洞实例 key */
    private String iconGridKey;

    /**
     * 样式枚举使用 {@link DummyFantasy}:
     *
     * {@link DummyFantasy#CERBERUS} - 每行 5 个, 无背景;
     * {@link DummyFantasy#BASILISK} - 每行 4 个, 无背景;
     * {@link DummyFantasy#BEHEMOTH} - 每行 3 个, 无背景;
     * {@link DummyFantasy#MEDUSA} - 每行 4 个, 图标尺寸较大, 无背景;
     */
    private String iconGridStyle;

    /** 门洞是否启用 */
    private Boolean iconGridEnabled;

    /** 正文内容 */
    private String content;

    /** 正文格式. {@link NewsContentType} */
    private String contentType;

    /** 顶部轮播图实例 key */
    private String bannerKey;

    /** 顶部轮播图样式 {@link DummyFantasy} */
    private String bannerStyle;

    /** 顶部轮播图是否启用 */
    private Boolean bannerEnabled;

    /** 腰封轮播图实例 key */
    private String beltKey;

    /** 腰封轮播图样式 {@link DummyFantasy} */
    private String beltStyle;

    /** 腰封轮播图是否启用 */
    private Boolean beltEnabled;

    /** 投票, 支持多个 */
    private List<String> voteList;

    /** 投票区域是否启用 */
    private Boolean voteEnabled;

    /** 内容区折叠策略, 默认为 {@link LayoutFoldPolicy#FORBID} */
    private String foldPolicy;

    /** 浮标组件 */
    private TopicFloat floatIcon;


    /** 创建时间, 单位毫秒. 设置后不可修改. */
    private Long createTime;

    /** 更新时间, 单位毫秒. */
    private Long updateTime;

}
