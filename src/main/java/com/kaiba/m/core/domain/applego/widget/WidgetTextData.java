package com.kaiba.m.core.domain.applego.widget;

import com.kaiba.lib.base.constant.applego.WidgetType;
import com.kaiba.lib.base.constant.common.KbAlign;
import com.kaiba.lib.base.constant.common.KbTextStyle;
import com.kaiba.lib.base.domain.applego.IWidget;
import com.kaiba.m.core.domain.applego.LegoBlock;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * author: lyux
 * date: 2023-05-24
 *
 * 组件化控件数据 - 文本控件.
 * {@link com.kaiba.lib.base.constant.applego.WidgetType#TEXT}
 */
@Data
@ToString
@NoArgsConstructor
@Document("k_app_lego_widget_text")
public class WidgetTextData implements IWidget {

    /** 物理主键 */
    @Id
    private String id;

    /** {@link LegoBlock#getId()} */
    @Indexed
    private String blockId;

    /** 电台 ID */
    private Integer siteId;

    /** 文字内容 */
    private String text;

    /** 文字颜色. 格式举例: #fff0f0f0 . */
    private String textColor;

    /** {@link KbTextStyle} */
    private String textStyle;

    /** 文字大小, 单位 pt */
    private Integer textSize;

    /** 文字位置 {@link KbAlign} */
    private Integer textAlign;

    /** 最大行数 */
    private Integer textMaxLine;

    /** 更新时间, 单位毫秒 */
    private Long updateTime;

    /** 创建时间, 单位毫秒 */
    private Long createTime;

    @Override
    public WidgetType getType() {
        return WidgetType.TEXT;
    }
}
