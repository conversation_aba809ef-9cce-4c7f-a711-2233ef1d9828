package com.kaiba.m.core.domain.applego.widget;

import com.kaiba.lib.base.constant.applego.WidgetType;
import com.kaiba.lib.base.constant.common.DummyFantasy;
import com.kaiba.lib.base.constant.common.KbImageFormat;
import com.kaiba.lib.base.domain.applego.IWidget;
import com.kaiba.m.core.domain.applego.LegoBlock;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * author: lyux
 * date: 2023-05-24
 *
 * 组件化控件数据 - preference 控件.
 * {@link com.kaiba.lib.base.constant.applego.WidgetType#PREF_HELIUM}
 */
@Data
@ToString
@NoArgsConstructor
@Document("k_app_lego_widget_pref_helium")
public class WidgetPrefHeliumData implements IWidget {

    /** 物理主键 */
    @Id
    private String id;

    /** {@link LegoBlock#getId()} */
    @Indexed
    private String blockId;

    /** 电台 ID */
    private Integer siteId;

    /**
     * {@link DummyFantasy}
     */
    private String style;

    /** 图片 url. */
    private String imageUrl;

    /** 图片格式. {@link KbImageFormat} */
    private String imageFormat;

    /** 主标题 */
    private String title;

    /** 副标题 */
    private String subtitle;

    /** 更新时间, 单位毫秒 */
    private Long updateTime;

    /** 创建时间, 单位毫秒 */
    private Long createTime;

    @Override
    public WidgetType getType() {
        return WidgetType.PREF_HELIUM;
    }
}
