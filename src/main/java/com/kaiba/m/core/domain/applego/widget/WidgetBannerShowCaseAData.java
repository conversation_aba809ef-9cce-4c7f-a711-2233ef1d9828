package com.kaiba.m.core.domain.applego.widget;

import com.kaiba.lib.base.constant.applego.WidgetType;
import com.kaiba.lib.base.domain.applego.IWidget;
import com.kaiba.lib.base.domain.appwidget.banner.BannerInstanceModel;
import com.kaiba.lib.base.domain.common.ActionLink;
import com.kaiba.m.core.domain.applego.LegoBlock;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * author: lyux
 * date: 2025-01-11
 *
 * 组件化控件数据 - banner 橱窗.
 * {@link WidgetType#BANNER_SHOW_CASE_A}
 */
@Data
@ToString
@NoArgsConstructor
@Document("k_app_lego_widget_banner_show_case_a")
public class WidgetBannerShowCaseAData implements IWidget {

    /** 物理主键 */
    @Id
    private String id;

    /** {@link LegoBlock#getId()} */
    @Indexed
    private String blockId;

    /** 橱窗绑定的轮播图数据源. {@link BannerInstanceModel#getKey()} */
    private String bannerLeft;

    /** 橱窗绑定的轮播图数据源的展示模式: FIXED-固定; SEQ-顺序; RANDOM-随机. **/
    private String bannerLeftMode;

    /** 橱窗绑定的轮播图数据源. {@link BannerInstanceModel#getKey()} */
    private String bannerRight;

    /** 橱窗绑定的轮播图数据源的展示模式: FIXED-固定; SEQ-顺序; RANDOM-随机. **/
    private String bannerRightMode;

    /** 顶部图标 */
    private String icon;

    /** 顶部标题 */
    private String title;

    /** 点击查看更多按钮配置 */
    private ActionLink more;

    /** 更新时间, 单位毫秒 */
    private Long updateTime;

    /** 创建时间, 单位毫秒 */
    private Long createTime;

    @Override
    public WidgetType getType() {
        return WidgetType.BANNER_SHOW_CASE_A;
    }
}
