package com.kaiba.m.core.domain.applego;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.List;

/**
 * author: lyux
 * date: 2023-05-24
 *
 * 布局单元. 一旦创建, 不许修改.
 */
@Data
@ToString
@NoArgsConstructor
@Document("k_app_lego_layout")
public class LegoLayout {

    /** 物理主键 */
    @Id
    private String id;

    /** 布局名称, 需要用户可读, 在 TAB 等场景下会被展示给用户. */
    private String name;

    /** 背景图片. */
    private String bkgImage;

    /** 背景颜色, 以#开头接6或8位16进制数, 颜色位在最前或可省略. 举例: #fff0f0f0 或 #f6f6f6. 如果同时存在背景图片, 则此项设置无效. */
    private String bkgColor;

    /** 布局中的横向布局列表. {@link LegoRow} */
    private List<String> rowIds;

    /** 创建时间, 单位毫秒 */
    private Long createTime;

}
