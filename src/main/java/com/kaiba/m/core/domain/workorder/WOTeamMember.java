package com.kaiba.m.core.domain.workorder;

import com.kaiba.lib.base.constant.workorder.WOBusiness;
import com.kaiba.lib.base.constant.workorder.WOTeamMemberState;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * author: lyux
 * date: 2023-11-07
 *
 * 工单系统, 用户组的成员.
 */
@Data
@ToString
@NoArgsConstructor
@Document(collection = "k_work_order_team_member")
public class WOTeamMember {

    @Id
    private String id;

    /** {@link WOTeam#getId()} */
    @Indexed
    private String teamId;

    /** 业务标识, 冗余字段. {@link WOBusiness} */
    private String biz;

    /** 关联的用户 id */
    @Indexed
    private Integer userId;

    /** 组内联系方式, 也用来关联待注册用户. 敏感信息. */
    @Indexed
    private String mobile;

    /** {@link WOTeamMemberState} */
    private String state;

    /** 组内职位 */
    private String title;

    /** 组内昵称 */
    private String nickName;

    /** 真实姓名 */
    private String realName;

    /** 备注 */
    private String remark;

    /** 更新时间, 单位毫秒. */
    private Long updateTime;

    /** 创建时间, 单位毫秒. */
    private Long createTime;

}
