package com.kaiba.m.core.domain;

import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.Date;

/**
 * Created by shenxl on 2021/12/16
 */
@Data
@NoArgsConstructor
@Document(collection = "k_android_error_record")
public class AndroidErrorRecord {
    @Id
    private String id;

    @Indexed
    private Integer userId;

    /** 创建时间. 时间戳, 单位毫秒 */
    private Long createTime;

    @Indexed(expireAfterSeconds = 60)
    private Date expireTime;

    /** 异常发生时间. 时间戳, 单位毫秒 */
    @Indexed
    private Long happenTime;

    /** app版本号 */
    @Indexed
    private Integer vc;

    /** 异常类型 */
    @Indexed
    private String type;

    /** 异常信息 */
    @Indexed
    private String message;

    /** 堆栈 */
    private String trace;

    /** app最大内存，单位byte */
    private Long maxAppMemory;

    /** app可用内存，单位byte */
    private Long freeAppMemory;

    /** 系统总内存，单位byte */
    private Long totalSystemMemory;

    /** 系统可用内存，单位byte */
    private Long freeSystemMemory;

    /** 系统总存储空间，单位byte */
    private Long totalStorage;

    /** 系统可用存储空间，单位byte */
    private Long freeStorage;

    /** 报错时所处的activity */
    private String currentActivity;

    /** 是否是测试包 */
    private Boolean isDebug;

    /** 操作系统版本号 */
    private String dv;

    /** 手机品牌型号 */
    private String db;

    /** 端类型 */
    private String ep;

    /** 设备类型 */
    private String cid;

}