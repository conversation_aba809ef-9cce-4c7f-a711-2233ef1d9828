package com.kaiba.m.core.domain.applego;

import com.kaiba.lib.base.constant.applego.WidgetType;
import com.kaiba.lib.base.constant.common.KbAlign;
import com.kaiba.lib.base.domain.appactionholder.ActionHolderModel;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.Map;

/**
 * author: lyux
 * date: 2023-05-24
 *
 * 布局块. 一旦创建, 不许修改.
 * 组件化行内元素布局. 所有 宽/高/边距 单位均为相对单位, 1 单位高度表示屏幕宽度的 1/1440 .
 */
@Data
@ToString
@NoArgsConstructor
@Document("k_app_lego_block")
public class LegoBlock {

    /** 物理主键 */
    @Id
    private String id;

    /** 宽 */
    private Integer width;

    /** 高, 正值表示指定高, 负值表示高度无限 (即支持无限下拉加载), null 表示客户端自适应. */
    private Integer height;

    /** 内部的控件的对齐方式. {@link KbAlign} */
    private Integer align;

    /** 外边距 top */
    private Integer marginTop;

    /** 外边距 bottom */
    private Integer marginBottom;

    /** 外边距 left */
    private Integer marginLeft;

    /** 外边距 right */
    private Integer marginRight;

    /** 内边距 top */
    private Integer paddingTop;

    /** 内边距 bottom */
    private Integer paddingBottom;

    /** 内边距 left */
    private Integer paddingLeft;

    /** 内边距 right */
    private Integer paddingRight;

    /** 背景图片. */
    private String bkgImage;

    /** 背景颜色, 以#开头接6或8位16进制数, 颜色位在最前或可省略. 举例: #fff0f0f0 或 #f6f6f6. 如果同时存在背景图片, 则此项设置无效. */
    private String bkgColor;

    /**
     * action 的引用. {@link ActionHolderModel#getId()}.
     * 供管理后台配置时使用. 给到客户端时总是会将其组装为符合规范的 action, 所以客户端应忽略该字段.
     * 当与 {@link #action} 同时存在时, 本字段优先生效.
     */
    private String actionHolderId;

    /** 指定消息点击时的跳转页面. */
    private String action;

    /** 指定消息点击时的跳转页面, 参数. */
    private Map<String, Object> actionParams;

    /** 控件类型. {@link WidgetType} */
    private String widgetType;

    /** 创建时间, 单位毫秒 */
    private Long createTime;

}
