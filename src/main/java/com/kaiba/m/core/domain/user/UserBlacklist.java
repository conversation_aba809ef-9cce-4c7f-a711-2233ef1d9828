package com.kaiba.m.core.domain.user;

import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * 用户黑名单
 *
 * <AUTHOR>
 * @date 2022/03/07 17:30
 **/
@Data
@NoArgsConstructor
@Document(collection = "k_user_blacklist")
public class UserBlacklist {

    @Id
    private String id;

    /** 本条黑名单数据属于哪个用户 */
    @Indexed
    private Integer uid;

    /** 本条黑名单数据针对哪个用户 */
    @Indexed
    private Integer bid;

    /** 更新时间 */
    private Long updateTime;

}
