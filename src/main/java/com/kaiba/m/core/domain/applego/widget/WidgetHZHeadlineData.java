package com.kaiba.m.core.domain.applego.widget;

import com.kaiba.lib.base.constant.applego.WidgetType;
import com.kaiba.lib.base.constant.common.DummyFantasy;
import com.kaiba.lib.base.domain.actmix.ActMixTabModel;
import com.kaiba.lib.base.domain.applego.IWidget;
import com.kaiba.m.core.domain.applego.LegoBlock;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * author: lyux
 * date: 2023-05-24
 *
 * 组件化控件数据 - 杭州首页要闻词条滚动器.
 * {@link WidgetType#HZ_HEADLINE}
 */
@Data
@ToString
@NoArgsConstructor
@Document("k_app_lego_widget_hz_headline")
public class WidgetHZHeadlineData implements IWidget {

    /** 物理主键 */
    @Id
    private String id;

    /** {@link LegoBlock#getId()} */
    @Indexed
    private String blockId;

    /** 电台 ID */
    private Integer siteId;

    /**
     * 杭州首页要闻词条滚动器样式:
     * {@link DummyFantasy#GRIFFIN}: 一行.
     * {@link DummyFantasy#PHOENIX}: 二行.
     */
    private String style;

    /** 更新时间, 单位毫秒 */
    private Long updateTime;

    /** 创建时间, 单位毫秒 */
    private Long createTime;

    @Override
    public WidgetType getType() {
        return WidgetType.HZ_HEADLINE;
    }
}
