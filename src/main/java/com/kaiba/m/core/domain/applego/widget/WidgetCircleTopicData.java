package com.kaiba.m.core.domain.applego.widget;

import com.kaiba.lib.base.constant.applego.WidgetType;
import com.kaiba.lib.base.domain.applego.IWidget;
import com.kaiba.lib.base.domain.note.NoteThreadModel;
import com.kaiba.m.core.domain.applego.LegoBlock;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * author: lyux
 * date: 2023-05-24
 *
 * 组件化控件数据 - 圈子话题.
 * {@link WidgetType#CIRCLE_TOPIC}
 */
@Data
@ToString
@NoArgsConstructor
@Document("k_app_lego_widget_circle_topic")
public class WidgetCircleTopicData implements IWidget {

    /** 物理主键 */
    @Id
    private String id;

    /** {@link LegoBlock#getId()} */
    @Indexed
    private String blockId;

    /** 板块 ID. {@link NoteThreadModel#getId()} */
    private String threadId;

    /** 更新时间, 单位毫秒 */
    private Long updateTime;

    /** 创建时间, 单位毫秒 */
    private Long createTime;

    @Override
    public WidgetType getType() {
        return WidgetType.CIRCLE_TOPIC;
    }
}
