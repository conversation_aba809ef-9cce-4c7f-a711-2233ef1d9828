package com.kaiba.m.core.domain.news.channel;

import com.kaiba.lib.base.constant.news.NewsChannel;
import com.kaiba.lib.base.constant.news.article.ChannelArticleListStyle;
import com.kaiba.lib.base.constant.news.article.ChannelProgrammeListStyle;
import com.kaiba.lib.base.domain.common.ShareModel;
import com.kaiba.lib.base.domain.news.pool.bygroup.GroupTopicTab;
import com.kaiba.m.core.domain.news.pool.topic.TopicLayout;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.List;

/**
 * author: lyux
 * date: 2024-08-08
 *
 * 频道主页.
 */
@Data
@ToString
@NoArgsConstructor
@Document(collection = "k_news_channel_main_page")
public class ChannelMainPage {

    @Id
    private String id;

    /** 频道标识. {@link NewsChannel} */
    @Indexed(unique = true)
    private String channelKey;

    /** 实例标题 */
    private String title;

    /** 顶部布局. {@link TopicLayout#getId()} */
    private String layoutId;

    /** 栏目列表样式类型. {@link ChannelProgrammeListStyle} */
    private String programmeListStyle;

    /** 全部文章列表样式类型. {@link ChannelArticleListStyle} */
    private String articleListStyle;

    /** tab 分组标识属性. */
    private List<GroupTopicTab> tabs;

    /** 分享配置 */
    private ShareModel share;

    /** 创建时间, 单位毫秒. 设置后不可修改. */
    private Long createTime;

    /** 更新时间, 单位毫秒. */
    private Long updateTime;

}
