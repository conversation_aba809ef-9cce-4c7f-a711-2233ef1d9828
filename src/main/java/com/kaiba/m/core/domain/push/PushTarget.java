package com.kaiba.m.core.domain.push;

import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

@Data
@NoArgsConstructor
@Document(collection = "k_push_target")
public class PushTarget {

    @Id
    private String id;

    @Indexed
    private Integer userId;

    @Indexed
    private Integer siteId;

    @Indexed
    private Integer source;

    private String pushToken;

    //设备唯一标识，ios每次重装应用会改变
    private String deviceNumber;

    @Indexed
    private String deviceType;

    /**
     * 集成了 JPush SDK 的应用程序在第一次成功注册到 JPush 服务器时，JPush 服务器会给客户端返回一个唯一的该设备的标识 - RegistrationID
     * https://docs.jiguang.cn/jpush/client/Android/android_api#%E8%8E%B7%E5%8F%96-registrationid-api
     */
    private String jPushRegId;

    /** 极光 SDK 版本号, 仅用于故障排查 */
    private String jPushVersion;

    /** 极光 Core SDK 版本号, 仅用于故障排查 */
    private String jPushCoreVersion;

    /** 用户推送开关状态, 仅用于故障排查. 0-关闭; 1-打开 */
    private Integer devicePushConfig;

    /** {@link com.kaiba.lib.base.constant.KbEndpoint} */
    @Indexed
    private Integer endpoint;

    private Long updateTime;

    /** 推送渠道 {@link com.kaiba.lib.base.constant.KbPushChannel} */
    private Integer pushChannel;

    /** 推送渠道用户标识历史版本快照 */
    private String pushTokenSnapshot;

}
