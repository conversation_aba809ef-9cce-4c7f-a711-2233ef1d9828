package com.kaiba.m.core.domain.tmuyun;

import com.kaiba.lib.base.constant.tmuyun.EventPositionState;
import com.kaiba.lib.base.constant.tmuyun.TmuyunRefType;

import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * <AUTHOR>
 * @date 2022/08/02 9:45
 **/
@Data
@ToString
@NoArgsConstructor
@Document(collection = "k_tmuyun_event_position")
public class EventPosition {

    @Id
    private String id;

    /** 事件点位. 逻辑主键. */
    private String position;

    /** 事件类型. {@link TmuyunRefType} */
    private Integer type;

    /** 点位状态. {@link EventPositionState} */
    private Integer state;

    private Integer siteId;

    /** 点位描述 */
    private String description;

    /** 创建时间 */
    private Long updateTime;

    /** 修改时间 */
    private Long createTime;
}
