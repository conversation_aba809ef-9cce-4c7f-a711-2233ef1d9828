package com.kaiba.m.core.domain.program;

import com.kaiba.lib.base.domain.program.TopicContent;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.List;

/**
 * author wangsj
 * date 2020-09-01
 *
 * 话题:
 * 从属于排班实例. {@link ScheduleInstance}
 */
@Data
@ToString
@NoArgsConstructor
@Document(collection = "k_program_topic")
public class Topic {

    @Id
    private String id;

    /** 电台 id */
    @Indexed
    private Integer siteId;

    /** 节目 id. {@link Program} */
    @Indexed
    private String programId;

    /** 排班 id. {@link ScheduleInstance} */
    @Indexed
    private String scheduleId;

    /** 话题内容 */
    private List<TopicContent> contents;

    /** 话题标题 */
    private String title;

    /** 将一组话题关联在一起的标识. 比如某个广告要在一组排班实例中展示, 则将需要展示广告的话题赋予同一标识. */
    private String adId;

    /** 创建时间, 单位秒 */
    private Long createTime;

    /** 创建者 */
    private Integer createUser;

    /** 话题是否启用, 0 - 未启用, 1 - 启用. 一个排班实例下只能有一个启用状态的话题. */
    private Integer active;

    /** 上传省宣标题 */
    private String scheduleName;
}
