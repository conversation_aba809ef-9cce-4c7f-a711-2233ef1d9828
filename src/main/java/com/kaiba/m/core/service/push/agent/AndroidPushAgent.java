package com.kaiba.m.core.service.push.agent;

import cn.jiguang.common.ClientConfig;
import cn.jiguang.common.resp.APIConnectionException;
import cn.jiguang.common.resp.APIRequestException;
import cn.jpush.api.JPushClient;
import cn.jpush.api.push.PushResult;
import cn.jpush.api.push.model.Options;
import cn.jpush.api.push.model.Platform;
import cn.jpush.api.push.model.PushPayload;
import cn.jpush.api.push.model.audience.Audience;
import cn.jpush.api.push.model.audience.AudienceTarget;
import cn.jpush.api.push.model.audience.AudienceType;
import cn.jpush.api.push.model.notification.AndroidNotification;
import cn.jpush.api.push.model.notification.Notification;

import cn.jpush.api.schedule.ScheduleResult;
import com.google.common.collect.Maps;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.kaiba.lib.base.constant.push.PushRange;
import com.kaiba.lib.base.domain.push.PushModel;
import com.kaiba.lib.base.response.KbCode;
import com.kaiba.lib.base.response.KbException;
import com.kaiba.lib.base.util.GsonUtils;
import com.kaiba.m.core.domain.push.PushTarget;
import com.kaiba.m.core.repository.push.PushRepository;
import com.kaiba.m.core.util.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.*;
import java.util.concurrent.Executor;
import java.util.concurrent.Executors;

/**
 * author: lyux
 * date: 18-12-7
 */
@Slf4j
@Component
public class AndroidPushAgent {
    @Value("${kaiba.jiguang.app_key}")
    private String APP_KEY;
    @Value("${kaiba.jiguang.master_secret}")
    private String MASTER_SECRET;

    @Value("${kaiba.push.dev:true}")
    private boolean isDev;

    private final Executor executor = Executors.newSingleThreadExecutor();
    private final PushRepository pushRepository;
    private JPushClient pushClient;

    public AndroidPushAgent(PushRepository pushRepository) {
        this.pushRepository = pushRepository;
    }

    @PostConstruct
    public void init() {
        this.pushClient = new JPushClient(MASTER_SECRET, APP_KEY, null, ClientConfig.getInstance());
    }

    public void push(PushModel model, Collection<PushTarget> pushTarget) {
        PushRange range = PushRange.valueOf(model.getRange()).orElseThrow(
                () -> new KbException(KbCode.ILLEGAL_ARGUMENT, "unknown range: " + model.getRange()));
        List<AudienceTarget> audienceTargets = new ArrayList<>();
        switch (range) {
            case ALL:
                audienceTargets.add(AudienceTarget.tag_not("disable"));
                break;
            case SOURCE:
                int source = model.targetIdAsInteger(0);
                audienceTargets.add(AudienceTarget.tag_and("source_" + source));
                audienceTargets.add(AudienceTarget.tag_not("disable"));
                break;
            case SITE:
                int siteId = model.targetIdAsInteger(0);
                audienceTargets.add(AudienceTarget.tag_and("siteId_" + siteId));
                audienceTargets.add(AudienceTarget.tag_not("disable"));
                break;
            case USER:
            case CUSTOM:
                if (pushTarget.isEmpty()) {
                    log.warn("push target is empty : " + model);
                    return;
                }
                List<String> alias = new LinkedList<>();
                for (PushTarget target : pushTarget) {
                    alias.add(target.getPushToken());
                }
                alias.removeIf(Objects::isNull);
                if (alias.isEmpty()) {
                    log.warn("push alias is empty : " + model);
                    return;
                }
                audienceTargets.add(AudienceTarget.alias(alias));
                audienceTargets.add(AudienceTarget.tag_not("disable"));
                break;
            default:
                log.error("push target type is unsupported : " + model.getType());
                return;
        }
        List<AudienceTarget> assembleTag = fillDevTag(audienceTargets);
        Audience audience = buildAudience(assembleTag);

        Map<String, String> extra = new HashMap<>();
        extra.put("type", "6");//此处主要为了兼容老版本推送的type设置，后续取消掉
        extra.put("action", model.getAction());
        if (model.getActionParams() != null && !model.getActionParams().isEmpty()){
            extra.put("actionParams", JsonUtils.getGson().toJson(model.getActionParams()));
        }
        AndroidNotification notification = AndroidNotification.newBuilder()
                .setTitle(model.getTitle())
                .setAlert(model.getSubTitle())
                .setAlertType(7)//二进制表示声音/震动/呼吸灯都启用 7=0111
                .addExtras(extra)
                .setIntent(assembleIntent())
                .addCustom("uri_activity", "com.fm1031.app.act.common.PushIntentActivity")
                .addCustom("uri_action", "kaiba.android.PushIntentActivity")
                .build();
        PushPayload payload = PushPayload.newBuilder()
                .setPlatform(Platform.android())
                .setAudience(audience)
                .setOptions(thirdPartyChannelOptions())
                .setNotification(Notification.newBuilder().addPlatformNotification(notification).build())
                .build();
        //20241008 关闭极光推送api调用
//        executor.execute(()->{
//            try {
//                PushResult result = pushClient.sendPush(payload);
//                log.info("push result : " + JsonUtils.getGson().toJson(result));
//                if (result.isResultOK()) {
//                    pushRepository.updateJPushMsgId(model.getId(), result.msg_id);
//                } else if (result.error != null) {
//                    // 极光推送接口返回值文档:
//                    // https://docs.jiguang.cn/jpush/server/push/rest_api_v3_push#%E8%B0%83%E7%94%A8%E8%BF%94%E5%9B%9E
//                    if (result.error.getCode() == 2002) {
//                        // 极光文档: API 调用频率超出该应用的限制
//                        int secondToReset = Math.min(result.getRateLimitReset(), 60);
//                        Thread.sleep(secondToReset * 1000L);
//                        Thread.currentThread().interrupt();
//                    }
//                }
//            } catch (APIConnectionException | APIRequestException e) {
//                log.error("jPush error", e);
//            } catch (InterruptedException e) {
//                log.error("push interrupted", e);
//            }
//        });
    }

    private static final String STRING_ACTION = "kaiba.android.PushIntentActivity";
    private static final String STRING_APP = "com.hz.czfw.app";
    private static final String STRING_ACTIVITY = "com.fm1031.app.act.common.PushIntentActivity";
    private JsonObject assembleIntent() {
        JsonObject jsonObject = new JsonObject();
        String sb = "intent:#Intent;action=" + STRING_ACTION + ";component=" + STRING_APP +
            "/" + STRING_ACTIVITY + ";end";
        jsonObject.addProperty("url", sb);
        return jsonObject;
    }

    private Options thirdPartyChannelOptions() {
        final Map<String, String> distribution = Maps.newHashMapWithExpectedSize(4);
        distribution.put("distribution", "first_ospush");

        Map<String, Map<String, String>> channel = Maps.newHashMapWithExpectedSize(8);
        channel.put("xiaomi", distribution);
        channel.put("huawei", distribution);
        channel.put("honor", distribution);
        channel.put("meizu", distribution);
        channel.put("oppo", distribution);
        channel.put("vivo", distribution);
        channel.put("fcm", distribution);
        return Options.newBuilder()
            .setThirdPartyChannel(channel)
            .build();
    }

    private List<AudienceTarget> fillDevTag(List<AudienceTarget> targets) {
        if (!isDev) {
            return targets;
        }
        //忽略原有的tag_and标签,使用siteId_154作为测试标签,覆盖原有的tag_and,只使用测试标签
        //tag_and标签的内部逻辑为and, tag标签的内部逻辑为or
        List<AudienceTarget> newTargets = new ArrayList<>();
        for (AudienceTarget target : targets) {
            if (target.getAudienceType() != AudienceType.TAG_AND) {
                newTargets.add(target);
            }
        }
        newTargets.add(AudienceTarget.tag_and("siteId_154"));
        return newTargets;
    }

    private Audience buildAudience(List<AudienceTarget> targets) {
        Audience.Builder builder = new Audience.Builder();
        for (AudienceTarget audienceTarget : targets) {
            builder.addAudienceTarget(audienceTarget);
        }
        return builder.build();
    }
}
