package com.kaiba.m.core.service.workorder.search;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/06/9 11:33
 **/
@Data
@ToString
@NoArgsConstructor
public class SearchResultModel {
    private String status;
    private ResultInfo result;

    @Data
    @ToString
    public static class ResultInfo {
        private Integer total;
        private Integer num;
        private List<ResultItem> items;
    }

    @Data
    @ToString
    public static class ResultItem {
        private WOOpenSearchDocument fields;
    }
}
