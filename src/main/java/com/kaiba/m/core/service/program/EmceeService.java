package com.kaiba.m.core.service.program;

import com.google.common.collect.Sets;
import com.kaiba.lib.base.constant.Values;
import com.kaiba.lib.base.response.KbCode;
import com.kaiba.lib.base.response.KbException;
import com.kaiba.m.core.domain.program.Emcee;
import com.kaiba.m.core.repository.program.EmceeRepository;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;

/**
 * author wangsj
 * date 2020-09-01
 */
@Service
public class EmceeService {

    private final EmceeRepository emceeRepository;
    private final EmceeCacheService emceeCacheService;

    public EmceeService(EmceeRepository emceeRepository, EmceeCacheService emceeCacheService) {
        this.emceeRepository = emceeRepository;
        this.emceeCacheService = emceeCacheService;
    }

    public Emcee createEmcee(Emcee emcee) {
        if (emcee.getReserved().equals(1)) {
            emceeRepository.findFirstBySiteIdAndReserved(emcee.getSiteId(), 1).ifPresent(t -> {
                if (!t.getId().equals(emcee.getId())) {
                    throw new KbException(KbCode.REQUEST_PARAM_INVALID).li().setReadableMessage("已经存在空白档主持人");
                }
            });
        }

        long currentTime = System.currentTimeMillis() / 1000;
        emcee.setCreateTime(currentTime);
        emcee.setUpdateTime(currentTime);
        emcee.setActive(emcee.getUserId() != null ? Values.TRUE : Values.FALSE);
        emceeRepository.insert(emcee);
        emceeCacheService.setEmcee(emcee);
        return emcee;
    }

    public Optional<Emcee> getReservedEmceeBySiteId(Integer siteId) {
        return emceeRepository.findFirstBySiteIdAndReserved(siteId, 1);
    }

    public Emcee updateEmcee(Emcee emcee) {
        Emcee updatedEmcee = emceeRepository.updateEmcee(emcee);
        emceeCacheService.setEmcee(updatedEmcee);
        return updatedEmcee;
    }

    public Emcee updateEmceeAlbumId(String emceeId, String albumId) {
        Emcee emcee = emceeRepository.updateAlbumId(emceeId, albumId);
        emceeCacheService.setEmcee(emcee);
        return emcee;
    }

    public Emcee updateEmceeActive(String emceeId, boolean active, Integer userId, Integer operator) {
        Emcee emcee = emceeRepository.updateActive(emceeId, active, userId, operator);
        emceeCacheService.setEmcee(emcee);
        return emcee;
    }

    public Emcee updateEmceeAccountId(String emceeId, Long accountId) {
        Emcee emcee = emceeRepository.updateAccountId(emceeId, accountId);
        emceeCacheService.setEmcee(emcee);
        return emcee;
    }

    public Emcee removeEmcee(String emceeId, Integer operatorId) {
        Emcee emcee = emceeRepository.updateActive(emceeId, false, null, operatorId);
        if (emcee != null) {
            emceeCacheService.deleteEmcee(emceeId);
        }
        return emcee;
    }

    public Optional<Emcee> getEmceeById(String emceeId) {
        return getEmceeById(emceeId, true);
    }

    public Optional<Emcee> getEmceeById(String emceeId, boolean cached) {
        if (cached) {
            Optional<Emcee> cache = emceeCacheService.getEmceeById(emceeId);
            if (cache.isPresent()) {
                return cache;
            }
        }
        Optional<Emcee> optional = emceeRepository.findById(emceeId);
        optional.ifPresent(emceeCacheService::setEmcee);
        return optional;
    }

    public Optional<Emcee> getFirstEmceeByUserId(Integer userId, Integer siteId) {
        if (siteId == null) {
            return emceeRepository.findFirstByUserId(userId);
        } else {
            return emceeRepository.findFirstByUserIdAndSiteId(userId, siteId);
        }
    }

    public Optional<Emcee> getFirstEmceeBySiteId(Integer siteId) {
        return emceeRepository.findFirstBySiteIdAndActive(siteId, Values.TRUE);
    }

    public List<Emcee> getEmceeListByUserIdIn(Integer[] userIds, Integer siteId) {
        if (siteId == null) {
            return emceeRepository.findByUserIdIn(userIds);
        } else {
            return emceeRepository.findByUserIdInAndSiteId(userIds, siteId);
        }
    }

    public Map<String, Emcee> getEmceeMapByIdIn(Set<String> emceeIds) {
        Map<String, Emcee> emceeMap = emceeCacheService.getEmceeByIdIn(emceeIds);
        if (emceeIds.size() == emceeMap.size()) {
            return emceeMap;
        }
        Set<String> differentSet = Sets.difference(emceeIds, emceeMap.keySet());
        Iterable<Emcee> differentEmcee = emceeRepository.findAllById(differentSet);
        for (Emcee emcee : differentEmcee) {
            emceeMap.put(emcee.getId(), emcee);
        }
        emceeCacheService.setEmceeList(differentEmcee);
        return emceeMap;
    }

    public Page<Emcee> getActiveEmceePageBySiteId(Integer siteId, Integer page, Integer pageSize) {
        int p = page == null ? 0 : page - 1;
        int ps = pageSize == null ? 15 : pageSize;
        return emceeRepository.findBySiteIdAndActive(siteId, Values.TRUE, PageRequest.of(p, ps));
    }

    public Page<Emcee> getEmceeListBySiteId(Integer siteId, Integer page, Integer pageSize) {
        int p = page == null ? 0 : page - 1;
        int ps = pageSize == null ? 15 : pageSize;
        return emceeRepository.findBySiteId(
                siteId, PageRequest.of(p, ps, Sort.by(Sort.Direction.DESC, "active")));
    }

}
