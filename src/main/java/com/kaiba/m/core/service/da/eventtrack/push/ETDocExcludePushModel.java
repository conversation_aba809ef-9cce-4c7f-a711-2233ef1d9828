package com.kaiba.m.core.service.da.eventtrack.push;

import com.kaiba.lib.base.constant.KbModule;
import lombok.Data;

/**
 * <AUTHOR>
 * @version ETDocExcludePushModel, v0.1 2025/4/11 15:30 daopei Exp $
 **/
@Data
public class ETDocExcludePushModel {

    private String id;
    /** 业务类型. {@link KbModule}. 非空. */
    private String biz;
    /** 业务单元. 用以保持稿件定义的同构性: 同一个业务单元下的 ref 含义和层级结构总是一致的. 可为空. */
    private String unit;
    /** 对象标识1. 非空. */
    private String ref1;
    /** 对象标识2. 可为空. */
    private String ref2;
    /** 对象标识3. 可为空. */
    private String ref3;
    /** 站点 ID. 可为空. */
    private String siteId;
    /** 稿件标识, 拼接非空字段得到: biz-unit-ref1-ref2-ref3-siteId */
    private String docId;
    /** 稿件标题. 非必填. */
    private String title;
    /** 创建时间 */
    private String createTime;
    /** 修改时间 */
    private String updateTime;
}
