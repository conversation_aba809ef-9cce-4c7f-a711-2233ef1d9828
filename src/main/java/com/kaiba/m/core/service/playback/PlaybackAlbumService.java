package com.kaiba.m.core.service.playback;

import com.kaiba.lib.base.constant.playback.PlaybackAlbumType;
import com.kaiba.lib.base.response.KbCode;
import com.kaiba.lib.base.response.KbException;
import com.kaiba.lib.base.util.StringUtils;
import com.kaiba.m.core.domain.playback.PlaybackAlbum;
import com.kaiba.m.core.repository.playback.PlaybackAlbumRepository;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2019/7/31
 */
@Service
public class PlaybackAlbumService {

    private final PlaybackAlbumRepository playbackAlbumRepository;

    public PlaybackAlbumService(PlaybackAlbumRepository playbackAlbumRepository) {
        this.playbackAlbumRepository = playbackAlbumRepository;
    }

    public PlaybackAlbum createAlbum(PlaybackAlbum album) {
        album.setCreateTime(System.currentTimeMillis() / 1000);
        verifyAlbum(album);
        return playbackAlbumRepository.save(album);
    }

    public PlaybackAlbum updateAlbum(PlaybackAlbum album) {
        verifyAlbum(album);
        return playbackAlbumRepository.save(album);
    }

    public void updateAlbumSubTitle(String albumId, String subTitle) {
         playbackAlbumRepository.updateSubTitle(albumId, subTitle);
    }

    public Optional<PlaybackAlbum> getAlbumById(String albumId) {
        return playbackAlbumRepository.findById(albumId);
    }

    public Page<PlaybackAlbum> getAlbumList(Integer siteId, PlaybackAlbumType type, Integer page, Integer pageSize){
        int p = page == null ? 0 : page - 1;
        int ps = pageSize == null ? 10 : pageSize;
        Pageable pageable = PageRequest.of(p, ps);
        if (siteId == null) {
            if (null == type) {
                return playbackAlbumRepository.findAllByOrderByTypeAscCreateTimeDesc(pageable);
            } else {
                return playbackAlbumRepository.findAllByTypeOrderByCreateTimeDesc(type.getValue(), pageable);
            }
        } else {
            if (null == type) {
                return playbackAlbumRepository.findAllBySiteIdOrderByCreateTimeDesc(siteId, pageable);
            } else {
                return playbackAlbumRepository.findAllBySiteIdAndTypeOrderByCreateTimeDesc(siteId, type.getValue(), pageable);
            }
        }
    }

    public Page<PlaybackAlbum> getMonthHotAlbumList(Integer siteId,Integer type,Integer page, Integer pageSize){
        int p = page == null ? 0 : page - 1;
        int ps = pageSize == null ? 10 : pageSize;
        if(siteId == null){
            if(null == type){
                return playbackAlbumRepository.findAllByOrderByMonthPlayCountDesc(PageRequest.of(p, ps));
            }else {
                return playbackAlbumRepository.findAllByTypeOrderByMonthPlayCountDesc(type,PageRequest.of(p, ps));
            }
        }else {
            if(null == type){
                return playbackAlbumRepository.findAllByOrderByMonthPlayCountDesc(PageRequest.of(p, ps));
            }else {
                return playbackAlbumRepository.findAllBySiteIdAndTypeOrderByMonthPlayCountDesc(siteId,type, PageRequest.of(p, ps));
            }
        }
    }

    public Page<PlaybackAlbum> getHotAlbumList(Integer siteId,Integer type,Integer page, Integer pageSize){
        int p = page == null ? 0 : page - 1;
        int ps = pageSize == null ? 10 : pageSize;
        if(siteId == null){
            if(null == type){
                return playbackAlbumRepository.findAllByOrderByPlayCountDesc(PageRequest.of(p, ps));
            }else {
                return playbackAlbumRepository.findAllByTypeOrderByPlayCountDesc(type,PageRequest.of(p, ps));
            }
        }else {
            if(null == type){
                return playbackAlbumRepository.findAllByOrderByPlayCountDesc(PageRequest.of(p, ps));
            }else {
                return playbackAlbumRepository.findAllBySiteIdAndTypeOrderByPlayCountDesc(siteId,type, PageRequest.of(p, ps));
            }
        }
    }

    public Page<PlaybackAlbum> getSharedAlbumList(Integer siteId,Boolean isShare,Integer page, Integer pageSize){
        int p = page == null ? 0 : page - 1;
        int ps = pageSize == null ? 10 : pageSize;
        return playbackAlbumRepository.getSharedList(siteId,isShare, PageRequest.of(p, ps));
    }

    public void albumUpdateIsShare(String albumId, Boolean isShare) {
        playbackAlbumRepository.updateIsShare(albumId, isShare);
    }

    void albumUpdatePlayCount(String albumId, Integer playCount) {
        playbackAlbumRepository.updatePlayCount(albumId, playCount);
    }

    void albumUpdateMonthPlayCount(String albumId, Integer monthPlayCount) {
        playbackAlbumRepository.updateMonthPlayCount(albumId, monthPlayCount);
    }

    public void albumUpdateViewCount(String albumId, Integer viewCount) {
        playbackAlbumRepository.updateViewCount(albumId, viewCount);
    }

    void albumUpdatePlaybackCount(String albumId, Integer playbackCount) {
        playbackAlbumRepository.updatePlaybackCount(albumId, playbackCount);
    }

    private void verifyAlbum(PlaybackAlbum album) {
        if (album.getSiteId() == null) {
            throw new KbException(KbCode.ILLEGAL_ARGUMENT, "siteId null").li();
        }
        if (StringUtils.isEmpty(album.getCover())) {
            throw new KbException(KbCode.ILLEGAL_ARGUMENT, "cover null").li();
        }
        if (StringUtils.isEmpty(album.getTitle().trim())) {
            throw new KbException(KbCode.ILLEGAL_ARGUMENT, "title empty").li();
        }
        album.setTitle(album.getTitle().trim());
    }

}
