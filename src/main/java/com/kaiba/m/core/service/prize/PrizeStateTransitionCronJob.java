package com.kaiba.m.core.service.prize;

import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * author: lyux
 * date: 20-1-11
 */
@Slf4j
@Component
public class PrizeStateTransitionCronJob {

    private final PrizeService prizeService;

    public PrizeStateTransitionCronJob(PrizeService prizeService) {
        this.prizeService = prizeService;
    }

    // 每天 01:05 执行
    @XxlJob("user-prize-check-expired")
    public ReturnT<String> checkAndUpdateExpired(String param) {
        long count = prizeService.checkAndUpdateExpiredPrize();
        log.info("check prize state transition, done. updated: " + count);
        return ReturnT.SUCCESS;
    }

}
