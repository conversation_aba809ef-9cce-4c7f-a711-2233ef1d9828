package com.kaiba.m.core.service.news.legacy;

import com.kaiba.m.core.domain.news.legacy.News;
import com.kaiba.m.core.domain.news.legacy.SiteNewsThread;
import com.kaiba.m.core.util.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.connection.StringRedisConnection;
import org.springframework.data.redis.core.RedisCallback;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.util.*;

@Slf4j
@Service
public class NewsCacheService {

    private final StringRedisTemplate stringRedisTemplate;

    private final static Duration REFRESH_TIME = Duration.ofMinutes(3);
    private final static Duration EXPIRE_TIME = Duration.ofDays(3);
    private final static String SORT_PREFIXED = "list_sort_";
    private final static String SIMPLE_NEWS_PREFIXED = "today_news_";
    private final static String NEWS_SITE_THREAD_PREFIXED = "news_site_thread_";
    private final static String NEWS_SITE_TYPE_URL_PREFIXED = "java_cache_core_news_site_type_url_";
    private static final String CACHE_KEY_LOCK = "java_lock_core_news_site_site_type_";
    private final static List<Object> SIMPLE_FIELDS = new ArrayList<>();
    private final static List<Object> NEWS_SITE_THREAD_FIELDS = new ArrayList<>();
    private final static Map<String, Integer> SIMPLE_FIELDS_MAP = new HashMap<>();
    private final static Map<String, Integer> THREAD_FIELDS_MAP = new HashMap<>();

    static {
        SIMPLE_FIELDS.add("id");
        SIMPLE_FIELDS.add("title");
        SIMPLE_FIELDS.add("siteId");
        SIMPLE_FIELDS.add("type");
        SIMPLE_FIELDS.add("wxMiniId");
        SIMPLE_FIELDS.add("wxMiniPath");
        SIMPLE_FIELDS.add("coverImg");
        SIMPLE_FIELDS.add("createTime");
        SIMPLE_FIELDS.add("signTime");
        SIMPLE_FIELDS.add("viewCount");
        SIMPLE_FIELDS.add("isTop");
        SIMPLE_FIELDS.add("isDisplay");
        for (int i = 0; i < SIMPLE_FIELDS.size(); i++) {
            SIMPLE_FIELDS_MAP.put((String) SIMPLE_FIELDS.get(i), i);
        }

        NEWS_SITE_THREAD_FIELDS.add("id");
        NEWS_SITE_THREAD_FIELDS.add("siteId");
        NEWS_SITE_THREAD_FIELDS.add("base");
        NEWS_SITE_THREAD_FIELDS.add("multiply");
        NEWS_SITE_THREAD_FIELDS.add("threadId");
        for (int i = 0; i < NEWS_SITE_THREAD_FIELDS.size(); i++) {
            THREAD_FIELDS_MAP.put((String) NEWS_SITE_THREAD_FIELDS.get(i), i);
        }
    }

    public NewsCacheService(StringRedisTemplate stringRedisTemplate) {
        this.stringRedisTemplate = stringRedisTemplate;
    }

    public void delete(Integer siteId, String id) {
        stringRedisTemplate.opsForZSet().remove(SORT_PREFIXED + siteId, id);
        stringRedisTemplate.delete(SIMPLE_NEWS_PREFIXED + id);
    }

    public void setSimpleNewsCache(News news) {
        String simpleKey = SIMPLE_NEWS_PREFIXED + news.getId();
        Map<String, String> fields = new HashMap<>();
        fields.put("id", news.getId());
        fields.put("title", news.getTitle());
        fields.put("siteId", news.getSiteId().toString());
        fields.put("type", news.getType().toString());
        fields.put("isDisplay", news.getIsDisplay() == null ? "1" : news.getIsDisplay().toString());
        fields.put("wxMiniId", news.getWxMiniId());
        fields.put("wxMiniPath", news.getWxMiniPath());
        fields.put("coverImg", JsonUtils.getGson().toJson(news.getCoverImg()));
        fields.put("createTime", news.getCreateTime().toString());
        fields.put("signTime", news.getSignTime().toString());
        fields.put("viewCount", news.getViewCount()==null? "0" : news.getViewCount().toString());
        final int baseScore;
        if (news.getIsTop() == null) {
            fields.put("isTop", "0");
            baseScore = 0;
        } else {
            fields.put("isTop", news.getIsTop().toString());
            baseScore = news.getIsTop() == 1 ? 3600 * 24 * 365 * 10 : 0;
        }
        stringRedisTemplate.executePipelined((RedisCallback<Object>) connection -> {
            StringRedisConnection conn = (StringRedisConnection) connection;
            conn.hMSet(simpleKey, fields);
            conn.expire(simpleKey, 3600 * 24 * 3);
            conn.zAdd(SORT_PREFIXED + news.getSiteId(), baseScore + news.getSignTime(), news.getId());
            //保证最新的15条数据的有效性，并且留出3条位置给资讯取消置顶的条目使用
            conn.zRemRange(SORT_PREFIXED + news.getSiteId(), -20, -18);
            return null;
        });
    }

    public void setNewsSiteThread(SiteNewsThread thread) {
        Map<String, String> fields = new HashMap<>();
        fields.put("id",thread.getId());
        fields.put("siteId", thread.getSiteId().toString());
        fields.put("base", thread.getBase().toString());
        fields.put("multiply", thread.getMultiply().toString());
        fields.put("threadId", thread.getThreadId());
        stringRedisTemplate.opsForHash().putAll(NEWS_SITE_THREAD_PREFIXED + thread.getSiteId(), fields);
    }

    public void setRecommendList(Integer siteId, Integer type, List<String> list, Boolean removeData) {
        String lockKey = CACHE_KEY_LOCK + siteId + "_" + type;
        if(!lock(lockKey, String.valueOf(System.currentTimeMillis()))) {
            return;
        }

        String cacheKey = NEWS_SITE_TYPE_URL_PREFIXED + siteId + "_" + type;
        Set<String> values = stringRedisTemplate.opsForSet().members(cacheKey);
        if(removeData && values != null && !values.isEmpty()) {
            stringRedisTemplate.opsForSet().remove(cacheKey, values.toArray());
        }
        stringRedisTemplate.opsForSet().add(cacheKey, list.toArray(new String[0]));
        stringRedisTemplate.expire(cacheKey, EXPIRE_TIME);
    }

    public SiteNewsThread getNewsSiteThread(Integer siteId) {
        List<Object> fields = stringRedisTemplate.opsForHash().multiGet(NEWS_SITE_THREAD_PREFIXED + siteId, NEWS_SITE_THREAD_FIELDS);
        String idCache = (String) fields.get(THREAD_FIELDS_MAP.get("id"));
        if (idCache != null && !idCache.isEmpty()) {
            SiteNewsThread thread = new SiteNewsThread();
            thread.setId(idCache);
            thread.setThreadId((String) fields.get(THREAD_FIELDS_MAP.get("threadId")));
            thread.setSiteId(Integer.parseInt(fields.get(THREAD_FIELDS_MAP.get("siteId")).toString()));
            thread.setBase(Integer.parseInt(fields.get(THREAD_FIELDS_MAP.get("base")).toString()));
            thread.setMultiply(Integer.parseInt(fields.get(THREAD_FIELDS_MAP.get("multiply")).toString()));
            return thread;
        } else {
            return null;
        }
    }

    public void incrViewCount(String id) {
        String keys = SIMPLE_NEWS_PREFIXED + id;
        Boolean hasKey = stringRedisTemplate.hasKey(keys);
        if (hasKey != null && hasKey) {
            stringRedisTemplate.opsForHash().increment(SIMPLE_NEWS_PREFIXED + id, "viewCount", 1);
        }
    }

    public boolean isExpired(Long expire) {
        return expire == null || expire < 0 ||
            expire + REFRESH_TIME.getSeconds() < EXPIRE_TIME.getSeconds();
    }

    public List<Object> getRecommendListBySiteIdAndType(Integer siteId, Integer type, Integer count) {
        String cacheKey = NEWS_SITE_TYPE_URL_PREFIXED + siteId + "_" + type;
        return stringRedisTemplate.executePipelined((RedisCallback<Object>) connection -> {
            StringRedisConnection conn = (StringRedisConnection) connection;
            conn.sRandMember(cacheKey, count);
            conn.ttl(cacheKey);
            return null;
        });
    }

    public Set<String> getTodaySimpleListBySiteId(Integer siteId) {
        String sortKey = SORT_PREFIXED + siteId;
        return stringRedisTemplate.opsForZSet().reverseRange(sortKey, 0, 20);
    }

    public News getSimpleNews(String id) {
        List<Object> fields = stringRedisTemplate.opsForHash().multiGet(SIMPLE_NEWS_PREFIXED + id, SIMPLE_FIELDS);
        String idCache = (String) fields.get(SIMPLE_FIELDS_MAP.get("id"));
        if (idCache != null && !idCache.isEmpty()) {
            News news = new News();
            news.setId(idCache);
            news.setTitle((String) fields.get(SIMPLE_FIELDS_MAP.get("title")));
            news.setSiteId(getIntegerFromSimpleFieldsByKey(fields, "siteId"));
            news.setType(getIntegerFromSimpleFieldsByKey(fields, "type"));
            news.setWxMiniId((String) fields.get(SIMPLE_FIELDS_MAP.get("wxMiniId")));
            news.setWxMiniPath((String) fields.get(SIMPLE_FIELDS_MAP.get("wxMiniPath")));
            news.setCoverImg(JsonUtils.toImageList((String) fields.get(SIMPLE_FIELDS_MAP.get("coverImg"))));
            news.setCreateTime(getLongFromSimpleFieldsByKey(fields, "createTime"));
            news.setSignTime(getLongFromSimpleFieldsByKey(fields, "signTime"));
            news.setViewCount(getIntegerFromSimpleFieldsByKey(fields, "viewCount"));
            news.setIsTop(getIntegerFromSimpleFieldsByKey(fields, "isTop"));
            news.setIsDisplay(getIntegerFromSimpleFieldsByKey(fields, "isDisplay"));
            return news;
        } else {
            return null;
        }
    }

    private Boolean lock(String key, String value) {
        return stringRedisTemplate.opsForValue().setIfAbsent(key, value, Duration.ofSeconds(3));
    }

    private Long getLongFromSimpleFieldsByKey(List<Object> fields, String key) {
        Object obj = fields.get(SIMPLE_FIELDS_MAP.get(key));
        if (null == obj) return null;
        try {
            return Long.parseLong(obj.toString());
        } catch (NumberFormatException e) {
            return null;
        }
    }

    private Integer getIntegerFromSimpleFieldsByKey(List<Object> fields, String key) {
        Object obj = fields.get(SIMPLE_FIELDS_MAP.get(key));
        if (null == obj) return null;
        try {
            return Integer.parseInt(obj.toString());
        } catch (NumberFormatException e) {
            return null;
        }
    }

}
