package com.kaiba.m.core.service.news.pool.topic;

import com.kaiba.lib.base.assembler.Mapper;
import com.kaiba.lib.base.constant.common.DummyFantasy;
import com.kaiba.lib.base.constant.news.article.LayoutFoldPolicy;
import com.kaiba.lib.base.domain.news.pool.topic.TopicLayoutModel;
import com.kaiba.m.core.domain.news.pool.topic.TopicLayout;

/**
 * author: lyux
 * date: 2024-09-03
 */
public class NewsTopicLayoutModelHelper {

    public static TopicLayoutModel layout2model(TopicLayout instance) {
        TopicLayoutModel model = Mapper.map(instance, TopicLayoutModel.class);
        if (model.getStyle() == null) {
            model.setStyle(DummyFantasy.CENTAUR.name());
        }
        if (model.getHeaderImage() != null && model.getHeaderStyle() == null) {
            model.setHeaderStyle(DummyFantasy.CENTAUR.name());
        }
        if (model.getBannerKey() != null && model.getBannerStyle() == null) {
            model.setBannerStyle(DummyFantasy.CENTAUR.name());
        }
        if (model.getBeltKey() != null && model.getBeltStyle() == null) {
            model.setBeltStyle(DummyFantasy.CENTAUR.name());
        }
        if (model.getIconGridKey() != null && model.getIconGridStyle() == null) {
            model.setIconGridStyle(DummyFantasy.CENTAUR.name());
        }
        if (model.getFoldPolicy() == null) {
            model.setFoldPolicy(LayoutFoldPolicy.FORBID.name());
        }
        return model;
    }

    public static TopicLayoutModel trimApiLayoutModel(TopicLayoutModel model) {
        if (model == null) {
            return null;
        }
        if (model.getBannerEnabled() != null && !model.getBannerEnabled()) {
            model.setBannerKey(null);
            model.setBannerStyle(null);
        }
        if (model.getBeltEnabled() != null && !model.getBeltEnabled()) {
            model.setBeltKey(null);
            model.setBeltStyle(null);
        }
        if (model.getIconGridEnabled() != null && !model.getIconGridEnabled()) {
            model.setIconGridKey(null);
            model.setIconGridStyle(null);
        }
        if (model.getVideoEnabled() != null && !model.getVideoEnabled()) {
            model.setVideoUrl(null);
            model.setVideoCover(null);
            model.setVideoLive(null);
            model.setVideoFormat(null);
            model.setVideoStartTime(null);
            model.setVideoEndTime(null);
        }
        if (model.getVoteEnabled() != null && !model.getVoteEnabled()) {
            model.setVoteList(null);
        }
        model.setBannerEnabled(null);
        model.setBeltEnabled(null);
        model.setIconGridEnabled(null);
        model.setVoteEnabled(null);
        model.setCreateTime(null);
        model.setUpdateTime(null);
        model.setSiteId(null);
        return model;
    }

}
