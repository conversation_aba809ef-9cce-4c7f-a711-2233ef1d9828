package com.kaiba.m.core.service.note.event.handler;

import com.kaiba.lib.base.constant.user.UserScoreConst;
import com.kaiba.lib.base.domain.note.NoteModel;
import com.kaiba.lib.base.domain.user.UserModel;
import com.kaiba.m.core.domain.note.Note;
import com.kaiba.m.core.domain.note.NoteComment;
import com.kaiba.m.core.service.note.event.INoteEventReceiver;
import com.kaiba.m.core.service.note.event.NoteCreateOrigin;
import com.kaiba.m.core.service.user.UserScoreService;
import org.springframework.core.task.TaskExecutor;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Component;

/**
 * author: lyux
 * date: 2020-04-25
 *
 * 帖子相关的积分操作
 */
@Component
public class UserScoreHandler implements INoteEventReceiver {

    private static final boolean ENABLE_PRAISE_SCORE = true;

    private final UserScoreService userScoreService;
    private final TaskExecutor taskExecutor;

    public UserScoreHandler(UserScoreService userScoreService, TaskExecutor taskExecutor) {
        this.userScoreService = userScoreService;
        this.taskExecutor = taskExecutor;
    }

    @Override
    public void onNoteCreated(@NonNull Note note, @Nullable NoteModel noteModel, UserModel owner, String reviewId, @NonNull NoteCreateOrigin origin) {
        taskExecutor.execute(() ->
                userScoreService.addScore(note.getUserId(), UserScoreConst.NOTE_POST, null));
    }

    @Override
    public void onNoteDeleted(@NonNull Note note, Integer deleteUserId, boolean softDelete) {
        taskExecutor.execute(() ->
                userScoreService.addScore(note.getUserId(), UserScoreConst.MINUS_NOTE_POST, null));
    }

    @Override
    public void onNotePraiseUpdated(@NonNull Note note, UserModel praiser, boolean praise) {
        taskExecutor.execute(() -> {
            if (ENABLE_PRAISE_SCORE) {
                if (praise) {
                    userScoreService.addScore(note.getUserId(), UserScoreConst.PRAISE, null);
                } else {
                    userScoreService.addScore(note.getUserId(), UserScoreConst.MINUS_PRAISE, null);
                }
            }
        });
    }

    @Override
    public void onCommentCreated(
            @NonNull Note note, @NonNull NoteComment comment,
            UserModel owner, UserModel toUser,
            String reviewId, @NonNull NoteCreateOrigin origin) {
        taskExecutor.execute(() ->
                userScoreService.addScore(comment.getUserId(), UserScoreConst.NOTE_COMMENT, null));
    }

    @Override
    public void onCommentDeleted(
            @NonNull Note note, @NonNull NoteComment comment,
            Integer deleteUserId, boolean softDelete) {
        if (note.getUserId().equals(deleteUserId)) {
            taskExecutor.execute(() ->
                    userScoreService.addScore(note.getUserId(), UserScoreConst.MINUS_NOTE_COMMENT, null));
        }
    }
}
