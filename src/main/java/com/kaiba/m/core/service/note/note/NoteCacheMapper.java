package com.kaiba.m.core.service.note.note;

import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.LoadingCache;
import com.google.gson.reflect.TypeToken;
import com.kaiba.lib.base.constant.note.NoteDefaults;
import com.kaiba.lib.base.domain.common.Gallery;
import com.kaiba.lib.base.domain.note.NoteGrainFlag;
import com.kaiba.lib.base.domain.note.NoteLink;
import com.kaiba.lib.base.util.GsonUtils;
import com.kaiba.lib.base.util.StringUtils;
import com.kaiba.m.core.domain.note.Note;

import java.time.Duration;
import java.util.*;

/**
 * author: lyux
 * date: 19-6-11
 */
final class NoteCacheMapper {

    private static final String ID_VALUE_FOR_NONE = "_id_none";

    private static final LoadingCache<Long, FieldMapper> mapperCache = Caffeine.newBuilder()
            .expireAfterWrite(Duration.ofHours(6))
            .build(FieldMapper::new);

    static FieldMapper getFieldMapperByGrain(NoteGrainFlag grain) {
        long grainFlag = grain == null ? NoteDefaults.DEFAULT_NOTE_QUERY_GRAIN.getFlag() : grain.getFlag();
        FieldMapper mapper = mapperCache.get(grainFlag);
        mapper = mapper == null ? new FieldMapper(NoteDefaults.DEFAULT_NOTE_QUERY_GRAIN) : mapper;
        return mapper;
    }

    static Note getNote(List<String> dataList, NoteGrainFlag grain) {
        return new NoteCacheParser(getFieldMapperByGrain(grain), dataList).getCache();
    }

    static NoteCacheParser getNoteCacheParser(List<?> dataList, NoteGrainFlag grain) {
        return new NoteCacheParser(getFieldMapperByGrain(grain), dataList);
    }

    static Map<String, String> note2mapForNone() {
        return Collections.singletonMap("id", ID_VALUE_FOR_NONE);
    }

    static Map<String, String> note2map(Note note) {
        Map<String, String> map = new HashMap<>();
        map.put("id", note.getId());
        map.put("userId", note.getUserId().toString());
        if (note.getSiteId() != null) {
            map.put("siteId", note.getSiteId().toString());
        }
        if (note.getSource() != null) {
            map.put("source", note.getSource().toString());
        }
        if (note.getVideo() != null) {
            map.put("video", GsonUtils.getGson().toJson(note.getVideo()));
        }
        if (note.getAudio() != null) {
            map.put("audio", GsonUtils.getGson().toJson(note.getAudio()));
        }
        if (note.getImages() != null) {
            map.put("images", GsonUtils.getGson().toJson(note.getImages()));
        }
        if (note.getContent() != null) {
            map.put("content", note.getContent());
        }
        if (note.getRemark() != null) {
            map.put("remark", note.getRemark());
        }
        if (note.getLongitude() != null) {
            map.put("longitude", note.getLongitude().toString());
        }
        if (note.getLatitude() != null) {
            map.put("latitude", note.getLatitude().toString());
        }
        if (note.getStreet() != null) {
            map.put("street", note.getStreet());
        }
        if (note.getLinks() != null) {
            map.put("links", GsonUtils.getGson().toJson(note.getLinks()));
        }
        if (note.getGallery() != null) {
            map.put("gallery", GsonUtils.getGson().toJson(note.getGallery()));
        }
        if (note.getStickyCommentId() != null) {
            map.put("stickyCommentId", note.getStickyCommentId());
        }
        if (note.getOriginThreadId() != null) {
            map.put("originThreadId", note.getOriginThreadId());
        }
        if (note.getThreads() != null) {
            map.put("threads", GsonUtils.getGson().toJson(note.getThreads()));
        }
        if (note.getTopThreads() != null) {
            map.put("topThreads", GsonUtils.getGson().toJson(note.getTopThreads()));
        }
        if (note.getHotThreads() != null) {
            map.put("hotThreads", GsonUtils.getGson().toJson(note.getHotThreads()));
        }
        if (note.getIsAllowComment() != null) {
            map.put("isAllowComment", note.getIsAllowComment().toString());
        }
        if (note.getIsAllowPraise() != null) {
            map.put("isAllowPraise", note.getIsAllowPraise().toString());
        }
        if (note.getIsAnonymous() != null) {
            map.put("isAnonymous", note.getIsAnonymous().toString());
        }
        if (note.getState() != null) {
            map.put("state", note.getState().toString());
        }
        map.put("createTime", note.getCreateTime().toString());
        map.put("updateTime", note.getUpdateTime().toString());
        map.put("refreshTime", note.getRefreshTime().toString());
        map.put("createTimeMS", note.getCreateTimeMS().toString());
        map.put("refreshTimeMS", note.getRefreshTimeMS().toString());
        if (note.getCommentCount() != null) {
            map.put("commentCount", Long.toString(note.getCommentCount() > 0 ? note.getCommentCount() : 0));
        }
        if (note.getPraiseCount() != null) {
            map.put("praiseCount", Long.toString(note.getPraiseCount() > 0 ? note.getPraiseCount() : 0));
        }
        if (note.getShareCount() != null) {
            map.put("shareCount", Long.toString(note.getShareCount() > 0 ? note.getShareCount() : 0));
        }
        if (note.getExtra() != null && note.getExtra().size() != 0) {
            map.put("extra", GsonUtils.getGson().toJson(note.getExtra()));
        }
        return map;
    }

    // ---------------------------------------------------

    static class FieldMapper {

        private final NoteGrainFlag grain;
        private final Map<String, Integer> fieldMap;
        private final List<String> fieldList;
        private final List<Object> fieldObjectList;
        private final String[] fieldArray;

        FieldMapper(long grainFlag) {
            this(new NoteGrainFlag(grainFlag));
        }

        FieldMapper(NoteGrainFlag grain) {
            this.grain = grain;
            this.fieldList = new ArrayList<>();
            this.fieldMap = new HashMap<>();

            addField("id");
            addField("siteId");
            addField("source");
            addField("userId");
            addField("hotThreads");
            addField("topThreads");
            addField("affinity");
            addField("isAllowComment");
            addField("isAllowPraise");
            addField("isAnonymous");
            addField("state");
            addField("createTime");
            addField("refreshTime");
            addField("updateTime");
            addField("refreshTimeMS");
            addField("createTimeMS");

            if (grain.isNeedContent() || grain.isNeedContentAsText()) {
                addField("content");
                addField("video");
                addField("mediaId");
                addField("audio");
                addField("images");
                addField("gallery");
                addField("remark");
            }
            if (grain.isNeedLocation()) {
                addField("longitude");
                addField("latitude");
                addField("street");
            }
            if (grain.isNeedCountData()) {
                addField("commentCount");
                addField("praiseCount");
                addField("shareCount");
            }
            if (grain.isNeedLinks()) {
                addField("links");
            }
            if (grain.isNeedThreads()) {
                addField("threads");
            }
            if (grain.isNeedStickyCommentData()) {
                addField("stickyCommentId");
            }
            if (grain.isNeedOriginThread()) {
                addField("originThreadId");
            }
            if (grain.isNeedExtra()) {
                addField("extra");
            }

            this.fieldArray = fieldList.toArray(new String[0]);
            this.fieldObjectList = new ArrayList<>(fieldList);
        }

        private void addField(String fieldName) {
            int size = fieldList.size();
            fieldList.add(fieldName);
            fieldMap.put(fieldName, size);
        }

        String[] getFieldArray() {
            return fieldArray;
        }

        List<Object> getFieldObjectList() {
            return fieldObjectList;
        }
    }

    // ---------------------------------------------------

    static class NoteCacheParser {
        private final FieldMapper mapper;
        private final List<?> resultList;

        private NoteCacheParser(FieldMapper mapper, List<?> resultList) {
            this.mapper = mapper;
            this.resultList = resultList;
        }

        String getValue(String fieldName, String defaultValue) {
            Integer fieldIndex = mapper.fieldMap.get(fieldName);
            if (null == fieldIndex || fieldIndex > resultList.size()) {
                return defaultValue;
            }
            String value = (String) resultList.get(fieldIndex);
            return null == value ? defaultValue : value;
        }

        String getValue(String fieldName) {
            return getValue(fieldName, null);
        }

        Integer getInteger(String fieldName) {
            return StringUtils.toIntegerIgnoreError(getValue(fieldName));
        }

        Long getLong(String fieldName) {
            return StringUtils.toLongIgnoreError(getValue(fieldName));
        }

        Double getDouble(String fieldName) {
            return StringUtils.toDoubleIgnoreError(getValue(fieldName));
        }

        Boolean getBoolean(String fieldName) {
            return StringUtils.toBooleanIgnoreError(getValue(fieldName));
        }

        Note getCache() {
            String id = getValue("id");
            if (null == id) {
                return null;
            } else if (ID_VALUE_FOR_NONE.equals(id)) {
                // id 为占位符, 表明 cache 可用, 但该 id 对应的 note 并不存在
                return new Note();
            }

            NoteGrainFlag grain = mapper.grain;
            Note note = new Note();
            note.setId(id);
            note.setSiteId(getInteger("siteId"));
            note.setSource(getInteger("source"));
            note.setUserId(getInteger("userId"));

            if (grain.isNeedContent() || grain.isNeedContentAsText()) {
                String strVideo = getValue("video");
                if (strVideo != null) {
                    note.setVideo(GsonUtils.toVideoIgnoreError(strVideo));
                }
                String strAudio = getValue("audio");
                if (strAudio != null) {
                    note.setAudio(GsonUtils.toAudioIgnoreError(strAudio));
                }
                String strImages = getValue("images");
                if (strImages != null) {
                    note.setImages(GsonUtils.toImageListIgnoreError(strImages));
                }
                String strGallery = getValue("gallery");
                if (strGallery != null) {
                    note.setGallery(GsonUtils.toModelIgnoreError(strGallery, Gallery.class));
                }
                note.setContent(getValue("content"));
                note.setRemark(getValue("remark"));
            }

            if (grain.isNeedLocation()) {
                note.setStreet(getValue("street"));
                note.setLongitude(getDouble("longitude"));
                note.setLatitude(getDouble("latitude"));
            }

            if (grain.isNeedLinks()) {
                String strLinks = getValue("links");
                if (strLinks != null) {
                    note.setLinks(GsonUtils.toModelIgnoreError(strLinks, new TypeToken<List<NoteLink>>() {}.getType()));
                }
            }

            if (grain.isNeedStickyCommentData()) {
                note.setStickyCommentId(getValue("stickyCommentId"));
            }

            if (grain.isNeedOriginThread()) {
                note.setOriginThreadId(getValue("originThreadId"));
            }

            if (grain.isNeedThreads()) {
                String strThreads = getValue("threads");
                if (strThreads != null) {
                    note.setThreads(GsonUtils.toModelIgnoreError(strThreads, new TypeToken<List<String>>() {}.getType()));
                }
            }

            if (grain.isNeedCountData()) {
                note.setCommentCount(getLong("commentCount"));
                note.setPraiseCount(getLong("praiseCount"));
                note.setShareCount(getLong("shareCount"));
            }

            if (grain.isNeedExtra()) {
                String strExtra = getValue("extra");
                if (strExtra != null) {
                    note.setExtra(GsonUtils.toModelIgnoreError(strExtra, new TypeToken<Map<String, String>>() {}.getType()));
                }
            }

            String strHotThreads = getValue("hotThreads");
            if (strHotThreads != null) {
                note.setHotThreads(GsonUtils.toModelIgnoreError(strHotThreads, new TypeToken<List<String>>() {}.getType()));
            }
            String strTopThreads = getValue("topThreads");
            if (strTopThreads != null) {
                note.setTopThreads(GsonUtils.toModelIgnoreError(strTopThreads, new TypeToken<List<String>>() {}.getType()));
            }

            note.setIsAllowComment(getBoolean("isAllowComment"));
            note.setIsAllowPraise(getBoolean("isAllowPraise"));
            note.setIsAnonymous(getBoolean("isAnonymous"));
            note.setState(getInteger("state"));
            note.setCreateTime(getLong("createTime"));
            note.setRefreshTime(getLong("refreshTime"));
            note.setUpdateTime(getLong("updateTime"));
            note.setCreateTimeMS(getLong("createTimeMS"));
            note.setRefreshTimeMS(getLong("refreshTimeMS"));

            return note;
        }
    }

}
