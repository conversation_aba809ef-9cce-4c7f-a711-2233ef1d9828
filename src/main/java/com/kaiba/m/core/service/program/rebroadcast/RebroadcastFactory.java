package com.kaiba.m.core.service.program.rebroadcast;

import com.kaiba.lib.base.constant.program.RebroadcastType;
import org.springframework.stereotype.Component;

/**
 * author wangsj
 * date 2020-09-01
 */
@Component
public class RebroadcastFactory {

    private final PreviousOneBroadcast previousOneBroadcast;
    private final PreviousDayBroadcast previousDayBroadcast;

    public RebroadcastFactory(PreviousOneBroadcast previousOneBroadcast, PreviousDayBroadcast previousDayBroadcast) {
        this.previousOneBroadcast = previousOneBroadcast;
        this.previousDayBroadcast = previousDayBroadcast;
    }

    public IRebroadcastHandle getRebroadcast(RebroadcastType strategy) {
        switch (strategy) {
            case PREVIOUS_DAY:
                return this.previousDayBroadcast;
            default:
                return this.previousOneBroadcast;
        }
    }
}
