package com.kaiba.m.core.service.kri.matcher;

import com.kaiba.lib.base.constant.KbModule;
import com.kaiba.lib.base.constant.kri.KRIMatcherFact;
import com.kaiba.lib.base.domain.kri.KbResource;
import com.kaiba.lib.base.domain.kri.fact.dataanalysis.DAChannel;
import com.kaiba.lib.base.domain.kri.fact.dataanalysis.DAGetuiBizTag;
import com.kaiba.lib.base.domain.kri.fact.viewcount.VCRegister;
import com.kaiba.lib.base.domain.kri.fact.viewcount.VCVirtual;
import com.kaiba.lib.base.response.KbCode;
import com.kaiba.lib.base.response.KbException;
import com.kaiba.lib.base.util.StringUtils;
import com.kaiba.m.core.util.JsonUtils;

/**
 * author: lyux
 * date: 2024-12-17
 */
public class MatcherUtil {

    public static final String DATA_EMPTY = "__empty__"; // 匹配的 matcher 没有 data

    public static void verifyResource(KbResource resource) {
        if (resource == null) {
            throw new KbException(KbCode.ILLEGAL_ARGUMENT, "missing resource").r("资源标识缺失").li();
        }
        if (StringUtils.isEmpty(resource.getBiz())) {
            throw new KbException(KbCode.ILLEGAL_ARGUMENT, "missing module").r("模块类型缺失").li();
        }
        KbModule.resolveByName(resource.getBiz()).orElseThrow(
                () -> new KbException(KbCode.ILLEGAL_ARGUMENT, "unknown module: " + resource.getBiz()).r("未知的模块类型").li());
    }

    public static void verifyFactData(KRIMatcherFact fact, String data) {
        switch (fact) {
            case DA_GETUI_TAG:
                JsonUtils.getGson().fromJson(data, DAGetuiBizTag.class);
                break;
            case DA_CHANNEL:
                JsonUtils.getGson().fromJson(data, DAChannel.class);
                break;
            case VC_REGISTER:
                JsonUtils.getGson().fromJson(data, VCRegister.class);
                break;
            case VC_VIRTUAL:
                JsonUtils.getGson().fromJson(data, VCVirtual.class);
                break;
        }
    }

}
