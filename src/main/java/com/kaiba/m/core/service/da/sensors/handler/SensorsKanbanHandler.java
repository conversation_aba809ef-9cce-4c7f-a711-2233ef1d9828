package com.kaiba.m.core.service.da.sensors.handler;

import com.kaiba.lib.base.constant.da.sensors.KanbanColConfigType;
import com.kaiba.lib.base.constant.da.sensors.KanbanColDataType;
import com.kaiba.m.core.controller.da.sensors.model.SensorsKanbanTimeRangeModel;

import java.util.List;

/**
 * <AUTHOR>
 * @version SensorsKanbanHandler, v0.1 2024/3/22 10:38 daopei Exp $
 **/
public interface SensorsKanbanHandler {

    KanbanColConfigType getConfigType();

    boolean support(KanbanColDataType dataType);

    void loadData(SensorsKanbanTimeRangeModel timeRange);

    void loadDataByIds(List<String> configIds, List<SensorsKanbanTimeRangeModel> times);

}
