package com.kaiba.m.core.service.note.praise;

import com.kaiba.lib.base.domain.user.UserModel;
import com.kaiba.lib.base.util.GsonUtils;
import com.kaiba.m.core.domain.note.Note;
import com.kaiba.m.core.domain.note.NotePraise;
import com.kaiba.m.core.service.note.NoteCacheConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.connection.StringRedisConnection;
import org.springframework.data.redis.core.RedisCallback;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * author: lyux
 * date: 19-6-5
 */
@Slf4j
@Service
public class NotePraiseCacheService {

    private final StringRedisTemplate stringRedisTemplate;

    public NotePraiseCacheService(StringRedisTemplate stringRedisTemplate) {
        this.stringRedisTemplate = stringRedisTemplate;
    }

    boolean isCacheReliable(Note note) {
        if (note.getCreateTime() == null) {
            return false;
        }
        long current = System.currentTimeMillis() / 1000;
        return current - note.getCreateTime() < NoteCacheConfig.PRAISE_CACHE_EXPIRE.getSeconds();
    }

    void addPraise(Note note, NotePraise notePraise) {
        String cacheKeyForSet = noteId2keyForSet(notePraise.getNoteId());
        String cacheKeyForList = noteId2keyForList(notePraise.getNoteId());
        String cacheDataForList = GsonUtils.getGson().toJson(praise2user(notePraise));
        stringRedisTemplate.executePipelined((RedisCallback<Object>) connection -> {
            StringRedisConnection conn = (StringRedisConnection) connection;
            conn.zAdd(cacheKeyForList, notePraise.getScore(), cacheDataForList);
            conn.sAdd(cacheKeyForSet, notePraise.getUserId().toString());
            conn.expire(cacheKeyForList, NoteCacheConfig.PRAISE_CACHE_EXPIRE.getSeconds());
            conn.expire(cacheKeyForSet, NoteCacheConfig.PRAISE_CACHE_EXPIRE.getSeconds());
            return null;
        });
    }

    void removePraise(NotePraise notePraise) {
        String cacheKeyForSet = noteId2keyForSet(notePraise.getNoteId());
        String cacheKeyForList = noteId2keyForList(notePraise.getNoteId());
        String cacheDataForList = GsonUtils.getGson().toJson(praise2user(notePraise));
        stringRedisTemplate.executePipelined((RedisCallback<Object>) connection -> {
            StringRedisConnection conn = (StringRedisConnection) connection;
            conn.zRem(cacheKeyForList, cacheDataForList);
            conn.sRem(cacheKeyForSet, notePraise.getUserId().toString());
            return null;
        });
    }

    List<UserModel> getPraiseUserListByNoteId(String noteId, Integer page, Integer pageSize) {
        if (NoteCacheConfig.CACHE_DEBUG) {
            log.info("[cache_debug][cache] get praise by note: " + noteId);
        }
        if (null == noteId) {
            return Collections.emptyList();
        }
        int p = page == null || page < 1 ? 0 : page - 1;
        int ps = pageSize == null ? 10 : pageSize;
        int start = p * ps;
        int end = (p + 1) * ps - 1;
        String cacheKey = noteId2keyForList(noteId);
        Set<String> set = stringRedisTemplate.opsForZSet().range(cacheKey, start, end);
        if (null == set || set.size() == 0) {
            return Collections.emptyList();
        } else {
            return dataSet2user(set);
        }
    }

    @SuppressWarnings("unchecked")
    Map<String, List<UserModel>> getPraiseUserListMapByNoteIdIn(List<String> noteIdList, Integer page, Integer pageSize) {
        if (NoteCacheConfig.CACHE_DEBUG) {
            log.info("[cache_debug][cache] get praise by note list: " + noteIdList);
        }
        if (null == noteIdList || noteIdList.size() == 0) {
            return Collections.emptyMap();
        }
        int listSize = noteIdList.size();
        int p = page == null || page < 1 ? 0 : page - 1;
        int ps = pageSize == null ? 10 : pageSize;
        int start = p * ps;
        int end = (p + 1) * ps - 1;
        List<String> cacheKeyList = noteIdList.stream()
                .map(NotePraiseCacheService::noteId2keyForList)
                .collect(Collectors.toList());
        List<Object> redisResult;
        try {
            // size of 'ids' and 'dataList' are always the same:
            // redis will fill coordinate position of 'dataList' with null if cache can not be found.
            redisResult = stringRedisTemplate.executePipelined((RedisCallback<Object>) connection -> {
                StringRedisConnection conn = (StringRedisConnection) connection;
                for (String key : cacheKeyList) {
                    conn.zRevRange(key, start, end);
                }
                return null;
            });
        } catch (Exception e) {
            redisResult = Collections.emptyList();
            log.error("get note list by id from redis fail", e);
        }
        if (redisResult.size() != listSize) {
            // something unexpected happens. just fill return list with null.
            return Collections.emptyMap();
        } else {
            Map<String, List<UserModel>> result = new HashMap<>(listSize);
            for (int i = 0; i < listSize; i ++) {
                Set<String> data = (Set<String>) redisResult.get(i);
                String noteId = noteIdList.get(i);
                if (noteId != null && data != null && data.size() != 0) {
                    result.put(noteId, dataSet2user(data));
                }
            }
            return result;
        }
    }

    boolean isPraisedByUser(String noteId, Integer userId) {
        if (null == noteId || null == userId) {
            return false;
        }
        if (NoteCacheConfig.CACHE_DEBUG) {
            log.info("[cache_debug][cache] get isPraised by note: " + noteId);
        }
        String cacheKey = noteId2keyForSet(noteId);
        String cacheValue = userId.toString();
        Boolean result = stringRedisTemplate.opsForSet().isMember(cacheKey, cacheValue);
        return result != null && result;
    }

    Map<String, Boolean> isPraisedByUser(List<String> noteIdList, Integer userId) {
        if (null == noteIdList || noteIdList.size() == 0 || null == userId) {
            return Collections.emptyMap();
        }
        if (NoteCacheConfig.CACHE_DEBUG) {
            log.info("[cache_debug][cache] get isPraised by noteList: " + noteIdList);
        }
        int listSize = noteIdList.size();
        List<String> cacheKeyList = noteIdList.stream()
                .map(NotePraiseCacheService::noteId2keyForSet)
                .collect(Collectors.toList());
        String cacheValue = userId.toString();
        List<Object> redisResult;
        try {
            // size of 'ids' and 'dataList' are always the same:
            // redis will fill coordinate position of 'dataList' with null if cache can not be found.
            redisResult = stringRedisTemplate.executePipelined((RedisCallback<Object>) connection -> {
                StringRedisConnection conn = (StringRedisConnection) connection;
                for (String key : cacheKeyList) {
                    conn.sIsMember(key, cacheValue);
                }
                return null;
            });
        } catch (Exception e) {
            redisResult = Collections.emptyList();
            log.error("get note list by id from redis fail", e);
        }
        if (redisResult.size() != listSize) {
            // something unexpected happens. just fill return list with null.
            return Collections.emptyMap();
        } else {
            Map<String, Boolean> result = new HashMap<>(listSize);
            for (int i = 0; i < listSize; i ++) {
                Boolean data = (Boolean) redisResult.get(i);
                String noteId = noteIdList.get(i);
                result.put(noteId, data);
            }
            return result;
        }
    }

    private static String noteId2keyForList(String noteId) {
        return NoteCacheConfig.PRAISE_LIST_BY_NOTE_KEY + noteId;
    }

    private static String noteId2keyForSet(String noteId) {
        return NoteCacheConfig.PRAISE_SET_BY_NOTE_KEY + noteId;
    }

    private static List<UserModel> dataSet2user(Set<String> set) {
        return set.stream()
                .map(data -> GsonUtils.toModelIgnoreError(data, UserModel.class))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    private static UserModel praise2user(NotePraise praise) {
        UserModel user = new UserModel();
        user.setUserId(praise.getUserId());
        user.setRole(praise.getUserRole());
        user.setUserName(praise.getUserName());
        user.setAvatar(praise.getUserAvatar());
        return user;
    }

}
