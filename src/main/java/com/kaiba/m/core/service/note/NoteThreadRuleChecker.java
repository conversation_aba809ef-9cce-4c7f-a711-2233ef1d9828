package com.kaiba.m.core.service.note;

import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.LoadingCache;
import com.kaiba.lib.base.constant.KbProperties;
import com.kaiba.lib.base.constant.note.INoteThreadPostRule;
import com.kaiba.lib.base.constant.note.NoteDefaults;
import com.kaiba.lib.base.constant.note.NoteThreadCondition;
import com.kaiba.lib.base.domain.note.NoteThreadRuleModel;
import com.kaiba.lib.base.domain.user.UserModel;
import com.kaiba.lib.base.response.KbCode;
import com.kaiba.lib.base.response.KbException;
import com.kaiba.lib.base.util.StringUtils;
import com.kaiba.m.core.domain.note.*;
import com.kaiba.m.core.service.green.GreenTextService;
import com.kaiba.m.core.service.note.comment.NoteCommentService;
import com.kaiba.m.core.service.note.note.NoteService;
import com.kaiba.m.core.service.note.setting.NoteSettingService;
import com.kaiba.m.core.service.note.thread.NoteThreadService;
import com.kaiba.m.core.util.ContentUtils;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * author: lyux
 * date: 19-6-13
 * <p>
 * 该类用于检查与板块相关的规则.
 * 该类中的板块内存缓存仅用于验证规则, 不用于数据组装和返回.
 */
@Slf4j
@Component
public class NoteThreadRuleChecker {

    private static final boolean SHOW_REVIEW_HINT = false;
    private static final String REVIEW_CONTENT_HINT = "[开吧小编正在努力审核您发布的内容,请耐心等待.]";

    private static final NoteThread PLACE_HOLDER_THREAD_RULE = new NoteThread();

    static {
        PLACE_HOLDER_THREAD_RULE.setCondition(NoteDefaults.DEFAULT_THREAD_CONDITION.getValue());
        PLACE_HOLDER_THREAD_RULE.setTopMax(NoteDefaults.DEFAULT_THREAD_MAX_TOP);
        PLACE_HOLDER_THREAD_RULE.setHotMax(NoteDefaults.DEFAULT_THREAD_MAX_HOT);
        PLACE_HOLDER_THREAD_RULE.setPostRule(NoteDefaults.DEFAULT_POST_RULE);
    }

    private final NoteThreadService noteThreadService;
    private final NoteService noteService;
    private final NoteCommentService noteCommentService;
    private final NoteSettingService settingService;
    private final GreenTextService greenTextService;
    private final NoteMultimediaScan noteMultimediaScan;

    private final LoadingCache<String, NoteThreadRule> threadRuleCache = Caffeine.newBuilder()
            .expireAfterWrite(NoteCacheConfig.CHECK_RULE_THREAD_CACHE_EXPIRE)
            .build(this::getNoteThreadForRuleCheck);

    private final LoadingCache<Integer, NoteSettingRule> siteSettingCache = Caffeine.newBuilder()
            .expireAfterWrite(NoteCacheConfig.CHECK_RULE_THREAD_CACHE_EXPIRE)
            .build(this::getNoteSettingForCheck);

    public NoteThreadRuleChecker(
            NoteThreadService noteThreadService,
            NoteService noteService,
            NoteCommentService noteCommentService,
            NoteSettingService settingService,
            GreenTextService greenTextService,
            NoteMultimediaScan noteMultimediaScan
    ) {
        this.noteThreadService = noteThreadService;
        this.noteService = noteService;
        this.noteCommentService = noteCommentService;
        this.settingService = settingService;
        this.greenTextService = greenTextService;
        this.noteMultimediaScan = noteMultimediaScan;
    }

    public NoteCreateResult addNote(Note note, UserModel user, boolean honorCondition) {
        if (note.getThreads() == null || note.getThreads().size() == 0) {
            throw new KbException(KbCode.ILLEGAL_ARGUMENT, "note should have at least 1 thread.");
        }
        if (ContentUtils.isSuspectXSS(note.getContent())) {
            throw new KbException(KbCode.ILLEGAL_ARGUMENT, "note content xss suspect: " + note.getContent())
                    .r("帖子中可能含有非法字符, 请重新编辑后再尝试发布").li();
        }
        Map<String, NoteThreadRule> rules = threadRuleCache.getAll(note.getThreads());
        if (rules.size() == 0) {
            throw new KbException(KbCode.ILLEGAL_ARGUMENT, "note threads not valid: " + note.getId() + "->" + note.getThreads());
        }
        INoteThreadPostRule rule = rules.get(note.getThreads().get(0)).thread.getPostRule();
        checkNotePostRule(note, rule);
        if (!rule.isLocationStreetRequired() && user.getIsPublic() != null && user.getIsPublic() == 0) {
            note.setStreet(null);
        }
        if (honorCondition) {
            NoteThreadCondition condition = getMostStrictCondition(rules.values());
            // log.info("determine condition by threads: "
            //         + String.join(",", note.getThreads()) + ", condition: " + condition);
            switch (condition) {
                case FREE:
                    return NoteCreateResult.asCreate(noteService.noteCreate(note, user));
                case AUTO_REVIEW:
                    if (noteMultimediaScan.needAsyncScan(note)) {
                        NoteReview noteReview = noteService.noteCreateForReview(note);
                        noteMultimediaScan.multimediaSafeScanAsync(noteReview);
                        return NoteCreateResult.asReview(note, noteReview);
                    } else {
                        if (greenTextService.scan(note.getContent())) {
                            return NoteCreateResult.asCreate(noteService.noteCreate(note, user));
                        } else {
                            return NoteCreateResult.asReview(note, noteService.noteCreateForReview(note));
                        }
                    }
                case REVIEW:
                    return NoteCreateResult.asReview(note, noteService.noteCreateForReview(note));
                case READONLY:
                    throw new KbException(KbCode.NOTE_THREAD_CONDITION_READONLY).setLevel(KbException.LEVEL_DEBUG);
                case CLOSED:
                    throw new KbException(KbCode.NOTE_THREAD_CONDITION_CLOSED).setLevel(KbException.LEVEL_DEBUG);
                default:
                    throw new KbException(KbCode.ILLEGAL_STATE);
            }
        } else {
            return NoteCreateResult.asCreate(noteService.noteCreate(note, user));
        }
    }

    public List<String> getRouteThreadList(String threadId) {
        List<String> threadList = new LinkedList<>();
        threadList.add(threadId);
        NoteThreadRule threadRule = threadRuleCache.get(threadId);
        if (threadRule != null) {
            threadList.addAll(threadRule.thread.getRouteThreads());
        }
        return threadList;
    }

    public Set<String> getRouteThreadList(List<String> threadIds) {
        return threadRuleCache.getAll(threadIds).values().stream()
                .flatMap(rule -> rule.thread.getRouteThreads().stream())
                .collect(Collectors.toSet());
    }

    public Optional<NoteThreadRuleModel> getThreadRuleById(String threadId) {
        NoteThreadRule rule = threadRuleCache.get(threadId);
        if (rule == null || rule.thread == null || rule.thread.getId() == null) {
            return Optional.empty();
        } else {
            NoteThreadRuleModel model = new NoteThreadRuleModel();
            model.setId(rule.thread.getId());
            model.setPostRule(rule.thread.getPostRule());
            model.setCondition(rule.condition.getValue());
            return Optional.of(model);
        }
    }

    public CommentCreateResult addComment(Note note, NoteComment comment, boolean honorCondition) {
        if (note.getThreads() == null || note.getThreads().size() == 0) {
            throw new KbException(KbCode.ILLEGAL_ARGUMENT, "note should have at least 1 thread: " + note.getId());
        }
        Map<String, NoteThreadRule> rules = threadRuleCache.getAll(note.getThreads());
        if (rules.size() == 0) {
            throw new KbException(KbCode.ILLEGAL_ARGUMENT, "note threads not valid: " + note.getId() + "->" + note.getThreads());
        }
        checkCommentPostRule(comment, rules.get(note.getThreads().get(0)).thread.getPostRule());
        if (honorCondition) {
            NoteThreadCondition condition = getMostStrictCondition(rules.values());
            switch (condition) {
                case FREE:
                    return CommentCreateResult.asCreate(noteCommentService.commentCreate(comment));
                case AUTO_REVIEW:
                    if (greenTextService.scan(comment.getContent())) {
                        return CommentCreateResult.asCreate(noteCommentService.commentCreate(comment));
                    } else {
                        return CommentCreateResult.asReview(
                                comment, noteCommentService.commentCreateForReview(comment));
                    }
                case REVIEW:
                    return CommentCreateResult.asReview(
                            comment, noteCommentService.commentCreateForReview(comment));
                case READONLY:
                    throw new KbException(KbCode.NOTE_THREAD_CONDITION_READONLY).setLevel(KbException.LEVEL_DEBUG);
                case CLOSED:
                    throw new KbException(KbCode.NOTE_THREAD_CONDITION_CLOSED).setLevel(KbException.LEVEL_DEBUG);
                default:
                    throw new KbException(KbCode.ILLEGAL_STATE);
            }
        } else {
            return CommentCreateResult.asCreate(noteCommentService.commentCreate(comment));
        }
    }

    public void viewNoteCheckCondition(List<String> threadIdList) {
        if (null != threadIdList && threadIdList.size() != 0) {
            Map<String, NoteThreadRule> rules = threadRuleCache.getAll(threadIdList);
            if (getMostStrictCondition(rules.values()) == NoteThreadCondition.CLOSED) {
                throw new KbException(KbCode.NOTE_THREAD_CONDITION_CLOSED).setLevel(KbException.LEVEL_DEBUG);
            }
        }
    }

    public void viewNoteCheckCondition(String threadId) {
        if (null != threadId) {
            NoteThreadRule rule = threadRuleCache.get(threadId);
            if (rule != null && rule.condition == NoteThreadCondition.CLOSED) {
                throw new KbException(KbCode.NOTE_THREAD_CONDITION_CLOSED).setLevel(KbException.LEVEL_DEBUG);
            }
        }
    }

    public NoteThread getThread(String threadId) {
        NoteThreadRule rule = threadRuleCache.get(threadId);
        return rule == null ? null : rule.thread;
    }

    public int getThreadHotMax(String threadId) {
        NoteThreadRule rule = threadRuleCache.get(threadId);
        if (null == rule) {
            return 0;
        } else if (rule.condition == NoteThreadCondition.CLOSED) {
            return 0;
        } else {
            int hotMax = rule.thread.getHotMax();
            return hotMax < 0 ? Integer.MAX_VALUE : hotMax;
        }
    }

    public int getThreadTopMax(String threadId) {
        NoteThreadRule rule = threadRuleCache.get(threadId);
        if (null == rule) {
            return 0;
        } else if (rule.condition == NoteThreadCondition.CLOSED) {
            return 0;
        } else {
            return rule.thread.getTopMax();
        }
    }

    private NoteThreadRule getNoteThreadForRuleCheck(String threadId) {
        NoteThread thread = noteThreadService.getThreadById(threadId).orElse(PLACE_HOLDER_THREAD_RULE);
        NoteThreadRule rule = new NoteThreadRule();
        rule.thread = thread;
        rule.condition = NoteThreadCondition.valueOf(thread.getCondition())
                .orElse(NoteDefaults.DEFAULT_THREAD_CONDITION);
        thread.setCondition(rule.condition.getValue());
        if (thread.getPostRule() == null) {
            thread.setPostRule(NoteDefaults.DEFAULT_POST_RULE);
        }
        if (thread.getTopMax() == null) {
            thread.setTopMax(NoteDefaults.DEFAULT_THREAD_MAX_TOP);
        }
        if (thread.getHotMax() == null) {
            thread.setHotMax(NoteDefaults.DEFAULT_THREAD_MAX_HOT);
        }
        if (thread.getRouteThreads() == null) {
            thread.setRouteThreads(Collections.emptyList());
        }
        return rule;
    }

    private NoteSettingRule getNoteSettingForCheck(Integer siteId) {
        NoteSetting setting = settingService.getSiteSetting(siteId);
        NoteSettingRule rule = new NoteSettingRule();
        rule.setting = setting;
        rule.setting.setSiteId(siteId);
        rule.condition = NoteThreadCondition.valueOf(setting.getCondition()).orElse(NoteThreadCondition.FREE);
        return rule;
    }

    public NoteThreadCondition getMostStrictCondition(String threadId) {
        NoteThreadRule rule = threadRuleCache.get(threadId);
        if (rule == null) {
            return NoteThreadCondition.READONLY;
        }
        NoteThread thread = rule.thread;
        int routeSize = thread.getRouteThreads() == null ? 0 : thread.getRouteThreads().size();
        List<NoteThreadRule> rules = new ArrayList<>(routeSize + 1);
        rules.add(rule);
        if (routeSize > 0) {
            rules.addAll(threadRuleCache.getAll(thread.getRouteThreads()).values());
        }
        return getMostStrictCondition(rules);
    }

    private NoteThreadCondition getMostStrictCondition(Collection<NoteThreadRule> list) {
        if (null == list || list.size() == 0) {
            return NoteThreadCondition.READONLY;
        }
        NoteThreadCondition mostStrictCondition = null;
        Integer lastSiteId = null;
        for (NoteThreadRule rule : list) {
            if (mostStrictCondition == null || rule.condition.getStrict() > mostStrictCondition.getStrict()) {
                mostStrictCondition = rule.condition;
            }
            // 检查板块所属电台的总板块配置
            Integer siteId = rule.thread.getSiteId();
            // 绝大多数情况下 list 中的 thread 都是从属于同一个电台的, 这段代码用于跳过此种情况下的重复检测
            // 之所以没有跳过所有重复电台检测, 是为了避免创建新的容器对象 (有容器才可以判断是否检测过)
            if (siteId == null || siteId.equals(lastSiteId)) {
                continue;
            }
            lastSiteId = siteId;
            NoteSettingRule setting = siteSettingCache.get(rule.thread.getSiteId());
            if (setting != null && setting.condition.getStrict() > mostStrictCondition.getStrict()) {
                mostStrictCondition = setting.condition;
            }
        }
        // 检查平台板块配置
        NoteSettingRule platformSetting = siteSettingCache.get(KbProperties.PLATFORM_SITE_ID);
        if (platformSetting != null && platformSetting.condition.getStrict() > mostStrictCondition.getStrict()) {
            mostStrictCondition = platformSetting.condition;
        }
        return mostStrictCondition;
    }

    private static void checkNotePostRule(Note note, INoteThreadPostRule rule) {
        if (rule == null) {
            rule = NoteDefaults.DEFAULT_POST_RULE;
        }

        int textLength = note.getContent() == null ? 0 : note.getContent().length();
        if (textLength < rule.getTextMin()) {
            String msg = rule.getTextMin() == 0 ? "请添加文字内容" : "请添加至少" + rule.getTextMin() + "个文字";
            throw new KbException(KbCode.NOTE_RULE_NOT_MET).setReadableMessage(msg).setLevel(0);
        }
        if (textLength > rule.getTextMax()) {
            throw new KbException(KbCode.NOTE_RULE_NOT_MET)
                    .setReadableMessage("最多只能添加" + rule.getTextMax() + "个文字")
                    .setLevel(KbException.LEVEL_DEBUG);
        }

        int imageSize = note.getImages() == null ? 0 : note.getImages().size();
        if (imageSize < rule.getImageMin()) {
            throw new KbException(KbCode.NOTE_RULE_NOT_MET)
                    .setReadableMessage("请添加至少" + (rule.getImageMin() + 1) + "张图片")
                    .setLevel(KbException.LEVEL_DEBUG);
        }
        if (imageSize > rule.getImageMax()) {
            throw new KbException(KbCode.NOTE_RULE_NOT_MET)
                    .setReadableMessage("最多只能添加" + rule.getImageMax() + "张图片")
                    .setLevel(KbException.LEVEL_DEBUG);
        }

        if (rule.isAudioRequired() && note.getAudio() == null) {
            throw new KbException(KbCode.NOTE_RULE_NOT_MET).setReadableMessage("请添加音频").setLevel(KbException.LEVEL_DEBUG);
        }

        if (rule.isVideoRequired() && note.getVideo() == null) {
            throw new KbException(KbCode.NOTE_RULE_NOT_MET).setReadableMessage("请添加视频").setLevel(KbException.LEVEL_DEBUG);
        }

        boolean hasLocation = (note.getLatitude() != null && note.getLongitude() != null);
        if (rule.isLocationRequired() && !hasLocation) {
            throw new KbException(KbCode.NOTE_RULE_NOT_MET).setReadableMessage("请添加位置信息").setLevel(KbException.LEVEL_DEBUG);
        }

        if (rule.isLocationStreetRequired() && note.getStreet() == null) {
            throw new KbException(KbCode.NOTE_RULE_NOT_MET).setReadableMessage("请添加街道信息").setLevel(KbException.LEVEL_DEBUG);
        }

        if (textLength == 0 && imageSize == 0
                && note.getAudio() == null && note.getVideo() == null
                && hasLocation && note.getStreet() == null) {
            throw new KbException(KbCode.NOTE_RULE_NOT_MET).setReadableMessage("不能发布空帖子").setLevel(KbException.LEVEL_DEBUG);
        }
    }

    private static void checkCommentPostRule(NoteComment comment, INoteThreadPostRule rule) {
        if (rule == null) {
            rule = NoteDefaults.DEFAULT_POST_RULE;
        }
        int textLength = comment.getContent() == null ? 0 : comment.getContent().length();
        if (textLength < rule.getCommentTextMin()) {
            throw new KbException(KbCode.NOTE_RULE_NOT_MET)
                    .r("请添加至少" + rule.getCommentTextMin() + "个文字").li();
        }
        if (textLength > rule.getCommentTextMax()) {
            throw new KbException(KbCode.NOTE_RULE_NOT_MET)
                    .r("最多只能添加" + rule.getCommentTextMax() + "个文字").li();
        }
    }

    private static class NoteThreadRule {
        NoteThread thread;
        NoteThreadCondition condition;
    }

    private static class NoteSettingRule {
        NoteSetting setting;
        NoteThreadCondition condition;
    }

    @Getter
    public static class NoteCreateResult {
        private final NoteReview noteReview;
        private final Note note;

        private NoteCreateResult(Note note, NoteReview noteReview) {
            this.noteReview = noteReview;
            this.note = note;
        }

        public boolean isReview() {
            return noteReview != null;
        }

        private static NoteCreateResult asReview(Note note, NoteReview noteReview) {
            if (noteReview == null || noteReview.getId() == null) {
                throw new KbException(KbCode.ILLEGAL_ARGUMENT, "note review null").li();
            }
            if (SHOW_REVIEW_HINT) {
                if (StringUtils.isEmpty(note.getContent())) {
                    note.setContent(REVIEW_CONTENT_HINT);
                } else {
                    note.setContent(REVIEW_CONTENT_HINT + "\n" + note.getContent());
                }
            }
            return new NoteCreateResult(note, noteReview);
        }

        private static NoteCreateResult asCreate(Note note) {
            return new NoteCreateResult(note, null);
        }
    }

    @Getter
    public static class CommentCreateResult {
        private final NoteCommentReview commentReview;
        private final NoteComment comment;

        private CommentCreateResult(NoteComment comment, NoteCommentReview commentReview) {
            this.commentReview = commentReview;
            this.comment = comment;
        }

        public boolean isReview() {
            return commentReview != null;
        }

        private static CommentCreateResult asReview(NoteComment comment, NoteCommentReview commentReview) {
            if (commentReview == null || commentReview.getId() == null) {
                throw new KbException(KbCode.ILLEGAL_ARGUMENT, "note comment review null").li();
            }
            if (SHOW_REVIEW_HINT) {
                if (StringUtils.isEmpty(comment.getContent())) {
                    comment.setContent(REVIEW_CONTENT_HINT);
                } else {
                    comment.setContent(REVIEW_CONTENT_HINT + "\n" + comment.getContent());
                }
            }
            return new CommentCreateResult(comment, commentReview);
        }

        static CommentCreateResult asCreate(NoteComment comment) {
            return new CommentCreateResult(comment, null);
        }
    }
}
