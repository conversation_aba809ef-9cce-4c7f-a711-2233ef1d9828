package com.kaiba.m.core.service.da.eventtrack.strategy;

import com.kaiba.lib.base.domain.common.KbTimeRange;
import com.kaiba.lib.base.util.GsonUtils;
import com.kaiba.m.core.constant.da.eventtrack.ETDocSyncStrategyType;
import com.kaiba.m.core.domain.da.eventtrack.ETDocSyncCursor;
import com.kaiba.m.core.repository.da.eventtrack.ETDocSyncCursorRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * ETDoc同步策略工厂
 */
@Slf4j
@Component
public class ETDocSyncStrategyFactory {

    private final ETDocSyncCursorRepository etDocSyncCursorRepository;

    private final Map<String, ETDocSyncStrategy> strategyMap = new ConcurrentHashMap<>();

    public ETDocSyncStrategyFactory(
            ETDocSyncCursorRepository etDocSyncCursorRepository,
            List<ETDocSyncStrategy> strategies
    ) {
        this.etDocSyncCursorRepository = etDocSyncCursorRepository;
        for (ETDocSyncStrategy strategy : strategies) {
            strategyMap.put(strategy.getStrategy(), strategy);
        }
    }
    
    /**
     * 获取策略
     * @param strategy 策略名称
     * @return 同步策略
     */
    public ETDocSyncStrategy getStrategy(String strategy) {
        return strategyMap.get(strategy);
    }
    

    /**
     * 执行全量同步
     * @param strategy 策略名称
     */
    public void execute(String strategy) {
        ETDocSyncStrategy handler = getStrategy(strategy);
        if (handler == null) {
            return;
        }
        handler.syncAll();
        recordSyncCursor(strategy, System.currentTimeMillis());
    }
    
    /**
     * 执行时间范围同步
     * @param strategy 策略名称
     * @param timeRange 时间范围
     */
    public void executeByTimeRange(String strategy, KbTimeRange timeRange, boolean refreshCursor) {
        ETDocSyncStrategy handler = getStrategy(strategy);
        if (handler == null) {
            return;
        }
        handler.syncByTimeRange(timeRange);
        if (refreshCursor) {
            Long timestamp = timeRange.getUpper() == null ? System.currentTimeMillis() : timeRange.obtainUpperInMillis();
            recordSyncCursor(strategy, timestamp);
        }
    }

    /**
     * 根据游标自动进行时间范围同步
     * @param strategy
     * @param refreshCursor
     */
    public void executeByCursor(String strategy, boolean refreshCursor) {
        ETDocSyncStrategy handler = getStrategy(strategy);
        if (handler == null) {
            return;
        }
        ETDocSyncStrategyType strategyType = ETDocSyncStrategyType.resolverByName(strategy).orElse(null);
        if (strategyType == null) {
            return;
        }
        Long baseTime = etDocSyncCursorRepository.findByStrategy(strategy).map(ETDocSyncCursor::getTimestamp).orElse(0L);
        KbTimeRange timeRange = KbTimeRange.rangeClosed(baseTime, System.currentTimeMillis());
        handler.syncByTimeRange(timeRange);
        if (refreshCursor) {
            recordSyncCursor(strategy, timeRange.obtainUpperInMillis());
        }
    }


    /**
     * 记录同步游标
     * @param strategy
     * @param timestamp
     */
    private void recordSyncCursor(String strategy, Long timestamp) {
        if (timestamp == null || strategy == null) {
            log.error("recordSyncCursor error, strategy or time miss: {}, {}", strategy, timestamp);
            return;
        }
        ETDocSyncCursor cursor = etDocSyncCursorRepository.upsert(strategy, timestamp);
        log.info("record sync cursor data:{}", GsonUtils.getGson().toJson(cursor));
    }


}