package com.kaiba.m.core.service.da.eventtrack.strategy.impl;

import com.kaiba.lib.base.constant.KbModule;
import com.kaiba.lib.base.constant.news.NewsChannel;
import com.kaiba.lib.base.domain.common.KbTimeRange;
import com.kaiba.lib.base.domain.da.sensors.channel.SensorsArticleChannelModel;
import com.kaiba.m.core.constant.da.eventtrack.ETDocSyncStrategyType;
import com.kaiba.m.core.domain.activityhub.KbActivity;
import com.kaiba.m.core.domain.activityhub.KbActivityPage;
import com.kaiba.m.core.domain.da.eventtrack.ETDoc;
import com.kaiba.m.core.domain.da.sensors.channel.SensorsArticleChannel;
import com.kaiba.m.core.repository.activityhub.ActivityPageRepository;
import com.kaiba.m.core.repository.activityhub.ActivityRepository;
import com.kaiba.m.core.repository.da.eventtrack.ETDocRepository;
import com.kaiba.m.core.repository.da.eventtrack.ETDocSyncCursorRepository;
import com.kaiba.m.core.service.da.eventtrack.ETDocModelHelper;
import com.kaiba.m.core.service.da.eventtrack.strategy.model.ETDocSyncQueryModel;
import com.kaiba.m.core.service.da.sensors.channel.SensorsArticleService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @version ActivityHubETDocSyncStrategy, v0.1 2025/4/9 18:19 daopei Exp $
 **/
@Slf4j
@Service
public class ActivityHubETDocSyncStrategy extends AbstractETDocSyncStrategy{

    private final ActivityRepository activityRepository;
    private final ActivityPageRepository activityPageRepository;
    private final SensorsArticleService sensorsArticleService;

    public ActivityHubETDocSyncStrategy(
            ETDocRepository etDocRepository,
            ETDocSyncCursorRepository etDocSyncCursorRepository,
            ActivityRepository activityRepository,
            ActivityPageRepository activityPageRepository,
            SensorsArticleService sensorsArticleService
    ) {
        super(etDocRepository, etDocSyncCursorRepository);
        this.activityRepository = activityRepository;
        this.activityPageRepository = activityPageRepository;
        this.sensorsArticleService = sensorsArticleService;
    }

    @Override
    public void syncAll() {
        initDocByPageExecutor(this::execute, null);
    }

    @Override
    public void syncByTimeRange(KbTimeRange time) {
        initDocByPageExecutor(this::execute, time);
    }

    @Override
    public String getStrategy() {
        return ETDocSyncStrategyType.ACTIVITY_HUB.name();
    }


    private boolean execute(ETDocSyncQueryModel query) {
        Pageable pageable = createPageable(query.getPage(), query.getPageSize(), Sort.by(Sort.Direction.DESC,"updateTime"));
        KbTimeRange timeRange = defaultOrFillTime(query.getTimeRange());
        Long startTime = timeRange.obtainLowerInMillis();
        Long endTime = timeRange.obtainUpperInMillis();
        Page<KbActivity> page = activityRepository.findByUpdateTimeBetween(startTime, endTime, pageable);

        for (KbActivity activity : page.getContent()) {
            ETDoc doc = ETDocModelHelper.createByAll(KbModule.ACTIVITY_HUB.name(), null,
                    activity.getKey(), "index", null,
                    activity.getSiteId(), activity.getName(),
                    activity.getChannelKey(), null, Collections.singleton("da_activity"),
                    activity.getCreateTime());
            fillChannelAndDepart(doc);
            saveETDoc(doc);
            //补齐因为前端可能存在不上报 siteId 的场景
            ETDoc docOther = ETDocModelHelper.createByAll(KbModule.ACTIVITY_HUB.name(), null,
                    activity.getKey(), "index", null,
                    null, activity.getName(),
                    activity.getChannelKey(), null, Collections.singleton("da_activity"),
                    activity.getCreateTime());
            fillChannelAndDepart(docOther);
            saveETDoc(docOther);

            Pageable pagePageable = createPageable(1, 100, Sort.by(Sort.Direction.DESC,"seq"));
            List<KbActivityPage> activityPages = activityPageRepository.findByActivityIdOrderBySeqDesc(activity.getId(), pagePageable).getContent();
            for (KbActivityPage activityPage : activityPages) {
                ETDoc pageDoc = ETDocModelHelper.createByAll(KbModule.ACTIVITY_HUB.name(), null,
                        activity.getKey(), activityPage.getPageKey(), null,
                        activity.getSiteId(), activityPage.getName(),
                        activity.getChannelKey(), null, Collections.singleton("da_activity"),
                        activityPage.getCreateTime());
                fillChannelAndDepart(pageDoc);
                saveETDoc(pageDoc);

                ETDoc pageDocOther = ETDocModelHelper.createByAll(
                        KbModule.ACTIVITY_HUB.name(), null,
                        activity.getKey(), activityPage.getPageKey(), null,
                        null, activityPage.getName(),
                        activity.getChannelKey(), null, Collections.singleton("da_activity"),
                        activityPage.getCreateTime());
                fillChannelAndDepart(pageDocOther);
                saveETDoc(pageDocOther);
            }
        }

        return page.hasNext();
    }

    private void fillChannelAndDepart(ETDoc doc) {
        if (doc == null || doc.getBiz() == null || doc.getRef1() == null) {
            return;
        }
        String refKey = SensorsArticleChannelModel.buildRefKey(doc.getBiz(), doc.getRef1(), null, null);
        SensorsArticleChannel articleChannel = sensorsArticleService.getByRefKey(refKey).orElse(null);
        if (articleChannel == null || articleChannel.getChannelKey() == null) {
            return;
        }
        String channel = articleChannel.getChannelKey();
        if ("OTHER_SITE".equals(channel)) {
            channel = NewsChannel.OTHER.name();
        }
        doc.setChannel(channel);
        doc.setDepart(articleChannel.getDepartKey());
    }
}
