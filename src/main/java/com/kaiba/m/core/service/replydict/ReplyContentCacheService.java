package com.kaiba.m.core.service.replydict;

import com.kaiba.lib.base.util.GsonUtils;
import com.kaiba.m.core.domain.replydict.ReplyContent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.time.Duration;

/**
 * 回复内容的缓存
 * Created by shenxl on 2020/10/14
 */
@Slf4j
@Service
class ReplyContentCacheService {

    private static final String REPLY_CONTENT_BY_ID = "k_reply_content_by_id_";
    private static final Duration REPLY_CACHE_EXPIRE = Duration.ofMinutes(30);

    private final StringRedisTemplate stringRedisTemplate;

    public ReplyContentCacheService(StringRedisTemplate stringRedisTemplate) {
        this.stringRedisTemplate = stringRedisTemplate;
    }

    ReplyContent findByGroupId(Integer siteId, String groupId) {
        String cacheResult = stringRedisTemplate.opsForValue().get(replyId2key(siteId, groupId));
        return GsonUtils.toModelIgnoreError(cacheResult, ReplyContent.class);
    }

    void delete(Integer siteId, String groupId) {
        String key = replyId2key(siteId, groupId);
        stringRedisTemplate.delete(key);
        log.info("for key " + key + ", reply content in cache is deleted");
    }

    void setReplyContent(ReplyContent replyContent) {
        if (replyContent != null) {
            String json = GsonUtils.getGson().toJson(replyContent);
            String key = replyId2key(replyContent.getSiteId(), replyContent.getGroupId());
            stringRedisTemplate.opsForValue().set(key, json, REPLY_CACHE_EXPIRE);
            log.info("for key " + key + ", reply content is added in cache:" + json);
        }
    }

    private static String replyId2key(Integer siteId, String groupId) {
        return REPLY_CONTENT_BY_ID + siteId + "_"+ groupId;
    }
}