package com.kaiba.m.core.service.note;

import com.kaiba.lib.base.constant.note.NoteDefaults;
import com.kaiba.lib.base.constant.note.NoteState;
import com.kaiba.lib.base.domain.common.Image;
import com.kaiba.lib.base.domain.note.NoteCommentModel;
import com.kaiba.lib.base.domain.note.NoteGrainFlag;
import com.kaiba.lib.base.domain.note.NoteModel;
import com.kaiba.lib.base.domain.note.NoteThreadModel;
import com.kaiba.lib.base.domain.user.UserModel;
import com.kaiba.lib.base.response.KbCode;
import com.kaiba.lib.base.response.KbException;
import com.kaiba.lib.base.service.IUserService;
import com.kaiba.m.core.domain.note.Note;
import com.kaiba.m.core.domain.note.NoteComment;
import com.kaiba.m.core.domain.note.NoteThread;
import com.kaiba.m.core.service.note.comment.NoteCommentService;
import com.kaiba.m.core.service.note.note.NoteService;
import com.kaiba.m.core.service.note.praise.NotePraiseService;
import com.kaiba.m.core.service.note.thread.NoteThreadService;
import lombok.extern.slf4j.Slf4j;

import java.util.*;
import java.util.stream.Collectors;

/**
 * author: lyux
 * date: 19-5-29
 */
@Slf4j
public class NoteConvertHelper {

    private final NoteService noteService;
    private final NoteThreadService noteThreadService;
    private final NoteCommentService noteCommentService;
    private final NotePraiseService notePraiseService;
    private final IUserService userService;

    private List<Note> noteList;
    private Map<String, Note> noteMap;
    private String relatedThreadId;
    private Integer relatedUserId;
    private NoteGrainFlag grain;
    private boolean isAssembleNoteList; // 标记本次组装的是单一帖子, 还是帖子列表, 主要用于用户黑名单逻辑

    private List<NoteModel> resultList;

    private Set<Integer> userIdSet;
    private Set<String> threadIdSet;
    private Set<String> commentListNoteIdSet;
    private Set<String> commentIdSet;
    private List<Note> praiseStateNoteList;
    private List<Note> praiseListByNoteList;
    private Set<Integer> userBlacklist;

    private Map<Integer, UserModel> userMap;
    private Map<String, NoteThreadModel> threadMap;
    private Map<String, NoteComment> commentMap;
    private Map<String, List<NoteComment>> noteCommentListMap;
    private Map<String, Boolean> praiseStateMap;
    private Map<String, List<UserModel>> praiseListMap;

    NoteConvertHelper(
            NoteService noteService,
            NoteThreadService noteThreadService,
            NoteCommentService noteCommentService,
            NotePraiseService notePraiseService,
            IUserService userService
    ) {
        this.noteService = noteService;
        this.noteCommentService = noteCommentService;
        this.notePraiseService = notePraiseService;
        this.userService = userService;
        this.noteThreadService = noteThreadService;
    }

    public NoteConvertHelper setNote(Note note) {
        this.noteList = Collections.singletonList(note);
        this.isAssembleNoteList = false;
        return this;
    }

    public NoteConvertHelper setNoteList(List<Note> noteList) {
        this.noteList = noteList;
        this.isAssembleNoteList = true;
        return this;
    }

    public NoteConvertHelper setRelatedThreadId(String relatedThreadId) {
        this.relatedThreadId = relatedThreadId;
        return this;
    }

    public NoteConvertHelper setRelatedUserId(Integer relatedUserId) {
        this.relatedUserId = relatedUserId;
        return this;
    }

    public NoteConvertHelper setGrain(NoteGrainFlag grain) {
        this.grain = grain;
        return this;
    }

    public NoteConvertHelper setGrain(Long grain) {
        if (null != grain) {
            this.grain = new NoteGrainFlag(grain);
        }
        return this;
    }

    public List<NoteModel> getResultList() {
        if (noteList == null) {
            throw new KbException(KbCode.ILLEGAL_ARGUMENT);
        }
        if (grain == null) {
            grain = NoteDefaults.DEFAULT_NOTE_QUERY_GRAIN;
        }
        collect();
        request();
        assemble();
        return resultList;
    }

    public NoteModel getResult() {
        collect();
        request();
        assemble();
        return resultList.get(0);
    }

    private void collect() {
        int noteSize = noteList.size();
        if (noteSize > 1) {
            noteMap = new HashMap<>(noteSize);
        }
        this.resultList = new ArrayList<>(noteSize);

        for (Note note : noteList) {
            if (note == null) {
                continue;
            }
            if (noteSize > 1) {
                if (isAssembleNoteList) {
                    if (grain.isHonorSoftDelete() && NoteConverter.isNoteSoftDeleted(note)) {
                        continue;
                    }
                    if (grain.isHonorAuthorOnly()
                            && Objects.equals(note.getState(), NoteState.AUTHOR_ONLY.getValue())
                            && !Objects.equals(note.getUserId(), relatedUserId)) {
                        continue;
                    }
                }
                noteMap.put(note.getId(), note);
            }
            NoteModel model = preAssemble(note, grain);

            if (grain.isHonorAnonymous() && note.getIsAnonymous() != null && note.getIsAnonymous()) {
                model.setUser(emptyUserModel(null));
            } else if (grain.isNeedUserInfo()) {
                lazyUserIdSet().add(note.getUserId());
            } else {
                model.setUser(emptyUserModel(note.getUserId()));
            }

            if (grain.isNeedThreads() && note.getThreads() != null) {
                lazyThreadIdSet().addAll(note.getThreads());
            }

            if (grain.isNeedOriginThread()
                    && note.getOriginThreadId() != null
                    && !note.getOriginThreadId().equals(relatedThreadId)) {
                lazyThreadIdSet().add(note.getOriginThreadId());
            }

            if (grain.isNeedTopAndHotThreads()) {
                if (note.getTopThreads() != null) {
                    note.getTopThreads().forEach(threadId -> lazyThreadIdSet().add(threadId));
                }
                if (note.getHotThreads() != null) {
                    note.getHotThreads().forEach(threadId -> lazyThreadIdSet().add(threadId));
                }
            }

            if (grain.getNeedCommentListSize() > 0 && note.getCommentCount() != null && note.getCommentCount() > 0) {
                lazyCommentListNoteIdSet().add(note.getId());
            }

            if (grain.isNeedStickyCommentData() && note.getStickyCommentId() != null) {
                lazyCommentIdSet().add(note.getStickyCommentId());
            }

            if (grain.getNeedPraiseListSize() > 0 && note.getPraiseCount() != null && note.getPraiseCount() > 0) {
                lazyPraiseListByNoteList().add(note);
            }

            if (relatedThreadId != null) {
                if (note.getHotThreads() != null && note.getHotThreads().contains(relatedThreadId)) {
                    model.setIsHot(true);
                }
                if (note.getTopThreads() != null && note.getTopThreads().contains(relatedThreadId)) {
                    model.setIsTop(true);
                }
            }

            if (relatedUserId != null && model.getIsPraised() == null) {
                lazyPraiseStateNoteList().add(note);
            }

            if (grain.isAsViewCount()) {
                noteService.noteIncreaseViewCount(note);
            }

            resultList.add(model);
        }
    }

    private void request() {
        requestCommentListByNoteIdSet();
        requestCommentByIdSet();
        requestUserPraiseListByNote();
        requestUserPraiseStateByIdSet();
        requestThreadByIdSet();
        requestUserByIdSet();

        // 是否加载用户黑名单数据要基于以上请求结果来判断, 因此必须放在最后
        requestUserBlacklist();
    }

    private void requestCommentByIdSet() {
        if (commentIdSet != null && commentIdSet.size() != 0) {
            if (grain.isAllowCache()) {
                commentMap = noteCommentService.getCommentMapInAllowCache(new ArrayList<>(commentIdSet));
                commentMap.forEach((commentId, comment) -> {
                    lazyUserIdSet().add(comment.getUserId());
                    if (comment.getToUserId() != null) {
                        lazyUserIdSet().add(comment.getToUserId());
                    }
                });
            } else {
                commentMap = noteCommentService.getCommentListIn(new ArrayList<>(commentIdSet)).stream()
                        .filter(Objects::nonNull)
                        .peek(comment -> {
                            lazyUserIdSet().add(comment.getUserId());
                            if (comment.getToUserId() != null) {
                                lazyUserIdSet().add(comment.getToUserId());
                            }
                        })
                        .collect(Collectors.toMap(NoteComment::getId, c -> c));
            }
        }
    }

    private void requestCommentListByNoteIdSet() {
        if (commentListNoteIdSet != null && commentListNoteIdSet.size() != 0) {
            int pageSize = grain.getNeedCommentListSize();
            List<String> noteIdList = new ArrayList<>(commentListNoteIdSet);
            noteCommentListMap = grain.isAllowCache() ?
                    noteCommentService.getCommentListByNoteIdInAllowCache(noteIdList, 1, pageSize) :
                    noteCommentService.getCommentListByNoteIdIn(noteIdList, 1, pageSize);
            if (grain.isNeedCommentUserInfo()) {
                noteCommentListMap.forEach((noteId, commentList) -> {
                    for (NoteComment c : commentList) {
                        lazyUserIdSet().add(c.getUserId());
                        if (c.getToUserId() != null) {
                            lazyUserIdSet().add(c.getToUserId());
                        }
                    }
                });
            }
        }
    }

    private void requestUserByIdSet() {
        if (userIdSet != null && userIdSet.size() != 0) {
            userMap = userService.getBasicMapByIds(userIdSet).dataOrThrow();
        }
    }

    private void requestThreadByIdSet() {
        if (threadIdSet != null && threadIdSet.size() != 0) {
            List<NoteThread> threadList = grain.isAllowCache() ?
                    noteThreadService.getThreadListInAllowCache(new ArrayList<>(threadIdSet)) :
                    noteThreadService.getThreadListIn(new ArrayList<>(threadIdSet));
            threadMap = threadList.stream()
                    .filter(Objects::nonNull)
                    .collect(Collectors.toMap(NoteThread::getId, NoteConvertHelper::thread2model));
        }
    }

    private void requestUserPraiseListByNote() {
        if (praiseListByNoteList != null && praiseListByNoteList.size() != 0 && grain.getNeedPraiseListSize() != 0) {
            if (grain.isAllowCache()) {
                praiseListMap = notePraiseService.getPraiseUserListMapByNoteListAllowCache(
                        praiseListByNoteList, 1, grain.getNeedPraiseListSize());
            } else {
                praiseListMap = new HashMap<>(praiseListByNoteList.size());
                for (Note note : praiseListByNoteList) {
                    List<UserModel> praiseUserList = notePraiseService.getPraiseUserListByNoteId(
                            note.getId(), 1, grain.getNeedPraiseListSize()).getContent();
                    praiseListMap.put(note.getId(), praiseUserList);
                }
            }
        }
    }

    private void requestUserPraiseStateByIdSet() {
        if (praiseStateNoteList != null && praiseStateNoteList.size() != 0) {
            if (grain.isAllowCache()) {
                praiseStateMap = notePraiseService.isNoteListPraisedByUserAllowCache(praiseStateNoteList, relatedUserId);
            } else {
                praiseStateMap = notePraiseService.isNoteListPraisedByUser(praiseStateNoteList, relatedUserId);
            }
        }
    }

    private void requestUserBlacklist() {
        // 根据 flag 及请求结果确定是否需要用户黑名单数据
        if (relatedUserId == null) {
            userBlacklist = Collections.emptySet();
        } else if (!grain.isHonorBlacklist()) {
            userBlacklist = Collections.emptySet();
        } else if (!isAssembleNoteList && grain.getNeedCommentListSize() == 0 && !grain.isNeedStickyCommentData()) {
            // 用户只请求了单条帖子, 且不需要评论信息. 等同于不需要考虑黑名单.
            userBlacklist = Collections.emptySet();
        } else if (!isAssembleNoteList && (commentIdSet == null || commentIdSet.size() == 0)) {
            // 用户只请求了单条帖子, 且该帖没有评论信息. 等同于不需要考虑黑名单.
            userBlacklist = Collections.emptySet();
        } else if (isAssembleNoteList && (noteList == null || noteList.size() == 0)) {
            // 并未获取到帖子数据, 不需要请求黑名单.
            userBlacklist = Collections.emptySet();
        } else {
            userBlacklist = userService
                    .getUserBlacklist(relatedUserId, true)
                    .data().orElse(Collections.emptySet());
        }
    }

    private void assemble() {
        if (isEmpty(userMap) && isEmpty(threadMap) && isEmpty(threadMap)
                && isEmpty(praiseStateMap) && isEmpty(noteCommentListMap)) {
            return;
        }

        for (int i = resultList.size() - 1; i >= 0; i --) {
            NoteModel model = resultList.get(i);
            if (isAssembleNoteList && shouldRemoveAccordingToBlacklist(model)) {
                resultList.remove(i);
                continue;
            }

            Note note = getNoteById(model.getId());

            if (grain.isHonorAnonymous() && note.getIsAnonymous() != null && note.getIsAnonymous()) {
                model.setUser(emptyUserModel(null));
            } else if (grain.isNeedUserInfo()) {
                model.setUser(getUserFromMap(note.getUserId()));
            }

            if (grain.getNeedCommentListSize() > 0 && !isEmpty(noteCommentListMap)) {
                List<NoteComment> commentList = noteCommentListMap.get(model.getId());
                if (commentList != null) {
                    model.setCommentList(commentList.stream()
                            .filter(comment -> {
                                if (grain.isHonorAuthorOnly()
                                        && Objects.equals(comment.getState(), NoteState.AUTHOR_ONLY.getValue())) {
                                    return Objects.equals(comment.getUserId(), relatedUserId);
                                } else {
                                    return true;
                                }
                            })
                            .filter(comment -> !shouldRemoveAccordingToBlacklist(comment))
                            .map(this::comment2model)
                            .collect(Collectors.toList()));
                }
            }

            if (grain.isNeedStickyCommentData() && !isEmpty(commentMap)) {
                NoteComment stickyComment = commentMap.get(note.getStickyCommentId());
                if (stickyComment != null) {
                    if (grain.isHonorAuthorOnly()
                            && Objects.equals(stickyComment.getState(), NoteState.AUTHOR_ONLY.getValue())
                            && !Objects.equals(stickyComment.getUserId(), relatedUserId)) {
                        model.setStickyComment(null);
                    } else if (shouldRemoveAccordingToBlacklist(stickyComment)) {
                        model.setStickyComment(null);
                    } else {
                        model.setStickyComment(comment2model(stickyComment));
                    }
                }
            }

            if (grain.getNeedPraiseListSize() > 0 && note.getPraiseCount() != null
                    && note.getPraiseCount() > 0 && !isEmpty(praiseListMap)) {
                List<UserModel> praiseUserList = praiseListMap.get(note.getId());
                if (praiseUserList != null && praiseUserList.size() != 0) {
                    model.setPraiseList(praiseUserList);
                }
            }

            if (grain.isNeedThreads() && note.getThreads() != null && !isEmpty(threadMap)) {
                model.setThreads(note.getThreads().stream()
                        .map(tid -> threadMap.get(tid))
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList()));
            }

            if (grain.isNeedOriginThread() && note.getOriginThreadId() != null && !isEmpty(threadMap)) {
                model.setOriginThread(threadMap.get(note.getOriginThreadId()));
            }

            if (grain.isNeedTopAndHotThreads() && !isEmpty(threadMap)) {
                if (note.getTopThreads() != null) {
                    model.setTopThreads(note.getTopThreads().stream()
                            .map(tid -> threadMap.get(tid))
                            .filter(Objects::nonNull)
                            .collect(Collectors.toList()));
                }
                if (note.getHotThreads() != null) {
                    model.setHotThreads(note.getHotThreads().stream()
                            .map(tid -> threadMap.get(tid))
                            .filter(Objects::nonNull)
                            .collect(Collectors.toList()));
                }
            }

            if (relatedUserId != null && model.getIsPraised() == null && !isEmpty(praiseStateMap)) {
                model.setIsPraised(praiseStateMap.get(model.getId()));
            }

            // TODO: 此处为 workaround, 为解决 ios 开吧 6.1.0 版本 无图片尺寸无法正常显示帖子的问题, 新版本普及后应去掉这段代码
            if (note.getImages() != null) {
                for (Image image : note.getImages()) {
                    if (image.getHeight() == null || image.getHeight() == 0
                            || image.getWidth() == null || image.getWidth() == 0) {
                        image.setHeight(300);
                        image.setWidth(300);
                    }
                }
            }
        }
    }

    private static NoteModel preAssemble(Note note, NoteGrainFlag grain) {
        NoteModel model = new NoteModel();
        model.setId(note.getId());
        model.setSiteId(note.getSiteId());
        model.setSource(note.getSource());
        model.setUserId(note.getUserId());
        model.setOriginThreadId(note.getOriginThreadId());
        model.setThreadIds(note.getThreads());
        model.setStickyCommentId(note.getStickyCommentId());
        model.setCreateTime(note.getCreateTime());
        model.setRefreshTime(note.getRefreshTime());
        model.setUpdateTime(note.getUpdateTime());
        model.setState(NoteConverter.getNoteState(note));
        model.setIsSoftDeleted(NoteConverter.isNoteSoftDeleted(note));
        model.setIsAnonymous(note.getIsAnonymous());
        model.setIsAllowComment(note.getIsAllowComment());
        model.setIsAllowPraise(note.getIsAllowPraise());
        model.setCreateTimeMS(note.getCreateTimeMS());
        model.setRefreshTimeMS(note.getRefreshTimeMS());

        if (grain.isHideContentOnDelete() && NoteConverter.isNoteSoftDeleted(note)) {
            model.setContent(NoteDefaults.REPLACE_CONTENT_ON_DELETE);
        } else if (grain.isNeedContentAsText()) {
            model.setContent(note.contentAsString());
        } else if (grain.isNeedContent()) {
            model.setContent(note.getContent());
            model.setAudio(note.getAudio());
            model.setVideo(note.getVideo());
            model.setMediaId(note.getMediaId());
            model.setGallery(note.getGallery());
            model.setRemark(note.getRemark());
            if (note.getImages() != null && note.getImages().size() != 0) {
                model.setImages(note.getImages());
            }
        }

        if (grain.isNeedLocation()) {
            model.setLatitude(note.getLatitude());
            model.setLongitude(note.getLongitude());
            model.setStreet(note.getStreet());
        }

        if (grain.isNeedLinks()) {
            model.setLinks(note.getLinks());
        }

        if (grain.isNeedCountData()) {
            if (note.getCommentCount() != null) {
                model.setCommentCount(note.getCommentCount() > 0 ? note.getCommentCount() : 0);
            }
            if (note.getPraiseCount() != null) {
                model.setPraiseCount(note.getPraiseCount() > 0 ? note.getPraiseCount() : 0);
            }
            if (note.getViewCount() != null) {
                model.setViewCount(note.getViewCount() > 0 ? note.getViewCount() : 0);
            }
            if (note.getShareCount() != null) {
                model.setShareCount(note.getShareCount() > 0 ? note.getShareCount() : 0);
            }
        }

        if (grain.isNeedExtra()) {
            model.setExtra(note.getExtra());
        }

        return model;
    }

    private Set<Integer> lazyUserIdSet() {
        if (userIdSet == null) {
            userIdSet = new HashSet<>();
        }
        return userIdSet;
    }

    private Set<String> lazyThreadIdSet() {
        if (threadIdSet == null) {
            threadIdSet = new HashSet<>();
        }
        return threadIdSet;
    }

    private Set<String> lazyCommentIdSet() {
        if (commentIdSet == null) {
            commentIdSet = new HashSet<>();
        }
        return commentIdSet;
    }

    private Set<String> lazyCommentListNoteIdSet() {
        if (commentListNoteIdSet == null) {
            commentListNoteIdSet = new HashSet<>();
        }
        return commentListNoteIdSet;
    }

    private List<Note> lazyPraiseStateNoteList() {
        if (praiseStateNoteList == null) {
            praiseStateNoteList = new LinkedList<>();
        }
        return praiseStateNoteList;
    }

    private List<Note> lazyPraiseListByNoteList() {
        if (praiseListByNoteList == null) {
            praiseListByNoteList = new LinkedList<>();
        }
        return praiseListByNoteList;
    }

    private boolean shouldRemoveAccordingToBlacklist(NoteModel model) {
        if (userBlacklist == null || userBlacklist.size() == 0) {
            return false;
        } else {
            return userBlacklist.contains(model.getUserId());
        }
    }

    private boolean shouldRemoveAccordingToBlacklist(NoteComment comment) {
        if (userBlacklist == null || userBlacklist.size() == 0) {
            return false;
        } else {
            return userBlacklist.contains(comment.getUserId());
        }
    }

    private UserModel getUserFromMap(Integer userId) {
        if (null == userMap || userMap.size() == 0) {
            return emptyUserModel(userId);
        } else {
            UserModel u = userMap.get(userId);
            if (u == null) {
                return emptyUserModel(userId);
            } else {
                u.setSeries(null);
                u.setSiteId(null);
                u.setSource(null);
                return u;
            }
        }
    }

    private Note getNoteById(String id) {
        if (noteMap == null) {
            return noteList.get(0);
        } else {
            return noteMap.get(id);
        }
    }

    private NoteCommentModel comment2model(NoteComment comment) {
        NoteCommentModel cm = new NoteCommentModel();
        cm.setId(comment.getId());
        cm.setNoteId(comment.getNoteId());
        cm.setCreateTime(comment.getCreateTime());
        cm.setToCommentId(comment.getToCommentId());
        cm.setUser(getUserFromMap(comment.getUserId()));
        cm.setLink(comment.getLink());
        if (comment.getToUserId() != null) {
            cm.setToUser(getUserFromMap(comment.getToUserId()));
        }
        cm.setIsSoftDeleted(NoteConverter.isCommentSoftDeleted(comment));
        cm.setState(NoteConverter.getCommentState(comment));
        if (grain.isHideContentOnDelete() && NoteConverter.isCommentSoftDeleted(comment)) {
            cm.setContent(NoteDefaults.REPLACE_CONTENT_ON_DELETE);
        } else {
            cm.setContent(comment.getContent());
        }
        return cm;
    }

    private static NoteThreadModel thread2model(NoteThread thread) {
        NoteThreadModel model = new NoteThreadModel();
        model.setId(thread.getId());
        model.setTitle(thread.getTitle());
        model.setAttr(thread.getAttr());
        return model;
    }

    private static boolean isEmpty(Map<?, ?> map) {
        return null == map || map.isEmpty();
    }

    private static UserModel emptyUserModel(Integer userId) {
        UserModel user = new UserModel(userId);
        user.setUserName("匿名用户");
        return user;
    }

}
