package com.kaiba.m.core.service.appcomponent.lego;

import com.kaiba.lib.base.assembler.Mapper;
import com.kaiba.lib.base.constant.KbModule;
import com.kaiba.lib.base.constant.applego.AppComponentState;
import com.kaiba.lib.base.constant.applego.WidgetType;
import com.kaiba.lib.base.domain.applego.IWidget;
import com.kaiba.lib.base.domain.applego.lego.*;
import com.kaiba.lib.base.middleware.adminlogrecorder.AdminLogRecorder;
import com.kaiba.lib.base.response.KbCode;
import com.kaiba.lib.base.response.KbException;
import com.kaiba.lib.base.service.IAdminLogService;
import com.kaiba.m.core.domain.applego.*;
import com.kaiba.m.core.repository.applego.*;
import com.kaiba.m.core.util.PageUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * author: lyux
 * date: 2023-07-03
 */
@Slf4j
@Service
public class AppLegoService {

    private final LegoDraftRepository draftRepository;
    private final LegoPageRepository pageRepository;
    private final LegoLayoutRepository layoutRepository;
    private final LegoRowRepository rowRepository;
    private final LegoBlockRepository blockRepository;
    private final AppWidgetService widgetService;
    private final AdminLogRecorder adminLogRecorder;

    public AppLegoService(
            LegoDraftRepository draftRepository,
            LegoPageRepository pageRepository,
            LegoLayoutRepository layoutRepository,
            LegoRowRepository rowRepository,
            LegoBlockRepository blockRepository,
            AppWidgetService widgetService,
            IAdminLogService adminLogService
    ) {
        this.draftRepository = draftRepository;
        this.pageRepository = pageRepository;
        this.layoutRepository = layoutRepository;
        this.rowRepository = rowRepository;
        this.blockRepository = blockRepository;
        this.widgetService = widgetService;
        this.adminLogRecorder = AdminLogRecorder
                .builder(adminLogService)
                .module(KbModule.APP_LEGO)
                .unit(AppLegoConsts.LOG_UNIT_PAGE, "组件化页面管理")
                .create();
    }

    public LegoDraft saveDraft(LegoPageModel model) {
        long now = System.currentTimeMillis();
        LegoDraft draft = new LegoDraft();
        draft.setId(model.getId());
        draft.setScene(model.getScene());
        draft.setDraft(model);
        draft.setUpdateTime(now);
        if (draft.getId() == null) {
            draft.setCreateTime(now);
            LegoDraft created = draftRepository.insert(draft);
            adminLogRecorder.on().act("draft_create", "创建组件化草稿").ref1(created.getId()).add();
            return created;
        } else {
            LegoDraft updated = draftRepository.save(draft);
            adminLogRecorder.on().act("draft_update", "更新组件化草稿").ref1(updated.getId()).add();
            return updated;
        }
    }

    public void deleteDraft(String draftId) {
        draftRepository.deleteById(draftId);
        adminLogRecorder.on().act("draft_delete", "删除组件化草稿").ref1(draftId).add();
    }

    public Optional<LegoDraft> getDraftById(String draftId) {
        return draftRepository.findById(draftId).map(draft -> {
            draft.getDraft().setId(draftId);
            draft.getDraft().setScene(draft.getScene());
            return draft;
        });
    }

    public Page<LegoDraft> getDraftListBySite(Integer siteId, String scene, Integer page, Integer pageSize) {
        Pageable pageable = PageUtils.ofDefault(page, pageSize);
        return draftRepository.findBySiteIdAndSceneOrderByUpdateTimeDesc(siteId, scene, pageable).map(draft -> {
            draft.getDraft().setId(draft.getId());
            draft.getDraft().setScene(draft.getScene());
            return draft;
        });
    }

    // ------------------------------------------------------------------------

    public LegoPage createBetaPage(LegoPageModel model) {
        AppLegoCreationWrapper wrapper = new AppLegoCreationWrapper(model);
        adminLogRecorder.on().act("create_begin", "开始创建内测组件化").add();

        // check and create blocks
        if (wrapper.getBlockCreateList().size() != 0) {
            for (LegoBlockModel block : wrapper.getBlockCreateList()) {
                WidgetType widgetType = WidgetType.resolveByName(block.getWidgetType()).orElseThrow(() ->
                        new KbException(KbCode.ILLEGAL_ARGUMENT, "unknown widget type: " + block.getWidgetType())
                                .r("未知的控件类型").li());
                LegoBlock b = Mapper.map(block, LegoBlock.class);
                b.setCreateTime(System.currentTimeMillis());
                String blockId = blockRepository.insert(b).getId();
                block.setId(blockId);
                if (widgetType.isRequireId()) {
                    IWidget widget = widgetService.createWidgetData(blockId, widgetType, block.getWidgetData());
                    wrapper.addWidgetData(widget);
                }
            }
        }
        wrapper.collectBlockIds();

        // check and create rows
        if (wrapper.getRowCreateList().size() != 0) {
            for (LegoRowModel row : wrapper.getRowCreateList()) {
                LegoRow r = Mapper.map(row, LegoRow.class);
                r.setCreateTime(System.currentTimeMillis());
                String rowId = rowRepository.insert(r).getId();
                row.setId(rowId);
            }
        }
        wrapper.collectRowIds();

        // check and create layouts
        if (wrapper.getLayoutCreateList().size() != 0) {
            for (LegoLayoutModel layout : wrapper.getLayoutCreateList()) {
                LegoLayout l = Mapper.map(layout, LegoLayout.class);
                l.setCreateTime(System.currentTimeMillis());
                String layoutId = layoutRepository.insert(l).getId();
                layout.setId(layoutId);
            }
        }
        wrapper.collectLayoutIds();

        // assemble tab data
        LegoPage page = Mapper.map(model, LegoPage.class);
        if (model.getTabLayouts() != null) {
            page.setTabs(new LinkedList<>());
            page.setTabLayoutIds(new LinkedList<>());
            int tabSize = model.getTabs() == null ? 0 : model.getTabs().size();
            for (int i = 0; i < model.getTabLayouts().size(); i ++) {
                LegoLayoutModel tabLayout = model.getTabLayouts().get(i);
                LegoLayoutTab tab;
                if (i < tabSize) {
                    tab = model.getTabs().get(i);
                    tab.setLayoutId(tabLayout.getId());
                    if (tab.getSelected() != null && tab.getSelected()) {
                        model.setTabSelectedId(tabLayout.getId());
                    }
                } else {
                    tab = new LegoLayoutTab();
                    tab.setLayoutId(tabLayout.getId());
                    tab.setSTitle(tabLayout.getName());
                    tab.setNTitle(tabLayout.getName());
                }
                page.getTabLayoutIds().add(tabLayout.getId());
                page.getTabs().add(tab);
            }
        }

        // persistent page data
        long now = System.currentTimeMillis();
        page.setRefreshTime(now);
        page.setCreateTime(now);
        page.setState(AppComponentState.BETA.getValue());
        LegoPage created = pageRepository.insert(page);
        adminLogRecorder.on().act("create_done", "创建内测组件化完成").ref1(created.getId()).add();
        model.setId(created.getId());
        return created;
    }

    public LegoPage updatePageStateAsOnline(String pageId) {
        LegoPage page = pageRepository.findById(pageId).orElseThrow(() ->
                new KbException(KbCode.RESOURCE_NOT_FOUND).r("页面不存在").li());
        if (page.getState() == AppComponentState.ONLINE.getValue()) {
            throw new KbException(KbCode.ILLEGAL_STATE, "already online").r("页面已上线, 请勿重复操作").li();
        } else if (page.getState() == AppComponentState.DRAFT.getValue()) {
            throw new KbException(KbCode.ILLEGAL_STATE, "there should be no draft").r("页面状态错误").li();
        }
        LegoPage updated = pageRepository.updateState(pageId, AppComponentState.ONLINE);
        adminLogRecorder.on().act("update_online", "组件化上线").ref1(updated.getId()).add();
        return updated;
    }

    public LegoPage updatePageRefreshTime(LegoPage page) {
        LegoPage updated = pageRepository.updateRefreshTime(page.getId());
        adminLogRecorder.on().act("update_refresh", "组件化刷新上线").ref1(updated.getId()).add();
        return updated;
    }

    public LegoPage updatePageDescription(String pageId, String description) {
        pageRepository.findById(pageId).orElseThrow(() ->
                new KbException(KbCode.RESOURCE_NOT_FOUND).r("页面不存在").li());
        LegoPage updated = pageRepository.updateDescription(pageId, description);
        adminLogRecorder.on().act("update_description", "组件化更改描述").ref1(updated.getId()).add();
        return updated;
    }

    public void deleteBetaPage(String pageId) {
        LegoPage page = pageRepository.findById(pageId).orElseThrow(() ->
                new KbException(KbCode.RESOURCE_NOT_FOUND).r("页面不存在").li());
        if (page.getState() == AppComponentState.ONLINE.getValue()) {
            throw new KbException(KbCode.ILLEGAL_STATE, "delete online page not allowed").r("已上线页面不允许删除").li();
        }
        adminLogRecorder.on().act("delete_beta", "删除内侧组件化").ref1(pageId).add();
        pageRepository.deleteById(pageId);
    }

    public Optional<LegoPage> getPageById(String pageId) {
        return pageRepository.findById(pageId);
    }

    public Optional<LegoPage> getLatestPageBySiteIdAndScene(Integer siteId, String scene, Integer state) {
        return pageRepository.findFirstBySiteIdAndSceneAndStateOrderByRefreshTimeDesc(siteId, scene, state);
    }

    public Page<LegoPage> getPageListBySiteIdAndSceneAndState(
            Integer siteId, String scene, Integer state, Integer page, Integer pageSize) {
        Pageable pageable = PageUtils.ofDefault(page, pageSize);
        if (state == null) {
            return pageRepository.findBySiteIdAndSceneOrderByRefreshTimeDesc(siteId, scene, pageable);
        } else {
            return pageRepository.findBySiteIdAndSceneAndStateOrderByRefreshTimeDesc(siteId, scene, state, pageable);
        }
    }

    // ------------------------------------------------------------------------

    public AppLegoModelWrapper getPageModelById(String pageId) {
        LegoPage page =  pageRepository.findById(pageId)
                .orElseThrow(() -> new KbException(KbCode.RESOURCE_NOT_FOUND).r("页面不存在").li());
        return getPageModelByPage(page);
    }

    public AppLegoModelWrapper getPageModelByPage(LegoPage page) {
        AppLegoModelWrapper wrapper = new AppLegoModelWrapper(page);
        switch (wrapper.structure) {
            case SEQUENCE:
                wrapper.layoutMap.put(wrapper.model.getLayoutId(), null);
                break;
            case TAB:
                wrapper.model.getTabLayoutIds().forEach(layoutId -> wrapper.layoutMap.put(layoutId, null));
                break;
            case SEQ_TAB:
                wrapper.layoutMap.put(wrapper.model.getLayoutId(), null);
                wrapper.model.getTabLayoutIds().forEach(layoutId -> wrapper.layoutMap.put(layoutId, null));
                break;
        }
        layoutRepository.findByIdIn(wrapper.layoutMap.keySet()).forEach(layout -> {
            wrapper.layoutMap.put(layout.getId(), Mapper.map(layout, LegoLayoutModel.class));
            layout.getRowIds().forEach(rowId -> wrapper.rowMap.put(rowId, null));
        });
        rowRepository.findByIdIn(wrapper.rowMap.keySet()).forEach(row -> {
            wrapper.rowMap.put(row.getId(), Mapper.map(row, LegoRowModel.class));
            if (row.getBlockIds() != null) {
                row.getBlockIds().forEach(blockId -> wrapper.blockMap.put(blockId, null));
            }
        });
        if (wrapper.blockMap.size() > 0) {
            blockRepository.findByIdIn(wrapper.blockMap.keySet()).forEach(block -> {
                LegoBlockModel m = Mapper.map(block, LegoBlockModel.class);
                wrapper.blockMap.put(block.getId(), m);
                if (block.getWidgetType() != null) {
                    WidgetType type = WIDGET_TYPE_MAP.get(block.getWidgetType());
                    if (type == null) {
                        log.error("unknown widget type: " + type);
                    } else if (type.isRequireId()) {
                        wrapper.widgetKeySet.add(new AppLegoModelWrapper.WidgetKey(type, m));
                    }
                }
            });
        }
        wrapper.assemble();
        return wrapper;
    }

    // ------------------------------------------------------------------------

    public Optional<LegoLayout> getLayoutById(String layoutId) {
        return layoutRepository.findById(layoutId);
    }

    public List<LegoLayout> getLayoutListByIdIn(Collection<String> layoutIds) {
        return layoutRepository.findByIdIn(layoutIds);
    }

    // ------------------------------------------------------------------------

    public Optional<LegoRow> getRowById(String rowId) {
        return rowRepository.findById(rowId);
    }

    public List<LegoRow> getRowListByIdIn(Collection<String> rowIds) {
        return rowRepository.findByIdIn(rowIds);
    }

    // ------------------------------------------------------------------------

    public Optional<LegoBlock> getBlockById(String blockId) {
        return blockRepository.findById(blockId);
    }

    public List<LegoBlock> getBlockListByIdIn(Collection<String> blockIds) {
        return blockRepository.findByIdIn(blockIds);
    }

    // ------------------------------------------------------------------------

    private static final Map<String, WidgetType> WIDGET_TYPE_MAP = Arrays.stream(WidgetType.values())
            .collect(Collectors.toMap(WidgetType::name, w -> w));

}
