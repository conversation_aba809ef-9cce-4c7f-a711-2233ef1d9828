package com.kaiba.m.core.service.appversion;

import com.kaiba.lib.base.constant.KbEndpoint;
import com.kaiba.lib.base.constant.Values;
import com.kaiba.lib.base.constant.appversion.AppUpdatePolicy;
import com.kaiba.lib.base.constant.appversion.AppVersionRouteState;
import com.kaiba.lib.base.constant.appversion.AppVersionState;
import com.kaiba.lib.base.lang.verifier.F;
import com.kaiba.lib.base.lang.verifier.Verifier;
import com.kaiba.lib.base.lang.verifier.VerifierBuilder;
import com.kaiba.lib.base.lang.verifier.VerifyResult;
import com.kaiba.lib.base.response.KbCode;
import com.kaiba.lib.base.response.KbException;
import com.kaiba.lib.base.util.AppVersionUtils;
import com.kaiba.m.core.domain.appversion.AppVersion;
import com.kaiba.m.core.domain.appversion.AppVersionRoute;
import lombok.extern.slf4j.Slf4j;

import java.util.Arrays;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * author: lyux
 * date: 2023-04-13
 */
@Slf4j
public class AppVersionModelHelper {

    private static final Map<Integer, String> ANDROID_EP_PACKAGE_MAP = Arrays.stream(KbEndpoint.values())
            .filter(ep -> Objects.nonNull(ep.getAndroidPackageName()))
            .collect(Collectors.toMap(KbEndpoint::getValue, KbEndpoint::getAndroidPackageName, (v1, v2) -> v1));

    private static final Map<Integer, String> IOS_EP_PACKAGE_MAP = Arrays.stream(KbEndpoint.values())
            .filter(ep -> Objects.nonNull(ep.getIosBundleId()))
            .collect(Collectors.toMap(KbEndpoint::getValue, KbEndpoint::getIosBundleId, (v1, v2) -> v1));

    public static String getPackageIdByEndpoint(Integer endpoint, String deviceType) {
        if (endpoint == null || deviceType == null) {
            return null;
        } else if (Values.DEVICE_ANDROID.equals(deviceType)) {
            return ANDROID_EP_PACKAGE_MAP.get(endpoint);
        } else if (Values.DEVICE_IOS.equals(deviceType)) {
            return IOS_EP_PACKAGE_MAP.get(endpoint);
        } else {
            return null;
        }
    }

    public static String getDeviceTypeByPackageId(String packageId) {
        if (packageId == null) {
            return null;
        }
        KbEndpoint[] endpoints = KbEndpoint.values();
        for (KbEndpoint endpoint : endpoints) {
            if (packageId.equals(endpoint.getIosBundleId())) {
                return Values.DEVICE_IOS;
            } else if (packageId.equals(endpoint.getAndroidPackageName())) {
                return Values.DEVICE_ANDROID;
            } else if (packageId.equals(endpoint.getOhosPackageId())) {
                return Values.DEVICE_OHOS;
            }
        }
        return null;
    }

    // ---------------------------------------------------------------

    static final Verifier<AppVersion> VERSION_VERIFIER = new VerifierBuilder<AppVersion>().defaultOrElseThrow()
            .and(F.str(AppVersion::getPackageId).notNull())
            .and(AppVersionModelHelper::verifyVersionCode)
            .and(F.str(AppVersion::getDevice).enums(new String[] { "Android", "iOS", "ohos" }))
            .and(F.intF(AppVersion::getState).enums(AppVersionState.values()))
            .and(F.str(AppVersion::getContent).notEmpty())
            .ifNotNull(F.str(AppVersion::getDisplayUrl).isUrl())
            .ifNotNull(F.str(AppVersion::getPackageUrl).isUrl())
            .create();

    static final Verifier<AppVersionRoute> VERSION_ROUTE_VERIFIER = new VerifierBuilder<AppVersionRoute>().defaultOrElseThrow()
            .and(F.str(AppVersionRoute::getPackageId).notNull())
            .and(F.intF(AppVersionRoute::getOriginCode).notNull())
            .and(F.intF(AppVersionRoute::getTargetCode).notNull())
            .and(F.intF(AppVersionRoute::getState).enums(AppVersionRouteState.values()))
            .and(F.intF(AppVersionRoute::getPolicy).enums(AppUpdatePolicy.values()))
            .create();

    private static VerifyResult<AppVersion> verifyVersionCode(AppVersion version) {
        if (version.getCode() == null && version.getName() == null) {
            throw new KbException(KbCode.ILLEGAL_ARGUMENT, "no code nor name").r("必须提供版本号或版本名").li();
        }
        if (version.getCode() != null && version.getName() == null) {
            try {
                String versionName = AppVersionUtils.getVersionNameByCode(version.getCode());
                version.setName(versionName);
                return VerifyResult.ok();
            } catch (IllegalArgumentException e) {
                throw new KbException(KbCode.ILLEGAL_ARGUMENT, "version code parse fail: " + version.getCode(), e)
                        .r("错误的版本号").li();
            }
        }
        if (version.getName() != null && version.getCode() == null) {
            try {
                int versionCode = AppVersionUtils.getVersionCodeByName(version.getName());
                version.setCode(versionCode);
                return VerifyResult.ok();
            } catch (IllegalArgumentException e) {
                throw new KbException(KbCode.ILLEGAL_ARGUMENT, "version name parse fail 1: " + version.getName(), e)
                        .r("错误的版本名").li();
            }
        }
        if (version.getCode() != null && version.getName() != null) {
            try {
                int versionCode = AppVersionUtils.getVersionCodeByName(version.getName());
                if (version.getCode() == versionCode) {
                    return VerifyResult.ok();
                } else {
                    throw new KbException(KbCode.ILLEGAL_ARGUMENT,
                            "version code and name mismatch: " + version.getCode() + " -> " + version.getName())
                            .r("错误的版本名").li();
                }
            } catch (IllegalArgumentException e) {
                throw new KbException(KbCode.ILLEGAL_ARGUMENT, "version name parse fail 2: " + version.getName(), e)
                        .r("错误的版本名").li();
            }
        }
        return VerifyResult.ok();
    }

}
