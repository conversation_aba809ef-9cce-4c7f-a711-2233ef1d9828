package com.kaiba.m.core.service.user;

import com.aliyuncs.DefaultAcsClient;
import com.aliyuncs.IAcsClient;
import com.aliyuncs.afs.model.v20180112.AnalyzeNvcRequest;
import com.aliyuncs.afs.model.v20180112.AnalyzeNvcResponse;
import com.aliyuncs.afs.model.v20180112.AuthenticateSigRequest;
import com.aliyuncs.afs.model.v20180112.AuthenticateSigResponse;
import com.aliyuncs.exceptions.ClientException;
import com.aliyuncs.profile.DefaultProfile;
import com.google.gson.JsonSyntaxException;
import com.kaiba.lib.base.constant.KbHttpHeaders;
import com.kaiba.lib.base.constant.user.LoginCMSResultCode;
import com.kaiba.lib.base.domain.user.LoginCMSResult;
import com.kaiba.lib.base.util.ServletRequestUtils;
import com.kaiba.lib.base.util.StringUtils;
import com.kaiba.m.core.util.JsonUtils;
import lombok.Data;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;

/**
 * author: fuj, lyux
 * date: 2023-03-13
 */
@Slf4j
@Component
public class AliAcsChecker {

    private static final String APP_KEY = "FFFF0N0N00000000B39A";
    private static final String SCENE_LOGIN = "nc_login";

    private final IAcsClient client;

    public AliAcsChecker(
            @Value("${ali.afs.access_key_id}") String afsAccessKey,
            @Value("${ali.afs.access_key_secret}") String afsAccessSecret,
            @Value("${ali.afs.region_id}") String afsRegionId,
            @Value("${ali.afs.product}") String afsProduct,
            @Value("${ali.afs.endpoint}") String afsEndPoint
    ) {
        this.client = new DefaultAcsClient(DefaultProfile.getProfile(afsRegionId, afsAccessKey, afsAccessSecret));
        DefaultProfile.addEndpoint(afsRegionId, afsProduct, afsEndPoint);
    }

    // -----------------------------------------------

    public LoginCMSResult checkAcs(String awcs) {
        if (StringUtils.isEmpty(awcs)) {
            return new LoginCMSResult(LoginCMSResultCode.ACS_NOT_PRESENT);
        }
        AliAcsChecker.AliAcsCode aliAcsCode = requestAcs(awcs);
        switch (aliAcsCode) {
            case FURTHER_VERIFY_BY_SLIDE:
            case FURTHER_VERIFY_BY_SCRATCH:
            case FURTHER_VERIFY_BY_CODE: {
                LoginCMSResult result = new LoginCMSResult(LoginCMSResultCode.ACS_SUSPECT);
                result.setAliACSCode(aliAcsCode.getCode());
                return result;
            }
            case FAIL_DIRECT:
            case FAIL_CONFIRM: {
                LoginCMSResult result = new LoginCMSResult(LoginCMSResultCode.ACS_BLOCKED);
                result.setAliACSCode(aliAcsCode.getCode());
                return result;
            }
            case ERROR:
                log.info("multi vote with afs encounter error");
            case PASS_CONFIRM:
            case PASS_DIRECT:
            default:
                // 防水墙调用失败时, 暂时按照通过处理
                LoginCMSResult result = new LoginCMSResult(LoginCMSResultCode.SUCCESS);
                result.setAliACSCode(aliAcsCode.getCode());
                return result;
        }
    }

    public LoginCMSResult checkSig(String awcs) {
        if (awcs == null) {
            return new LoginCMSResult(LoginCMSResultCode.ACS_NOT_PRESENT);
        }
        final HttpServletRequest request = ServletRequestUtils.getCurrentRequest();
        if (request == null) {
            return new LoginCMSResult(LoginCMSResultCode.CONTEXT_INVALID);
        }
        String requestIp = request.getHeader(KbHttpHeaders.KB_IP.getHeaderName());
        if (requestIp == null) {
            requestIp = request.getHeader("X-FORWARDED-FOR");
        }
        if (StringUtils.isEmpty(requestIp)) {
            requestIp = request.getHeader("remoteip");
        }
        if (requestIp == null) {
            return new LoginCMSResult(LoginCMSResultCode.IP_NOT_FOUND);
        }
        try {
            AliAcsChecker.AliSigModel model = JsonUtils.getGson().fromJson(awcs, AliAcsChecker.AliSigModel.class);
            AliAcsCode code = requestSig(model.getSessionId(), model.getSig(), model.getToken(), requestIp);
            LoginCMSResult result = new LoginCMSResult();
            result.setAliACSCode(code.getCode());
            if (code == AliAcsChecker.AliAcsCode.PASS_CONFIRM) {
                result.setCode(LoginCMSResultCode.SUCCESS.getValue());
            } else {
                result.setCode(LoginCMSResultCode.FAIL.getValue());
            }
            return result;
        } catch (JsonSyntaxException e) {
            log.error("request sig, param malformed: " + awcs, e);
            return new LoginCMSResult(LoginCMSResultCode.ACS_SIG_INVALID);
        } catch (ClientException e) {
            log.error("request sig, http fail: " + awcs, e);
            return new LoginCMSResult(LoginCMSResultCode.ACS_REQUEST_FAIL);
        }
    }

    private AliAcsCode requestAcs(String nvcVal) {
        AnalyzeNvcRequest request = new AnalyzeNvcRequest();
        request.setData(nvcVal);
        request.setScoreJsonStr("{\"200\":\"PASS\",\"400\":\"NC\",\"600\":\"SC\",\"700\":\"LC\",\"800\":\"BLOCK\"}");
        try {
            AnalyzeNvcResponse response = client.getAcsResponse(request);
            return AliAcsCode.resolveByValue(response.getBizCode());
        } catch (Exception e) {
            log.error("request ali acs fail", e);
            return AliAcsCode.PASS_DIRECT;
        }
    }

    private AliAcsCode requestSig(String sessionId, String sig, String token, String remoteIp) throws ClientException {
        AuthenticateSigRequest request = new AuthenticateSigRequest();
        request.setSessionId(sessionId);
        request.setSig(sig);
        request.setToken(token);
        request.setScene(SCENE_LOGIN);
        request.setAppKey(APP_KEY);
        request.setRemoteIp(remoteIp);
        try {
            AuthenticateSigResponse response = client.getAcsResponse(request);
            return AliAcsCode.resolveByCode(response.getCode());
        } catch (Exception e) {
            log.error("request ali acs fail", e);
            return AliAcsCode.PASS_DIRECT;
        }
    }

    @Data
    public static class AliSigModel {
        private String sig;
        private String token;
        private String sessionId;
    }

    @Getter
    public enum AliAcsCode {
        /** 阿里验证码 */
        PASS_CONFIRM(100, "二次验签通过"),
        PASS_DIRECT(200, "无痕验证直接通过"),
        FURTHER_VERIFY_BY_SLIDE(400, "前端弹出nc滑动验证"),
        FURTHER_VERIFY_BY_SCRATCH(600, "前端弹出sc刮刮卡验证"),
        FURTHER_VERIFY_BY_CODE(700, "前端弹出lc验证码验证"),
        FAIL_DIRECT(800, "直接拦截"),
        FAIL_CONFIRM(900, "二次验签失败"),
        ERROR(-1, "验证出错"),
        ;

        private final int code;
        private final String codeStr;
        private final String desc;

        AliAcsCode(Integer code, String desc) {
            this.code = code;
            this.desc = desc;
            this.codeStr = Integer.toString(code);
        }

        private static AliAcsCode resolveByValue(String value) {
            if (null == value) {
                return ERROR;
            }
            for (AliAcsCode v : values()) {
                if (value.equals(v.getCodeStr())) {
                    return v;
                }
            }
            return ERROR;
        }

        private static AliAcsCode resolveByCode(Integer code) {
            if (null == code) {
                return ERROR;
            }
            for (AliAcsCode v : values()) {
                if (code.equals(v.getCode())) {
                    return v;
                }
            }
            return ERROR;
        }

    }

}
