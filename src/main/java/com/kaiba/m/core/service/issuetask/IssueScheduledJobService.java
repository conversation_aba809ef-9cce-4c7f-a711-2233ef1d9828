package com.kaiba.m.core.service.issuetask;

import com.kaiba.m.core.service.issuetask.expert.WorkingExpertService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * author: lyux
 * date: 2020-08-26
 */
@Slf4j
@Component
public class IssueScheduledJobService {

    private final IssueSummarizeService issueSummarizeService;
    private final IssueExpertSummarizeService issueExpertSummarizeService;
    private final WorkingExpertService workingExpertService;

    public IssueScheduledJobService(
            IssueSummarizeService issueSummarizeService,
            IssueExpertSummarizeService issueExpertSummarizeService,
            WorkingExpertService workingExpertService
    ) {
        this.issueSummarizeService = issueSummarizeService;
        this.issueExpertSummarizeService = issueExpertSummarizeService;
        this.workingExpertService = workingExpertService;
    }

    // every 10 minute
    @XxlJob("issue-check-trim-experts")
    public ReturnT<String> jobIssueCheckTrimExperts(String param) {
        workingExpertService.checkTrimExperts();
        return ReturnT.SUCCESS;
    }

    // every 5 minute
    @XxlJob("issue-check-active-issues")
    public ReturnT<String> jobIssueCheckActiveIssues(String param) {
        issueSummarizeService.checkActiveIssues();
        return ReturnT.SUCCESS;
    }

    // 00:30 every day
    @XxlJob("issue-summarize-experts")
    public ReturnT<String> jobIssueSummarizeExperts(String param) {
        issueExpertSummarizeService.summarizeExpertAll();
        return ReturnT.SUCCESS;
    }

}
