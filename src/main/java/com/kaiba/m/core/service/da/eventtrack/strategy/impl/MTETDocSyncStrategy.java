package com.kaiba.m.core.service.da.eventtrack.strategy.impl;

import com.kaiba.lib.base.constant.KbModule;
import com.kaiba.lib.base.constant.news.NewsChannel;
import com.kaiba.lib.base.domain.common.KbTimeRange;
import com.kaiba.lib.base.domain.minitheatre.MTEpisodeModel;
import com.kaiba.lib.base.domain.minitheatre.MTTheatreModel;
import com.kaiba.lib.base.response.KbEntity;
import com.kaiba.lib.base.service.IMiniTheatreService;
import com.kaiba.m.core.constant.da.eventtrack.ETDocSyncStrategyType;
import com.kaiba.m.core.domain.da.eventtrack.ETDoc;
import com.kaiba.m.core.repository.da.eventtrack.ETDocRepository;
import com.kaiba.m.core.repository.da.eventtrack.ETDocSyncCursorRepository;
import com.kaiba.m.core.service.da.eventtrack.ETDocModelHelper;
import com.kaiba.m.core.service.da.eventtrack.strategy.model.ETDocSyncQueryModel;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @version MTETDocSyncStrategy, v0.1 2025/4/9 15:57 daopei Exp $
 **/
@Service
public class MTETDocSyncStrategy extends AbstractETDocSyncStrategy{

    private final IMiniTheatreService miniTheatreService;
    private final static Set<String> default_modules = Collections.singleton("da_mini_theatre");


    public MTETDocSyncStrategy(
            ETDocRepository etDocRepository,
            ETDocSyncCursorRepository etDocSyncCursorRepository,
            IMiniTheatreService miniTheatreService
    ) {
        super(etDocRepository, etDocSyncCursorRepository);
        this.miniTheatreService  = miniTheatreService;
    }

    @Override
    public void syncAll() {
        initDocByPageExecutor(this::execute, null);
    }

    @Override
    public void syncByTimeRange(KbTimeRange time) {
        initDocByPageExecutor(this::execute, time);
    }

    @Override
    public String getStrategy() {
        return ETDocSyncStrategyType.MINI_THEATRE.name();
    }


    private boolean execute(ETDocSyncQueryModel query) {
        Long baseTime = query.getTimeRange() == null || query.getTimeRange().getLower() == null ?
                0L : query.getTimeRange().getLower();
        KbEntity<List<MTTheatreModel>> kbRes = miniTheatreService.getTheatreList(null, query.getPage(), query.getPageSize());
        List<MTTheatreModel> list = kbRes.dataOrThrow();

        for (MTTheatreModel mtTheatreModel : list) {
            if (mtTheatreModel.getUpdateTime() < baseTime) {
                continue;
            }
            ETDoc doc = ETDocModelHelper.createByAll(
                    KbModule.MINI_THEATRE.name(), "theatre",
                    mtTheatreModel.getId(), null, null,
                    null, mtTheatreModel.getTitle(),
                    NewsChannel.HTV4.name(), null, default_modules,
                    mtTheatreModel.getCreateTime()
                    );

            saveETDoc(doc);

            episodeByTheatre(mtTheatreModel.getId(), mtTheatreModel.getTitle());
        }
        return kbRes.getTotalPage() > query.getPage();
    }

    private void episodeByTheatre(String theatreId, String theatreTitle) {
        List<MTEpisodeModel> list = miniTheatreService.getEpisodeList(theatreId, null, 1, 200).dataOrThrow();
        if (list.isEmpty()) {
            return;
        }
        for (MTEpisodeModel episodeModel : list) {
            ETDoc doc = ETDocModelHelper.createByAll(
                    KbModule.MINI_THEATRE.name(), "episode",
                    theatreId, episodeModel.getId(), null,
                    null, theatreTitle + "-" +episodeModel.getIdxName(),
                    NewsChannel.HTV4.name(), null, default_modules,
                    episodeModel.getCreateTime()
            );
            saveETDoc(doc);
        }
    }
}
