package com.kaiba.m.core.service.opensearch;

import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.LoadingCache;
import com.kaiba.lib.base.assembler.Mapper;
import com.kaiba.lib.base.constant.opensearch.SearchModule;
import com.kaiba.lib.base.constant.opensearch.SearchTopState;
import com.kaiba.lib.base.constant.opensearch.SearchVisibleLevel;
import com.kaiba.lib.base.domain.circle.CircleTopicModel;
import com.kaiba.lib.base.domain.common.Image;
import com.kaiba.lib.base.domain.news.NewsBaseModel;
import com.kaiba.lib.base.domain.note.NoteModel;
import com.kaiba.lib.base.domain.opensearch.*;
import com.kaiba.lib.base.domain.publicservice.PublicServiceItemModel;
import com.kaiba.lib.base.lang.verifier.F;
import com.kaiba.lib.base.lang.verifier.Verifier;
import com.kaiba.lib.base.lang.verifier.VerifierBuilder;
import com.kaiba.lib.base.service.ICircleService;
import com.kaiba.lib.base.service.INewsService;
import com.kaiba.lib.base.service.IPublicServiceService;
import com.kaiba.lib.base.service.IVideoliveService;
import com.kaiba.lib.base.util.StringUtils;
import com.kaiba.lib.base.util.appaction.AppActionType;
import com.kaiba.m.core.domain.opensearch.SearchTop;
import com.kaiba.m.core.repository.opensearch.OpenSearchTopRepository;
import com.kaiba.m.core.util.PageUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/08/24 17:46
 */
@Slf4j
@Service
public class OpenSearchDataService {

    public static final Integer SEARCH_SERVICE_COMPOSITE_LIMIT = 4;

    private final OpenSearchTopRepository topRepository;
    private final IVideoliveService videoliveService;
    private final INewsService newsService;
    private final ICircleService circleService;
    private final IPublicServiceService publicServiceService;

    private final LoadingCache<Integer, List<SearchTop>> searchTopCache = Caffeine.newBuilder()
            .expireAfterWrite(Duration.ofMinutes(14))
            .refreshAfterWrite(Duration.ofMinutes(3))
            .build(this::queryFrontDataFromDb);

    private final LoadingCache<Integer, List<String>> newsCache = Caffeine.newBuilder()
            .expireAfterWrite(Duration.ofMinutes(14))
            .refreshAfterWrite(Duration.ofMinutes(3))
            .build(this::getNewsBySiteId);

    public OpenSearchDataService(
            OpenSearchTopRepository topRepository,
            IVideoliveService videoliveService,
            INewsService newsService,
            ICircleService circleService,
            IPublicServiceService publicServiceService
    ) {
        this.topRepository = topRepository;
        this.videoliveService = videoliveService;
        this.newsService = newsService;
        this.circleService = circleService;
        this.publicServiceService = publicServiceService;
    }

    public SearchTop addToTopList(Integer userId, SearchTopModel model) {
        IMAGE_VERIFIER.verify(model.getImage());
        TOP_VERIFIER.verify(model);
        SearchTop searchTop = model2Entity(model);
        searchTop.setState(SearchTopState.DRAFT.getValue());
        searchTop.setUserId(userId);
        long time = System.currentTimeMillis();
        searchTop.setCreateTime(time);
        searchTop.setUpdateTime(time);
        return topRepository.save(searchTop);
    }

    public SearchTop edit(Integer userId, SearchTopModel model) {
        IMAGE_VERIFIER.verify(model.getImage());
        TOP_VERIFIER.verify(model);
        SearchTop searchTop = model2Entity(model);
        searchTop.setUserId(userId);
        long time = System.currentTimeMillis();
        searchTop.setUpdateTime(time);
        return topRepository.updateSearchTop(searchTop);
    }

    public SearchTop editTopStateAsOffline(String id) {
        return topRepository.updateTopStateById(id, SearchTopState.ARCHIVED.getValue());
    }

    public SearchTop editTopStateAsOnline(String id) {
        return topRepository.updateTopStateById(id, SearchTopState.ONLINE.getValue());
    }

    public List<SearchModuleModel> getCircleList(Integer siteId, String queryParam) {
        return circleService.getTopicListBySite(siteId, 1, 200)
            .map(list -> list.stream()
                .map(t -> circleTopic2Search(siteId, t))
                .filter(t -> filterCircle(t, queryParam))
                .limit(SEARCH_SERVICE_COMPOSITE_LIMIT)
                .map(t -> (SearchModuleModel) t)
                .collect(Collectors.toList()))
            .data().orElse(Collections.emptyList());
    }

    public List<SearchTopModel> getSearchTopList(String queryParam, Integer siteId) {
        return Optional.ofNullable(searchTopCache.get(siteId))
            .map(list -> list.stream()
                .filter(t -> filterSearchTop(t, queryParam))
                .map(t -> {
                    SearchTopModel topModel = Mapper.map(t, SearchTopModel.class);
                    topModel.setTypeName(t.getTypeName());
                    return topModel;
                }).collect(Collectors.toList()))
            .orElse(Collections.emptyList());
    }

    public Page<SearchTop> getSearchTopList(String queryParams, Integer state, Integer page, Integer pageSize) {
        Pageable pageable = PageUtils.ofDefault(page, pageSize, Sort.by(Sort.Direction.DESC, "id"));
        return topRepository.findByQuery(queryParams, state, pageable);
    }

    public List<SearchServiceModel> getServiceListFromCache(Integer siteId, String queryParam) {
        return publicServiceService.searchService("hz_app_main_page")
            .map(list -> list.stream()
                .flatMap(model -> model.getItems().stream()
                    .map(item -> service2SearchModel(item, siteId, model.getName())))
                .filter(t -> filterService(t, queryParam))
                .limit(SEARCH_SERVICE_COMPOSITE_LIMIT)
                .collect(Collectors.toList()))
            .data().orElse(Collections.emptyList());
    }

    public List<SearchModuleModel> getVideoLiveList(Integer siteId, String queryParam) {
        return videoliveService.getAllActiveLiveList(siteId)
            .map(t -> t.stream()
                .filter(live -> live.getTitle().contains(queryParam))
                .map(live -> {
                    SearchLiveModel dest = Mapper.map(live, SearchLiveModel.class);
                    dest.setTypeName("视频直播");
                    return (SearchModuleModel) dest;
                }).collect(Collectors.toList()))
            .data().orElse(Collections.emptyList());
    }

    public List<String> searchBox(Integer siteId) {
        List<String> results = new ArrayList<>();
        Optional.ofNullable(searchTopCache.get(siteId))
            .ifPresent(searchTops -> {
                List<String> copyWritingList = searchTops.stream()
                    .map(SearchTop::getCopywriting)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
                results.addAll(copyWritingList);
            });
        Optional.ofNullable(newsCache.get(siteId)).ifPresent(results::addAll);
        return results;
    }

    // ------------------------------------------------

    private SearchCircleModel circleTopic2Search(Integer siteId, CircleTopicModel source) {
        SearchCircleModel dest = new SearchCircleModel();
        dest.setSiteId(siteId);
        dest.setTopic(source.getTitle());
        dest.setDesc(Optional.ofNullable(source.getContent())
            .map(NoteModel::getContent).orElse(""));
        dest.setThreadId(source.getThreadId());
        dest.setVisible(SearchVisibleLevel.ALL.getValue());
        dest.setAction(AppActionType.PAGE_CIRCLE_TOPIC.getAction());
        dest.setTypeName("圈子");
        dest.setActionParams(Collections.singletonMap("threadId", source.getThreadId()));
        if (!StringUtils.isEmpty(source.getCover1())) {
            Image cover = new Image();
            cover.setUrl(source.getCover1());
            dest.setCover(cover);
        }
        return dest;
    }

    private boolean filterCircle(SearchCircleModel model, String queryParam) {
        return (model.getTopic() != null && model.getTopic().contains(queryParam)) ||
            (model.getDesc() != null && model.getDesc().contains(queryParam));
    }

    private boolean filterSearchTop(SearchTop searchTop, String queryParam) {
        return (searchTop.getKeywords() != null && searchTop.getKeywords().contains(queryParam))
            || (searchTop.getCopywriting() != null && searchTop.getCopywriting().contains(queryParam));
    }

    private boolean filterService(SearchServiceModel model, String queryParam) {
        return (model.getGroup() != null && model.getGroup().contains(queryParam))
            || (model.getName() != null && model.getName().contains(queryParam));
    }

    private SearchServiceModel service2SearchModel(PublicServiceItemModel item, Integer siteId, String groupName) {
        SearchServiceModel dest = new SearchServiceModel();
        dest.setSiteId(siteId);
        dest.setVisible(SearchVisibleLevel.ALL.getValue());
        dest.setTypeName("服务");
        dest.setName(item.getName());
        dest.setGroup(groupName);
        dest.setCover(item.getIcon());
        dest.setAction(item.getAction());
        dest.setActionParams(item.getActionParams());
        return dest;
    }

    private SearchTop model2Entity(SearchTopModel model) {
        SearchTop searchTop = Mapper.map(model, SearchTop.class);
        searchTop.setStartTime(Optional.ofNullable(model.getStartTime())
            .map(t -> t * 1000).orElse(null));
        searchTop.setEndTime(Optional.ofNullable(model.getEndTime())
            .map(t -> t * 1000).orElse(null));
        searchTop.setSearchType(model.getSearchType());
        searchTop.setTypeName(model.getTypeName());
        searchTop.setCopywriting(model.getCopywriting());
        return searchTop;
    }

    private List<SearchTop> queryFrontDataFromDb(Integer siteId) {
        return Optional.ofNullable(
                topRepository.findAllBySiteAndStateAndTime(
                    siteId, SearchTopState.ONLINE.getValue(), System.currentTimeMillis()))
            .filter(list -> !list.isEmpty())
            .orElseGet(() -> {
                SearchTop top = new SearchTop();
                top.setKeywords(Collections.singletonList(""));
                return Collections.singletonList(top);
            });
    }

    private List<String> getNewsBySiteId(Integer siteId) {
        return newsService.getTodayApprovedList(siteId)
            .map(list -> list.stream()
                .map(NewsBaseModel::getTitle)
                .limit(5).collect(Collectors.toList()))
            .data().orElse(Collections.emptyList());
    }

    private static final Verifier<Image> IMAGE_VERIFIER = new VerifierBuilder<Image>()
            .defaultOrElseThrow()
            .and(F.str(Image::getImageUrl).isUrl().r("无效的图片地址"))
            .create();

    private static final Verifier<SearchTopModel> TOP_VERIFIER = new VerifierBuilder<SearchTopModel>().defaultOrElseThrow()
            .ifNotNull(F.str(SearchTopModel::getId).isObjectId())
            .and(F.str(SearchTopModel::getTypeName).notEmpty())
            .and(F.str(SearchTopModel::getTitle).notEmpty())
            .and(F.intF(SearchTopModel::getSearchType).enums(SearchModule.values()).r("无效的搜索类型"))
            .and(F.list(SearchTopModel::getKeywords).each(t -> F.str(t).notEmpty()).r("关键词不能为空"))
            .and(F.str(SearchTopModel::getAction).notEmpty())
            .create();
}
