package com.kaiba.m.core.service.news.legacy;

import com.github.benmanes.caffeine.cache.CacheLoader;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.LoadingCache;
import com.kaiba.m.core.domain.news.legacy.News;
import com.kaiba.m.core.repository.news.legacy.NewsRepository;
import lombok.extern.slf4j.Slf4j;
import org.checkerframework.checker.nullness.qual.NonNull;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.util.*;
import java.util.stream.Collectors;

/**
 * author: lyux
 * date: 2023-03-31
 */
@Slf4j
@Service
public class NewsApiService {

    private static final int BUFFER_MAX_SIZE = 120;
    private static final int DB_PAGE_SIZE = 65;
    private static final String REDIS_KEY_NEWS_ID_LIST = "java_cache_core_news_id_list_by_site_";
    private static final String FAKE_ID = "f";

    private final NewsRepository newsRepository;
    private final StringRedisTemplate stringRedisTemplate;

    public NewsApiService(
            NewsRepository newsRepository,
            StringRedisTemplate stringRedisTemplate
    ) {
        this.newsRepository = newsRepository;
        this.stringRedisTemplate = stringRedisTemplate;
    }

    public List<News> getNewsListBySite(Integer siteId, Integer page, Integer pageSize) {
        int p = page == null ? 0 : page - 1;
        int ps = pageSize == null ? 20 : pageSize;
        int st = p * ps;
        int et = (p + 1) * ps - 1;
        if (et > BUFFER_MAX_SIZE) {
            // 超出缓存数据上限, 直接从数据库读取
            //log.info("getNewsListBySite11 from db, siteId: " + siteId + ", [" + st + "," + et + "]");
            Pageable pageable = PageRequest.of(p, ps, Sort.Direction.DESC, "isTop", "signTime");
            return newsRepository.getBySiteIdAndIsSign(siteId, 1, pageable).stream()
                    .filter(NewsApiService::isValid)
                    .collect(Collectors.toList());
        }

        String redisKey = REDIS_KEY_NEWS_ID_LIST + siteId;
        List<String> newsIdList = stringRedisTemplate.opsForList().range(redisKey, st, et);
        if (newsIdList == null || newsIdList.size() == 0) {
            NewsListHolder holder = getOrFillNewsIdListBySiteIdFromDB(siteId);
//            log.info("getNewsListBySite22 from db, siteId: " + siteId + ", [" + st + "," + et + "], " +
//                    "news cache size: " + holder.newsList.size());
            stringRedisTemplate.opsForList().rightPushAll(redisKey, holder.newsIdList);
            stringRedisTemplate.expire(redisKey, Duration.ofMinutes(1));
            if (holder.newsList == null || holder.newsList.isEmpty()) {
                return Collections.emptyList();
            } else {
                return getSubListByPage(holder.newsList, p, ps);
            }
        } else {
            return getNewsListByIdIn(newsIdList);
        }
    }

    private List<News> getNewsListByIdIn(List<String> list) {
        Map<String, News> map = newsByIdCache.getAll(list);
        return list.stream().map(map::get).filter(NewsApiService::isValid).collect(Collectors.toList());
    }

    private NewsListHolder getOrFillNewsIdListBySiteIdFromDB(Integer siteId) {
        List<News> newsList = new LinkedList<>();
        List<String> newsIdList = new LinkedList<>();
        for (int i = 0; ; i ++) {
            Pageable pageable = PageRequest.of(i, DB_PAGE_SIZE, Sort.Direction.DESC, "isTop", "signTime");
            List<News> list = newsRepository.getBySiteIdAndIsSign(siteId, 1, pageable);
            if (list.size() == 0) {
                break;
            }
            for (News news : list) {
                if (isValid(news)) {
                    newsList.add(news);
                    newsIdList.add(news.getId());
                    newsByIdCache.put(news.getId(), news);
                } else {
                    // 不可用时也填充列表, 避免出现分页错乱问题
                    newsIdList.add(FAKE_ID);
                }
                if (newsIdList.size() >= BUFFER_MAX_SIZE) {
                    break;
                }
            }
            if (newsIdList.size() >= BUFFER_MAX_SIZE) {
                break;
            }
        }
        if (newsIdList.size() < BUFFER_MAX_SIZE) {
            // 不足额则用假 id 填充, 避免无数据时反复透库
            int fillCount = BUFFER_MAX_SIZE - newsIdList.size();
            for (int i = 0; i < fillCount; i ++) {
                newsIdList.add(FAKE_ID);
            }
        }
        return new NewsListHolder(newsList, newsIdList);
    }

    private final LoadingCache<String, News> newsByIdCache = Caffeine.newBuilder()
            .expireAfterWrite(Duration.ofMinutes(10))
            .refreshAfterWrite(Duration.ofMinutes(1))
            .build(new CacheLoader<String, News>() {
                @Override
                public News load(@NonNull String newsId) {
                    return newsRepository.findById(newsId).orElseGet(News::new);
                }

                @Override
                public @NonNull Map<@NonNull String, @NonNull News> loadAll(
                        @NonNull Iterable<? extends @NonNull String> newsIds) {
                    Set<String> newsIdSet = new HashSet<>();
                    newsIds.forEach(newsIdSet::add);
                    return newsRepository.findByIdIn(newsIdSet).stream()
                            .collect(Collectors.toMap(News::getId, n -> n, (n1, n2) -> n1));
                }

                @Override
                public News reload(@NonNull String newsId, @NonNull News oldValue) {
                    News news = load(newsId);
                    return news == null ? oldValue : news;
                }

            });

    private static boolean isValid(News news) {
        if (news == null || news.getId() == null) {
            return false;
        }
        if (FAKE_ID.equals(news.getId())) {
            return false;
        }
        if (news.getIsDisplay() != null && news.getIsDisplay() == 0) {
            return false;
        }
        return true;
    }

    private static <T> List<T> getSubListByPage(List<T> list, int page, int pageSize) {
        if (page < 0 || pageSize <= 0 ) {
            return Collections.emptyList();
        }
        int count = list.size();
        int fromIndex = page * pageSize;
        if (fromIndex >= count) {
            return Collections.emptyList();
        } else {
            int toIndex = (page + 1) * pageSize;
            toIndex = Math.min(toIndex, count);
            return list.subList(fromIndex, toIndex);
        }
    }

    private static class NewsListHolder {
        List<News> newsList;
        List<String> newsIdList;

        private NewsListHolder(List<News> newsList, List<String> newsIdList) {
            this.newsList = newsList;
            this.newsIdList = newsIdList;
        }
    }

}
