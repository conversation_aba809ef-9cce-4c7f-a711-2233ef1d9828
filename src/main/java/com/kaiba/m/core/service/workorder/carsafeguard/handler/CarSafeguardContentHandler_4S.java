package com.kaiba.m.core.service.workorder.carsafeguard.handler;

import com.kaiba.lib.base.assembler.Mapper;
import com.kaiba.lib.base.lang.verifier.F;
import com.kaiba.lib.base.lang.verifier.V;
import com.kaiba.lib.base.lang.verifier.Verifier;
import com.kaiba.lib.base.lang.verifier.VerifierBuilder;
import com.kaiba.m.core.domain.workorder.WOTag;
import com.kaiba.m.core.domain.workorder.carsafeguard.CarSafeguard4SStoreCase;
import com.kaiba.m.core.domain.workorder.carsafeguard.CarSafeguardCaseContent;
import com.kaiba.m.core.repository.workorder.carsafeguard.CarSafeguard4SStoreCaseRepository;
import com.kaiba.m.core.repository.workorder.carsafeguard.CarSafeguardCaseContentRepository;
import com.kaiba.m.core.service.workorder.carsafeguard.CarSafeguardBizType;
import com.kaiba.m.core.service.workorder.carsafeguard.CarSafeguardConstants;
import com.kaiba.m.core.service.workorder.carsafeguard.CarSafeguardUtils;
import com.kaiba.m.core.model.safeguard.CarSafeguard4SStoreCaseContentModel;
import com.kaiba.m.core.model.safeguard.CarSafeguardCaseContentModel;
import com.kaiba.m.core.model.safeguard.request.CS4SStoreCaseCreateModel;
import com.kaiba.m.core.model.safeguard.request.CarSafeguardCaseCreateModel;
import com.kaiba.m.core.service.workorder.tag.WOTagService;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version CarSafeguardContentHandler_4S, v0.1 2024/7/22 14:55 daopei Exp $
 **/
@Service
public class CarSafeguardContentHandler_4S implements CarSafeguardContentHandler{

    private final CarSafeguard4SStoreCaseRepository caseRepository;
    private final CarSafeguardCaseContentRepository contentRepository;
    private final WOTagService tagService;

    public CarSafeguardContentHandler_4S(
            CarSafeguard4SStoreCaseRepository caseRepository,
            CarSafeguardCaseContentRepository contentRepository,
            WOTagService tagService
    ) {
        this.caseRepository = caseRepository;
        this.contentRepository = contentRepository;
        this.tagService = tagService;
    }

    @Override
    public boolean support(String contentType) {
        return CarSafeguardBizType.AUTOMOBILE_4S_STORE == CarSafeguardBizType.valueOf(contentType);
    }

    @Override
    public Class<CarSafeguard4SStoreCaseContentModel> contentClass() {
        return CarSafeguard4SStoreCaseContentModel.class;
    }

    @Override
    public CarSafeguardCaseContentModel getContentById(String id) {
        return caseRepository.findById(id)
                .map(c -> Mapper.map(c, CarSafeguard4SStoreCaseContentModel.class))
                .orElse(null);
    }

    @Override
    public List<CarSafeguardCaseContentModel> getContentById(Collection<String> ids) {
        return caseRepository.findByIdIn(ids).stream()
                .map(c -> Mapper.map(c, CarSafeguard4SStoreCaseContentModel.class))
                .collect(Collectors.toList());
    }

    @Override
    public <T extends CarSafeguardCaseCreateModel> CarSafeguardCaseContentModel createContent(T model) {
        CS4SStoreCaseCreateModel createModel = (CS4SStoreCaseCreateModel) model;
        storeVerify.verify(createModel);
        CarSafeguard4SStoreCase entity = Mapper.map(createModel, CarSafeguard4SStoreCase.class);
        //父类属性
        entity.setContent(createModel.getContent());
        entity.setContentType(CarSafeguardBizType.AUTOMOBILE_4S_STORE.name());
        entity.setUserId(createModel.getClientUserId());
        entity.setSource(createModel.getSource());
        entity.setImages(createModel.getImages());
        entity.setVideo(createModel.getVideo());
        //基础属性
        entity.setPersonal(CarSafeguardUtils.getACLStringDataListOrThrow(createModel));
        entity.setOriginResolverLack(createModel.getOriginResolverLack());
        entity.setCreateTime(System.currentTimeMillis());
        entity.setUpdateTime(System.currentTimeMillis());
        entity = caseRepository.save(entity);

        createContentIndex(entity.getId(), CarSafeguardBizType.AUTOMOBILE_4S_STORE.name());
        return map2Model(entity);
    }

    @Override
    public <T extends CarSafeguardCaseCreateModel> List<Long> obtainTag(T model) {
        CS4SStoreCaseCreateModel createModel = (CS4SStoreCaseCreateModel) model;
        String carSeriesKey = CarSafeguardConstants.car_series_tag_prefix + createModel.getCarSeries();
        return tagService.getTagByKey(carSeriesKey, true)
                .map(WOTag::getCode)
                .map(Collections::singletonList)
                .orElse(Collections.emptyList());
    }

    public void createContentIndex(String contentId, String contentType) {
        CarSafeguardCaseContent content = new CarSafeguardCaseContent();
        content.setContentId(contentId);
        content.setContentType(contentType);
        content.setCreateTime(System.currentTimeMillis());
        contentRepository.save(content);
    }

    private static CarSafeguard4SStoreCaseContentModel map2Model(CarSafeguard4SStoreCase entity) {
        CarSafeguard4SStoreCaseContentModel model = new CarSafeguard4SStoreCaseContentModel();
        model.setId(entity.getId());
        model.setContent(entity.getContent());
        model.setContentType(CarSafeguardBizType.AUTOMOBILE_4S_STORE.name());
        model.setCreateTime(entity.getCreateTime());
        model.setUpdateTime(entity.getUpdateTime());
        model.setPersonal(entity.getPersonal());
        model.setOriginResolverLack(entity.getOriginResolverLack());
        model.setSource(entity.getSource());
        model.setUserId(entity.getUserId());
        model.setVideo(entity.getVideo());
        model.setImages(entity.getImages());
        model.setCarRegistration(entity.getCarRegistration());
        model.setCarRegistrationNo(entity.getCarRegistrationNo());

        model.setCarBrand(entity.getCarBrand());
        model.setCarSeries(entity.getCarSeries());
        model.setCarMileage(entity.getCarMileage());
        model.setCarBuyTime(entity.getCarBuyTime());
        model.setCarHappenTime(entity.getCarHappenTime());
        return model;
    }


    static Verifier<CS4SStoreCaseCreateModel> storeVerify = new VerifierBuilder<CS4SStoreCaseCreateModel>().defaultOrElseThrow()
            .and(F.str(CS4SStoreCaseCreateModel::getSource).notNull().r("请选择地区"))
            .and(F.str(CS4SStoreCaseCreateModel::getCarBrand).notNull().r("请选择车系"))
            .and(F.str(CS4SStoreCaseCreateModel::getCarSeries).notNull().r("请选择车系"))
            .and(F.longF(CS4SStoreCaseCreateModel::getCarBuyTime).notNull().r("请输入购车时间"))
            .addVerifier(V.or(
                    F.str(CS4SStoreCaseCreateModel::getOriginResolver).notNull(),
                    F.str(CS4SStoreCaseCreateModel::getOriginResolverLack).notNull())
                    .r("请选择或者输入投诉对象"))
            .and(F.intF(CS4SStoreCaseCreateModel::getCarRegistration).in(1, 0))
            .addVerifier(V.or(
                    F.intF(CS4SStoreCaseCreateModel::getCarRegistration).eq(0),
                    V.and(
                            F.intF(CS4SStoreCaseCreateModel::getCarRegistration).eq(1),
                            F.str(CS4SStoreCaseCreateModel::getCarRegistrationNo).notNull().r("请输入车牌号")
                    )
            ))
            .create();
}
