package com.kaiba.m.core.service.videolive;

import com.kaiba.lib.base.constant.videolive.PortalState;
import com.kaiba.lib.base.response.KbCode;
import com.kaiba.lib.base.response.KbException;
import com.kaiba.m.core.domain.videolive.Portal;
import com.kaiba.m.core.repository.videolive.PortalRepository;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2020/6/5
 */
@Service
public class PortalService {

    private final PortalRepository portalRepository;

    public PortalService(PortalRepository portalRepository) {
        this.portalRepository = portalRepository;
    }

    public Portal create(Portal portal) {
        portal.setState(PortalState.INIT.getValue());
        portal.setCreateTime(System.currentTimeMillis() / 1000);
        return portalRepository.insert(portal);
    }

    public Portal save(Portal portal) {
        return portalRepository.save(portal);
    }

    public void changeState(Portal portal, PortalState state) {
        PortalState currentState = PortalState.valueOf(portal.getState()).orElseThrow(KbException.supplier(KbCode.ILLEGAL_STATE));
        if (!currentState.isStateChangeAllowed(state)) {
            throw new KbException(KbCode.ILLEGAL_STATE,
                    "illegal portal state change: " + currentState + " -> " + state + ", " + portal.getId());
        }
        portalRepository.updatePortalState(portal.getId(), state.getValue());
    }

    public Optional<Portal> detail(String portalId) {
        return portalRepository.findById(portalId);
    }

    public List<Portal> getPortalList(String videoliveId, Integer[] states) {
        if (null == states) {
            return portalRepository.findByVideoliveIdOrderByStartTimeDescStateDesc(videoliveId);
        }
        return portalRepository.findByVideoliveIdAndStateInOrderByStartTimeDescStateDesc(videoliveId, states);
    }

    public Page<Portal> getPortalByStateAndEndTimeLessThanEqual(PortalState state, Long time, Integer page, Integer pageSize) {
        Pageable pageable = createPageable(page, pageSize);
        return portalRepository.findByStateAndEndTimeLessThanEqual(state.getValue(), time, pageable);
    }

    //--------------------------------
    private static Pageable createPageable(Integer page, Integer pageSize) {
        int p = (page == null || page < 1) ? 0 : page - 1;
        int ps = (pageSize == null || pageSize > 100) ? 15 : pageSize;
        return PageRequest.of(p, ps);
    }
}
