package com.kaiba.m.core.service.da.eventtrack.strategy.impl;

import com.kaiba.lib.base.constant.KbModule;
import com.kaiba.lib.base.constant.apphome.AppHomeTabType;
import com.kaiba.lib.base.constant.news.NewsChannel;
import com.kaiba.lib.base.constant.workorder.WOBusiness;
import com.kaiba.lib.base.domain.apphome.AppHomeFrameModel;
import com.kaiba.lib.base.domain.apphome.AppHomeFrameTab;
import com.kaiba.lib.base.domain.common.KbTimeRange;
import com.kaiba.lib.base.domain.site.SiteModel;
import com.kaiba.lib.base.service.ISiteService;
import com.kaiba.m.core.constant.da.eventtrack.ETDocSyncStrategyType;
import com.kaiba.m.core.domain.da.eventtrack.ETDoc;
import com.kaiba.m.core.repository.da.eventtrack.ETDocRepository;
import com.kaiba.m.core.repository.da.eventtrack.ETDocSyncCursorRepository;
import com.kaiba.m.core.service.appcomponent.home.AppHomeFrameCacheService;
import com.kaiba.m.core.service.da.eventtrack.ETDocModelHelper;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 手动配置ETDoc同步策略
 */
@Service
public class ManualETDocSyncStrategy extends AbstractETDocSyncStrategy {


    private final ISiteService siteService;
    private final AppHomeFrameCacheService appHomeFrameCacheService;


    public ManualETDocSyncStrategy(
            ETDocRepository etDocRepository,
            ETDocSyncCursorRepository etDocSyncCursorRepository,
            ISiteService siteService,
            AppHomeFrameCacheService appHomeFrameCacheService
    ) {
        super(etDocRepository, etDocSyncCursorRepository);
        this.siteService = siteService;
        this.appHomeFrameCacheService = appHomeFrameCacheService;
    }

    @Override
    public void syncAll() {
        execute();
    }

    @Override
    public void syncByTimeRange(KbTimeRange time) {
        execute();
    }

    private void execute() {
        //首页
        List<ETDoc> mainPageDoc = initMainPageDoc();
        //节目
        List<ETDoc> programDoc = initProgramDoc();
        //回放
        List<ETDoc> playbackDoc = initPlaybackDoc();
        //业务
        List<ETDoc> bizDoc = initBizDoc();
        List<ETDoc> manualDocs = new ArrayList<>();
        manualDocs.addAll(mainPageDoc);
        manualDocs.addAll(programDoc);
        manualDocs.addAll(playbackDoc);
        manualDocs.addAll(bizDoc);
        for (ETDoc doc : manualDocs) {
            if (doc != null) {
                saveETDoc(doc);
            }
        }
    }

    @Override
    public String getStrategy() {
        return ETDocSyncStrategyType.MANUAL.name();
    }




    private List<Integer> getAllSite() {
        return siteService.getOpenSiteList().dataIgnoreError()
                .orElse(Collections.emptyList())
                .stream()
                .map(SiteModel::getId)
                .collect(Collectors.toList());
    }


    /**
     * 首页模块稿件
     * @return
     */
    private List<ETDoc> initMainPageDoc() {
        List<ETDoc> docs = new ArrayList<>();
        for (Integer siteId : getAllSite()) {
            AppHomeFrameModel onlineInstance = appHomeFrameCacheService.getOnlineInstanceBySiteId(siteId).orElse(null);
            if (onlineInstance == null) {
                continue;
            }
            List<AppHomeFrameTab> tabs = Arrays.asList(onlineInstance.getTab1(), onlineInstance.getTab2(), onlineInstance.getTab3(), onlineInstance.getTab4(), onlineInstance.getTab5(), onlineInstance.getTab6());
            for (AppHomeFrameTab tab : tabs) {
                if (tab == null) {
                    continue;
                }
                AppHomeTabType tabType = AppHomeTabType.resolveByNameIgnoreCase(tab.getType()).orElse(null);
                if (tabType == null) {
                    continue;
                }
                //首页-首屏TAB自定义 详情查看MainPage策略
                if (tabType == AppHomeTabType.TAB_HOME) {
                    docs.add(ETDocModelHelper.createByAll(
                            KbModule.APP_MAIN_PAGE.name(), null,
                            "home", "home", null,
                            siteId, "首页-首屏",
                            null, null, null));
                }
                if (tabType == AppHomeTabType.TAB_CIRCLE) {
                    docs.add(ETDocModelHelper.createByRef1(
                            KbModule.APP_MAIN_PAGE.name(), "circle", siteId, "首页-圈子"));
                }
                if (tabType == AppHomeTabType.TAB_SERVICE) {
                    docs.add(ETDocModelHelper.createByRef1(
                            KbModule.APP_MAIN_PAGE.name(), "service", siteId, "首页-服务"));
                }
                if (tabType == AppHomeTabType.TAB_ACTIVITY) {
                    docs.add(ETDocModelHelper.createByRef1(
                            KbModule.APP_MAIN_PAGE.name(), "act_mix", siteId, "首页-活动"));
                }
                if (tabType == AppHomeTabType.TAB_SELF) {
                    docs.add(ETDocModelHelper.createByRef1(
                            KbModule.APP_MAIN_PAGE.name(), "personal", siteId, "首页-我的"));
                }
            }
        }
        return docs;
    }

    /**
     * 节目互动稿件
     * @return
     */
    private List<ETDoc> initProgramDoc() {
        List<ETDoc> docs = new ArrayList<>();
        for (Integer siteId : getAllSite()) {
            docs.add(ETDocModelHelper.createByRef1(
                    KbModule.PROGRAM.name(),"PAGE", "program_list_page", siteId, "节目列表"));
            docs.add(ETDocModelHelper.createByRef1(
                    KbModule.PROGRAM.name(),"PAGE", "program_today_rank_page", siteId, "当日节目排行"));
            docs.add(ETDocModelHelper.createByRef1(
                    KbModule.PROGRAM.name(),"PAGE", "program_user_rank_page", siteId, "节目用户排行"));
        }
        return docs;
    }

    private List<ETDoc> initPlaybackDoc() {
        List<ETDoc> docs = new ArrayList<>();
        for (Integer siteId : getAllSite()) {
            docs.add(ETDocModelHelper.createByRef1(
                    KbModule.PLAYBACK.name(),"PAGE", "playback_list_page", siteId, "音频回播专辑列表"));
        }
        return docs;
    }


    private List<ETDoc> initBizDoc() {
        List<ETDoc> docs = new ArrayList<>();
        //爆料台首页
        docs.add(ETDocModelHelper.createByAll(
                KbModule.REVEAL.name(),"PAGE",
                "reveal_main_page", null, null,
                9, "爆料台首页",
                null, null, null));
        //剧好看/短剧
        docs.add(ETDocModelHelper.createByAll(
                KbModule.MINI_THEATRE.name(),"page",
                "mini_theatre_main_page", null, null,
                null, "剧好看主页",
                NewsChannel.FM_89.name(), null, null));

        // 集团广播频率播放 =========================
        //听见杭州主页
        docs.add(ETDocModelHelper.createByAll(
                KbModule.AUDIO_LIVE.name(),"PAGE",
                "hangzhou_fm_plaza", null, null,
                null, "听见杭州主页",
                null, null, Collections.singleton("da_hear_hz")));
        //听见杭州主播放页面
        docs.addAll(audioLiveGroup());


        // 工单 =========================
        //汽车维权
        docs.add(ETDocModelHelper.createByAll(
                KbModule.WORK_ORDER.name(),"wo_page",
                WOBusiness.CAR_SAFEGUARD.name(), "wo_safeguard_main_page", null,
                null, "汽车维权主页",
                NewsChannel.FM_918.name(), null, null));
        docs.add(ETDocModelHelper.createByAll(
                KbModule.WORK_ORDER.name(),"wo_page",
                WOBusiness.CAR_SAFEGUARD.name(), "wo_safeguard_rank_page", null,
                null, "汽车维权-投诉排行页",
                NewsChannel.FM_918.name(), null, null));
        docs.add(ETDocModelHelper.createByAll(
                KbModule.WORK_ORDER.name(),"wo_page",
                WOBusiness.CAR_SAFEGUARD.name(), "wo_safeguard_my_case_list", null,
                null, "汽车维权-我的维权列表页",
                NewsChannel.FM_918.name(), null, null));
        docs.add(ETDocModelHelper.createByAll(
                KbModule.WORK_ORDER.name(),"wo_page",
                WOBusiness.CAR_SAFEGUARD.name(), "wo_safeguard_followed_case_list", null,
                null, "汽车维权-我关注的维权列表页",
                NewsChannel.FM_918.name(), null, null));
        //我要问局长
        docs.add(ETDocModelHelper.createByAll(
                KbModule.WORK_ORDER.name(),"wo_page",
                WOBusiness.HZ_ASK_GOVERNOR.name(), "wo_hz_ask_governor_main_page", null,
                null, "我要问局长主页",
                NewsChannel.FM_89.name(), null, null));
        docs.add(ETDocModelHelper.createByAll(
                KbModule.WORK_ORDER.name(),"wo_page",
                WOBusiness.HZ_ASK_GOVERNOR.name(), "wo_hz_ask_governor_my_case_list", null,
                null, "我要问局长-我的投诉列表页",
                NewsChannel.FM_89.name(), null, null));

        // 教育稿件 ======================
        //开吧教育
        docs.add(ETDocModelHelper.createByAll(
                KbModule.EDUCATION.name(),"hz_education_page",
                "hz_education_main_page", null, null,
                null, "开吧教育主页",
                NewsChannel.FM_918.name(), null, null));
        docs.add(ETDocModelHelper.createByAll(
                KbModule.EDUCATION.name(),"hz_education_918_page",
                "hz_education_918_study_tour",null, null,
                null, "918研学主页",
                NewsChannel.FM_918.name(), null, null));
        docs.add(ETDocModelHelper.createByAll(
                KbModule.EDUCATION.name(),"hz_education_918_page",
                "hz_education_918_i_am_host",null, null,
                null, "918星少年小主播",
                NewsChannel.FM_918.name(), null, null));
        docs.add(ETDocModelHelper.createByAll(
                KbModule.EDUCATION.name(),"hz_education_918_page",
                "hz_education_918_i_am_host_txmb",null, null,
                null, "918星少年小主播-童心妙笔",
                NewsChannel.FM_918.name(), null, null));
        docs.add(ETDocModelHelper.createByAll(
                KbModule.EDUCATION.name(),"hz_education_918_page",
                "hz_education_918_i_am_host_wadt",null, null,
                null, "918星少年小主播-晚安电台",
                NewsChannel.FM_918.name(), null, null));
        docs.add(ETDocModelHelper.createByAll(
                KbModule.EDUCATION.name(),"hz_education_918_page",
                "hz_education_918_i_am_host_tgsj",null, null,
                null, "918星少年小主播-童观世界",
                NewsChannel.FM_918.name(), null, null));
        docs.add(ETDocModelHelper.createByAll(
                KbModule.EDUCATION.name(),"hz_education_918_page",
                "hz_education_918_i_am_host_kid_list",null, null,
                null, "918星少年小主播-小主播列表",
                NewsChannel.FM_918.name(), null, null));
        //89朗诵团
        docs.add(ETDocModelHelper.createByAll(
                KbModule.EDUCATION.name(),"hz_education_89_recitation_page",
                "hz_education_89_recitation_main_page",null, null,
                null, "89朗诵团主页",
                NewsChannel.FM_89.name(), null, null));
        docs.add(ETDocModelHelper.createByAll(
                KbModule.EDUCATION.name(),"hz_education_89_recitation_page",
                "hz_education_89_recitation_member_list_page",null, null,
                null, "89朗诵团-成员列表页",
                NewsChannel.FM_89.name(), null, null));

        // 零散单页 =======================
        docs.add(ETDocModelHelper.createByAll(
                KbModule.SINGLE_PAGE.name(), null,
                "traffic_restriction_map", null ,null,
                null, "限行地图",
                null, null, null));
        docs.add(ETDocModelHelper.createByAll(
                KbModule.SINGLE_PAGE.name(), null,
                "parking_map", null ,null,
                null, "找车位",
                null, null, null));
        docs.add(ETDocModelHelper.createByAll(
                KbModule.SINGLE_PAGE.name(), null,
                "in_hangzhou", null ,null,
                null, "in杭州主页",
                null, null, Collections.singleton("da_in_hz")));
        docs.add(ETDocModelHelper.createByAll(
                KbModule.SINGLE_PAGE.name(), null,
                "hangzhou_tv_plaza", null ,null,
                null, "看见杭州主页",
                null, null, null));
        docs.add(ETDocModelHelper.createByAll(
                KbModule.SINGLE_PAGE.name(), null,
                "road_condition", null ,null,
                null, "路况",
                null, null, null));
        return docs;
    }


    private static List<ETDoc> audioLiveGroup() {
       List<ETDoc> docs = new ArrayList<>();

        docs.add(ETDocModelHelper.createByAll(
                KbModule.AUDIO_LIVE.name(), "GROUP",
                "FM89", null, null,
                null, "杭州之声",
                NewsChannel.FM_89.name(), null, Collections.singleton("da_hear_hz")));
        docs.add(ETDocModelHelper.createByAll(
                KbModule.AUDIO_LIVE.name(), "GROUP",
                "FM1054", null, null,
                null, "西湖之声",
                NewsChannel.FM_1054.name(), null, Collections.singleton("da_hear_hz")));
        docs.add(ETDocModelHelper.createByAll(
                KbModule.AUDIO_LIVE.name(), "GROUP",
                "FM918", null, null,
                null, "交通91.8",
                NewsChannel.FM_918.name(), null, Collections.singleton("da_hear_hz")));
        docs.add(ETDocModelHelper.createByAll(
                KbModule.AUDIO_LIVE.name(), "GROUP",
                "FM907", null, null,
                null, "城市资讯广播",
                NewsChannel.FM_907.name(), null, Collections.singleton("da_hear_hz")));
        docs.add(ETDocModelHelper.createByAll(
                KbModule.AUDIO_LIVE.name(), "GROUP",
                "AM_954", null, null,
                null, "老朋友广播",
                NewsChannel.AM_954.name(), null, Collections.singleton("da_hear_hz")));

        return docs;
    }

} 