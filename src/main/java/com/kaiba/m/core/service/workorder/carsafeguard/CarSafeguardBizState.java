package com.kaiba.m.core.service.workorder.carsafeguard;

import lombok.Getter;

/**
 * <AUTHOR>
 * @version CarSafeguardContentType, v0.1 2024/7/16 11:37 daopei Exp $
 **/
@Getter
public enum CarSafeguardBizState {
    WAIT_AUDIT("待审核"),
    AUDIT_PASS("审核通过"),
    AUDIT_REJECT("审核拒绝"),
    ;


    private String desc;

    CarSafeguardBizState(String desc) {
        this.desc = desc;
    }

    public static CarSafeguardBizState resolveByName(String name) {
        if (name == null) {
            return null;
        }
        for (CarSafeguardBizState value : CarSafeguardBizState.values()) {
            if (value.name().equals(name)) {
                return value;
            }
        }
        return null;
    }
}
