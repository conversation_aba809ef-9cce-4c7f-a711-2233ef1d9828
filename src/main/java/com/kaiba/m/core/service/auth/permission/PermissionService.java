package com.kaiba.m.core.service.auth.permission;

import com.kaiba.lib.base.constant.auth.AuthScope;
import com.kaiba.lib.base.constant.auth.AuthSpecialRoles;
import com.kaiba.lib.base.constant.auth.AuthType;
import com.kaiba.lib.base.response.KbCode;
import com.kaiba.lib.base.response.KbException;
import com.kaiba.m.core.domain.auth.AuthPermission;
import com.kaiba.m.core.domain.auth.AuthRole;
import com.kaiba.m.core.domain.auth.AuthUser;
import com.kaiba.m.core.repository.auth.AuthPermissionRepository;
import com.kaiba.m.core.repository.auth.AuthRoleRepository;
import com.kaiba.m.core.repository.auth.AuthUserRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * author: lyux
 * date: 19-8-13
 */
@Service
@Slf4j
public class PermissionService {

    private final PermissionCache permissionCache;
    private final UserRoleCache userRoleCache;
    private final AuthPermissionRepository authPermissionRepository;
    private final AuthRoleRepository authRoleRepository;
    private final AuthUserRepository authUserRepository;

    public PermissionService(
            PermissionCache permissionCache,
            UserRoleCache userRoleCache,
            AuthPermissionRepository authPermissionRepository,
            AuthRoleRepository authRoleRepository,
            AuthUserRepository authUserRepository
    ) {
        this.permissionCache = permissionCache;
        this.userRoleCache = userRoleCache;
        this.authPermissionRepository = authPermissionRepository;
        this.authRoleRepository = authRoleRepository;
        this.authUserRepository = authUserRepository;
    }

    public AuthPermission addPermission(AuthPermission permission) {
        return authPermissionRepository.save(permission);
    }

    public List<AuthPermission> savePermissionList(List<AuthPermission> permissionList) {
        return authPermissionRepository.saveAll(permissionList);
    }

    public AuthRole addRole(AuthRole role) {
        if (null != role.getPermissions() && role.getPermissions().size() != 0) {
            Map<String, AuthPermission> permissionMap = getPermissionMapIn(role.getPermissions());
            for (String p : role.getPermissions()) {
                if (permissionMap.get(p) == null) {
                    throw new KbException(KbCode.AUTH_PERMISSION_NOT_EXISTS, "permission not exists: " + p);
                }
            }
        }
        return authRoleRepository.save(role);
    }

    public List<AuthRole> saveRoleList(List<AuthRole> roleList) {
        return authRoleRepository.saveAll(roleList);
    }

    public void grantPermissionToRole(String permission, String role) {
        authPermissionRepository.findFirstByPermission(permission).orElseThrow(
                () -> new KbException(KbCode.AUTH_PERMISSION_NOT_EXISTS, "permission not exists: " + permission));
        AuthRole authRole = authRoleRepository.findFirstByRole(role).orElseThrow(
                () -> new KbException(KbCode.AUTH_ROLE_NOT_EXISTS, "role not exists: " + role));
        if (authRole.getPermissions() == null || authRole.getPermissions().size() == 0) {
            authRoleRepository.updatePermissions(role, Collections.singletonList(permission));
        } else {
            if (authRole.getPermissions().contains(role)) {
                return;
            }
            List<String> rolePermissionList = new ArrayList<>(authRole.getPermissions());
            rolePermissionList.add(role);
            authRoleRepository.updatePermissions(role, rolePermissionList);
        }
    }

    public void deletePermissionFromRole(String permission, String role) {
        AuthRole authRole = authRoleRepository.findFirstByRole(role).orElseThrow(
                () -> new KbException(KbCode.AUTH_ROLE_NOT_EXISTS, "role not exists: " + role));
        if (authRole.getPermissions() != null && authRole.getPermissions().contains(role)) {
            List<String> rolePermissionList = new ArrayList<>(authRole.getPermissions());
            rolePermissionList.remove(permission);
            authRole.setPermissions(rolePermissionList);
            authRoleRepository.save(authRole);
        }
    }

    public void updateUserRoles(Integer authUserId, String[] roles, Integer targetUserId) {
        List<AuthUser> addAuthUserList = new LinkedList<>();
        List<String> deleteRoleList = new LinkedList<>();
        Set<String> collideRoleSet;
        List<AuthRole> newRoleList = roles == null || roles.length == 0 ?
                Collections.emptyList() : getRoleListIn(Arrays.asList(roles));
        Map<String, AuthRole> newRoleMap = new HashMap<>(newRoleList.size());
        for (AuthRole newRole : newRoleList) {
            newRoleMap.put(newRole.getRole(), newRole);
        }
        List<AuthUser> oldAuthUserList = getUserRoleRelation(targetUserId);
        Map<String, AuthUser> oldAuthUserMap = new HashMap<>(oldAuthUserList.size());
        for (AuthUser oldAuthUser : oldAuthUserList) {
            oldAuthUserMap.put(oldAuthUser.getRole(), oldAuthUser);
        }

        collideRoleSet = checkRoleCollideWith(newRoleList);

        // 找到所有需要删除的角色
        for (AuthUser oldAuthUser : oldAuthUserList) {
            AuthRole role = newRoleMap.get(oldAuthUser.getRole());
            if (null == role) {
                deleteRoleList.add(oldAuthUser.getRole());
            }
        }

        // 找到所有需要新增和更新的角色
        for (AuthRole newRole : newRoleList) {
            AuthUser oldAuthUser = oldAuthUserMap.get(newRole.getRole());
            if (oldAuthUser == null) {
                addAuthUserList.add(new AuthUser(targetUserId, newRole.getRole(), authUserId));
            }
        }

        // 判断新增角色是否与'普通用户'角色冲突, 如果不冲突, 则将'普通角色'也赋予该用户
        if (!newRoleMap.containsKey(AuthSpecialRoles.COMMON_USER)
                && !collideRoleSet.contains(AuthSpecialRoles.COMMON_USER)) {
            addAuthUserList.add(new AuthUser(targetUserId, AuthSpecialRoles.COMMON_USER, authUserId));
        }

        if (deleteRoleList.size() != 0) {
            authUserRepository.deleteByUserIdAndRoleIn(targetUserId, deleteRoleList);
        }
        if (addAuthUserList.size() != 0) {
            authUserRepository.saveAll(addAuthUserList);
        }
        userRoleCache.invalidCacheByUser(targetUserId);
    }

    public void grantRoleToUser(String role, Integer userId, Integer authUserId, Long expire, String remark) {
        AuthRole newRole = authRoleRepository.findFirstByRole(role).orElseThrow(
                () -> new KbException(KbCode.AUTH_ROLE_NOT_EXISTS, "role not exists: " + role));
        List<String> rolesByUser = getUserRoleRelation(userId).stream()
                .map(AuthUser::getRole)
                .collect(Collectors.toList());
        List<AuthRole> roleList = authRoleRepository.findByRoleInOrderByRole(rolesByUser);
        roleList.add(newRole);
        Set<String> collideRoleSet = checkRoleCollideWith(roleList);
        if (null == expire) {
            expire = (System.currentTimeMillis() / 1000) + TimeUnit.DAYS.toSeconds(3650);
        }
        authUserRepository.grantRole(userId, authUserId, role, expire, remark);
        // 判断新增角色是否与'普通用户'角色冲突, 如果不冲突, 则将'普通角色'也赋予该用户
        if (!role.equals(AuthSpecialRoles.COMMON_USER)
                && !rolesByUser.contains(AuthSpecialRoles.COMMON_USER)
                && !collideRoleSet.contains(AuthSpecialRoles.COMMON_USER)) {
            authUserRepository.grantRole(userId, authUserId, AuthSpecialRoles.COMMON_USER, expire, remark);
        }
        userRoleCache.invalidCacheByUser(userId);
    }

    public void grantTempRoleToUser(String role, Integer userId, Long expire) {
        userRoleCache.addTempRoleToUser(userId, role, expire);
    }

    public void deleteRoleFromUser(String role, Integer userId) {
        authUserRepository.deleteByUserIdAndRole(userId, role);
        userRoleCache.invalidCacheByUser(userId);
    }

    public void deletePermission(String permission) {
        if (authRoleRepository.countByPermissions(permission) > 0) {
            throw new KbException(KbCode.ILLEGAL_STATE,
                    "permission still in use, deletion not allowed: " + permission)
                    .setLevel(KbException.LEVEL_INFO)
                    .setReadableMessage("不能删除仍在使用的权限");
        }
        authPermissionRepository.deleteByPermission(permission);
    }

    public void deletePermissionByFormat(String format) {
        Set<String> permissionSet = new HashSet<>();
        int step = 100;
        for (int i = 1; ; i ++) {
            Page<AuthPermission> permissionPage = authPermissionRepository
                    .findByFormatOrderByPermission(format, getPageable(i, step));
            List<AuthPermission> permissionList = permissionPage.getContent();
            for (AuthPermission permission : permissionList) {
                permissionSet.add(permission.getPermission());
            }
            if (permissionPage.isLast()) {
                break;
            }
        }
        long roleCount = authRoleRepository.countByPermissionsIn(permissionSet);
        if (roleCount > 0) {
            throw new KbException(KbCode.ILLEGAL_STATE,
                    "permission format still in use: " + format)
                    .setLevel(KbException.LEVEL_INFO)
                    .setReadableMessage("不能删除仍在使用的权限");
        }
        authPermissionRepository.deleteByFormat(format);
    }

    public void deleteRole(String role) {
        pruneExpiredAuth();
        if (authUserRepository.countByRole(role) > 0) {
            throw new KbException(KbCode.ILLEGAL_STATE,
                    "role still in use by user, deletion not allowed: " + role)
                    .setLevel(KbException.LEVEL_INFO)
                    .setReadableMessage("不能删除仍在使用的角色");
        }
        authRoleRepository.deleteByRole(role);
    }

    public void deleteRoleByFormat(String format) {
        pruneExpiredAuth();
        Set<String> roleSet = new HashSet<>();
        int step = 100;
        for (int i = 1; ; i ++) {
            Page<AuthRole> rolePage = authRoleRepository
                    .findByFormatOrderByRole(format, getPageable(i, step));
            List<AuthRole> roleList = rolePage.getContent();
            for (AuthRole role : roleList) {
                roleSet.add(role.getRole());
            }
            if (rolePage.isLast()) {
                break;
            }
        }
        long roleCount = authUserRepository.countByRoleIn(roleSet);
        if (roleCount > 0) {
            throw new KbException(KbCode.ILLEGAL_STATE,
                    "role format still in use: " + format)
                    .setLevel(KbException.LEVEL_INFO)
                    .setReadableMessage("不能删除仍在使用的角色");
        }
        authRoleRepository.deleteByFormat(format);
    }

    public void updatePermissionAuthTypes(String permission, List<AuthType> authTypes) {
        authPermissionRepository.updateAuthTypes(permission, authTypes);
    }

    public void updatePermissionLevel(String permission, Integer level) {
        authPermissionRepository.updateLevel(permission, level);
    }

    public void updateRoleCollideWith(String role, List<String> collideWithRoles) {
        authRoleRepository.updateCollideWith(role, collideWithRoles);
    }

    public void updateRolePermissions(String role, String[] permissions) {
        List<String> permissionList = (null == permissions || permissions.length == 0) ?
                null : Arrays.asList(permissions);
        if (null != permissionList) {
            Map<String, AuthPermission> permissionMap = getPermissionMapIn(permissionList);
            for (String p : permissions) {
                if (permissionMap.get(p) == null) {
                    throw new KbException(KbCode.AUTH_PERMISSION_NOT_EXISTS, "permission not exists: " + p);
                }
            }
        }
        authRoleRepository.updatePermissions(role, permissionList);
    }

    public void pruneExpiredAuth() {
        authUserRepository.pruneExpiredAuth();
    }

    public void invalidatePermissionCache() {
        permissionCache.invalidate();
    }

    // ----------------------------------------------

    public boolean hasPermission(Integer userId, String permission) {
        Set<String> userRoles = userRoleCache.getRoleSetByUser(userId);
        return permissionCache.hasPermission(userRoles, permission);
    }

    public boolean hasPermissionByRole(String role, String permission) {
        return permissionCache.hasPermission(role, permission);
    }

    // ----------------------------------------------

    public Page<AuthPermission> getPermissionList(Integer page, Integer pageSize) {
        return authPermissionRepository.findAllByOrderByPermission(getPageable(page, pageSize));
    }

    public List<AuthPermission> getPermissionListIn(List<String> permissions) {
        return authPermissionRepository.findByPermissionInOrderByPermission(permissions);
    }

    public Map<String, AuthPermission> getPermissionMapIn(List<String> permissions) {
        return getPermissionListIn(permissions).stream()
                .collect(Collectors.toMap(AuthPermission::getPermission, p -> p, (k1, k2) -> k1));
    }

    public Page<AuthPermission> getPermissionListByScope(
            AuthScope scope, String referenceId, Integer page, Integer pageSize) {
        if (null == referenceId) {
            return authPermissionRepository.findByScopeOrderByPermission(
                    scope.getValue(), getPageable(page, pageSize));
        } else {
            return authPermissionRepository.findByScopeAndReferenceIdOrderByPermission(
                    scope.getValue(), referenceId, getPageable(page, pageSize));
        }
    }

    public Page<AuthPermission> getPermissionListByScopeIn(List<Integer> scopes, Integer page, Integer pageSize) {
        return authPermissionRepository.findByScopeInOrderByPermission(scopes, getPageable(page, pageSize));
    }

    public Page<AuthPermission> getPermissionListByFormat(String format, Integer page, Integer pageSize) {
        return authPermissionRepository.findByFormatOrderByPermission(format, getPageable(page, pageSize));
    }

    public Page<AuthPermission> searchPermission(
            Integer scope, String referenceId,
            String permissionRegex, String descriptionRegex,
            Integer page, Integer pageSize) {
        return authPermissionRepository.searchForPage(scope, referenceId, permissionRegex, descriptionRegex, page, pageSize);
    }

    public List<AuthPermission> getPermissionListByFormatIn(Integer scope, List<String> formats) {
        if (null == scope) {
            return authPermissionRepository.findByFormatInOrderByPermission(formats);
        } else {
            return authPermissionRepository.findByScopeAndFormatInOrderByPermission(scope, formats);
        }
    }

    public Optional<AuthPermission> getAuthPermission(String permission) {
        return permissionCache.getAuthPermission(permission);
    }

    public Optional<AuthRole> getAuthRole(String role) {
        return permissionCache.getAuthRole(role);
    }

    public List<String> getPermissionListByUser(Integer userId) {
        Set<String> roleSet = userRoleCache.getRoleSetByUser(userId);
        List<String> permissions = new LinkedList<>();
        for (String role : roleSet) {
            Set<String> permissionSet = permissionCache.getPermissionSetByRole(role);
            permissions.addAll(permissionSet);
        }
        return permissions;
    }

    public List<String> getPermissionListByRole(String role, boolean allowCache) {
        if (allowCache) {
            return new LinkedList<>(permissionCache.getPermissionSetByRole(role));
        } else {
            List<String> permissions = authRoleRepository.findFirstByRole(role)
                    .orElseThrow(() -> new KbException(KbCode.AUTH_ROLE_NOT_EXISTS, "role not exists: " + role))
                    .getPermissions();
            return permissions == null ? Collections.emptyList() : permissions;
        }
    }

    public Page<AuthRole> getRoleList(Integer page, Integer pageSize) {
        return authRoleRepository.findAllByOrderByRole(getPageable(page, pageSize));
    }

    public List<AuthRole> getRoleListIn(List<String> roles) {
        return authRoleRepository.findByRoleInOrderByRole(roles);
    }

    public Page<AuthRole> getRoleListByScope(AuthScope scope, String referenceId, Integer page, Integer pageSize) {
        if (null == referenceId) {
            return authRoleRepository.findByScopeOrderByRole(
                    scope.getValue(), getPageable(page, pageSize));
        } else {
            return authRoleRepository.findByScopeAndReferenceIdOrderByRole(
                    scope.getValue(), referenceId, getPageable(page, pageSize));
        }
    }

    public Page<AuthRole> getRoleListByFormat(
            String format, Integer page, Integer pageSize) {
        return authRoleRepository.findByFormatOrderByRole(format, getPageable(page, pageSize));
    }

    public Page<AuthRole> searchRole(
            Integer scope, String referenceId,
            String roleRegex, String descriptionRegex,
            Integer page, Integer pageSize) {
        return authRoleRepository.searchForPage(scope, referenceId, roleRegex, descriptionRegex, page, pageSize);
    }

    public List<AuthRole> getRoleListByFormatIn(Integer scope, List<String> formats) {
        if (null == scope) {
            return authRoleRepository.findByFormatInOrderByRole(formats);
        } else {
            return authRoleRepository.findByScopeAndFormatInOrderByRole(scope, formats);
        }
    }

    public Page<AuthRole> getRoleListByPermission(String permission, Integer page, Integer pageSize) {
        return authRoleRepository.findByPermissionsOrderByRole(permission, getPageable(page, pageSize));
    }

    public List<String> getRoleListByUserFromCache(Integer userId) {
        return new ArrayList<>(userRoleCache.getRoleSetByUser(userId));
    }

    public List<AuthUser> getUserRoleRelation(Integer userId) {
        return authUserRepository.findByUserIdAndExpireTimeGreaterThan(
                userId, System.currentTimeMillis() / 1000);
    }

    public Optional<AuthUser> getUserRoleByUserAndRole(Integer userId, String role){
        return authUserRepository.findFirstByUserIdAndRoleAndExpireTimeGreaterThan(
                userId, role,System.currentTimeMillis()/1000);
    }

    public Page<AuthUser> getUserRoleByRole(String role, Integer page, Integer pageSize) {
        return authUserRepository.findByRoleAndExpireTimeGreaterThanOrderByCreateTimeDesc(
                role, System.currentTimeMillis() / 1000, getPageable(page, pageSize));
    }

    public long getPermissionCountByFormat(String format) {
        return authPermissionRepository.countByFormat(format);
    }

    public long getRoleCountByFormat(String format) {
        return authRoleRepository.countByFormat(format);
    }

    public long getRoleCountByPermission(String permission) {
        return authRoleRepository.countByPermissions(permission);
    }

    // -------------------------------------------------------

    private static Set<String> checkRoleCollideWith(List<AuthRole> roleList) {
        Set<String> roleSet = new LinkedHashSet<>();
        Set<String> collideRoleSet = new LinkedHashSet<>();
        for (AuthRole role : roleList) {
            roleSet.add(role.getRole());
            if (role.getCollideWith() != null) {
                collideRoleSet.addAll(role.getCollideWith());
            }
        }
        if (collideRoleSet.size() == 0) {
            return collideRoleSet;
        }
        Set<String> union = new HashSet<>(roleSet.size() + collideRoleSet.size());
        union.addAll(roleSet);
        union.addAll(collideRoleSet);
        if (union.size() != (roleSet.size() + collideRoleSet.size())) {
            throw new KbException(KbCode.AUTH_ROLE_COLLIDE,
                    "role collide: " + roleSet + " -> " + collideRoleSet)
                    .setLevel(KbException.LEVEL_INFO);
        }
        return collideRoleSet;
    }

    private static Pageable getPageable(Integer page, Integer pageSize) {
        int p = null == page ? 1 : page - 1;
        int ps = null == pageSize ? 20 : pageSize;
        return PageRequest.of(p, ps);
    }

}
