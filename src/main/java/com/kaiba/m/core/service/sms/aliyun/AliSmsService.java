package com.kaiba.m.core.service.sms.aliyun;

import com.aliyun.auth.credentials.Credential;
import com.aliyun.auth.credentials.provider.StaticCredentialProvider;
import com.aliyun.sdk.service.dysmsapi20170525.AsyncClient;
import com.aliyun.sdk.service.dysmsapi20170525.models.*;
import com.kaiba.lib.base.domain.sms.SmsModel;
import com.kaiba.lib.base.response.KbCode;
import com.kaiba.lib.base.response.KbException;
import com.kaiba.lib.base.util.GsonUtils;
import com.kaiba.m.core.util.JsonUtils;
import darabonba.core.client.ClientOverrideConfiguration;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/08/29 09:50
 * <a href="https://next.api.aliyun.com/document/Dysmsapi/2017-05-25/SendSms?spm=api-workbench.CodeSample%20Detail%20Page.0.0.56252e706fk7eH">阿里云短信文档</a>
 */
@Slf4j
@Service
public class AliSmsService {

    private static final String SMS_CLIENT_REGION = "cn-hangzhou";
    private static final String SMS_CLIENT_ENDPOINT = "dysmsapi.aliyuncs.com";
    private static final String SMS_SIGN_NAME = "开吧APP";
    private final Map<String, String> templateCodeMap;
    private final StaticCredentialProvider aliProvider;

    public AliSmsService(
            @Value("${ali.sms.access_key_id}") String accessKeyId,
            @Value("${ali.sms.access_key_secret}") String accessKeySecret
    ) {
        aliProvider = StaticCredentialProvider.create(Credential.builder()
                .accessKeyId(accessKeyId)
                .accessKeySecret(accessKeySecret)
                .build());
        templateCodeMap = Collections.unmodifiableMap(Arrays.stream(AliSmsTemplateCode.values())
                .collect(Collectors.toMap(AliSmsTemplateCode::getOldTempleId, AliSmsTemplateCode::getTemplateCode, (o1, o2) -> o1)));
    }

    public void sendMessage(SmsModel model) {
        List<String> phoneNumbers = model.getTargetArr();
        if(phoneNumbers.size() == 1) {
            sendSingleSms(model);
            return;
        }

        sendBatchSms(model);
    }

    private void sendSingleSms(SmsModel model) {
        AsyncClient client = AsyncClient.builder()
                .region(SMS_CLIENT_REGION)
                .credentialsProvider(aliProvider)
                .overrideConfiguration(ClientOverrideConfiguration.create().setEndpointOverride(SMS_CLIENT_ENDPOINT))
                .build();

        Map<String, String> map = new HashMap<>(4);
        //优先匹配placeholder
        if (model.getPlaceHolder() != null && model.getPlaceHolder().length > 0) {
            map.put("code", model.getPlaceHolder()[0]);
        } else if (model.getPlaceHolderMap() != null) {
            map.putAll(model.getPlaceHolderMap());
        }
        SendSmsRequest sendSmsRequest = SendSmsRequest.builder()
                .phoneNumbers(model.getTargetArr().get(0))
                .signName(SMS_SIGN_NAME)
                .templateCode(getTemplateCode(model.getTemplateId()))
                .templateParam(map.isEmpty() ? null : JsonUtils.getGson().toJson(map))
                .build();
        CompletableFuture<SendSmsResponse> response = client.sendSms(sendSmsRequest);

        try {
            SendSmsResponse resp = response.get();
            SendSmsResponseBody body = resp.getBody();
            log.info("code: {}, message: {}", body.getCode(), body.getMessage());
            processResponse(body.getCode(), body.getMessage());
        } catch (InterruptedException | ExecutionException e) {
            Thread.currentThread().interrupt();
            throw new KbException(KbCode.REQUEST_FAIL).setReadableMessage("短信发送失败: " + e.getMessage()).li();
        } finally {
            try {
                client.close();
            } catch (Exception e) {
                log.error("send message async failed: {}", e.getMessage());
            }
        }
    }

    private void sendBatchSms(SmsModel model) {
        AsyncClient client = AsyncClient.builder()
                .region(SMS_CLIENT_REGION)
                .credentialsProvider(aliProvider)
                .overrideConfiguration(ClientOverrideConfiguration.create().setEndpointOverride(SMS_CLIENT_ENDPOINT))
                .build();

        List<String> signNames = Collections.nCopies(model.getTargetArr().size(), SMS_SIGN_NAME);
        SendBatchSmsRequest sendBatchSmsRequest = SendBatchSmsRequest.builder()
                .phoneNumberJson(JsonUtils.getGson().toJson(model.getTargetArr()))
                .signNameJson(JsonUtils.getGson().toJson(signNames))
                .templateCode(getTemplateCode(model.getTemplateId()))
                .templateParamJson(assembleTemplateParam(model.getPlaceHolder(), model.getPlaceHolderMap()))
                .build();
        CompletableFuture<SendBatchSmsResponse> response = client.sendBatchSms(sendBatchSmsRequest);

        try {
            SendBatchSmsResponse resp = response.get();
            log.info("send batch sms response: {}", JsonUtils.getGson().toJson(response));
        } catch (InterruptedException | ExecutionException e) {
            Thread.currentThread().interrupt();
            throw new KbException(KbCode.REQUEST_FAIL).setReadableMessage("短信发送失败: " + e.getMessage()).li();
        } finally {
            try {
                client.close();
            } catch (Exception e) {
                log.error("send message async failed: {}", e.getMessage());
            }
        }
    }

    // --------------------------------------------

    private void processResponse(String code, String message) {
        switch (code) {
            case "OK":
                break;
            case "isv.DAY_LIMIT_CONTROL":
                throw new KbException(KbCode.REQUEST_FAIL).setReadableMessage("已达到当日限额").li();
            case "isv.DENY_IP_RANGE":
                throw new KbException(KbCode.REQUEST_FAIL).setReadableMessage("非中国内地地区").li();
            case "isv.OUT_OF_SERVICE":
                throw new KbException(KbCode.REQUEST_FAIL).setReadableMessage("业务停机，请联系管理员").li();
            case "isv.BUSINESS_LIMIT_CONTROL":
                String readableMessage = message.contains("天级") ? "该号码已达今日上限" : "短信发送频率过快，请稍后再试";
                throw new KbException(KbCode.REQUEST_FAIL).setReadableMessage(readableMessage).li();
            case "isv.BLACK_KEY_CONTROL_LIMIT":
                throw new KbException(KbCode.REQUEST_FAIL).setReadableMessage("因您投诉过短信服务商, 导致验证码无法送达. 请至开吧意见反馈联系运营人员处理").li();
            default:
                throw new KbException(KbCode.REQUEST_FAIL).setReadableMessage("短信发送失败").li();
        }
    }

    private String getTemplateCode(String oldTemple) {
        if (oldTemple != null && oldTemple.startsWith("SMS_")) {
            return oldTemple;
        }
        return templateCodeMap.getOrDefault(oldTemple, AliSmsTemplateCode.DEFAULT.getTemplateCode());
    }

    private String assembleTemplateParam(String[] datas, Map<String, String> paramMap) {
        if (datas != null) {
            StringBuilder sb = new StringBuilder("[");
            for (String s : datas) {
                sb.append("{\"code\":\"")
                    .append(s)
                    .append("\"},");
            }
            sb.replace(sb.length() - 1, sb.length(), "]");
            return sb.toString();
        } else if (paramMap != null) {
            return GsonUtils.getGson().toJson(paramMap);
        }
        return null;
    }
}
