package com.kaiba.m.core.service.floatview;

import com.kaiba.lib.base.constant.floatview.FloatViewState;
import com.kaiba.lib.base.domain.floatview.FloatWindowModel;
import com.kaiba.lib.base.response.KbCode;
import com.kaiba.lib.base.response.KbException;
import com.kaiba.m.core.domain.floatview.FloatIcon;
import com.kaiba.m.core.domain.floatview.FloatWindow;
import com.kaiba.m.core.repository.floatview.FloatWindowRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.util.Optional;
import java.util.regex.Pattern;

/**
 * author: lyux
 * date: 19-12-25
 */
@Slf4j
@Service
public class FloatWindowService {

    private static final Pattern COLOR_PATTERN = Pattern.compile("^[0-9a-fA-F]{8}$");

    private final FloatWindowRepository floatWindowRepository;

    public FloatWindowService(FloatWindowRepository floatWindowRepository) {
        this.floatWindowRepository = floatWindowRepository;
    }

    public FloatWindow createFloatWindow(FloatWindow floatWindow) {
        log.info("create float window: " + floatWindow);
        verifyData(floatWindow);
        long currentTime = System.currentTimeMillis() / 1000;
        floatWindow.setState(FloatViewState.PREPARE.getValue());
        floatWindow.setCreateTime(currentTime);
        floatWindow.setUpdateTime(currentTime);
        return floatWindowRepository.insert(floatWindow);
    }

    public FloatWindow updateState(String id, FloatViewState state) {
        FloatWindow floatWindow = floatWindowRepository.findById(id).orElseThrow(() ->
                new KbException(KbCode.RESOURCE_NOT_FOUND, "float window not found: " + id).li());
        return updateState(floatWindow, state);
    }

    public FloatWindow updateState(FloatWindow floatWindow, FloatViewState state) {
        log.info("update float window state: " + floatWindow + ", to " + state);
        FloatViewState oldState = FloatViewState.valueOf(floatWindow.getState()).orElseThrow(() ->
                new KbException(KbCode.ILLEGAL_STATE, "float window state wrong: " + floatWindow));
        if (!oldState.isStateChangeAllowed(state)) {
            throw new KbException(KbCode.ILLEGAL_STATE,
                    "float window state change not allowed: " + floatWindow + ", to state: " + state).li();
        }
        return floatWindowRepository.updateState(floatWindow.getId(), state);
    }

    public void stopOnlineExcept(FloatWindow floatWindow) {
        floatWindowRepository.stopOnlineExcept(floatWindow.getSiteId(), floatWindow.getId(), floatWindow.getMark());
    }

    public FloatWindow updateData(FloatWindow floatWindow) {
        log.info("update float window: " + floatWindow);
        verifyData(floatWindow);
        return floatWindowRepository.updateData(floatWindow);
    }

    public void deleteById(String floatWindowId) {
        FloatWindow floatWindow = floatWindowRepository.findById(floatWindowId).orElseThrow(() ->
                new KbException(KbCode.REQUEST_PARAM_INVALID, "float window not exists: " + floatWindowId).ld());
        if (floatWindow.getState() != FloatViewState.PREPARE.getValue()) {
            throw new KbException(KbCode.ILLEGAL_STATE, "delete allowed only for state prepare").ld();
        }
        floatWindowRepository.deleteById(floatWindowId);
    }

    public Optional<FloatWindow> getById(String id) {
        return floatWindowRepository.findById(id);
    }

    public Optional<FloatWindow> getLastBySiteId(Integer siteId, String mark, Integer[] states) {
        if (null == states) {
            if (null == mark) {
                return floatWindowRepository.findFirstBySiteIdOrderByUpdateTimeDesc(siteId);
            } else {
                return floatWindowRepository.findFirstBySiteIdAndMarkOrderByUpdateTimeDesc(siteId, mark);
            }
        } else {
            if (null == mark) {
                return floatWindowRepository.findFirstBySiteIdAndStateInOrderByUpdateTimeDesc(siteId, states);
            } else {
                return floatWindowRepository.findFirstBySiteIdAndMarkAndStateInOrderByUpdateTimeDesc(siteId, mark, states);
            }
        }
    }

    public Page<FloatWindow> getPageBySiteId(Integer siteId, String mark, Integer[] states, Integer page, Integer pageSize) {
        Pageable pageable = createPageable(page, pageSize);
        if (null == states) {
            if (null == mark) {
                return floatWindowRepository.findBySiteIdOrderByUpdateTimeDesc(siteId, pageable);
            } else {
                return floatWindowRepository.findBySiteIdAndMarkOrderByUpdateTimeDesc(siteId, mark, pageable);
            }
        } else {
            if (null == mark) {
                return floatWindowRepository.findBySiteIdAndStateInOrderByUpdateTimeDesc(siteId, states, pageable);
            } else {
                return floatWindowRepository.findBySiteIdAndMarkAndStateInOrderByUpdateTimeDesc(siteId, mark, states, pageable);
            }
        }
    }

    Page<FloatWindow> getPageByStates(Integer[] states, Integer page, Integer pageSize) {
        Pageable pageable = createPageable(page, pageSize);
        return floatWindowRepository.findByStateInOrderByUpdateTimeDesc(states, pageable);
    }

    private static void verifyData(FloatWindow floatWindow) {
        if (floatWindow.getSiteId() == null) {
            throw new KbException(KbCode.ILLEGAL_ARGUMENT, "need siteId. " + floatWindow).li();
        }
        if (floatWindow.getMark() == null) {
            throw new KbException(KbCode.ILLEGAL_ARGUMENT, "need mark. " + floatWindow).li();
        }
        if (floatWindow.getLink() == null) {
            throw new KbException(KbCode.ILLEGAL_ARGUMENT, "need link. " + floatWindow).li();
        }
        if (floatWindow.getWidthScreenRatio() == null) {
            floatWindow.setWidthScreenRatio(75);
        } else if (floatWindow.getWidthScreenRatio() < 1 || floatWindow.getWidthScreenRatio() > 100) {
            throw new KbException(KbCode.ILLEGAL_ARGUMENT, "widthScreenRatio should be [10, 100]. " + floatWindow).li();
        }
        if (floatWindow.getWidthHeightRatio() == null) {
            floatWindow.setWidthHeightRatio(100);
        } else if (floatWindow.getWidthHeightRatio() < 1) {
            throw new KbException(KbCode.ILLEGAL_ARGUMENT, "widthHeightRatio should be positive. " + floatWindow).li();
        }
        if (floatWindow.getOnlyForLogin() == null) {
            floatWindow.setOnlyForLogin(false);
        }
        if (floatWindow.getDecorateLink() == null) {
            floatWindow.setDecorateLink(true);
        }
        if (floatWindow.getBackgroundColor() == null) {
            floatWindow.setBackgroundColor(FloatWindowModel.DEFAULT_BACKGROUND_COLOR);
        }
        if (!COLOR_PATTERN.matcher(floatWindow.getBackgroundColor()).matches()) {
            throw new KbException(KbCode.ILLEGAL_ARGUMENT,
                    "color format malformed: " + floatWindow.getBackgroundColor())
                    .setReadableMessage("颜色格式错误")
                    .setLevel(KbException.LEVEL_INFO);
        }
        if (floatWindow.getScheduledEndTime() == null) {
            throw new KbException(KbCode.ILLEGAL_ARGUMENT, "need endTime. " + floatWindow).li();
        }
        long current = System.currentTimeMillis() / 1000;
        if (floatWindow.getScheduledEndTime() <= current) {
            throw new KbException(KbCode.ILLEGAL_ARGUMENT, "endTime should be greater then current timestamp. " + floatWindow).li();
        }
        if (floatWindow.getScheduledStartTime() != null) {
            if (floatWindow.getScheduledStartTime() > floatWindow.getScheduledEndTime()) {
                throw new KbException(KbCode.ILLEGAL_ARGUMENT, "endTime should be greater then startTime. " + floatWindow).li();
            }
        }
    }

    private static Pageable createPageable(Integer page, Integer pageSize) {
        int p = (page == null || page < 1) ? 0 : page - 1;
        int ps = (pageSize == null || pageSize > 100) ? 15 : pageSize;
        return PageRequest.of(p, ps);
    }

}
