package com.kaiba.m.core.service.note.event.handler;

import com.kaiba.lib.base.cache.LazyExpireCache;
import com.kaiba.lib.base.constant.note.NoteMqEventType;
import com.kaiba.lib.base.domain.note.NoteMqEventModel;
import com.kaiba.m.core.domain.circle.CircleThread;
import com.kaiba.m.core.domain.news.legacy.SiteNewsThread;
import com.kaiba.m.core.domain.note.Note;
import com.kaiba.m.core.domain.note.NoteComment;
import com.kaiba.m.core.domain.note.NoteCommentReview;
import com.kaiba.m.core.domain.note.NoteReview;
import com.kaiba.m.core.domain.note.NoteThread;
import com.kaiba.m.core.middleware.amqpsender.NoteEventSender;
import com.kaiba.m.core.service.circle.CircleService;
import com.kaiba.m.core.service.news.legacy.NewsService;
import com.kaiba.m.core.service.note.event.INoteEventReceiver;
import com.kaiba.m.core.service.note.thread.NoteThreadService;
import com.kaiba.m.core.service.user.UserRosterService;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.util.List;
import java.util.Objects;

/**
 * 文广幸福吧 基于发帖回复触发mq消息完成任务
 *
 * <AUTHOR>
 * @version HcrtHappyTaskHandler, v0.1 2023/11/27 16:32 daopei Exp $
 **/
@Slf4j
@Component
public class HcrtHappyTaskHandler implements INoteEventReceiver {

    private static final Integer SITE_HZ = 9;
    private static final String REVEAL_MAIN_THREAD_HZ_KEY = "reveal_program_main_thread_key_9";
    private static final String HCRT_HAPPY_USER_ROSTER_KEY = "hcrt_happy_2023";


    private final CircleService circleService;
    private final NewsService newsService;
    private final NoteThreadService noteThreadService;
    private final UserRosterService userRosterService;
    private final NoteEventSender noteEventSender;

    private final LazyExpireCache<String> newsThreadCache;
    private final LazyExpireCache<String> revealThreadCache;
    private final LazyExpireCache<String> circleThreadCache;


    public HcrtHappyTaskHandler(@NonNull CircleService circleService,
                                @NonNull NewsService newsService,
                                @NonNull NoteThreadService noteThreadService,
                                @NonNull UserRosterService userRosterService,
                                @NonNull NoteEventSender noteEventSender) {
        this.circleService = circleService;
        this.newsService = newsService;
        this.noteThreadService = noteThreadService;
        this.userRosterService = userRosterService;
        this.noteEventSender = noteEventSender;
        this.newsThreadCache = new LazyExpireCache.Builder<String>()
                .setExpireTime(Duration.ofMinutes(30).toMillis())
                .setSupplier(this::getNewsMainThread)
                .setDataValidator(Objects::nonNull)
                .create();
        this.revealThreadCache = new LazyExpireCache.Builder<String>()
                .setExpireTime(Duration.ofMinutes(30).toMillis())
                .setSupplier(this::getRevealMainThread)
                .setDataValidator(Objects::nonNull)
                .create();
        this.circleThreadCache = new LazyExpireCache.Builder<String>()
                .setExpireTime(Duration.ofMinutes(30).toMillis())
                .setSupplier(this::getCircleMainThread)
                .setDataValidator(Objects::nonNull)
                .create();
    }

    @Override
    public void onNoteAdd(Note note, NoteReview noteReview) {
        try {
            if (checkThreadAccess(note.getThreads()) && checkUserAccess(note.getUserId())) {
                NoteMqEventModel eventModel =
                        NoteMqEventModel.builder()
                                .noteId(note.getId())
                                .noteReviewId(noteReview == null ? null : noteReview.getId())
                                .threads(note.getThreads())
                                .eventType(NoteMqEventType.NOTE_CREATE.name())
                                .userId(note.getUserId()).build();
                noteEventSender.send(eventModel);
            }
        } catch (Exception e) {
            log.warn("send note create event mq fail , e : {}", e.getMessage());
        }
    }

    @Override
    public void onCommentAdd(Note note, NoteComment comment, NoteCommentReview commentReview) {
        try {
            if (checkThreadAccess(comment.getThreads()) && checkUserAccess(comment.getUserId())) {
                NoteMqEventModel eventModel =
                        NoteMqEventModel.builder()
                                .noteId(note.getId())
                                .commentId(comment.getId())
                                .commentReviewId(commentReview == null ? null : commentReview.getId())
                                .threads(comment.getThreads())
                                .eventType(NoteMqEventType.NOTE_COMMENT.name())
                                .userId(comment.getUserId()).build();
                noteEventSender.send(eventModel);
            }
        } catch (Exception e) {
            log.warn("send comment create event mq fail , e : {}", e.getMessage());
        }
    }

    //--------------------------------------------

    /**
     * 花名册判断
     *
     * @param userId
     * @return
     */
    private boolean checkUserAccess(Integer userId) {
        return userRosterService.getByUserIdAndInstance(userId, HCRT_HAPPY_USER_ROSTER_KEY)
                .filter(roster -> roster.getState() == 1).isPresent();
    }

    /**
     * 判断是否业务通知板块要求
     *
     * @param threads
     * @return
     */
    private boolean checkThreadAccess(List<String> threads) {
        String newsThread = newsThreadCache.getData();
        String revealThread = revealThreadCache.getData();
        String circleThread = circleThreadCache.getData();
        return threads.contains(newsThread) || threads.contains(revealThread) || threads.contains(circleThread);
    }


    private String getCircleMainThread() {
        return circleService.getMainCircleBySite(SITE_HZ).map(CircleThread::getThreadId).orElse("");
    }

    private String getNewsMainThread() {
        return newsService.getSiteNewsThreadBySiteId(SITE_HZ).map(SiteNewsThread::getThreadId).orElse("");
    }

    private String getRevealMainThread() {
        return noteThreadService.getThreadByKey(REVEAL_MAIN_THREAD_HZ_KEY).map(NoteThread::getId).orElse("");
    }
}
