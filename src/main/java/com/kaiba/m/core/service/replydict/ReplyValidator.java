package com.kaiba.m.core.service.replydict;

import com.kaiba.lib.base.response.KbCode;
import com.kaiba.lib.base.response.KbException;
import com.kaiba.lib.base.util.StringUtils;
import com.kaiba.m.core.domain.replydict.ReplyContent;
import com.kaiba.m.core.domain.replydict.ReplyKey;

/**
 * Created by shenxl on 2020/10/15
 */
class ReplyValidator {
    private ReplyValidator() {
    }

    static void validateReplyKey(ReplyKey replyKey) {
        if (null == replyKey.getSiteId()) {
            throw new KbException(KbCode.ILLEGAL_ARGUMENT).setReadableMessage("电台标识不能为空").li();
        }

        if (null == replyKey.getCreateUserId()) {
            throw new KbException(KbCode.ILLEGAL_ARGUMENT).setReadableMessage("userId不能为空").li();
        }

        if (StringUtils.isEmpty(replyKey.getKey())) {
            throw new KbException(KbCode.ILLEGAL_ARGUMENT).setReadableMessage("关键词不能为空").li();
        } else {
            if (replyKey.getKey().length() > 20) {
                throw new KbException(KbCode.ILLEGAL_ARGUMENT).setReadableMessage("关键词长度不能超过20个字符").li();
            }
        }

        if (StringUtils.isEmpty(replyKey.getGroupId())) {
            throw new KbException(KbCode.ILLEGAL_ARGUMENT).setReadableMessage("groupId不能为空").li();
        }
    }

    static void validateReplyContent(ReplyContent replyContent) {
        if (null == replyContent.getSiteId()) {
            throw new KbException(KbCode.ILLEGAL_ARGUMENT).setReadableMessage("电台标识不能为空").li();
        }

        if (null == replyContent.getCreateUserId()) {
            throw new KbException(KbCode.ILLEGAL_ARGUMENT).setReadableMessage("userId不能为空").li();
        }

        if (!StringUtils.isEmpty(replyContent.getLink())) {
            if (!StringUtils.isValidUrl(replyContent.getLink())) {
                throw new KbException(KbCode.ILLEGAL_ARGUMENT).setReadableMessage("链接格式错误").li();
            }
        }
    }
}