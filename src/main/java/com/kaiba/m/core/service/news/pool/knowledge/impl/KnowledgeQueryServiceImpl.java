package com.kaiba.m.core.service.news.pool.knowledge.impl;

import com.google.common.collect.Lists;
import com.kaiba.lib.base.domain.news.pool.knowledge.BasedAnswerModel;
import com.kaiba.lib.base.domain.news.pool.knowledge.KnowledgeModel;
import com.kaiba.m.core.domain.knowledge.Knowledge;
import com.kaiba.m.core.repository.news.pool.knowledge.KnowledgeRepository;
import com.kaiba.m.core.service.news.pool.knowledge.IBasedAnswerQueryService;
import com.kaiba.m.core.service.news.pool.knowledge.IKnowledgeQueryService;
import com.kaiba.m.core.service.news.pool.knowledge.IQuestionLogService;
import com.kaiba.m.core.service.news.pool.knowledge.mapper.KnowledgeMapping;
import com.kaiba.m.core.service.opensearch.news.NewsSearchClient;
import com.kaiba.m.core.service.opensearch.news.NewsSearchConfig;
import com.kaiba.m.core.service.opensearch.news.OpenSearchNewsDTO;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.StreamSupport;
import javax.annotation.Resource;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

/**
 * Description: 知识库查询Service实现
 * Author: ZM227
 * Date: 2025/1/21 10:11
 */
@Service
public class KnowledgeQueryServiceImpl implements IKnowledgeQueryService {

    @Resource
    private NewsSearchClient newsSearchClient;
    @Resource
    private KnowledgeRepository knowledgeRepository;
    @Resource
    private KnowledgeMapping knowledgeMapping;
    @Resource
    private IBasedAnswerQueryService answerQueryService;
    @Resource
    private IQuestionLogService questionLogService;

    @Override
    public Page<KnowledgeModel> queryKnowledge(NewsSearchConfig searchConfig, Pageable pageable) {
        Page<OpenSearchNewsDTO> newsDTOPage = newsSearchClient.commonSearch(searchConfig, pageable);
        if (CollectionUtils.isEmpty(newsDTOPage.getContent())) {
            return new PageImpl<>(Lists.newArrayList(), newsDTOPage.getPageable(),
                newsDTOPage.getTotalElements());
        }
        List<String> knowledgeIds = newsDTOPage.get().map(OpenSearchNewsDTO::getKnowledgeId)
            .collect(Collectors.toList());
        Iterable<Knowledge> knowledge = knowledgeRepository.findAllById(knowledgeIds);
        return new PageImpl<>(knowledgeMapping.knowledgeDomainToModelList(
            StreamSupport.stream(knowledge.spliterator(), false).collect(Collectors.toList())),
            newsDTOPage.getPageable(), newsDTOPage.getTotalElements());
    }

    @Override
    public String queryKnowledgeContent(NewsSearchConfig searchConfig) {
        // 先查询常见回复
//        if (searchConfig.getBasedAnswer()) {
        BasedAnswerModel basedAnswerModel = answerQueryService.queryBasedAnswer(
            searchConfig.getBaseId(), searchConfig.getCategoryIds(), searchConfig.getKeyWords());
        if (Objects.nonNull(basedAnswerModel)) {
            if (searchConfig.getAddLog()) {
                questionLogService.addQuestionLog(searchConfig, basedAnswerModel);
            }
            return basedAnswerModel.getContent();
        }
        return null;
//        }
//        Page<OpenSearchNewsDTO> newsDTOPage = newsSearchClient.commonSearch(searchConfig, null);
//        if (CollectionUtils.isEmpty(newsDTOPage.getContent())) {
//            return null;
//        }
//        OpenSearchNewsDTO newsDTO = newsDTOPage.getContent().get(0);
//        // 增加热度
//        if (searchConfig.getPlusPopularity()) {
//            knowledgeRepository.plusPopularity(newsDTO.getKnowledgeId(), 1);
//        }
//        if (searchConfig.getAddLog()) {
//            questionLogService.addQuestionLog(searchConfig, newsDTO);
//        }
//        return newsDTOPage.getContent().get(0).getContent();
    }

    @Override
    public KnowledgeModel findKnowledgeById(String knowledgeId) {
        Optional<Knowledge> knowledgeOptional = knowledgeRepository.findById(knowledgeId);
        return knowledgeOptional.map(
            knowledge -> knowledgeMapping.knowledgeDomainToModel(knowledge)).orElse(null);
    }
}
