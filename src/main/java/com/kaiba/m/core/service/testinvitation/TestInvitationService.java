package com.kaiba.m.core.service.testinvitation;

import com.kaiba.lib.base.constant.common.KbContentState;
import com.kaiba.lib.base.domain.testinvitation.TestInvitationModifyModel;
import com.kaiba.lib.base.response.KbCode;
import com.kaiba.lib.base.response.KbException;
import com.kaiba.lib.base.util.appaction.AppActionUtils;
import com.kaiba.m.core.domain.testinvitation.TestInvitation;
import com.kaiba.m.core.repository.IdsRepository;
import com.kaiba.m.core.repository.testinvitation.TestInvitationRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

/**
 * author: lyux
 * date: 2023-07-25
 */
@Slf4j
@Service
public class TestInvitationService {

    private static final SeqCodeGenerator CODE_GENERATOR = new SeqCodeGenerator(4, 3785);

    private final IdsRepository idsRepository;
    private final TestInvitationRepository invitationRepository;

    public TestInvitationService(
            IdsRepository idsRepository,
            TestInvitationRepository invitationRepository
    ) {
        this.idsRepository = idsRepository;
        this.invitationRepository = invitationRepository;
    }

    public TestInvitation createInvitation(TestInvitationModifyModel model) {
        TestInvitation invitation = new TestInvitation();
        invitation.setSiteId(model.getSiteId());
        invitation.setState(KbContentState.ONLINE.getValue());
        invitation.setAction(model.getAction());
        invitation.setActionParams(model.getActionParams());
        invitation.setDescription(model.getDescription());
        invitation.setCreateTime(System.currentTimeMillis());
        AppActionUtils.verifyAction(invitation);
        long seq = idsRepository.incrSeq("k_test_invitation");
        invitation.setSeq((int) seq % 10000);
        TestInvitation created = invitationRepository.insert(invitation);
        log.info("test invitation created: " + invitation);
        return created;
    }

    public TestInvitation updateInvitation(TestInvitationModifyModel model) {
        TestInvitation invitation = invitationRepository.findById(model.getId())
                .orElseThrow(() -> new KbException(KbCode.RESOURCE_NOT_FOUND).li());
        invitation.setAction(model.getAction());
        invitation.setActionParams(model.getActionParams());
        invitation.setDescription(model.getDescription());
        AppActionUtils.verifyAction(invitation);
        TestInvitation updated = invitationRepository.save(invitation);
        log.info("test invitation updated: " + invitation);
        return updated;
    }

    public void updateInvitationState(String invitationId, Integer state) {
        KbContentState.resolveByValue(state).orElseThrow(
                () -> new KbException(KbCode.ILLEGAL_ARGUMENT, "unknown state: " + state).li());
        invitationRepository.updateState(invitationId, state);
    }

    public void deleteInvitationById(String invitationId) {
        invitationRepository.deleteById(invitationId);
    }

    public Optional<TestInvitation> getInvitationById(String invitationId) {
        return invitationRepository.findById(invitationId);
    }

    public Optional<TestInvitation> getLastInvitationByCode(int code) {
        int seq = code2seq(code);
        return invitationRepository.findLastBySeqOrderByIdAsc(seq);
    }

    public Optional<TestInvitation> getLastInvitationBySeq(Integer seq) {
        return invitationRepository.findLastBySeqOrderByIdAsc(seq);
    }

    public List<TestInvitation> getInvitationListByCode(int code, Integer page, Integer pageSize) {
        int seq = code2seq(code);
        return getInvitationListBySeq(seq, page, pageSize);
    }

    public List<TestInvitation> getInvitationListBySeq(Integer seq, Integer page, Integer pageSize) {
        int p = page == null ? 0 : page - 1;
        int ps = pageSize == null ? 20 : pageSize;
        Pageable pageable = PageRequest.of(p, ps);
        return invitationRepository.getBySeqOrderByIdDesc(seq, pageable);
    }

    public Page<TestInvitation> getInvitationPageBySite(
            Integer siteId, Integer state, Integer page, Integer pageSize) {
        int p = page == null ? 0 : page - 1;
        int ps = pageSize == null ? 20 : pageSize;
        Pageable pageable = PageRequest.of(p, ps);
        if (state == null) {
            return invitationRepository.findBySiteIdOrderByIdDesc(siteId, pageable);
        } else {
            return invitationRepository.findBySiteIdAndStateOrderByIdDesc(siteId, state, pageable);
        }
    }

    public static int seq2code(int seq) {
        return CODE_GENERATOR.generate(seq);
    }

    public static int code2seq(int code) {
        return CODE_GENERATOR.reverse(code);
    }

}
