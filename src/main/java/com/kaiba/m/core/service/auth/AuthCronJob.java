package com.kaiba.m.core.service.auth;

import com.kaiba.m.core.service.auth.permission.PermissionService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * author: lyux
 * date: 21-06-24
 */
@Slf4j
@Component
public class AuthCronJob {

    private final PermissionService permissionService;

    public AuthCronJob(PermissionService permissionService) {
        this.permissionService = permissionService;
    }

    @XxlJob("auth-prune-expire")
    public ReturnT<String> pruneExpiredUserAuth(String param) {
        permissionService.pruneExpiredAuth();
        return ReturnT.SUCCESS;
    }

}
