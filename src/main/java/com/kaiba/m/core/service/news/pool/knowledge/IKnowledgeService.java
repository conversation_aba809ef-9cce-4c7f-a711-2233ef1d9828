package com.kaiba.m.core.service.news.pool.knowledge;

import com.kaiba.lib.base.domain.news.pool.knowledge.KnowledgeCreateModel;
import com.kaiba.lib.base.domain.news.pool.knowledge.KnowledgeModel;

/**
 * Description: 知识条目Service层接口
 * Author: ZM227
 * Date: 2025/1/2 11:18
 */
public interface IKnowledgeService {

    /**
     * 新增知识条目
     *
     * @param createModel 知识条目新增model
     * @return 知识条目code
     */
    String addKnowledge(KnowledgeCreateModel createModel);

    /**
     * 更新知识条目
     *
     * @param knowledgeModel 待更新的知识条目数据
     * @return 更新结果
     */
    boolean updateKnowledge(KnowledgeModel knowledgeModel);

    /**
     * 增加知识点热度
     *
     * @param knowledgeId 知识点ID
     * @param popNum      增加的热度值
     */
    void plusPopularity(String knowledgeId, int popNum);

}
