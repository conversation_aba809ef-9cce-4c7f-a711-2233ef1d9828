package com.kaiba.m.core.service.note.thread;

import com.kaiba.lib.base.constant.note.NoteDefaults;
import com.kaiba.lib.base.response.KbCode;
import com.kaiba.lib.base.response.KbException;
import com.kaiba.m.core.domain.note.Note;
import com.kaiba.m.core.domain.note.NoteThread;
import com.kaiba.m.core.domain.note.NoteThreadHot;
import com.kaiba.m.core.domain.note.NoteThreadTop;
import com.kaiba.m.core.repository.note.NoteThreadHotRepository;
import com.kaiba.m.core.repository.note.NoteThreadRepository;
import com.kaiba.m.core.repository.note.NoteThreadTopRepository;
import com.kaiba.m.core.util.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * author: lyux
 * date: 19-6-3
 */
@Slf4j
@Service
public class NoteThreadDBService {

    private final NoteThreadRepository noteThreadRepository;
    private final NoteThreadHotRepository noteThreadHotRepository;
    private final NoteThreadTopRepository noteThreadTopRepository;

    public NoteThreadDBService(
            NoteThreadRepository noteThreadRepository,
            NoteThreadHotRepository noteThreadHotRepository,
            NoteThreadTopRepository noteThreadTopRepository
    ) {
        this.noteThreadRepository = noteThreadRepository;
        this.noteThreadHotRepository = noteThreadHotRepository;
        this.noteThreadTopRepository = noteThreadTopRepository;
    }

    Optional<NoteThread> getThreadById(String threadId) {
        return noteThreadRepository.findById(threadId);
    }

    Optional<NoteThread> getThreadByKey(String key) {
        return noteThreadRepository.findFirstByKey(key);
    }

    List<NoteThread> getThreadListIn(List<String> threadIds) {
        return noteThreadRepository.findByIdIn(threadIds);
    }

    Page<NoteThread> getThreadListBySite(Integer siteId, Integer page, Integer pageSize) {
        PageRequest pageable = PageRequest.of(page == null ? 0 : page - 1, pageSize == null ? 15 : pageSize);
        return noteThreadRepository.findBySiteId(siteId, pageable);
    }

    List<NoteThread> getThreadListByRoute(String threadId, Integer page, Integer pageSize) {
        PageRequest pageable = PageRequest.of(page == null ? 0 : page - 1, pageSize == null ? 15 : pageSize);
        return noteThreadRepository.findByRouteThreads(threadId, pageable);
    }

    List<NoteThread> searchThreadListByTitle(String title, Integer page, Integer pageSize) {
        PageRequest pageable = PageRequest.of(page == null ? 0 : page - 1, pageSize == null ? 15 : pageSize);
        return noteThreadRepository.searchListByTitle(title, pageable);
    }

    Optional<NoteThreadHot> getNoteHotById(String id) {
        return noteThreadHotRepository.findById(id);
    }

    Optional<NoteThreadHot> getNoteHotByThreadAndNote(String threadId, String noteId) {
        return noteThreadHotRepository.findFirstByNoteIdAndThreadId(noteId, threadId);
    }

    Page<NoteThreadHot> getNoteHotListByThread(String threadId, Integer page, Integer pageSize) {
        PageRequest pageable = PageRequest.of(
                page == null ? 0 : page - 1,
                pageSize == null ? 15 : pageSize);
        return noteThreadHotRepository.findByThreadIdOrderByRefreshTimeDesc(threadId, pageable);
    }

    List<NoteThreadHot> getNoteHotListByNoteId(String noteId) {
        return noteThreadHotRepository.findByNoteId(noteId);
    }

    List<NoteThreadHot> getNoteHotListByNoteIds(List<String> noteIds) {
        return noteThreadHotRepository.findAllByNoteIdIn(noteIds);
    }

    Page<NoteThreadTop> getNoteTopListByThread(String threadId, Integer page, Integer pageSize) {
        PageRequest pageable = PageRequest.of(
                page == null ? 0 : page - 1,
                pageSize == null ? NoteDefaults.THREAD_MAX_TOP : pageSize);
        return noteThreadTopRepository.findByThreadIdOrderByRefreshTimeDesc(threadId, pageable);
    }

    List<NoteThreadTop> getNoteTopListByThreadIn(List<String> threadIdList) {
        return noteThreadTopRepository.findByThreadIdIn(threadIdList);
    }

    List<NoteThreadTop> getNoteTopListByNoteId(String noteId) {
        return noteThreadTopRepository.findByNoteId(noteId);
    }

    boolean isThreadExistsByKey(String key) {
        return noteThreadRepository.existsByKey(key);
    }

    // ----------------------------------------------------------------------
    // 帖子板块管理

    NoteThread threadCreate(NoteThread thread) {
        if (thread.getTopMax() == null) {
            thread.setTopMax(NoteDefaults.DEFAULT_THREAD_MAX_TOP);
        }
        if (thread.getHotMax() == null) {
            thread.setTopMax(NoteDefaults.DEFAULT_THREAD_MAX_HOT);
        }
        if (thread.getRouteThreads() != null && thread.getRouteThreads().size() != 0) {
            List<String> routeThreads = thread.getRouteThreads();
            if (noteThreadRepository.findByIdIn(routeThreads).size() != routeThreads.size()) {
                throw new KbException(KbCode.NOTE_THREAD_NOT_EXISTS,
                        "one of the threads not exist: " + JsonUtils.getGson().toJson(routeThreads)).li();
            }
        }
        if (thread.getKey() != null && noteThreadRepository.existsByKey(thread.getKey())) {
            throw new KbException(KbCode.NOTE_THREAD_NOT_EXISTS,
                    "thread key already exists: " + thread.getKey()).li();
        }
        thread.setCreateTime(System.currentTimeMillis() / 1000);
        thread.setCreateTimeMS(System.currentTimeMillis());
        return noteThreadRepository.save(thread);
    }

    void saveAllHots(List<NoteThreadHot> noteThreadHots) {
        noteThreadHotRepository.saveAll(noteThreadHots);
    }

    NoteThreadHot threadNoteRefreshHot(String threadId, String noteId) {
        return noteThreadHotRepository.updateHotNoteRefreshTime(threadId, noteId);
    }

    void threadNoteRefreshHots(List<String> noteIds, long refreshTime) {
        noteThreadHotRepository.updateHotNotesRefreshTime(noteIds, refreshTime);
    }

    NoteThreadHot threadNoteToHot(Note note, String threadId, String title, String cover) {
        NoteThread thread = noteThreadRepository.findById(threadId).orElseThrow(() -> new KbException(
                KbCode.NOTE_THREAD_NOT_EXISTS, "thread not found: " + threadId).li());
        if (!noteThreadHotRepository.existsByNoteIdAndThreadId(note.getId(), threadId)) {
            int hotMax = thread.getHotMax() == null ? NoteDefaults.DEFAULT_THREAD_MAX_HOT : thread.getHotMax();
            if (hotMax == 0) {
                throw new KbException(KbCode.NOTE_THREAD_HOT_MAX).r("该板块没有开放热议功能").li();
            } else if (hotMax > 0) {
                long currentHotCount = noteThreadHotRepository.countByThreadId(threadId);
                if (currentHotCount >= hotMax) {
                    throw new KbException(KbCode.NOTE_THREAD_HOT_MAX).li();
                }
            }
        }
        return noteThreadHotRepository.updateHotNote(threadId, note, title, cover);
    }

    void threadNoteCancelHot(Note note, String threadId) {
        noteThreadHotRepository.deleteByNoteIdAndThreadId(note.getId(), threadId);
    }

    void threadNoteToTop(Note note, String threadId) {
        NoteThread thread = noteThreadRepository.findById(threadId).orElseThrow(() -> new KbException(
                KbCode.NOTE_THREAD_NOT_EXISTS, "thread not found: " + threadId).li());
        if (!noteThreadHotRepository.existsByNoteIdAndThreadId(note.getId(), threadId)) {
            int topMax = thread.getTopMax() == null ? NoteDefaults.DEFAULT_THREAD_MAX_TOP : thread.getTopMax();
            if (topMax <= 0) {
                throw new KbException(KbCode.NOTE_THREAD_TOP_MAX).r("该板块没有开放置顶功能").li();
            } else {
                long currentTopCount = noteThreadTopRepository.countByThreadId(threadId);
                if (currentTopCount >= topMax) {
                    throw new KbException(KbCode.NOTE_THREAD_TOP_MAX).li();
                }
            }
        }
        noteThreadTopRepository.updateTopNote(threadId, note.getId());
    }

    void threadNoteCancelTop(Note note, String threadId) {
        noteThreadTopRepository.deleteByNoteIdAndThreadId(note.getId(), threadId);
    }

    boolean threadNoteRefreshTop(String threadId, String noteId) {
        return noteThreadTopRepository.updateTopNoteRefreshTime(threadId, noteId) != null;
    }

    // ----------------------------------------------------------------------

    NoteThread updateThread(NoteThread thread) {
        return noteThreadRepository.update(thread);
    }

    NoteThread updateRoute(String threadId, List<String> routeThreads) {
        if (routeThreads == null || routeThreads.size() == 0) {
            return noteThreadRepository.updateRoute(threadId, null);
        } else {
            if (noteThreadRepository.findByIdIn(routeThreads).size() != routeThreads.size()) {
                throw new KbException(KbCode.NOTE_THREAD_NOT_EXISTS,
                        "one of the threads not exist: " + JsonUtils.getGson().toJson(routeThreads)).li();
            }
            return noteThreadRepository.updateRoute(threadId, routeThreads);
        }
    }

    NoteThread updateKey(String threadId, String key) {
        NoteThread thread = noteThreadRepository.findFirstByKey(key).orElse(null);
        if (thread != null) {
            if (!thread.getId().equals(threadId)) {
                // key 已经存在
                throw new KbException(KbCode.ILLEGAL_ARGUMENT, "thread key already exists: " + key).r("key 已存在").li();
            } else if (thread.getKey().equals(key)) {
                // 不需要更新
                return thread;
            }
        }
        return noteThreadRepository.updateKey(threadId, key);
    }

    public NoteThread updateAttr(String threadId, Map<String, String> attr, boolean replace) {
        return noteThreadRepository.updateAttr(threadId, attr, replace);
    }

    public NoteThread updateAttrKeyValue(String threadId, String key, String value) {
        return noteThreadRepository.updateAttrKeyValue(threadId, key, value);
    }

}
