package com.kaiba.m.core.service.da.sensors.channel;

import com.kaiba.lib.base.domain.da.sensors.channel.SensorsArticleChannelModel;
import com.kaiba.lib.base.domain.da.sensors.channel.SensorsArticleStatModel;
import com.kaiba.m.core.domain.da.sensors.channel.SensorsArticleChannel;
import com.kaiba.m.core.repository.da.sensors.channel.SensorsArticleRepository;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @version SensorsArticleService, v0.1 2024/7/8 11:33 daopei Exp $
 **/
@Slf4j
@Service
public class SensorsArticleService {

    private static final String OTHER_SITE = "OTHER_SITE";
    private static final String HTV1 = "HTV1";
    private static final String FM_918 = "FM_918";
    private static final String FM_918_zxb = "zxb";

    private final SensorsArticleRepository articleRepository;


    public SensorsArticleService(SensorsArticleRepository articleRepository) {
        this.articleRepository = articleRepository;
    }


    public SensorsArticleChannel upsert(
            String biz, String ref1, String ref2, String ref3,
            String ref1Title, String ref2Title, String ref3Title,
            Long activeTime, String channelKey, String departKey,
            Integer creatorId, Integer siteId
    ) {
        ChannelModel channelModel = defaultChannel(siteId, biz, channelKey, departKey);
        String refKey = SensorsArticleChannelModel.buildRefKey(biz, ref1, ref2, ref3);
        return articleRepository.upsert(biz, ref1, ref2, ref3,
                ref1Title, ref2Title, ref3Title,
                refKey, activeTime, channelModel.getChannelKey(), channelModel.getDepartKey(), creatorId, siteId);
    }

    public SensorsArticleChannel updateState(String refKey, Integer state) {
        return articleRepository.updateState(refKey, state);
    }

    public Optional<SensorsArticleChannel> getByRefKey(String refKey) {
        return articleRepository.findByRefKey(refKey);
    }

    public Page<SensorsArticleChannel> getByParam(
            String biz,
            String ref1,
            String ref2,
            String ref3,
            Integer state,
            String channelKey,
            String departKey,
            Integer userId,
            Long activeTimeST,
            Long activeTimeET,
            Long createTimeST,
            Long createTimeET,
            Integer page,
            Integer pageSize
    ) {
        return articleRepository.getByParam(
                biz, ref1, ref2, ref3, state,
                channelKey, departKey, userId,
                activeTimeST, activeTimeET,
                createTimeST, createTimeET,
                p(page, pageSize, Sort.by(Sort.Direction.DESC, "createTime")));
    }

    public List<SensorsArticleStatModel> countStatByDepartKey(
            String channelKey, String departKey,
            Long activeTimeStart, Long activeTimeEnd) {
        if (activeTimeStart == null || activeTimeEnd == null) {
            return Collections.emptyList();
        }
        return articleRepository.countByDepartKey(channelKey, departKey, activeTimeStart, activeTimeEnd);
    }



    private Pageable p(Integer page, Integer pageSize, Sort sort) {
        int p = page == null? 0 : page - 1;
        int ps = pageSize == null ? 20 : pageSize;
        return PageRequest.of(p, ps, sort);
    }

    public void restoreChannel() {
        long start = System.currentTimeMillis();
        log.info("sensors article restore channel start. time = {}", start);
        for (int i = 1; ; i++) {
            Page<SensorsArticleChannel> page =
                    getByParam(null, null, null, null, null, null, null, null, null, null, null, null,
                    i,100);
            if (page.isEmpty()) {
                break;
            }
            for (SensorsArticleChannel article : page.toList()) {
                ChannelModel channelModel = defaultChannel(article.getSiteId(), article.getBiz(), article.getChannelKey(), article.getDepartKey());
                articleRepository.upsert(
                        article.getBiz(),
                        article.getRef1(),
                        article.getRef2(),
                        article.getRef3(),
                        article.getRef1Title(),
                        article.getRef2Title(),
                        article.getRef3Title(),
                        article.getRefKey(),
                        article.getActiveTime(),
                        channelModel.getChannelKey(),
                        channelModel.getDepartKey(),
                        article.getCreatorId(),
                        article.getSiteId()
                );
            }
        }
        log.info("sensors article restore channel end. time = {}, spent = {}", System.currentTimeMillis(), System.currentTimeMillis() - start);
    }


    /**
     * 稿件归属频率频道默认兜底策略,如果未选择频率频道则按照下面的规则返回对应频率频道
     *
     * PROGRAM：      (电台9：FM_918, 电台193：FM_1054, 无电台或其他电台：other_site)
     * NEWS：         (有channel则设为channel，没有channel: 电台9：FM_918、zxb，电台非9：other_site)
     * VIDEO_LIVE：   (有channel则设为channel，没有channel: 电台9：FM_918、zxb，电台非9：other_site)
     * HOT_TOPIC：    (有channel则设为channel，没有channel: 电台9：FM_918、zxb，电台非9：other_site)
     * CIRCLE：       (有channel则设为channel，没有channel: 电台9：FM_918, 电台非9：other_site)
     * REVEAL：       (有channel则设为channel，没有channel: 电台9：FM_918，电台非9：other_site)
     * ACTIVITY_HUB： (有channel则设为channel，没有channel: 电台9：FM_918，电台非9：other_site)
     * NEWS_NEO：     (有channel则设为channel，没有channel: 电台9：FM_918，电台非9：other_site)
     * NEWS_VIDEO_CLIP：  (channel全部设为HTV1)
     */
    private static ChannelModel defaultChannel(Integer siteId, String biz, String channelKey, String departKey) {
        if (channelKey != null && !channelKey.isEmpty()) {
            return new ChannelModel(channelKey, departKey);
        }
        switch (biz) {
            case "PROGRAM":
                return new ChannelModel(channelKey, departKey);
            case "NEWS":
            case "VIDEO_LIVE":
            case "HOT_TOPIC":
                if (siteId == null || siteId == 9) {
                    return new ChannelModel(FM_918, FM_918_zxb);
                } else {
                    return new ChannelModel(OTHER_SITE, null);
                }
            case "CIRCLE":
            case "REVEAL":
            case "ACTIVITY_HUB":
            case "NEWS_NEO":
                if (siteId == null || siteId == 9) {
                    return new ChannelModel(FM_918, null);
                } else {
                    return new ChannelModel(OTHER_SITE, null);
                }
            case "NEWS_VIDEO_CLIP":
                return new ChannelModel(HTV1, null);
            default:
                return new ChannelModel(FM_918, null);
        }
    }

    @Data
    static class ChannelModel {
        private String channelKey;
        private String departKey;

        public ChannelModel(String channelKey, String departKey) {
            this.channelKey = channelKey;
            this.departKey = departKey;
        }
    }

}
