package com.kaiba.m.core.service.workorder.search;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version SearchRequest, v0.1 2024/7/30 18:28 daopei Exp $
 **/
@Data
public class SearchRequest {

    /** 检索返回属性字段列表 */
    private List<String> fetchFields;

    /** QUERY子句, 必传属性 */
    private SearchQuery query;
    /** FILTER字句, 非必传 */
    private SearchFilter filter;
    /** SORT字句, 非必传 */
    private SearchSort sort;

    public SearchQuery buildQuery() {
        this.query = new SearchQuery();
        return query;
    }

    public SearchFilter buildFilter() {
        this.filter = new SearchFilter();
        return filter;
    }

    public SearchSort buildSort() {
        if (this.sort != null) {
            return sort;
        }
        this.sort = new SearchSort();
        return sort;
    }

}
