package com.kaiba.m.core.service.hoolo.programme;

import com.google.gson.Gson;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import java.io.IOException;
import java.net.URI;
import java.net.URISyntaxException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.Header;
import org.apache.http.NameValuePair;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.utils.URIBuilder;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.apache.http.entity.mime.MultipartEntityBuilder;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * Description: http请求工具类
 * Author: ZM227
 * Date: 2024/4/23 15:35
 */
public enum HttpClientUtils {
    INSTANCE;

    private static final Logger log = LoggerFactory.getLogger(HttpClientUtils.class);

    private static final RequestConfig requestConfig;

    private static final RequestConfig uploadConfig;

    static {
        requestConfig = RequestConfig.custom()
            // 客户端和服务器建立连接的timeout
            .setConnectTimeout(1000 * 60)
            // 指从连接池获取连接的timeout
            .setConnectionRequestTimeout(6000)
            // 客户端从服务器读取数据的timeout
            .setSocketTimeout(1000 * 60 * 3).build();
        uploadConfig = RequestConfig.custom()
            // 客户端和服务器建立连接的timeout
            .setConnectTimeout(1000 * 60 * 20)
            // 指从连接池获取连接的timeout
            .setConnectionRequestTimeout(6000)
            // 客户端从服务器读取数据的timeout
            .setSocketTimeout(1000 * 60 * 20).build();
    }

    /**
     * 发送get请求，接收json响应数据
     * GET
     *
     * @param url   访问地址，无query参数
     * @param param query参数
     */
    public JsonElement doGet(String url, Map<String, String> param, Header header)
        throws HttpClientException {
        // 创建Httpclient对象
        CloseableHttpClient httpClient = HttpClients.createDefault();
        String resultString;
        CloseableHttpResponse response = null;
        try {
            // 创建uri
            URIBuilder builder = new URIBuilder(url);
            if (param != null) {
                for (String key : param.keySet()) {
                    builder.addParameter(key, param.get(key));
                }
            }
            URI uri = builder.build();
            log.debug("-->>Http GET请求地址：" + url);
            if (null != param) {
                log.debug("-->>Http 请求参数：" + param);
            }
            // 创建http GET请求
            HttpGet httpGet = new HttpGet(uri);
            if (Objects.nonNull(header)) {
                httpGet.setHeader(header);
            }
            httpGet.setConfig(requestConfig);
            // 执行请求
            response = httpClient.execute(httpGet);
            // 判断返回状态是否为200
            if (response.getStatusLine().getStatusCode() == 200) {
                resultString = EntityUtils.toString(response.getEntity(), "UTF-8");
                log.debug("<<--Http 响应内容：" + resultString);
            } else {
                log.error("<<--Http 响应状态码：" + response.getStatusLine().getStatusCode());
                throw new HttpClientException(
                    HttpClientException.EXTERNAL_ERROR,
                    response.getStatusLine().getStatusCode());
            }
        } catch (IOException | URISyntaxException e) {
            log.error("Http 发送请求异常 url:{}", url, e);
            throw new HttpClientException(
                HttpClientException.REQUEST_ERROR,
                HttpClientException.REQUEST_ERROR);
        } finally {
            try {
                if (response != null) {
                    response.close();
                }
                httpClient.close();
            } catch (IOException e) {
                log.error("Http 关闭流异常", e);
            }
        }
        Gson gson = new Gson();
        return gson.fromJson(resultString, JsonElement.class);
    }

    /**
     * 发送post请求，上传byte
     * POST binary
     *
     * @param url 请求地址，不拼接
     */
    public JsonObject doPostBinaryBody(String url, byte[] bytes, String fileName)
        throws HttpClientException {
        // 创建Httpclient对象
        CloseableHttpClient httpClient = HttpClients.createDefault();
        CloseableHttpResponse response = null;
        String resultString;
        try {
            // 创建Http Post请求
            log.debug("-->>Http POST请求地址：" + url);

            HttpPost httpPost = new HttpPost(url);
            httpPost.setConfig(uploadConfig);
            // 创建参数列表

            MultipartEntityBuilder multipartEntityBuilder = MultipartEntityBuilder.create();

            multipartEntityBuilder.addBinaryBody("fileName", bytes, ContentType.MULTIPART_FORM_DATA,
                fileName);

            httpPost.setEntity(multipartEntityBuilder.build());
            // 执行http请求
            response = httpClient.execute(httpPost);
            // 判断返回状态是否为200
            if (response.getStatusLine().getStatusCode() == 200) {
                resultString = EntityUtils.toString(response.getEntity(), "UTF-8");
                log.debug("<<--Http 响应内容：" + resultString);
            } else {
                log.error("<<--Http 响应状态码：" + response.getStatusLine().getStatusCode());
                throw new HttpClientException(
                    HttpClientException.EXTERNAL_ERROR,
                    response.getStatusLine().getStatusCode());
            }
        } catch (Exception e) {
            log.error("Http 发送请求异常 url:{}", url, e);
            throw new HttpClientException(
                HttpClientException.REQUEST_ERROR,
                HttpClientException.REQUEST_ERROR);
        } finally {
            try {
                if (response != null) {
                    response.close();
                }
                httpClient.close();
            } catch (IOException e) {
                log.error("Http 关闭流异常", e);
            }
        }
        Gson gson = new Gson();
        return gson.fromJson(resultString, JsonObject.class);
    }

    /**
     * 发送post请求，form-data数据传输
     * POST multipart/form-data
     *
     * @param url 请求地址
     */
    public JsonObject doPostFormData(String url, Map<String, Object> formData, Header header)
        throws HttpClientException {
        // 创建Httpclient对象
        CloseableHttpClient httpClient = HttpClients.createDefault();
        CloseableHttpResponse response = null;
        String resultString;
        Gson gson = new Gson();
        try {
            // 创建Http Post请求
            log.debug("-->>Http POST请求地址：" + url);

            HttpPost httpPost = new HttpPost(url);
            httpPost.setConfig(requestConfig);
            // 创建参数列表
            MultipartEntityBuilder multipartEntityBuilder = MultipartEntityBuilder.create();
            if (formData != null) {
                log.debug("-->>Http POST form-data内容：" + gson.toJson(formData));
                for (String key : formData.keySet()) {
                    multipartEntityBuilder.addTextBody(key, gson.toJson(formData.get(key)),
                        ContentType.MULTIPART_FORM_DATA);
                }
            }

            httpPost.setEntity(multipartEntityBuilder.build());
            if (Objects.nonNull(header)) {
                httpPost.setHeader(header);
            }
            // 执行http请求
            response = httpClient.execute(httpPost);
            // 判断返回状态是否为200
            if (response.getStatusLine().getStatusCode() == 200) {
                resultString = EntityUtils.toString(response.getEntity(), "UTF-8");
                log.debug("<<--Http 响应内容：" + resultString);
            } else {
                log.error("<<--Http 响应状态码：" + response.getStatusLine().getStatusCode());
                throw new HttpClientException(
                    HttpClientException.EXTERNAL_ERROR,
                    response.getStatusLine().getStatusCode());
            }
        } catch (Exception e) {
            log.error("Http 发送请求异常", e);
            throw new HttpClientException(
                HttpClientException.REQUEST_ERROR,
                HttpClientException.REQUEST_ERROR);
        } finally {
            try {
                if (response != null) {
                    response.close();
                }
                httpClient.close();
            } catch (IOException e) {
                log.error("Http 关闭流异常", e);
            }
        }
        return gson.fromJson(resultString, JsonObject.class);
    }

    /**
     * 发送post请求，接收json响应数据
     * POST application/x-www-form-urlencoded
     *
     * @param url   请求地址，不拼接
     * @param param 表单query参数
     */
    public JsonObject doPost(String url, Map<String, String> param) throws HttpClientException {
        // 创建Httpclient对象
        CloseableHttpClient httpClient = HttpClients.createDefault();
        CloseableHttpResponse response = null;
        String resultString = "";
        try {
            // 创建Http Post请求
            log.debug("-->>Http POST请求地址：" + url);
            if (null != param) {
                log.debug("-->>Http 请求参数：" + param);
            }

            HttpPost httpPost = new HttpPost(url);
            httpPost.setConfig(requestConfig);
            // 创建参数列表
            if (param != null) {
                List<NameValuePair> paramList = new ArrayList<>();
                for (String key : param.keySet()) {
                    paramList.add(new BasicNameValuePair(key, param.get(key)));
                }
                // 模拟表单
                UrlEncodedFormEntity entity = new UrlEncodedFormEntity(paramList);
                httpPost.setEntity(entity);
            }
            // 执行http请求
            response = httpClient.execute(httpPost);
            // 判断返回状态是否为200
            if (response.getStatusLine().getStatusCode() == 200) {
                resultString = EntityUtils.toString(response.getEntity(), "UTF-8");
                log.debug("<<--Http 响应内容：" + resultString);
            } else {
                log.error("<<--Http 响应状态码：" + response.getStatusLine().getStatusCode());
                throw new HttpClientException(
                    HttpClientException.EXTERNAL_ERROR,
                    response.getStatusLine().getStatusCode());
            }
        } catch (Exception e) {
            log.error("Http 发送请求异常 url:{}", url, e);
            throw new HttpClientException(
                HttpClientException.REQUEST_ERROR,
                HttpClientException.REQUEST_ERROR);
        } finally {
            try {
                if (response != null) {
                    response.close();
                }
                httpClient.close();
            } catch (IOException e) {
                log.error("Http 关闭流异常", e);
            }
        }
        Gson gson = new Gson();
        return gson.fromJson(resultString, JsonObject.class);
    }

    /**
     * 发送post请求，接收json响应数据
     * POST application/json
     *
     * @param url  请求地址
     * @param json json入参
     */
    public JsonObject doPostJson(String url, String json, Header header)
        throws HttpClientException {
        if (StringUtils.isBlank(json)) {
            log.error("-->>Http POST发送json数据，json不能为空，url:" + url);
            return null;
        }
        // 创建Httpclient对象
        CloseableHttpClient httpClient = HttpClients.createDefault();
        CloseableHttpResponse response = null;
        String resultString = "";
        try {
            log.debug("-->>Http POST请求地址：" + url);
            log.debug("-->>Http 请求参数：" + json);
            // 创建Http Post请求
            HttpPost httpPost = new HttpPost(url);
            if (Objects.nonNull(header)) {
                httpPost.setHeader(header);
            }
            httpPost.setConfig(requestConfig);
            // 创建请求内容
            StringEntity entity = new StringEntity(json, ContentType.APPLICATION_JSON);
            httpPost.setEntity(entity);
            // 执行http请求
            response = httpClient.execute(httpPost);
            // 判断返回状态是否为200
            if (response.getStatusLine().getStatusCode() == 200) {
                resultString = EntityUtils.toString(response.getEntity(), "UTF-8");
                log.debug("<<--Http 响应内容：" + resultString);
            } else {
                log.error("<<--Http 响应状态码：" + response.getStatusLine().getStatusCode());
                throw new HttpClientException(
                    HttpClientException.EXTERNAL_ERROR,
                    response.getStatusLine().getStatusCode());
            }
        } catch (Exception e) {
            log.error("Http 发送请求异常 url:{}", url, e);
            throw new HttpClientException(
                HttpClientException.REQUEST_ERROR,
                HttpClientException.REQUEST_ERROR);
        } finally {
            try {
                if (response != null) {
                    response.close();
                }
                httpClient.close();
            } catch (IOException e) {
                log.error("Http 关闭流异常", e);
            }
        }
        Gson gson = new Gson();
        return gson.fromJson(resultString, JsonObject.class);
    }
}