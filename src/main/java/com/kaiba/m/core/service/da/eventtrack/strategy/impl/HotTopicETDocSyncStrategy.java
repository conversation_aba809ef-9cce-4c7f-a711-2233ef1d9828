package com.kaiba.m.core.service.da.eventtrack.strategy.impl;

import com.kaiba.lib.base.constant.KbModule;
import com.kaiba.lib.base.constant.news.NewsChannel;
import com.kaiba.lib.base.domain.common.KbTimeRange;
import com.kaiba.lib.base.domain.da.sensors.channel.SensorsArticleChannelModel;
import com.kaiba.lib.base.domain.hottopic.HotTopicModel;
import com.kaiba.lib.base.lang.collections.KbColUtils;
import com.kaiba.lib.base.response.KbEntity;
import com.kaiba.lib.base.service.IHotTopicService;
import com.kaiba.m.core.constant.da.eventtrack.ETDocSyncStrategyType;
import com.kaiba.m.core.domain.da.eventtrack.ETDoc;
import com.kaiba.m.core.domain.da.sensors.channel.SensorsArticleChannel;
import com.kaiba.m.core.repository.da.eventtrack.ETDocRepository;
import com.kaiba.m.core.repository.da.eventtrack.ETDocSyncCursorRepository;
import com.kaiba.m.core.service.da.eventtrack.ETDocModelHelper;
import com.kaiba.m.core.service.da.eventtrack.strategy.helper.ActMixItemResourceService;
import com.kaiba.m.core.service.da.eventtrack.strategy.model.ETDocSyncQueryModel;
import com.kaiba.m.core.service.da.sensors.channel.SensorsArticleService;
import org.springframework.stereotype.Service;

import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * todo activity实现
 * <AUTHOR>
 * @version HotTopicETDocSyncStrategy, v0.1 2025/4/8 16:26 daopei Exp $
 **/
@Service
public class HotTopicETDocSyncStrategy extends AbstractETDocSyncStrategy{

    private final IHotTopicService hotTopicService;
    private final ActMixItemResourceService actMixItemResourceService;
    private final SensorsArticleService sensorsArticleService;


    public HotTopicETDocSyncStrategy(
            ETDocRepository etDocRepository,
            ETDocSyncCursorRepository etDocSyncCursorRepository,
            IHotTopicService hotTopicService,
            ActMixItemResourceService actMixItemResourceService,
            SensorsArticleService sensorsArticleService
    )
    {
        super(etDocRepository, etDocSyncCursorRepository);
        this.hotTopicService = hotTopicService;
        this.actMixItemResourceService = actMixItemResourceService;
        this.sensorsArticleService = sensorsArticleService;
    }


    @Override
    public void syncAll() {
        initDocByPageExecutor(this::execute, null);
    }

    @Override
    public void syncByTimeRange(KbTimeRange time) {
        initDocByPageExecutor(this::execute, time);
    }

    @Override
    public String getStrategy() {
        return ETDocSyncStrategyType.HOT_TOPIC.name();
    }


    private boolean execute(ETDocSyncQueryModel query) {
        KbTimeRange timeRange = defaultOrFillTime(query.getTimeRange());
        KbEntity<List<HotTopicModel>> result = hotTopicService.getListBySiteId(9, null, query.getPage(), query.getPageSize());
        List<HotTopicModel> list = result.dataOrThrow();
        List<String> actMixTopicIds = actMixItemResourceService.getHotTopicActivity();
        boolean hasNext = result.getTotalPage() > query.getPage();
        for (HotTopicModel topic : list) {
            if (!checkItemPageRoll(timeRange, topic)) {
                hasNext = false;
                break;
            }

            ETDoc doc = ETDocModelHelper.createByAll(
                    KbModule.HOT_TOPIC.name(), null,
                    topic.getId(), null, null,
                    topic.getSiteId(), topic.getTitle(),
                    topic.getChannelKey(), null, topicModuleByActMix(topic.getId(), actMixTopicIds, null),
                    topic.getCreateTime() * 1000
            );
            fillChannelAndDepart(doc);
            saveETDoc(doc);
        }
        return hasNext;
    }


    /**
     * 是否需要打上活动标签
     * @param hotTopicId
     * @param actMixTopicId
     * @param modules
     * @return
     */
    private Set<String> topicModuleByActMix(String hotTopicId, List<String> actMixTopicId, HashSet<String> modules) {
        if (actMixTopicId == null || actMixTopicId.isEmpty()) {
            return modules;
        }
        if (actMixTopicId.contains(hotTopicId)) {
            return KbColUtils.SetOpt.upsertHashSet(modules, "da_activity");
        }
        return modules;
    }

    /**
     * 填充频道和部门
     * @param doc
     */
    private void fillChannelAndDepart(ETDoc doc) {
        if (doc == null || doc.getBiz() == null || doc.getRef1() == null) {
            return;
        }
        String refKey = SensorsArticleChannelModel.buildRefKey(doc.getBiz(), doc.getRef1(), null, null);
        SensorsArticleChannel articleChannel = sensorsArticleService.getByRefKey(refKey).orElse(null);
        if (articleChannel == null || articleChannel.getChannelKey() == null) {
            return;
        }
        String channel = articleChannel.getChannelKey();
        if ("OTHER_SITE".equals(channel)) {
            channel = NewsChannel.OTHER.name();
        }
        doc.setChannel(channel);
        doc.setDepart(articleChannel.getDepartKey());
    }

    /**
     * 确认数据是否需要继续分页滚动
     * 1.置顶数据默认继续
     * 2.数据必须大于指定时间下限才继续滚动
     * @param range
     * @param topic
     * @return
     */
    private boolean checkItemPageRoll(KbTimeRange range, HotTopicModel topic) {
        if (topic.getTop() == 1) {
            return true;
        }
        //历史数据 21年之前都是没有创建时间。不处理
        if (topic.getCreateTime() == null) {
            return false;
        }
        return range.isLowerOpen() ?
                topic.getCreateTime() > range.obtainLowerInSecond()
                : topic.getCreateTime() >= range.obtainLowerInSecond();
    }


}
