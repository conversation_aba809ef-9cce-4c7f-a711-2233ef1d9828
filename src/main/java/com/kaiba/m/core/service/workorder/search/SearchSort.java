package com.kaiba.m.core.service.workorder.search;

import com.aliyun.opensearch.sdk.generated.search.Order;
import com.aliyun.opensearch.sdk.generated.search.Sort;
import com.aliyun.opensearch.sdk.generated.search.SortField;
import com.kaiba.lib.base.response.KbCode;
import com.kaiba.lib.base.response.KbException;
import lombok.Getter;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version SearchSort, v0.1 2024/8/9 10:25 daopei Exp $
 **/
public class SearchSort {

    /**
     * 多维排序定义. 先按照第一维分数排序，如果第一维分数一样，再按照第二维分数进行档内排序，以此类推
     */
    private final List<SortField> sortFields = new ArrayList<>();

    @Getter
    private String kvPairs;

    public SearchSort fieldDefault(String fieldName, boolean asc) {
        if (asc) {
            return fieldAsc(fieldName);
        } else {
            return fieldDesc(fieldName);
        }
    }


    public SearchSort fieldDesc(String fieldName) {
        sortFields.add(new SortField(fieldName, Order.DECREASE));
        return this;
    }

    public SearchSort fieldAsc(String fieldName) {
        sortFields.add(new SortField(fieldName, Order.INCREASE));
        return this;
    }

    /**
     * 标签排序. 默认权重值, 匹配上的标签默认10分
     * @param fieldName
     * @param tags
     * @return tag_match(query_key, doc_field, kv_op, merge_op, has_default, doc_kv)
     */
    public SearchSort tagMatch(String fieldName, List<String> tags) {
        if (tags == null || tags.isEmpty()) {
            return this;
        }
        if (this.kvPairs != null) {
            throw new KbException(KbCode.ILLEGAL_ARGUMENT).r("kvpairs already used");
        }
        this.kvPairs = "query_tags:" + String.join(":", tags);
        String tagMatch = "tag_match(\"query_tags\", %s, 10,\"sum\",\"false\",\"false\")";
        sortFields.add(new SortField(String.format(tagMatch, fieldName), Order.DECREASE));
        return this;
    }

    /**
     * 标签排序. 自定义排序权重
     *
     * kvPairs ==> key1=value1:key2=value2
     * @param fieldName
     * @param kvPairs key=标签,value=权重值
     * @return tag_match(query_key, doc_field, kv_op, merge_op, has_default, doc_kv)
     */
    public SearchSort tagMatchWithKvPairs(String fieldName, LinkedHashMap<String, Integer> kvPairs) {
        if (kvPairs == null || kvPairs.isEmpty()) {
            return this;
        }
        if (this.kvPairs != null) {
            throw new KbException(KbCode.ILLEGAL_ARGUMENT).r("kvpairs already used");
        }
        //key1=value1:key2=value2
        List<String> tags = kvPairs.entrySet().stream().map(entry -> entry.getKey() + "=" + entry.getValue()).collect(Collectors.toList());
        this.kvPairs = "query_tags:" + String.join(":", tags);
        String tagMatch = "tag_match(\"query_tags\", %s, \"sum\",\"sum\",\"false\",\"false\")";
        sortFields.add(new SortField(String.format(tagMatch, fieldName), Order.DECREASE));
        return this;
    }


    public Sort create() {
        if (sortFields.isEmpty()) {
            return null;
        }
        return new Sort(sortFields);
    }



}
