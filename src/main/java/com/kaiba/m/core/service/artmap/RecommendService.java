package com.kaiba.m.core.service.artmap;

import com.kaiba.lib.base.constant.KbProperties;
import com.kaiba.lib.base.domain.news.pool.bygroup.GroupModel;
import com.kaiba.lib.base.response.KbCode;
import com.kaiba.lib.base.response.KbEntity;
import com.kaiba.lib.base.response.KbException;
import com.kaiba.lib.base.service.INeoNewsByGroupService;
import com.kaiba.m.core.domain.artmap.Recommend;
import com.kaiba.m.core.repository.artmap.RecommendRepository;
import com.kaiba.m.core.service.education.recitation.GeneCodeUtils;
import java.util.Objects;
import javax.annotation.Resource;
import org.springframework.stereotype.Service;

/**
 * Description: 艺术地图每日推荐Service
 * Author: ZM227
 * Date: 2025/6/6 16:37
 */
@Service
public class RecommendService {

    @Resource
    private RecommendRepository recommendRepository;
    @Resource
    private INeoNewsByGroupService groupService;

    private static final String RECOMMEND_GROUP_KEY_PREFIX = "articleMap_recommend_group_";
    private static final String RECOMMEND_GROUP_NAME_PREFIX = "艺术地图每日推荐分组-";

    public Recommend addRecommend(Recommend recommend) {
        // 先新增分组
        recommend.setRecommendCode(GeneCodeUtils.INSTANCE.geneCode());
        String groupKey = RECOMMEND_GROUP_KEY_PREFIX + recommend.getRecommendCode();
        KbEntity<GroupModel> groupModelKbEntity = groupService.createGroup(groupKey,
            KbProperties.HANGZHOU_SITE_ID, null, RECOMMEND_GROUP_NAME_PREFIX + recommend.getTitle(),
            null, null, null, null);
        if (!groupModelKbEntity.isOk() || Objects.isNull(groupModelKbEntity.getData())) {
            throw new KbException(KbCode.EXCEPTION).r("新增分组异常");
        }
        recommend.setGroupKey(groupModelKbEntity.getData().getKey());
        recommend.setGroupName(RECOMMEND_GROUP_NAME_PREFIX + recommend.getTitle());
        // 默认下架
        recommend.setStatus(0);
        long current = System.currentTimeMillis();
        recommend.setUpdateTime(current);
        recommend.setCreateTime(current);
        return recommendRepository.insert(recommend);
    }

    public Recommend updateRecommend(Recommend recommend) {
        return recommendRepository.updateByRecommendCode(recommend);
    }

    /**
     * 修改推荐状态
     *
     * @param recommendCode 推荐编码
     * @param status        推荐状态
     * @return 修改后推荐信息
     */
    public Recommend modifyRecommendStatus(String recommendCode, Integer status) {
        Recommend recommend = new Recommend();
        recommend.setRecommendCode(recommendCode);
        recommend.setStatus(status);
        return recommendRepository.updateByRecommendCode(recommend);
    }

}
