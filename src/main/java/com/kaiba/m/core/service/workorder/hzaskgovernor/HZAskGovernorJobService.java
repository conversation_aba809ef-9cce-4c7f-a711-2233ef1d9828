package com.kaiba.m.core.service.workorder.hzaskgovernor;

import com.kaiba.lib.base.constant.workorder.WOBusiness;
import com.kaiba.lib.base.constant.workorder.WOCaseState;
import com.kaiba.lib.base.constant.workorder.WOOperation;
import com.kaiba.lib.base.constant.workorder.WOTeamState;
import com.kaiba.lib.base.util.StringUtils;
import com.kaiba.m.core.domain.workorder.WOTeam;
import com.kaiba.m.core.domain.workorder.aggregation.WOCaseRatingAggr;
import com.kaiba.m.core.domain.workorder.aggregation.WOCountAggr;
import com.kaiba.m.core.domain.workorder.hzaskgovernor.AGResolverStat;
import com.kaiba.m.core.repository.workorder.WOCaseRepository;
import com.kaiba.m.core.repository.workorder.WOEventRepository;
import com.kaiba.m.core.repository.workorder.WOTeamRepository;
import com.kaiba.m.core.repository.workorder.hzaskgovernor.AGResolverStatRepository;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.mongodb.core.aggregation.AggregationResults;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * author: lyux
 * date: 2024-03-25
 */
@Slf4j
@Service
public class HZAskGovernorJobService {

    private final AGResolverStatRepository statRepository;
    private final WOCaseRepository caseRepository;
    private final WOTeamRepository teamRepository;
    private final WOEventRepository eventRepository;

    public HZAskGovernorJobService(
            WOCaseRepository caseRepository,
            WOTeamRepository teamRepository,
            WOEventRepository eventRepository,
            AGResolverStatRepository statRepository
    ) {
        this.caseRepository = caseRepository;
        this.teamRepository = teamRepository;
        this.eventRepository = eventRepository;
        this.statRepository = statRepository;
    }

    @XxlJob("work-order-hz-ask-governor-team-stat")
    public ReturnT<String> statAllTeam(String param) {
        long closeTimeGT = System.currentTimeMillis() - TimeUnit.DAYS.toMillis(365);
        long st = System.currentTimeMillis();
        int teamCount = 0;
        log.info("stat resolver start. closeTimeGT: " + closeTimeGT);
        for (int i = 0; ; i++) {
            Page<WOTeam> teamPage = teamRepository.findByStateAndBizOrderByIdDesc(
                    WOTeamState.ENABLED.name(), WOBusiness.HZ_ASK_GOVERNOR.name(), PageRequest.of(i, 100));
            List<WOTeam> teamList = teamPage.getContent();
            for (WOTeam team : teamList) {
                long statTeamST = System.currentTimeMillis();
                AGResolverStat stat = generateTeamStat(team, closeTimeGT);
                log.info("stat team done: " + stat + ", " +
                        "time elapsed: " + (System.currentTimeMillis() - statTeamST));
                statRepository.upsert(stat);
                teamCount ++;
            }
            if (teamPage.isLast()) {
                break;
            }
        }
        log.info("stat resolver end, " +
                "team count: " + teamCount + ", " +
                "time elapsed: " + (System.currentTimeMillis() - st));
        return ReturnT.SUCCESS;
    }

    public AGResolverStat generateTeamStat(WOTeam team, long st) {
        AGResolverStat stat = new AGResolverStat();

        //案件量统计
        long caseResolvedCount = caseRepository.countByCloseTimeGreaterThanAndBizAndStateAndResolver(
                st, WOBusiness.HZ_ASK_GOVERNOR.name(), WOCaseState.CLOSED.name(), team.getTeam());
        long caseCount = caseRepository.countByCloseTimeGreaterThanAndBizAndStateAndResolverListContaining(
                st, WOBusiness.HZ_ASK_GOVERNOR.name(), WOCaseState.CLOSED.name(), team.getTeam());

        //事件量统计
        AggregationResults<WOCountAggr> eventAggrResult =
                eventRepository.aggrEventCountGroupByOperation(WOBusiness.HZ_ASK_GOVERNOR.name(), team.getTeam(), st);
        long eventCount = eventAggrResult.getMappedResults().stream().mapToLong(WOCountAggr::getCount).sum();
        Map<String, Long> eventCountMap = eventAggrResult.getMappedResults().stream().collect(
                Collectors.toMap(WOCountAggr::getId, WOCountAggr::getCount, (o1, o2) -> o1));

        //评分统计
        AggregationResults<WOCaseRatingAggr> ratingAggrResult = caseRepository.aggrStatRatingByCloseTimeGT(
                st, WOBusiness.HZ_ASK_GOVERNOR.name(), WOCaseState.CLOSED.name(), Collections.singletonList(team.getTeam()));
        ratingAggrResult.getMappedResults()
//                .stream()
//                .filter(r -> StringUtils.isEqual(r.get_id(), team.getTeam())).findFirst()
                .forEach(result -> {
                    stat.setRatingCount(result.getRatingCount());
                    stat.setRatingSum(result.getRatingSum());
                    stat.setRatingAverage(result.getRatingAverage());
                });

        //数据落库
        stat.setCaseCount((int) caseCount);
        stat.setCaseResolvedCount((int) caseResolvedCount);
        stat.setEventCount((int) eventCount);
        stat.setEventReplyCount((int) (long) eventCountMap.getOrDefault(WOOperation.REPLY.name(), 0L));
        stat.setEventBackCount((int) (long) eventCountMap.getOrDefault(WOOperation.BACK.name(), 0L));
        stat.setTeamId(team.getId());
        stat.setTeamAbbr(team.getAbbr());
        stat.setIsInRank(team.getAttr() == null || !team.getAttr().containsKey(HZAskGovernorConsts.TEAM_ATTR_IGNORE_RANK));
        return stat;
    }

}
