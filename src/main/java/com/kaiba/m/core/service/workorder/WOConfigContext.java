package com.kaiba.m.core.service.workorder;

import com.kaiba.lib.base.assembler.Mapper;
import com.kaiba.lib.base.constant.workorder.WOBusiness;
import com.kaiba.lib.base.constant.workorder.WOConfigState;
import com.kaiba.lib.base.constant.workorder.WODispatchWay;
import com.kaiba.lib.base.domain.workorder.WOConfigModel;
import com.kaiba.lib.base.domain.workorder.WOConfigRole;
import com.kaiba.lib.base.response.KbCode;
import com.kaiba.lib.base.response.KbException;
import com.kaiba.m.core.domain.workorder.WOConfig;
import com.kaiba.m.core.domain.workorder.WOTeam;
import lombok.Getter;

import java.util.HashMap;
import java.util.Map;

/**
 * author: lyux
 * date: 2024-01-10
 */
@Getter
public class WOConfigContext {

    private final WOConfig config;

    private final WOBusiness biz;

    private final WOConfigState state;

    private final WODispatchWay dispatchWay;

    private final Map<String, WOConfigRole.Opt> permissions;

    // ---------------------------------------------

    WOConfigContext(WOConfig config) {
        this.config = config;
        this.biz = WOBusiness.resolveByName(config.getBiz())
                .orElseThrow(() -> new KbException(KbCode.ILLEGAL_ARGUMENT).li());
        this.state = WOConfigState.resolveByName(config.getState())
                .orElseThrow(() -> new KbException(KbCode.ILLEGAL_ARGUMENT).li());
        this.dispatchWay = WODispatchWay.resolveByName(config.getDispatchWay())
                .orElseThrow(() -> new KbException(KbCode.ILLEGAL_ARGUMENT).li());
        this.permissions = new HashMap<>();
        for (WOConfigRole role : config.getRoles()) {
            for (WOConfigRole.Opt opt : role.getOperations()) {
                String key = generatePermissionKey(opt.getPhase(), role.getRole(), opt.getOperation());
                permissions.put(key, opt);
            }
        }
    }

    public String getConfigId() {
        return config.getId();
    }

    public boolean hasPermission(String phase, String operation, WOTeam team) {
        if (team.getRoles() == null || team.getRoles().size() == 0) {
            return false;
        }
        for (String teamRole : team.getRoles()) {
            String key = generatePermissionKey(phase, teamRole, operation);
            if (permissions.containsKey(key)) {
                return true;
            }
        }
        return false;
    }

    public WOConfigContext hasPermissionOrThrow(String phase, String operation, WOTeam team) {
        if (!hasPermission(phase, operation, team)) {
            throw new KbException(KbCode.ILLEGAL_STATE,
                    "permission denied: " + team.getId() + "|" + team.getName() + " -> " + operation)
                    .r("没有操作权限").li();
        }
        return this;
    }

    public WOConfigModel toModel() {
        return Mapper.map(this, WOConfigModel.class);
    }

    private static String generatePermissionKey(String phase, String role, String operation) {
        return phase + "-" + role + "-" + operation;
    }

}
