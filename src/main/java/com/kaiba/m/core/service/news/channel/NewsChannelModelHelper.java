package com.kaiba.m.core.service.news.channel;

import com.kaiba.lib.base.assembler.Mapper;
import com.kaiba.lib.base.constant.news.NewsChannelType;
import com.kaiba.lib.base.domain.common.ShareModel;
import com.kaiba.lib.base.domain.news.channel.CProgrammeModel;
import com.kaiba.lib.base.domain.news.channel.ChannelConfigModel;
import com.kaiba.lib.base.domain.news.channel.ChannelMainPageModel;
import com.kaiba.lib.base.util.StringUtils;
import com.kaiba.m.core.domain.news.channel.CProgramme;
import com.kaiba.m.core.domain.news.channel.ChannelConfig;
import com.kaiba.m.core.domain.news.channel.ChannelMainPage;
import lombok.extern.slf4j.Slf4j;

/**
 * author: lyux
 * date: 2024-09-10
 */
@Slf4j
public class NewsChannelModelHelper {

    public static CProgrammeModel programme2model(CProgramme instance) {
        return Mapper.map(instance, CProgrammeModel.class);
    }

    public static ChannelConfigModel channel2model(ChannelConfig channel) {
        return Mapper.map(channel, ChannelConfigModel.class);
    }

    public static ChannelMainPageModel mainPage2model(ChannelMainPage mainPage, ChannelConfig channel) {
        ChannelMainPageModel model = Mapper.map(mainPage, ChannelMainPageModel.class);
        if (channel != null) {
            model.setChannelName(channel.getName());
            model.setChannelAbbr(channel.getAbbr());
            model.setChannelLogo(channel.getLogo());
            model.setChannelType(channel.getType());
        }
        return model;
    }

    public static ShareModel assembleChannelShare(String pageHost, ChannelMainPageModel mainPage) {
        if (mainPage.getShare() != null
                && mainPage.getShare().getEnabled() != null
                && !mainPage.getShare().getEnabled()) {
            return null;
        }
        ShareModel share = mainPage.getShare();
        if (share == null) {
            share = new ShareModel();
        }
        if (StringUtils.isEmpty(share.getTitle())) {
            if (StringUtils.isEmpty(mainPage.getChannelName())) {
                share.setTitle("精彩频道");
            } else {
                share.setTitle(mainPage.getChannelName());
            }
        }
        if (StringUtils.isEmpty(share.getImageUrl())) {
            if (mainPage.getChannelLogo() == null) {
                share.setImageUrl("https://static.kaiba315.com.cn/kaiba-logo.png");
            } else {
                share.setImageUrl(mainPage.getChannelLogo().getImageUrl());
            }
        }
        if (NewsChannelType.HCRT_CHANNEL.name().equals(mainPage.getChannelType())) {
            share.setUrl("https://" + pageHost + "/neo-news/hcrt-channel-main-page?channelKey=" + mainPage.getChannelKey());
        } else {
            share.setUrl("https://" + pageHost + "/neo-news/county-channel-main-page?channelKey=" + mainPage.getChannelKey());
        }
        return share;
    }

    public static ShareModel assembleProgrammeShare(String pageHost, CProgrammeModel programme) {
        if (programme.getShare() != null
                && programme.getShare().getEnabled() != null
                && !programme.getShare().getEnabled()) {
            return null;
        }
        ShareModel share = programme.getShare();
        if (share == null) {
            share = new ShareModel();
        }
        if (StringUtils.isEmpty(share.getTitle())) {
            if (StringUtils.isEmpty(programme.getName())) {
                share.setTitle("精彩节目");
            } else {
                share.setTitle(programme.getName());
            }
        }
        if (StringUtils.isEmpty(share.getImageUrl())) {
            if (programme.getCover() == null) {
                share.setImageUrl("https://static.kaiba315.com.cn/kaiba-logo.png");
            } else {
                share.setImageUrl(programme.getCover().getImageUrl());
            }
        }
        share.setUrl("https://" + pageHost + "/neo-news/programme-main-page?programmeId=" + programme.getId());
        return share;
    }

}
