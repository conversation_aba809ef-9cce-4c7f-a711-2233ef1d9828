package com.kaiba.m.core.service.videolive;

import com.kaiba.lib.base.constant.videolive.VideoLiveState;
import com.kaiba.m.core.domain.videolive.VideoLive;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import java.util.Calendar;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/6/15
 */
@Slf4j
@Component
public class VideoliveStateCronJob {

    private static final String VIDEOLIVE_ACTIVE_SITE_PREFIX = "java_active_video_live_site_id_";

    private final VideoliveService videoliveService;
    private final StringRedisTemplate stringRedisTemplate;
    private static final Integer[] SIGN_ARRAY = new Integer[]{VideoLiveState.SIGNED.getValue()};

    public VideoliveStateCronJob(VideoliveService videoliveService, StringRedisTemplate stringRedisTemplate) {
        this.videoliveService = videoliveService;
        this.stringRedisTemplate = stringRedisTemplate;
    }

    // 每隔 1 分钟执行一次
    @XxlJob("video-live-state-transition")
    public ReturnT<String> stateTransition(String param) {
        long currentTime = Calendar.getInstance().getTimeInMillis() / 1000;
        for (int i = 1; ; i++) {
            Page<VideoLive> page = videoliveService.getPageByStatesAndLessThanEqualTime(SIGN_ARRAY, currentTime, i, 100);
            List<VideoLive> list = page.getContent();
            for (VideoLive videoLive : list) {
                videoliveService.changeLiveState(videoLive, VideoLiveState.ONLINE);
                expireActiveVideoliveList(videoLive.getSiteId());
            }
            if (page.isLast()) {
                log.info("rong chatroom auto online (warm) done");
                break;
            }
        }
        return ReturnT.SUCCESS;
    }

    private void expireActiveVideoliveList(Integer siteId) {
        String siteActiveKey = VIDEOLIVE_ACTIVE_SITE_PREFIX + siteId;
        stringRedisTemplate.delete(siteActiveKey);
    }

}
