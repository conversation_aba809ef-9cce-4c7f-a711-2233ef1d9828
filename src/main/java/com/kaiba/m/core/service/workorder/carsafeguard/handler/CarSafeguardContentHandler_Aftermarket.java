package com.kaiba.m.core.service.workorder.carsafeguard.handler;

import com.kaiba.lib.base.assembler.Mapper;
import com.kaiba.lib.base.lang.verifier.F;
import com.kaiba.lib.base.lang.verifier.V;
import com.kaiba.lib.base.lang.verifier.Verifier;
import com.kaiba.lib.base.lang.verifier.VerifierBuilder;
import com.kaiba.m.core.domain.workorder.carsafeguard.CarSafeguardAftermarketCase;
import com.kaiba.m.core.domain.workorder.carsafeguard.CarSafeguardCaseContent;
import com.kaiba.m.core.model.safeguard.request.CS4SStoreCaseCreateModel;
import com.kaiba.m.core.repository.workorder.carsafeguard.CarSafeguardAftermarketRepository;
import com.kaiba.m.core.repository.workorder.carsafeguard.CarSafeguardCaseContentRepository;
import com.kaiba.m.core.service.workorder.carsafeguard.CarSafeguardBizType;
import com.kaiba.m.core.service.workorder.carsafeguard.CarSafeguardUtils;
import com.kaiba.m.core.model.safeguard.CarSafeguardAftermarketCaseContentModel;
import com.kaiba.m.core.model.safeguard.CarSafeguardCaseContentModel;
import com.kaiba.m.core.model.safeguard.request.CSAftermarketCaseCreateModel;
import com.kaiba.m.core.model.safeguard.request.CarSafeguardCaseCreateModel;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version CarSafeguardContentHandler_Aftermarket, v0.1 2024/7/22 16:57 daopei Exp $
 **/
@Service
public class CarSafeguardContentHandler_Aftermarket implements CarSafeguardContentHandler{

    private final CarSafeguardAftermarketRepository caseRepository;
    private final CarSafeguardCaseContentRepository contentRepository;


    public CarSafeguardContentHandler_Aftermarket(CarSafeguardAftermarketRepository caseRepository, CarSafeguardCaseContentRepository contentRepository) {
        this.caseRepository = caseRepository;
        this.contentRepository = contentRepository;
    }

    @Override
    public boolean support(String contentType) {
        return CarSafeguardBizType.AUTOMOBILE_AFTERMARKET == CarSafeguardBizType.valueOf(contentType);
    }

    @Override
    public Class<CarSafeguardAftermarketCaseContentModel> contentClass() {
        return CarSafeguardAftermarketCaseContentModel.class;
    }

    @Override
    public CarSafeguardCaseContentModel getContentById(String id) {
        return caseRepository.findById(id)
                .map(c -> Mapper.map(c, CarSafeguardAftermarketCaseContentModel.class))
                .orElse(null);
    }

    @Override
    public List<CarSafeguardCaseContentModel> getContentById(Collection<String> ids) {
        return caseRepository.findByIdIn(ids).stream()
                .map(c -> Mapper.map(c, CarSafeguardAftermarketCaseContentModel.class))
                .collect(Collectors.toList());
    }

    @Override
    public <T extends CarSafeguardCaseCreateModel> CarSafeguardCaseContentModel createContent(T model) {
        aftermarketVerify.verify((CSAftermarketCaseCreateModel)model);
        CarSafeguardAftermarketCase entity = Mapper.map(model, CarSafeguardAftermarketCase.class);
        //父类属性
        entity.setContent(model.getContent());
        entity.setContentType(CarSafeguardBizType.AUTOMOBILE_AFTERMARKET.name());
        entity.setUserId(model.getClientUserId());
        entity.setSource(model.getSource());
        entity.setImages(model.getImages());
        entity.setVideo(model.getVideo());
        //基础属性
        entity.setPersonal(CarSafeguardUtils.getACLStringDataListOrThrow(model));
        entity.setOriginResolverLack(model.getOriginResolverLack());
        entity.setCreateTime(System.currentTimeMillis());
        entity.setUpdateTime(System.currentTimeMillis());
        entity = caseRepository.save(entity);

        createContentIndex(entity.getId(), CarSafeguardBizType.AUTOMOBILE_AFTERMARKET.name());
        return map2Model(entity);
    }

    public void createContentIndex(String contentId, String contentType) {
        CarSafeguardCaseContent content = new CarSafeguardCaseContent();
        content.setContentId(contentId);
        content.setContentType(contentType);
        content.setCreateTime(System.currentTimeMillis());
        contentRepository.save(content);
    }

    private static CarSafeguardAftermarketCaseContentModel map2Model(CarSafeguardAftermarketCase entity) {
        CarSafeguardAftermarketCaseContentModel model = new CarSafeguardAftermarketCaseContentModel();
        model.setId(entity.getId());
        model.setContent(entity.getContent());
        model.setContentType(CarSafeguardBizType.AUTOMOBILE_AFTERMARKET.name());
        model.setCreateTime(entity.getCreateTime());
        model.setUpdateTime(entity.getUpdateTime());
        model.setPersonal(entity.getPersonal());
        model.setOriginResolverLack(entity.getOriginResolverLack());
        model.setSource(entity.getSource());
        model.setUserId(entity.getUserId());
        model.setVideo(entity.getVideo());
        model.setImages(entity.getImages());
        model.setCarRegistration(entity.getCarRegistration());
        model.setCarRegistrationNo(entity.getCarRegistrationNo());
        return model;
    }

    static Verifier<CSAftermarketCaseCreateModel> aftermarketVerify = new VerifierBuilder<CSAftermarketCaseCreateModel>().defaultOrElseThrow()
            .and(F.intF(CSAftermarketCaseCreateModel::getCarRegistration).allowNull().in(1, 0))
            .addVerifier(V.or(
                            F.str(CSAftermarketCaseCreateModel::getOriginResolver).notNull(),
                            F.str(CSAftermarketCaseCreateModel::getOriginResolverLack).notNull())
                    .r("请选择或者输入投诉对象"))
            .addVerifier(V.or(
                    F.intF(CSAftermarketCaseCreateModel::getCarRegistration).isNull(),
                    F.intF(CSAftermarketCaseCreateModel::getCarRegistration).eq(0),
                    V.and(
                            F.intF(CSAftermarketCaseCreateModel::getCarRegistration).eq(1),
                            F.str(CSAftermarketCaseCreateModel::getCarRegistrationNo).notNull().r("请输入车牌号")
                    )
            ))
            .create();
}
