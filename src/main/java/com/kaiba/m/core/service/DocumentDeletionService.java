package com.kaiba.m.core.service;

import com.kaiba.lib.base.response.KbCode;
import com.kaiba.lib.base.response.KbException;
import com.kaiba.m.core.domain.DeletionDocument;
import com.kaiba.m.core.repository.DeletionDocumentRepository;
import com.kaiba.m.core.util.JsonUtils;
import org.bson.types.ObjectId;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;

import java.lang.reflect.Method;
import java.util.HashSet;
import java.util.Set;

/**
 * author: lyux
 * date: 18-10-22
 */
@Service
public class DocumentDeletionService {

    private static final Set<String> ALLOWED_COLLECTION_SET = new HashSet<>();
    static {
        ALLOWED_COLLECTION_SET.add("issue");
        ALLOWED_COLLECTION_SET.add("issue_expert");
        ALLOWED_COLLECTION_SET.add("issue_expert_comment");
        ALLOWED_COLLECTION_SET.add("issue_lawyer");
        ALLOWED_COLLECTION_SET.add("k_playback");
        ALLOWED_COLLECTION_SET.add("k_playback_album");
    }

    private final DeletionDocumentRepository repository;

    @Autowired
    public DocumentDeletionService(DeletionDocumentRepository repository) {
        this.repository = repository;
    }

    public void doDeletion(String collection, String documentId, Integer userId, String reason) {
        if (null == collection) {
            throw new KbException(KbCode.ILLEGAL_ARGUMENT, "collection is required when doing deletion");
        }
        if (null == documentId) {
            throw new KbException(KbCode.ILLEGAL_ARGUMENT, "documentId is required when doing deletion");
        }
        checkDeletionAllowed(collection);
        repository.doDeletion(collection, new ObjectId(documentId), userId, reason);
    }

    public void doDeletion(String collection, Integer documentId, Integer userId, String reason) {
        if (null == collection) {
            throw new KbException(KbCode.ILLEGAL_ARGUMENT, "collection is required when doing deletion");
        }
        if (null == documentId) {
            throw new KbException(KbCode.ILLEGAL_ARGUMENT, "documentId is required when doing deletion");
        }
        checkDeletionAllowed(collection);
        repository.doDeletion(collection, documentId, userId, reason);
    }

    public <T> T recover(String collection, String documentId, Class<T> clazz) {
        return getDocumentInstance(repository.doRecover(collection, new ObjectId(documentId)), documentId, clazz);
    }

    public <T> T recover(String collection, Integer documentId, Class<T> clazz) {
        return getDocumentInstance(repository.doRecover(collection, documentId), documentId, clazz);
    }

    public <T> T getById(String id, Class<T> clazz) {
        DeletionDocument document = repository
                .findById(id)
                .orElseThrow(()->new KbException(KbCode.RESOURCE_NOT_FOUND).li());
        return getDocumentInstance(document.getDocument(), document.getDocumentId(), clazz);
    }

    public <T> T getByDocument(String collection, String documentId, Class<T> clazz) {
        DeletionDocument document = repository
                .findFirstByCollectionNameAndDocumentId(collection, new ObjectId(documentId))
                .orElseThrow(()->new KbException(KbCode.RESOURCE_NOT_FOUND).li());
        return getDocumentInstance(document.getDocument(), document.getDocumentId(), clazz);
    }

    public <T> T getByDocument(String collection, Integer documentId, Class<T> clazz) {
        DeletionDocument document = repository
                .findFirstByCollectionNameAndDocumentId(collection, documentId)
                .orElseThrow(()->new KbException(KbCode.RESOURCE_NOT_FOUND).li());
        return getDocumentInstance(document.getDocument(), document.getDocumentId(), clazz);
    }

    public <T> Page<T> getListByCollection(String collection, Class<T> clazz, Integer page, Integer pageSize) {
        return repository
                .findByCollectionNameOrderByDeleteTimeDesc(collection, PageRequest.of(page - 1, pageSize))
                .map(deletion -> getDocumentInstance(deletion.getDocument(), deletion.getDocumentId(), clazz));
    }

    // ----------------------------------------------------

    private static void checkDeletionAllowed(String collectionName) {
        if (!ALLOWED_COLLECTION_SET.contains(collectionName)) {
            throw new KbException(KbCode.ILLEGAL_ARGUMENT, "deletion not allowed on collection: " + collectionName);
        }
    }

    private static <T> T getDocumentInstance(Object source, Object id, Class<T> clazz) {
        String json = JsonUtils.getGson().toJson(source);
        T document = JsonUtils.getGson().fromJson(json, clazz);
        trySetId(document, id);
        return document;
    }

    private static void trySetId(Object obj, Object id) {
        // TODO: cache the setter method if called frequently
        try {
            Class<?> beanClass = obj.getClass();
            Method getter = beanClass.getMethod("getId");
            Method setter = beanClass.getMethod("setId", getter.getReturnType());
            setter.invoke(obj, id);
        } catch (Exception ignore) {
        }
    }
}
