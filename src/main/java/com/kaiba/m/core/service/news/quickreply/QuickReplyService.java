package com.kaiba.m.core.service.news.quickreply;

import com.kaiba.lib.base.assembler.Mapper;
import com.kaiba.lib.base.domain.news.QuickReplyCategoryModel;
import com.kaiba.lib.base.domain.news.QuickReplyTemplateModel;
import com.kaiba.lib.base.response.KbCode;
import com.kaiba.lib.base.response.KbException;
import com.kaiba.lib.base.util.StringUtils;
import com.kaiba.m.core.domain.news.quickreply.QuickReplyCategory;
import com.kaiba.m.core.domain.news.quickreply.QuickReplyTemplate;
import com.kaiba.m.core.repository.news.quickreply.QuickReplyCategoryRepository;
import com.kaiba.m.core.repository.news.quickreply.QuickReplyTemplateRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

/**
 * author: lyux
 * date: 2022-08-03
 */
@Slf4j
@Service
public class QuickReplyService {

    private final QuickReplyCategoryRepository categoryRepository;
    private final QuickReplyTemplateRepository templateRepository;
    private final QuickReplyCacheService cacheService;

    public QuickReplyService(
            QuickReplyCategoryRepository categoryRepository,
            QuickReplyTemplateRepository templateRepository,
            QuickReplyCacheService cacheService) {
        this.categoryRepository = categoryRepository;
        this.templateRepository = templateRepository;
        this.cacheService = cacheService;
    }

    public Page<QuickReplyCategoryModel> getCategoryList(Integer siteId, String name, Integer page, Integer pageSize) {
        return categoryRepository.getListByPublicType(siteId, name, PageRequest.of(page - 1, pageSize))
                .map(category -> Mapper.map(category, QuickReplyCategoryModel.class));
    }

    public List<QuickReplyTemplateModel> getTemplateListByCategory(String categoryId, boolean allowCache) {
        if (allowCache) {
            return cacheService.getTemplateListByCategoryId(categoryId);
        } else {
            return templateRepository
                    .findByCategory(categoryId, PageRequest.of(0, 30))
                    .map(template -> Mapper.map(template, QuickReplyTemplateModel.class))
                    .getContent();
        }
    }

    public Optional<QuickReplyTemplateModel> getTemplateById(String templateId, boolean allowCache) {
        if (allowCache) {
            return cacheService.getTemplateInfoByTemplateId(templateId);
        } else {
            return templateRepository.findById(templateId)
                    .map(template -> Mapper.map(template, QuickReplyTemplateModel.class));
        }
    }

    public Optional<QuickReplyCategoryModel> getCategory(String categoryId) {
        return categoryRepository.findById(categoryId).map(c -> Mapper.map(c, QuickReplyCategoryModel.class));
    }

    public QuickReplyCategoryModel addCategory(Integer siteId, String name, String newsId, String description) {
        if (StringUtils.isEmpty(name)) {
            throw new KbException(KbCode.ILLEGAL_ARGUMENT, "name should not be null").r("分类名称不可为空").li();
        }
        QuickReplyCategory c = new QuickReplyCategory();
        c.setSiteId(siteId);
        c.setName(name);
        c.setNewsId(newsId);
        c.setDescription(description);
        c.setCreateTime(System.currentTimeMillis());
        c.setUpdateTime(System.currentTimeMillis());
        return Mapper.map(categoryRepository.insert(c), QuickReplyCategoryModel.class);
    }

    public QuickReplyCategoryModel updateCategory(String categoryId, String name, String description) {
        return Mapper.map(
                categoryRepository.updateCategory(categoryId, name, description),
                QuickReplyCategoryModel.class);
    }

    public void deleteCategory(String categoryId) {
        categoryRepository.deleteById(categoryId);
    }

    public QuickReplyTemplateModel addTemplate(String categoryId, String content) {
        if (StringUtils.isEmpty(categoryId)) {
            throw new KbException(KbCode.ILLEGAL_ARGUMENT, "category should not be null").r("分类标识不可为空").li();
        }
        if (StringUtils.isEmpty(content)) {
            throw new KbException(KbCode.ILLEGAL_ARGUMENT, "content should not be null").r("内容不可为空").li();
        }
        QuickReplyTemplate t = new QuickReplyTemplate();
        t.setCategory(categoryId);
        t.setContent(content);
        return Mapper.map(templateRepository.insert(t), QuickReplyTemplateModel.class);
    }

    public void deleteTemplate(String templateId) {
        templateRepository.deleteById(templateId);
    }

}
