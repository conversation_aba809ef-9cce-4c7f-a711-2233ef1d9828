package com.kaiba.m.core.service.da.eventtrack.strategy.impl;

import com.kaiba.lib.base.constant.KbModule;
import com.kaiba.lib.base.domain.applego.lego.LegoLayoutTab;
import com.kaiba.lib.base.domain.common.KbTimeRange;
import com.kaiba.m.core.constant.da.eventtrack.ETDocSyncStrategyType;
import com.kaiba.m.core.domain.applego.LegoPage;
import com.kaiba.m.core.domain.da.eventtrack.ETDoc;
import com.kaiba.m.core.repository.applego.LegoPageRepository;
import com.kaiba.m.core.repository.da.eventtrack.ETDocRepository;
import com.kaiba.m.core.repository.da.eventtrack.ETDocSyncCursorRepository;
import com.kaiba.m.core.service.da.eventtrack.ETDocModelHelper;
import com.kaiba.m.core.service.da.eventtrack.strategy.model.ETDocSyncQueryModel;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 首页稿件同步策略
 * <AUTHOR>
 * @version MainPageETDocSyncStrategy, v0.1 2025/4/8 14:25 daopei Exp $
 **/
@Service
public class MainPageETDocSyncStrategy extends AbstractETDocSyncStrategy{

    private final LegoPageRepository legoPageRepository;

    public MainPageETDocSyncStrategy(
            ETDocRepository etDocRepository,
            ETDocSyncCursorRepository etDocSyncCursorRepository,
            LegoPageRepository legoPageRepository
    ) {
        super(etDocRepository, etDocSyncCursorRepository);
        this.legoPageRepository = legoPageRepository;
    }


    @Override
    public void syncAll() {
        initDocByPageExecutor(this::execute, null);
    }

    @Override
    public void syncByTimeRange(KbTimeRange time) {
        initDocByPageExecutor(this::execute, time);
    }

    @Override
    public String getStrategy() {
        return ETDocSyncStrategyType.MAIN_PAGE.name();
    }


    private boolean execute(ETDocSyncQueryModel query) {
        Pageable pageable = createPageable(query.getPage(), query.getPageSize());
        Page<LegoPage> page;
        if (query.getTimeRange() != null) {
            Long refreshTimeStart = query.getTimeRange().getLower();
            Long refreshTimeEnd = query.getTimeRange().getUpper();
            page = legoPageRepository.findByRefreshTimeBetweenAndTabsNotNullOrderByRefreshTimeAsc(refreshTimeStart, refreshTimeEnd, pageable);
        } else {
            page = legoPageRepository.findByTabsNotNullOrderByRefreshTimeAsc(pageable);
        }
        for (LegoPage legoPage : page.getContent()) {
            List<ETDoc> docs = createDocByLego(legoPage);
            for (ETDoc doc : docs) {
                saveETDoc(doc);
            }
        }
        return page.hasNext();
    }

    private static List<ETDoc> createDocByLego(LegoPage legoPage) {
        if (legoPage == null || legoPage.getTabs() == null) {
            return null;
        }
        List<ETDoc> docs = new ArrayList<>();
        for (LegoLayoutTab tab : legoPage.getTabs()) {
            docs.add(ETDocModelHelper.createByAll(
                    KbModule.APP_MAIN_PAGE.name(), null,
                    "home", tab.getTabKey(), null,
                    legoPage.getSiteId(), "首页-首屏-"+tab.getSTitle(),
                    null,null,null,
                    legoPage.getCreateTime()
            ));
        }
        return docs;
    }
}
