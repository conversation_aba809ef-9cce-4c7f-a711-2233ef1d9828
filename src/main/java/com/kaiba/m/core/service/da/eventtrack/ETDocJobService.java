package com.kaiba.m.core.service.da.eventtrack;

import com.kaiba.m.core.constant.da.eventtrack.ETDocPushType;
import com.kaiba.m.core.constant.da.eventtrack.ETDocSyncStrategyType;
import com.kaiba.m.core.service.da.eventtrack.push.ETDocPushService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @version ETDocJobService, v0.1 2025/4/15 09:28 daopei Exp $
 **/
@Slf4j
@Service
public class ETDocJobService {

    private final ETDocSyncService etDocSyncService;
    private final ETDocPushService etDocPushService;

    public ETDocJobService(
            ETDocSyncService etDocSyncService,
            ETDocPushService etDocPushService
    ) {
        this.etDocSyncService = etDocSyncService;
        this.etDocPushService = etDocPushService;
    }

    @XxlJob("etDoc-sync-hour")
    public ReturnT<String> syncHourly(String param) {
        etDocSyncService.syncStrategyByHOUR();
        return ReturnT.SUCCESS;
    }

    @XxlJob("etDoc-sync-day")
    public ReturnT<String> syncDaily(String param) {
        etDocSyncService.syncStrategyByDAY();
        return ReturnT.SUCCESS;
    }


    @XxlJob("etDoc-push-hour")
    public ReturnT<String> pushHourly(String param) {
        etDocPushService.pushWithLock(ETDocPushType.DOC);
        return ReturnT.SUCCESS;
    }
}
