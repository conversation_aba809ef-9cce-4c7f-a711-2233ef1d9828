package com.kaiba.m.core.service.news.pool.knowledge.impl;

import com.google.common.collect.Lists;
import com.kaiba.m.core.domain.knowledge.Knowledge;
import com.kaiba.m.core.domain.news.article.NewsArticle;
import com.kaiba.m.core.repository.news.pool.knowledge.KnowledgeRepository;
import com.kaiba.m.core.service.news.article.NewsArticleService;
import com.kaiba.m.core.service.news.pool.knowledge.IKnowledgeSyncOpenSearchService;
import com.kaiba.m.core.service.news.pool.knowledge.mapper.KnowledgeMapping;
import com.kaiba.m.core.service.opensearch.news.NewsSearchClient;
import java.util.Optional;
import javax.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

/**
 * Description: 知识库同步OpenSearch的Service实现
 * Author: ZM227
 * Date: 2025/1/16 17:09
 */
@Service
public class KnowledgeSyncOpenSearchServiceImpl implements IKnowledgeSyncOpenSearchService {

    @Resource
    private KnowledgeRepository knowledgeRepository;
    @Resource
    private NewsArticleService newsArticleService;
    @Resource
    private NewsSearchClient newsSearchClient;
    @Resource
    private KnowledgeMapping knowledgeMapping;

    @Override
    @Async
    public void syncOpenSearch(String knowledgeId) {
        if (StringUtils.isBlank(knowledgeId)) {
            return;
        }
        Optional<Knowledge> knowledgeOptional = knowledgeRepository.findById(knowledgeId);
        if (!knowledgeOptional.isPresent()) {
            return;
        }
        Knowledge knowledge = knowledgeOptional.get();
        Optional<NewsArticle> articleOptional = newsArticleService.getArticleById(
            knowledge.getArticleId());
        if (!articleOptional.isPresent()) {
            return;
        }
        newsSearchClient.addOrUpdateNews(Lists.newArrayList(
            knowledgeMapping.toOpenSearchNewsDTO(knowledge, articleOptional.get())));
    }
}