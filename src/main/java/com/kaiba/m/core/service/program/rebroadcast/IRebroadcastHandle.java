package com.kaiba.m.core.service.program.rebroadcast;

import com.kaiba.m.core.domain.program.ScheduleInstance;
import com.kaiba.m.core.service.program.ScheduleInstanceService;

import java.util.Optional;

/**
 * author wangsj
 * date 2020-09-01
 */
public interface IRebroadcastHandle {
    Optional<ScheduleInstance> getBroadcastInstance(ScheduleInstanceService service, String programId, Integer rebroadcastTime);
}
