package com.kaiba.m.core.service.note.event.handler;

import com.kaiba.m.core.domain.note.Note;
import com.kaiba.m.core.domain.note.NoteReview;
import com.kaiba.m.core.service.note.event.INoteEventReceiver;
import com.kaiba.m.core.service.note.note.NoteMediaService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version NoteMediaHandler, v0.1 2025/7/2 14:20 daopei Exp $
 **/
@Slf4j
@Component
public class NoteMediaHandler implements INoteEventReceiver {

    private final NoteMediaService noteMediaService;

    public NoteMediaHandler(
            NoteMediaService noteMediaService
    ) {
        this.noteMediaService = noteMediaService;
    }

    @Override
    public void onNoteAdd(Note note, NoteReview noteReview) {
        //待审核则忽略
        if (note == null) {
            return;
        }
        //无媒资视频则忽略
        if (note.getMediaId() == null) {
            return;
        }
        noteMediaService.noteAddAfter(note);
    }
}
