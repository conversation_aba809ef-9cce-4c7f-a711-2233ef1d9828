package com.kaiba.m.core.service.program.adrush;

import com.kaiba.lib.base.assembler.Mapper;
import com.kaiba.lib.base.constant.program.ActiveTimeType;
import com.kaiba.lib.base.constant.program.AdRushType;
import com.kaiba.lib.base.constant.rush.RushState;
import com.kaiba.lib.base.domain.program.adrush.ActiveTimeModel;
import com.kaiba.lib.base.domain.program.adrush.AdRushModel;
import com.kaiba.lib.base.domain.rush.RushModel;
import com.kaiba.lib.base.domain.urlmap.UrlMapperModel;
import com.kaiba.lib.base.response.KbCode;
import com.kaiba.lib.base.response.KbException;
import com.kaiba.lib.base.service.IRushService;
import com.kaiba.lib.base.service.IUrlMapperService;
import com.kaiba.m.core.domain.program.adrush.AdRush;
import com.kaiba.m.core.domain.program.adrush.ActiveTime;
import com.kaiba.m.core.repository.program.adrush.AdRushRepository;
import com.kaiba.m.core.repository.program.adrush.ActiveTimeRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

/**
 * 节目 - 广告抽奖
 * author: yeqq
 * date: 21-02-05
 */
@Slf4j
@Service
public class AdRushService {

    private final AdRushRepository adRushRepository;
    private final ActiveTimeRepository activeTimeRepository;
    private final IUrlMapperService urlMapperService;
    private final IRushService rushService;
    private final String adRushUrl;

    public AdRushService(
            @Value("${kaiba.host.page}") String pageHost,
            AdRushRepository adRushRepository,
            ActiveTimeRepository activeTimeRepository,
            IUrlMapperService urlMapperService,
            IRushService rushService
    ) {
        this.adRushRepository = adRushRepository;
        this.activeTimeRepository = activeTimeRepository;
        this.urlMapperService = urlMapperService;
        this.rushService = rushService;
        this.adRushUrl = pageHost + "/programAd/index?adId=";
    }

    public AdRushModel createAdRush(AdRushModel adRushModel) {
        AdRush adRush = model2adRush(adRushModel);
        List<ActiveTimeModel> activeTimeModels = adRushModel.getActiveTimes();
        ActiveTimeModel activeTimeModel = activeTimeModels.get(0);
        verifyAdRushOnCreate(adRush);
        verifyActiveTimeModelOnCreate(activeTimeModel);
        if (null != adRush.getAdLink()) {
            boolean needDecorate = adRush.getAdLink().contains("kaiba315.com.cn");
            UrlMapperModel model = urlMapperService.createUrlMapper(adRush.getAdLink(), adRush.getSiteId(),
                    needDecorate, null).dataOrThrow();
            adRush.setAdUrlMapper(model.getMapUrl());
        }
        adRushModel.getRushModel().setStateChangeCallbackUrl("http://kaiba-m-core/adRush/rushStateChangeCallback");
        RushModel rush = rushService.createRushByBody(adRushModel.getRushModel()).dataOrThrow();
        adRush.setRushId(rush.getId());
        adRush.setRushState(rush.getState());
        adRush.setCreateTime(System.currentTimeMillis() / 1000);
        adRush = adRushRepository.insert(adRush);
        log.info("adRush create: " + adRush);
        createActiveTime(adRush.getId(), rush.getId(), rush.getState(), adRush.getSiteId(), activeTimeModel);
        String originalUrl = adRushUrl + adRush.getId();
        UrlMapperModel urlMapperModel = urlMapperService.createUrlMapper(originalUrl, adRush.getSiteId(),
                true, null).dataOrThrow();
        adRush.setOriginalUrl(originalUrl);
        adRush.setMapperUrl(urlMapperModel.getMapUrl());
        adRush = adRushRepository.updateAdRush(adRush);
        log.info("adRush update: " + adRush);
        return adRush2model(adRush);
    }

    public AdRushModel updateAdRush(AdRushModel adRushModel) {
        AdRush newAdRush = model2adRush(adRushModel);
        if (null == newAdRush.getId()) {
            throw new KbException(KbCode.RESOURCE_NOT_FOUND, "adRush id null on update: " + newAdRush).li();
        }
        verifyAdRushOnCreate(newAdRush);
        AdRush oldAdRush = adRushRepository.findById(newAdRush.getId()).orElseThrow(() -> new KbException(
                KbCode.RESOURCE_NOT_FOUND, "ad rush not exists: " + adRushModel.getId()).li());
        if (oldAdRush.getAdLink() != null && adRushModel.getAdLink() != null
                && !oldAdRush.getAdLink().equals(adRushModel.getAdLink())) {
            UrlMapperModel model = urlMapperService.createUrlMapper(adRushModel.getAdLink(),
                    adRushModel.getSiteId(), null, null).dataOrThrow();
            newAdRush.setAdUrlMapper(model.getMapUrl());
        }
        newAdRush = adRushRepository.updateAdRush(newAdRush);
        log.info("adRush update: " + newAdRush);
        return adRush2model(newAdRush);
    }

    private void createActiveTime(String adRushId, String rushId, Integer rushState, Integer siteId, ActiveTimeModel activeTimeModel) {
        ActiveTime activeTime = new ActiveTime();
        activeTime.setType(activeTimeModel.getType());
        activeTime.setStartTime(activeTimeModel.getStartTime());
        activeTime.setEndTime(activeTimeModel.getEndTime());
        activeTime.setScheduleId(activeTimeModel.getScheduleId());
        activeTime.setProgramName(activeTimeModel.getProgramName());
        activeTime.setAdRushId(adRushId);
        activeTime.setSiteId(siteId);
        activeTime.setRushId(rushId);
        activeTime.setRushState(rushState);
        activeTime.setCreateTime(System.currentTimeMillis() / 1000);
        activeTime = activeTimeRepository.insert(activeTime);
        log.info("adRush active time create: " + activeTime);
    }

    public ActiveTimeModel updateActiveTime(ActiveTimeModel activeTimeModel) {
        if (null == activeTimeModel.getId()) {
            throw new KbException(KbCode.ILLEGAL_ARGUMENT, "activeTime id should not be null")
                    .setReadableMessage("更新的广告关联信息不存在").li();
        }
        if (null == activeTimeModel.getType()) {
            throw new KbException(KbCode.ILLEGAL_ARGUMENT, "activeTimeType should not be null")
                    .setReadableMessage("广告的展示类型不能为空").li();
        }
        if (null == activeTimeModel.getStartTime() || null == activeTimeModel.getEndTime()) {
            throw new KbException(KbCode.ILLEGAL_ARGUMENT, "startTime or endTime should not be null")
                    .setReadableMessage("广告关联的时间不能为空").li();
        }
        if (activeTimeModel.getType() == ActiveTimeType.APPOINT_PROGRAM.getValue()) {//指定节目
            if (null == activeTimeModel.getScheduleId()) {
                throw new KbException(KbCode.ILLEGAL_ARGUMENT, "scheduleId should not be null")
                        .setReadableMessage("广告关联的节目实例不能为空").li();
            }
        }
        Integer[] states = new Integer[]{
                RushState.PREPARE.getValue(),
                RushState.SIGNED.getValue(),
                RushState.ONLINE.getValue(),
                RushState.PAUSED.getValue(),
        };
        Optional<ActiveTime> optionalActiveTime = activeTimeRepository.findById(activeTimeModel.getId());
        if (!optionalActiveTime.isPresent()) {
            throw new KbException(KbCode.RESOURCE_NOT_FOUND, "active time is not be found")
                    .setReadableMessage("要更新的关联时间为空").li();
        }
        ActiveTime oldActiveTime = optionalActiveTime.get();
        List<ActiveTime> activeTimes = getActiveTimeList(activeTimeModel.getSiteId(), states
                , activeTimeModel.getStartTime(), activeTimeModel.getEndTime(), 1, 15).getContent();
        if (activeTimes.size() == 0) {
            ActiveTime updateActiveTime = updateActiveTime(oldActiveTime, activeTimeModel);
            log.info("activeTime update: " + updateActiveTime);
            return activeTime2model(updateActiveTime);
        } else if (activeTimes.size() == 1) {
            ActiveTime findActive = activeTimes.get(0);
            if (findActive.getId().equals(oldActiveTime.getId())) {
                ActiveTime updateActiveTime = updateActiveTime(oldActiveTime, activeTimeModel);
                log.info("activeTime update: " + updateActiveTime);
                return activeTime2model(updateActiveTime);
            } else {
                throw new KbException(KbCode.ILLEGAL_ARGUMENT, "this time is has adRush")
                        .setReadableMessage("该时间段已有内容广告").li();
            }

        } else {
            throw new KbException(KbCode.ILLEGAL_ARGUMENT, "this time is has adRush")
                    .setReadableMessage("该时间段已有内容广告").li();
        }
    }

    private ActiveTime updateActiveTime(ActiveTime oldActiveTime, ActiveTimeModel activeTimeModel) {
        oldActiveTime.setStartTime(activeTimeModel.getStartTime());
        oldActiveTime.setEndTime(activeTimeModel.getEndTime());
        oldActiveTime.setType(activeTimeModel.getType());
        oldActiveTime.setScheduleId(activeTimeModel.getScheduleId());
        oldActiveTime.setProgramName(activeTimeModel.getProgramName());
        return activeTimeRepository.updateActiveTime(oldActiveTime);
    }

    public void updateRushState(String rushId, Integer rushState) {
        activeTimeRepository.updateRushState(rushId, rushState);
        adRushRepository.updateRushState(rushId, rushState);
    }

    public void deleteAdRushById(String adRushId) {
        adRushRepository.deleteById(adRushId);
        activeTimeRepository.deleteByAdRushId(adRushId);
    }

    public long checkActiveTimeByTime(Integer siteId, Long startTime, Long endTime) {
        Integer[] states = new Integer[]{
                RushState.PREPARE.getValue(),
                RushState.SIGNED.getValue(),
                RushState.ONLINE.getValue(),
                RushState.PAUSED.getValue(),
        };
        return activeTimeRepository.countBySiteIdAndRushStateInAndEndTimeGreaterThanAndStartTimeLessThanOrderByCreateTimeDesc(siteId,
                states, startTime, endTime);
    }

    public List<AdRush> getAdRushListByIdIn(List<String> adRushIdList) {
        return adRushRepository.findByIdIn(adRushIdList);
    }

    public Optional<AdRush> getAdRushById(String adRushId) {
        return adRushRepository.findById(adRushId);
    }

    public Optional<ActiveTime> getActiveTimeByAdRushId(String adRushId) {
        return activeTimeRepository.findFirstByAdRushId(adRushId);
    }

    public Page<AdRush> getAdRushListBySiteId(Integer siteId, Integer[] states, Integer[] types, Integer page, Integer pageSize) {
        int p = page == null ? 0 : page - 1;
        int ps = pageSize == null || pageSize > 100 ? 15 : pageSize;
        Pageable pageable = PageRequest.of(p, ps);
        if (null == states && null == types) {
            return adRushRepository.findBySiteIdOrderByCreateTimeDesc(siteId, pageable);
        } else {
            if (null != states && null != types) {
                return adRushRepository.findBySiteIdAndRushStateInAndTypeInOrderByCreateTimeDesc(siteId, states, types, pageable);
            } else if (null != types) {
                return adRushRepository.findBySiteIdAndTypeInOrderByCreateTimeDesc(siteId, types, pageable);
            } else {
                return adRushRepository.findBySiteIdAndRushStateInOrderByCreateTimeDesc(siteId, states, pageable);
            }
        }
    }

    public Page<ActiveTime> getActiveTimeList(Integer siteId, Integer[] states, Long startTime, Long endTime,
                                              Integer page, Integer pageSize) {
        int p = page == null ? 0 : page - 1;
        int ps = pageSize == null || pageSize > 100 ? 15 : pageSize;
        Pageable pageable = PageRequest.of(p, ps);
        if (null == states) {
            return activeTimeRepository.findBySiteIdAndEndTimeGreaterThanAndStartTimeLessThanOrderByCreateTimeDesc(siteId,
                    startTime, endTime, pageable);
        } else {
            return activeTimeRepository.findBySiteIdAndRushStateInAndEndTimeGreaterThanAndStartTimeLessThanOrderByCreateTimeDesc(siteId,
                    states, startTime, endTime, pageable);
        }
    }

    private void verifyAdRushOnCreate(AdRush adRush) {
        if (adRush == null) {
            throw new KbException(KbCode.ILLEGAL_ARGUMENT, "adRush should note be null").li();
        }

        if (adRush.getCreateUserId() == null) {
            throw new KbException(KbCode.ILLEGAL_ARGUMENT, "user id should note be null: " + adRush)
                    .setReadableMessage("创建者标识不能为空").li();
        }

        if (adRush.getSiteId() == null) {
            throw new KbException(KbCode.ILLEGAL_ARGUMENT, "site id should note be null: " + adRush)
                    .setReadableMessage("电台不能为空").li();
        }

        if (adRush.getTitle() == null) {
            throw new KbException(KbCode.ILLEGAL_ARGUMENT, "title should note be null: " + adRush)
                    .setReadableMessage("标题不能为空").li();
        }

        if (adRush.getType() == null) {
            throw new KbException(KbCode.ILLEGAL_ARGUMENT, "type should note be null: " + adRush)
                    .setReadableMessage("内容广告类型不能为空").li();
        } else {
            AdRushType adRushType = AdRushType.valueOf(adRush.getType()).orElseThrow(() ->
                    new KbException(KbCode.ILLEGAL_ARGUMENT, "wrong prize type: " + adRush).li());
            switch (adRushType) {
                case IMAGE:
                    if (adRush.getImageAdRush() == null) {
                        throw new KbException(KbCode.ILLEGAL_ARGUMENT, "ad rush image note be null: " + adRush)
                                .setReadableMessage("广告图片不能为空").li();
                    }
                    break;
                case RUSH: {
                    if (adRush.getImageAdRush() == null) {
                        throw new KbException(KbCode.ILLEGAL_ARGUMENT, "ad rush image note be null: " + adRush)
                                .setReadableMessage("抽奖图片不能为空").li();
                    }
                    break;
                }
                case CUSTOM_RUSH: {
                    if (adRush.getImageAdRush() == null) {
                        throw new KbException(KbCode.ILLEGAL_ARGUMENT, "ad rush image note be null: " + adRush)
                                .setReadableMessage("抽奖图片不能为空").li();
                    }
                    if (adRush.getImageAdLucky() == null) {
                        throw new KbException(KbCode.ILLEGAL_ARGUMENT, "ad rush lucky image note be null: " + adRush)
                                .setReadableMessage("中奖图片不能为空").li();
                    }
                    if (adRush.getImageAdUnlucky() == null) {
                        throw new KbException(KbCode.ILLEGAL_ARGUMENT, "ad rush un lucky image note be null: " + adRush)
                                .setReadableMessage("未中奖图片不能为空").li();
                    }
                    if (adRush.getImageAdRushNoChance() == null) {
                        throw new KbException(KbCode.ILLEGAL_ARGUMENT, "ad rush no chance image note be null: " + adRush)
                                .setReadableMessage("没有机会图片不能为空").li();
                    }
                    break;
                }
            }
        }

    }

    private void verifyActiveTimeModelOnCreate(ActiveTimeModel activeTimeModel) {
        if (null == activeTimeModel.getType()) {
            throw new KbException(KbCode.ILLEGAL_ARGUMENT, "activeTimeType should not be null")
                    .setReadableMessage("广告的展示类型不能为空").li();
        }
        if (null == activeTimeModel.getStartTime() || null == activeTimeModel.getEndTime()) {
            throw new KbException(KbCode.ILLEGAL_ARGUMENT, "startTime or endTime should not be null")
                    .setReadableMessage("广告关联的时间不能为空").li();
        }
        if (activeTimeModel.getType() == ActiveTimeType.APPOINT_PROGRAM.getValue()) {//指定节目
            if (null == activeTimeModel.getScheduleId()) {
                throw new KbException(KbCode.ILLEGAL_ARGUMENT, "scheduleId should not be null")
                        .setReadableMessage("广告关联的节目实例不能为空").li();
            }
        }
        if (checkActiveTimeByTime(activeTimeModel.getSiteId(), activeTimeModel.getStartTime(),
                activeTimeModel.getEndTime()) > 0) {
            throw new KbException(KbCode.ILLEGAL_ARGUMENT, "this time is has adRush")
                    .setReadableMessage("该时间段已有内容广告").li();
        }
    }

    public static AdRushModel adRush2model(AdRush adRush) {
        return Mapper.map(adRush, AdRushModel.class);
    }

    public static AdRush model2adRush(AdRushModel adRushModel) {
        return Mapper.map(adRushModel, AdRush.class);
    }

    public static ActiveTimeModel activeTime2model(ActiveTime activeTime) {
        return Mapper.map(activeTime, ActiveTimeModel.class);
    }

    public static ActiveTime model2activeTime(ActiveTimeModel activeTimeModel) {
        return Mapper.map(activeTimeModel, ActiveTime.class);
    }
}
