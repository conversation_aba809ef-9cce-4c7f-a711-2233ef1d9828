package com.kaiba.m.core.service.prize;

import com.kaiba.lib.base.constant.accounting.BusinessType;
import com.kaiba.lib.base.domain.accounting.TransactionModel;
import com.kaiba.lib.base.response.KbCode;
import com.kaiba.lib.base.response.KbEntity;
import com.kaiba.lib.base.service.IAccountingService;
import com.kaiba.lib.base.service.IAccountingTransactionService;
import com.kaiba.m.core.domain.prize.Prize;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

/**
 * author: lyux
 * date: 2020-06-03
 */
@Slf4j
@Service
public class PrizeTransactionService {

    private final IAccountingService accountingService;
    private final IAccountingTransactionService accountingTransactionService;
    private final PrizeService prizeService;

    public PrizeTransactionService(
            IAccountingService accountingService,
            IAccountingTransactionService accountingTransactionService,
            PrizeService prizeService
    ) {
        this.accountingService = accountingService;
        this.accountingTransactionService = accountingTransactionService;
        this.prizeService = prizeService;
    }

    public Optional<String> doOrGetTransactionForPrizeConfirmPay(Prize prize) {
        if (prize.getTransactionId() != null) {
            return Optional.of(prize.getTransactionId());
        }
        KbEntity<TransactionModel> entity = accountingTransactionService.prizeConfirmPayUser(
                prize.getUserId(),
                prize.getTransactionAccountId(),
                prize.getId(),
                prize.getTransactionAmount().floatValue(), "幸福消费券");
        if (entity.getCode() == KbCode.ACCOUNTING_TRANSACTION_BUSINESS_ID_EXISTS.getCode()) {
            List<TransactionModel> tranList = accountingService
                    .getTransactionListByBusiness(BusinessType.PRIZE_CONFIRM_TRANSACTION.getType(), prize.getId(), 1, 1, null)
                    .dataOrThrow();
            if (tranList.size() == 0) {
                log.error("get transaction by business id fail, transaction list empty." +
                        " businessType: " + BusinessType.PRIZE_CONFIRM_TRANSACTION.getType() + "," +
                        " prizeId: " + prize.getId());
                return Optional.empty();
            } else {
                return Optional.of(tranList.get(0).getId());
            }
        } else {
            return entity.data().map(transaction -> {
                prize.setTransactionId(transaction.getId());
                prizeService.updateTransactionId(prize.getId(), transaction.getId());
                return transaction.getId();
            });
        }
    }

}
