package com.kaiba.m.core.service.workorder.carsafeguard;

import com.kaiba.lib.base.constant.workorder.WOBusiness;
import com.kaiba.lib.base.constant.workorder.WOCloseReason;
import com.kaiba.lib.base.constant.workorder.WOIdentity;
import com.kaiba.lib.base.constant.workorder.WOOperation;
import com.kaiba.lib.base.domain.workorder.WOACLStringData;
import com.kaiba.lib.base.domain.workorder.WOCaseContentBrief;
import com.kaiba.lib.base.domain.workorder.WOCaseCreateModel;
import com.kaiba.lib.base.domain.workorder.WOCaseSearchResult;
import com.kaiba.lib.base.lang.collections.KbColUtils;
import com.kaiba.lib.base.response.KbCode;
import com.kaiba.lib.base.response.KbException;
import com.kaiba.lib.base.util.GsonUtils;
import com.kaiba.lib.base.util.StringUtils;
import com.kaiba.m.core.domain.workorder.WOCallbackTrigger;
import com.kaiba.m.core.domain.workorder.WOCase;
import com.kaiba.m.core.domain.workorder.WOEvent;
import com.kaiba.m.core.domain.workorder.WOTag;
import com.kaiba.m.core.domain.workorder.WOTeam;
import com.kaiba.m.core.domain.workorder.carsafeguard.CarSafeguardCaseContent;
import com.kaiba.m.core.domain.workorder.carsafeguard.CarSafeguardTriggerPause;
import com.kaiba.m.core.domain.workorder.carsafeguard.CarSafeguardTriggerPauseHis;
import com.kaiba.m.core.repository.workorder.WOCallbackTriggerRepository;
import com.kaiba.m.core.repository.workorder.carsafeguard.CarSafeguardCaseContentRepository;
import com.kaiba.m.core.repository.workorder.carsafeguard.CarSafeguardTriggerPauseHisRepository;
import com.kaiba.m.core.repository.workorder.carsafeguard.CarSafeguardTriggerPauseRepository;
import com.kaiba.m.core.service.workorder.AbsBizCaseHandler;
import com.kaiba.m.core.service.workorder.WOCaseToolkit;
import com.kaiba.m.core.service.workorder.WOConsts;
import com.kaiba.m.core.service.workorder.WOEventCommitter;
import com.kaiba.m.core.service.workorder.WOEventContext;
import com.kaiba.m.core.service.workorder.WOModelUtil;
import com.kaiba.m.core.service.workorder.carsafeguard.event.CarSafeguardEventListener;
import com.kaiba.m.core.service.workorder.carsafeguard.handler.CarSafeguardContentHandler;
import com.kaiba.m.core.model.safeguard.CarSafeguardCaseContentModel;
import com.kaiba.m.core.model.safeguard.CarSafeguardTriggerModel;
import com.kaiba.m.core.model.safeguard.request.CarSafeguardCaseCreateModel;
import com.kaiba.m.core.model.safeguard.search.CSQuerySearchBaseModel;
import com.kaiba.m.core.service.workorder.search.SearchQueryExpression;
import com.kaiba.m.core.service.workorder.search.SearchRequest;
import com.kaiba.m.core.service.workorder.search.SearchResultModel;
import com.kaiba.m.core.service.workorder.search.SearchSort;
import com.kaiba.m.core.service.workorder.search.OpenSearchSearcherService;
import com.kaiba.m.core.service.workorder.search.WOOpenSearchMapper;
import com.kaiba.m.core.service.workorder.search.WOOpenSearchUtils;
import com.kaiba.m.core.service.workorder.tag.WOTagCacheService;
import com.kaiba.m.core.service.workorder.tag.WOTagConsts;
import com.kaiba.m.core.service.workorder.tag.WOTagService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version CarSafeguardService, v0.1 2024/7/12 14:38 daopei Exp $
 **/
@Slf4j
@Service
public class CarSafeguardService extends AbsBizCaseHandler<CarSafeguardCaseContentModel> {

    private final static String close_refuse_cache_key = "java_core_wo_close_refuse_times_";

    private final CarSafeguardCaseContentRepository contentRepository;
    private final CarSafeguardTriggerPauseRepository triggerPauseRepository;
    private final CarSafeguardTriggerPauseHisRepository triggerPauseHisRepository;
    private final WOCallbackTriggerRepository triggerRepository;
    private final WOCaseToolkit caseToolkit;
    private final WOTagCacheService tagCacheService;
    private final WOTagService tagService;
    private final CarSafeguardEventListener eventListener;
    private final CarSafeguardNotifyService notifyService;
    private final OpenSearchSearcherService searcherService;
    private final StringRedisTemplate redisTemplate;

    private final List<CarSafeguardContentHandler> contentHandlers;

    public CarSafeguardService(
            CarSafeguardCaseContentRepository contentRepository,
            CarSafeguardTriggerPauseRepository triggerPauseRepository,
            CarSafeguardTriggerPauseHisRepository triggerPauseHisRepository,
            WOCallbackTriggerRepository triggerRepository,
            WOCaseToolkit caseToolkit,
            WOTagCacheService tagCacheService,
            WOTagService tagService,
            CarSafeguardEventListener eventListener,
            CarSafeguardNotifyService notifyService,
            OpenSearchSearcherService searcherService,
            StringRedisTemplate redisTemplate,
            List<CarSafeguardContentHandler> contentHandlers
    ) {
        this.contentRepository = contentRepository;
        this.triggerPauseRepository = triggerPauseRepository;
        this.triggerPauseHisRepository = triggerPauseHisRepository;
        this.triggerRepository = triggerRepository;
        this.caseToolkit = caseToolkit;
        this.tagCacheService = tagCacheService;
        this.tagService = tagService;
        this.contentHandlers = contentHandlers;
        this.eventListener = eventListener;
        this.notifyService = notifyService;
        this.searcherService = searcherService;
        this.redisTemplate = redisTemplate;
        //注册案件倒计时模块事件
        super.afterEvent(this::acceptAfter);
        super.afterEvent(this::auditAfter);
        super.afterEvent(this::refuseAfter);
        super.afterEvent(this::closeSuggest);
    }

    private CarSafeguardContentHandler getContentHandler(String contentType) {
        for (CarSafeguardContentHandler contentHandler : contentHandlers) {
            if (contentHandler.support(contentType)) {
                return contentHandler;
            }
        }
        throw new KbException(KbCode.ILLEGAL_ARGUMENT).r("未找到匹配的处理器");
    }


    public WOCase createCaseByClient(CarSafeguardCaseCreateModel model, CarSafeguardCaseContentModel content, Integer userId) {
        //转化内容获取对应的标签并且填充
        transferContentToTag(model, content);
        //验证标签
        verifyTag(model.getTagCodes());
        WOCaseCreateModel caseCreateModel = new WOCaseCreateModel();
        caseCreateModel.setBiz(getBusiness().name());
        caseCreateModel.setContentId(content.obtainContentId());
        caseCreateModel.setOrigin(model.getOrigin());
        caseCreateModel.setClientUserId(userId);
        caseCreateModel.setClientMobile(model.getClientMobile());
        caseCreateModel.setClientName(model.getClientName());
        caseCreateModel.setOriginResolver(model.getOriginResolver());
        caseCreateModel.setBizState(CarSafeguardBizState.WAIT_AUDIT.name());
        WOCase woCase = caseToolkit.createCaseByClient(caseCreateModel, content2brief(content), userId);
        if (model.getTagCodes() != null && !model.getTagCodes().isEmpty()) {
            tagService.addTagListToCase(woCase.getId(), model.getTagCodes(), userId);
        }
        return woCase;
    }

    public WOCase createCaseByManager(CarSafeguardCaseCreateModel model, CarSafeguardCaseContentModel content, Integer userId) {
        //转化内容获取对应的标签并且填充
        transferContentToTag(model, content);
        //验证标签
        verifyTag(model.getTagCodes());
        WOCaseCreateModel caseCreateModel = new WOCaseCreateModel();
        caseCreateModel.setBiz(getBusiness().name());
        caseCreateModel.setContentId(content.obtainContentId());
        caseCreateModel.setOrigin(model.getOrigin());
        caseCreateModel.setClientUserId(model.getClientUserId());
        caseCreateModel.setClientMobile(model.getClientMobile());
        caseCreateModel.setOriginResolver(model.getOriginResolver());
        caseCreateModel.setBizState(CarSafeguardBizState.WAIT_AUDIT.name());
        WOCase woCase = caseToolkit.createCaseByManager(caseCreateModel, content2brief(content), userId);
        if (model.getTagCodes() != null && !model.getTagCodes().isEmpty()) {
            tagService.addTagListToCase(woCase.getId(), model.getTagCodes(), userId);
        }
        //推送管理员新增案件
        notifyService.managerCreateCase(woCase, content);
        return woCase;
    }

    public <T extends CarSafeguardCaseCreateModel> CarSafeguardCaseContentModel createContent(CarSafeguardBizType contentType, T model, Integer userId, boolean manager) {
        verifyBaseCreateModel(model, manager);
        return getContentHandler(contentType.name())
                .createContent(model);
    }

    // ------------------------------------

    public List<CarSafeguardTriggerModel> getTriggerByCaseIds(List<String> caseIds) {
        if (caseIds == null || caseIds.isEmpty()) {
            return Collections.emptyList();
        }
        List<CarSafeguardTriggerPause> pauseList = triggerPauseRepository.findByCaseIdIn(caseIds);
        List<WOCallbackTrigger> triggerList = triggerRepository.findByCaseIdInOrderByFireTimeDesc(caseIds);

        return map2TriggerList(caseIds, triggerList, pauseList);
    }

    public CarSafeguardTriggerModel getTriggerByCaseId(String caseId) {
        if (caseId == null) {
            return null;
        }
        CarSafeguardTriggerPause pause = triggerPauseRepository.findByCaseId(caseId).orElse(null);
        List<WOCallbackTrigger> triggerList = triggerRepository.getByCaseIdOrderByFireTimeDesc(caseId);
        WOCallbackTrigger trigger = triggerList.isEmpty() ? null : triggerList.get(0);
        return map2Trigger(caseId, trigger, pause);
    }

    public void pauseCaseTrigger(String caseId, Integer userId) {
        WOCase woCase = caseToolkit.getCaseById(caseId);
        List<WOCallbackTrigger> triggers = triggerRepository.getByCaseIdOrderByFireTimeDesc(caseId);
        if (triggers.isEmpty()) {
            throw new KbException(KbCode.RESOURCE_NOT_FOUND).r("not found trigger").li();
        }
        if (triggerPauseRepository.findByCaseId(caseId).isPresent()) {
            throw new KbException(KbCode.ILLEGAL_STATE).r("trigger already paused").li();
        }

        WOCallbackTrigger trigger = triggers.get(0);
        CarSafeguardTriggerPause pause = mapTriggerPause(trigger, woCase, userId);
        if (pause.getPauseLessMils() <= 0) {
            throw new KbException(KbCode.ILLEGAL_STATE).r("trigger less time not enough").li();
        }
        triggerPauseRepository.save(pause);
        triggerRepository.deleteByCaseId(caseId);
    }

    public void resumeCaseTrigger(String caseId, Integer userId) {
        CarSafeguardTriggerPause pause = triggerPauseRepository.findByCaseId(caseId)
                .orElseThrow(() -> new KbException(KbCode.RESOURCE_NOT_FOUND).li());
        CarSafeguardTriggerPauseHis his = mapTriggerPauseHis(pause, userId);
        WOCallbackTrigger trigger = mapTriggerResume(pause);
        triggerRepository.upsert(
                trigger.getCaseId(), trigger.getBiz(), trigger.getEventId(),
                trigger.getMark(), trigger.getFireTime(), trigger.getExpireTime()
        );
        triggerPauseHisRepository.save(his);
        triggerPauseRepository.deleteById(pause.getId());
    }

    public void caseTriggerDelay(String caseId, Long timestamp, Integer userId) {
        List<WOCallbackTrigger> triggers = triggerRepository.getByCaseIdOrderByFireTimeDesc(caseId);
        if (triggers.isEmpty()) {
            throw new KbException(KbCode.RESOURCE_NOT_FOUND).r("当前维权未处于计时阶段").li();
        }
        WOCallbackTrigger trigger = triggers.get(0);
        long expiredDiff = trigger.getExpireTime() - trigger.getFireTime();
        triggerRepository.updateTimeByTriggerId(trigger.getId(), timestamp, timestamp + expiredDiff);
        log.info("car safeguard update trigger time for case:{}, userId:{}", caseId, userId);
    }

    /**
     * 给案件打上节目曝光标签和节目曝光未播报标签
     *
     * @param caseId
     * @param userId
     */
    public void caseProgramExposure(String caseId, Integer userId) {
        Map<String, Long> tagCodeMap =
                tagService.getTagCodeByKeyIn(Arrays.asList(
                        CarSafeguardConstants.platform_exposure_tag,
                        CarSafeguardConstants.program_exposure_tag,
                        CarSafeguardConstants.program_exposure_show_tag,
                        CarSafeguardConstants.program_exposure_not_show_tag), true);

        List<Long> needTagCode = new ArrayList<>();
        if (!tagService.isCaseTagged(caseId, tagCodeMap.get(CarSafeguardConstants.platform_exposure_tag))) {
            throw new KbException(KbCode.ILLEGAL_STATE).r("案件未平台曝光不可进行节目曝光");
        }
        if (!tagService.isCaseTagged(caseId, tagCodeMap.get(CarSafeguardConstants.program_exposure_tag))) {
            needTagCode.add(tagCodeMap.get(CarSafeguardConstants.program_exposure_tag));
        }
        //已播报和未播报实现互斥效果
        if (!tagService.isCaseTagged(caseId, tagCodeMap.get(CarSafeguardConstants.program_exposure_show_tag))) {
            needTagCode.add(tagCodeMap.get(CarSafeguardConstants.program_exposure_not_show_tag));
        }
        tagService.addTagListToCase(caseId, needTagCode, userId);
        //发送消息
        WOCase woCase = caseToolkit.getCaseById(caseId);
        CarSafeguardCaseContentModel content = getContentById(woCase.getContentId()).orElseThrow(() -> new KbException(KbCode.RESOURCE_NOT_FOUND));
        notifyService.programExposure(woCase, content);
    }

    public void caseProgramExposureCancel(String caseId, Integer userId) {
        Map<String, Long> tagCodeMap =
                tagService.getTagCodeByKeyIn(Arrays.asList(
                        CarSafeguardConstants.program_exposure_tag,
                        CarSafeguardConstants.program_exposure_show_tag,
                        CarSafeguardConstants.program_exposure_not_show_tag), true);
        //已播报不可取消
        if (tagService.isCaseTagged(caseId, tagCodeMap.get(CarSafeguardConstants.program_exposure_show_tag))) {
            throw new KbException(KbCode.ILLEGAL_STATE).r("已播报不可取消").li();
        }
        List<WOTag> deleteTag = tagService.getTagListByCodeIn(Arrays.asList(
                tagCodeMap.get(CarSafeguardConstants.program_exposure_tag),
                tagCodeMap.get(CarSafeguardConstants.program_exposure_not_show_tag)), true);
        tagService.removeTagListFromCase(caseId, deleteTag, userId);
    }

    /**
     * 给案件打上节目曝光已播出标签
     *
     * @param caseId
     * @param userId
     */
    public void caseProgramExposureShow(String caseId, Integer userId) {
        Map<String, Long> tagCodeMap =
                tagService.getTagCodeByKeyIn(Arrays.asList(
                        CarSafeguardConstants.program_exposure_tag,
                        CarSafeguardConstants.program_exposure_show_tag,
                        CarSafeguardConstants.program_exposure_not_show_tag), true);
        if (!tagService.isCaseTagged(caseId, tagCodeMap.get(CarSafeguardConstants.program_exposure_tag))) {
            throw new KbException(KbCode.ILLEGAL_STATE).r("案件需要先进行节目曝光").ld();
        }
        if (tagService.isCaseTagged(caseId, tagCodeMap.get(CarSafeguardConstants.program_exposure_show_tag))) {
            throw new KbException(KbCode.ILLEGAL_STATE).r("该案件已经标记为已播出").ld();
        }
        Map<Long, WOTag> tagMap = tagService.getTagMapByCodeIn(tagCodeMap.values(), true);
        //删除未播报标签,并且新增已播报标签
        tagService.removeTagFromCase(caseId, tagMap.get(tagCodeMap.get(CarSafeguardConstants.program_exposure_not_show_tag)), userId);
        tagService.addTagToCase(caseId, tagCodeMap.get(CarSafeguardConstants.program_exposure_show_tag), userId);
    }

    /**
     * 案件曝光标签全清空
     * @param caseId
     * @param userId
     */
    public void caseExposureCancel(String caseId, Integer userId) {
        Map<String, Long> tagCodeMap =
                tagService.getTagCodeByKeyIn(Arrays.asList(
                        CarSafeguardConstants.platform_exposure_tag,
                        CarSafeguardConstants.program_exposure_tag,
                        CarSafeguardConstants.program_exposure_show_tag,
                        CarSafeguardConstants.program_exposure_not_show_tag), true);

        List<WOTag> tagList = tagService.getTagListByCodeIn(tagCodeMap.values(), true);
        tagService.removeTagListFromCase(caseId, tagList, userId);
    }


    @Deprecated
    public Page<WOCaseSearchResult> searchCase(CSQuerySearchBaseModel model) {
        SearchRequest searchRequest = new SearchRequest();
        searchRequest.buildQuery()
                .and(true, () -> SearchQueryExpression.query(WOOpenSearchUtils.F_BIZ, WOBusiness.CAR_SAFEGUARD.name()))
                .and(model.getAnyResolver() != null, () -> SearchQueryExpression.query(WOOpenSearchUtils.F_RESOLVER_LIST, model.getAnyResolver()))
                .and(model.getClientUserId() != null, () -> SearchQueryExpression.query(WOOpenSearchUtils.F_USER_ID, model.getClientUserId().toString()))
                .and(model.getStates() != null, SearchQueryExpression.in(WOOpenSearchUtils.F_STATE, model.getStates()))
                .and(model.getCloseTypes() != null, SearchQueryExpression.in(WOOpenSearchUtils.F_CLOSE_TYPE, model.getCloseTypes()))
                .and(model.getBizState() != null, SearchQueryExpression.query(WOOpenSearchUtils.F_BIZ_STATE, model.getBizState()))
                .and(model.getOrigin() != null, SearchQueryExpression.query(WOOpenSearchUtils.F_ORIGIN, model.getOrigin()))
                .and(model.getSearchContent() != null, SearchQueryExpression.query(WOOpenSearchUtils.F_SEARCHABLE, model.getSearchContent()))
                .and(model.getOriginResolver() != null, SearchQueryExpression.query(WOOpenSearchUtils.F_ORIGIN_RESOLVER, model.getOriginResolver()))
                .and(model.getRangeSt() != null && model.getRangeEt() != null, () -> SearchQueryExpression.rangeInclude(WOOpenSearchUtils.F_CREATE_TIME, model.getRangeSt().toString(), model.getRangeEt().toString()))
                .and(model.getClientName() != null, SearchQueryExpression.query(WOOpenSearchUtils.F_CLIENT_NAME, model.getClientName()))
                .and(model.getClientMobile() != null, SearchQueryExpression.term(WOOpenSearchUtils.F_CLIENT_MOBILE_MD5, WOTagConsts.str2md5(model.getClientMobile())))
                .and(model.getContentType() != null, SearchQueryExpression.term(WOOpenSearchUtils.F_CONTENT_TYPE, model.getContentType()))
                .andList(model.getTagCodes() != null && !model.getTagCodes().isEmpty(), () -> SearchQueryExpression.query(WOOpenSearchUtils.F_TAGS, model.getTagCodes().stream().map(String::valueOf).collect(Collectors.toList())));

        SearchSort sort = searchRequest.buildSort();

        //基于标签索引排序
        if (model.needSortByTag()) {
            if (model.getSortTagStrategy() == null || model.getSortTagStrategy() == CSQuerySearchBaseModel.SORT_TAG_VALUE_DEFAULT) {
                sort.tagMatch(WOOpenSearchUtils.F_TAGS, model.getSortTag().stream().map(String::valueOf).collect(Collectors.toList()));
            } else if (model.getSortTagStrategy() == CSQuerySearchBaseModel.SORT_TAG_VALUE_ASC) {
                //权重分值 每个标签10分递增
                int defaultValue = 10;
                LinkedHashMap<String, Integer> kvPairs = new LinkedHashMap<>();
                for (int i = 0; i < model.getSortTag().size(); i++) {
                    Long tag = model.getSortTag().get(i);
                    kvPairs.put(tag.toString(), defaultValue * (i+1));
                }
                sort.tagMatchWithKvPairs(WOOpenSearchUtils.F_TAGS, kvPairs);
            }else if (model.getSortTagStrategy() == CSQuerySearchBaseModel.SORT_TAG_VALUE_DESC) {
                //权重分值 每个标签10分递减
                int size = model.getSortTag().size();
                int defaultValue = 10;
                LinkedHashMap<String, Integer> kvPairs = new LinkedHashMap<>();
                for (int i = 0; i < model.getSortTag().size(); i++) {
                    Long tag = model.getSortTag().get(i);
                    kvPairs.put(tag.toString(), defaultValue * (size - i));
                }
                sort.tagMatchWithKvPairs(WOOpenSearchUtils.F_TAGS, kvPairs);
            }
        }
        //基础属性排序, 优先级低于sortByTag, 编码上需要置于其后添加
        if (model.getSortBy() == null) {
            sort.fieldDesc(WOOpenSearchUtils.F_CREATE_TIME);
        } else if (model.getSortBy() == CSQuerySearchBaseModel.SORT_BY_CREATE_TIME_DESC) {
            sort.fieldDesc(WOOpenSearchUtils.F_CREATE_TIME);
        } else if (model.getSortBy() == CSQuerySearchBaseModel.SORT_BY_UPDATE_TIME_DESC) {
            sort.fieldDesc(WOOpenSearchUtils.F_UPDATE_TIME);
        }


        searchRequest.setFetchFields(WOOpenSearchUtils.getAllFields());
        Page<SearchResultModel.ResultItem> pageResult = searcherService.search(searchRequest, WOModelUtil.p(model.getPage(), model.getPageSize()));
        return pageResult.map(item -> WOOpenSearchMapper.toResult(item.getFields()));
    }


    // ------------------------------------

    @Override
    public WOBusiness getBusiness() {
        return WOBusiness.CAR_SAFEGUARD;
    }

    @Override
    public Optional<CarSafeguardCaseContentModel> getContentById(String contentId) {
        Optional<CarSafeguardCaseContent> contentOp = contentRepository.findByContentId(contentId);
        if (!contentOp.isPresent()) {
            return Optional.empty();
        }
        CarSafeguardContentHandler handler = getContentHandler(contentOp.get().getContentType());
        return Optional.of(handler.getContentById(contentId));
    }

    @Override
    public List<CarSafeguardCaseContentModel> getContentListByIdIn(Collection<String> contentIds) {
        //内容构建
        List<CarSafeguardCaseContent> contentList = contentRepository.findByContentIdIn(contentIds);
        Map<String, List<String>> type2Ids = contentList.stream().collect(
                Collectors.groupingBy(CarSafeguardCaseContent::getContentType,
                        Collectors.mapping(CarSafeguardCaseContent::getContentId, Collectors.toList())));

        List<CarSafeguardCaseContentModel> contentModels = new ArrayList<>();
        type2Ids.forEach((type, ids) -> {
            CarSafeguardContentHandler handler = getContentHandler(type);
            contentModels.addAll(handler.getContentById(ids));
        });
        return contentModels;
    }

    @Override
    public WOCaseContentBrief content2brief(CarSafeguardCaseContentModel content) {
        WOCaseContentBrief model = new WOCaseContentBrief();
        model.setId(content.obtainContentId());
        model.setType(content.getContentType());
        model.setContent(content.getContent());
        model.setVideo(content.getVideo());
        model.setImages(content.getImages());
        model.setOriginContentJson(GsonUtils.getGson().toJson(content));
        return model;
    }

    @Override
    public CarSafeguardCaseContentModel brief2Content(WOCaseContentBrief brief) {
        if (brief == null || brief.getOriginContentJson() == null) {
            return null;
        }
        CarSafeguardContentHandler handler = getContentHandler(brief.getType());
        return GsonUtils.getGson().fromJson(brief.getOriginContentJson(), handler.contentClass());
    }

    @Override
    public void checkContentACL(WOCase woCase, CarSafeguardCaseContentModel content, Integer userId, WOTeam team) {
        if (content == null || content.getPersonal() == null || content.getPersonal().isEmpty()) {
            return;
        }
        for (WOACLStringData data : content.getPersonal()) {
            if (data.getAcl() == null || data.getAcl().isEmpty()) {
                CarSafeguardConstants.PERSONAL_INFO_SETTER_MAP.get(data.getKey()).accept(content, data.getData());
                continue;
            }
            WOModelUtil.AccessResult result = WOModelUtil.testAccess(data.getAcl(), userId, team, () -> woCase);
            if (!result.isVisible()) {
                CarSafeguardConstants.PERSONAL_INFO_SETTER_MAP.get(data.getKey()).accept(content, null);
            } else if (!result.isReadable()) {
                CarSafeguardConstants.PERSONAL_INFO_SETTER_MAP.get(data.getKey()).accept(content, "****");
            } else {
                CarSafeguardConstants.PERSONAL_INFO_SETTER_MAP.get(data.getKey()).accept(content, data.getData());
            }
        }
        content.setPersonal(null);
    }

    @Override
    public List<WOACLStringData> getContentACL(String contentId) {
        CarSafeguardCaseContentModel content = getContentById(contentId).orElse(null);
        if (content == null || content.getPersonal() == null || content.getPersonal().isEmpty()) {
            return Collections.emptyList();
        }

        return content.getPersonal();
    }

    @Override
    public void afterEventHandler(WOEventContext context, WOEvent event) {
        //消息推送处理
        CarSafeguardCaseContentModel content = getContentById(context.getCase().getContentId()).orElseThrow(() -> new KbException(KbCode.RESOURCE_NOT_FOUND).r("未找到对应案件内容").ld());
        notifyService.eventNotify(context, event, content);
        //用户结案拒绝处理
        if (event.getOperation().equals(WOOperation.CLOSE_REFUSE.name())) {
            closeRefuseHandler(event.getCaseId(), event.getUserId());
        }
        //结案后删除倒计时
        if (event.getOperation().equals(WOOperation.CLOSE.name())) {
            clearTriggerHandler(event.getCaseId());
        }
    }

    @Override
    public String overwriteEventMessage(WOEventContext context, WOEventCommitter committer) {
        //尊重原始消息
        if (committer.getMessage() != null) {
            return committer.getMessage();
        }
        /**
         * 生成自定义业务消息
         */
        //受理
        if (committer.getEvent().getOperation().equals(WOOperation.ACCEPT.name())) {
            String resolverName = context.getNextResolver().getName();
            return "问题已转交给" + resolverName + ", 正在处理中, 请耐心等待回复";
        }
        //转到节目组结案
        if (committer.getEvent().getOperation().equals(WOOperation.CLOSE.name())) {
            if (StringUtils.isEqual(CarSafeguardConstants.CT_PROGRAM_HANDOVER_CLOSE, committer.getCloseType())) {
                return "感谢您对开吧的信任！您的问题转《我的汽车有话说》节目组，如果受理，一周之内联系您。";
            } else {
                return null;
            }
        }
        return null;
    }

    // -------------------------------------

    /**
     * 案件受理后计时器登记
     * @param matcher
     */
    private void acceptAfter(AfterEventMatcher matcher) {
        FieldMatcher<String> fieldMatcher =
                new FieldMatcher<>((WOEventContext context, WOEvent event) -> event.getOperation())
                        .is(WOOperation.ACCEPT.name());
        matcher.matches(fieldMatcher)
                .delayMark(woEvent -> CarSafeguardTriggerMarker.ACCEPT.name())
                .delayKeepNew("+72H")//72小时
                .delayThen(this::exposureTriggerConsumer);
    }

    /**
     * 用户结案拒绝,注册曝光倒计时
     * @param matcher
     */
    private void refuseAfter(AfterEventMatcher matcher) {
        FieldMatcher<String> fieldMatcher =
                new FieldMatcher<>((WOEventContext context, WOEvent event) -> event.getOperation())
                        .is(WOOperation.CLOSE_REFUSE.name());
        matcher.matches(fieldMatcher)
                .delayMark(woEvent -> CarSafeguardTriggerMarker.CLOSE_REFUSE.name())
                .delayKeepNew("+72H")//72小时
                .delayThen(this::exposureTriggerConsumer);
    }

    /**
     * 案件审核通过后计时器登记
     * @param matcher
     */
    private void auditAfter(AfterEventMatcher matcher) {
        FieldMatcher<String> fieldMatcher =
                new FieldMatcher<>((WOEventContext context, WOEvent event) -> event.getOperation())
                        .is(WOOperation.AUDIT.name());
        matcher.matches(fieldMatcher)
                .delayMark(woEvent -> CarSafeguardTriggerMarker.AUDIT.name())
                .delayKeepNew("+48H")//48小时
                .delayThen(this::exposureTriggerConsumer);
    }

    /**
     * 结案建议触发用户结案倒计时
     * @param matcher
     */
    private void closeSuggest(AfterEventMatcher matcher) {
        //用户结案倒计时加入属性标识 不在管理后台展示
        FieldMatcher<String> fieldMatcher =
                new FieldMatcher<>((WOEventContext context, WOEvent event) -> event.getOperation())
                        .is(WOOperation.CLOSE_SUGGEST.name());
        matcher.matches(fieldMatcher)
                .delayMark(woEvent -> CarSafeguardTriggerMarker.CLOSE_SUGGEST.name())
                .delayKeepNew("+48H")//48小时
                .delayThen(this::userTimeoutTriggerConsumer);
    }

    /**
     * 事件超时后的回调处理,给案件打上标签[平台曝光]
     *
     * @param trigger
     * @param event
     */
    private void exposureTriggerConsumer(WOCallbackTrigger trigger, WOEvent event) {
        Long tagCode = tagCacheService.getTagByKey(CarSafeguardConstants.platform_exposure_tag)
                .map(WOTag::getCode)
                .orElse(null);
        if (tagCode == null) {
            return;
        }
        tagService.addTagToCase(event.getCaseId(), tagCode, WOConsts.SYSTEM_USER_ID);
        WOCase woCase = caseToolkit.getCaseById(event.getCaseId());
        eventListener.reply(woCase, WOConsts.SYSTEM_USER_ID, WOIdentity.SYSTEM.asTeam(), null, "您的投诉因商家未及时处理已在平台曝光");
        //清空计时器
        clearTriggerHandler(event.getCaseId());
        //发送消息
        CarSafeguardCaseContentModel content = getContentById(woCase.getContentId()).orElseThrow(() -> new KbException(KbCode.RESOURCE_NOT_FOUND));
        notifyService.platformExposure(woCase, content);
    }

    /**
     * 用户对于结案建议超时处理器,自动结案
     * @param trigger
     * @param event
     */
    private void userTimeoutTriggerConsumer(WOCallbackTrigger trigger, WOEvent event) {
        WOCase woCase = caseToolkit.getCaseById(event.getCaseId());
        eventListener.close(
                woCase, WOConsts.SYSTEM_USER_ID, WOIdentity.SYSTEM.asTeam(),
                WOCloseReason.USER_TIMEOUT.name(), "结案建议处理超时,自动结案","您投诉的问题未在48小时内确认,系统自动结案", null);
    }


    /**
     * 用户拒绝结案建议后续处理
     * 如果拒绝次数>=2,则将案件进行平台曝光
     *
     * @param caseId
     * @param userId
     */
    private void closeRefuseHandler(String caseId, Integer userId) {
        String cacheKey = close_refuse_cache_key + caseId;
        Long refuseTimes = redisTemplate.opsForValue().increment(cacheKey, 1);
        redisTemplate.expire(cacheKey, Duration.ofDays(30));
        if (refuseTimes == null || refuseTimes < 2L) {
            return;
        }
        //进行平台曝光
        Long tagCode = tagCacheService.getTagByKey(CarSafeguardConstants.platform_exposure_tag)
                .map(WOTag::getCode)
                .orElse(null);
        tagService.addTagToCase(caseId, tagCode, userId);
        clearTriggerHandler(caseId);
    }

    /**
     * 删除计时器
     * 计时器模块:
     * 1.基础计时器
     * 2.暂停计时器
     * 3.延期申请计时器
     * @param caseId
     */
    private void clearTriggerHandler(String caseId) {
        //删除倒计时
        triggerRepository.deleteByCaseId(caseId);
        //删除暂停记录
        triggerPauseRepository.deleteByCaseId(caseId);
    }

    private void transferContentToTag(CarSafeguardCaseCreateModel model, CarSafeguardCaseContentModel content) {
        CarSafeguardContentHandler contentHandler = getContentHandler(content.getContentType());
        List<Long> tags = contentHandler.obtainTag(model);
        if (model.getTagCodes() == null) {
            model.setTagCodes(new ArrayList<>());
        }
        model.getTagCodes().addAll(tags);
    }

    private void verifyTag(List<Long> tagCodes) {
        if (tagCodes == null || tagCodes.isEmpty()) {
            return;
        }
        Map<Long, WOTag> tagMap = tagCacheService.getTagMapByCodes(tagCodes);
        for (Long tagCode : tagCodes) {
            WOTag tag = tagMap.get(tagCode);
            if (tag == null) {
                throw new KbException(KbCode.ILLEGAL_ARGUMENT).r("标签不存在:" + tagCode).li();
            }
        }
    }


    private static void verifyBaseCreateModel(CarSafeguardCaseCreateModel model, boolean manager) {
        if (StringUtils.isEmpty(model.getContent())) {
            throw new KbException(KbCode.ILLEGAL_ARGUMENT, "missing content").r("请输入问题描述").li();
        }
        long contentLength = model.getContent().length();
        if (!manager && contentLength < CarSafeguardConstants.CONTENT_MIN_TEXT) {
            throw new KbException(KbCode.ILLEGAL_ARGUMENT, "content exceed min length").r("问题描述至少需要20个字").li();
        }
        if (contentLength > CarSafeguardConstants.CONTENT_MAX_TEXT) {
            throw new KbException(KbCode.ILLEGAL_ARGUMENT, "content exceed max length").r("请将问题描述缩减至1000字以内").li();
        }
        int imageSize = model.getImages() == null ? 0 : model.getImages().size();
        if (imageSize > CarSafeguardConstants.CONTENT_MAX_IMAGE) {
            throw new KbException(KbCode.ILLEGAL_ARGUMENT, "images exceed max count").r("最多上传5张图片").li();
        }
    }

    private static CarSafeguardTriggerPause mapTriggerPause(WOCallbackTrigger trigger, WOCase woCase, Integer userId) {
        long pauseMils = trigger.getFireTime() - System.currentTimeMillis();
        CarSafeguardTriggerPause pause = new CarSafeguardTriggerPause();
        pause.setCaseId(trigger.getCaseId());
        pause.setPauseLessMils(pauseMils);
        pause.setPauseUserId(userId);
        pause.setCreateTime(System.currentTimeMillis());
        pause.setTrigger(trigger);
        return pause;
    }

    private static WOCallbackTrigger mapTriggerResume(CarSafeguardTriggerPause pause) {
        WOCallbackTrigger trigger = pause.getTrigger();
        long expireTimeDiff = trigger.getExpireTime() - trigger.getFireTime();
        long now = System.currentTimeMillis();
        long fireTime = now + pause.getPauseLessMils();
        long expireTime = fireTime + expireTimeDiff;

        trigger.setId(null);
        trigger.setFireTime(fireTime);
        trigger.setExpireTime(expireTime);
        trigger.setUpdateTime(now);
        return trigger;
    }

    private static CarSafeguardTriggerPauseHis mapTriggerPauseHis(CarSafeguardTriggerPause pause, Integer userId) {
        CarSafeguardTriggerPauseHis his = new CarSafeguardTriggerPauseHis();
        his.setCaseId(pause.getCaseId());
        his.setPauseLessMils(pause.getPauseLessMils());
        his.setPauseUserId(pause.getPauseUserId());
        his.setPauseTime(pause.getCreateTime());
        his.setResumeUserId(userId);
        his.setCreateTime(System.currentTimeMillis());
        return his;
    }

    private static List<CarSafeguardTriggerModel> map2TriggerList(List<String> caseIds, List<WOCallbackTrigger> triggers, List<CarSafeguardTriggerPause> pauseList) {
        if (caseIds == null || caseIds.isEmpty()) {
            return Collections.emptyList();
        }
        Map<String, CarSafeguardTriggerPause> pauseMap = pauseList.stream()
                .collect(Collectors.toMap(CarSafeguardTriggerPause::getCaseId, Function.identity(), (o1, o2) -> o1));
        Map<String, WOCallbackTrigger> triggerMap = triggers.stream()
                .collect(Collectors.toMap(WOCallbackTrigger::getCaseId, Function.identity(), (o1, o2) -> o1));
        return caseIds.stream()
                .map(caseId -> map2Trigger(caseId, triggerMap.get(caseId), pauseMap.get(caseId)))
                .collect(Collectors.toList());
    }


    private static CarSafeguardTriggerModel map2Trigger(String caseId, WOCallbackTrigger trigger, CarSafeguardTriggerPause pause) {
        CarSafeguardTriggerModel.CarSafeguardTriggerModelBuilder builder = CarSafeguardTriggerModel.builder().caseId(caseId);
        if (pause != null) {
            builder.paused(true);
            builder.pauseLessMils(pause.getPauseLessMils());
        }
        if (trigger != null) {
            builder.fireTime(trigger.getFireTime());
            builder.triggerId(trigger.getId());
            //用户倒计时B端不展示
            if (StringUtils.isEqual(trigger.getMark(), CarSafeguardTriggerMarker.CLOSE_SUGGEST.name())) {
                builder.tobHidden(true);
            }
        }
        return builder.build();
    }
}
