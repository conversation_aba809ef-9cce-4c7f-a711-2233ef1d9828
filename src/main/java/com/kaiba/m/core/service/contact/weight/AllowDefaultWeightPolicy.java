package com.kaiba.m.core.service.contact.weight;


import com.kaiba.lib.base.constant.contact.ContactState;
import com.kaiba.m.core.domain.contact.Contact;
import com.kaiba.m.core.domain.contact.ContactGroup;
import org.springframework.stereotype.Component;

/**
 * author: lyux
 * date: 2021-12-13
 */
@Component
public class AllowDefaultWeightPolicy implements IWeightPolicy {

    @Override
    public long priority(ContactGroup group) {
        if (group.getAllowDefault() != null && group.getAllowDefault()) {
            return 10000;
        } else {
            return 0;
        }
    }

    @Override
    public long calculateWeight(Contact contact) {
        long refreshTime = contact.getRefreshTime() == null ? contact.getCreateTime() : contact.getRefreshTime();
        long weightByTime = refreshTime / 1000; // 减去基准时间: 2020-01-01, 单位秒. 得到 10^10 位.
        long weightByState = weightByState(contact.getState()); // 得到 10^12 位.
        long weightByIsDefault = (contact.getIsDefault() != null && contact.getIsDefault()) ? 2 * 10^13 : 0;// 得到 10^13 位
        return weightByIsDefault + weightByState + weightByTime;
    }

    private static long weightByState(Integer state) {
        if (state == null) {
            return 0;
        } else if (state == ContactState.ENABLED.getValue()) {
            return 9 * 10^12;
        } else if (state == ContactState.NEED_COMPLEMENT.getValue()) {
            return 8 * 10^12;
        } else if (state == ContactState.DRAFT.getValue()) {
            return 7 * 10^12;
        } else if (state == ContactState.DISABLED.getValue()) {
            return 5 * 10^12;
        } else {
            return 0;
        }
    }

}
