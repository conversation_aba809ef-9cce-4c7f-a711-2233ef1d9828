package com.kaiba.m.core.service.appcomponent.icongrid;

import com.kaiba.lib.base.domain.appwidget.icongrid.IconGridDataModel;
import org.springframework.data.domain.Sort;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;

/**
 * author: lyux
 * date: 2023-07-12
 */
public class AppIconGridConst {

    public static final int MAX_SIGNED_PER_INSTANCE = 20;

    public static final int MAX_ONLINE_PER_INSTANCE = 20;

    public static final Sort defaultSort = Sort.by(Sort.Order.asc("idx"), Sort.Order.desc("createTime"));

    // ----------------------------------------------------------

    static final String FAKE_ID = "app_icon_grid_fake_id";

    static final String ME_PAGE_DEFAULT_ID = "app_icon_grid_me_page_default_id";

    static final List<IconGridDataModel> mePageDefaultData = new ArrayList<>(4);

    static {
        //我的圈子
        IconGridDataModel pageMyCircle = new IconGridDataModel();
        pageMyCircle.setIdx(0);
        pageMyCircle.setUrl("https://static.kaiba315.com.cn/sys/icon/grid/wode/myquanzi.png?imageView2/2/w/1080/h/1080");
        pageMyCircle.setFormat("PNG");
        pageMyCircle.setTitle("我的圈子");
        pageMyCircle.setAction("pageMyCircle");
        pageMyCircle.setActionParams(new HashMap<>());
        //我的红包
        IconGridDataModel pageRedWeb = new IconGridDataModel();
        pageRedWeb.setIdx(1);
        pageRedWeb.setUrl("https://static.kaiba315.com.cn/sys/icon/grid/wode/myhongbao.png?imageView2/2/w/1080/h/1080");
        pageRedWeb.setFormat("PNG");
        pageRedWeb.setTitle("我的红包");
        pageRedWeb.setAction("pageWeb");
        HashMap<String, Object> pageRedWebParams = new HashMap<>();
        pageRedWebParams.put("requireLogin", true);
        pageRedWebParams.put("decorateUrl", true);
        pageRedWebParams.put("url", "https://page.kaiba315.com.cn/app_page/Red/redList.html");
        pageRedWeb.setActionParams(pageRedWebParams);
        //我要合作
        IconGridDataModel pageHeZuo = new IconGridDataModel();
        pageHeZuo.setIdx(2);
        pageHeZuo.setUrl("https://static.kaiba315.com.cn/sys/icon/grid/wode/hezuo.png?imageView2/2/w/1080/h/1080");
        pageHeZuo.setFormat("PNG");
        pageHeZuo.setTitle("我要合作");
        pageHeZuo.setAction("pageWeb");
        HashMap<String, Object> pageHeZuoParams = new HashMap<>();
        pageHeZuoParams.put("requireLogin", true);
        pageHeZuoParams.put("decorateUrl", true);
        pageHeZuoParams.put("url", "http://api.kaiba315.com.cn/Hezuo");
        pageHeZuo.setActionParams(pageHeZuoParams);

        mePageDefaultData.add(pageMyCircle);
        mePageDefaultData.add(pageRedWeb);
        mePageDefaultData.add(pageHeZuo);
    }

}
