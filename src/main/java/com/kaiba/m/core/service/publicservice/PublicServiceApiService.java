package com.kaiba.m.core.service.publicservice;

import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.LoadingCache;
import com.kaiba.lib.base.assembler.Mapper;
import com.kaiba.lib.base.constant.publicservice.GroupType;
import com.kaiba.lib.base.constant.publicservice.ServiceState;
import com.kaiba.lib.base.domain.publicservice.PublicServiceApiModel;
import com.kaiba.lib.base.domain.publicservice.PublicServiceGroupModel;
import com.kaiba.lib.base.domain.publicservice.PublicServiceItemModel;
import com.kaiba.lib.base.domain.publicservice.PublicServiceModel;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/01/09 17:20
 */
@Slf4j
@Service
public class PublicServiceApiService {

    private static final String DEFAULT_INSTANCE_KEY = "hz_app_main_page";
    private static final String SERVICE_BANNER_KEY = "hz_service_page_banner";
    private final PublicServiceService publicServiceService;

    private final LoadingCache<String, CacheModel> mainPageCache = Caffeine.newBuilder()
            .expireAfterWrite(Duration.ofMinutes(15))
            .refreshAfterWrite(Duration.ofSeconds(59))
            .build(this::getPublicServiceListFromDb);

    private final LoadingCache<String, PublicServiceModel> groupCache = Caffeine.newBuilder()
            .expireAfterWrite(Duration.ofMinutes(15))
            .refreshAfterWrite(Duration.ofSeconds(59))
            .build(this::getPublicServiceGroupFromDb);

    private final LoadingCache<String, String> bannerKeyCache = Caffeine.newBuilder()
            .expireAfterWrite(Duration.ofMinutes(15))
            .refreshAfterWrite(Duration.ofSeconds(59))
            .build(this::getBannerKeyByInstanceKey);

    public PublicServiceApiService(PublicServiceService publicServiceService) {
        this.publicServiceService = publicServiceService;
    }

    public PublicServiceApiModel getPublicServiceList(String instanceKey) {
        if(instanceKey == null) {
            instanceKey = DEFAULT_INSTANCE_KEY;
        }

        CacheModel cacheModel = mainPageCache.get(instanceKey);
        if(cacheModel == null) {
            return new PublicServiceApiModel();
        }

        return cacheModel.getApiModel();
    }

    public PublicServiceModel getPublicServiceListByGroupId(String groupId) {
        CacheModel cacheModel = mainPageCache.get(DEFAULT_INSTANCE_KEY);
        if(cacheModel == null) {
            return new PublicServiceModel();
        }

        return cacheModel.getGroupMap().getOrDefault(groupId, new PublicServiceModel());
    }

    public PublicServiceModel getPublicServiceListFrontend(String groupId) {
        PublicServiceModel model = groupCache.get(groupId);
        return model == null ? new PublicServiceModel() : model;
    }

    public List<PublicServiceModel> searchService(String instanceKey, String queryParam) {
        CacheModel cacheModel = mainPageCache.get(instanceKey);
        if(cacheModel == null || cacheModel.getGroupMap() == null) {
            return Collections.emptyList();
        }
        List<PublicServiceModel> list = new ArrayList<>();
        Map<String, PublicServiceModel> groupMap = cacheModel.getGroupMap();
        for(PublicServiceModel model : groupMap.values()) {
            if(model.getType() == GroupType.RECOMMEND.getValue() || model.getType() == GroupType.MAIN_GROUP.getValue()) {
                List<PublicServiceItemModel> items = model.getItems().stream().filter(i -> i.getName() != null && i.getName().contains(queryParam)).collect(Collectors.toList());
                if(!items.isEmpty()) {
                    PublicServiceModel newGroup = Mapper.map(model, PublicServiceModel.class);
                    newGroup.setItems(items);
                    list.add(newGroup);
                }
            }
        }
        return list;
    }

    // ----------------------------------------------------

    private CacheModel getPublicServiceListFromDb(String instanceKey) {
        List<PublicServiceGroupModel> serviceGroups = publicServiceService.getServiceGroupListByInstanceKey(instanceKey, null, null, ServiceState.ENABLE.getValue(), 1, 100).getContent();

        List<String> groupIds = serviceGroups.stream().map(PublicServiceGroupModel::getId).collect(Collectors.toList());
        Map<String, List<PublicServiceItemModel>> groupItemMap = publicServiceService.getServiceGroupItemListByGroupIdIn(groupIds).stream()
                .filter(t -> t.getState() == ServiceState.ENABLE.getValue())
                .sorted(Comparator.comparing(PublicServiceItemModel::getOrder))
                .collect(Collectors.groupingBy(PublicServiceItemModel::getGroupId));

        Map<Integer, List<PublicServiceModel>> serviceMap = serviceGroups.stream()
                .map(t -> transGroup2Model(t, groupItemMap.getOrDefault(t.getId(), Collections.emptyList())))
                .collect(Collectors.groupingBy(PublicServiceModel::getType));

        CacheModel cacheModel = new CacheModel();
        cacheModel.setApiModel(assembleApiModel(instanceKey, serviceMap));
        cacheModel.setGroupMap(serviceMap.values().stream().flatMap(Collection::stream).collect(Collectors.toMap(PublicServiceModel::getGroupId, o -> o, (o1, o2) -> o1)));
        return cacheModel;
    }

    private PublicServiceModel getPublicServiceGroupFromDb(String groupId) {
        PublicServiceGroupModel groupModel = publicServiceService.getServiceGroupByGroupId(groupId);
        List<PublicServiceItemModel> itemList = publicServiceService.getServiceGroupItemListByGroupIdIn(Collections.singleton(groupId))
            .stream().filter(t -> t.getState() == ServiceState.ENABLE.getValue()).collect(Collectors.toList());
        return transGroup2Model(groupModel, itemList);
    }

    private String getBannerKeyByInstanceKey(String instanceKey) {
        String bannerKey = publicServiceService.getServiceInstance(instanceKey).getBannerKey();
        return bannerKey == null ? SERVICE_BANNER_KEY : bannerKey;
    }

    // ---------------------------------------------------

    private PublicServiceApiModel assembleApiModel(String instanceKey, Map<Integer, List<PublicServiceModel>> serviceMap) {
        PublicServiceApiModel model = new PublicServiceApiModel();
        List<PublicServiceModel> recommendGroups = serviceMap.getOrDefault(GroupType.RECOMMEND.getValue(), Collections.emptyList());
        if(!recommendGroups.isEmpty()) {
            PublicServiceModel recommendGroup = recommendGroups.get(0);
            model.setRecommendItems(getItemList(recommendGroup.getShowLimit(), recommendGroup.getItems()));
        }
        model.setFrequentItems(serviceMap.getOrDefault(GroupType.FREQUENTLY.getValue(), Collections.emptyList()).stream().map(PublicServiceModel::getItems).findFirst().orElse(Collections.emptyList()));
        model.setList(serviceMap.getOrDefault(GroupType.MAIN_GROUP.getValue(), Collections.emptyList()));
        model.setBannerKey(bannerKeyCache.get(instanceKey));
        return model;
    }

    private List<PublicServiceItemModel> getItemList(Integer limit, List<PublicServiceItemModel> items) {
        if(items == null) {
            return Collections.emptyList();
        }
        int min = limit == -1 ? items.size() : Math.min(limit, items.size());
        return items.subList(0, min);
    }

    private PublicServiceModel transGroup2Model(PublicServiceGroupModel group, List<PublicServiceItemModel> items) {
        items.forEach(t -> t.setGroupName(group.getName()));
        PublicServiceModel model = Mapper.map(group, PublicServiceModel.class);
        model.setGroupId(group.getId());
        model.setItems(items);
        return model;
    }

    @Data
    @ToString
    @NoArgsConstructor
    private static class CacheModel {
        /** 前端首页数据 */
        private PublicServiceApiModel apiModel;
        /** 服务分组数据 */
        private Map<String, PublicServiceModel> groupMap;
    }
}
