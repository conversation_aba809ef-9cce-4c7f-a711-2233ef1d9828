package com.kaiba.m.core.service.note.comment;

import com.kaiba.lib.base.constant.note.NoteState;
import com.kaiba.lib.base.util.GsonUtils;
import com.kaiba.m.core.domain.note.NoteComment;
import com.kaiba.m.core.service.note.NoteCacheConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.connection.RedisStringCommands;
import org.springframework.data.redis.connection.StringRedisConnection;
import org.springframework.data.redis.core.RedisCallback;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.types.Expiration;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * author: lyux
 * date: 19-6-5
 */
@Slf4j
@Service
public class NoteCommentCacheByIdService {

    private final StringRedisTemplate stringRedisTemplate;

    public NoteCommentCacheByIdService(StringRedisTemplate stringRedisTemplate) {
        this.stringRedisTemplate = stringRedisTemplate;
    }

    void setComment(NoteComment comment) {
        if (!isValidComment(comment)) {
            return;
        }
        String cacheKey = id2key(comment.getId());
        String cacheData = GsonUtils.getGson().toJson(comment);
        stringRedisTemplate.opsForValue().set(
                cacheKey, cacheData, NoteCacheConfig.COMMENT_BY_ID_EXPIRE.getSeconds(), TimeUnit.SECONDS);
    }

    void setCommentList(List<NoteComment> comments) {
        if (null == comments || comments.size() == 0) {
            return;
        }
        List<NoteComment> validatedCommentList = comments.stream()
                .filter(NoteCommentCacheByIdService::isValidComment)
                .collect(Collectors.toList());
        stringRedisTemplate.executePipelined((RedisCallback<Object>) connection -> {
            StringRedisConnection conn = (StringRedisConnection) connection;
            for (NoteComment comment : validatedCommentList) {
                String cacheKey = id2key(comment.getId());
                String cacheData = GsonUtils.getGson().toJson(comment);
                conn.set(cacheKey, cacheData,
                        Expiration.from(NoteCacheConfig.COMMENT_BY_ID_EXPIRE),
                        RedisStringCommands.SetOption.UPSERT);
            }
            return null;
        });
    }

    Optional<NoteComment> getComment(String commentId) {
        if (null == commentId) {
            return Optional.empty();
        }
        if (NoteCacheConfig.CACHE_DEBUG) {
            log.info("[cache_debug][cache] get comment by id: " + commentId);
        }
        String cacheKey = id2key(commentId);
        String cacheData = stringRedisTemplate.opsForValue().get(cacheKey);
        if (cacheData != null) {
            return Optional.ofNullable(GsonUtils.toModelIgnoreError(cacheData, NoteComment.class));
        } else {
            return Optional.empty();
        }
    }

    List<NoteComment> getCommentIn(List<String> commentIds) {
        if (NoteCacheConfig.CACHE_DEBUG) {
            log.info("[cache_debug][cache] get comment by id in: " + commentIds);
        }
        if (null == commentIds || commentIds.size() == 0) {
            return Collections.emptyList();
        }
        List<String> cacheKeys = commentIds.stream()
                .map(NoteCommentCacheByIdService::id2key)
                .collect(Collectors.toList());
        List<String> cacheDataList = stringRedisTemplate.opsForValue().multiGet(cacheKeys);
        if (null == cacheDataList) {
            return Collections.emptyList();
        }
        return cacheDataList.stream()
                .map(data -> GsonUtils.toModelIgnoreError(data, NoteComment.class))
                .collect(Collectors.toList());
    }

    void updateState(NoteComment comment, NoteState state) {
        if (isValidComment(comment)) {
            comment.setState(state.getValue());
            setComment(comment);
        }
    }

    private static String id2key(String commentId) {
        return NoteCacheConfig.COMMENT_BY_ID_KEY + commentId;
    }

    private static boolean isValidComment(NoteComment comment) {
        return comment != null && comment.getId() != null && comment.getContent() != null;
    }

    void invalid(String commentId) {
        String cacheKey = id2key(commentId);
        stringRedisTemplate.delete(cacheKey);
    }
}
