package com.kaiba.m.core.service.media;

import com.fasterxml.jackson.annotation.JsonAlias;
import com.google.gson.annotations.SerializedName;
import lombok.Data;

/**
 * <AUTHOR>
 * @version AliyunMediaCallbackEvent, v0.1 2024/1/10 14:22 daopei Exp $
 **/
@Data
public class AliyunMediaCallbackEvent {

    @SerializedName(value = "status",alternate = {"Status"})
    private String status;
    @SerializedName(value = "eventTime", alternate = {"EventTime"})
    private String eventTime;
    @SerializedName(value = "eventType", alternate = {"EventType"})
    private String eventType;
    @SerializedName(value = "videoId", alternate = {"VideoId"})
    private String videoId;
}
