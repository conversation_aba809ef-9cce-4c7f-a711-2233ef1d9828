package com.kaiba.m.core.service.wx;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 微信开放平台配置属性,支持动态刷新
 * <AUTHOR>
 * @version WxOpenProperties, v0.1 2024/6/12 10:25 daopei Exp $
 **/
@Data
@Component
@RefreshScope
public class WxOpenProperties {


    @Value("${wx.open.msg.notice.register-site-ids:}")
    private List<Integer> registerSiteIds;
}
