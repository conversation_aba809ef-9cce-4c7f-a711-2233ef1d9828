package com.kaiba.m.core.service.appcomponent.icongrid;

import com.kaiba.lib.base.assembler.Mapper;
import com.kaiba.lib.base.constant.common.KbImageFormat;
import com.kaiba.lib.base.domain.appwidget.icongrid.IconGridDataCreateModel;
import com.kaiba.lib.base.domain.appwidget.icongrid.IconGridDataModel;
import com.kaiba.lib.base.domain.appwidget.icongrid.IconGridDataUpdateModel;
import com.kaiba.lib.base.domain.appwidget.icongrid.IconGridInstanceModel;
import com.kaiba.lib.base.lang.verifier.F;
import com.kaiba.lib.base.lang.verifier.Verifier;
import com.kaiba.lib.base.lang.verifier.VerifierBuilder;
import com.kaiba.lib.base.util.appaction.AppActionUtils;
import com.kaiba.m.core.domain.appwidget.icongrid.IconGrid;
import com.kaiba.m.core.domain.appwidget.icongrid.IconGridInstance;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version AppIconGridModelHelper, v0.1 2023/7/17 19:03 daopei Exp $
 **/
@Component
public class AppIconGridModelHelper {

    public static IconGridInstance model2Instance(IconGridInstanceModel model) {
        return Mapper.map(model, IconGridInstance.class);
    }

    public static IconGridInstanceModel instance2Model(IconGridInstance instance) {
        return Mapper.map(instance, IconGridInstanceModel.class);
    }

    public static IconGridDataModel data2Model(IconGrid data) {
        return Mapper.map(data, IconGridDataModel.class);
    }

    public static List<IconGridDataModel> data2ModelList(List<IconGrid> dataList) {
        return dataList.stream().map(AppIconGridModelHelper::data2Model).collect(Collectors.toList());
    }

    public static IconGrid createModel2Data(IconGridDataCreateModel model) {
        return Mapper.map(model, IconGrid.class);
    }


    public static IconGridDataModel data2ModelForClient(IconGrid iconGrid) {
        if (iconGrid == null) {
             return null;
        }
        IconGridDataModel dataModel = new IconGridDataModel();
        dataModel.setId(iconGrid.getId());
        dataModel.setIdx(iconGrid.getIdx());
        dataModel.setTitle(iconGrid.getTitle());
        dataModel.setUrl(iconGrid.getUrl());
        dataModel.setAction(iconGrid.getAction());
        dataModel.setActionParams(iconGrid.getActionParams());
        dataModel.setFormat(iconGrid.getFormat());
        return dataModel;
    }

    static final Verifier<IconGridInstanceModel> INSTANCE_VERIFIER = new VerifierBuilder<IconGridInstanceModel>().defaultOrElseThrow()
            .and(F.str(IconGridInstanceModel::getKey).notEmpty())
            .and(F.intF(IconGridInstanceModel::getSiteId).notNull())
            .and(F.intF(IconGridInstanceModel::getLoadType).notNull())
            .and(F.intF(IconGridInstanceModel::getBackendFilterType).notNull())
            .and(F.str(IconGridInstanceModel::getName).notEmpty())
            .create();

    static final Verifier<IconGridDataCreateModel> DATA_CREATE_VERIFIER = new VerifierBuilder<IconGridDataCreateModel>().defaultOrElseThrow()
            .and(F.str(IconGridDataCreateModel::getInstanceId).isObjectId())
            .and(F.str(IconGridDataCreateModel::getUrl).isUrl())
            .and(F.str(IconGridDataCreateModel::getFormat).enums(KbImageFormat.values()))
            .and(AppActionUtils::verifyAction)
            .create();

    static final Verifier<IconGridDataUpdateModel> DATA_UPDATE_VERIFIER = new VerifierBuilder<IconGridDataUpdateModel>().defaultOrElseThrow()
            .and(F.str(IconGridDataUpdateModel::getId).isObjectId())
            .and(F.str(IconGridDataUpdateModel::getUrl).isUrl())
            .and(F.str(IconGridDataUpdateModel::getFormat).enums(KbImageFormat.values()))
            .and(AppActionUtils::verifyAction)
            .create();
}
