package com.kaiba.m.core.service.user.blacklist;

import com.kaiba.lib.base.cache.LazyExpireCache;
import com.kaiba.m.core.repository.user.UserBlacklistRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.List;
import java.util.Set;
import java.util.concurrent.TimeUnit;

/**
 * author: lyux
 * date: 2022-03-09
 */
@Slf4j
@Component
public class BlacklistCacheFactory implements IBlacklistCache {

    private final UserBlacklistRepository blacklistRepository;
    private final LazyBlacklistCaffeineCache lazyBlacklistCaffeineCache;
    private final PreloadBlacklistCache preloadBlacklistCache;
    private final LazyExpireCache<Long> dataCountCache;

    public BlacklistCacheFactory(
            LazyBlacklistCaffeineCache lazyBlacklistCaffeineCache,
            PreloadBlacklistCache preloadBlacklistCache,
            UserBlacklistRepository blacklistRepository,
            ThreadPoolTaskScheduler taskExecutor
    ) {
        this.blacklistRepository = blacklistRepository;
        this.lazyBlacklistCaffeineCache = lazyBlacklistCaffeineCache;
        this.preloadBlacklistCache = preloadBlacklistCache;
        this.dataCountCache = new LazyExpireCache.Builder<Long>()
                .setExpireTime(TimeUnit.MINUTES.toMillis(30))
                .setDataValidatorNonNull()
                .setSupplier(blacklistRepository::countAllBy)
                .setExecutor(taskExecutor)
                .create();
    }

    @PostConstruct
    @Override
    public void initializeCache() {
        dataCountCache.setData(blacklistRepository.countAllBy());
        chooseCache().initializeCache();
    }

    @Override
    public Set<Integer> getBlacklist(Integer userId) {
        return chooseCache().getBlacklist(userId);
    }

    @Override
    public boolean isInBlacklist(Integer userId, Integer targetUserId) {
        return chooseCache().isInBlacklist(userId, targetUserId);
    }

    @Override
    public void addToBlacklist(Integer userId, Integer targetUserId) {
        chooseCache().addToBlacklist(userId, targetUserId);
    }

    @Override
    public void removeFromBlacklist(Integer userId, Integer targetUserId) {
        chooseCache().removeFromBlacklist(userId, targetUserId);
    }

    @Override
    public void updateBlacklist(Integer userId, List<Integer> blacklistUserIds) {
        chooseCache().updateBlacklist(userId, blacklistUserIds);
    }

    @Override
    public void invalidBlacklist(Integer userId) {
        chooseCache().invalidBlacklist(userId);
    }

    private IBlacklistCache chooseCache() {
        Long count = dataCountCache.getData(true);
        if (count == null || count > UserBlacklistConsts.SWITCH_CACHE_THRESHOLD) {
            return lazyBlacklistCaffeineCache;
        } else {
            return preloadBlacklistCache;
        }
    }

}
