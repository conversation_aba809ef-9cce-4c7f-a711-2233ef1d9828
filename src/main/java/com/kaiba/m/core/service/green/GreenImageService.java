package com.kaiba.m.core.service.green;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.aliyuncs.DefaultAcsClient;
import com.aliyuncs.IAcsClient;
import com.aliyuncs.green.model.v20180509.ImageSyncScanRequest;
import com.aliyuncs.http.FormatType;
import com.aliyuncs.http.HttpResponse;
import com.aliyuncs.http.MethodType;
import com.aliyuncs.http.ProtocolType;
import com.aliyuncs.profile.DefaultProfile;
import com.aliyuncs.profile.IClientProfile;
import com.kaiba.lib.base.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2021/6/15
 */
@Service
@Slf4j
public class GreenImageService {

    private static final String ALI_GREEN_SUGGESTION_PASS = "pass";
    private static final String ALI_GREEN_SUGGESTION_REVIEW = "review";
    private static final String ALI_GREEN_SUGGESTION_BLOCK = "block";

    @Value("${ali.green.access_key_id}")
    private String accessKeyId;

    @Value("${ali.green.access_key_secret}")
    private String accessKeySecret;

    @Value("${ali.green.region_id}")
    private String accessRegionId;

    private IAcsClient recognitionClient;

    private static final List<String> SCAN_SCENES = Arrays.asList("porn", "terrorism", "ad", "live", "qrcode", "logo");

    @PostConstruct
    public void init() {
        IClientProfile profile = DefaultProfile.getProfile(accessRegionId, accessKeyId, accessKeySecret);
        recognitionClient = new DefaultAcsClient(profile);
    }

    /**
     *
     * @param urls
     * urls 中存在无法访问的文件或访问超时，返回false
     * @return
     */
    public boolean multiScan(List<String> urls) {
        if (urls == null || urls.size() == 0) {
            return true;
        }

        HttpResponse httpResponse;
        try {
            httpResponse = recognitionClient.doAction(createImageSyncScanRequest(urls));
        } catch (Exception e) {
            log.error("GreenImageService image scan fail for " + urls + " due to exception", e);
            return false;
        }

        if (!httpResponse.isSuccess()) {
            log.error("GreenImageService image scan fail for " + urls + ", http response: " +
                    org.apache.commons.codec.binary.StringUtils.newStringUtf8(httpResponse.getHttpContent()));
            return false;
        }

        JSONObject scrResponse = JSON.parseObject(org.apache.commons.codec.binary.StringUtils.newStringUtf8(httpResponse.getHttpContent()));
        int requestCode = scrResponse.getIntValue("code");
        JSONArray taskResults = scrResponse.getJSONArray("data");
        if (200 != requestCode) {
            log.error("the whole image scan request failed. response:" + JSON.toJSONString(scrResponse));
            return false;
        }

        for (Object taskResult : taskResults) {
            int taskCode = ((JSONObject) taskResult).getIntValue("code");
            JSONArray sceneResults = ((JSONObject) taskResult).getJSONArray("results");
            if (200 == taskCode) {
                for (Object sceneResult : sceneResults) {
                    String scene = ((JSONObject) sceneResult).getString("scene");
                    String suggestion = ((JSONObject) sceneResult).getString("suggestion");
                    //todo 可根据具体情况允许部分场景下的类别通过以提高通过率
                    if (!ALI_GREEN_SUGGESTION_PASS.equals(suggestion)) {
                        log.info(urls + "scene = [" + scene + "],suggestion = [" + suggestion + "]");
                        return false;
                    }
                }
            } else {
                log.error("task process fail. task response:" + JSON.toJSONString(taskResult));
                return false;
            }
        }
        return true;
    }

    public boolean scan(String url) {
        if (StringUtils.isEmpty(url)) {
            return true;
        }
        return multiScan(Collections.singletonList(url));
    }


    private ImageSyncScanRequest createImageSyncScanRequest(List<String> urls) {
        ImageSyncScanRequest imageSyncScanRequest = new ImageSyncScanRequest();
        imageSyncScanRequest.setAcceptFormat(FormatType.JSON);
        imageSyncScanRequest.setMethod(MethodType.POST);
        imageSyncScanRequest.setEncoding("utf-8");
        imageSyncScanRequest.setProtocol(ProtocolType.HTTP);

        JSONObject httpBody = new JSONObject();
        httpBody.put("scenes", SCAN_SCENES);

        List<JSONObject> tasks = new ArrayList<>(urls.size());
        urls.forEach(url -> {
            if (url.startsWith("http")) {
                JSONObject task = new JSONObject();
                task.put("dataId", UUID.randomUUID().toString());
                task.put("url", url);
                task.put("time", new Date());
                tasks.add(task);
            }
        });
        httpBody.put("tasks", tasks);

        imageSyncScanRequest.setHttpContent(org.apache.commons.codec.binary.StringUtils.getBytesUtf8(httpBody.toJSONString()),
                "UTF-8", FormatType.JSON);
        imageSyncScanRequest.setConnectTimeout(3000);
        imageSyncScanRequest.setReadTimeout(10000);

        return imageSyncScanRequest;
    }


}
