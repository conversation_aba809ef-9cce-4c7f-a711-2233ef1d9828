package com.kaiba.m.core.service.news.article;

import com.kaiba.lib.base.constant.KbModule;
import com.kaiba.lib.base.constant.news.NewsChannel;
import com.kaiba.lib.base.constant.news.article.*;
import com.kaiba.lib.base.domain.common.ShareModel;
import com.kaiba.lib.base.domain.da.sensors.pgc.SensorsPGCObjUserActModel;
import com.kaiba.lib.base.domain.news.article.*;
import com.kaiba.lib.base.domain.news.pool.bygroup.DisplayConfig;
import com.kaiba.lib.base.lang.dsl.timedisplay.DisplayTimeFormatter;
import com.kaiba.lib.base.lang.verifier.F;
import com.kaiba.lib.base.lang.verifier.Verifier;
import com.kaiba.lib.base.lang.verifier.VerifierBuilder;
import com.kaiba.lib.base.response.KbCode;
import com.kaiba.lib.base.response.KbException;
import com.kaiba.lib.base.service.ISensorsService;
import com.kaiba.lib.base.util.StringUtils;
import com.kaiba.lib.base.util.appaction.AppActionUtils;
import com.kaiba.m.core.domain.news.article.NewsArticle;
import com.kaiba.m.core.domain.news.article.NewsModuleConfig;
import com.kaiba.m.core.domain.news.article.NewsSiteConfig;
import com.kaiba.m.core.domain.news.pool.bygroup.IdsGroup;
import lombok.extern.slf4j.Slf4j;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

/**
 * author: lyux
 * date: 2023-07-28
 */
@Slf4j
public class NewsArticleModelHelper {

    private static final Map<String, String> ARTICLE_DETAIL_PATH_MAP;
    static {
        Map<String, String> paths = new HashMap<>();
        paths.put(NewsRenderer.NEWS.name(), "/neo-news/news");
        paths.put(NewsRenderer.NONE.name(), "/neo-news/news-redirector"); // 外链先跳重定向以便计数
        paths.put(NewsRenderer.CUSTOM.name(), "/neo-news/custom"); // 仅用于向用户提示错误
        paths.put(NewsRenderer.VIDEO_TOP.name(), "/neo-news/video-top");
        paths.put(NewsRenderer.VIDEO_FULL.name(), "/neo-news/video-full");
        paths.put(NewsRenderer.AUDIO_TOP.name(), "/neo-news/audio-top");
        paths.put(NewsRenderer.AUDIO_FULL.name(), "/neo-news/audio-full");
        paths.put(NewsRenderer.VIDEO_EPISODE.name(), "/neo-news/video-episode");
        paths.put(NewsRenderer.VIDEO_EPISODE_CLIP.name(), "/neo-news/video-episode-clip");
        ARTICLE_DETAIL_PATH_MAP = Collections.unmodifiableMap(paths);
    }

    public static final DisplayConfig DEFAULT_ON_ABSENT_DISPLAY_CONFIG;
    static {
        DEFAULT_ON_ABSENT_DISPLAY_CONFIG = new DisplayConfig();
        DEFAULT_ON_ABSENT_DISPLAY_CONFIG.setViewStyle(DisplayViewStyle.NONE.name());
        DEFAULT_ON_ABSENT_DISPLAY_CONFIG.setReactStyle(DisplayReactStyle.NONE.name());
        DEFAULT_ON_ABSENT_DISPLAY_CONFIG.setReplyStyle(DisplayReplyStyle.CLOSED.name());
        DEFAULT_ON_ABSENT_DISPLAY_CONFIG.setDisplayTimeBy(ArticleTimeType.ONLINE_TIME.name());
        DEFAULT_ON_ABSENT_DISPLAY_CONFIG.setDisplayTimeFormat("yyyy-MM-dd");
        DEFAULT_ON_ABSENT_DISPLAY_CONFIG.setShareEnabled(true);
        DEFAULT_ON_ABSENT_DISPLAY_CONFIG.setPosterEnabled(false);
    }

    // ------------------------------------------------------

    public static ArticleModel copyArticleForFrontend(ArticleModel from, IdsGroup group, GroupApiQueryModel query) {
        ArticleModel article = new ArticleModel();
        article.setId(from.getId());
        article.setChannelKey(from.getChannelKey());
        article.setChannel(from.getChannel());
        article.setBusinessType(from.getBusinessType());
        article.setCreator(from.getCreator());
        article.setThreadId(from.getThreadId());
        article.setRenderer(from.getRenderer());
        article.setTitle(from.getTitle());
        article.setSubtitle(from.getSubtitle());
        article.setCovers(from.getCovers());
        article.setQrCategoryId(from.getQrCategoryId());
        article.setSiteId(from.getSiteId());
        if (query != null && query.isRequireContent()) {
            article.setContent(from.getContent());
        }
        article.setContentType(from.getContentType());
        article.setVideo(from.getVideo());
        article.setVoteId(from.getVoteId());
        article.setAudio(from.getAudio());
        article.setAttr(from.getAttr());

        article.setIsOriginal(from.getIsOriginal());
        article.setAction(from.getAction());
        article.setActionParams(from.getActionParams());

        article.setViewStyle(from.getViewStyle());
        article.setReactStyle(from.getReactStyle());
        article.setReplyStyle(from.getReplyStyle());
        article.setDisplayTimeBy(from.getDisplayTimeBy());
        article.setDisplayTimeFormat(from.getDisplayTimeFormat());
        article.setShare(from.getShare());

        article.setCreateTime(from.getCreateTime());
        article.setReleaseTime(from.getReleaseTime());
        article.setReleaseEndTime(from.getReleaseEndTime());
        article.setDisplayTime(from.getDisplayTime());
        if (query != null && query.isRequireTimeAll()) {
            article.setUpdateTime(from.getUpdateTime());
            article.setSignTime(from.getSignTime());
            article.setOnlineTime(from.getOnlineTime());
            article.setArchiveTime(from.getArchiveTime());
        }

        if (group != null) {
            NewsArticleModelHelper.fillGroupConfigToArticle(article, group.getDcOnAbsent());
            NewsArticleModelHelper.overrideGroupConfigToArticle(article, group.getDcOnFetch());
        }
        NewsArticleModelHelper.fillGroupConfigToArticle(
                article, NewsArticleModelHelper.DEFAULT_ON_ABSENT_DISPLAY_CONFIG);

        if (article.getDisplayTime() == null) {
            String timeBy = null;
            if (query != null && query.getTimeBy() != null) {
                timeBy = query.getTimeBy();
            } else if (article.getDisplayTimeBy() != null) {
                timeBy = article.getDisplayTimeBy();
            } else if (group != null && group.getSortTimeBy() != null) {
                timeBy = group.getSortTimeBy();
            }
            if (timeBy == null) {
                article.setDisplayTime(from.getOnlineTime());
            } else if (timeBy.equals(ArticleTimeType.CREATE_TIME.name())) {
                article.setDisplayTime(from.getCreateTime());
            } else if (timeBy.equals(ArticleTimeType.UPDATE_TIME.name())) {
                article.setDisplayTime(from.getUpdateTime());
            } else if (timeBy.equals(ArticleTimeType.SIGN_TIME.name())) {
                article.setDisplayTime(from.getSignTime());
            } else if (timeBy.equals(ArticleTimeType.ONLINE_TIME.name())) {
                article.setDisplayTime(from.getOnlineTime());
            } else if (timeBy.equals(ArticleTimeType.RELEASE_TIME.name())) {
                article.setDisplayTime(from.getReleaseTime());
            } else if (timeBy.equals(ArticleTimeType.ARCHIVE_TIME.name())) {
                article.setDisplayTime(from.getArchiveTime());
            }
        }

        if (article.getDisplayTime() != null) {
            String timeFormat = null;
            if (query != null && query.getTimeFormat() != null) {
                timeFormat = query.getTimeFormat();
            } else if (article.getDisplayTimeFormat() != null) {
                timeFormat = article.getDisplayTimeFormat();
            }
            if (timeFormat != null) {
                article.setDisplayTimeStr(DisplayTimeFormatter.formatMillis(timeFormat, article.getDisplayTime()));
            }

            // 尝试设置海报时间
            if (article.getShare() != null && article.isPosterEnabled()) {
                if (article.getShare().getPosterTimeFormat() == null) {
                    article.getShare().setPosterTime(article.getDisplayTimeStr());
                } else {
                    article.getShare().setPosterTime(DisplayTimeFormatter.formatMillis(
                            article.getShare().getPosterTimeFormat(), article.getDisplayTime()));
                }
            }
        }

        // 确保 stat 字段存在, 方便前端处理
        if (query == null || query.isRequireViewStat() || query.isRequireLikeStat()) {
            article.setStat(new ArticleStatModel());
        } else {
            article.setStat(EMPTY_ARTICLE_STAT);
        }

        return article;
    }

    public static void fillGroupConfigToArticle(ArticleModel article, DisplayConfig config) {
        if (article == null || config == null) {
            return;
        }
        if ((article.getCovers() == null || article.getCovers().isEmpty()) && config.getCover() != null) {
            article.setCovers(Collections.singletonList(config.getCover()));
        }
        if (article.getViewStyle() == null && config.getViewStyle() != null) {
            article.setViewStyle(config.getViewStyle());
        }
        if (article.getReactStyle() == null && config.getReactStyle() != null) {
            article.setReactStyle(config.getReactStyle());
        }
        if (article.getReplyStyle() == null && config.getReplyStyle() != null) {
            article.setReplyStyle(config.getReplyStyle());
        }
        if (article.getDisplayTimeBy() == null && config.getDisplayTimeBy() != null) {
            article.setDisplayTimeBy(config.getDisplayTimeBy());
        }
        if (article.getDisplayTimeFormat() == null && config.getDisplayTimeFormat() != null) {
            article.setDisplayTimeFormat(config.getDisplayTimeFormat());
        }
        if (article.getShare() == null && !config.isShareEnabled()) {
            article.setShare(ShareModel.createDisabledShare());
        }
        if (article.isShareEnabled()
                && (article.getShare() == null || article.getShare().getPosterEnabled() == null)
                && config.isPosterEnabled()) {
            article.setShare(ShareModel.setOrCreatePosterEnabled(article.getShare()));
        }
        if (article.getShare() != null && article.isPosterEnabled()) {
            if (article.getShare().getPosterTimeFormat() == null && config.getPosterTimeFormat() != null) {
                article.getShare().setPosterTimeFormat(config.getPosterTimeFormat());
            }
            if (article.getShare().getPosterHint() == null && config.getPosterHint() != null) {
                article.getShare().setPosterHint(config.getPosterHint());
            }
        }
    }

    public static void overrideGroupConfigToArticle(ArticleModel article, DisplayConfig config) {
        if (article == null || config == null) {
            return;
        }
        if (config.getCover() != null) {
            article.setCovers(Collections.singletonList(config.getCover()));
        }
        if (config.getViewStyle() != null) {
            article.setViewStyle(config.getViewStyle());
        }
        if (config.getReactStyle() != null) {
            article.setReactStyle(config.getReactStyle());
        }
        if (config.getReplyStyle() != null) {
            article.setReplyStyle(config.getReplyStyle());
        }
        if (config.getDisplayTimeBy() != null) {
            article.setDisplayTimeBy(config.getDisplayTimeBy());
        }
        if (config.getDisplayTimeFormat() != null) {
            article.setDisplayTimeFormat(config.getDisplayTimeFormat());
        }
        if (!config.isShareEnabled()) {
            article.setShare(ShareModel.createDisabledShare());
        }
        if (config.isShareEnabled() && config.isPosterEnabled()) {
            ShareModel.setOrCreatePosterEnabled(article.getShare());
        }
        if (config.getPosterTimeFormat() != null && article.getShare() != null && article.isPosterEnabled()) {
            article.getShare().setPosterTimeFormat(config.getPosterTimeFormat());
        }
    }

    public static ArticleStatModel createArticleStat(ArticleModel article, ISensorsService sensorsService) {
        ArticleStatModel stat = new ArticleStatModel();
        SensorsPGCObjUserActModel act = new SensorsPGCObjUserActModel();
        act.setBusiness(KbModule.NEWS_NEO.name());
        act.setRef1(article.getId());
        sensorsService.kbPGCCountByCacheRT(act).data().ifPresent(neoCount -> {
            stat.setReactRCount(neoCount.getLikeCount() == null ? 0 : neoCount.getLikeCount());
            stat.setViewRCount(neoCount.getViewCount() == null ? 0 : neoCount.getViewCount());
        });
        return stat;
    }

    public static void determineReplyStyle(ArticleModel article, String configReplyStyle) {
        DisplayReplyStyle style = DisplayReplyStyle.resolveByName(configReplyStyle).orElse(null);
        determineReplyStyle(article, style);
    }

    public static void determineReplyStyle(ArticleModel article, DisplayReplyStyle configReplyStyle) {
        if (configReplyStyle == null) {
            return;
        }
        if (article.getReplyStyle() == null) {
            article.setReplyStyle(configReplyStyle.name());
        } else {
            DisplayReplyStyle articleReplyStyle =
                    DisplayReplyStyle.resolveByName(article.getReplyStyle()).orElse(DisplayReplyStyle.FREE);
            if (articleReplyStyle.getStrict() < configReplyStyle.getStrict()) {
                article.setReplyStyle(configReplyStyle.name());
            }
        }
    }

    public static String assembleArticleUrl(String pageHost, String articleId, String renderer, String group) {
        String path;
        if (renderer == null || renderer.equals(NewsRenderer.CUSTOM.name())) {
            path = ARTICLE_DETAIL_PATH_MAP.get(NewsRenderer.NEWS.name());
        } else {
            path = ARTICLE_DETAIL_PATH_MAP.get(renderer);
        }
        if (path == null) {
            return null;
        }
        String url = "https://" + pageHost + path + "?articleId=" + articleId;
        if (group != null) {
            url = url + "&groupKey=" + group;
        }
        return url;
    }

    public static void assembleArticleAction(String pageHost, ArticleModel article, String group) {
        if (article.getRenderer() != null && !article.getRenderer().equals(NewsRenderer.NONE.name())) {
            String url = assembleArticleUrl(pageHost, article.getId(), article.getRenderer(), group);
            article.putActionWebParam(url, article.getTitle(), false, true);
        }
    }

    public static ShareModel assembleArticleShare(String pageHost, ArticleModel article, String group) {
        if (article.getShare() != null && article.getShare().getEnabled() != null && !article.getShare().getEnabled()) {
            return null;
        }
        ShareModel share = article.getShare();
        if (share == null) {
            share = new ShareModel();
        }
        if (StringUtils.isEmpty(share.getTitle())) {
            if (StringUtils.isEmpty(article.getTitle())) {
                share.setTitle("开吧资讯, 精彩杭州");
            } else {
                share.setTitle(article.getTitle());
            }
        }
        if (StringUtils.isEmpty(share.getContent()) && !StringUtils.isEmpty(article.getSubtitle())) {
            share.setContent(article.getSubtitle());
        }
        if (StringUtils.isEmpty(share.getImageUrl())) {
            if (article.getCovers() == null || article.getCovers().isEmpty()) {
                share.setImageUrl("https://static.kaiba315.com.cn/kaiba-logo.png");
            } else {
                share.setImageUrl(article.getCovers().get(0).getImageUrl());
            }
        }
        if (StringUtils.isEmpty(share.getUrl())) {
            share.setUrl(assembleArticleUrl(pageHost, article.getId(), article.getRenderer(), group));
        }
        if (share.isPosterEnabled()) {
            if (share.getPosterTimeFormat() == null) {
                share.setPosterTime(article.getDisplayTimeStr());
            } else {
                share.setPosterTime(DisplayTimeFormatter.formatMillis(
                        article.getShare().getPosterTimeFormat(), article.getDisplayTime()));
            }
        }
        return share;
    }

    public static void attachPushStat(ArticleModel article, Integer pushCount) {
        if (pushCount != null) {
            if (article.getStat() == null) {
                article.setStat(new ArticleStatModel());
            }
            article.getStat().setPushCount(pushCount);
        }
    }

    // ------------------------------------------------------

    static final Verifier<NewsArticle> ARTICLE_VERIFIER = new VerifierBuilder<NewsArticle>().defaultOrElseThrow()
            .and(F.intF(NewsArticle::getSiteId).notNull().r("需要指定所属电台"))
            .ifNotNull(F.str(NewsArticle::getChannelKey).enums(NewsChannel.values()).r("未知的频道"))
            .and(F.str(NewsArticle::getState).notEmpty().r("需要填写文章状态"))
            .ifNotNull(F.str(NewsArticle::getContentType).enums(NewsContentType.values()).r("未知的内容类型"))
            .ifNotNull(F.str(NewsArticle::getViewStyle).enums(DisplayViewStyle.values()).r("未知的浏览样式"))
            .ifNotNull(F.str(NewsArticle::getReplyStyle).enums(DisplayReplyStyle.values()).r("未知的评论功能配置"))
            .ifNotNull(F.str(NewsArticle::getReactStyle).enums(DisplayReactStyle.values()).r("未知的互动按钮设置"))
            .ifNotNull(F.str(NewsArticle::getDisplayTimeBy).enums(ArticleTimeType.values()).r("未知的展示时间类型"))
            .and(F.str(NewsArticle::getRenderer).enums(NewsRenderer.values()).r("未知的渲染器类型"))
            .ifNotNull(F.str(NewsArticle::getBusinessType).enums(NewsBusinessType.values()).r("未知的业务类型"))
            .ifNotNull(F.longF(NewsArticle::getReleaseTime).gt(0L))
            .ifNotNull(F.longF(NewsArticle::getReleaseEndTime).gt(0L))
            .and(F.str(NewsArticle::getTitle).notEmpty().r("文章标题不可为空"))
            .create();

    static final Verifier<NewsSiteConfig> SITE_CONFIG_VERIFIER = new VerifierBuilder<NewsSiteConfig>().defaultOrElseThrow()
            .and(F.intF(NewsSiteConfig::getSiteId).notNull().r("需要指定所属电台"))
            .and(F.str(NewsSiteConfig::getThreadId).notNull().r("需要指定电台帖子总板"))
            .create();

    static final Verifier<NewsModuleConfig> MODULE_CONFIG_VERIFIER = new VerifierBuilder<NewsModuleConfig>().defaultOrElseThrow()
            .and(F.intF(NewsModuleConfig::getSiteId).notNull().r("需要指定所属电台"))
            .and(F.str(NewsModuleConfig::getModule).notNull().r("需要指定所属模块"))
            .and(F.str(NewsModuleConfig::getThreadId).notNull().r("需要指定模块帖子总板"))
            .create();

    // ------------------------------------------------------

    static void verifyContent(NewsArticle article) {
        NewsRenderer renderer = NewsRenderer.resolveByName(article.getRenderer()).orElseThrow(
                () -> new KbException(KbCode.ILLEGAL_ARGUMENT, "unknown renderer type: " + article.getRenderer())
                        .r("未知的渲染器类型").li());
        NewsContentType contentType =
                NewsContentType.resolveByName(article.getContentType()).orElse(NewsContentType.NONE);
        boolean ensureAction = false;
        boolean ensureAudio = false;
        boolean ensureVideo = false;
        switch (renderer) {
            case NONE:
                if (article.getAction() == null) {
                    throw new KbException(KbCode.ILLEGAL_ARGUMENT, "action null").r("请配置正确的跳转外链").li();
                }
                ensureAction = true;
                break;
            case CUSTOM:
                break;
            case NEWS:
                if (!contentType.isHasContent()) {
                    throw new KbException(KbCode.ILLEGAL_ARGUMENT, "should be type with content").r("正文内容类型错误").li();
                }
                if (StringUtils.isEmpty(article.getContent())) {
                    throw new KbException(KbCode.ILLEGAL_ARGUMENT, "content null").r("正文内容不可为空").li();
                }
                break;
            case VIDEO_TOP:
            case VIDEO_FULL:
                if (article.getVideo() == null) {
                    throw new KbException(KbCode.ILLEGAL_ARGUMENT, "video null").r("请配置正确的视频内容").li();
                }
                ensureVideo = true;
                break;
            case AUDIO_TOP:
            case AUDIO_FULL:
                if (article.getAudio() == null) {
                    throw new KbException(KbCode.ILLEGAL_ARGUMENT, "audio null").r("请配置正确的音频内容").li();
                }
                ensureAudio = true;
                break;
            case VIDEO_EPISODE:
            case VIDEO_EPISODE_CLIP:
                if (article.getVideo() == null && article.getAudio() == null) {
                    throw new KbException(KbCode.ILLEGAL_ARGUMENT, "video or audio null").r("请配置正确的音视频内容").li();
                }
                break;
            default:
                throw new KbException(KbCode.ILLEGAL_ARGUMENT, "unsupported renderer: " + renderer).r("尚不支持的渲染类型").li();
        }
        if (article.getVideo() != null && ensureVideo) {
            validateArticleVideo(article.getVideo());
        }
        if (article.getAudio() != null && ensureAudio) {
            validateArticleAudio(article.getAudio());
        }
        if (article.getAction() != null && ensureAction) {
            AppActionUtils.validateAction(article.getAction(), article.getActionParams());
        }
    }

    static void trimContentItem(NewsArticle article) {
        if (article.getTitle() != null) {
            String title = article.getTitle().trim();
            article.setTitle(StringUtils.isEmpty(title) ? null : title);
        }
        if (article.getSubtitle() != null) {
            String subtitle = article.getSubtitle().trim();
            article.setSubtitle(StringUtils.isEmpty(subtitle) ? null : subtitle);
        }
        if (article.getVideo() != null) {
            try {
                validateArticleVideo(article.getVideo());
            } catch (Exception e) {
                log.warn("video malformed, just clear. " + article.getId() + " -> " + article.getVideo());
                article.setVideo(null);
            }
        }
        if (article.getAudio() != null) {
            try {
                validateArticleAudio(article.getAudio());
            } catch (Exception e) {
                log.warn("audio malformed, just clear. " + article.getId() + " -> " + article.getAudio());
                article.setAudio(null);
            }
        }
        if (article.getAction() != null) {
            if (!AppActionUtils.isActionValid(article.getAction(), article.getActionParams())) {
                log.warn("action malformed, just clear. " + article.getId() + " -> " + article.getAction());
                article.setAction(null);
                article.setActionParams(null);
            }
        }
    }

    private static void validateArticleVideo(ArticleVideo video) {
        if (video == null) {
            throw new KbException(KbCode.ILLEGAL_ARGUMENT, "video null").r("视频内容不可为空").li();
        }
        if (video.getVideoUrl() == null && video.getMediaId() == null) {
            throw new KbException(KbCode.ILLEGAL_ARGUMENT, "video content null").r("需要视频链接或媒资ID").li();
        }
        if (video.getVideoUrl() != null && !StringUtils.isValidUrl(video.getVideoUrl())) {
            throw new KbException(KbCode.ILLEGAL_ARGUMENT, "video url invalid").r("视频地址错误").li();
        }
        if (video.getWidth() == null || video.getHeight() == null) {
            log.warn("article video without width or height, will set to 1000|1000"
                    + ", videoUrl=" + video.getVideoUrl()
                    + ", videoWH=" + video.getWidth() + "|" + video.getHeight());
            video.setWidth(1000);
            video.setHeight(1000);
        }
    }

    private static void validateArticleAudio(ArticleAudio audio) {
        if (audio == null) {
            throw new KbException(KbCode.ILLEGAL_ARGUMENT, "audio null").r("音频内容不可为空").li();
        }
        if (audio.getAudioUrl() == null && audio.getMediaId() == null) {
            throw new KbException(KbCode.ILLEGAL_ARGUMENT, "audio content null").r("需要音频链接或媒资ID").li();
        }
        if (audio.getAudioUrl() != null && !StringUtils.isValidUrl(audio.getAudioUrl())) {
            throw new KbException(KbCode.ILLEGAL_ARGUMENT, "audio url invalid").r("音频地址错误").li();
        }
    }

    private static final ArticleStatModel EMPTY_ARTICLE_STAT = new ArticleStatModel();

}
