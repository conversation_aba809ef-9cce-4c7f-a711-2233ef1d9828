package com.kaiba.m.core.service.da.sensors;

import com.kaiba.lib.base.assembler.Mapper;
import com.kaiba.lib.base.constant.da.sensors.KanbanColConfigType;
import com.kaiba.lib.base.constant.da.sensors.KanbanColDataType;
import com.kaiba.lib.base.constant.da.sensors.KanbanDivState;
import com.kaiba.lib.base.domain.da.sensors.kanban.DivConfigActModel;
import com.kaiba.lib.base.domain.da.sensors.kanban.DivConfigKaibaModel;
import com.kaiba.lib.base.domain.da.sensors.kanban.DivConfigPGCModel;
import com.kaiba.lib.base.domain.da.sensors.kanban.DivConfigPageModel;
import com.kaiba.lib.base.domain.da.sensors.kanban.DivConfigPlatformModel;
import com.kaiba.lib.base.domain.da.sensors.kanban.KanbanColumn;
import com.kaiba.lib.base.domain.da.sensors.kanban.KanbanConfigGroupModel;
import com.kaiba.lib.base.domain.da.sensors.kanban.KanbanConfigModel;
import com.kaiba.lib.base.response.KbCode;
import com.kaiba.lib.base.response.KbException;
import com.kaiba.lib.base.util.StringUtils;
import com.kaiba.m.core.domain.da.sensors.kanban.DivConfig;
import com.kaiba.m.core.domain.da.sensors.kanban.DivConfigAct;
import com.kaiba.m.core.domain.da.sensors.kanban.DivConfigKaiba;
import com.kaiba.m.core.domain.da.sensors.kanban.DivConfigPGC;
import com.kaiba.m.core.domain.da.sensors.kanban.DivConfigPage;
import com.kaiba.m.core.domain.da.sensors.kanban.DivConfigPlatform;
import com.kaiba.m.core.domain.da.sensors.kanban.KanbanConfig;
import com.kaiba.m.core.domain.da.sensors.kanban.KanbanConfigGroup;
import com.kaiba.m.core.repository.da.sensors.kanban.DivConfigActRepository;
import com.kaiba.m.core.repository.da.sensors.kanban.DivConfigKaibaRepository;
import com.kaiba.m.core.repository.da.sensors.kanban.DivConfigPGCRepository;
import com.kaiba.m.core.repository.da.sensors.kanban.DivConfigPageRepository;
import com.kaiba.m.core.repository.da.sensors.kanban.DivConfigPlatformRepository;
import com.kaiba.m.core.repository.da.sensors.kanban.KanbanConfigGroupRepository;
import com.kaiba.m.core.repository.da.sensors.kanban.KanbanConfigRepository;
import com.kaiba.m.core.util.PageUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @date 2024/02/28 14:20
 */
@Slf4j
@Service
public class SensorsKanbanConfigService {

    private final DivConfigActRepository actConfigRepository;
    private final DivConfigPageRepository pageConfigRepository;
    private final DivConfigPGCRepository pgcConfigRepository;
    private final DivConfigPlatformRepository platformRepository;
    private final DivConfigKaibaRepository kaibaRepository;
    private final KanbanConfigRepository kanbanConfigRepository;

    private final KanbanConfigGroupRepository kanbanConfigGroupRepository;

    public SensorsKanbanConfigService(
            DivConfigActRepository actConfigRepository,
            DivConfigPageRepository pageConfigRepository,
            DivConfigPGCRepository pgcConfigRepository,
            DivConfigPlatformRepository platformRepository,
            DivConfigKaibaRepository kaibaRepository,
            KanbanConfigRepository kanbanConfigRepository,
            KanbanConfigGroupRepository kanbanConfigGroupRepository
    ) {
        this.actConfigRepository = actConfigRepository;
        this.pageConfigRepository = pageConfigRepository;
        this.pgcConfigRepository = pgcConfigRepository;
        this.platformRepository = platformRepository;
        this.kaibaRepository = kaibaRepository;
        this.kanbanConfigRepository = kanbanConfigRepository;
        this.kanbanConfigGroupRepository = kanbanConfigGroupRepository;
    }

    public DivConfigActModel addDivConfigAct(DivConfigActModel actModel) {
        actModel.setState(KanbanDivState.PREPARE.name());
        actModel.setMark(actModel.generateConfigMark());
        long time = System.currentTimeMillis();
        actModel.setCreateTime(time);
        actModel.setUpdateTime(time);
        SensorsKanbanHelper.DIV_CONFIG_ACT_MODEL_VERIFIER.verify(actModel);
        actConfigRepository.findByMark(actModel.getMark()).ifPresent(e -> {
            throw new KbException(KbCode.RESOURCE_ALREADY_EXIST).r("该分时策略下的活动配置已存在").li();
        });
        DivConfigAct save = actConfigRepository.save(model2Entity(actModel));
        return entity2Model(save);
    }

    public DivConfigPageModel addDivConfigPage(DivConfigPageModel pageModel) {
        pageModel.setState(KanbanDivState.PREPARE.name());
        pageModel.setMark(pageModel.generateConfigMark());
        long time = System.currentTimeMillis();
        pageModel.setCreateTime(time);
        pageModel.setUpdateTime(time);
        SensorsKanbanHelper.DIV_CONFIG_PAGE_MODEL_VERIFIER.verify(pageModel);
        pageConfigRepository.findByMark(pageModel.getMark()).ifPresent(e -> {
            throw new KbException(KbCode.RESOURCE_ALREADY_EXIST).r("该分时策略下的页面配置已存在").li();
        });
        DivConfigPage save = pageConfigRepository.save(model2Entity(pageModel));
        return entity2Model(save);
    }

    public DivConfigPGCModel addDivConfigPGC(DivConfigPGCModel pgcModel) {
        pgcModel.setState(KanbanDivState.PREPARE.name());
        pgcModel.setMark(pgcModel.generateConfigMark());
        long time = System.currentTimeMillis();
        pgcModel.setCreateTime(time);
        pgcModel.setUpdateTime(time);
        SensorsKanbanHelper.DIV_CONFIG_PGC_MODEL_VERIFIER.verify(pgcModel);
        pgcConfigRepository.findByMark(pgcModel.getMark()).ifPresent(e -> {
            throw new KbException(KbCode.RESOURCE_ALREADY_EXIST).r("该分时策略下的业务类型或引用对象配置已存在").li();
        });
        DivConfigPGC save = pgcConfigRepository.save(model2Entity(pgcModel));
        return entity2Model(save);
    }

    public DivConfigPlatformModel addDivConfigPlatform(DivConfigPlatformModel platformModel) {
        platformModel.setState(KanbanDivState.PREPARE.name());
        platformModel.setMark(platformModel.generateConfigMark());
        long time = System.currentTimeMillis();
        platformModel.setCreateTime(time);
        platformModel.setUpdateTime(time);
        SensorsKanbanHelper.DIV_CONFIG_PLATFORM_MODEL_VERIFIER.verify(platformModel);
        platformRepository.findByMark(platformModel.getMark()).ifPresent(e -> {
            throw new KbException(KbCode.RESOURCE_ALREADY_EXIST).r("该分时策略下的电台配置已存在").li();
        });
        DivConfigPlatform save = platformRepository.save(model2Entity(platformModel));
        return entity2Model(save);
    }

    public DivConfigKaibaModel addDivConfigKaiba(DivConfigKaibaModel kaibaModel) {
        kaibaModel.setState(KanbanDivState.PREPARE.name());
        kaibaModel.setMark(kaibaModel.generateConfigMark());
        long time = System.currentTimeMillis();
        kaibaModel.setCreateTime(time);
        kaibaModel.setUpdateTime(time);
        SensorsKanbanHelper.DIV_CONFIG_KAIBA_MODEL_VERIFIER.verify(kaibaModel);
        kaibaRepository.findByMark(kaibaModel.getMark()).ifPresent(e -> {
            throw new KbException(KbCode.RESOURCE_ALREADY_EXIST).r("该分时策略下的电台配置已存在").li();
        });
        DivConfigKaiba save = kaibaRepository.save(model2Entity(kaibaModel));
        return entity2Model(save);
    }

    public KanbanConfigModel addKanbanConfig(KanbanConfigModel configModel) {
        long time = System.currentTimeMillis();
        configModel.setCreateTime(time);
        configModel.setUpdateTime(time);
        SensorsKanbanHelper.CONFIG_MODEL_VERIFIER.verify(configModel);
        checkKanbanColumn(configModel);
        KanbanConfig save = kanbanConfigRepository.save(model2Entity(configModel));
        return entity2Model(save);
    }

    public KanbanConfigGroupModel addKanbanConfigGroup(KanbanConfigGroupModel configModel) {
        long time = System.currentTimeMillis();
        configModel.setCreateTime(time);
        configModel.setUpdateTime(time);
        SensorsKanbanHelper.CONFIG_GROUP_MODEL_VERIFIER.verify(configModel);
        KanbanConfigGroup save = kanbanConfigGroupRepository.save(model2Entity(configModel));
        return entity2Model(save);
    }

    public void delDivConfigActById(String id) {
        actConfigRepository.deleteById(id);
    }

    public void delDivConfigPageById(String id) {
        pageConfigRepository.deleteById(id);
    }

    public void delDivConfigPGCById(String id) {
        pgcConfigRepository.deleteById(id);
    }

    public void delDivConfigPlatformById(String id) {
        platformRepository.deleteById(id);
    }

    public void delDivConfigKaibaById(String id) {
        kaibaRepository.deleteById(id);
    }

    public DivConfigActModel editDivConfigAct(DivConfigActModel model) {
        SensorsKanbanHelper.DIV_CONFIG_ACT_MODEL_VERIFIER.verify(model);
        if (model.getId() == null) {
            throw new KbException(KbCode.ILLEGAL_ARGUMENT).r("id缺失").li();
        }
        DivConfigAct config = actConfigRepository.update(model2Entity(model));
        return entity2Model(config);
    }

    public DivConfigPageModel editDivConfigPage(DivConfigPageModel model) {
        SensorsKanbanHelper.DIV_CONFIG_PAGE_MODEL_VERIFIER.verify(model);
        if (model.getId() == null) {
            throw new KbException(KbCode.ILLEGAL_ARGUMENT).r("id缺失").li();
        }
        DivConfigPage config = pageConfigRepository.update(model2Entity(model));
        return entity2Model(config);
    }

    public DivConfigPGCModel editDivConfigPGC(DivConfigPGCModel model) {
        SensorsKanbanHelper.DIV_CONFIG_PGC_MODEL_VERIFIER.verify(model);
        if (model.getId() == null) {
            throw new KbException(KbCode.ILLEGAL_ARGUMENT).r("id缺失").li();
        }
        DivConfigPGC config = pgcConfigRepository.update(model2Entity(model));
        return entity2Model(config);
    }

    public DivConfigPlatformModel editDivConfigPlatform(DivConfigPlatformModel model) {
        SensorsKanbanHelper.DIV_CONFIG_PLATFORM_MODEL_VERIFIER.verify(model);
        if (model.getId() == null) {
            throw new KbException(KbCode.ILLEGAL_ARGUMENT).r("id缺失").li();
        }
        DivConfigPlatform config = platformRepository.update(model2Entity(model));
        return entity2Model(config);
    }

    public DivConfigKaibaModel editDivConfigKaiba(DivConfigKaibaModel model) {
        SensorsKanbanHelper.DIV_CONFIG_KAIBA_MODEL_VERIFIER.verify(model);
        if (model.getId() == null) {
            throw new KbException(KbCode.ILLEGAL_ARGUMENT).r("id缺失").li();
        }
        DivConfigKaiba config = kaibaRepository.update(model2Entity(model));
        return entity2Model(config);
    }

    public void delKanbanConfigById(String id) {
        kanbanConfigRepository.deleteById(id);
    }

    public void delKanbanConfigGroupById(String id) {
        kanbanConfigGroupRepository.deleteById(id);
    }

    public KanbanConfigModel editKanbanConfig(KanbanConfigModel configModel) {
        SensorsKanbanHelper.CONFIG_MODEL_VERIFIER.verify(configModel);
        checkKanbanColumn(configModel);
        KanbanConfig config = model2Entity(configModel);
        return entity2Model(kanbanConfigRepository.updateKanbanConfig(config));
    }

    public KanbanConfigGroupModel editKanbanConfigGroup(KanbanConfigGroupModel configModel) {
        SensorsKanbanHelper.CONFIG_GROUP_MODEL_VERIFIER.verify(configModel);
        KanbanConfigGroup config = model2Entity(configModel);
        return entity2Model(kanbanConfigGroupRepository.update(config));
    }

    public DivConfigAct getDivConfigActById(String id) {
        return actConfigRepository.findById(id).orElseThrow(() ->
                new KbException(KbCode.RESOURCE_NOT_FOUND).setReadableMessage("对应act配置不存在:" + id).li());
    }

    public List<DivConfigAct> getDivConfigActByIdAndState(List<String> ids, String state) {
        return actConfigRepository.findByIdInAndState(ids, state);
    }

    public DivConfigPGC getDivConfigPGCById(String id) {
        return pgcConfigRepository.findById(id).orElseThrow(() ->
                new KbException(KbCode.RESOURCE_NOT_FOUND).setReadableMessage("对应pgc配置不存在:" + id).li());
    }

    public List<DivConfigPGC> getDivConfigPGCByIdAndState(List<String> ids, String state) {
        return pgcConfigRepository.findByIdInAndState(ids, state);
    }

    public DivConfigPage getDivConfigPageById(String id) {
        return pageConfigRepository.findById(id).orElseThrow(() ->
                new KbException(KbCode.RESOURCE_NOT_FOUND).setReadableMessage("对应page配置不存在:" + id).li());
    }

    public List<DivConfigPage> getDivConfigPageByIdAndState(List<String> ids, String state) {
        return pageConfigRepository.findByIdInAndState(ids, state);
    }

    public DivConfigPlatform getDivConfigPlatformById(String id) {
        return platformRepository.findById(id).orElseThrow(() ->
                new KbException(KbCode.RESOURCE_NOT_FOUND).setReadableMessage("对应platform配置不存在:" + id).li());
    }

    public DivConfigKaiba getDivConfigKaibaById(String id) {
        return kaibaRepository.findById(id).orElseThrow(() ->
                new KbException(KbCode.RESOURCE_NOT_FOUND).setReadableMessage("对应kaiba配置不存在:" + id).li());
    }

    public List<DivConfigPlatform> getDivConfigPlatformByIdAndState(List<String> ids, String state) {
        return platformRepository.findByIdInAndState(ids, state);
    }

    public List<DivConfigKaiba> getDivConfigKaibaByIdAndState(List<String> ids, String state) {
        return kaibaRepository.findByIdInAndState(ids, state);
    }

    public Page<DivConfigPage> getDivConfigPageByParam(
            String id,
            String name,
            String strategy,
            String state,
            Long executeTime,
            Integer page, Integer pageSize) {
        return pageConfigRepository.findByParam(id, name, strategy, state, executeTime, PageUtils.ofDefault(page, pageSize, Sort.by(Sort.Direction.DESC,"createTime")));
    }

    public Page<DivConfigAct> getDivConfigActByParam(
            String id,
            String name,
            String activity,
            String pageUrl,
            String strategy,
            String state,
            Long executeTime,
            Integer page, Integer pageSize) {
        return actConfigRepository.findByParam(id, name, activity, pageUrl, strategy, state, executeTime, PageUtils.ofDefault(page, pageSize, Sort.by(Sort.Direction.DESC,"createTime")));
    }

    public Page<DivConfigPGC> getDivConfigPGCByParam(
            String id,
            String name,
            String strategy,
            String state,
            Long executeTime,
            Integer page, Integer pageSize) {
        return pgcConfigRepository.findByParam(id, name, strategy, state, executeTime, PageUtils.ofDefault(page, pageSize, Sort.by(Sort.Direction.DESC,"createTime")));
    }

    public Page<DivConfigPlatform> getDivConfigPlatformByParam(
            String id,
            String name,
            Integer siteId,
            String strategy,
            String state,
            Long executeTime,
            Integer page, Integer pageSize) {
        return platformRepository.findByParam(id, name, siteId, strategy, state, executeTime, PageUtils.ofDefault(page, pageSize, Sort.by(Sort.Direction.DESC,"createTime")));
    }

    public Page<DivConfigKaiba> getDivConfigKaibaByParam(
            String id,
            String name,
            Integer siteId,
            String strategy,
            String state,
            Long executeTime,
            Integer page, Integer pageSize) {
        return kaibaRepository.findByParam(id, name, siteId, strategy, state, executeTime, PageUtils.ofDefault(page, pageSize, Sort.by(Sort.Direction.DESC,"createTime")));
    }

    public KanbanConfigModel getKanbanDataById(String kanbanId) {
        return kanbanConfigRepository.findById(kanbanId)
                .map(this::entity2Model)
                .orElseThrow(() -> new KbException(KbCode.RESOURCE_NOT_FOUND).r("对应看板配置不存在:" + kanbanId).li());
    }

    public Page<KanbanConfig> getKanbanConfigByParam(
            String id,
            String name,
            String key,
            String strategy,
            Integer page, Integer pageSize
    ) {
        return kanbanConfigRepository.findByParam(id, name, key, strategy, PageUtils.ofDefault(page, pageSize, Sort.by(Sort.Direction.DESC,"createTime")));
    }

    public List<KanbanConfig> getKanbanConfigByIds(List<String> ids) {
        if (ids == null || ids.isEmpty()) {
            return Collections.emptyList();
        }
        if (ids.size() >= 200) {
            throw new KbException(KbCode.ILLEGAL_ARGUMENT).r("id参数列表过多,最多支持200个").li();
        }
        return kanbanConfigRepository.findByIdIn(ids);
    }

    public Page<KanbanConfigGroup> getKanbanConfigGroupByParam(
            String id,
            String name,
            String key,
            String configId,
            Integer page, Integer pageSize
    ) {
        return kanbanConfigGroupRepository.findByParam(id, name, key, configId, PageUtils.ofDefault(page, pageSize, Sort.by(Sort.Direction.DESC,"createTime")));
    }


    // ------------------------------------------------------------

    private DivConfigAct model2Entity(DivConfigActModel actModel) {
        return Mapper.map(actModel, DivConfigAct.class);
    }

    private DivConfigPage model2Entity(DivConfigPageModel pageModel) {
        return Mapper.map(pageModel, DivConfigPage.class);
    }

    private DivConfigPGC model2Entity(DivConfigPGCModel pgcModel) {
        return Mapper.map(pgcModel, DivConfigPGC.class);
    }

    private DivConfigPlatform model2Entity(DivConfigPlatformModel platformModel) {
        return Mapper.map(platformModel, DivConfigPlatform.class);
    }

    private DivConfigKaiba model2Entity(DivConfigKaibaModel kaibaModel) {
        return Mapper.map(kaibaModel, DivConfigKaiba.class);
    }

    private KanbanConfig model2Entity(KanbanConfigModel configModel) {
        return Mapper.map(configModel, KanbanConfig.class);
    }

    private KanbanConfigGroup model2Entity(KanbanConfigGroupModel configModel) {
        return Mapper.map(configModel, KanbanConfigGroup.class);
    }

    private DivConfigActModel entity2Model(DivConfigAct actEntity) {
        return Mapper.map(actEntity, DivConfigActModel.class);
    }

    private DivConfigPageModel entity2Model(DivConfigPage pageEntity) {
        return Mapper.map(pageEntity, DivConfigPageModel.class);
    }

    private DivConfigPGCModel entity2Model(DivConfigPGC pgcEntity) {
        return Mapper.map(pgcEntity, DivConfigPGCModel.class);
    }

    private DivConfigPlatformModel entity2Model(DivConfigPlatform platformEntity) {
        return Mapper.map(platformEntity, DivConfigPlatformModel.class);
    }

    private DivConfigKaibaModel entity2Model(DivConfigKaiba kaibaEntity) {
        return Mapper.map(kaibaEntity, DivConfigKaibaModel.class);
    }

    private KanbanConfigModel entity2Model(KanbanConfig config) {
        return Mapper.map(config, KanbanConfigModel.class);
    }

    private KanbanConfigGroupModel entity2Model(KanbanConfigGroup config) {
        return Mapper.map(config, KanbanConfigGroupModel.class);
    }


    private <T extends DivConfig> List<T> getAndVerifyConfigList(KanbanColConfigType type, List<String> configIds) {
        switch (type) {
            case ACT:
                return (List<T>) actConfigRepository.findByIdIn(configIds).stream().map(DivConfigAct.class::cast).collect(Collectors.toList());
            case PAGE:
                return (List<T>) pageConfigRepository.findByIdIn(configIds).stream().map(DivConfigPage.class::cast).collect(Collectors.toList());
            case PGC:
                return (List<T>) pgcConfigRepository.findByIdIn(configIds).stream().map(DivConfigPGC.class::cast).collect(Collectors.toList());
            case PLATFORM:
                return (List<T>) platformRepository.findByIdIn(configIds).stream().map(DivConfigPlatform.class::cast).collect(Collectors.toList());
            case KAIBA:
                return (List<T>) kaibaRepository.findByIdIn(configIds).stream().map(DivConfigKaiba.class::cast).collect(Collectors.toList());
            default:
                return Collections.emptyList();
        }
    }

    private void checkKanbanColumn(KanbanConfigModel configModel) {
        if (configModel.getColumns() == null || configModel.getColumns().isEmpty()) {
            return;
        }
        Map<KanbanColConfigType, List<KanbanColumn>> map =
                configModel.getColumns().stream().collect(Collectors.groupingBy(o ->
                        KanbanColDataType.resolveByName(o.getType())
                                .map(KanbanColDataType::getConfigType).get()));
        List<String> allConfigIdList = configModel.getColumns().stream()
                .map(KanbanColumn::getConfigId)
                .collect(Collectors.toList());

        List<String> actConfigIdList = getConfigIdList(map, KanbanColConfigType.ACT);
        List<String> pageConfigIdList = getConfigIdList(map, KanbanColConfigType.PAGE);
        List<String> pgcConfigIdList = getConfigIdList(map, KanbanColConfigType.PGC);
        List<String> platformConfigIdList = getConfigIdList(map, KanbanColConfigType.PLATFORM);
        List<String> kaibanConfigIdList = getConfigIdList(map, KanbanColConfigType.KAIBA);

        List<DivConfig> actConfigList = getAndVerifyConfigList(KanbanColConfigType.ACT, actConfigIdList);
        List<DivConfig> pageConfigList = getAndVerifyConfigList(KanbanColConfigType.PAGE, pageConfigIdList);
        List<DivConfig> pgcConfigList = getAndVerifyConfigList(KanbanColConfigType.PGC, pgcConfigIdList);
        List<DivConfig> platformConfigList = getAndVerifyConfigList(KanbanColConfigType.PLATFORM, platformConfigIdList);
        List<DivConfig> kaibaConfigList = getAndVerifyConfigList(KanbanColConfigType.KAIBA, kaibanConfigIdList);

        List<DivConfig> configList = Stream.of(actConfigList, pageConfigList, pgcConfigList, platformConfigList, kaibaConfigList)
                .flatMap(List::stream)
                .collect(Collectors.toList());

        List<String> findIdList = configList.stream().map(DivConfig::getId).collect(Collectors.toList());
        List<String> diffList = allConfigIdList.stream()
                .filter(o -> !findIdList.contains(o)).collect(Collectors.toList());
        if (!diffList.isEmpty()) {
            throw new KbException(KbCode.ILLEGAL_ARGUMENT)
                    .r("对应配置不存在:" + diffList).li();
        }
        List<String> strategyList = configList.stream()
                .map(DivConfig::getStrategy)
                .distinct()
                .collect(Collectors.toList());
        if (strategyList.size() > 1) {
            throw new KbException(KbCode.ILLEGAL_ARGUMENT)
                    .r("配置存在多个策略:" + strategyList).li();
        }
        if (!StringUtils.isEqual(strategyList.get(0), configModel.getStrategy())) {
            throw new KbException(KbCode.ILLEGAL_ARGUMENT)
                    .r("表分时配置需要仅只能设置相同分时策略的数据列").li();
        }
    }

    private List<String> getConfigIdList(Map<KanbanColConfigType, List<KanbanColumn>> map, KanbanColConfigType type) {
        List<KanbanColumn> columns = map.getOrDefault(type, Collections.emptyList());
        return columns.stream().map(KanbanColumn::getConfigId).collect(Collectors.toList());
    }
}
