package com.kaiba.m.core.service.news.pool.knowledge;

import com.kaiba.lib.base.domain.news.pool.knowledge.BasedAnswerModel;
import com.kaiba.lib.base.domain.news.pool.knowledge.KnowledgeBaseModel;
import com.kaiba.m.core.domain.knowledge.KnowledgeBase;

/**
 * Description: 知识库Service层服务接口
 * Author: ZM227
 * Date: 2025/2/11 17:12
 */
public interface IKnowledgeBaseService {

    /**
     * 创建知识库实例
     * @param knowledgeBase 创建知识库实例
     * @return 知识库实例Id
     */
    String createKnowledgeBase(KnowledgeBase knowledgeBase);
}
