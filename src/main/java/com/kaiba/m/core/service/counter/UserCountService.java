package com.kaiba.m.core.service.counter;

import com.kaiba.lib.base.response.KbCode;
import com.kaiba.lib.base.response.KbException;
import com.kaiba.m.core.domain.counter.KbCounterUser;
import com.kaiba.m.core.repository.counter.CounterUserRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import reactor.core.publisher.FluxSink;
import reactor.core.publisher.UnicastProcessor;
import reactor.core.scheduler.Schedulers;

import java.time.Duration;
import java.util.LinkedList;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * author: lyux
 * date: 2021-05-26
 */
@Slf4j
@Component
class UserCountService {

    private static final int BUFFER_MAX_SIZE = 200; // 队列最多缓冲的事件数
    private static final Duration BUFFER_MAX_TIME = Duration.ofSeconds(3); // 队列最多缓冲的时长

    private final StringRedisTemplate redisTemplate;
    private final CounterUserRepository counterUserRepository;
    private final FluxSink<KbCounterUser> userCounterSink;

    UserCountService(
            StringRedisTemplate redisTemplate,
            CounterUserRepository counterUserRepository
    ) {
        this.redisTemplate = redisTemplate;
        this.counterUserRepository = counterUserRepository;
        UnicastProcessor<KbCounterUser> countEventProcessor = UnicastProcessor.create();
        countEventProcessor
                .publishOn(Schedulers.elastic())
                .doOnError(e -> log.warn("event error", e))
                .onErrorContinue((throwable, obj) -> {})
                .bufferTimeout(BUFFER_MAX_SIZE, BUFFER_MAX_TIME, LinkedList::new)
                .subscribeOn(Schedulers.parallel())
                .subscribe(this::bulkInsertUserCount);
        userCounterSink = countEventProcessor.sink();
    }

    boolean checkUserLimit(Integer userId, CounterSetting setting, long count) {
        if (count <= 0) {
            throw new KbException(KbCode.ILLEGAL_ARGUMENT, "count should be positive: " + count).li();
        } else if (setting.getUidLimit() <= 0) {
            // 计数不受限, 因此不必操作用户计数缓存, 以节省性能.
            return true;
        } else if (count > setting.getUidLimit()) {
            // 本次申请的计数已经超过单用户上限, 因此直接返回 false.
            return false;
        }

        if (userId == null) {
            throw new KbException(KbCode.ILLEGAL_ARGUMENT, "userId null").li();
        }
        // 我们假设缓存不存在的场景占多数.
        // 因此先尝试将计数设置到缓存, 若失败则再执行 incr 操作.
        // 若假设成立, 则上述逻辑在多数场景下将节省一次 IO.
        String limitCacheKey = CounterConst.getUserLimitCacheKey(setting.getId(), userId);
        Boolean success = redisTemplate.opsForValue().setIfAbsent(
                limitCacheKey, Long.toString(count), setting.getUidLimitCacheExpireAt(), TimeUnit.MILLISECONDS);
        if (success != null && success) {
            return true;
        } else {
            if (setting.getUidLimit() == count) {
                // 若缓存存在且请求计数和计数上限相等, 则本次计数操作必然超限, 直接返回 false.
                // 此优化主要针对 "一人一票" 这个极为常见的应用场景.
                return false;
            }
            Long userCount = redisTemplate.opsForValue().increment(limitCacheKey, count);
            return userCount != null && userCount <= setting.getUidLimit();
        }
    }

    long recordUserCount(Integer userId, CounterSetting setting, long count) {
        if (userId == null) {
            throw new KbException(KbCode.ILLEGAL_ARGUMENT, "userId null");
        }

        // 延迟写入数据库
        KbCounterUser u = new KbCounterUser();
        u.setCounterId(setting.getId());
        u.setUserId(userId);
        u.setCount(count);
        u.setCreateTime(System.currentTimeMillis());
        userCounterSink.next(u);

        // 写入缓存
        String cacheKey = CounterConst.getUserCountCacheKey(setting.getId(), userId);
        // 我们假设缓存不存在的场景占多数.
        // 因此先尝试将计数设置到缓存, 若失败则再执行 incr 操作.
        // 若假设成立, 则上述逻辑在多数场景下将节省一次 IO.
        Boolean success = redisTemplate.opsForValue().setIfAbsent(
                cacheKey, Long.toString(count), setting.getCountCacheExpire().toMillis(), TimeUnit.MILLISECONDS);
        if (success != null && success) {
            return count;
        } else {
            Long userCount = redisTemplate.opsForValue().increment(cacheKey, count);
            return userCount == null ? 0 : userCount;
        }
    }

    long getUserCountFromCache(Integer userId, CounterSetting setting) {
        if (userId == null) {
            throw new KbException(KbCode.ILLEGAL_ARGUMENT, "userId null");
        }
        String cacheKey = CounterConst.getUserCountCacheKey(setting.getId(), userId);
        String userCountStr = redisTemplate.opsForValue().get(cacheKey);
        if (userCountStr == null) {
            return 0;
        }
        try {
            return Long.parseLong(userCountStr);
        } catch (NumberFormatException e) {
            log.error("get user count from cache fail: " + cacheKey + " -> " + userCountStr, e);
            return 0;
        }
    }

    Page<KbCounterUser> getUserCountRecordFromDB(Integer userId, String counterId, Integer page, Integer pageSize) {
        int p = page == null ? 0 : page + 1;
        int ps = pageSize == null ? 20 : pageSize;
        if (userId == null) {
            return counterUserRepository.findByCounterIdOrderByIdDesc(counterId, PageRequest.of(p, ps));
        } else {
            return counterUserRepository.findByCounterIdAndUserIdOrderByIdDesc(counterId, userId, PageRequest.of(p, ps));
        }
    }

    // 危险操作: 应仅用于测试清理数据
    void clearUserCountByCounter(String counterId, String secret) {
        if (!CounterConst.SECRET.equals(secret)) {
            throw new KbException(KbCode.REQUEST_PARAM_INVALID, "wrong secret").li();
        }
        // 清理缓存
        for (int i = 0; ; i ++) {
            Page<KbCounterUser> records = counterUserRepository
                    .findByCounterIdOrderByIdDesc(counterId, PageRequest.of(i, 100));
            List<String> cacheKeys = records.stream()
                    .map(KbCounterUser::getUserId)
                    .map(userId -> CounterConst.getUserCountCacheKey(counterId, userId))
                    .collect(Collectors.toList());
            redisTemplate.delete(cacheKeys);
            if (records.isLast()) {
                break;
            }
        }
        // 清理数据库记录
        counterUserRepository.deleteByCounterId(counterId);
    }

    // --------------------------------------------------------

    private void bulkInsertUserCount(List<KbCounterUser> list) {
        List<KbCounterUser> filteredList = list.stream()
                .filter(u -> u.getCounterId() != null && u.getUserId() != 0 && u.getCount() > 0 && u.getCreateTime() != null)
                .collect(Collectors.toList());
        if (filteredList.size() != 0) {
            counterUserRepository.saveAll(filteredList);
        }
    }

}
