package com.kaiba.m.core.service.news.article;

import com.github.benmanes.caffeine.cache.CacheLoader;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.LoadingCache;
import com.kaiba.lib.base.assembler.Mapper;
import com.kaiba.lib.base.cache.redis.BeanKVCache;
import com.kaiba.lib.base.cache.redis.ObjectKVCache;
import com.kaiba.lib.base.constant.news.article.NewsContentType;
import com.kaiba.lib.base.constant.news.article.NewsState;
import com.kaiba.lib.base.domain.news.article.ArticleModel;
import com.kaiba.lib.base.domain.da.sensors.channel.SensorsChannelModel;
import com.kaiba.lib.base.response.KbCode;
import com.kaiba.lib.base.response.KbEntity;
import com.kaiba.lib.base.response.KbException;
import com.kaiba.lib.base.service.IPushService;
import com.kaiba.lib.base.service.ISensorsChannelService;
import com.kaiba.lib.base.util.StringUtils;
import com.kaiba.m.core.domain.news.article.NewsArticle;
import com.kaiba.m.core.domain.news.article.NewsModuleConfig;
import com.kaiba.m.core.domain.news.article.NewsSiteConfig;
import com.kaiba.m.core.repository.news.article.NewsArticleRepository;
import com.kaiba.m.core.repository.news.article.NewsModuleConfigRepository;
import com.kaiba.m.core.repository.news.article.NewsSiteConfigRepository;
import com.kaiba.m.core.util.JsonUtils;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.checkerframework.checker.nullness.qual.NonNull;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.util.*;
import java.util.stream.Collectors;

/**
 * author: lyux
 * date: 2023-07-27
 */
@Slf4j
@Service
public class NewsArticleCacheService {

    private static final String CACHE_KEY_SUMMARY = "java_news_article_summary_";
    private static final String CACHE_KEY_CONTENT = "java_news_article_content_";
    private static final String CACHE_PUSH_COUNT = "java_news_article_push_count_";
    private static final Duration CACHE_EXPIRE_SUMMARY = Duration.ofMinutes(10);
    private static final Duration CACHE_EXPIRE_CONTENT = Duration.ofMinutes(12);
    private static final Duration CACHE_EXPIRE_PUSH = Duration.ofDays(1);

    private final String articleDetailUrl;
    private final NewsArticleRepository articleRepository;
    private final NewsSiteConfigRepository siteConfigRepository;
    private final NewsModuleConfigRepository moduleConfigRepository;
    private final BeanKVCache<String, ArticleModel> summaryCache;
    private final ObjectKVCache<String> contentCache;
    private final ObjectKVCache<Integer> pushCountCache;
    private final StringRedisTemplate redisTemplate;
    private final ISensorsChannelService channelService;
    private final IPushService pushService;

    public NewsArticleCacheService(
            @Value("${kb.host.page}") String pageHost,
            NewsArticleRepository articleRepository,
            NewsSiteConfigRepository siteConfigRepository,
            NewsModuleConfigRepository moduleConfigRepository,
            StringRedisTemplate redisTemplate,
            ISensorsChannelService channelService,
            IPushService pushService
    ) {
        this.articleDetailUrl = "https://" + pageHost + "/neo-news/";
        this.articleRepository = articleRepository;
        this.siteConfigRepository = siteConfigRepository;
        this.moduleConfigRepository = moduleConfigRepository;
        this.channelService = channelService;
        this.redisTemplate = redisTemplate;
        this.pushService = pushService;
        this.summaryCache = new BeanKVCache.Builder<String, ArticleModel>(redisTemplate)
                .cacheKeyPrefix(CACHE_KEY_SUMMARY)
                .cacheExpire(CACHE_EXPIRE_SUMMARY)
                .dataIdConverter(ArticleModel::getId)
                .dataListProvider(this::getListFromDatabase)
                .dataAsJson(ArticleModel.class)
                .create();
        this.contentCache = new ObjectKVCache.Builder<String>(redisTemplate)
                .cacheKeyPrefix(CACHE_KEY_CONTENT)
                .cacheExpire(CACHE_EXPIRE_CONTENT)
                .dataOptionalProvider(this::getContentFromDatabase)
                .dataMultiProvider(this::getContentMapFromDatabase)
                .dataAsString().create();
        this.pushCountCache = new ObjectKVCache.Builder<Integer>(redisTemplate)
                .cacheKeyPrefix(CACHE_PUSH_COUNT)
                .cacheExpire(CACHE_EXPIRE_PUSH)
                .dataProvider(this::getPushCountFromDatabase)
                .string2dataConverter(Integer::parseInt)
                .create();
    }

    public Optional<ArticleModel> getSummaryById(String articleId) {
        return summaryCache.getDataById(articleId);
    }

    public List<ArticleModel> getSummaryListByIdIn(List<String> strings) {
        return summaryCache.getDataListByIdIn(strings);
    }

    public Map<String, ArticleModel> getSummaryMapByIdIn(List<String> strings) {
        return summaryCache.getDataMapByIdIn(strings);
    }

    public String getContentById(String articleId) {
        return contentCache.get(articleId);
    }

    public Map<String, String> getContentMapByIdIn(List<String> articleIds) {
        return contentCache.getMap(articleIds, true);
    }

    public String getDefaultOrgName(Integer siteId, String module) {
        if (module != null) {
            NewsModuleConfig moduleConfig = moduleConfigCache.get(new ModuleCacheKey(siteId, module));
            if (moduleConfig != null && moduleConfig.getOrgName() != null) {
                return moduleConfig.getOrgName();
            }
        }
        NewsSiteConfig siteConfig = siteConfigCache.get(siteId);
        if (siteConfig != null && siteConfig.getOrgName() != null) {
            return siteConfig.getOrgName();
        }
        return null;
    }

    public String getSiteThreadIdOrThrow(Integer siteId) {
        NewsSiteConfig siteConfig = siteConfigCache.get(siteId);
        if (siteConfig == null || siteConfig.getThreadId() == null) {
            throw new KbException(KbCode.RESOURCE_NOT_FOUND, "threadId not found for " + siteId)
                    .r("获取电台总控板块失败");
        } else {
            return siteConfig.getThreadId();
        }
    }

    public String getModuleThreadIdOrThrow(Integer siteId, String module) {
        NewsModuleConfig moduleConfig = moduleConfigCache.get(new ModuleCacheKey(siteId, module));
        if (moduleConfig == null || moduleConfig.getThreadId() == null) {
            throw new KbException(KbCode.RESOURCE_NOT_FOUND, "threadId not found for " + siteId + "-" + module)
                    .r("获取模块总控板块失败");
        } else {
            return moduleConfig.getThreadId();
        }
    }

    public List<String> getModuleListThreadIdsOrThrow(Integer siteId, Set<String> modules) {
        if (modules == null || modules.size() == 0) {
            return Collections.emptyList();
        }
        List<String> threadIds = new ArrayList<>(modules.size());
        for (String module : modules) {
            String threadId = getModuleThreadIdOrThrow(siteId, module);
            threadIds.add(threadId);
        }
        return threadIds;
    }

    public void invalidateSummaryCache(String articleId) {
        summaryCache.invalidate(articleId);
    }

    public void invalidateContentCache(String articleId) {
        contentCache.invalidate(articleId);
    }

    public TempToken createTempVisitToken(ArticleModel article) {
        long now = System.currentTimeMillis();
        long dueTime;
        Duration expire;
        if (NewsState.DRAFT.name().equals(article.getState())) {
            expire = Duration.ofHours(24);
        } else if (NewsState.ARCHIVED.name().equals(article.getState())) {
            expire = Duration.ofHours(1);
        } else {
            return new TempToken(article.getId(), "FAKE_TOKEN", Long.MAX_VALUE);
        }
        dueTime = now - (now % 60000) + expire.toMillis();
        String token = StringUtils.toMd5(article.getId() + "_" + dueTime);
        TempToken tempToken = new TempToken(article.getId(), token, dueTime);
        redisTemplate.opsForValue().set(token, JsonUtils.getGson().toJson(tempToken), expire);
        return tempToken;
    }

    public TempToken getTempVisitToken(String token) {
        String cachedStr = redisTemplate.opsForValue().get(token);
        if (cachedStr == null) {
            throw new KbException(KbCode.AUTH_NO_PERMISSION, "token invalid or expired").r("没有权限或令牌已过期").li();
        }
        return JsonUtils.toModel(cachedStr, TempToken.class);
    }

    public Integer getPushCount(String articleId) {
        return pushCountCache.get(articleId);
    }

    public void invalidPushCountCache(String articleId) {
        pushCountCache.invalidate(articleId);
    }
    // --------------------------------------------------

    private List<ArticleModel> getListFromDatabase(List<String> articleIds) {
        Set<String> channelKeys = new HashSet<>();
        List<ArticleModel> list = articleRepository.findByIdIn(articleIds).stream()
                .map(article -> {
                    if (NewsContentType.HTML.name().equals(article.getContentType())
                            || NewsContentType.HTML_NEO.name().equals(article.getContentType())
                            || NewsContentType.PLAIN_TEXT.name().equals(article.getContentType())
                    ) {
                        contentCache.putNullable(article.getId(), article.getContent());
                    }
                    article.setContent(null);
                    if (article.getChannelKey() != null) {
                        channelKeys.add(article.getChannelKey());
                    }
                    return Mapper.map(article, ArticleModel.class);
                })
                .collect(Collectors.toList());
        try {
            attachArticleStat(list, channelKeys);
        } catch (Exception e) {
            log.error("attach article stat fail", e);
        }
        return list;
    }

    private void attachArticleStat(List<ArticleModel> list, Set<String> channelKeys) {
        if (channelKeys.size() != 0) {
            Map<String, SensorsChannelModel> channelMap = channelService.getMapByKeySetFromCache(channelKeys).getData();
            for (ArticleModel article : list) {
                if (article.getChannelKey() != null) {
                    article.setChannel(channelMap.get(article.getChannelKey()));
                }
            }
        }
    }

    private Optional<String> getContentFromDatabase(String articleId) {
        return articleRepository.findById(articleId).map(NewsArticle::getContent);
    }

    private Map<String, String> getContentMapFromDatabase(Collection<String> articleIds) {
        return articleRepository.findByIdIn(articleIds).stream()
                .collect(Collectors.toMap(NewsArticle::getId, NewsArticle::getContent, (a1, a2) -> a1));
    }

    private Integer getPushCountFromDatabase(String articleId) {
        try {
            KbEntity<?> kbResponse = pushService.getPushListByReferenceId(articleId, null, 1, 1);
            return kbResponse.getTotalCount().intValue();
        } catch (Exception e) {
            log.error("getPushCountFromDatabase error", e);
            return 0;
        }
    }

    private final LoadingCache<Integer, NewsSiteConfig> siteConfigCache = Caffeine.newBuilder()
            .expireAfterWrite(Duration.ofMinutes(10))
            .refreshAfterWrite(Duration.ofSeconds(57))
            .build(new CacheLoader<Integer, NewsSiteConfig>() {
                @Override
                public NewsSiteConfig load(@NonNull Integer siteId) {
                    return siteConfigRepository.findFirstBySiteId(siteId).orElseGet(NewsSiteConfig::new);
                }

                @Override
                public NewsSiteConfig reload(@NonNull Integer siteId, @NonNull NewsSiteConfig oldValue) {
                    NewsSiteConfig config = load(siteId);
                    return config == null || config.getId() == null ? oldValue : config;
                }
            });

    private final LoadingCache<ModuleCacheKey, NewsModuleConfig> moduleConfigCache = Caffeine.newBuilder()
            .expireAfterWrite(Duration.ofMinutes(10))
            .refreshAfterWrite(Duration.ofSeconds(57))
            .build(new CacheLoader<ModuleCacheKey, NewsModuleConfig>() {
                @Override
                public NewsModuleConfig load(@NonNull ModuleCacheKey key) {
                    return moduleConfigRepository.findFirstBySiteIdAndModule(key.siteId, key.module)
                            .orElseGet(NewsModuleConfig::new);
                }

                @Override
                public NewsModuleConfig reload(@NonNull ModuleCacheKey key, @NonNull NewsModuleConfig oldValue) {
                    NewsModuleConfig config = load(key);
                    return config == null || config.getId() == null ? oldValue : config;
                }
            });

    @EqualsAndHashCode
    private static class ModuleCacheKey {
        private final Integer siteId;
        private final String module;

        public ModuleCacheKey(Integer siteId, String module) {
            this.siteId = siteId;
            this.module = module;
        }
    }

    @Data
    @NoArgsConstructor
    public static class TempToken {
        private String articleId;
        private String token;
        private Long dueTime;

        public TempToken(String articleId, String token, long dueTime) {
            this.articleId = articleId;
            this.token = token;
            this.dueTime = dueTime;
        }
    }

}
