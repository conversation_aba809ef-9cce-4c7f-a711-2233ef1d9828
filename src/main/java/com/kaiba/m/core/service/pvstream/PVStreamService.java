package com.kaiba.m.core.service.pvstream;

import com.kaiba.lib.base.response.KbCode;
import com.kaiba.lib.base.response.KbException;
import com.kaiba.lib.base.util.StringUtils;
import com.kaiba.m.core.domain.livestreaming.PVStream;
import com.kaiba.m.core.middleware.yunxin.*;
import com.kaiba.m.core.middleware.yunxin.constant.YXStatus;
import com.kaiba.m.core.repository.livestreaming.PVStreamRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.util.*;

@Slf4j
@Service
public class PVStreamService {

    private final StringRedisTemplate redisTemplate;
    private final PVStreamRepository streamRepository;

    public PVStreamService(StringRedisTemplate redisTemplate, PVStreamRepository streamRepository) {
        this.redisTemplate = redisTemplate;
        this.streamRepository = streamRepository;
    }

    public Page<PVStream> getStreamList(Integer page, Integer pageSize) {
        return streamRepository.findAll(PageRequest.of(page - 1, pageSize));
    }

    public Optional<PVStream> getPrimaryStreamBySiteId(Integer siteId) {
        return streamRepository.findFirstBySiteIdAndPrimaryIsTrue(siteId);
    }

    public List<PVStream> getStreamListBySiteId(Integer siteId) {
        return streamRepository.findAllBySiteId(siteId);
    }

    public Optional<PVStream> getStreamById(String id) {
        return streamRepository.findById(id);
    }

    public PVStream createOrEdit(PVStream stream) {
        String yxName = stream.getName() + "_" + stream.getSiteId();
        if (StringUtils.isEmpty(stream.getId())) {
            YXResponse<Channel> response = YunXinApi.addChannel(yxName);
            if (response.getCode() == 200) {
                Channel channel = response.getRet();
                stream.setChannelId(channel.getCid());
                stream.setPushUrl(channel.getPushUrl());
                stream.setHttpPullUrl(channel.getHttpPullUrl());
                stream.setRtmpPullUrl(channel.getRtmpPullUrl());
                stream.setHlsPullUrl(channel.getHlsPullUrl());

                stream.setDataSource(1);
                stream.setPrimary(!streamRepository.existsBySiteId(stream.getSiteId()));
                stream.setIdle(true);
                stream.setCreateTime(System.currentTimeMillis() / 1000);

                YunXinApi.autoRecordSwitch(stream.getChannelId(),1);
            } else {
                throw new KbException(KbCode.REQUEST_FAIL).setReadableMessage(response.getMsg()).li();
            }
        } else {
            YXResponse<Void> response = YunXinApi.updateChannel(stream.getChannelId(), yxName);
            if (response.getCode() != 200) {
                throw new KbException(KbCode.REQUEST_FAIL).setReadableMessage(response.getMsg()).li();
            }
        }
        YunXinApi.updateConfig(stream.getChannelId(),stream.getName(),stream.getSiteId());
        return streamRepository.save(stream);
    }

    public void deleteById(String id) {
        streamRepository.deleteById(id);
    }

    public YXPage<YXVideoRecord> getRecordList(String channelId, Integer page, Integer pageSize) {
        YXResponse<YXPage<YXVideoRecord>> response = YunXinApi.getRecordList(channelId, page, pageSize);
        if (response.getCode() == 200) {
            return response.getRet();
        } else {
            throw new KbException(KbCode.REQUEST_FAIL).setReadableMessage(response.getMsg()).li();
        }
    }

    public void autoRecordSwitch(){
        List<PVStream> list = streamRepository.findAll();
        list.forEach(t->{
            YunXinApi.updateConfig(t.getChannelId(),t.getName(),t.getSiteId());
        });
    }

    public String onlinePrimary(Integer siteId) {
        String url = redisTemplate.opsForValue().get(primaryKey(siteId));
        if (StringUtils.isEmpty(url)) {
            PVStream pvStream = streamRepository.findFirstBySiteIdAndPrimaryIsTrue(siteId).orElseThrow(()->new KbException(KbCode.RESOURCE_NOT_FOUND).li());
            redisTemplate.opsForValue().set(primaryKey(siteId), pvStream.getHlsPullUrl());
            return pvStream.getHlsPullUrl();
        } else {
            return url;
        }
    }

    public void offlinePrimary(Integer siteId) {
        redisTemplate.delete(primaryKey(siteId));
    }

    public String getOnlineStream(Integer siteId) {
        return redisTemplate.opsForValue().get(primaryKey(siteId));
    }

    public Optional<PVStream> applySecondaryStream(Integer siteId) {
        Optional<PVStream> optional;
        do {
            optional = streamRepository.applySecondary(siteId);
            if (optional.isPresent()) {
                YXResponse<Status> status = YunXinApi.channelStats(optional.get().getChannelId());
                if (status.getCode() == 200 && status.getRet().getStatus() == YXStatus.IDLE.getValue()) {
                    redisTemplate.opsForZSet().add(secondaryKey(), optional.get().getId(), System.currentTimeMillis());
                    return optional;
                }
            } else {
                return Optional.empty();
            }
        } while (true);
    }

    public Optional<PVStream> applyPrimaryStream(Integer siteId) {
        Optional<PVStream> optional = streamRepository.applyPrimary(siteId);
        if (optional.isPresent()) {
            YXResponse<Status> status = YunXinApi.channelStats(optional.get().getChannelId());
            if (status.getCode() == 200 && status.getRet().getStatus() == YXStatus.IDLE.getValue()) {
                redisTemplate.opsForZSet().add(secondaryKey(), optional.get().getId(), System.currentTimeMillis());
                return optional;
            }
        }
        return Optional.empty();
    }

    public void offline(String id) {
        streamRepository.updateIdle(id, true);
        redisTemplate.opsForZSet().remove(secondaryKey(), id);
    }

    public void liveReport(String id) {
        redisTemplate.opsForZSet().add(secondaryKey(), id, System.currentTimeMillis());
    }

    public void liveCheck() {
        long until = System.currentTimeMillis() - 65 * 1000;
        Set<String> idSet = redisTemplate.opsForZSet().rangeByScore(secondaryKey(), 0, until);
        if (idSet != null && idSet.size() > 0) {
            streamRepository.dispatchOffline(idSet);
            redisTemplate.opsForZSet().remove(secondaryKey(), idSet.toArray());
        }
    }

    private String primaryKey(Integer siteId) {
        return "pv_stream_primary_" + siteId;
    }

    private String secondaryKey() {
        return "pv_stream_occupy";
    }
}
