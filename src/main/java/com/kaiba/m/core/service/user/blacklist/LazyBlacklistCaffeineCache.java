package com.kaiba.m.core.service.user.blacklist;

import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.LoadingCache;
import com.kaiba.m.core.domain.user.UserBlacklist;
import com.kaiba.m.core.middleware.instantcache.KbInstantCaffeineService;
import com.kaiba.m.core.repository.user.UserBlacklistRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 黑名单缓存
 *
 * <AUTHOR>
 * @date 2022/03/08 13:41
 **/
@Slf4j
@Component
class LazyBlacklistCaffeineCache implements IBlacklistCache {

    private static final Duration CACHE_EXPIRE = Duration.ofMinutes(30);

    private final UserBlacklistRepository userBlacklistRepository;
    private final LoadingCache<Integer, Set<Integer>> userBlacklistCache;

    private LazyBlacklistCaffeineCache(
            UserBlacklistRepository userBlacklistRepository,
            KbInstantCaffeineService instantCaffeineService
    ) {
        this.userBlacklistRepository = userBlacklistRepository;
        this.userBlacklistCache = instantCaffeineService
                .registerInstantCache("user_blacklist_lazy_cache", Caffeine.newBuilder()
                        .expireAfterWrite(CACHE_EXPIRE)
                        .build(this::getUserBlacklistForCache));
    }

    @Override
    public Set<Integer> getBlacklist(Integer userId) {
        return userBlacklistCache.get(userId);
    }

    @Override
    public boolean isInBlacklist(Integer userId, Integer targetUserId) {
        Set<Integer> blacklist = userBlacklistCache.get(userId);
        return blacklist != null && blacklist.contains(targetUserId);
    }

    @Override
    public void addToBlacklist(Integer userId, Integer targetUserId) {
        userBlacklistCache.invalidate(userId);
    }

    @Override
    public void removeFromBlacklist(Integer userId, Integer targetUserId) {
        userBlacklistCache.invalidate(userId);
    }

    @Override
    public void updateBlacklist(Integer userId, List<Integer> targetUserIds) {
        userBlacklistCache.invalidate(userId);
    }

    @Override
    public void invalidBlacklist(Integer userId) {
        userBlacklistCache.invalidate(userId);
    }

    private Set<Integer> getUserBlacklistForCache(Integer userId) {
        return userBlacklistRepository.getByUid(userId, PageRequest.of(0, 100))
                .stream().map(UserBlacklist::getBid)
                .collect(Collectors.toSet());
    }

}
