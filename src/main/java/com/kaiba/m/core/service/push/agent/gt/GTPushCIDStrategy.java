package com.kaiba.m.core.service.push.agent.gt;

import com.getui.push.v2.sdk.api.PushApi;
import com.getui.push.v2.sdk.common.ApiResult;
import com.getui.push.v2.sdk.dto.req.Audience;
import com.getui.push.v2.sdk.dto.req.message.PushDTO;
import com.kaiba.lib.base.constant.KbPushChannel;
import com.kaiba.lib.base.constant.push.PushRange;
import com.kaiba.lib.base.domain.push.PushModel;
import com.kaiba.lib.base.response.KbCode;
import com.kaiba.lib.base.response.KbException;
import com.kaiba.lib.base.util.GsonUtils;
import com.kaiba.m.core.domain.push.PushTarget;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version GTPushAliasStrategy, v0.1 2024/2/2 14:01 daopei Exp $
 **/
@Slf4j
@Service
public class GTPushCIDStrategy implements GTPushStrategy {

    @Override
    public boolean support(PushRange range) {
        return range == PushRange.USER;
    }

    @Override
    public GTPushResult execute(PushApi pushApi, PushModel model, List<PushTarget> targetList) {
        Audience audience = new Audience();
        List<String> cidList = new ArrayList<>();
        for (PushTarget pushTarget : targetList) {
            cidList.add(pushTarget.getPushToken());
        }
        if (cidList.isEmpty()) {
            log.warn("个推推送未找到有效推送用户,push:{},target:{}",
                    GsonUtils.getGson().toJson(model),
                    GsonUtils.getGson().toJson(targetList));
            return null;
        }
        audience.setCid(cidList);
        PushDTO<Audience> pushDTO = new PushDTO<>();
        GTPushHelper.wrapperRequest(pushDTO, model, audience);
        ApiResult<Map<String, Map<String, String>>> result = pushApi.pushToSingleByCid(pushDTO);
        String taskId = GTPushHelper.parseMapTaskId(result);
        return GTPushHelper.map2Result(result, taskId);
    }
}
