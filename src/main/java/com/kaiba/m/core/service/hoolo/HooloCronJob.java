package com.kaiba.m.core.service.hoolo;

import com.google.gson.JsonObject;
import com.kaiba.lib.base.constant.KbProperties;
import com.kaiba.m.core.service.hoolo.programme.HooloNewsService;
import com.kaiba.m.core.util.JsonUtils;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2022/08/04 15:42
 **/
@Slf4j
@Component
public class HooloCronJob {

    private final HooloNewsService hooloNewsService;

    public HooloCronJob(HooloNewsService newsService) {
        this.hooloNewsService = newsService;
    }

    @XxlJob("hoolo-programme-news-sync")
    public ReturnT<String> programmeNewsSync(String param) {
        if (StringUtils.isNotBlank(param)) {
            JsonObject object = JsonUtils.getGson().fromJson(param, JsonObject.class);
            int syncCount = object.has("syncCount") ? object.get("syncCount").getAsInt() : 300;
            long limitTime = object.has("limitTime") ? object.get("limitTime").getAsLong()
                : System.currentTimeMillis() - 30 * 24 * 3600 * 1000L;
            if (object.has("pId")) {
                String programmeId = object.get("pId").getAsString();
                hooloNewsService.requestProgrammeNews(programmeId, syncCount,
                    KbProperties.ADMIN_USER_ID, limitTime);
            }
        } else {
            hooloNewsService.batchSyncProgrammeNews(KbProperties.ADMIN_USER_ID);
        }
        return ReturnT.SUCCESS;
    }
}
