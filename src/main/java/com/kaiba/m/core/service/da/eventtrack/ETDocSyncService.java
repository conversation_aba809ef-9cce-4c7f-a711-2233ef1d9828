package com.kaiba.m.core.service.da.eventtrack;

import com.kaiba.lib.base.constant.news.NewsDepartment;
import com.kaiba.lib.base.domain.common.KbTimeRange;
import com.kaiba.lib.base.response.KbCode;
import com.kaiba.lib.base.response.KbException;
import com.kaiba.lib.base.util.GsonUtils;
import com.kaiba.m.core.constant.da.eventtrack.ETDocSyncStrategyType;
import com.kaiba.m.core.domain.da.eventtrack.ETDocDepart;
import com.kaiba.m.core.domain.da.eventtrack.ETDocModule;
import com.kaiba.m.core.domain.da.eventtrack.ETDocModuleArticleRule;
import com.kaiba.m.core.domain.da.eventtrack.ETDocSyncCursor;
import com.kaiba.m.core.middleware.DistributedLock;
import com.kaiba.m.core.repository.da.eventtrack.ETDocDepartRepository;
import com.kaiba.m.core.repository.da.eventtrack.ETDocModuleArticleRuleRepository;
import com.kaiba.m.core.repository.da.eventtrack.ETDocModuleRepository;
import com.kaiba.m.core.repository.da.eventtrack.ETDocSyncCursorRepository;
import com.kaiba.m.core.service.da.eventtrack.strategy.ETDocSyncStrategyFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * 业务数据生成标准稿件服务同步服务
 *
 * <AUTHOR>
 * @version ETDocSyncService, v0.1 2025/3/26 10:42 daopei Exp $
 **/
@Slf4j
@Service
public class ETDocSyncService {


    private final ETDocModuleRepository moduleRepository;
    private final ETDocDepartRepository departRepository;
    private final ETDocModuleArticleRuleRepository articleRuleRepository;
    private final ETDocSyncCursorRepository syncCursorRepository;

    private final ETDocSyncStrategyFactory syncStrategyFactory;
    private final DistributedLock syncLock;

    private final static String SYNC_LOCK = "et_doc_sync_lock";
    private final static String SYNC_STRATEGY_LOCK = "et_doc_sync_lock_by_";
    private final static int SYNC_LOCK_TTL = 2 * 60 * 60 * 1000;
    private final static int SYNC_STRATEGY_LOCK_TTL = 20 * 60 * 1000;

    private final ExecutorService executors = Executors.newFixedThreadPool(2);

    public ETDocSyncService(
            ETDocModuleRepository moduleRepository,
            ETDocDepartRepository departRepository,
            ETDocModuleArticleRuleRepository articleRuleRepository,
            ETDocSyncCursorRepository syncCursorRepository,
            ETDocSyncStrategyFactory syncStrategyFactory,
            StringRedisTemplate redisTemplate
    ) {
        this.moduleRepository = moduleRepository;
        this.departRepository = departRepository;
        this.articleRuleRepository = articleRuleRepository;
        this.syncCursorRepository = syncCursorRepository;
        this.syncStrategyFactory = syncStrategyFactory;
        this.syncLock = new DistributedLock(redisTemplate);
    }

    // 标准稿件同步 -----------------------------------------------------------

    /**
     * 谨慎使用
     * 全业务类型和全时间范围同步
     */
    public void syncAll() {
        if (syncLock.isLocked(SYNC_LOCK)) {
            throw new KbException(KbCode.ILLEGAL_STATE, "sync all is already in executing, wait a moment").li();
        }
        CompletableFuture.runAsync(() -> {
            long start = System.currentTimeMillis();
            try {
                if (syncLock.tryGetDistributedLock(SYNC_LOCK, SYNC_LOCK_TTL)) {
                    for (ETDocSyncStrategyType strategy : ETDocSyncStrategyType.values()) {
                        try {
                            syncStrategyByAll(strategy.name());
                        } catch (Exception e) {
                            log.error("sync strategy fail:{}, e:{}", strategy, e.getMessage());
                        }
                    }
                }
            } catch (Exception e) {
                log.error("Error during syncAll execution", e);
            } finally {
                syncLock.releaseDistributedLock(SYNC_LOCK);
                long end = System.currentTimeMillis();
                log.info("sync all finished, cost:{}ms", end - start);
            }
        }, executors);
    }

    /**
     * 谨慎使用
     * 根据策略全量同步数据
     * @param strategy
     */
    public void syncStrategyByAll(String strategy) {
        ETDocSyncStrategyType strategyType = ETDocSyncStrategyType.resolverByName(strategy).orElseThrow(() -> new KbException(KbCode.ILLEGAL_ARGUMENT, "strategy should not be null"));
        String lockMark = getSyncStrategyLockMark(strategy);
        if (syncLock.tryGetDistributedLock(lockMark, SYNC_STRATEGY_LOCK_TTL)) {
            try {
                long strategyStart = System.currentTimeMillis();
                syncStrategyFactory.execute(strategyType.name());
                long strategyEnd = System.currentTimeMillis();
                log.info("sync strategy {} finished, cost:{}ms", strategyType.name(), strategyEnd - strategyStart);
            } finally {
                syncLock.releaseDistributedLock(lockMark);
            }
        }
    }

    /**
     * 指定业务和时间范围同步
     * 手工调用不刷新游标时间
     * @param strategy
     * @param startTime
     * @param endTime
     */
    public void syncStrategyByTime(String strategy, Long startTime, Long endTime) {
        ETDocSyncStrategyType.resolverByName(strategy).orElseThrow(() -> new KbException(KbCode.ILLEGAL_ARGUMENT, "strategy should not be null"));
        String strategyLockMark = getSyncStrategyLockMark(strategy);
        boolean locked = syncLock.isLocked(strategyLockMark);
        if (locked) {
            throw new KbException(KbCode.ILLEGAL_STATE, "sync strategy " + strategy + " is already in executing, wait a moment").li();
        }
        //异步线程执行
        CompletableFuture.runAsync(() -> {
            try {
                if (syncLock.tryGetDistributedLock(strategyLockMark, SYNC_STRATEGY_LOCK_TTL)) {
                    long strategyStart = System.currentTimeMillis();
                    log.info("sync strategy {} start by time : {},{}", strategy, startTime, endTime);
                    KbTimeRange timeRange = KbTimeRange.rangeClosed(startTime, endTime);
                    syncStrategyFactory.executeByTimeRange(strategy, timeRange, false);
                    long strategyEnd = System.currentTimeMillis();
                    log.info("sync strategy {} finished by time, cost:{}ms", strategy, strategyEnd - strategyStart);
                }
            } catch (Exception e) {
                e.printStackTrace();
                log.error("sync strategy fail:{}, e:{}", strategy, e.toString());
            } finally {
                syncLock.releaseDistributedLock(strategyLockMark);
            }
        }, executors);
    }

    /**
     * 稿件同步策略(每日类型) 同步
     */
    public void syncStrategyByDAY() {
        List<ETDocSyncStrategyType> executeStrategy = ETDocSyncStrategyType.getDailySyncTypes();
        syncByStrategyAuto(executeStrategy);
    }

    /**
     * 稿件同步策略(小时类型) 同步
     */
    public void syncStrategyByHOUR() {
        List<ETDocSyncStrategyType> executeStrategy = ETDocSyncStrategyType.getHourlySyncTypes();
        syncByStrategyAuto(executeStrategy);
    }

    /**
     * 检查是否正在执行该策略
     * @param strategy
     * @return
     */
    public boolean checkSyncLock(String strategy) {
        String strategyLockMark = SYNC_STRATEGY_LOCK + strategy;
        return syncLock.isLocked(strategyLockMark);
    }

    /**
     * 根据同步时间游标自动批量同步稿件
     * 自动同步,触发策略游标时间更新
     * @param executeStrategy
     */
    private void syncByStrategyAuto(List<ETDocSyncStrategyType> executeStrategy) {
        CompletableFuture.runAsync(() -> {
            long start = System.currentTimeMillis();
            log.info("sync strategy start for :{}", executeStrategy);
            for (ETDocSyncStrategyType strategy : executeStrategy) {
                String strategyLockMark = getSyncStrategyLockMark(strategy.name());
                try {
                    Long baseTime = syncCursorRepository.findByStrategy(strategy.name())
                            .map(ETDocSyncCursor::getTimestamp)
                            .orElse(0L);
                    if (syncLock.tryGetDistributedLock(strategyLockMark, SYNC_STRATEGY_LOCK_TTL)) {
                        long strategyStart = System.currentTimeMillis();
                        log.info("sync strategy {} start by auto : {}", strategy, baseTime);
                        KbTimeRange timeRange = KbTimeRange.rangeClosed(baseTime, System.currentTimeMillis());
                        syncStrategyFactory.executeByTimeRange(strategy.name(), timeRange,  true);
                        long strategyEnd = System.currentTimeMillis();
                        log.info("sync strategy {} finished by auto, cost:{}ms", strategy, strategyEnd - strategyStart);
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                    log.error("sync strategy fail:{}, e:{}", strategy, e.toString());
                } finally {
                    syncLock.releaseDistributedLock(strategyLockMark);
                }
            }
            long end = System.currentTimeMillis();
            log.info("sync all finished, cost:{}ms", end - start);
        }, executors);
    }


    // ------------------------------------------------------------

    private String getSyncStrategyLockMark(String strategy) {
        return SYNC_STRATEGY_LOCK + strategy;
    }




    // 默认基础配置初始化 (稿件模块规则、稿件单位、稿件模块)-----------------------------------------------------------

    private static final Map<String, String> NEWS_GROUP_GETUI_EVENT_MAP = new HashMap<>();

    static {
        NEWS_GROUP_GETUI_EVENT_MAP.put("news_main_list.9", "da_news");

        NEWS_GROUP_GETUI_EVENT_MAP.put("headline.9", "da_headline");
        NEWS_GROUP_GETUI_EVENT_MAP.put("home_article_banner.9", "da_headline");
        NEWS_GROUP_GETUI_EVENT_MAP.put("home_article_list_top.9", "da_headline");
        NEWS_GROUP_GETUI_EVENT_MAP.put("channel_main_page_tab__all_HTV1", "da_headline");
        NEWS_GROUP_GETUI_EVENT_MAP.put("inhz_main_news", "da_in_hz");

        NEWS_GROUP_GETUI_EVENT_MAP.put("channel_main_page_tab__all_HZ_SC", "da_county");
        NEWS_GROUP_GETUI_EVENT_MAP.put("channel_main_page_tab__all_HZ_GS", "da_county");
        NEWS_GROUP_GETUI_EVENT_MAP.put("channel_main_page_tab__all_HZ_XH", "da_county");
        NEWS_GROUP_GETUI_EVENT_MAP.put("channel_main_page_tab__all_HZ_BJ", "da_county");
        NEWS_GROUP_GETUI_EVENT_MAP.put("channel_main_page_tab__all_HZ_QT", "da_county");
        NEWS_GROUP_GETUI_EVENT_MAP.put("channel_main_page_tab__all_HZ_LP", "da_county");
        NEWS_GROUP_GETUI_EVENT_MAP.put("channel_main_page_tab__all_HZ_XS", "da_county");
        NEWS_GROUP_GETUI_EVENT_MAP.put("channel_main_page_tab__all_HZ_YH", "da_county");
        NEWS_GROUP_GETUI_EVENT_MAP.put("channel_main_page_tab__all_HZ_FY", "da_county");
        NEWS_GROUP_GETUI_EVENT_MAP.put("channel_main_page_tab__all_HZ_LA", "da_county");
        NEWS_GROUP_GETUI_EVENT_MAP.put("channel_main_page_tab__all_HZ_TL", "da_county");
        NEWS_GROUP_GETUI_EVENT_MAP.put("channel_main_page_tab__all_HZ_CA", "da_county");
        NEWS_GROUP_GETUI_EVENT_MAP.put("channel_main_page_tab__all_HZ_JD", "da_county");

        NEWS_GROUP_GETUI_EVENT_MAP.put("programme_episodes_670ddb861de456527d1fb22e", "da_ai_news");
        NEWS_GROUP_GETUI_EVENT_MAP.put("67515b7826cd347a12c63f78", "da_ai_news");
        NEWS_GROUP_GETUI_EVENT_MAP.put("6757e6dae8f8622bf43cf7ec", "da_ai_news");

        NEWS_GROUP_GETUI_EVENT_MAP.put("66dea5262f3ac90cc90668c7_group_key", "da_jz_said");

        NEWS_GROUP_GETUI_EVENT_MAP.put("programme_episodes_67074473c8ffba44652f7252", "da_htv1_news");
        //新杭州 新观察专题
        NEWS_GROUP_GETUI_EVENT_MAP.put("66a21b43f5b8dc64b373dc32_group_key", "da_xhz_xgc");
    }


    /**
     * 整理稿件标签列表:
     * <p>
     * * 主要业务模块
     * <p>
     * * da_news  // 资讯
     * * da_drama  // 超有戏
     * * da_activity  // 活动 (杭有礼)
     * * da_service  // 服务
     * * da_headline  // 首页 (时政首页)
     * * da_circle  // 圈子
     * * da_circle_kol  // 达人圈子
     * * 时政首页业务模块
     * <p>
     * * da_in_hz  // In杭州
     * * da_ai_news  // AI快报
     * * da_htv1_news  // 新闻联播
     * * da_jz_said  // 局长如是说
     * * da_jcwg  // 精彩文广
     * * da_hear_hz  // 听见杭州
     * * da_headline_exclude  // 时政首页排除新闻联播内容
     * * 其他业务模块
     * * da_jcwg  // 精彩文广
     * * 看见杭州
     * * 区县市标签
     * * hzc_sc  // 上城区
     * * hzc_gs  // 拱墅区
     * * hzc_xh  // 西湖区
     * * hzc_bj  // 滨江区
     * * hzc_qt  // 钱塘区
     * * hzc_lp  // 临平区
     * * hzc_xs  // 萧山区
     * * hzc_yh  // 余杭区
     * * hzc_fy  // 富阳区
     * * hzc_la  // 临安区
     * * hzc_tl  // 桐庐县
     * * hzc_ca  // 淳安县
     * * hzc_jd  // 建德市
     */
    public void initModule() {
        List<ETDocModule> modules = new ArrayList<>();
        ETDocModule daNews = new ETDocModule();
        daNews.setModule("da_news");
        daNews.setName("资讯");
        modules.add(daNews);

        ETDocModule daDrama = new ETDocModule();
        daDrama.setModule("da_drama");
        daDrama.setName("超有戏");
        modules.add(daDrama);

        ETDocModule daActivity = new ETDocModule();
        daActivity.setModule("da_activity");
        daActivity.setName("活动(杭有礼)");
        modules.add(daActivity);

        ETDocModule daHbx = new ETDocModule();
        daHbx.setModule("da_hangbangxia");
        daHbx.setName("杭帮侠");
        modules.add(daHbx);

        ETDocModule daJhk = new ETDocModule();
        daJhk.setModule("da_mini_theatre");
        daJhk.setName("剧好看");
        modules.add(daJhk);

        ETDocModule daService = new ETDocModule();
        daService.setModule("da_service");
        daService.setName("服务");
        modules.add(daService);

        ETDocModule daHeadline = new ETDocModule();
        daHeadline.setModule("da_headline");
        daHeadline.setName("首页(时政首页)");
        modules.add(daHeadline);

        ETDocModule daCircle = new ETDocModule();
        daCircle.setModule("da_circle");
        daCircle.setName("圈子");
        modules.add(daCircle);

        ETDocModule daCircleKol = new ETDocModule();
        daCircleKol.setModule("da_circle_kol");
        daCircleKol.setName("达人圈子");
        modules.add(daCircleKol);

        // 时政首页业务模块
        ETDocModule daInHz = new ETDocModule();
        daInHz.setModule("da_in_hz");
        daInHz.setName("In杭州");
        modules.add(daInHz);

        ETDocModule daAiNews = new ETDocModule();
        daAiNews.setModule("da_ai_news");
        daAiNews.setName("AI快报");
        modules.add(daAiNews);

        ETDocModule daHtv1News = new ETDocModule();
        daHtv1News.setModule("da_htv1_news");
        daHtv1News.setName("新闻联播");
        modules.add(daHtv1News);

        ETDocModule daXhzxgc = new ETDocModule();
        daXhzxgc.setModule("da_xhz_xgc");
        daXhzxgc.setName("新杭州新观察");
        modules.add(daXhzxgc);

        ETDocModule daJzSaid = new ETDocModule();
        daJzSaid.setModule("da_jz_said");
        daJzSaid.setName("局长如是说");
        modules.add(daJzSaid);

        ETDocModule daJcwg = new ETDocModule();
        daJcwg.setModule("da_jcwg");
        daJcwg.setName("精彩文广");
        modules.add(daJcwg);

        ETDocModule daHearHz = new ETDocModule();
        daHearHz.setModule("da_hear_hz");
        daHearHz.setName("听见杭州");
        modules.add(daHearHz);

        ETDocModule daHeadlineExclude = new ETDocModule();
        daHeadlineExclude.setModule("da_headline_exclude");
        daHeadlineExclude.setName("时政首页排除新闻联播内容");
        modules.add(daHeadlineExclude);

        // 区县市标签
        ETDocModule hzcSc = new ETDocModule();
        hzcSc.setModule("hzc_sc");
        hzcSc.setName("上城区");
        modules.add(hzcSc);

        ETDocModule hzcGs = new ETDocModule();
        hzcGs.setModule("hzc_gs");
        hzcGs.setName("拱墅区");
        modules.add(hzcGs);

        ETDocModule hzcXh = new ETDocModule();
        hzcXh.setModule("hzc_xh");
        hzcXh.setName("西湖区");
        modules.add(hzcXh);

        ETDocModule hzcBj = new ETDocModule();
        hzcBj.setModule("hzc_bj");
        hzcBj.setName("滨江区");
        modules.add(hzcBj);

        ETDocModule hzcQt = new ETDocModule();
        hzcQt.setModule("hzc_qt");
        hzcQt.setName("钱塘区");
        modules.add(hzcQt);

        ETDocModule hzcLp = new ETDocModule();
        hzcLp.setModule("hzc_lp");
        hzcLp.setName("临平区");
        modules.add(hzcLp);

        ETDocModule hzcXs = new ETDocModule();
        hzcXs.setModule("hzc_xs");
        hzcXs.setName("萧山区");
        modules.add(hzcXs);

        ETDocModule hzcYh = new ETDocModule();
        hzcYh.setModule("hzc_yh");
        hzcYh.setName("余杭区");
        modules.add(hzcYh);

        ETDocModule hzcFy = new ETDocModule();
        hzcFy.setModule("hzc_fy");
        hzcFy.setName("富阳区");
        modules.add(hzcFy);

        ETDocModule hzcLa = new ETDocModule();
        hzcLa.setModule("hzc_la");
        hzcLa.setName("临安区");
        modules.add(hzcLa);

        ETDocModule hzcTl = new ETDocModule();
        hzcTl.setModule("hzc_tl");
        hzcTl.setName("桐庐县");
        hzcTl.setCategory(Collections.singleton("test_category"));
        modules.add(hzcTl);

        ETDocModule hzcCa = new ETDocModule();
        hzcCa.setModule("hzc_ca");
        hzcCa.setName("淳安县");
        hzcCa.setCategory(Collections.singleton("test_category"));
        modules.add(hzcCa);

        ETDocModule hzcJd = new ETDocModule();
        hzcJd.setModule("hzc_jd");
        hzcJd.setName("建德市");
        hzcJd.setCategory(Collections.singleton("test_category"));
        modules.add(hzcJd);

        // 保存所有模块
        log.info("module init start ======================");
        for (ETDocModule module : modules) {
            module = moduleRepository.upsert(module);
            log.info(GsonUtils.getGson().toJson(module));
        }
        log.info("module init end ======================");
    }

    public void initDepart() {
        log.info("depart init start ======================");
        for (NewsDepartment department : NewsDepartment.values()) {
            ETDocDepart depart = new ETDocDepart();
            depart.setDepart(department.name());
            depart.setName(department.getName());
            depart.setChannel(department.getChannel().name());
            depart = departRepository.upsert(depart);
            log.info(GsonUtils.getGson().toJson(depart));
        }
        log.info("depart init start ======================");
    }

    public void initArticleRule() {
        for (String group : NEWS_GROUP_GETUI_EVENT_MAP.keySet()) {
            ETDocModuleArticleRule rule = new ETDocModuleArticleRule();
            rule.setGroupKey(group);
            rule.setModules(Collections.singleton(NEWS_GROUP_GETUI_EVENT_MAP.get(group)));
            rule.setName(group);
            articleRuleRepository.upsert(rule);
        }
    }

    public static void main(String[] args){
        for (String group : NEWS_GROUP_GETUI_EVENT_MAP.keySet()) {
            ETDocModuleArticleRule rule = new ETDocModuleArticleRule();
            rule.setGroupKey(group);
            rule.setModules(Collections.singleton(NEWS_GROUP_GETUI_EVENT_MAP.get(group)));
            rule.setName(group);
            System.out.println(GsonUtils.getGson().toJson(rule));
        }
    }


}
