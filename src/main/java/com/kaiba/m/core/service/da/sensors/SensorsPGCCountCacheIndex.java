package com.kaiba.m.core.service.da.sensors;

import lombok.Getter;

/**
 * <AUTHOR>
 * @version SensorsPGCCountCacheIndex, v0.1 2024/1/2 15:48 daopei Exp $
 **/
@Getter
public enum SensorsPGCCountCacheIndex implements RedisHashObjAbility {
    viewCount("viewCount"),
    share("share"),
    reply("reply"),
    note("note"),
    noteAudio("noteAudio"),
    noteVideo("noteVideo"),
    noteImage("noteImage"),
    noteText("noteText"),
    comment("comment"),
    commentText("commentText"),
    like("like"),
    reward("reward"),
    ;

    private String field;

    SensorsPGCCountCacheIndex(String field) {
        this.field = field;
    }
}
