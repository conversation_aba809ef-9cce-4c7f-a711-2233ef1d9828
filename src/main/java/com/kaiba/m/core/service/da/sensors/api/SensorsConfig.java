package com.kaiba.m.core.service.da.sensors.api;

import com.sensorsdata.analytics.javasdk.ISensorsAnalytics;
import com.sensorsdata.analytics.javasdk.SensorsAnalytics;
import com.sensorsdata.analytics.javasdk.consumer.BatchConsumer;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @date 2024/02/01 18:21
 */
@Configuration
public class SensorsConfig {

    private static final String SERVER_URL_PREFIX = "https://reporter-sensors.kaiba315.com.cn/sa?project=";

    @Value("${kaiba.sensors.project:default}")
    private String projectName;

    @Bean(destroyMethod = "shutdown")
    public ISensorsAnalytics init() {
        String serverUrl = SERVER_URL_PREFIX + projectName;
        // 测试环境debug使用
        //return new SensorsAnalytics(new DebugConsumer(serverUrl, true));
        return new SensorsAnalytics(new BatchConsumer(serverUrl));
    }
}
