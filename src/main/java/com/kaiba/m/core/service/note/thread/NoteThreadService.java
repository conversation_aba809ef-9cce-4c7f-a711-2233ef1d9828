package com.kaiba.m.core.service.note.thread;

import com.kaiba.lib.base.constant.note.NoteDefaults;
import com.kaiba.lib.base.constant.note.NoteListStyle;
import com.kaiba.lib.base.constant.note.NoteThreadCondition;
import com.kaiba.lib.base.domain.common.ShareModel;
import com.kaiba.lib.base.domain.note.NoteThreadPostRule;
import com.kaiba.lib.base.response.KbCode;
import com.kaiba.lib.base.response.KbException;
import com.kaiba.lib.base.util.StringUtils;
import com.kaiba.m.core.domain.note.Note;
import com.kaiba.m.core.domain.note.NoteThread;
import com.kaiba.m.core.domain.note.NoteThreadHot;
import com.kaiba.m.core.domain.note.NoteThreadTop;
import com.kaiba.m.core.service.note.NoteCacheConfig;
import com.kaiba.m.core.util.redis.AbsObjectListCache;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;
import rx.Observable;
import rx.schedulers.Schedulers;
import rx.subjects.PublishSubject;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * author: lyux
 * date: 19-6-3
 */
@Slf4j
@Service
public class NoteThreadService {

    private final NoteThreadDBService dbService;
    private final NoteThreadCacheService cacheService;
    private final NoteThreadHotCacheService hotCacheService;
    private final PublishSubject<String> refreshHotSubject;

    public NoteThreadService(
            NoteThreadDBService dbService,
            NoteThreadCacheService cacheService,
            NoteThreadHotCacheService hotCacheService
    ) {
        this.dbService = dbService;
        this.cacheService = cacheService;
        this.hotCacheService = hotCacheService;
        this.refreshHotSubject = PublishSubject.create();
        this.refreshHotSubject
                .window(5, TimeUnit.SECONDS)
                .subscribeOn(Schedulers.io())
                .flatMap(Observable::toList)
                .doOnError(e -> log.warn("refresh hot note error", e))
                .subscribe(this::threadNoteRefreshHots);
    }

    public Optional<NoteThread> getThreadByKey(String key) {
        return dbService.getThreadByKey(key);
    }

    public Optional<NoteThread> getThreadById(String threadId) {
        return dbService.getThreadById(threadId);
    }

    public Optional<NoteThread> getThreadByIdAllowCache(String threadId) {
        Optional<NoteThread> opFromCache = cacheService.getThread(threadId);
        if (opFromCache.isPresent()) {
            return opFromCache;
        } else {
            if (NoteCacheConfig.CACHE_DEBUG) {
                log.info("[cache_debug][db] get thread from db: " + threadId);
            }
            Optional<NoteThread> opFromDB = dbService.getThreadById(threadId);
            opFromDB.ifPresent(cacheService::updateThread);
            return opFromDB;
        }
    }

    public List<NoteThread> getThreadListIn(List<String> threadIds) {
        return dbService.getThreadListIn(threadIds);
    }

    public List<NoteThread> getThreadListInAllowCache(List<String> threadIds) {
        return threadListCache.getListIn(threadIds);
    }

    public List<NoteThread> getThreadListIn(String[] threadIds) {
        return dbService.getThreadListIn(Arrays.asList(threadIds));
    }

    public Page<NoteThread> getThreadListBySite(Integer siteId, Integer page, Integer pageSize) {
        return dbService.getThreadListBySite(siteId, page, pageSize);
    }

    public List<NoteThread> getThreadListByRoute(String threadId, Integer page, Integer pageSize) {
        return dbService.getThreadListByRoute(threadId, page, pageSize);
    }

    public List<NoteThread> searchThreadListByTitle(String title, Integer page, Integer pageSize) {
        return dbService.searchThreadListByTitle(title, page, pageSize);
    }

    public Optional<NoteThreadHot> getNoteHotById(String id) {
        return dbService.getNoteHotById(id);
    }

    public Optional<NoteThreadHot> getNoteHotByThreadAndNote(String threadId, String noteId) {
        return dbService.getNoteHotByThreadAndNote(threadId, noteId);
    }

    public List<NoteThreadHot> getNoteHotListByThread(String threadId, Integer page, Integer pageSize) {
        return dbService.getNoteHotListByThread(threadId, page, pageSize).getContent();
    }

    public List<NoteThreadHot> getNoteHotListByThreadAllowCache(String threadId, Integer page, Integer pageSize) {
        int p = page == null ? 1 : page;
        int ps = pageSize == null ? 15 : pageSize;
        if (p * ps >= NoteCacheConfig.THREAD_HOT_LIST_INIT_COUNT) {
            // 所请求页不在缓存范围内, 从库中读取
            if (NoteCacheConfig.CACHE_DEBUG) {
                log.info("[cache_debug][db] " +
                        "hot list cache not available for page " + ", [" + p + "," + ps + "]");
            }
            return dbService.getNoteHotListByThread(threadId, p, ps).getContent();
        }

        // 尝试从缓存读取
        Optional<List<NoteThreadHot>> opIdList = hotCacheService.getHotList(threadId, p, ps);
        if (opIdList.isPresent()) {
            return opIdList.get();
        } else {
            if (NoteCacheConfig.CACHE_DEBUG) {
                log.info("[cache_debug][db] about to populate hot list for thread: "
                        + threadId + ", runtime: " + Thread.currentThread().getId());
            }
            List<NoteThreadHot> list = dbService
                    .getNoteHotListByThread(threadId, 1, NoteCacheConfig.THREAD_HOT_LIST_INIT_COUNT)
                    .getContent();
            hotCacheService.populateAsync(threadId, list);
            if (list.size() == 0 || (p == 1 && list.size() < ps)) {
                return list;
            } else {
                int subListStart = (p - 1) * ps;
                int subListEnd = p * ps;
                if (list.size() < subListEnd) {
                    subListEnd = list.size();
                }
                return list.subList(subListStart, subListEnd);
            }
        }
    }

    public List<NoteThreadHot> getNoteHotListByNoteId(String noteId) {
        return dbService.getNoteHotListByNoteId(noteId);
    }

    public List<String> getNoteTopListByThread(String threadId) {
        return dbService.getNoteTopListByThread(threadId, 1, NoteDefaults.THREAD_MAX_TOP)
                .map(NoteThreadTop::getNoteId)
                .getContent();
    }

    public List<NoteThreadTop> getNoteTopListByThreadIn(List<String> threadIdList) {
        return dbService.getNoteTopListByThreadIn(threadIdList);
    }

    public List<NoteThreadTop> getNoteTopListByNoteId(String noteId) {
        return dbService.getNoteTopListByNoteId(noteId);
    }

    public List<String> getNoteTopListByThreadAllowCache(String threadId) {
        Optional<List<String>> op = cacheService.getTopNoteIdList(threadId);
        if (op.isPresent()) {
            return op.get();
        } else {
            List<String> list = dbService
                    .getNoteTopListByThread(threadId, 1, NoteDefaults.THREAD_MAX_TOP)
                    .map(NoteThreadTop::getNoteId)
                    .getContent();
            cacheService.setTopNoteIdList(threadId, list);
            return list;
        }
    }

    // ----------------------------------------------------------------------
    // 帖子板块管理

    public NoteThreadPostRule getThreadPostRuleById(String threadId) {
        NoteThreadPostRule rule = cacheService.getPostRuleById(threadId);
        if (null == rule) {
            if (NoteCacheConfig.CACHE_DEBUG) {
                log.info("[cache_debug][db] get thread from db: " + threadId);
            }
            NoteThread thread = dbService.getThreadById(threadId)
                    .orElseThrow(() -> new KbException(KbCode.NOTE_THREAD_NOT_EXISTS));
            if (thread.getPostRule() == null) {
                thread.setPostRule(NoteDefaults.DEFAULT_POST_RULE);
            }
            cacheService.updateThread(thread);
            rule = thread.getPostRule();
        }
        return rule;
    }

    public NoteThread threadCreate(NoteThread thread) {
        verifyThread(thread);
        if (thread.getKey() != null && dbService.isThreadExistsByKey(thread.getKey())) {
            throw new KbException(KbCode.ILLEGAL_ARGUMENT, "thread key already exists: " + thread.getKey()).li();
        }
        return dbService.threadCreate(thread);
    }

    public void threadNoteRefreshHot(String threadId, String noteId) {
        dbService.getNoteHotByThreadAndNote(threadId, noteId).ifPresent(hotCacheService::removeHot);
        NoteThreadHot hot = dbService.threadNoteRefreshHot(threadId, noteId);
        hotCacheService.addHot(hot);
    }

    public void threadNoteRefreshHotAsync(String noteId) {
        refreshHotSubject.onNext(noteId);
    }

    private void threadNoteRefreshHots(List<String> noteIds) {
        if (noteIds == null || noteIds.isEmpty()) {
            return;
        }
        List<String> ids = noteIds.stream().distinct().collect(Collectors.toList());
        List<NoteThreadHot> hotNotes = dbService.getNoteHotListByNoteIds(ids);
        if (hotNotes != null) {
            long now = System.currentTimeMillis();
            for (NoteThreadHot hotNote : hotNotes) {
                hotCacheService.removeHot(hotNote);
            }
            dbService.threadNoteRefreshHots(ids, now);
            for (NoteThreadHot hotNote : hotNotes) {
                hotNote.setRefreshTime(now / 1000);
                hotNote.setRefreshTimeMS(now);
                hotCacheService.addHot(hotNote);
            }
        }
    }

    public void threadNoteToHot(Note note, String threadId, String title, String cover) {
        dbService.getNoteHotByThreadAndNote(threadId, note.getId()).ifPresent(hotCacheService::removeHot);
        NoteThreadHot hot = dbService.threadNoteToHot(note, threadId, title, cover);
        hotCacheService.addHot(hot);
    }

    public void threadNoteCancelHot(Note note, String threadId) {
        dbService.getNoteHotByThreadAndNote(threadId, note.getId()).ifPresent(hotCacheService::removeHot);
        dbService.threadNoteCancelHot(note, threadId);
    }

    public void threadNoteToTop(Note note, String threadId) {
        dbService.threadNoteToTop(note, threadId);
        List<String> list = dbService
                .getNoteTopListByThread(threadId, 1, NoteDefaults.THREAD_MAX_TOP)
                .map(NoteThreadTop::getNoteId)
                .getContent();
        cacheService.setTopNoteIdList(threadId, list);
    }

    public void threadNoteCancelTop(Note note, String threadId) {
        dbService.threadNoteCancelTop(note, threadId);
        List<String> list = dbService
                .getNoteTopListByThread(threadId, 1, NoteDefaults.THREAD_MAX_TOP)
                .map(NoteThreadTop::getNoteId)
                .getContent();
        cacheService.setTopNoteIdList(threadId, list);
    }

    // ----------------------------------------------------------------------

    public NoteThread updateThreadBasic(
            NoteThread thread, String title, NoteThreadCondition condition,
            Integer hotMax, Integer topMax, Boolean mixReviewPageable) {
        log.info("update thread basic: " + thread + ", " +
                "condition: " + condition + ", " +
                "hotMax: " + hotMax + ", " +
                "topMax: " + topMax + ", " +
                "mixReviewPageable: " + mixReviewPageable
        );
        NoteThread updateThread = new NoteThread();
        updateThread.setId(thread.getId());
        updateThread.setTitle(title);
        updateThread.setCondition(condition.getValue());
        updateThread.setHotMax(hotMax);
        updateThread.setTopMax(topMax);
        updateThread.setMixReviewPageable(mixReviewPageable);
        return dbService.updateThread(updateThread);
    }

    public NoteThread updateTitle(NoteThread thread, String title) {
        NoteThread updateThread = new NoteThread();
        updateThread.setId(thread.getId());
        updateThread.setTitle(title);
        NoteThread updatedThread = dbService.updateThread(updateThread);
        cacheService.updateThread(updatedThread);
        return updatedThread;
    }

    public NoteThread updateCondition(NoteThread thread, NoteThreadCondition condition) {
        log.info("update thread condition: " + thread.getId() + ", " + thread.getCondition() + " -> " + condition.getValue());
        NoteThread updateThread = new NoteThread();
        updateThread.setId(thread.getId());
        updateThread.setCondition(condition.getValue());
        NoteThread updatedThread = dbService.updateThread(updateThread);
        cacheService.updateThread(updatedThread);
        return updatedThread;
    }

    public NoteThread updateRoute(NoteThread thread, List<String> routeThreads) {
        return dbService.updateRoute(thread.getId(), routeThreads);
    }

    public NoteThread updateStyle(NoteThread thread, NoteListStyle style) {
        NoteThread updateThread = new NoteThread();
        updateThread.setId(thread.getId());
        updateThread.setStyle(style.name());
        NoteThread updatedThread = dbService.updateThread(updateThread);
        cacheService.updateThread(updatedThread);
        return updatedThread;
    }

    public NoteThread updateHotMax(NoteThread thread, int hotMax, String hotTitle, Boolean hotPrefer) {
        NoteThread updateThread = new NoteThread();
        updateThread.setId(thread.getId());
        updateThread.setHotMax(hotMax);
        updateThread.setHotTitle(hotTitle);
        updateThread.setHotPrefer(hotPrefer);
        NoteThread updatedThread = dbService.updateThread(updateThread);
        cacheService.updateThread(updatedThread);
        return updatedThread;
    }

    public NoteThread updateTopMax(NoteThread thread, int topMax) {
        NoteThread updateThread = new NoteThread();
        updateThread.setId(thread.getId());
        updateThread.setTopMax(topMax);
        NoteThread updatedThread = dbService.updateThread(updateThread);
        cacheService.updateThread(updatedThread);
        return updatedThread;
    }

    public NoteThread updatePostRule(NoteThread thread, NoteThreadPostRule postRule) {
        NoteThread updateThread = new NoteThread();
        updateThread.setId(thread.getId());
        updateThread.setPostRule(postRule);
        NoteThread updatedThread = dbService.updateThread(updateThread);
        cacheService.updateThread(updatedThread);
        return updatedThread;
    }

    public NoteThread updateShareModel(NoteThread thread, ShareModel shareModel) {
        NoteThread updateThread = new NoteThread();
        updateThread.setId(thread.getId());
        updateThread.setShare(shareModel);
        NoteThread updatedThread = dbService.updateThread(updateThread);
        cacheService.updateThread(updatedThread);
        return updatedThread;
    }

    public NoteThread updateKey(String threadId, String key) {
        NoteThread updatedThread = dbService.updateKey(threadId, key);
        cacheService.updateThread(updatedThread);
        return updatedThread;
    }

    public NoteThread updateAttr(String threadId, Map<String, String> attr, boolean replace) {
        NoteThread updatedThread = dbService.updateAttr(threadId, attr, replace);
        cacheService.updateThread(updatedThread);
        return updatedThread;
    }

    public NoteThread updateAttrKeyValue(String threadId, String key, String value) {
        NoteThread updatedThread = dbService.updateAttrKeyValue(threadId, key, value);
        cacheService.updateThread(updatedThread);
        return updatedThread;
    }

    // ----------------------------------------------------------------------

    private void verifyThread(NoteThread thread) {
        if (StringUtils.isEmpty(thread.getTitle())) {
            throw new KbException(KbCode.ILLEGAL_ARGUMENT, "thread title empty").li();
        }
        if (thread.getTopMax() == null || thread.getTopMax() < 0) {
            thread.setTopMax(NoteDefaults.DEFAULT_THREAD_MAX_TOP);
        }
        if (thread.getHotMax() == null) {
            thread.setHotMax(NoteDefaults.DEFAULT_THREAD_MAX_HOT);
        }
        if (thread.getMixReviewPageable() == null) {
            thread.setMixReviewPageable(false);
        }
        if (thread.getCondition() == null) {
            thread.setCondition(NoteDefaults.DEFAULT_THREAD_CONDITION.getValue());
        } else {
            NoteThreadCondition.valueOf(thread.getCondition()).orElseThrow(() ->
                    new KbException(KbCode.ILLEGAL_ARGUMENT, "unknown thread condition: " + thread.getCondition()).li());
        }
        if (thread.getRouteThreads() != null) {
            if (thread.getRouteThreads().size() == 0) {
                thread.setRouteThreads(null);
            } else {
                int existedThreadSize = dbService.getThreadListIn(thread.getRouteThreads()).size();
                if (existedThreadSize != thread.getRouteThreads().size()) {
                    throw new KbException(KbCode.ILLEGAL_ARGUMENT, "some thread(s) provided in routeThreads not exist").li();
                }
            }
        }
        if (thread.getStyle() != null) {
            NoteListStyle.resolveByName(thread.getStyle()).orElseThrow(() ->
                    new KbException(KbCode.ILLEGAL_ARGUMENT, "style unknown: " + thread.getStyle()).li());
        }
    }

    // ----------------------------------------------------------------------

    private final AbsObjectListCache<NoteThread, String> threadListCache = new AbsObjectListCache<NoteThread, String>() {
        @Override
        public List<NoteThread> getListFromDatabase(List<String> idList) {
            if (NoteCacheConfig.CACHE_DEBUG) {
                log.info("[cache_debug][db] get thread list from db: " + idList);
            }
            return dbService.getThreadListIn(idList);
        }

        @Override
        public List<NoteThread> getListFromCache(List<String> idList) {
            return cacheService.getThreadListIn(idList);
        }

        @Override
        public void updateListToCache(List<NoteThread> dataList) {
            cacheService.updateThreadList(dataList);
        }

        @Override
        public String getIdFromData(NoteThread data) {
            return data.getId();
        }

        @Override
        public boolean isDataValid(NoteThread data) {
            return data != null && data.getId() != null;
        }
    };

}
