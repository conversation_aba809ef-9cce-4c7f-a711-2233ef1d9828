package com.kaiba.m.core.service.appcyx;

import com.kaiba.lib.base.assembler.Mapper;
import com.kaiba.lib.base.constant.news.NewsChannel;
import com.kaiba.lib.base.constant.news.article.ArticleTimeType;
import com.kaiba.lib.base.constant.news.article.GroupSortMode;
import com.kaiba.lib.base.constant.news.article.NewsRenderer;
import com.kaiba.lib.base.domain.common.ShareModel;
import com.kaiba.lib.base.domain.news.pool.bygroup.GroupCreateModel;
import com.kaiba.lib.base.domain.news.pool.bygroup.GroupModel;
import com.kaiba.lib.base.response.KbCode;
import com.kaiba.lib.base.response.KbException;
import com.kaiba.lib.base.service.INeoNewsByGroupService;
import com.kaiba.lib.base.util.GsonUtils;
import com.kaiba.lib.base.util.appaction.AppActionModel;
import com.kaiba.m.core.constant.app.cyx.CYXArticleGroupType;
import com.kaiba.m.core.domain.SeqBasedIdxFirstOrder;
import com.kaiba.m.core.domain.appcyx.CYXArticleGroup;
import com.kaiba.m.core.model.appcyx.CYXArticleGroupIdxOrder;
import com.kaiba.m.core.model.appcyx.CYXArticleGroupModel;
import com.kaiba.m.core.model.appcyx.CYXAppHomeModel;
import com.kaiba.m.core.model.appcyx.CYXArticleGroupState;
import com.kaiba.m.core.repository.appcyx.CYXHomeArticleGroupRepository;
import com.kaiba.m.core.util.PageUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 超有戏 服务
 * <AUTHOR>
 * @version YHJHomeService, v0.1 2025/6/23 10:50 daopei Exp $
 **/
@Slf4j
@Service
public class CYXAppHomeService {

    private final INeoNewsByGroupService newsByGroupService;
    private final CYXHomeArticleGroupRepository articleGroupRepository;
    private final StringRedisTemplate redisTemplate;

    private final static String appHomeCacheKey = "java_core_cyx_app_home";
    private final static String appHomeShareCacheKey = "java_core_cyx_app_home_share";
    private final static String iconKey = "cyx_app_home";
    private final static String bannerKey = "cyx_app_home";

    public CYXAppHomeService(
            INeoNewsByGroupService newsByGroupService,
            CYXHomeArticleGroupRepository articleGroupRepository,
            StringRedisTemplate redisTemplate
    ) {
        this.newsByGroupService = newsByGroupService;
        this.articleGroupRepository = articleGroupRepository;
        this.redisTemplate = redisTemplate;
    }


    public CYXArticleGroupModel createGroup(CYXArticleGroupModel model) {
        CYXArticleGroup group = Mapper.map(model, CYXArticleGroup.class);
        if (group.getState() == null) {
            group.setState(CYXArticleGroupState.SHOW.name());
        }
        String groupKey = whenEmptyInitGroup(group.getGroupKey());
        group.setGroupKey(groupKey);
        group.setCreateTime(System.currentTimeMillis());
        group.setUpdateTime(System.currentTimeMillis());
        CYXArticleGroupModel entity = Mapper.map(articleGroupRepository.save(group), CYXArticleGroupModel.class);
        invalidateCache();
        return entity;
    }

    public void deleteGroup(String id) {
        articleGroupRepository.deleteById(id);
        invalidateCache();
    }

    public CYXArticleGroupModel updateGroup(CYXArticleGroupModel model) {
        articleGroupRepository.findById(model.getId())
                .orElseThrow(() -> new KbException(KbCode.RESOURCE_NOT_FOUND).li());
        CYXArticleGroup group = Mapper.map(model, CYXArticleGroup.class);
        String groupKey = whenEmptyInitGroup(group.getGroupKey());
        group.setGroupKey(groupKey);
        CYXArticleGroupModel entity = Mapper.map(articleGroupRepository.update(group), CYXArticleGroupModel.class);
        invalidateCache();
        return entity;
    }

    public CYXArticleGroupModel updateGroupState(String id, String state) {
        articleGroupRepository.findById(id).orElseThrow(() -> new KbException(KbCode.RESOURCE_NOT_FOUND).li());
        CYXArticleGroup group = articleGroupRepository.updateState(id, state);
        CYXArticleGroupModel entity = Mapper.map(group, CYXArticleGroupModel.class);
        invalidateCache();
        return entity;
    }


    public void updateGroupIdx(List<CYXArticleGroupIdxOrder> orders) {
        List<CYXArticleGroup> groups = articleGroupRepository.findByIdIn(orders.stream().map(CYXArticleGroupIdxOrder::getId).collect(Collectors.toList()));
        Map<String, CYXArticleGroup> groupMap = groups.stream().collect(Collectors.toMap(CYXArticleGroup::getId, Function.identity(), (v1, v2) -> v1));
        List<SeqBasedIdxFirstOrder> updateSeqOrders = orders.stream()
                .filter(Objects::nonNull)
                .map(order -> {
                    CYXArticleGroup group = groupMap.get(order.getId());
                    if (group == null) {
                        return null;
                    }
                    return new SeqBasedIdxFirstOrder(group.getId(), order.getIdx(), group.getCreateTime());
                })
                .collect(Collectors.toList());
        articleGroupRepository.updateIdxBatch(updateSeqOrders);
        invalidateCache();
    }


    public void updateShare(ShareModel share) {
        log.info("cyx app home update share:{}", GsonUtils.getGson().toJson(share));
        if (share == null) {
            redisTemplate.delete(appHomeShareCacheKey);
            return;
        }
        ShareModel.verify(share);
        redisTemplate.opsForValue().set(appHomeShareCacheKey, GsonUtils.getGson().toJson(share));
    }

    public ShareModel getShare() {
        String shareJson = redisTemplate.opsForValue().get(appHomeShareCacheKey);
        if (shareJson == null) {
            return null;
        }
        return GsonUtils.getGson().fromJson(shareJson, ShareModel.class);
    }


    public Page<CYXArticleGroupModel> getGroupList(String state, String groupType, Integer page, Integer pageSize) {
        Pageable pageable = PageUtils.ofDefault(page, pageSize, Sort.by(Sort.Direction.DESC, "seq"));
        return articleGroupRepository.getByParam(state, groupType, pageable)
                .map(group -> Mapper.map(group, CYXArticleGroupModel.class));
    }

    // ============================================================

    /**
     * 分组为空时自动创建资讯分组
     * @param groupKey
     * @return
     */
    public String whenEmptyInitGroup(String groupKey) {
        if (groupKey != null) {
            return groupKey;
        }

        GroupCreateModel model = new GroupCreateModel();
        model.setChannelKey(NewsChannel.HPAG.name());
        model.setDesc("超有戏资讯分组-" + System.currentTimeMillis());
        model.setName("超有戏资讯分组-" + System.currentTimeMillis());
        model.setSiteId(9);
        model.setRenderer(NewsRenderer.NONE.name());
        model.setSortMode(GroupSortMode.SEQ.name());
        model.setSortTimeBy(ArticleTimeType.ONLINE_TIME.name());
        GroupModel initGroup = newsByGroupService.createGroupByBody(model).dataOrThrow();
        return initGroup.getKey();
    }



    // ============================================================

    public CYXAppHomeModel getHomeFromCache() {
        String value = redisTemplate.opsForValue().get(appHomeCacheKey);
        if (value == null) {
            //load
            CYXAppHomeModel appHome = loadAppHome();
            redisTemplate.opsForValue().set(appHomeCacheKey, GsonUtils.getGson().toJson(appHome));
            redisTemplate.expire(appHomeCacheKey, Duration.ofHours(1));
            return appHome;
        }

        CYXAppHomeModel appHome = GsonUtils.getGson().fromJson(value, CYXAppHomeModel.class);
        ShareModel share = getShare();
        appHome.setShareModel(share);
        return appHome;
    }

    private void invalidateCache() {
        redisTemplate.delete(appHomeCacheKey);
    }

    private CYXAppHomeModel loadAppHome() {
        CYXAppHomeModel model = new CYXAppHomeModel();
        model.setIconKey(iconKey);
        model.setBannerKey(bannerKey);

        List<CYXArticleGroupModel> groupList = getGroupList(
                CYXArticleGroupState.SHOW.name(), CYXArticleGroupType.HOME.name(),
                1, 100).getContent();
        model.setGroupList(groupList);
        return model;
    }

}
