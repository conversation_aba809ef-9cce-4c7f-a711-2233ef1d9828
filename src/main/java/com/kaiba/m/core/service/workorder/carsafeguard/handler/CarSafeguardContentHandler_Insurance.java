package com.kaiba.m.core.service.workorder.carsafeguard.handler;

import com.kaiba.lib.base.assembler.Mapper;
import com.kaiba.lib.base.lang.verifier.F;
import com.kaiba.lib.base.lang.verifier.V;
import com.kaiba.lib.base.lang.verifier.Verifier;
import com.kaiba.lib.base.lang.verifier.VerifierBuilder;
import com.kaiba.m.core.domain.workorder.carsafeguard.CarSafeguardCaseContent;
import com.kaiba.m.core.domain.workorder.carsafeguard.CarSafeguardInsuranceCase;
import com.kaiba.m.core.repository.workorder.carsafeguard.CarSafeguardCaseContentRepository;
import com.kaiba.m.core.repository.workorder.carsafeguard.CarSafeguardInsuranceRepository;
import com.kaiba.m.core.service.workorder.carsafeguard.CarSafeguardBizType;
import com.kaiba.m.core.service.workorder.carsafeguard.CarSafeguardUtils;
import com.kaiba.m.core.model.safeguard.CarSafeguardCaseContentModel;
import com.kaiba.m.core.model.safeguard.CarSafeguardInsuranceCaseContentModel;
import com.kaiba.m.core.model.safeguard.request.CSInsuranceCaseCreateModel;
import com.kaiba.m.core.model.safeguard.request.CarSafeguardCaseCreateModel;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version CarSafeguardContentHandler_Insurance, v0.1 2024/7/22 16:48 daopei Exp $
 **/
@Service
public class CarSafeguardContentHandler_Insurance implements CarSafeguardContentHandler{

    private final CarSafeguardInsuranceRepository caseRepository;
    private final CarSafeguardCaseContentRepository contentRepository;


    public CarSafeguardContentHandler_Insurance(CarSafeguardInsuranceRepository caseRepository,CarSafeguardCaseContentRepository contentRepository) {
        this.caseRepository = caseRepository;
        this.contentRepository = contentRepository;
    }

    @Override
    public boolean support(String contentType) {
        return CarSafeguardBizType.INSURANCE == CarSafeguardBizType.valueOf(contentType);
    }

    @Override
    public Class<CarSafeguardInsuranceCaseContentModel> contentClass() {
        return CarSafeguardInsuranceCaseContentModel.class;
    }

    @Override
    public CarSafeguardCaseContentModel getContentById(String id) {
        return caseRepository.findById(id)
                .map(c -> Mapper.map(c, CarSafeguardInsuranceCaseContentModel.class))
                .orElse(null);
    }

    @Override
    public List<CarSafeguardCaseContentModel> getContentById(Collection<String> ids) {
        return caseRepository.findByIdIn(ids).stream()
                .map(c -> Mapper.map(c, CarSafeguardInsuranceCaseContentModel.class))
                .collect(Collectors.toList());
    }

    @Override
    public <T extends CarSafeguardCaseCreateModel> CarSafeguardCaseContentModel createContent(T model) {
        insuranceVerify.verify((CSInsuranceCaseCreateModel)model);
        CarSafeguardInsuranceCase entity = Mapper.map(model, CarSafeguardInsuranceCase.class);
        //父类属性
        entity.setContent(model.getContent());
        entity.setContentType(CarSafeguardBizType.INSURANCE.name());
        entity.setUserId(model.getClientUserId());
        entity.setSource(model.getSource());
        entity.setImages(model.getImages());
        entity.setVideo(model.getVideo());
        //基础属性
        entity.setPersonal(CarSafeguardUtils.getACLStringDataListOrThrow(model));
        entity.setOriginResolverLack(model.getOriginResolverLack());
        entity.setCreateTime(System.currentTimeMillis());
        entity.setUpdateTime(System.currentTimeMillis());
        entity = caseRepository.save(entity);

        createContentIndex(entity.getId(), CarSafeguardBizType.INSURANCE.name());
        return map2Model(entity);
    }

    private void createContentIndex(String contentId, String contentType) {
        CarSafeguardCaseContent content = new CarSafeguardCaseContent();
        content.setContentId(contentId);
        content.setContentType(contentType);
        content.setCreateTime(System.currentTimeMillis());
        contentRepository.save(content);
    }

    private static CarSafeguardInsuranceCaseContentModel map2Model(CarSafeguardInsuranceCase insurance) {
        CarSafeguardInsuranceCaseContentModel model = new CarSafeguardInsuranceCaseContentModel();
        model.setId(insurance.getId());
        model.setContent(insurance.getContent());
        model.setContentType(CarSafeguardBizType.INSURANCE.name());
        model.setCreateTime(insurance.getCreateTime());
        model.setUpdateTime(insurance.getUpdateTime());
        model.setPersonal(insurance.getPersonal());
        model.setOriginResolverLack(insurance.getOriginResolverLack());
        model.setSource(insurance.getSource());
        model.setUserId(insurance.getUserId());
        model.setVideo(insurance.getVideo());
        model.setImages(insurance.getImages());
        model.setCarRegistration(insurance.getCarRegistration());
        model.setCarRegistrationNo(insurance.getCarRegistrationNo());
        return model;
    }

    static Verifier<CSInsuranceCaseCreateModel> insuranceVerify = new VerifierBuilder<CSInsuranceCaseCreateModel>().defaultOrElseThrow()
            .and(F.intF(CSInsuranceCaseCreateModel::getCarRegistration).allowNull().in(1, 0))
            .addVerifier(V.or(
                            F.str(CSInsuranceCaseCreateModel::getOriginResolver).notNull(),
                            F.str(CSInsuranceCaseCreateModel::getOriginResolverLack).notNull())
                    .r("请选择或者输入投诉对象"))
            .addVerifier(V.or(
                    F.intF(CSInsuranceCaseCreateModel::getCarRegistration).isNull(),
                    F.intF(CSInsuranceCaseCreateModel::getCarRegistration).eq(0),
                    V.and(
                            F.intF(CSInsuranceCaseCreateModel::getCarRegistration).eq(1),
                            F.str(CSInsuranceCaseCreateModel::getCarRegistrationNo).notNull().r("请输入车牌号")
                    )
            ))
            .create();
}
