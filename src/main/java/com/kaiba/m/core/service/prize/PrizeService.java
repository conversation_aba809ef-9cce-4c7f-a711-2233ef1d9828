package com.kaiba.m.core.service.prize;

import com.kaiba.lib.base.assembler.Mapper;
import com.kaiba.lib.base.constant.prize.*;
import com.kaiba.lib.base.domain.prize.PrizeModel;
import com.kaiba.lib.base.domain.prize.WriteOffResult;
import com.kaiba.lib.base.response.KbCode;
import com.kaiba.lib.base.response.KbException;
import com.kaiba.lib.base.service.IUserService;
import com.kaiba.lib.base.util.StringUtils;
import com.kaiba.m.core.domain.prize.DispatchMethod;
import com.kaiba.m.core.domain.prize.Prize;
import com.kaiba.m.core.domain.prize.PrizeWriteOffLog;
import com.kaiba.m.core.repository.prize.PrizeRepository;
import com.kaiba.m.core.repository.prize.PrizeWriteOffLogRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * author: lyux
 * date: 19-11-12
 */
@Slf4j
@Service
public class PrizeService {

    private static final String CACHE_KEY_PREFIX = "java_user_prize_short_number_sequence_";

    private final PrizeRepository prizeRepository;
    private final PrizeWriteOffLogRepository prizeWriteOffLogRepository;
    private final DispatchMethodService dispatchMethodService;
    private final ShortNOGenerator shortNOGenerator;
    private final IUserService userService;

    public PrizeService(
            PrizeRepository prizeRepository,
            PrizeWriteOffLogRepository prizeWriteOffLogRepository,
            DispatchMethodService dispatchMethodService,
            StringRedisTemplate stringRedisTemplate,
            IUserService userService
    ) {
        this.prizeRepository = prizeRepository;
        this.prizeWriteOffLogRepository = prizeWriteOffLogRepository;
        this.dispatchMethodService = dispatchMethodService;
        this.shortNOGenerator = new ShortNOGenerator(CACHE_KEY_PREFIX, stringRedisTemplate);
        this.userService = userService;
    }

    public Prize createPrize(Prize prize) {
        decorateAndCheckPrizeForUpdateOrCreate(prize);
        prize.setV(1L);
        prize = prizeRepository.insert(prize);
        log.info("prize create: " + prize);
        return prize;
    }

    public Prize upsertPrize(Prize prize) {
        decorateAndCheckPrizeForUpdateOrCreate(prize);
        prize.setV(1L);
        prize = prizeRepository.upsertByReferenceId(prize);
        log.info("prize upsert: " + prize);
        return prize;
    }

    public Optional<Prize> getPrizeById(String prizeId) {
        return prizeRepository.findById(prizeId);
    }

    public Optional<Prize> getPrizeByNo(String no) {
        return prizeRepository.findFirstByNoOrderByCreateTimeDesc(no);
    }

    public Optional<Prize> getPrizeByIdOrNo(String prizeId, String prizeNo) {
        if (null != prizeId) {
            return prizeRepository.findById(prizeId);
        } else if (null != prizeNo) {
            return prizeRepository.findFirstByNoOrderByCreateTimeDesc(prizeNo);
        } else {
            throw new KbException(KbCode.ILLEGAL_ARGUMENT, "no id and no");
        }
    }

    public List<Prize> getListByPrizeIdIn(String[] prizeIds) {
        return prizeRepository.findByIdIn(prizeIds);
    }

    public List<Prize> getListByReferenceId1AndLastId(String referenceId1, String lastId, int pageSize) {
        Pageable pageable = PageRequest.of(1, pageSize, Sort.by(Sort.Direction.DESC, "_id"));
        if (null == lastId) {
            return prizeRepository.findByReferenceId1(referenceId1, pageable);
        } else {
            return prizeRepository.findByReferenceId1AndIdLessThan(referenceId1, lastId, pageable);
        }
    }

    public Page<Prize> getPageByState(PrizeState state, Integer page, Integer pageSize) {
        int p = page == null ? 0 : page - 1;
        int ps = pageSize == null || pageSize > 200 ? 15 : pageSize;
        return prizeRepository.findByState(state.getValue(),
                PageRequest.of(p, ps, Sort.by(Sort.Direction.DESC, "_id")));
    }

    public Page<Prize> getPageByDispatchMethodIdIn(List<String> dispatchMethodIdList, Integer page, Integer pageSize) {
        int p = page == null ? 0 : page - 1;
        int ps = pageSize == null || pageSize > 200 ? 15 : pageSize;
        return prizeRepository.findByDispatchMethodIdIn(dispatchMethodIdList,
                PageRequest.of(p, ps, Sort.by(Sort.Direction.DESC, "_id")));
    }

    public Page<Prize> getListWithPrize(Prize prize, Integer page, Integer pageSize) {
        int p = page == null ? 0 : page - 1;
        int ps = pageSize == null || pageSize > 200 ? 15 : pageSize;
        return prizeRepository.getListWithPrize(prize,
                PageRequest.of(p, ps, Sort.by(Sort.Direction.DESC, "_id")));
    }

    public long getCountWithPrize(Prize prize) {
        return prizeRepository.getCountWithPrize(prize);
    }

    public Page<PrizeWriteOffLog> getWriteOffLogByPrizeId(String prizeId, Integer page, Integer pageSize) {
        int p = page == null ? 0 : page - 1;
        int ps = pageSize == null || pageSize > 200 ? 15 : pageSize;
        return prizeWriteOffLogRepository.findAllByPrizeIdOrderByCreateTimeDesc(prizeId, PageRequest.of(p, ps));
    }

    public Page<PrizeWriteOffLog> getWriteOffLogByOperatorId(Integer operatorId, Integer page, Integer pageSize) {
        int p = page == null ? 0 : page - 1;
        int ps = pageSize == null || pageSize > 200 ? 15 : pageSize;
        return prizeWriteOffLogRepository.findAllByOperatorIdOrderByCreateTimeDesc(operatorId, PageRequest.of(p, ps));
    }

    public WriteOffResult writeOffPrize(PrizeModel prize, Integer writeOffUserId, String channel) {
        Prize updatedPrize = prizeRepository.writeOff(prize.getId(), prize.getTransactionId(), writeOffUserId);
        if (updatedPrize == null) {
            // 检查奖品是否已经核销了
            Optional<PrizeWriteOffLog> opWriteOffLog =
                    prizeWriteOffLogRepository.findFirstByPrizeIdOrderByCreateTimeDesc(prize.getId());
            if (opWriteOffLog.isPresent()) {
                PrizeWriteOffLog writeOffLog = opWriteOffLog.get();
                WriteOffResult result = new WriteOffResult(WriteOffResultCode.PRIZE_CLAIMED);
                result.setPrize(prize);
                result.setWriteOffOperator(userService.getBasic(writeOffLog.getOperatorId()).getData());
                result.setWriteOffChannel(writeOffLog.getChannel());
                result.setWriteOffTime(writeOffLog.getCreateTime());
                return result;
            } else {
                return new WriteOffResult(Mapper.map(prize, PrizeModel.class), WriteOffResultCode.FAIL);
            }
        } else {
            PrizeWriteOffLog writeOffLog = new PrizeWriteOffLog();
            writeOffLog.setPrizeId(prize.getId());
            writeOffLog.setOperatorId(writeOffUserId);
            writeOffLog.setChannel(channel);
            writeOffLog.setCreateTime(System.currentTimeMillis() / 1000);
            prizeWriteOffLogRepository.insert(writeOffLog);
            WriteOffResult result = new WriteOffResult(WriteOffResultCode.OK);
            result.setPrize(prize);
            result.setWriteOffOperator(userService.getBasic(writeOffLog.getOperatorId()).getData());
            result.setWriteOffChannel(writeOffLog.getChannel());
            result.setWriteOffTime(writeOffLog.getCreateTime());
            return result;
        }
    }

    public Prize giveUpPrize(String prizeId, PrizeGiveUpReason reason) {
        return prizeRepository.giveUp(prizeId, reason);
    }

    public Prize updateState(String prizeId, PrizeState state) {
        return prizeRepository.updateState(prizeId, state);
    }

    public Prize updateRedirectLink(String prizeId, String redirectLink) {
        return prizeRepository.updateRedirectLink(prizeId, redirectLink);
    }

    public Prize updatePrizeAttrs(String prizeId, Map<String, String> attrs) {
        return prizeRepository.updateAttrs(prizeId, attrs);
    }

    public Prize updatePrizeAttrsKeyValue(String prizeId, String key, String value) {
        return prizeRepository.updateAttrsKeyValue(prizeId, key, value);
    }

    public Prize updateShipmentId(String prizeId, String shipmentId) {
        return prizeRepository.updateShipmentId(prizeId, shipmentId);
    }

    public Prize updateUserInfo(
            String prizeId, Integer userId, String userName, String userMobile,
            String contactMobile, String addressId) {
        return prizeRepository.updateUserInfo(prizeId, userId, userName, userMobile, contactMobile, addressId);
    }

    public Prize updateTransactionId(String prizeId, String transactionId) {
        return prizeRepository.updateTransactionId(prizeId, transactionId);
    }

    long checkAndUpdateExpiredPrize() {
        return prizeRepository.checkAndExpirePrize();
    }

    // -----------------------------------------------------------

    private void decorateAndCheckPrizeForUpdateOrCreate(Prize prize) {
        long current = System.currentTimeMillis() / 1000;
        String dispatchMethodId = prize.getDispatchMethodId();
        DispatchMethod dispatchMethod = dispatchMethodService.getById(dispatchMethodId, true)
                .orElseThrow(() -> new KbException(KbCode.ILLEGAL_ARGUMENT, "dispatch method not exists: " + dispatchMethodId));
        prize.setSponsorId(dispatchMethod.getSponsorId());
        prize.setCreateTime(current);
        prize.setUpdateTime(current);
        prize = verifyPrizeOnCreate(prize, dispatchMethod);
        if (dispatchMethod.getWriteOffType() != DispatchWriteOffType.NONE.getValue()) {
            prize.setNo(shortNOGenerator.generateNO(prize.getSiteId().toString()));
        }
    }

    private static Prize verifyPrizeOnCreate(Prize prize, DispatchMethod dispatchMethod) {
        if (prize == null) {
            throw new KbException(KbCode.ILLEGAL_ARGUMENT, "prize should note be null").li();
        }
        if (prize.getDispatchMethodId() == null) {
            throw new KbException(KbCode.ILLEGAL_ARGUMENT, "dispatch method id null: " + prize).li();
        }
        if (dispatchMethod == null) {
            throw new KbException(KbCode.ILLEGAL_ARGUMENT, "dispatch method should note be null").li();
        }
        if (!prize.getDispatchMethodId().equals(dispatchMethod.getId())) {
            throw new KbException(KbCode.ILLEGAL_ARGUMENT, "dispatch method mismatch: " + prize + ", " + dispatchMethod).li();
        }
        if (prize.getId() != null) {
            throw new KbException(KbCode.ILLEGAL_ARGUMENT, "update not allowed here, id should be null: " + prize).li();
        }
        if (prize.getUserId() == null) {
            throw new KbException(KbCode.ILLEGAL_ARGUMENT, "user id should note be null: " + prize).li();
        }
        if (StringUtils.isEmpty(prize.getName())) {
            throw new KbException(KbCode.ILLEGAL_ARGUMENT, "name should not be empty: " + prize).li();
        }
        prize.setName(prize.getName().trim());
        if (!PrizeOrigin.valueOf(prize.getOrigin()).isPresent()) {
            throw new KbException(KbCode.ILLEGAL_ARGUMENT, "wrong prize origin: " + prize).li();
        }
        PrizeType prizeType = PrizeType.valueOf(prize.getType()).orElseThrow(() ->
                new KbException(KbCode.ILLEGAL_ARGUMENT, "wrong prize type: " + prize).li());
        if (prize.getState() == null) {
            prize.setState(dispatchMethod.getInitPrizeState());
        }
        if (!PrizeState.valueOf(prize.getState()).isPresent()) {
            throw new KbException(KbCode.ILLEGAL_ARGUMENT, "wrong prize state: " + prize).li();
        }
        if (prize.getExpireTime() == null) {
            prize.setExpireTime(dispatchMethod.getDispatchEndTime(prize.getCreateTime()));
        }
        if (dispatchMethod.getDeliveryType() != DispatchDeliveryType.USER_PICKUP_APPOINTED_TIME.getValue()
                && dispatchMethod.getDeliveryType() != DispatchDeliveryType.USER_PICKUP_APPOINTED_TIME_AND_ADDR.getValue()) {
            // 如果非用户自行预约时间, 则以 dispatchMethod 时间为准
            if (prize.getPickupStartTime() == null) {
                prize.setPickupStartTime(dispatchMethod.getDispatchStartTime(prize.getCreateTime()));
            }
        }
        if (prize.getConfirmState() == null) {
            if (dispatchMethod.getDeliveryType() == DispatchDeliveryType.USER_CONFIRM.getValue()) {
                prize.setConfirmState(PrizeConfirmState.CONFIRM_PENDING.getValue());
            } else {
                prize.setConfirmState(PrizeConfirmState.NONE.getValue());
            }
        }
        switch (prizeType) {
            case CASH:
            case COUPON:
            case ITEM: {
                break;
            }
            case COUPON_PAY:
            case ITEM_PAY: {
                if (prize.getTransactionId() == null) {
                    throw new KbException(KbCode.ILLEGAL_ARGUMENT, "payed prize need transaction info: " + prize).li();
                }
                break;
            }
            case CASH_CONFIRM: {
                if (prize.getTransactionAmount() == null || prize.getTransactionAccountId() == null) {
                    throw new KbException(KbCode.ILLEGAL_ARGUMENT, "confirm cash prize need transaction info: " + prize).li();
                }
                break;
            }
        }
        return prize;
    }

}
