package com.kaiba.m.core.service.program;

import com.kaiba.lib.base.domain.program.EmceeModel;
import com.kaiba.m.core.domain.program.Emcee;

/**
 * author wangsj
 * date 2020-09-01
 */
public final class EmceeConverter {

    public static EmceeModel simpleEmcee2Model(Emcee emcee){
        EmceeModel model = new EmceeModel();
        model.setId(emcee.getId());
        model.setUserId(emcee.getUserId());
        model.setNickname(emcee.getNickname());
        return model;
    }

    public static EmceeModel baseEmcee2Model(Emcee emcee){
        EmceeModel model = new EmceeModel();
        model.setId(emcee.getId());
        model.setUserId(emcee.getUserId());
        model.setNickname(emcee.getNickname());
        model.setAvatar(emcee.getAvatar());
        model.setPicture(emcee.getPicture());
        model.setSex(emcee.getSex());
        return model;
    }
}
