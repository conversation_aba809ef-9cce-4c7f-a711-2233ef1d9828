package com.kaiba.m.core.service.note.event.handler;

import com.kaiba.lib.base.domain.note.NoteModel;
import com.kaiba.lib.base.domain.user.UserModel;
import com.kaiba.m.core.domain.note.Note;
import com.kaiba.m.core.domain.note.NoteComment;
import com.kaiba.m.core.service.note.event.INoteEventReceiver;
import com.kaiba.m.core.service.note.event.NoteCreateOrigin;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Component;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

import java.util.List;

/**
 * 金豆帖子回复接收
 * <AUTHOR>
 * @date 2023/10/08 14:27
 *
 * 2024-06-13 备注: 金豆园活动最终未上线, 目前是废弃状态.
 */
@Slf4j
@Component
public class GoldenBeanHandler implements INoteEventReceiver {

    private static final String URL_REPLY_TASK = "http://kaiba-activity/admin/hz/goldenBean/completeReplyTask";

    private final RestTemplate restTemplate;

    public GoldenBeanHandler(RestTemplate restTemplate) {
        this.restTemplate = restTemplate;
    }

    @Override
    public void onCommentCreated(Note note, NoteComment comment, UserModel owner, UserModel toUser, String reviewId, @NonNull NoteCreateOrigin origin) {
        notifyReplyTask(comment.getUserId(), note.getThreads());
    }

    @Override
    public void onNoteCreated(@NonNull Note note, @Nullable NoteModel noteModel, UserModel owner, String reviewId, @NonNull NoteCreateOrigin origin) {
        notifyReplyTask(note.getUserId(), note.getThreads());
    }

    // -----------------------------------

    private void notifyReplyTask(Integer userId, List<String> threads) {
        MultiValueMap<String, Object> params = new LinkedMultiValueMap<>();
        params.add("userId", userId);
        params.add("threads", String.join(",", threads));
        HttpEntity<MultiValueMap<String, Object>> entity = new HttpEntity<>(params, getHttpHeaders());
        try {
            restTemplate.postForEntity(URL_REPLY_TASK, entity, String.class);
        } catch (Exception ignore) {
            log.info("golden bean notify reply task failed, userId: {}, {}", userId, threads);
        }
    }

    private static HttpHeaders getHttpHeaders() {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
        return headers;
    }
}
