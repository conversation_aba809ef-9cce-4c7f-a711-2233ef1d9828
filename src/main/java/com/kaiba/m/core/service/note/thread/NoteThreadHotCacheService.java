package com.kaiba.m.core.service.note.thread;

import com.kaiba.lib.base.response.KbCode;
import com.kaiba.lib.base.response.KbException;
import com.kaiba.lib.base.util.GsonUtils;
import com.kaiba.m.core.domain.note.NoteThreadHot;
import com.kaiba.m.core.service.note.NoteCacheConfig;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringEscapeUtils;
import org.springframework.data.redis.connection.DefaultStringTuple;
import org.springframework.data.redis.connection.StringRedisConnection;
import org.springframework.data.redis.core.RedisCallback;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * author: lyux
 * date: 19-6-3
 * <p>
 * 帖子板块缓存管理.
 * <p>
 * 板块缓存, 采用 redis hash 保存每一条板块信息, 支持的缓存查询方式为 按 id 取板块信息, 按 id 集合取板块信息.
 * 板块热议, 采用 key-value 保存整个热议贴列表.
 * 板块置顶, 采用 key-value 保存整个置顶帖id列表.
 */
@Slf4j
@Service
public class NoteThreadHotCacheService {

    private static final String PLACE_HOLDER_ELEMENT = "__place_holder__";
    private static final long PLACE_HOLDER_SCORE = Long.MAX_VALUE - 1;

    private final StringRedisTemplate stringRedisTemplate;

    public NoteThreadHotCacheService(StringRedisTemplate stringRedisTemplate) {
        this.stringRedisTemplate = stringRedisTemplate;
    }

    boolean isPopulating(String threadId) {
        String cacheKey = threadId2key(threadId);
        Set<String> set = stringRedisTemplate.opsForZSet().reverseRange(cacheKey, 0, 0);
        return set != null && set.size() != 0 && !set.contains(PLACE_HOLDER_ELEMENT);
    }

    @Async
    void populateAsync(String threadId, List<NoteThreadHot> hotList) {
        if (!isPopulating(threadId)) {
            populate(threadId, hotList);
        }
    }

    void populate(String threadId, List<NoteThreadHot> hotList) {
        if (hotList == null) {
            throw new KbException(KbCode.ILLEGAL_ARGUMENT, "hotList list empty");
        }
        List<NoteThreadHot> cacheHotList = hotList.size() > NoteCacheConfig.THREAD_HOT_LIST_INIT_COUNT ?
                hotList.subList(0, NoteCacheConfig.THREAD_HOT_LIST_INIT_COUNT) : hotList;
        final Set<StringRedisConnection.StringTuple> cacheData = cacheHotList.stream()
                .filter(NoteThreadHotCacheService::isValidHotModel)
                .map(hot -> string2tuple(stringRedisTemplate, hot2data(hot), hot.getRefreshTime()))
                .collect(Collectors.toSet());
        cacheData.add(string2tuple(stringRedisTemplate, PLACE_HOLDER_ELEMENT, PLACE_HOLDER_SCORE));
        String cacheKey = threadId2key(threadId);
        stringRedisTemplate.executePipelined((RedisCallback<Object>) connection -> {
            StringRedisConnection conn = (StringRedisConnection) connection;
            conn.del(cacheKey);
            conn.zAdd(cacheKey, cacheData);
            conn.expire(cacheKey, NoteCacheConfig.THREAD_HOT_LIST_EXPIRE.getSeconds());
            return null;
        });
    }

    void addHot(NoteThreadHot hot) {
        if (!isValidHotModel(hot)) {
            return;
        }
        String cacheKey = threadId2key(hot.getThreadId());
        stringRedisTemplate.execute((RedisCallback<Object>) connection -> {
            StringRedisConnection conn = (StringRedisConnection) connection;
            if (conn.exists(cacheKey)) {
                conn.zAdd(cacheKey, hot.getRefreshTime(), hot2data(hot));
            }
            return conn.closePipeline();
        });
    }

    void removeHot(NoteThreadHot hot) {
        if (hot == null || hot.getThreadId() == null) {
            return;
        }
        String cacheKey = threadId2key(hot.getThreadId());
        stringRedisTemplate.opsForZSet().remove(cacheKey, hot2data(hot));
    }

    /**
     * 获取板块热议列表
     *
     * @param threadId 板块 id
     * @return 若缓存无效, 则返回 Optional.empty();
     * 若缓存有效, 不论元素个数是否为0, 返回 Optional.of(list)
     */
    @SuppressWarnings("unchecked")
    Optional<List<NoteThreadHot>> getHotList(String threadId, int page, int pageSize) {
        if (NoteCacheConfig.CACHE_DEBUG) log.info("[cache_debug][cache] get thread hot list: " +
                threadId + ", [" + page + "," + pageSize + "]");
        if (page <= 0) page = 1;
        if (pageSize <= 0) pageSize = 15;
        int pageIndex = page * pageSize;
        if (pageIndex > NoteCacheConfig.THREAD_HOT_LIST_INIT_COUNT) {
            // 所请求数据已超过缓存最大值, 视为缓存有效, 但数据为空
            return Optional.of(Collections.emptyList());
        }
        String cacheKey = threadId2key(threadId);
        List<Object> redisResult;
        try {
            if (page == 1) {
                redisResult = stringRedisTemplate.executePipelined((RedisCallback<Object>) connection -> {
                    StringRedisConnection conn = (StringRedisConnection) connection;
                    conn.zRevRange(cacheKey, 0, pageIndex);
                    return null;
                });
            } else {
                int startIndex = (page - 1) * pageSize + 1;
                redisResult = stringRedisTemplate.executePipelined((RedisCallback<Object>) connection -> {
                    StringRedisConnection conn = (StringRedisConnection) connection;
                    conn.zRevRange(cacheKey, 0, 0);
                    conn.zRevRange(cacheKey, startIndex, pageIndex);
                    return null;
                });
            }
        } catch (Exception e) {
            log.error("get hot list by threadId from redis fail: " + threadId, e);
            return Optional.empty();
        }

        if (redisResult.size() == 0) {
            return Optional.empty();
        } else {
            List<String> dataList = new ArrayList<>(pageSize + 1);
            for (Object obj : redisResult) {
                dataList.addAll((Set<String>) obj);
            }
            if (dataList.size() == 0 || !PLACE_HOLDER_ELEMENT.equals(dataList.remove(0))) {
                // 不包含占位数据说明数据有误, 视为缓存无效
                return Optional.empty();
            } else {
                return Optional.of(dataList.stream()
                        .map(data -> GsonUtils.toModelIgnoreError(data, NoteThreadHot.class))
                        .filter(NoteThreadHotCacheService::isValidHotModel)
                        .collect(Collectors.toList()));
            }
        }
    }

    private static String threadId2key(String threadId) {
        return NoteCacheConfig.THREAD_HOT_LIST_KEY + threadId;
    }

    private static boolean isValidHotModel(NoteThreadHot hot) {
        return hot != null && hot.getThreadId() != null && hot.getNoteId() != null && hot.getRefreshTime() != null;
    }

    private static String hot2data(NoteThreadHot hot) {
        // 为了避免向 redis zset 插入重复数据, 并方便执行 redis.zrem() 命令, 序列化为字符串的操作必须满足幂等性:
        // 即对数据相同的 NoteThreadHot 多次执行序列化, 其结果必须完全相同.
        // 而 json 协议对字段顺序没有限制, 所以使用 json 库进行序列化并不可靠.
        // 这里手工完成 json 序列化.
        StringBuilder sb = new StringBuilder();
        sb.append("{");
        sb.append("\"id\":").append("\"").append(hot.getId()).append("\"").append(",");
        sb.append("\"noteId\":").append("\"").append(hot.getNoteId()).append("\"").append(",");
        sb.append("\"threadId\":").append("\"").append(hot.getThreadId()).append("\"").append(",");
        if (hot.getTitle() != null) {
            sb.append("\"title\":").append("\"")
                    .append(StringEscapeUtils.escapeJava(hot.getTitle()))
                    .append("\"").append(",");
        }
        if (hot.getSubTitle() != null) {
            sb.append("\"subTitle\":").append("\"")
                    .append(StringEscapeUtils.escapeJava(hot.getSubTitle()))
                    .append("\"").append(",");
        }
        if (hot.getCover() != null) {
            sb.append("\"cover\":").append("\"").append(hot.getCover()).append("\"").append(",");
        }
        sb.append("\"createTime\":").append(hot.getCreateTime()).append(",");
        sb.append("\"refreshTime\":").append(hot.getRefreshTime());
        sb.append("}");
        return sb.toString();
    }

    @SuppressWarnings("ConstantConditions")
    private static StringRedisConnection.StringTuple string2tuple(
            StringRedisTemplate stringRedisTemplate, String data, double score) {
        return new DefaultStringTuple(
                stringRedisTemplate.getStringSerializer().serialize(data), data, score);
    }

}
