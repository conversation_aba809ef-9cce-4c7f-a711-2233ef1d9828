package com.kaiba.m.core.service.note.setting;

import com.kaiba.lib.base.constant.KbProperties;
import com.kaiba.lib.base.constant.note.NoteThreadCondition;
import com.kaiba.m.core.domain.note.NoteSetting;
import com.kaiba.m.core.repository.note.NoteSettingRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;

/**
 * author: lyux
 * date: 2022-12-03
 *
 * 平台/电台全局板块设置.
 */
@Slf4j
@Service
public class NoteSettingService {

    private final NoteSettingRepository settingRepository;

    public NoteSettingService(NoteSettingRepository settingRepository) {
        this.settingRepository = settingRepository;
    }

    public NoteSetting updatePlatformCondition(NoteThreadCondition condition) {
        return updateSiteCondition(KbProperties.PLATFORM_SITE_ID, condition);
    }

    public NoteSetting updateSiteCondition(Integer siteId, NoteThreadCondition condition) {
        log.info("update site condition: " + siteId + " -> " + condition);
        return settingRepository.upsertCondition(siteId, condition);
    }

    public NoteSetting getPlatformSetting() {
        return settingRepository.findFirstBySiteId(KbProperties.PLATFORM_SITE_ID)
                .orElseGet(() -> {
                    NoteSetting setting = new NoteSetting();
                    setting.setSiteId(KbProperties.PLATFORM_SITE_ID);
                    setting.setCondition(NoteThreadCondition.FREE.getValue());
                    return setting;
                });
    }

    public NoteSetting getSiteSetting(Integer siteId) {
        if (KbProperties.PLATFORM_SITE_ID.equals(siteId)) {
            return getPlatformSetting();
        } else {
            return settingRepository.findFirstBySiteId(siteId).orElseGet(this::getPlatformSetting);
        }
    }

    public Page<NoteSetting> getSettingList(Integer page, Integer pageSize) {
        int p = page == null ? 1 : page;
        int ps = pageSize == null ? 20 : pageSize;
        return settingRepository.findByOrderByIdAsc(PageRequest.of(p, ps));
    }

}
