package com.kaiba.m.core.service.note.heat;

import lombok.Getter;

import java.util.List;

/**
 * author: lyux
 * date: 2023-03-24
 */
@Getter
public class HeatedNoteListResult {

    private final List<String> list;
    private String lastMark;
    private String currentMark;

    HeatedNoteListResult(List<String> list, String lastMark, String currentMark) {
        this.list = list;
        this.lastMark = lastMark;
        this.currentMark = currentMark;
    }

    HeatedNoteListResult(List<String> list) {
        this.list = list;
    }

    @Override
    public String toString() {
        return "[" + lastMark + "," + currentMark + "][" + String.join(",", list) + "]";
    }

}
