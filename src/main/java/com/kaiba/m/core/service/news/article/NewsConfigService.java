package com.kaiba.m.core.service.news.article;

import com.kaiba.lib.base.constant.KbHttpHeaders;
import com.kaiba.lib.base.constant.KbModule;
import com.kaiba.lib.base.constant.KbProperties;
import com.kaiba.lib.base.constant.note.NoteThreadCondition;
import com.kaiba.lib.base.domain.note.NoteThreadModel;
import com.kaiba.lib.base.domain.site.SiteModel;
import com.kaiba.lib.base.middleware.adminlogrecorder.AdminLogAction;
import com.kaiba.lib.base.middleware.adminlogrecorder.AdminLogRecorder;
import com.kaiba.lib.base.response.KbCode;
import com.kaiba.lib.base.response.KbException;
import com.kaiba.lib.base.service.IAdminLogService;
import com.kaiba.lib.base.service.INoteService;
import com.kaiba.lib.base.service.ISiteService;
import com.kaiba.lib.base.util.ServletRequestUtils;
import com.kaiba.m.core.constant.news.NewsConsts;
import com.kaiba.m.core.domain.news.article.NewsModuleConfig;
import com.kaiba.m.core.domain.news.article.NewsSiteConfig;
import com.kaiba.m.core.repository.news.article.NewsChannelConfigRepository;
import com.kaiba.m.core.repository.news.article.NewsModuleConfigRepository;
import com.kaiba.m.core.repository.news.article.NewsSiteConfigRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Optional;

/**
 * author: lyux
 * date: 2023-07-27
 */
@Slf4j
@Service
public class NewsConfigService {

    private final NewsSiteConfigRepository siteConfigRepository;
    private final NewsModuleConfigRepository moduleConfigRepository;
    private final AdminLogRecorder adminLogRecorder;
    private final ISiteService siteService;
    private final INoteService noteService;

    public NewsConfigService(
            NewsSiteConfigRepository siteConfigRepository,
            NewsModuleConfigRepository moduleConfigRepository,
            NewsChannelConfigRepository channelConfigRepository,
            IAdminLogService adminLogService,
            ISiteService siteService,
            INoteService noteService
    ) {
        this.siteConfigRepository = siteConfigRepository;
        this.moduleConfigRepository = moduleConfigRepository;
        this.adminLogRecorder = AdminLogRecorder.builder(adminLogService)
                .module(KbModule.NEWS_NEO)
                .registerUnit(NewsConsts.UNIT_SITE_CONFIG, "电台配置管理")
                .registerUnit(NewsConsts.UNIT_MODULE_CONFIG, "模块配置管理")
                .create();
        this.siteService = siteService;
        this.noteService = noteService;
    }

    // ----------------------------------------------------------

    public NewsSiteConfig createSiteConfig(Integer siteId, String orgName) {
        if (siteId == null) {
            throw new KbException(KbCode.ILLEGAL_ARGUMENT, "need siteId").r("请填写 siteId");
        }
        Integer creator = getOrDefaultCreator();
        SiteModel site = siteService.getSiteById(siteId).dataOrThrow();
        NoteThreadModel thread = noteService.createThread(
                        creator, siteId,
                        "新资讯电台总控板块-" + site.getName(),
                        "新资讯电台总控板块-" + site.getName(),
                        0, 0, false, null,
                        NoteThreadCondition.FREE.getValue())
                .dataOrThrow();
        NewsSiteConfig config = new NewsSiteConfig();
        config.setSiteId(siteId);
        config.setSiteName(site.getName());
        config.setOrgName(orgName);
        config.setThreadId(thread.getId());
        config.setCreateTime(System.currentTimeMillis());
        NewsArticleModelHelper.SITE_CONFIG_VERIFIER.verify(config);
        NewsSiteConfig created = siteConfigRepository.insert(config);
        log.info("news site config created: " + created);
        adminLogRecorder.on(NewsConsts.UNIT_SITE_CONFIG)
                .by(creator).act(AdminLogAction.CREATE).ref1(created.getId()).add();
        return created;
    }

    public NewsSiteConfig updateSiteOrgName(Integer siteId, String orgName) {
        NewsSiteConfig old = siteConfigRepository.findFirstBySiteId(siteId)
                .orElseThrow(() -> new KbException(KbCode.RESOURCE_NOT_FOUND));
        NewsSiteConfig updated = siteConfigRepository.updateOrgName(siteId, orgName);
        log.info("news site config update orgName: " + siteId + ", " + old.getOrgName() + " -> " + orgName);
        adminLogRecorder.on(NewsConsts.UNIT_SITE_CONFIG)
                .act(AdminLogAction.UPDATE_DATA, "默认组织名称变更: " + old.getOrgName() + " -> " + orgName)
                .ref1(old.getId()).add();
        return updated;
    }

    public NewsSiteConfig getOrCreateSiteConfig(Integer siteId) {
        return siteConfigRepository.findFirstBySiteId(siteId)
                .orElseGet(() -> createSiteConfig(siteId, null));
    }

    public Optional<NewsSiteConfig> getSiteConfig(Integer siteId) {
        return siteConfigRepository.findFirstBySiteId(siteId);
    }

    public void traverseAndCreateSiteConfig() {
        List<SiteModel> siteList = siteService.getOpenSiteList().dataOrThrow();
        for (SiteModel site : siteList) {
            NewsSiteConfig config = getOrCreateSiteConfig(site.getId());
            log.info("get or create site config: " + config);
        }
    }

    // ------------------------------------------------------

    public NewsModuleConfig createModuleConfig(Integer siteId, String module, String desc, String orgName) {
        if (siteId == null) {
            throw new KbException(KbCode.ILLEGAL_ARGUMENT, "need siteId").r("请填写 siteId");
        }
        Integer creator = getOrDefaultCreator();
        SiteModel site = siteService.getSiteById(siteId).dataOrThrow();
        NoteThreadModel thread = noteService.createThread(
                        creator, siteId,
                        "新资讯模块总控板块-" + module + "-" + site.getName(),
                        "新资讯模块总控板块-" + module + "-" + site.getName(),
                        0, 0, false, null,
                        NoteThreadCondition.AUTO_REVIEW.getValue())
                .dataOrThrow();
        NewsModuleConfig config = new NewsModuleConfig();
        config.setSiteId(siteId);
        config.setSiteName(site.getName());
        config.setModule(module);
        config.setOrgName(orgName);
        config.setDesc(desc);
        config.setThreadId(thread.getId());
        config.setCreateTime(System.currentTimeMillis());
        NewsArticleModelHelper.MODULE_CONFIG_VERIFIER.verify(config);
        NewsModuleConfig created = moduleConfigRepository.insert(config);
        log.info("news module config created: " + created);
        adminLogRecorder.on(NewsConsts.UNIT_MODULE_CONFIG)
                .by(creator).act(AdminLogAction.CREATE).ref1(created.getId()).add();
        return created;
    }

    public NewsModuleConfig updateModuleConfig(String id, String desc, String orgName) {
        Integer creator = getOrDefaultCreator();
        NewsModuleConfig moduleConfig = moduleConfigRepository.findById(id).orElseThrow(() -> new KbException(KbCode.RESOURCE_NOT_FOUND));
        moduleConfigRepository.updateDescAndOrgNameById(id, desc, orgName);
        log.info("news module config update id:{},desc:{},orgName:{} ", id, desc, orgName);
        adminLogRecorder.on(NewsConsts.UNIT_MODULE_CONFIG)
                .by(creator).act(AdminLogAction.UPDATE).ref1(id)
                .info(String.format("news module config update id:{},desc:{},orgName:{} ", id, desc, orgName))
                .snapshot(moduleConfig).add();
        return moduleConfig;
    }

    public Optional<NewsModuleConfig> getModuleConfig(Integer siteId, String module) {
        return moduleConfigRepository.findFirstBySiteIdAndModule(siteId, module);
    }

    // ------------------------------------------------------

    private static Integer getOrDefaultCreator() {
        Integer creator = null;
        HttpServletRequest request = ServletRequestUtils.getCurrentRequest();
        if (request != null) {
            creator = KbHttpHeaders.KB_USER_ID.getValidIntegerHeaderOrNull(request);
        }
        if (creator == null) {
            creator = KbProperties.SYSTEM_USER_ID;
        }
        return creator;
    }

}
