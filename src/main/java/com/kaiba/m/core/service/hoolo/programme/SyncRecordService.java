package com.kaiba.m.core.service.hoolo.programme;

import com.google.common.collect.Lists;
import com.kaiba.lib.base.domain.hoolo.SyncRecordModel;
import com.kaiba.m.core.domain.hoolo.SyncNewsArticleRecord;
import com.kaiba.m.core.model.education.recitation.MemberModel;
import com.kaiba.m.core.repository.hoolo.programme.SyncNewsArticleRecordRepository;
import com.kaiba.m.core.service.hoolo.programme.convert.HooloNewsMapper;
import java.util.List;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;

/**
 * Description: 资讯同步记录Service
 * Author: ZM227
 * Date: 2024/10/14 11:53
 */
@Slf4j
@Service
public class SyncRecordService {

    @Resource
    private SyncNewsArticleRecordRepository recordRepository;
    @Resource
    private HooloNewsMapper hooloNewsMapper;

    public Page<SyncRecordModel> searchRecordsByProgrammeKey(String programmeId, boolean onlyValid, int page, int pageSize) {
        Page<SyncNewsArticleRecord> articleRecords;
        Pageable request = PageRequest.of(page - 1, pageSize, Sort.Direction.DESC, "finishTime");
        if (onlyValid) {
            articleRecords = recordRepository.findSyncNewsArticleRecordsByProgrammeIdAndSyncArticleCountGreaterThanOrderByFinishTimeDesc(
                programmeId, 0, request);
        } else {
            articleRecords = recordRepository.findSyncNewsArticleRecordsByProgrammeIdOrderByFinishTimeDesc(
                programmeId, request);
        }
        return new PageImpl<>(
            articleRecords.getContent().stream().map(a -> hooloNewsMapper.syncRecordToModel(a))
                .collect(Collectors.toList()), articleRecords.getPageable(), articleRecords.getTotalElements());
    }

    public SyncRecordModel searchRecordById(String recordId) {
        SyncNewsArticleRecord articleRecord = recordRepository.findSyncNewsArticleRecordById(recordId);
        return hooloNewsMapper.syncRecordToModel(articleRecord);
    }

}
