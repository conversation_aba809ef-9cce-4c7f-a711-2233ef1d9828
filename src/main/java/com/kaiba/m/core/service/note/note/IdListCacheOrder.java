package com.kaiba.m.core.service.note.note;

import com.kaiba.lib.base.constant.note.NoteOrder;
import com.kaiba.m.core.domain.note.Note;
import lombok.Getter;

import java.util.function.ToDoubleFunction;

/**
 * author: lyux
 * date: 19-6-14
 */
@Getter
class IdListCacheOrder {

    final NoteOrder noteOrder;
    final ToDoubleFunction<Note> orderValueProvider;

    IdListCacheOrder(NoteOrder noteOrder, ToDoubleFunction<Note> orderValueProvider) {
        this.noteOrder = noteOrder;
        this.orderValueProvider = orderValueProvider;
    }

    double getOrderValue(Note note) {
        return orderValueProvider.applyAsDouble(note);
    }

}
