package com.kaiba.m.core.service.wx;

import com.kaiba.m.core.domain.wx.WxOpenAppAuthInfo;
import com.kaiba.m.core.repository.wx.WxOpenAppAuthInfoRepository;
import lombok.NonNull;
import me.chanjar.weixin.open.api.impl.WxOpenInRedisTemplateConfigStorage;
import me.chanjar.weixin.open.bean.result.WxOpenQueryAuthResult;
import org.springframework.data.redis.core.StringRedisTemplate;

/**
 * <AUTHOR>
 * @version WxOpenInRDSWithDbConfigStorage, v0.1 2024/4/24 13:42 daopei Exp $
 **/
public class WxOpenInRDSWithDbConfigStorage extends WxOpenInRedisTemplateConfigStorage {

    private final WxOpenAppAuthInfoRepository authInfoRepository;

    public WxOpenInRDSWithDbConfigStorage(
            @NonNull WxOpenAppAuthInfoRepository authInfoRepository,
            @NonNull StringRedisTemplate stringRedisTemplate, String keyPrefix
    ) {
        super(stringRedisTemplate, keyPrefix);
        this.authInfoRepository = authInfoRepository;
    }

    @Override
    public String getAuthorizerRefreshToken(String appId) {
        String refreshToken = super.getAuthorizerRefreshToken(appId);
        if (refreshToken != null) {
            return refreshToken;
        }
        //缓存为空则从db获取
        refreshToken = authInfoRepository.findById(appId)
                .map(WxOpenAppAuthInfo::getAuthorizerRefreshToken)
                .orElse(null);
        super.updateAuthorizerRefreshToken(appId, refreshToken);
        return refreshToken;
    }

    @Override
    public void updateAuthorizerRefreshToken(String appId, String authorizerRefreshToken) {
        //缓存更新
        super.updateAuthorizerRefreshToken(appId, authorizerRefreshToken);
        WxOpenAppAuthInfo authInfo = new WxOpenAppAuthInfo();
        authInfo.setId(appId);
        authInfo.setAuthorizerRefreshToken(authorizerRefreshToken);
        //db更新
        authInfoRepository.upsert(authInfo);
    }

    @Override
    public void updateAuthorizerAccessToken(String appId, String authorizerAccessToken, int expiresInSeconds) {
        super.updateAuthorizerAccessToken(appId, authorizerAccessToken, expiresInSeconds);
        WxOpenAppAuthInfo authInfo = new WxOpenAppAuthInfo();
        authInfo.setId(appId);
        authInfo.setAuthorizerAccessToken(authorizerAccessToken);
        authInfo.setExpiresIn(expiresInSeconds);
        //db更新
        authInfoRepository.upsert(authInfo);
    }

    public void authSuccess(WxOpenQueryAuthResult authResult) {
        WxOpenAppAuthInfo authInfo = new WxOpenAppAuthInfo();
        authInfo.setId(authResult.getAuthorizationInfo().getAuthorizerAppid());
        authInfo.setAuthorizerAccessToken(authResult.getAuthorizationInfo().getAuthorizerAccessToken());
        authInfo.setAuthorizerRefreshToken(authResult.getAuthorizationInfo().getAuthorizerRefreshToken());
        authInfo.setFuncInfos(authResult.getAuthorizationInfo().getFuncInfo());
        authInfo.setExpiresIn(authResult.getAuthorizationInfo().getExpiresIn());
        //db更新
        authInfoRepository.upsert(authInfo);
    }


}
