package com.kaiba.m.core.service.news.legacy;

import com.kaiba.m.core.domain.news.legacy.SiteNewsThread;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class DefaultNewsViewGenerator implements INewsViewGenerator {

    private final NewsService newsService;

    public DefaultNewsViewGenerator(NewsService newsService) {
        this.newsService = newsService;
    }

    @Override
    public Integer getViewCount(String newsId, Integer siteId, Integer viewCount) {
        int v = viewCount == null ? 1 : viewCount;
        SiteNewsThread thread = siteId == null ? null : newsService.getSiteNewsThreadBySiteId(siteId).orElse(null);
        if (thread != null && thread.getMultiply() != null) {
            int base = thread.getBase() == null ? 0 : thread.getBase();
            int n = thread.getMultiply();
            int f = Math.abs(newsId.hashCode()) % 137;
            return base + v * n + ( ((v + f + 31) * 13) % n);
        } else {
            return v;
        }
    }
}
