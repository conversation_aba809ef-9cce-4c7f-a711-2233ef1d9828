package com.kaiba.m.core.service.issuetask.expert;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.util.Set;
import java.util.concurrent.TimeUnit;

/**
 * author: lyux
 * date: 18-10-13
 */
@Service
public class ExpertDroppedService {

    private static final String PREFIX_TIMEOUT = "issue_expert_timeout_";
    private static final String PREFIX_REFUSED = "issue_expert_refused_";

    private static final long MAX_EXPIRE_TIME = TimeUnit.HOURS.toSeconds(24);

    private final StringRedisTemplate redisTemplate;

    @Autowired
    public ExpertDroppedService(StringRedisTemplate redisTemplate) {
        this.redisTemplate = redisTemplate;
    }

    public void taskTimeout(String issueId, Integer expertId) {
        String key = timeoutKey(issueId);
        redisTemplate.opsForSet().add(key, Integer.toString(expertId));
        redisTemplate.expire(key, MAX_EXPIRE_TIME, TimeUnit.SECONDS);
    }

    public void taskRefused(String issueId, Integer expertId) {
        String key = refusedKey(issueId);
        redisTemplate.opsForSet().add(key, Integer.toString(expertId));
        redisTemplate.expire(key, MAX_EXPIRE_TIME, TimeUnit.SECONDS);
    }

    public boolean isAlreadyTimeout(String issueId, Integer expertId) {
        Boolean result = redisTemplate.opsForSet().isMember(timeoutKey(issueId), Integer.toString(expertId));
        return result != null && result;
    }

    public boolean isAlreadyRefused(String issueId, Integer expertId) {
        Boolean result = redisTemplate.opsForSet().isMember(refusedKey(issueId), Integer.toString(expertId));
        return result != null && result;
    }

    public Set<String> getTimeoutExpertIdSetByIssue(String issueId) {
        return redisTemplate.opsForSet().members(timeoutKey(issueId));
    }

    public Set<String> getRefusedExpertIdSetByIssue(String issueId) {
        return redisTemplate.opsForSet().members(refusedKey(issueId));
    }

    public void deleteByIssue(String issueId) {
        redisTemplate.delete(timeoutKey(issueId));
        redisTemplate.delete(refusedKey(issueId));
    }

    private static String timeoutKey(String issueId) {
        return PREFIX_TIMEOUT + issueId;
    }

    private static String refusedKey(String issueId) {
        return PREFIX_REFUSED + issueId;
    }

}
