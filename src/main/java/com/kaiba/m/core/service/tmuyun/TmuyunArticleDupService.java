package com.kaiba.m.core.service.tmuyun;

import com.kaiba.lib.base.domain.tmuyun.tmuyun.ArticleApiModel;
import com.kaiba.lib.base.response.KbCode;
import com.kaiba.lib.base.response.KbException;
import com.kaiba.lib.base.util.StringUtils;
import com.kaiba.m.core.service.tmuyun.dupname.ITmuyunArticleDupNameStrategy;

import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2023/02/07 14:07
 **/
@Slf4j
@Service
public class TmuyunArticleDupService implements ITmuyunArticleDupNameStrategy, ApplicationContextAware {

    private final Map<String, ITmuyunArticleDupNameStrategy> strategyMap = new ConcurrentHashMap<>();

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        applicationContext.getBeansOfType(ITmuyunArticleDupNameStrategy.class)
            .forEach((k, v) -> strategyMap.putIfAbsent(k.toLowerCase(), v));
    }

    @Override
    public ArticleApiModel resolveDupName(ArticleApiModel model) {
        String strategyName = chooseStrategy(model);

        if(StringUtils.isEmpty(strategyName)) {
            return model;
        }

        ITmuyunArticleDupNameStrategy strategy = Optional
            .ofNullable(strategyMap.get(strategyName))
            .orElseThrow(() -> new KbException(KbCode.RESOURCE_NOT_FOUND, "invalid ref type: " + model.getRefType()));
        return strategy.resolveDupName(model);
    }
}
