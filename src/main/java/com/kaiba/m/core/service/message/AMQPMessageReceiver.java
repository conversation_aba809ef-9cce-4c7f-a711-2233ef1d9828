package com.kaiba.m.core.service.message;

import com.kaiba.lib.base.constant.usermessage.UserMessageType;
import com.kaiba.lib.base.domain.user.UserMessageModel;
import com.kaiba.lib.base.middleware.KbEventReceiver;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * author: lyux
 * date: 18-9-29
 */
@Slf4j
@Component
public class AMQPMessageReceiver extends KbEventReceiver<UserMessageModel> {

    private final MessageServiceFactory factory;

    @Autowired
    public AMQPMessageReceiver(MessageServiceFactory factory) {
        this.factory = factory;
    }

    @Override
    public void onReceiveEvent(UserMessageModel data) {
        if (!UserMessageType.valueOf(data.getType()).isPresent()) {
            log.warn("on receive user message, wrong type: " + data);
            return;
        }
        factory.getMessageService(data.getEndpoint()).ifPresent(service -> service.addMessage(data));
    }

    @Override
    public Class<UserMessageModel> getEventType() {
        return UserMessageModel.class;
    }

}
