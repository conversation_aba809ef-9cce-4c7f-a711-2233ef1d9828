package com.kaiba.m.core.service.kri.matcher;

import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.LoadingCache;
import com.kaiba.lib.base.domain.kri.KbResource;
import com.kaiba.m.core.domain.kri.matcher.KRIExistMatcher;
import com.kaiba.m.core.middleware.instantcache.KbInstantCaffeineService;
import com.kaiba.m.core.repository.kri.matcher.KRIExistMatcherRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * author: lyux
 * date: 2024-12-16
 */
@Slf4j
@Component
class MatcherCacheByExist {

    private final KRIExistMatcherRepository matcherRepository;
    private final LoadingCache<String, Map<String, String>> matcherCache;

    public MatcherCacheByExist(
            KRIExistMatcherRepository matcherRepository,
            KbInstantCaffeineService instantCaffeineService
    ) {
        this.matcherRepository = matcherRepository;
        this.matcherCache = instantCaffeineService
                .registerInstantCache("res_matcher_by_exist", Caffeine.newBuilder()
                        .expireAfterWrite(Duration.ofMinutes(10))
                        .refreshAfterWrite(Duration.ofSeconds(59))
                        .build(this::getMatchedDataFromDBByCacheKey));
    }

    // ------------------------------------------------------------------

    public Map<String, String> match(KbResource res) {
        String cacheKey = getCacheKey(res.getBiz(), res.getUnit(), res.getRef1(), res.getRef2(), res.getRef3());
        Map<String, String> result = matcherCache.get(cacheKey);
        return result == null ? Collections.emptyMap() : result;
    }

    public void invalidate(KbResource res) {
        String cacheKey = getCacheKey(res.getBiz(), res.getUnit(), res.getRef1(), res.getRef2(), res.getRef3());
        matcherCache.invalidate(cacheKey);
    }

    // ------------------------------------------------------------------

    private Map<String, String> getMatchedDataFromDBByCacheKey(String cacheKey) {
        KbResource res = KbResource.parseStringOrNull(cacheKey);
        if (res == null) {
            return Collections.emptyMap();
        } else {
            return getMatchedDataFromDB(res);
        }
    }

    private Map<String, String> getMatchedDataFromDB(KbResource resource) {
        List<KRIExistMatcher> matchers = matcherRepository
                .getPageByExist(resource, null, 0, 100).getContent();
        if (matchers.isEmpty()) {
            return Collections.emptyMap();
        } else {
            Map<String, String> result = new HashMap<>(matchers.size());
            for (KRIExistMatcher matcher : matchers) {
                if (matcher.getData() == null) {
                    result.put(matcher.getFact(), MatcherUtil.DATA_EMPTY);
                } else {
                    result.put(matcher.getFact(), matcher.getData());
                }
            }
            return result;
        }
    }

    private static String getCacheKey(String biz, String unit, String ref1, String ref2, String ref3) {
        return "KbResMatchByValue:" + KbResource.asString(biz, unit, ref1, ref2, ref3);
    }

}
