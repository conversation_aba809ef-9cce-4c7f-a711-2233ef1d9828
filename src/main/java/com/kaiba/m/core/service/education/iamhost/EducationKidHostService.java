package com.kaiba.m.core.service.education.iamhost;

import com.google.gson.reflect.TypeToken;
import com.kaiba.lib.base.assembler.Mapper;
import com.kaiba.lib.base.cache.redis.IntervalBasedListCache;
import com.kaiba.lib.base.domain.education.KidModel;
import com.kaiba.lib.base.domain.education.iamhost.KidHostModel;
import com.kaiba.lib.base.response.KbCode;
import com.kaiba.lib.base.response.KbException;
import com.kaiba.m.core.domain.education.iamhost.KidHost;
import com.kaiba.m.core.model.education.KidBaseModel;
import com.kaiba.m.core.repository.education.iamhost.KidHostRepository;
import com.kaiba.m.core.service.education.EducationK12KidService;
import com.kaiba.m.core.util.JsonUtils;

import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.*;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/03/25 11:20
 */
@Slf4j
@Service
public class EducationKidHostService {

    private static final Integer CACHE_KID_HOST_SIZE = 200;
    private static final String REDIS_KID_HOST_DETAIL_PREFIX = "java_core_education_kid_host_kid_id_";
    private static final String REDIS_KID_HOST_LIST_PREFIX = "java_core_education_kid_host_list_";
    private static final String TAG_KID_HOST = "KidHost";

    private final KidHostRepository kidHostRepository;
    private final EducationK12KidService kidService;
    private final StringRedisTemplate redisTemplate;
    private final IntervalBasedListCache<String, KidBaseModel> kidListCache;

    public EducationKidHostService(
            KidHostRepository kidHostRepository,
            EducationK12KidService kidService,
            StringRedisTemplate redisTemplate
    ) {
        this.kidHostRepository = kidHostRepository;
        this.kidService = kidService;
        this.redisTemplate = redisTemplate;
        this.kidListCache = new IntervalBasedListCache.Builder<String, KidBaseModel>(redisTemplate)
            .interval(Duration.ofMinutes(3)).ttl(5)
            .redisKeyPrefix(REDIS_KID_HOST_LIST_PREFIX)
            .redisDataDeserializer(data -> JsonUtils.getGson().fromJson(data, new TypeToken<List<KidModel>>() {}.getType()))
            .dataProvider(this::getKidListFromDB)
            .create();
    }

    public KidHostModel addKidHost(KidHostModel model) {
        KidHost kidHost = Mapper.map(model, KidHost.class);
        long time = System.currentTimeMillis();
        kidHost.setCreateTime(time);
        kidHost.setUpdateTime(time);
        KidModel kidModel = kidService.getKidById(model.getKidId()).orElse(new KidModel());
        kidHost.setName(kidModel.getRealName());
        kidHost.setSchoolName(kidModel.getSchoolName());
        KidHost save = kidHostRepository.upsert(kidHost);
        kidService.updateKidTag(model.getKidId(), TAG_KID_HOST);
        return Mapper.map(save, KidHostModel.class);
    }

    public KidHostModel editKidHost(KidHostModel model) {
        KidHost kidHost = Mapper.map(model, KidHost.class);
        KidModel kidModel = kidService.getKidById(model.getKidId()).orElse(new KidModel());
        kidHost.setName(kidModel.getRealName());
        kidHost.setSchoolName(kidModel.getSchoolName());
        KidHost update = kidHostRepository.upsert(kidHost);
        return Mapper.map(update, KidHostModel.class);
    }

    public KidHostModel getKidHostByKidId(String kidId) {
        Optional<KidHost> optional = kidHostRepository.findFirstByKidId(kidId);
        if(!optional.isPresent()) {
            throw new KbException(KbCode.RESOURCE_NOT_FOUND).setReadableMessage("未找到该小主播").li();
        }
        KidHostModel kidHost = Mapper.map(optional.get(), KidHostModel.class);
        KidModel kidModel = kidService.getKidById(kidId).orElse(new KidModel());
        kidHost.setKidModel(kidModel);
        return kidHost;
    }

    public KidHostModel getKidHostByKidIdFromCache(String kidId) {
        String cacheKey = REDIS_KID_HOST_DETAIL_PREFIX + kidId;
        String json = redisTemplate.opsForValue().get(cacheKey);
        if(json == null) {
            KidHostModel kidHost = kidHostRepository.findFirstByKidId(kidId).map(t -> Mapper.map(t, KidHostModel.class)).orElse(new KidHostModel());
            if(kidHost.getId() != null) {
                KidModel kidModel = kidService.getKidById(kidId).orElse(new KidModel());
                kidModel.setTags(null);
                kidModel.setMobiles(null);
                kidHost.setKidModel(kidModel);
            }
            redisTemplate.opsForValue().set(cacheKey, JsonUtils.getGson().toJson(kidHost), Duration.ofMinutes(10));
            return kidHost;
        }
        return JsonUtils.toModel(json, KidHostModel.class);
    }

    public Page<KidHostModel> getKidHostList(String name, String mobile, Integer page, Integer pageSize) {
        Page<KidModel> kidPageData = kidService.getKidList(name, mobile, TAG_KID_HOST, page, pageSize);
        Map<String, KidModel> kidMap = kidPageData.getContent().stream().collect(Collectors.toMap(KidModel::getId, o -> o, (o1, o2) -> o1));
        List<String> kidIds = kidPageData.getContent().stream().map(KidModel::getId).collect(Collectors.toList());
        List<KidHost> kidHostList = kidHostRepository.findAllByKidIdIn(kidIds);
        List<KidHostModel> results = kidHostList.stream().map(t -> {
                KidHostModel kidHostModel = Mapper.map(t, KidHostModel.class);
                kidHostModel.setKidModel(kidMap.get(t.getKidId()));
                return kidHostModel;
        }).collect(Collectors.toList());
        return new PageImpl<>(results, kidPageData.getPageable(), kidPageData.getTotalElements());
    }

    public void removeKidHostById(String kidHostId) {
        kidHostRepository.findById(kidHostId).ifPresent(t -> {
            kidService.removeKidTag(t.getKidId(), TAG_KID_HOST);
            kidHostRepository.deleteById(kidHostId);
        });
    }

    public List<KidBaseModel> searchKid(String queryParam, Integer page, Integer pageSize) {
        if(queryParam == null) {
            Pageable pageable = createPageable(page, pageSize);
            return kidListCache.getList(TAG_KID_HOST, pageable.getPageNumber(), pageable.getPageSize(), null).getList();
        }
        List<KidHost> list = kidHostRepository.findAllByNameRegex(queryParam, createPageable(page, pageSize));
        List<String> kidIds = list.stream().map(KidHost::getKidId).collect(Collectors.toList());
        return kidService.getKidBaseListByKidIdIn(kidIds);
    }

    public List<KidBaseModel> searchKidBySchoolName(String queryParam, Integer page, Integer pageSize) {
        List<KidHost> list = kidHostRepository.findAllBySchoolNameRegex(queryParam, createPageable(page, pageSize));
        List<String> kidIds = list.stream().map(KidHost::getKidId).collect(Collectors.toList());
        return kidService.getKidBaseListByKidIdIn(kidIds);
    }

    public void syncKidHostName(Integer page, Integer pageSize) {
        Page<KidHost> pageData = kidHostRepository.findAll(createPageable(page, pageSize));
        List<String> kidIds = pageData.map(KidHost::getKidId).getContent();
        Map<String, KidModel> kidMap = kidService.getKidListByKidIdIn(kidIds);
        pageData.forEach(t -> {
            KidModel kid = kidMap.get(t.getKidId());
            if(kid != null) {
                kidHostRepository.updateNameAndSchoolName(t.getKidId(), kid.getRealName(), kid.getSchoolName());
            }
        });
    }

    // -----------------------------------------

    private List<KidBaseModel> getKidListFromDB(String tag) {
        Pageable pageable = createPageable(1, CACHE_KID_HOST_SIZE);
        Page<KidHost> pageData = kidHostRepository.findAll(pageable);
        List<String> kidIds = pageData.map(KidHost::getKidId).getContent();
        Map<String, KidBaseModel> kidBaseMap = kidService.getKidBaseListByKidIdIn(kidIds).stream()
                .collect(Collectors.toMap(KidBaseModel::getId, o -> o, (o1, o2) -> o1));
        return pageData.map(t -> kidBaseMap.get(t.getKidId())).getContent();
    }

    private static Pageable createPageable(Integer page, Integer pageSize) {
        int p = (page == null || page < 1) ? 0 : page - 1;
        int ps = (pageSize == null || pageSize > 200) ? 20 : pageSize;
        return PageRequest.of(p, ps);
    }
}
