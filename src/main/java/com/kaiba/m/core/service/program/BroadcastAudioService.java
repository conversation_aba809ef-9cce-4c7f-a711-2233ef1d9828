package com.kaiba.m.core.service.program;

import com.kaiba.lib.base.constant.program.BroadcastAudioPlayer;
import com.kaiba.lib.base.domain.common.AppVersionRange;
import com.kaiba.lib.base.response.KbCode;
import com.kaiba.lib.base.response.KbException;
import com.kaiba.lib.base.util.StringUtils;
import com.kaiba.m.core.domain.program.BroadcastAudio;
import com.kaiba.m.core.repository.program.BroadcastAudioRepository;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Service
public class BroadcastAudioService {
    private final BroadcastAudioRepository audioRepository;

    public BroadcastAudioService(BroadcastAudioRepository audioRepository) {
        this.audioRepository = audioRepository;
    }

    public void save(BroadcastAudio audio) {
        if (StringUtils.isEmpty(audio.getId())){
            Optional<BroadcastAudio> optional = audioRepository.findFirstBySiteIdAndRebroadcastAndIsDefaultIsTrue(audio.getSiteId(), audio.getRebroadcast());
            audio.setIsDefault(!optional.isPresent());
        }
        audioRepository.save(audio);
    }

    public void delete(Integer siteId, String id) {
        audioRepository.deleteById(id);
    }

    public void changeToDefault(String id){
        BroadcastAudio update = audioRepository.findById(id).orElseThrow(()->new KbException(KbCode.RESOURCE_NOT_FOUND).li());
        Optional<BroadcastAudio> old = audioRepository.findFirstBySiteIdAndRebroadcastAndIsDefaultIsTrue(update.getSiteId(), update.getRebroadcast());
        List<BroadcastAudio> list = new ArrayList<>(2);
        old.ifPresent(audio -> {
            if (!audio.getId().equals(id)){
                audio.setIsDefault(false);
                list.add(audio);
            }
        });
        update.setIsDefault(true);
        list.add(update);
        audioRepository.saveAll(list);
    }

    public void updateByChannel(String channel, BroadcastAudioPlayer iOSPlayer, BroadcastAudioPlayer androidPlayer, List<AppVersionRange> ranges){
        String iPlayerName = iOSPlayer == null ? null : iOSPlayer.name();
        String aPlayerName = androidPlayer == null ? null : androidPlayer.name();
        audioRepository.updateByChannel(channel, iPlayerName, aPlayerName, ranges);
    }

    public Optional<BroadcastAudio> getDetailById(String id){
        return audioRepository.findById(id);
    }

    public Optional<BroadcastAudio> getDefaultBySiteIdAndRebroadcast(Integer siteId, Boolean rebroadcast) {
        return audioRepository.findFirstBySiteIdAndRebroadcastAndIsDefaultIsTrue(siteId, rebroadcast);
    }

    public List<BroadcastAudio> getListBySiteIdAndRebroadcast(Integer siteId) {
        return audioRepository.findAllBySiteId(siteId);
    }
}
