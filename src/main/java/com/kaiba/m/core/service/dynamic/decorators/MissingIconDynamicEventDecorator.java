package com.kaiba.m.core.service.dynamic.decorators;

import com.kaiba.lib.base.constant.dynamic.DynamicChannelType;
import com.kaiba.lib.base.domain.dynamic.DynamicEventModel;
import com.kaiba.m.core.service.dynamic.IDynamicEventDecorator;

/**
 * author: lyux
 * date: 19-3-7
 */
public class MissingIconDynamicEventDecorator implements IDynamicEventDecorator {
    @Override
    public DynamicEventModel decorate(DynamicEventModel dynamic, DynamicChannelType type) {
        if (null == dynamic.getIcon()) {
            dynamic.setIcon("http://static.kaiba315.com.cn/common_default_user_avatar.png");
        }
        return dynamic;
    }
}
