package com.kaiba.m.core.service.note;

import com.kaiba.lib.base.domain.note.NoteGrainFlag;
import com.kaiba.lib.base.domain.note.NoteModel;
import com.kaiba.lib.base.util.kburl.KbUrlFactory;
import com.kaiba.m.core.domain.note.Note;
import com.kaiba.m.core.domain.note.NoteReview;
import com.kaiba.m.core.service.green.GreenAudioService;
import com.kaiba.m.core.service.green.GreenImageService;
import com.kaiba.m.core.service.green.GreenTextService;
import com.kaiba.m.core.service.green.GreenVideoService;
import com.kaiba.m.core.service.note.event.NoteCreateOrigin;
import com.kaiba.m.core.service.note.event.NoteEventService;
import com.kaiba.m.core.service.note.note.NoteService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/6/19
 */
@Service
@Slf4j
public class NoteMultimediaScan {
    //todo 需要可配置
    private static final boolean contentScanFlag = false;
    private static final boolean imageScanFlag = false;
    private static final boolean audioScanFlag = false;
    private static final boolean videoScanFlag = false;

    private final GreenAudioService greenAudioService;
    private final GreenImageService greenImageService;
    private final GreenVideoService greenVideoService;
    private final GreenTextService greenTextService;

    private final NoteService noteService;
    private final NoteEventService noteEventService;
    private final NoteConverter noteConverter;

    public NoteMultimediaScan(GreenAudioService greenAudioService, GreenImageService greenImageService,
                              GreenVideoService greenVideoService, GreenTextService greenTextService,
                              NoteService noteService, NoteEventService noteEventService,
                              NoteConverter noteConverter) {
        this.greenAudioService = greenAudioService;
        this.greenImageService = greenImageService;
        this.greenVideoService = greenVideoService;
        this.greenTextService = greenTextService;
        this.noteService = noteService;
        this.noteEventService = noteEventService;
        this.noteConverter = noteConverter;
    }

    @Async
    public void multimediaSafeScanAsync(NoteReview noteReview) {
        if (!greenTextService.scan(noteReview.getContent())) {
            return;
        }

        //audioScanFlag imageScanFlag videoScanFlag CountDownLatch
//        if (audioScanFlag && noteReview.getAudio() != null) {
//            //todo 未必有mp3Key 需等待异步转码完成 待商榷
//            boolean audioFlag = greenAudioService.scan(noteReview.getAudio().getVoiceMP3Key());
//            if (!audioFlag) {
//                return;
//            }
//        }

        if (imageScanFlag && noteReview.getImages() != null && noteReview.getImages().size() > 0) {
            boolean imageFlag = greenImageService.multiScan(
                    noteReview.getImages()
                            .stream()
                            .map(image -> KbUrlFactory.imageAsQiniu().getUrl(image.getUrl()))
                            .collect(Collectors.toList()));
            if (!imageFlag) {
                return;
            }
        }
        if (videoScanFlag && noteReview.getVideo() != null) {
            String videoUrl = KbUrlFactory.imageAsQiniu().getUrl(noteReview.getVideo().getVideoUrl());
            boolean videoFlag = greenVideoService.scan(videoUrl);
            if (!videoFlag) {
                return;
            }
        }
        Note note = noteService.noteReviewAccept(noteReview.getId());
        NoteModel noteModel = noteConverter.note2model(note).setGrain(NoteGrainFlag.MINIMUM_GRAIN).getResult();
        noteEventService.onNoteCreated(note, noteModel, null, noteReview.getId(), NoteCreateOrigin.REVIEW_ACCEPTED);
    }

    public boolean needAsyncScan(Note note) {
        return contentScanFlag && hasMultimedia(note);
    }

    private boolean hasMultimedia(Note note) {
        if (note.getAudio() != null) {
            return true;
        }
        if (note.getImages() != null && note.getImages().size() > 0) {
            return true;
        }
        if (note.getVideo() != null) {
            return true;
        }
        return false;
    }
}
