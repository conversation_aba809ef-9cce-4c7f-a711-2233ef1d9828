package com.kaiba.m.core.service.news.channel;

import com.kaiba.lib.base.assembler.Mapper;
import com.kaiba.lib.base.constant.KbModule;
import com.kaiba.lib.base.constant.KbProperties;
import com.kaiba.lib.base.constant.news.NewsChannel;
import com.kaiba.lib.base.constant.news.article.*;
import com.kaiba.lib.base.domain.news.channel.ChannelConfigUpdateModel;
import com.kaiba.lib.base.domain.news.channel.ChannelMainPageUpdateModel;
import com.kaiba.lib.base.domain.news.channel.ChannelUpdateOrderModel;
import com.kaiba.lib.base.domain.news.pool.bygroup.GroupTopicTab;
import com.kaiba.lib.base.domain.note.NoteThreadModel;
import com.kaiba.lib.base.middleware.adminlogrecorder.AdminLogRecorder;
import com.kaiba.lib.base.response.KbCode;
import com.kaiba.lib.base.response.KbException;
import com.kaiba.lib.base.service.IAdminLogService;
import com.kaiba.lib.base.service.INoteService;
import com.kaiba.m.core.constant.news.NewsConsts;
import com.kaiba.m.core.domain.SeqBasedIdxFirstOrder;
import com.kaiba.m.core.domain.news.channel.ChannelConfig;
import com.kaiba.m.core.domain.news.channel.ChannelMainPage;
import com.kaiba.m.core.domain.news.pool.bygroup.IdsGroup;
import com.kaiba.m.core.repository.news.channel.ChannelConfigRepository;
import com.kaiba.m.core.repository.news.channel.ChannelMainPageRepository;
import com.kaiba.m.core.service.news.pool.bygroup.NewsGroupService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * author: lyux
 * date: 2023-07-27
 */
@Slf4j
@Service
public class NewsChannelService {

    private final ChannelConfigRepository channelRepository;
    private final ChannelMainPageRepository mainPageRepository;
    private final NewsGroupService groupService;
    private final INoteService noteService;
    private final AdminLogRecorder adminLogRecorder;

    public NewsChannelService(
            ChannelConfigRepository channelRepository,
            ChannelMainPageRepository mainPageRepository,
            NewsGroupService groupService,
            INoteService noteService,
            IAdminLogService adminLogService
    ) {
        this.channelRepository = channelRepository;
        this.mainPageRepository = mainPageRepository;
        this.groupService = groupService;
        this.noteService = noteService;
        this.adminLogRecorder = AdminLogRecorder.builder(adminLogService)
                .module(KbModule.NEWS_NEO)
                .unit(NewsConsts.UNIT_CHANNEL, "频道管理")
                .create();
    }

    // ------------------------------------------------------

    public ChannelConfig createChannelConfig(NewsChannel channel, String name, String abbr) {
        long now = System.currentTimeMillis();
        ChannelConfig config = new ChannelConfig();
        config.setChannel(channel.name());
        config.setState(NDisplayState.SHOW.name());
        config.setType(channel.getType().name());
        config.setName(name);
        config.setAbbr(abbr);
        config.setUpdateTime(now);
        config.setCreateTime(now);
        ChannelConfig created = channelRepository.insert(config);
        log.info("news channel config created: " + created);
        adminLogRecorder.on()
                .act("CREATE", "创建频道").ref1(created.getChannel()).add();
        return created;
    }

    public ChannelConfig updateChannelData(ChannelConfigUpdateModel model) {
        ChannelConfig legacy = channelRepository.findFirstByChannel(model.getChannel()).orElseThrow(
                () -> new KbException(KbCode.RESOURCE_NOT_FOUND).li());
        ChannelConfig updated = channelRepository.updateChannelData(
                model.getChannel(), model.getName(), model.getAbbr(), model.getLogo());
        String msg = model.getChannel() + ", " + model.getName() + "|" + model.getAbbr() + "|" + model.getLogo();
        log.info("news channel config data update: " + msg);
        adminLogRecorder.on()
                .act("UPDATE_DATA", "修改频道显示字段: " + msg).ref1(updated.getChannel()).snapshot(legacy).add();
        return updated;
    }

    public ChannelConfig updateChannelReplyStyle(String channelKey, DisplayReplyStyle style) {
        ChannelConfig legacy = channelRepository.findFirstByChannel(channelKey).orElseThrow(
                () -> new KbException(KbCode.RESOURCE_NOT_FOUND).li());
        ChannelConfig updated = channelRepository.updateChannelReplyStyle(channelKey, style);
        String msg = channelKey + ", " + legacy.getReplyStyle() + " -> " + style.name();
        log.info("news channel config data update: " + msg);
        adminLogRecorder.on()
                .act("UPDATE_REPLY_STYLE", "修改频道文章回复设置: " + msg).ref1(updated.getChannel()).add();
        return updated;
    }

    public ChannelConfig updateChannelState(String channelKey, NDisplayState state) {
        ChannelConfig legacy = channelRepository.findFirstByChannel(channelKey).orElseThrow(
                () -> new KbException(KbCode.RESOURCE_NOT_FOUND).li());
        ChannelConfig updated = channelRepository.updateState(channelKey, state);
        String msg = channelKey + ", " + legacy.getState() + " -> " + state.name();
        log.info("news channel config state update: " + msg);
        adminLogRecorder.on()
                .act("CREATE", "修改频道显示状态: " + msg).ref1(updated.getChannel()).add();
        return updated;
    }

    public ChannelConfig updateIdx(String channelKey, Long idx) {
        ChannelConfig channel = channelRepository.findFirstByChannel(channelKey)
                .orElseThrow(() -> new KbException(KbCode.RESOURCE_NOT_FOUND).r("栏目不存在").li());
        long seq = SeqBasedIdxFirstOrder.calculateSeq(channel.getCreateTime(), idx);
        if (channel.getSeq() != null && channel.getSeq() == seq) {
            return channel;
        } else {
            ChannelConfig updated = channelRepository.updateIdx(channelKey, seq, idx);
            log.info("news channel update idx: " + updated);
            adminLogRecorder.on()
                    .act("UPDATE_IDX", "更新手动排序: " + channel.getIdx() + " -> " + idx)
                    .ref1(channel.getChannel()).add();
            return updated;
        }
    }

    public void bulkUpdateIdx(ChannelUpdateOrderModel model) {
        if (model.getOrders() == null || model.getOrders().size() == 0) {
            return;
        }
        Map<String, ChannelConfig> map = new HashMap<>(model.getOrders().size());
        model.getOrders().forEach(c -> map.put(c.getChannel(), null));
        channelRepository.findByChannelIn(map.keySet()).forEach(c -> map.put(c.getId(), c));
        List<SeqBasedIdxFirstOrder> updateList = model.getOrders().stream()
                .map(c -> {
                    ChannelConfig channel = map.get(c.getChannel());
                    if (channel == null) {
                        throw new KbException(KbCode.ILLEGAL_ARGUMENT, "update idx but item not found: " + c.getChannel())
                                .r("频道不存在").li();
                    }
                    return new SeqBasedIdxFirstOrder(channel.getId(), c.getOrder(), channel.getCreateTime());
                })
                .collect(Collectors.toList());
        channelRepository.multiUpdateIdx(updateList);
        adminLogRecorder.on().act("BULK_UPDATE_IDX", "批量更新手动排序").add();
    }

    public Optional<ChannelConfig> getChannelByKey(String channelKey) {
        return channelRepository.findFirstByChannel(channelKey);
    }

    public List<ChannelConfig> getChannelList(String channelType, Integer page, Integer pageSize) {
        int p = page == null ? 0 : page - 1;
        int ps = pageSize == null ? 20 : pageSize;
        PageRequest pageable = PageRequest.of(p, ps);
        if (channelType == null) {
            return channelRepository.findByOrderBySeqDesc(pageable);
        } else {
            return channelRepository.findByTypeOrderBySeqDesc(channelType, pageable);
        }
    }

    // ------------------------------------------------------

    public ChannelMainPage createMainPage(String channelKey) {
        ChannelConfig channel = channelRepository.findFirstByChannel(channelKey).orElseThrow(
                () -> new KbException(KbCode.RESOURCE_NOT_FOUND).r("频道不存在").li());
        long now = System.currentTimeMillis();
        ChannelMainPage mainPage = new ChannelMainPage();
        mainPage.setChannelKey(channelKey);
        mainPage.setTitle(channel.getName());
        mainPage.setProgrammeListStyle(ChannelProgrammeListStyle.HIDE.name());
        mainPage.setArticleListStyle(ChannelArticleListStyle.HIDE.name());
        mainPage.setUpdateTime(now);
        mainPage.setCreateTime(now);
        ChannelMainPage created = mainPageRepository.insert(mainPage);
        log.info("news channel main page created: " + created);
        adminLogRecorder.on()
                .act("CREATE_MAIN_PAGE", "创建频道首页").ref1(channel.getChannel()).add();
        return created;
    }

    public ChannelMainPage updateMainPage(ChannelMainPage model, String desc) {
        ChannelMainPage legacy = mainPageRepository.findFirstByChannelKey(model.getChannelKey()).orElseThrow(
                () -> new KbException(KbCode.RESOURCE_NOT_FOUND).li());
        ChannelMainPage tobeUpdate = Mapper.map(model, legacy);
        ChannelMainPage updated = mainPageRepository.save(tobeUpdate);
        log.info("news channel config main page update 11: " + model.getChannelKey());
        adminLogRecorder.on()
                .act("UPDATE_MAIN_PAGE_1", desc)
                .ref1(updated.getChannelKey()).add();
        return updated;
    }

    public ChannelMainPage updateMainPageData(ChannelMainPageUpdateModel model) {
        ChannelMainPage legacy = mainPageRepository.findFirstByChannelKey(model.getChannelKey()).orElseThrow(() ->
                new KbException(KbCode.RESOURCE_NOT_FOUND, "main page not exists: " + model.getChannelKey())
                        .r("单位主页不存在"));
        ChannelMainPage tobeUpdate = Mapper.map(model, legacy);
        ChannelMainPage updated = mainPageRepository.save(tobeUpdate);
        log.info("news channel config main page update 22: " + model.getChannelKey());
        adminLogRecorder.on()
                .act("UPDATE_MAIN_PAGE_2", "修改频道首页: " + model.getChannelKey())
                .ref1(updated.getChannelKey()).add();
        return updated;
    }

    public void addMainPageTab(ChannelMainPage mainPage, GroupTopicTab tab, boolean initByKey) {
        if (mainPage.getId() == null) {
            throw new KbException(KbCode.ILLEGAL_ARGUMENT, "topic id missing");
        }
        if (GroupTabType.ARTICLE_GROUP.name().equals(tab.getType())) {
            addMainPageTabAsArticleGroup(mainPage, tab, initByKey);
        } else if (GroupTabType.NOTE_THREAD.name().equals(tab.getType())) {
            addMainPageTabAsNoteThread(mainPage, tab, initByKey);
        } else {
            throw new KbException(KbCode.ILLEGAL_ARGUMENT, "unknown tab type").r("未知的 tab 类型").li();
        }
    }

    public void addMainPageTabAsArticleGroup(ChannelMainPage mainPage, GroupTopicTab tab, boolean initByKey) {
        String groupKey = tab.getKey();
        if (initByKey) {
            if (groupKey != null && groupService.getGroup(null, groupKey).isPresent()) {
                throw new KbException(KbCode.RESOURCE_ALREADY_EXIST, "group already exists").r("分组已存在").li();
            }
            IdsGroup group = new IdsGroup();
            group.setKey(groupKey);
            group.setSiteId(KbProperties.HANGZHOU_SITE_ID);
            group.setChannelKey(mainPage.getChannelKey());
            group.setName("单位首页 tab: " + mainPage.getTitle() + "-" + tab.getName());
            IdsGroup createdGroup = groupService.createGroup(group);
            groupKey = createdGroup.getKey();
            tab.setKey(groupKey);
        } else {
            if (groupKey == null || !groupService.getGroup(null, groupKey).isPresent()) {
                throw new KbException(KbCode.RESOURCE_ALREADY_EXIST, "missing group").r("分组不存在").li();
            }
        }
        if (mainPage.getTabs() == null || mainPage.getTabs().isEmpty()) {
            mainPage.setTabs(Collections.singletonList(tab));
        } else {
            List<GroupTopicTab> tabs = new ArrayList<>(mainPage.getTabs().size() + 1);
            tabs.addAll(mainPage.getTabs());
            tabs.add(tab);
            mainPage.setTabs(tabs);
        }
        ChannelMainPage updated = mainPageRepository.save(mainPage);
        String msg = mainPage.getChannelKey() + " -> " + tab.getName();
        log.info("news channel config main page add article group tab: " + msg);
        adminLogRecorder.on()
                .act("ADD_MAIN_PAGE_ARTICLE_GROUP_TAB", "添加频道首页文章列表 tab: " + msg)
                .ref1(updated.getChannelKey()).add();
    }

    public void addMainPageTabAsNoteThread(ChannelMainPage mainPage, GroupTopicTab tab, boolean initByKey) {
        if (initByKey) {
            NoteThreadModel model = new NoteThreadModel();
            model.setTitle("分组专题 tab: " + mainPage.getTitle() + "-" + tab.getName());
            model.setSiteId(KbProperties.HANGZHOU_SITE_ID);
            model.setHotMax(0);
            model.setTopMax(5);
            String threadId = noteService.createThreadByBody(model).dataOrThrow().getId();
            tab.setKey(threadId);
        } else {
            String threadId = tab.getKey();
            if (threadId == null || !noteService.getThreadById(threadId).data().isPresent()) {
                throw new KbException(KbCode.RESOURCE_ALREADY_EXIST, "missing thread").r("帖子板块不存在").li();
            }
        }
        if (mainPage.getTabs() == null || mainPage.getTabs().isEmpty()) {
            mainPage.setTabs(Collections.singletonList(tab));
        } else {
            List<GroupTopicTab> tabs = new ArrayList<>(mainPage.getTabs().size() + 1);
            tabs.addAll(mainPage.getTabs());
            tabs.add(tab);
            mainPage.setTabs(tabs);
        }
        ChannelMainPage updated = mainPageRepository.save(mainPage);
        String msg = mainPage.getChannelKey() + " -> " + tab.getName();
        log.info("news channel config main page add note thread tab: " + msg);
        adminLogRecorder.on()
                .act("ADD_MAIN_PAGE_NOTE_THREAD_TAB", "添加频道首页帖子列表 tab: " + msg)
                .ref1(updated.getChannelKey()).add();
    }

    public Optional<ChannelMainPage> getMainPageByChannel(String channelKey) {
        return mainPageRepository.findFirstByChannelKey(channelKey);
    }

    public static final String MAIN_PAGE_TAB_KEY_PREFIX = "channel_main_page_tab_";

}
