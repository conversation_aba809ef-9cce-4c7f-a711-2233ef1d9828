package com.kaiba.m.core.service.program;

import com.kaiba.lib.base.constant.KbProperties;
import com.kaiba.lib.base.constant.Values;
import com.kaiba.lib.base.constant.program.ProgramThreadType;
import com.kaiba.lib.base.constant.program.RebroadcastType;
import com.kaiba.lib.base.domain.program.*;
import com.kaiba.lib.base.domain.program.input.ScheduleInstanceInput;
import com.kaiba.lib.base.domain.program.input.ScheduleTemplateInput;
import com.kaiba.lib.base.domain.program.input.ScheduleTemplateItemInput;
import com.kaiba.lib.base.util.StringUtils;
import com.kaiba.m.core.domain.program.*;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * author wangsj
 * date 2020-09-01
 */
public class ScheduleConverter {

    public static ScheduleWeekTemplateModel weekTemplate2Model(ScheduleWeekTemplate weekTemplate) {
        ScheduleWeekTemplateModel model = new ScheduleWeekTemplateModel();
        model.setId(weekTemplate.getId());
        model.setDayOfWeek(weekTemplate.getDayOfWeek());
        model.setSiteId(weekTemplate.getSiteId());
        model.setTemplateId(weekTemplate.getTemplateId());
        model.setItemList(new ArrayList<>());
        return model;
    }

    public static ScheduleTemplate input2Template(ScheduleTemplateInput input) {
        ScheduleTemplate template = new ScheduleTemplate();
        template.setId(input.getId());
        template.setName(input.getName());
        template.setSiteId(input.getSiteId());
        return template;
    }

    public static ScheduleTemplateItem input2TemplateItem(ScheduleTemplateItemInput input, Integer userId) {
        ScheduleTemplateItem item = new ScheduleTemplateItem();
        item.setId(input.getId());
        item.setSiteId(input.getSiteId());
        item.setTemplateId(input.getTemplateId());
        item.setProgramId(input.getProgramId());
        item.setEmceeId(input.getEmceeId());
        item.setStart(input.getStart());
        item.setRebroadcast(input.getRebroadcast());
        if (item.getRebroadcast() != null && item.getRebroadcast()) {
            item.setRebroadcastType(RebroadcastType.PREVIOUS_ONE.getType());
        }
        item.setEnd(input.getEnd());
        item.setUpdateUser(userId);
        item.setCreateInstanceStrategy(input.getCreateInstanceStrategy());
        return item;
    }

    public static ScheduleTemplateModel template2Model(
            ScheduleTemplate template, List<ScheduleTemplateItemModel> itemModelList
    ) {
        ScheduleTemplateModel model = new ScheduleTemplateModel();
        model.setId(template.getId());
        model.setName(template.getName());
        model.setSiteId(template.getSiteId());
        model.setItemList(itemModelList);
        return model;
    }

    public static ScheduleTemplateItemModel templateItem2Model(
            ScheduleTemplateItem templateItem, Program program, List<Emcee> emcee) {
        ScheduleTemplateItemModel model = new ScheduleTemplateItemModel();
        model.setId(templateItem.getId());
        model.setTemplateId(templateItem.getTemplateId());
        model.setProgramId(templateItem.getProgramId());
        model.setProgramName(program.getName());
        model.setEmceeList(emcee.stream().map(EmceeConverter::simpleEmcee2Model).collect(Collectors.toList()));
        model.setSiteId(templateItem.getSiteId());
        model.setStart(templateItem.getStart());
        model.setEnd(templateItem.getEnd());
        model.setRebroadcast(templateItem.getRebroadcast());
        return model;
    }

    public static ScheduleInstanceModel instanceModel(
            ScheduleInstance instance, Program program, List<Emcee> emcees, Topic topic, ProgramThread thread) {
        ScheduleInstanceModel model = new ScheduleInstanceModel();
        model.setId(instance.getId());
        model.setProgramId(instance.getProgramId());
        model.setProgramName(program.getName());
        model.setProgramCover(emcees.get(0).getProgramCover());
        model.setSiteId(instance.getSiteId());
        model.setAlbumId(program.getAlbumId());
        model.setRebroadcast(instance.getRebroadcast());
        model.setThreadId(instance.getThreadId());
        model.setBoardThreadId(instance.getBoardThreadId());
        model.setRewardThreadId(instance.getRewardThreadId());
        model.setBroadcastThreadId(instance.getBroadcastThreadId());
        model.setStartTime(instance.getStart());
        model.setEndTime(instance.getEnd());
        model.setUpdateTime(instance.getUpdateTime());
        model.setEmcee(emcees.stream().map(EmceeConverter::baseEmcee2Model).collect(Collectors.toList()));

        if (ProgramThreadType.ALL.getValue() == program.getThreadLevel() && thread != null) {
            model.setContentThreadId(thread.getThreadId());
        } else if (ProgramThreadType.PROGRAM.getValue() == program.getThreadLevel()) {
            model.setContentThreadId(program.getThreadId());
        } else {
            model.setContentThreadId(model.getThreadId());
        }

        if (topic != null) {
            model.setTopic(TopicConverter.topicModel(topic));
        }
        long current = System.currentTimeMillis() / 1000;
        if (model.getStartTime() > current) {
            model.setIsLive(Values.FALSE);
            model.setLabel("待播");
        } else if (current > model.getEndTime()) {
            model.setIsLive(Values.FALSE);
            model.setLabel("重播");
        } else {
            model.setIsLive(Values.TRUE);
            model.setLabel("直播");
        }
        return model;
    }

    public static ScheduleInstance inputModel2Instance(ScheduleInstanceInput inputModel) {
        ScheduleInstance instance = new ScheduleInstance();
        instance.setId(inputModel.getId());
        instance.setProgramId(inputModel.getProgramId());
        instance.setSiteId(inputModel.getSiteId());
        instance.setStart(inputModel.getStart());
        instance.setEnd(inputModel.getEnd());
        instance.setEmceeId(inputModel.getEmceeId());
        instance.setIsEmpty(false);
        instance.setPraise(0);
        instance.setView(0);
        instance.setRebroadcast(inputModel.getRebroadcast());
        if (inputModel.getRebroadcast() != null && instance.getRebroadcast()) {
            instance.setRebroadcastType(RebroadcastType.PREVIOUS_ONE.getType());
        }
        instance.setUpdateUser(inputModel.getUpdateUser());
        return instance;
    }

    public static ScheduleInstance createInstanceFromTemplateItem(ScheduleTemplateItem item, Integer dayStamp) {
        ScheduleInstance instance = new ScheduleInstance();
        instance.setProgramId(item.getProgramId());
        instance.setSiteId(item.getSiteId());
        instance.setStart(dayStamp + item.getStart());
        instance.setEnd(dayStamp + item.getEnd());
        instance.setIsDefault(true);
        instance.setIsEmpty(false);
        instance.setRebroadcast(item.getRebroadcast());
        instance.setRebroadcastType(item.getRebroadcastType());
        instance.setPraise(0);
        instance.setView(0);
        instance.setCreateTime(System.currentTimeMillis() / 1000);
        instance.setCreateUser(KbProperties.SYSTEM_USER_ID);
        instance.setUpdateTime(instance.getCreateTime());
        instance.setUpdateUser(instance.getCreateUser());
        instance.setEmceeId(new ArrayList<>(3));
        return instance;
    }

    public static ScheduleInstance createEmptyInstance(Program program, Emcee emcee, Integer start, Integer end) {
        ScheduleInstance instance = new ScheduleInstance();
        instance.setProgramId(program.getId());
        instance.setSiteId(program.getSiteId());
        instance.setStart(start);
        instance.setEnd(end);
        instance.setIsDefault(true);
        instance.setIsEmpty(true);
        instance.setRebroadcast(false);
        instance.setRebroadcastType(null);
        instance.setPraise(0);
        instance.setView(0);
        instance.setCreateTime(System.currentTimeMillis() / 1000);
        instance.setCreateUser(KbProperties.SYSTEM_USER_ID);
        instance.setUpdateTime(instance.getCreateTime());
        instance.setUpdateUser(instance.getCreateUser());
        instance.setEmceeId(Collections.singletonList(emcee.getId()));
        return instance;
    }

    public static ScheduleInstanceModel simpleInstanceModel(
            ScheduleInstance instance, Program program, List<Emcee> emcees) {
        ScheduleInstanceModel model = new ScheduleInstanceModel();
        model.setId(instance.getId());
        model.setProgramId(program.getId());
        model.setProgramName(program.getName());
        if (StringUtils.isEmpty(program.getCoverImg())) {
            model.setProgramCover(emcees.get(0).getProgramCover());
        } else {
            model.setProgramCover(program.getCoverImg());
        }
        model.setThreadId(instance.getThreadId());
        model.setStartTime(instance.getStart());
        model.setEndTime(instance.getEnd());
        model.setSiteId(instance.getSiteId());
        model.setRebroadcast(instance.getRebroadcast());
        model.setIsEmpty(instance.getIsEmpty());
        model.setEmcee(emcees.stream().map(EmceeConverter::baseEmcee2Model).collect(Collectors.toList()));
        model.setPraise(instance.getPraise());
        model.setView(instance.getView());
        long current = System.currentTimeMillis() / 1000;
        if (model.getStartTime() > current) {
            model.setIsLive(Values.FALSE);
            model.setLabel("待播");
        } else if (current > model.getEndTime()) {
            model.setIsLive(Values.FALSE);
            model.setLabel("重播");
        } else {
            model.setIsLive(Values.TRUE);
            model.setLabel("直播");
        }
        return model;
    }

    public static List<ScheduleInstanceModel> instanceModelList(
            List<ScheduleInstance> instanceList, Map<String, Program> programMap, Map<String, Emcee> emceeMap
    ) {
        List<ScheduleInstanceModel> list = new ArrayList<>();
        for (ScheduleInstance instance : instanceList) {
            List<Emcee> emceeList = new ArrayList<>();
            for (String emceeId : instance.getEmceeId()) {
                Emcee emcee = emceeMap.get(emceeId);
                if (emcee != null) {
                    emceeList.add(emcee);
                }
            }
            if (!emceeList.isEmpty()) {
                ScheduleInstanceModel model = simpleInstanceModel(instance, programMap.get(instance.getProgramId()), emceeList);
                list.add(model);
            }
        }
        return list;
    }
}
