package com.kaiba.m.core.service.issuetask.expert;


import com.kaiba.lib.base.assembler.Mapper;
import com.kaiba.lib.base.domain.issue.IssueModel;
import com.kaiba.m.core.configuration.IssueScoreProperties;
import com.kaiba.m.core.domain.issuetask.IssueExpertSummary;
import com.kaiba.m.core.domain.issuetask.RongOnlineStatus;
import com.kaiba.m.core.domain.issuetask.WorkingExpert;
import com.kaiba.m.core.repository.issuetask.redis.WorkingExpertRepository;
import com.kaiba.m.core.service.issuetask.IssueConfigs;
import com.kaiba.m.core.service.issuetask.IssueExpertSummarizeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * author: lyux
 * date: 18-9-19
 *
 * redis backed expert manager, so that we can share state among all application instances.
 */
@Slf4j
@Service
@RefreshScope
public class WorkingExpertService {

    private static final String REDIS_KEY_EXPERT_VIP_MAP = "java_issue_expert_vip_map";

    private final WorkingExpertRepository expertRepository;
    private final ExpertDroppedService droppedManager;
    private final IExpertEvaluator expertEvaluator;
    private final IssueExpertSummarizeService summarizeService;
    private final StringRedisTemplate redisTemplate;

    @Autowired
    public WorkingExpertService(
            IssueScoreProperties issueScoreProperties,
            WorkingExpertRepository expertRepository,
            ExpertDroppedService droppedManager,
            IssueExpertSummarizeService summarizeService,
            StringRedisTemplate redisTemplate
    ) {
        this.summarizeService = summarizeService;
        this.expertRepository = expertRepository;
        this.droppedManager = droppedManager;
        this.redisTemplate = redisTemplate;
        this.expertEvaluator = new SimpleExpertEvaluator(droppedManager, issueScoreProperties);
    }

    public void startWorking(Integer expertId) {
        log.info("expert start working: " + expertId);
        WorkingExpert expert = summarizeService.getSummary(expertId)
                .map(this::summary2expert)
                .orElse(summizeExpert(expertId));
        expert.setVip(getExpertVip(expertId));
        expertRepository.save(expert);
    }

    public void stopWorking(Integer expertId) {
        log.info("expert stop working: " + expertId);
        expertRepository.deleteById(expertId);
    }

    public Optional<WorkingExpert> dispatchJob(IssueModel issue) {
        return findExpertForIssue(issue);
    }

    public void acceptJob(Integer expertId, String issueId) {
        expertRepository.findById(expertId).ifPresent(
                expert -> expertRepository.save(expert.acceptIssue()));
    }

    public void refuseJob(Integer expertId, String issueId) {
        expertRepository.findById(expertId).ifPresent(
                expert -> {
                    expertRepository.save(expert.refusedIssue());
                    droppedManager.taskRefused(issueId, expertId);
                });
    }

    public void timeoutJob(Integer expertId, String issueId) {
        expertRepository.findById(expertId).ifPresent(
                expert -> {
                    expertRepository.save(expert.timeoutIssue());
                    droppedManager.taskTimeout(issueId, expertId);
                });
    }

    public Optional<WorkingExpert> findExpertById(Integer expertId) {
        return expertRepository.findById(expertId);
    }

    public List<WorkingExpert> getAllExperts() {
        List<WorkingExpert> list = new ArrayList<>();
        expertRepository.findAll().forEach(list::add);
        return list;
    }

    public long getExpertCount() {
        return expertRepository.count();
    }

    public void issuePeeringFinished(String issueId) {
        droppedManager.deleteByIssue(issueId);
    }

    public void setExpertVip(Integer expertId, Integer vip) {
        int vipValue = null == vip ? 0 : vip;
        if (vipValue > 0) {
            redisTemplate.opsForHash().put(REDIS_KEY_EXPERT_VIP_MAP, Integer.toString(expertId), Integer.toString(vipValue));
        } else {
            redisTemplate.opsForHash().delete(REDIS_KEY_EXPERT_VIP_MAP, Integer.toString(expertId));
        }
        expertRepository.findById(expertId).ifPresent(w -> w.setVip(vipValue));
    }

    public int getExpertVip(Integer expertId) {
        Object vipValue = redisTemplate.opsForHash().get(REDIS_KEY_EXPERT_VIP_MAP, Integer.toString(expertId));
        if (null != vipValue) {
            try {
                return Integer.parseInt((String) vipValue);
            } catch (Exception e) {
                log.error("get vip value for expertId " + expertId + " fail, value=" + vipValue, e);
            }
        }
        return 0;
    }

    public Map<Integer, Integer> getExpertVipList() {
        Map<Object, Object> map = redisTemplate.opsForHash().entries(REDIS_KEY_EXPERT_VIP_MAP);
        if (map.isEmpty()) {
            return Collections.emptyMap();
        } else {
            Map<Integer, Integer> result = new HashMap<>(map.size());
            map.forEach((k, v) -> {
                try {
                    Integer key = Integer.valueOf((String) k);
                    Integer value = Integer.valueOf((String) v);
                    result.put(key, value);
                } catch (Exception e) {
                    log.error("getExpertVipList fail: " + k + ", " + v, e);
                }
            });
            return result;
        }
    }

    @Async
    public void checkTrimExperts() {
        log.info("check trim experts");
        long rongOfflineTimeoutThreshold = IssueConfigs.taskExpertTrimRongOfflineThreshold * 1000;
        long rongOverallTimeoutThreshold = IssueConfigs.taskExpertTrimRongOverallThreshold * 1000;
        int taskTimeoutCountThreshold = IssueConfigs.taskExpertTrimTaskTimeoutThreshold;
        expertRepository.findAll().forEach(expert -> {
            log.info("check trim expert: " + expert);
            if (expert == null) {
                log.warn("check trim expert with null expert, " +
                        "have you changed package or name of " + WorkingExpert.class.getName() + " class?");
            } else if (expert.getLastTimeoutCount() != null &&
                    expert.getLastTimeoutCount() > taskTimeoutCountThreshold) {
                log.info("expert task timeout count exceed " + taskTimeoutCountThreshold +
                        ", trim: " + expert.getId());
                trimWorkingExpert(expert.getId(),
                        "您已有 " + expert.getLastTimeoutCount() + " 单状态超时, 系统已停止您的接单状态");
            } else {
                long lastRongUpdateTime = expert.getLastRongUpdateTime() == null ? 0L : expert.getLastRongUpdateTime();
                if (System.currentTimeMillis() - lastRongUpdateTime > rongOverallTimeoutThreshold) {
                    log.info("expert rong last update exceed " + rongOverallTimeoutThreshold + " seconds, " +
                            "trim: " + expert.getId());
                    trimWorkingExpert(expert.getId(), "连接状态超时, 系统已停止您的接单状态");
                } else if (expert.getLastRongStatus() != null &&
                        expert.getLastRongStatus() != RongOnlineStatus.RONG_STATE_ONLINE &&
                        System.currentTimeMillis() - lastRongUpdateTime > rongOfflineTimeoutThreshold) {
                    log.info("expert rong offline exceed " + rongOfflineTimeoutThreshold + " seconds, " +
                            "trim: " + expert.getId());
                    trimWorkingExpert(expert.getId(), "在线状态超时, 系统已停止您的接单状态");
                }
            }
        });
    }

    public void resetAllExperts() {
        List<Integer> expertIdList = new LinkedList<>();
        expertRepository.findAll().forEach(expert -> expertIdList.add(expert.getId()));
        if (!expertIdList.isEmpty()) {
            expertIdList.forEach(this::startWorking);
        }
    }

    private Optional<WorkingExpert> findExpertForIssue(IssueModel issue) {
        long rongOfflineTimeoutThreshold = IssueConfigs.taskExpertTrimRongOfflineThreshold * 1000;
        int taskTimeoutCountThreshold = IssueConfigs.taskExpertTrimTaskTimeoutThreshold;
        boolean allow_reassign_timeout = IssueConfigs.taskAllowReassignForDispatchTimeout;
        boolean allow_reassign_refused = IssueConfigs.taskAllowReassignForDispatchRefused;
        ExpertCandidate candidate = new ExpertCandidate();
        expertRepository.findAll().forEach(expert -> {
            if (!expert.getIsWorking()) {
                log.debug("for issue: " + issue.getId() + ", evaluate expert: " + expert.getId() + ", not working");
                return;
            }
            if (expert.getLastRongStatus() != null &&
                    expert.getLastRongStatus() != RongOnlineStatus.RONG_STATE_ONLINE &&
                    System.currentTimeMillis() - expert.getLastRongUpdateTime() > rongOfflineTimeoutThreshold) {
                log.debug("for issue: " + issue.getId() + ", evaluate expert: " + expert.getId() + ", rong offline timeout");
                return;
            }
            if (expert.getLastTimeoutCount() != null && expert.getLastTimeoutCount() > taskTimeoutCountThreshold) {
                log.debug("for issue: " + issue.getId() + ", evaluate expert: " + expert.getId() +
                        ", task timeout more than " + taskTimeoutCountThreshold);
                return;
            }
            if (!allow_reassign_timeout && droppedManager.isAlreadyTimeout(issue.getId(), expert.getId())) {
                log.debug("for issue: " + issue.getId() + ", evaluate expert: " + expert.getId() + ", already timeout");
                return;
            }
            if (!allow_reassign_refused && droppedManager.isAlreadyRefused(issue.getId(), expert.getId())) {
                log.debug("for issue: " + issue.getId() + ", evaluate expert: " + expert.getId() + ", already refused");
                return;
            }

            int evaluation = expertEvaluator.evaluate(issue, expert);
            if (candidate.evaluation < evaluation) {
                candidate.expert = expert;
                candidate.evaluation = evaluation;
            }
        });
        return Optional.ofNullable(candidate.expert);
    }

    private void trimWorkingExpert(Integer expertId, String reason) {
        expertRepository.deleteById(expertId);
    }

    public void receiveRongOnlineStatus(Integer expertId, Integer status, String os, Long time) {
        log.debug("receive rong online event: " + expertId + ", " + status + ", " + os + ", " + time);
        expertRepository.findById(expertId).ifPresent(expert -> {
            if (RongOnlineStatus.RONG_STATE_LOGOUT == status) {
                stopWorking(expertId);
            } else {
                long current = System.currentTimeMillis();
                expert.setLastRongStatus(status);
                expert.setLastRongUpdateTime(current);
                expert.setUpdateTime(current);
                expertRepository.save(expert);
            }
        });
    }

    private WorkingExpert summizeExpert(Integer expertId) {
        IssueExpertSummary summary = summarizeService.summarizeExpert(expertId);
        return summary2expert(summary);
    }

    private WorkingExpert summary2expert(IssueExpertSummary summary) {
        WorkingExpert w = Mapper.map(summary, WorkingExpert.class);
        long current = System.currentTimeMillis();
        w.setIsWorking(true);
        w.setUpdateTime(current);
        w.setWorkingTime(current);
        w.setLastRongUpdateTime(current);
        return w;
    }

    private static class ExpertCandidate {
        WorkingExpert expert;
        int evaluation = Integer.MIN_VALUE;
    }

}
