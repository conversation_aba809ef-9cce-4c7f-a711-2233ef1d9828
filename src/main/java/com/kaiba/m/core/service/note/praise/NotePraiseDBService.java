package com.kaiba.m.core.service.note.praise;

import com.kaiba.lib.base.domain.user.UserModel;
import com.kaiba.m.core.domain.note.Note;
import com.kaiba.m.core.domain.note.NotePraise;
import com.kaiba.m.core.repository.note.NotePraiseRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

/**
 * author: lyux
 * date: 19-5-28
 */
@Slf4j
@Service
public class NotePraiseDBService {

    private final NotePraiseRepository notePraiseRepository;

    public NotePraiseDBService(NotePraiseRepository notePraiseRepository) {
        this.notePraiseRepository = notePraiseRepository;
    }

    NotePraise praise(Note note, UserModel user) {
        return notePraiseRepository.praise(note, user);
    }

    boolean praiseUndo(String noteId, Integer userId) {
        return notePraiseRepository.unpraise(noteId, userId);
    }

    boolean isNotePraisedByUser(String noteId, Integer userId) {
        return notePraiseRepository.existsByNoteIdAndUserId(noteId, userId);
    }

    Optional<NotePraise> findByNoteIdAndUserId(String noteId, Integer userId) {
        return notePraiseRepository.findFirstByNoteIdAndUserId(noteId, userId);
    }

    Page<NotePraise> findByUserId(Integer userId, Integer page, Integer pageSize) {
        PageRequest pageable = PageRequest.of(page == null ? 0 : page - 1, pageSize == null ? 15 : pageSize);
        return notePraiseRepository.findByUserIdOrderByCreateTimeDesc(userId, pageable);
    }

    Page<NotePraise> findByUserIdAndSiteId(Integer userId, Integer siteId, Integer page, Integer pageSize) {
        PageRequest pageable = PageRequest.of(page == null ? 0 : page - 1, pageSize == null ? 15 : pageSize);
        return notePraiseRepository.findByUserIdAndSiteIdOrderByCreateTimeDesc(userId, siteId, pageable);
    }

    Page<NotePraise> getPraiseListByNoteIdOrderByCreateTime(String noteId, String sortDirection, Integer page, Integer pageSize) {
        Sort.Direction direction = Sort.Direction.fromOptionalString(sortDirection).orElse(Sort.Direction.DESC);
        Sort sortBy = Sort.by(direction, "createTimeMS");
        PageRequest pageable = PageRequest.of(page == null ? 0 : page - 1, pageSize == null ? 15 : pageSize, sortBy);
        return notePraiseRepository.findByNoteId(noteId, pageable);
    }

    Page<NotePraise> getPraiseListByNoteId(String noteId, Integer page, Integer pageSize) {
        PageRequest pageable = PageRequest.of(page == null ? 0 : page - 1, pageSize == null ? 15 : pageSize);
        return notePraiseRepository.findByNoteIdOrderByScoreDesc(noteId, pageable);
    }

    List<NotePraise> getPraiseListByConcatId(List<String> concatIds) {
        return notePraiseRepository.findByConcatIdInOrderByScoreDesc(concatIds);
    }

    long getCountByNoteId(String noteId) {
        return notePraiseRepository.countByNoteId(noteId);
    }

}
