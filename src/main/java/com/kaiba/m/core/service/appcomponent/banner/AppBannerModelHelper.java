package com.kaiba.m.core.service.appcomponent.banner;

import com.kaiba.lib.base.assembler.Mapper;
import com.kaiba.lib.base.constant.common.KbImageFormat;
import com.kaiba.lib.base.domain.appwidget.banner.BannerIdxUpdateModel;
import com.kaiba.lib.base.domain.appwidget.banner.BannerImageModel;
import com.kaiba.lib.base.domain.appwidget.banner.BannerInstanceModel;
import com.kaiba.lib.base.lang.verifier.F;
import com.kaiba.lib.base.lang.verifier.Verifier;
import com.kaiba.lib.base.lang.verifier.VerifierBuilder;
import com.kaiba.lib.base.util.appaction.AppActionUtils;
import com.kaiba.m.core.domain.appwidget.banner.BannerImage;
import com.kaiba.m.core.domain.appwidget.banner.BannerInstance;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * author: lyux
 * date: 2023-07-11
 */
@Slf4j
@Component
public class AppBannerModelHelper {

    public static BannerInstanceModel instance2model(BannerInstance instance) {
        return Mapper.map(instance, BannerInstanceModel.class);
    }

    public static BannerInstance model2instance(BannerInstanceModel model) {
        return Mapper.map(model, BannerInstance.class);
    }

    public static BannerImageModel banner2model(BannerImage banner) {
        return Mapper.map(banner, BannerImageModel.class);
    }

    public static BannerImageModel banner2modelForClient(BannerImage banner) {
        BannerImageModel model = new BannerImageModel();
        model.setId(banner.getId());
        model.setIdx(banner.getIdx());
        model.setUrl(banner.getUrl());
        model.setFormat(banner.getFormat());
        model.setTitle(banner.getTitle());
        model.setSubtitle(banner.getSubtitle());
        model.setIsPlayable(banner.getIsPlayable());
        model.setIsLive(banner.getIsLive());
        model.setAction(banner.getAction());
        model.setActionParams(banner.getActionParams());
        model.setAttr(banner.getAttr());
        model.setAutoplay(banner.getAutoplay());
        return model;
    }

    public static BannerImage model2banner(BannerImageModel model) {
        return Mapper.map(model, BannerImage.class);
    }

    static final Verifier<BannerInstance> INSTANCE_VERIFIER = new VerifierBuilder<BannerInstance>().defaultOrElseThrow()
            .and(F.str(BannerInstance::getKey).notEmpty())
            .and(F.intF(BannerInstance::getSiteId).notNull())
            .and(F.intF(BannerInstance::getWidth).gt(0))
            .and(F.intF(BannerInstance::getHeight).gt(0))
            .and(F.str(BannerInstance::getName).notEmpty())
            .create();

    static final Verifier<BannerImage> BANNER_VERIFIER = new VerifierBuilder<BannerImage>().defaultOrElseThrow()
            .and(F.str(BannerImage::getInstanceId).isObjectId())
            .and(F.str(BannerImage::getUrl).isUrl())
            .and(F.str(BannerImage::getFormat).enums(KbImageFormat.values()))
            .and(AppActionUtils::verifyAction)
            .create();

    static final Verifier<BannerIdxUpdateModel.BannerIdx> BANNER_IDX_EACH_VERIFIER = new VerifierBuilder<BannerIdxUpdateModel.BannerIdx>().defaultOrElseThrow()
            .and(F.str(BannerIdxUpdateModel.BannerIdx::getBannerId).notNull().r("bannerId不可为空"))
            .and(F.intF(BannerIdxUpdateModel.BannerIdx::getBannerIdx).notNull().r("bannerIdx不可为空"))
            .create();

    static final Verifier<BannerIdxUpdateModel> BANNER_IDX_UPDATE_VERIFIER = new VerifierBuilder<BannerIdxUpdateModel>().defaultOrElseThrow()
            .and(F.str(BannerIdxUpdateModel::getInstanceId).notNull())
            .and(F.obj(BannerIdxUpdateModel::getIdxList).notEmpty())
            .and(F.list(BannerIdxUpdateModel::getIdxList).each(fieldGetter -> BANNER_IDX_EACH_VERIFIER))
            .create();
}
