package com.kaiba.m.core.service.note.praise;

import com.kaiba.lib.base.domain.user.UserModel;
import com.kaiba.m.core.domain.note.Note;
import com.kaiba.m.core.domain.note.NotePraise;
import com.kaiba.m.core.service.note.NoteCacheConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * author: lyux
 * date: 19-5-28
 */
@Slf4j
@Service
public class NotePraiseService {

    private final NotePraiseCacheService cacheService;
    private final NotePraiseDBService dbService;

    public NotePraiseService(
            NotePraiseCacheService cacheService,
            NotePraiseDBService dbService
    ) {
        this.cacheService = cacheService;
        this.dbService = dbService;
    }

    public NotePraise praise(Note note, UserModel user) {
        NotePraise notePraise = dbService.praise(note, user);
        if (notePraise.getCount() == null || notePraise.getCount() <= 1) {
            cacheService.addPraise(note, notePraise);
        }
        return notePraise;
    }

    public boolean praiseUndo(String noteId, Integer userId) {
        Optional<NotePraise> op = dbService.findByNoteIdAndUserId(noteId, userId);
        if (op.isPresent()) {
            boolean success = dbService.praiseUndo(noteId, userId);
            if (success) {
                cacheService.removePraise(op.get());
            }
            return success;
        } else {
            return false;
        }
    }

    public boolean isNotePraisedByUser(Note note, Integer userId) {
        return dbService.isNotePraisedByUser(note.getId(), userId);
    }

    public boolean isNotePraisedByUserAllowCache(Note note, Integer userId) {
        if (cacheService.isCacheReliable(note)) {
            return cacheService.isPraisedByUser(note.getId(), userId);
        } else {
            return dbService.isNotePraisedByUser(note.getId(), userId);
        }
    }

    public Map<String, Boolean> isNoteListPraisedByUser(List<Note> noteList, Integer userId) {
        // TODO: 应使用缓存获取用户对某个帖子是否点赞
        if (noteList == null || noteList.size() == 0 || null == userId) {
            return Collections.emptyMap();
        }
        List<String> concatIds = noteList.stream()
                .map(note -> NotePraise.generateConcatId(note.getId(), userId))
                .collect(Collectors.toList());
        List<NotePraise> praiseList = dbService.getPraiseListByConcatId(concatIds).stream()
                .filter(Objects::nonNull).collect(Collectors.toList());
        if (praiseList.size() == 0) {
            return Collections.emptyMap();
        }
        Map<String, Boolean> result = new HashMap<>(praiseList.size());
        for (NotePraise notePraise : praiseList) {
            result.put(notePraise.getNoteId(), true);
        }
        return result;
    }

    public Map<String, Boolean> isNoteListPraisedByUserAllowCache(List<Note> noteList, Integer userId) {
        if (noteList == null || noteList.size() == 0 || null == userId) {
            return Collections.emptyMap();
        }
        int size = noteList.size();
        Map<String, Boolean> result = new HashMap<>(size);
        List<String> cacheReliableNoteList = new LinkedList<>();
        List<Note> fromDbNoteList = new LinkedList<>();
        for (Note note : noteList) {
            if (note.getPraiseCount() == null || note.getPraiseCount() == 0) {
                continue;
            }
            if (cacheService.isCacheReliable(note)) {
                cacheReliableNoteList.add(note.getId());
            } else {
                fromDbNoteList.add(note);
            }
        }
        Map<String, Boolean> cachedResult = cacheService.isPraisedByUser(cacheReliableNoteList, userId);
        Map<String, Boolean> dbResult = isNoteListPraisedByUser(fromDbNoteList, userId);
        result.putAll(cachedResult);
        result.putAll(dbResult);
        return result;
    }

    public Map<String, List<UserModel>> getPraiseUserListMapByNoteListAllowCache(
            List<Note> noteList, Integer page, Integer pageSize) {
        if (noteList == null || noteList.size() == 0) {
            return Collections.emptyMap();
        }
        int size = noteList.size();
        Map<String, List<UserModel>> result = new HashMap<>(size);

        List<String> cacheReliableNoteList = null;
        for (Note note : noteList) {
            if (note.getPraiseCount() != null && note.getPraiseCount() > 0 && cacheService.isCacheReliable(note)) {
                if (cacheReliableNoteList == null) {
                    cacheReliableNoteList = new LinkedList<>();
                }
                cacheReliableNoteList.add(note.getId());
            }
        }
        if (cacheReliableNoteList == null || cacheReliableNoteList.size() == 0) {
            // 列表中的帖子的点赞缓存都不可用, 直接从数据库读取
            for (Note note : noteList) {
                String noteId = note.getId();
                List<UserModel> userList = getPraiseUserListByNoteId(noteId, page, pageSize).getContent();
                if (userList.size() != 0) {
                    result.put(noteId, userList);
                }
            }
        } else {
            Map<String, List<UserModel>> cachedResult = cacheService
                    .getPraiseUserListMapByNoteIdIn(cacheReliableNoteList, page, pageSize);
            List<String> fromDbNoteList = null;
            for (Note note : noteList) {
                if (note.getPraiseCount() == null || note.getPraiseCount() == 0) {
                    continue;
                }
                List<UserModel> listFromCache = cachedResult.get(note.getId());
                if (listFromCache == null || listFromCache.size() == 0) {
                    // 缓存不存在或缓存无效, 从数据库读取
                    if (fromDbNoteList == null) {
                        fromDbNoteList = new LinkedList<>();
                    }
                    fromDbNoteList.add(note.getId());
                } else {
                    // 缓存有效
                    result.put(note.getId(), listFromCache);
                }
            }

            if (fromDbNoteList != null) {
                for (String noteId : fromDbNoteList) {
                    if (NoteCacheConfig.CACHE_DEBUG) {
                        log.info("[cache_debug][db] get praise user list, cache missing. note: " + noteId);
                    }
                    List<UserModel> userList = dbService.getPraiseListByNoteId(noteId, page, pageSize)
                            .map(praise -> {
                                UserModel u = new UserModel(praise.getUserId());
                                u.setUserName(praise.getUserName());
                                u.setAvatar(praise.getUserAvatar());
                                u.setRole(praise.getUserRole());
                                return u;
                            }).getContent();
                    result.put(noteId, userList);
                }
            }
        }
        return result;
    }

    public Page<NotePraise> getPraiseListByNoteIdOrderByCreateTime(String noteId, String sortDirection, Integer page, Integer pageSize) {
        return dbService.getPraiseListByNoteIdOrderByCreateTime(noteId, sortDirection, page, pageSize);
    }

    public Page<NotePraise> getPraiseListByNoteId(String noteId, Integer page, Integer pageSize) {
        return dbService.getPraiseListByNoteId(noteId, page, pageSize);
    }

    public Page<UserModel> getPraiseUserListByNoteId(String noteId, Integer page, Integer pageSize) {
        return dbService.getPraiseListByNoteId(noteId, page, pageSize)
                .map(praise -> {
                    UserModel u = new UserModel(praise.getUserId());
                    u.setUserName(praise.getUserName());
                    u.setAvatar(praise.getUserAvatar());
                    u.setRole(praise.getUserRole());
                    return u;
                });
    }

    public Page<NotePraise> getPraiseListByUserId(Integer userId, Integer page, Integer pageSize) {
        return dbService.findByUserId(userId, page, pageSize);
    }

    public Page<NotePraise> getPraiseListByUserIdAndSiteId(Integer userId, Integer siteId, Integer page, Integer pageSize) {
        return dbService.findByUserIdAndSiteId(userId, siteId, page, pageSize);
    }

    public List<NotePraise> getPraiseListByConcatId(List<String> concatIds) {
        return dbService.getPraiseListByConcatId(concatIds);
    }

    public long getCountByNoteId(String noteId) {
        return dbService.getCountByNoteId(noteId);
    }
}
