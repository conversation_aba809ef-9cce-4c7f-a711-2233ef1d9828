package com.kaiba.m.core.service.replydict;

import com.kaiba.lib.base.util.GsonUtils;
import com.kaiba.m.core.domain.replydict.ReplyKey;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.connection.DefaultStringTuple;
import org.springframework.data.redis.connection.StringRedisConnection;
import org.springframework.data.redis.core.RedisCallback;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 回复关键字的缓存
 * Created by shenxl on 2020/10/15
 */
@Slf4j
@Service
class ReplyKeyCacheService {
    private final StringRedisTemplate stringRedisTemplate;
    private final Random random = new Random();

    public ReplyKeyCacheService(StringRedisTemplate stringRedisTemplate) {
        this.stringRedisTemplate = stringRedisTemplate;
    }

    boolean isPopulating(Integer siteId) {
        String cacheKey = siteId2key(siteId);
        Set<String> set = stringRedisTemplate.opsForZSet().reverseRange(cacheKey, 0, 0);
        return set.size() != 0 && !set.contains(ReplyConstant.PLACE_HOLDER_ELEMENT);
    }

    void populate(Integer siteId, List<ReplyKey> replyKeys) {
        if (siteId == null || replyKeys == null || replyKeys.size() == 0) {
            return;
        }
        List<ReplyKey> cachedKeys = replyKeys.size() > ReplyConstant.KEY_MAX_CACHE_COUNT ?
                replyKeys.subList(0, ReplyConstant.KEY_MAX_CACHE_COUNT) : replyKeys;
        log.info("for siteId " + siteId + ", reply keys are populated in cache:" + cachedKeys.stream().map(ReplyKey::getKey).collect(Collectors.toList()));
        cachedKeys.forEach(key -> {
            if (key.getCreateTime() == null) {
                key.setCreateTime(ReplyConstant.PLACE_HOLDER_SCORE);
            }
        });
        final Set<StringRedisConnection.StringTuple> cacheData = cachedKeys.stream()
                .filter(ReplyKeyCacheService::isValidKey)
                .map(key -> string2tuple(stringRedisTemplate, replyKey2json(key), key.getCreateTime()))
                .collect(Collectors.toSet());
        cacheData.add(string2tuple(stringRedisTemplate, ReplyConstant.PLACE_HOLDER_ELEMENT, ReplyConstant.PLACE_HOLDER_SCORE));
        String cacheKey = siteId2key(siteId);
        stringRedisTemplate.executePipelined((RedisCallback<Object>) connection -> {
            StringRedisConnection conn = (StringRedisConnection) connection;
            conn.del(cacheKey);
            conn.zAdd(cacheKey, cacheData);
            conn.expire(cacheKey, ReplyConstant.KEY_LIST_EXPIRE.getSeconds());
            return null;
        });
    }

    void addReplyKey(ReplyKey replyKey) {
        addReplyKeys(replyKey.getSiteId(), Collections.singletonList(replyKey));
    }

    void addReplyKeys(Integer siteId, List<ReplyKey> replyKeys) {
        if (siteId == null || replyKeys == null || replyKeys.isEmpty()) {
            return;
        }
        log.info("for siteId " + siteId + ", reply keys are added in cache:" + replyKeys.stream().map(ReplyKey::getKey).collect(Collectors.toList()));
        String cacheKey = siteId2key(siteId);
        stringRedisTemplate.execute((RedisCallback<Object>) connection -> {
            StringRedisConnection conn = (StringRedisConnection) connection;
            if (conn.exists(cacheKey)) {
                replyKeys.forEach(key -> {
                    String cacheData = replyKey2json(key);
                    conn.zAdd(cacheKey, key.getCreateTime(), cacheData);
                });
                conn.expire(cacheKey, ReplyConstant.KEY_LIST_EXPIRE.getSeconds());
                if (random.nextInt(10) == 1) {
                    int trimThreshold = (int) (ReplyConstant.KEY_MAX_CACHE_COUNT * 1.5f);
                    long count = conn.zCard(cacheKey);
                    if (count > trimThreshold) {
                        conn.zRemRange(cacheKey, 0, count - ReplyConstant.KEY_MAX_CACHE_COUNT - 1);
                    }
                }
            }
            return conn.closePipeline();
        });
    }

    void invalid(Integer siteId) {
        String cacheKey = siteId2key(siteId);
        stringRedisTemplate.delete(cacheKey);
        log.info("reply keys cache invalid for siteId " + siteId);
    }

    CacheResult getKeysBySiteId(Integer siteId, int page, int pageSize) {
        if (page <= 0) page = 1;
        if (pageSize <= 0) pageSize = 15;
        int indexLast = page * pageSize;
        if (indexLast > ReplyConstant.KEY_MAX_CACHE_COUNT) {
            return EMPTY_FAILED_RESULT;
        }
        String cacheKey = siteId2key(siteId);
        Set<String> dataSet = stringRedisTemplate.opsForZSet().range(cacheKey, 0, indexLast);
        return data2result(dataSet, page, pageSize);
    }

    private static CacheResult data2result(Set<String> dataSet, int page, int pageSize) {
        if (dataSet == null) {
            // 数据集为空, 视为缓存无效
            return EMPTY_FAILED_RESULT;
        }
        if (!dataSet.remove(ReplyConstant.PLACE_HOLDER_ELEMENT)) {
            // 不包含占位数据, 视为缓存无效
            return EMPTY_FAILED_RESULT;
        }
        int size = dataSet.size();
        int subListBegin = (page - 1) * pageSize;
        if (size == 0 || size <= subListBegin) {
            return EMPTY_SUCCESS_RESULT;
        }
        List<String> idList = new ArrayList<>(dataSet);
        List<ReplyKey> replyKeys = idList.subList(subListBegin, size).stream()
                .map(str -> {
                    try {
                        return GsonUtils.toModelIgnoreError(str, ReplyKey.class);
                    } catch (Exception e) {
                        log.error("parse key from cache fail for " + str, e);
                        return null;
                    }
                })
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        return new CacheResult(true, replyKeys);
    }

    private static String siteId2key(Integer siteId) {
        return ReplyConstant.REPLY_KEY_BY_ID + siteId;
    }

    private static boolean isValidKey(ReplyKey replyKey) {
        return replyKey != null && replyKey.getId() != null && replyKey.getGroupId() != null;
    }

    private static StringRedisConnection.StringTuple string2tuple(
            StringRedisTemplate stringRedisTemplate, String data, double score) {
        return new DefaultStringTuple(
                stringRedisTemplate.getStringSerializer().serialize(data), data, score);
    }

    private static String replyKey2json(ReplyKey replyKey) {
        // 为了避免向 redis zset 插入重复数据
        return "{" +
                "\"key\":" + "\"" + replyKey.getKey() + "\"" + "," +
                "\"groupId\":" + "\"" + replyKey.getGroupId() + "\"" +
                "}";
    }

    private static final CacheResult EMPTY_SUCCESS_RESULT = new CacheResult(true, Collections.emptyList());
    private static final CacheResult EMPTY_FAILED_RESULT = new CacheResult(false, Collections.emptyList());

    @Getter
    static class CacheResult {
        private final boolean hasCache;
        private final List<ReplyKey> replyKeys;

        CacheResult(boolean hasCache, List<ReplyKey> replyKeys) {
            this.hasCache = hasCache;
            this.replyKeys = replyKeys;
        }
    }

}