package com.kaiba.m.core.service.hezuo;

import com.kaiba.lib.base.response.KbCode;
import com.kaiba.lib.base.response.KbException;
import com.kaiba.m.core.domain.hezuo.HeZuo;
import com.kaiba.m.core.repository.hezuo.HeZuoRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

@Slf4j
@Service
public class HeZuoService {

    private final HeZuoRepository heZuoRepository;

    public HeZuoService(HeZuoRepository heZuoRepository) {
        this.heZuoRepository = heZuoRepository;
    }

    public HeZuo createHeZuo(HeZuo heZuo) {
        verifyOnCreate(heZuo);
        heZuo.setCreateTime(System.currentTimeMillis()/1000);
        heZuo = heZuoRepository.insert(heZuo);
        log.info("heZuo create: " + heZuo);
        return heZuo;
    }

    public Optional<HeZuo> getHeZuoById(String id) {
        return heZuoRepository.findById(id);
    }

    public Optional<HeZuo> getHeZuoByUserId(Integer userId) {
        return heZuoRepository.findFirstByUserId(userId);
    }

    public Page<HeZuo> getHeZuoList(Integer siteId, String name, String type, String intention, Integer sign, Integer page, Integer pageSize) {
        int p = page == null ? 0 : page - 1;
        int ps = pageSize == null ? 10 : pageSize;
        Pageable pageable = PageRequest.of(p, ps);
        return heZuoRepository.getHeZuoList(siteId, name, type, intention, sign, pageable);
    }

    private void verifyOnCreate(HeZuo heZuo) {
        if (heZuo.getSiteId() == null) {
            throw new KbException(KbCode.ILLEGAL_ARGUMENT, "site id should note be null: " + heZuo)
                    .setReadableMessage("电台不能为空").li();
        }

        if (heZuo.getType() == null) {
            throw new KbException(KbCode.ILLEGAL_ARGUMENT, "type should note be null: " + heZuo)
                    .setReadableMessage("类型不能为空").li();
        } else {
            if ("2".equals(heZuo.getType()) && heZuo.getIntention() == null) {//商家
                throw new KbException(KbCode.ILLEGAL_ARGUMENT, "intention should note be null: " + heZuo)
                        .setReadableMessage("合作单位为商家时,合作意向不能为空").li();
            }
        }

        if (heZuo.getMobile() == null) {
            throw new KbException(KbCode.ILLEGAL_ARGUMENT, "mobile should note be null: " + heZuo)
                    .setReadableMessage("联系方式不能为空").li();
        }
    }

    public HeZuo updateSigned(String id) {
        return heZuoRepository.updateSigned(id);
    }

    public HeZuo updateNote(Integer userId, String id, String noteValue) {
        Map<String, Object> note = new HashMap<>();
        note.put("userId", userId);
        note.put("content", noteValue);
        note.put("createtime", System.currentTimeMillis() / 1000);
        return heZuoRepository.updateNote(id, note);
    }
}
