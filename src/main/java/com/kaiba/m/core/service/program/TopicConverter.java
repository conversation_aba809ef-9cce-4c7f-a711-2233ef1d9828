package com.kaiba.m.core.service.program;

import com.google.gson.reflect.TypeToken;
import com.kaiba.lib.base.constant.program.TopicContentType;
import com.kaiba.lib.base.domain.common.ActionLink;
import com.kaiba.lib.base.domain.common.Image;
import com.kaiba.lib.base.domain.common.Video;
import com.kaiba.lib.base.domain.program.TopicModel;
import com.kaiba.lib.base.response.KbCode;
import com.kaiba.lib.base.response.KbException;
import com.kaiba.lib.base.util.StringUtils;
import com.kaiba.lib.base.util.appaction.AppActionModel;
import com.kaiba.m.core.domain.program.Topic;
import com.kaiba.m.core.util.JsonUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * author wangsj
 * date 2020-09-01
 */
public class TopicConverter {

    public static Topic model2Topic(TopicModel model) {
        Topic topic = new Topic();
        topic.setId(model.getId());
        topic.setProgramId(model.getProgramId());
        topic.setScheduleId(model.getScheduleId());
        topic.setSiteId(model.getSiteId());
        topic.setTitle(model.getTitle());
        if (model.getContent() != null) {
            topic.setContents(model.getContent().stream()
                    .peek(content -> verifyTopicContent(content.getType(), content.getData()))
                    .collect(Collectors.toList())
            );
        }
        topic.setCreateUser(model.getCreateUser());
        topic.setAdId(model.getAdId());
        topic.setCreateTime(model.getCreateTime());
        return topic;
    }

    public static TopicModel topicModel(Topic topic) {
        TopicModel model = new TopicModel();
        model.setId(topic.getId());
        model.setSiteId(topic.getSiteId());
        model.setTitle(topic.getTitle());
        model.setProgramId(topic.getProgramId());
        model.setScheduleId(topic.getScheduleId());
        model.setAdId(topic.getAdId());
        model.setContent(topic.getContents());
        model.setCreateTime(topic.getCreateTime());
        model.setCreateUser(topic.getCreateUser());
        model.setScheduleName(topic.getScheduleName());
        return model;
    }

    @SuppressWarnings("unchecked")
    public static void verifyTopicContent(String type, Object content) {
        switch (TopicContentType.valueOf(type)) {
            case TEXT:
                String text = (String) content;
                if (StringUtils.isEmpty(text)) {
                    throw new KbException(KbCode.REQUEST_PARAM_INVALID, "text content is empty");
                }
                break;
            case VOTE_ID:
                List<String> voteId = (ArrayList<String>) content;
                if (voteId == null || voteId.size() == 0) {
                    throw new KbException(KbCode.REQUEST_PARAM_INVALID, "vote id is empty");
                }
                break;
            case VIDEO:
                Video video = JsonUtils.toVideo(JsonUtils.getGson().toJson(content));
                if (video == null || StringUtils.isEmpty(video.getVideoId()) || StringUtils.isEmpty(video.getVideoKey())) {
                    throw new KbException(KbCode.REQUEST_PARAM_INVALID, "video key is empty");
                }
                break;
            case IMAGES:
                List<Image> imageList = JsonUtils.toImageList(JsonUtils.getGson().toJson(content));
                imageList.forEach(image -> {
                    if (StringUtils.isEmpty(image.getUrl())) {
                        throw new KbException(KbCode.REQUEST_PARAM_INVALID, "image url is empty");
                    }
                });
                break;
            case WEB_LINK:
                List<ActionLink> webLinks = JsonUtils.getGson().fromJson(JsonUtils.getGson().toJson(content),
                        new TypeToken<List<ActionLink>>() {}.getType());
                webLinks.forEach(link->{
                    if (StringUtils.isEmpty(link.getTitle())|| StringUtils.isEmpty(link.getIcon())){
                        throw new KbException(KbCode.REQUEST_PARAM_INVALID, "web link title or icon is empty");
                    }
                    if (!Objects.equals(link.getAction(), "pageWXMiniProgram")) {
                        AppActionModel.on(link.getAction()).setActionParams(link.getActionParams()).createAndValidate();
                    }
                });
                break;
            default:
                throw new KbException(KbCode.REQUEST_PARAM_INVALID, "content type invalided : " + type);
        }
    }
}
