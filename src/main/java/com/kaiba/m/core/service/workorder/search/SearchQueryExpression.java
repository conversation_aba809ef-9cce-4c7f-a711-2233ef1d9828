package com.kaiba.m.core.service.workorder.search;


import java.util.Collections;
import java.util.List;
import java.util.StringJoiner;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version SearchQueryExpression, v0.1 2024/7/30 17:04 daopei Exp $
 **/

public class SearchQueryExpression {

    private static final String indexSplit = ":";
    private static final String queryQuote = "'";
    private static final String termQuote = "\"";
    private static final String rangeLeftInclude = "[";
    private static final String rangeLeft = "(";
    private static final String rangeRightInclude = "]";
    private static final String rangeRight = ")";
    private static final String comma = ",";
    private static final String orQuote = "|";

    public static List<String> query(String field, List<String> values) {
        if (values == null || values.isEmpty()) {
            return Collections.emptyList();
        }
        return values.stream().map(v -> query(field, v)).collect(Collectors.toList());
    }
    // index:'value'
    public static String query(String field, String value) {
        return field + indexSplit + queryQuote + value + queryQuote;
    }

    public static String in(String field, List<String> values) {
        if (values == null || values.isEmpty()) {
            return null;
        }
        StringJoiner joiner = new StringJoiner(orQuote);
        values.stream()
                .map(v -> queryQuote + v + queryQuote)
                .forEach(joiner::add);
        return field + indexSplit + joiner;
    }

    //index:"value" 使用 "" 括起来进行查询，表示 phrase（短语）查询。即要求查询词分词后各个term的位置相连、顺序一致
    public static String term(String field, String value) {
        return field + indexSplit + termQuote + value + termQuote;
    }

    public static String rangeInclude(String field, String from, String to) {
        return range(field, from, to, true, true);
    }

    public static String rangeNotInclude(String field, String from, String to) {
        return range(field, from, to, false, false);
    }

    //index:[from,to] , index:(from,to) , index:[from,to) , index:(from,to]
    public static String range(String field, String from, String to, boolean leftInclude, boolean rightInclude) {
        return field + indexSplit + (leftInclude ? rangeLeftInclude : rangeLeft) + from + comma + to + (rightInclude? rangeRightInclude : rangeRight);
    }
}
