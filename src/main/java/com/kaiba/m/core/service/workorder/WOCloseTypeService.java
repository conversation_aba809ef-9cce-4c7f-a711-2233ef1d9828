package com.kaiba.m.core.service.workorder;

import com.kaiba.lib.base.assembler.Mapper;
import com.kaiba.lib.base.constant.workorder.WOCloseTypeState;
import com.kaiba.lib.base.domain.workorder.WOCloseTypeModel;
import com.kaiba.lib.base.response.KbCode;
import com.kaiba.lib.base.response.KbException;
import com.kaiba.m.core.domain.workorder.WOCloseType;
import com.kaiba.m.core.repository.workorder.WOCloseTypeRepository;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @version WOCloseTypeService, v0.1 2024/7/26 17:21 daopei Exp $
 **/
@Service
public class WOCloseTypeService {

    private final WOCloseTypeRepository closeTypeRepository;

    public WOCloseTypeService(WOCloseTypeRepository closeTypeRepository) {
        this.closeTypeRepository = closeTypeRepository;
    }

    public WOCloseType create(WOCloseTypeModel model) {
        WOCloseType closeType = Mapper.map(model, WOCloseType.class);
        closeType.setState(WOCloseTypeState.ENABLED.name());
        closeType.setCreateTime(System.currentTimeMillis());
        return closeTypeRepository.save(closeType);
    }

    public void updateState(String code, String state) {
        WOCloseTypeState.resolveByName(state).orElseThrow(() -> new KbException(KbCode.ILLEGAL_STATE).r("无效的结案原因状态").ld());
        closeTypeRepository.findByCode(code).orElseThrow(() -> new KbException(KbCode.RESOURCE_NOT_FOUND).r("未找到对应结案原因配置").ld());
        closeTypeRepository.updateState(code, state);
    }

    public Page<WOCloseType> getList(String code, List<String> bizList, String state, String identity, Integer page, Integer pageSize) {
        return closeTypeRepository.findByParam(code, bizList, state, identity, WOModelUtil.p(page, pageSize));
    }

    public Optional<WOCloseType> getByCode(String code) {
        return closeTypeRepository.findByCode(code);
    }
}
