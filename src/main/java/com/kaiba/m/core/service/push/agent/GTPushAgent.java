package com.kaiba.m.core.service.push.agent;

import com.getui.push.v2.sdk.ApiHelper;
import com.getui.push.v2.sdk.GtApiConfiguration;
import com.getui.push.v2.sdk.api.PushApi;
import com.getui.push.v2.sdk.api.UserApi;
import com.getui.push.v2.sdk.common.ApiResult;
import com.getui.push.v2.sdk.dto.req.UserDTO;
import com.getui.push.v2.sdk.dto.res.CidStatusDTO;
import com.kaiba.lib.base.constant.push.PushRange;
import com.kaiba.lib.base.domain.push.PushModel;
import com.kaiba.lib.base.response.KbCode;
import com.kaiba.lib.base.response.KbException;
import com.kaiba.lib.base.util.GsonUtils;
import com.kaiba.m.core.domain.push.PushTarget;
import com.kaiba.m.core.repository.push.PushRepository;
import com.kaiba.m.core.service.push.agent.gt.GTPushResult;
import com.kaiba.m.core.service.push.agent.gt.GTPushStrategy;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 个推推送服务
 * tips:对应个推[开吧] 应用
 *  该推送配置将冷处理慢慢弃用迁移到新配置 GTPushV2Agent
 *
 * <AUTHOR>
 * @version GTPushAgent, v0.1 2024/2/1 11:47 daopei Exp $
 **/
@Slf4j
@Component
public class GTPushAgent {

    private final String appId = "SXqwK4FcwG6b0Ln6aRXTW3";
    private final String appKey = "U33YO1TUDIAQtyg2kLYX38";
    private final String masterSecret = "5LzgwSlpJpApXvuljPoM81";

    GtApiConfiguration configuration;
    PushApi pushApi;
    UserApi userApi;
    @NonNull
    private final List<GTPushStrategy> strategies;
    @NonNull
    private final PushRepository pushRepository;

    public GTPushAgent(
            @NonNull List<GTPushStrategy> strategies,
            @NonNull PushRepository pushRepository
    ) {
        this.strategies = strategies;
        this.pushRepository = pushRepository;

        this.configuration = new GtApiConfiguration();
        this.configuration.setAppId(appId);
        this.configuration.setAppKey(appKey);
        this.configuration.setMasterSecret(masterSecret);
        ApiHelper apiHelper = ApiHelper.build(this.configuration);
        this.pushApi = apiHelper.creatApi(PushApi.class);
        this.userApi = apiHelper.creatApi(UserApi.class);
    }

    private GTPushStrategy getStrategy(PushRange range) {
        for (GTPushStrategy strategy : strategies) {
            if (strategy.support(range)) {
                return strategy;
            }
        }
        throw new KbException(KbCode.ERROR).r("个推推送未找到匹配策略,range:" + range.name());
    }


    public void push(PushModel model, List<PushTarget> targetList) {
        PushRange range = PushRange.valueOf(model.getRange()).orElseThrow(
                () -> new KbException(KbCode.ILLEGAL_ARGUMENT, "unknown range: " + model.getRange()));
        GTPushResult taskResult = getStrategy(range).execute(pushApi, model, targetList);
        pushRepository.updateGtPushResult(model.getId(), taskResult.getOriginResult(), taskResult.getTaskId());
    }


    /**
     * 添加用户黑名单
     * @param cid 对应 pushTarget.pushToken
     */
    public void addBlackUser(String cid) {
        userApi.addBlackUser(Collections.singleton(cid));
    }

    /**
     * 移除用户黑名单
     * @param cid
     */
    public void removeBlackUser(String cid) {
        userApi.removeBlackUser(Collections.singleton(cid));
    }

    public void printUserStatus(String cid) {
        ApiResult<Map<String, CidStatusDTO>> result = userApi.queryUserStatus(Collections.singleton(cid));
        log.info("getUserStatus result:{}", GsonUtils.getGson().toJson(result));
    }

    /**
     * 批量添加用户标签
     * result : key为cid，value为结果，true表示成功，否则失败
     * @param tag 标签值
     * @param cidSet {@link PushTarget#getPushToken()} 推送用户CID 列表
     */
    public void addUserTagBatch(String tag, Set<String> cidSet) {
        UserDTO userDTO = UserDTO.build();
        userDTO.setCid(cidSet);
        ApiResult<Map<String, String>> result = userApi.usersBindTag(tag, userDTO);
        log.info("addUserTagBatch result:{}", GsonUtils.getGson().toJson(result));
    }

    /**
     * 批量删除用户标签
     * @param tag 标签值
     * @param cidSet {@link PushTarget#getPushToken()} 推送用户CID 列表
     */
    public void deleteUserTagBatch(String tag, Set<String> cidSet) {
        UserDTO userDTO = UserDTO.build();
        userDTO.setCid(cidSet);
        ApiResult<Map<String, String>> result = userApi.deleteUsersTag(tag, userDTO);
        log.info("deleteUserTagBatch result:{}", GsonUtils.getGson().toJson(result));
    }

}
