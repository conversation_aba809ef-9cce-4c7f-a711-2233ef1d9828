package com.kaiba.m.core.service.sms.legacy;

import com.cloopen.rest.sdk.CCPRestSmsSDK;
import com.kaiba.lib.base.constant.KbEndpoint;
import com.kaiba.lib.base.domain.sms.SmsModel;
import com.kaiba.lib.base.response.KbCode;
import com.kaiba.lib.base.response.KbException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.HashMap;

/**
 * <AUTHOR>
 * @date 2018/12/18
 * https://doc.yuntongxun.com/space/5a5098313b8496dd00dcdd7f
 * https://doc.yuntongxun.com/p/5a533e0c3b8496dd00dce08c#h3-1-2%20Java%20Demo%E4%B8%AD%E6%A8%A1%E6%9D%BF%E7%9F%AD%E4%BF%A1%E7%9A%84%E4%BD%BF%E7%94%A8%E8%AF%B4%E6%98%8E
 */
@Slf4j
@Service
public class RongLianSmsService {

    @Value("${kaiba.sms.ronglianyun.serverIP}")
    private String serverIp;

    @Value("${kaiba.sms.ronglianyun.serverPort}")
    private String serverPort;

    @Value("${kaiba.sms.ronglianyun.accountSid}")
    private String accountSid;

    @Value("${kaiba.sms.ronglianyun.accountToken}")
    private String accountToken;

    @Value("${kaiba.sms.ronglianyun.appIdForKaiba}")
    private String appIdForKaiba;

    @Value("${kaiba.sms.ronglianyun.appIdForExpert}")
    private String appIdForExpert;

    private CCPRestSmsSDK restAPIForKaiba;
    private CCPRestSmsSDK restAPIForExpert;

    @PostConstruct
    private void init() {
        restAPIForKaiba = new CCPRestSmsSDK();
        restAPIForKaiba.init(serverIp, serverPort);
        restAPIForKaiba.setAccount(accountSid, accountToken);
        restAPIForKaiba.setAppId(appIdForKaiba);

        restAPIForExpert = new CCPRestSmsSDK();
        restAPIForExpert.init(serverIp, serverPort);
        restAPIForExpert.setAccount(accountSid, accountToken);
        restAPIForExpert.setAppId(appIdForExpert);
    }

    public void sendMessage(SmsModel model) {
        HashMap<String, Object> result = getSmsSdk(model.getEndpoint()).sendTemplateSMS(
                StringUtils.join(model.getTargetArr(), ","),
                model.getTemplateId(),
                model.getPlaceHolder());
        if (!"000000".equals(result.get("statusCode"))) {
            String debugMessage = "ronglian error: " +
                    "errorCode=" + result.get("statusCode") +
                    " errorMeg= " + result.get("statusMsg") +
                    " info:" + model;
            // 容联云短信发送状态码文档:
            // http://doc.yuntongxun.com/p/5a5857203b8496dd00dd2b3d
            Object code = result.get("statusCode");
            if (("160038").equals(code)) {
                throw new KbException(KbCode.THIRD_PARTY_RONG_LIAN_WRONG_RESPONSE, debugMessage)
                        .setReadableMessage("验证码30秒内只能发送一次, 请稍后再试").li();
            } else if (("160034").equals(code)) {
                throw new KbException(KbCode.THIRD_PARTY_RONG_LIAN_WRONG_RESPONSE, debugMessage)
                        .setReadableMessage("因您投诉过短信服务商, 导致验证码无法送达. 请至开吧意见反馈联系运营人员处理").li();
            } else if (("160040").equals(code)) {
                throw new KbException(KbCode.THIRD_PARTY_RONG_LIAN_WRONG_RESPONSE, debugMessage)
                        .setReadableMessage("验证码超出同号码当天发送上限").li();
            } else {
                throw new KbException(KbCode.THIRD_PARTY_RONG_LIAN_WRONG_RESPONSE, debugMessage)
                        .setReadableMessage("短信发送失败").lw();
            }
        }
    }

    // ----------------------------------------------------------

    private CCPRestSmsSDK getSmsSdk(KbEndpoint endpoint) {
        switch (endpoint) {
            case KAIBA:
            case KAIBA_FLUTTER:
            case CMS:
                return restAPIForKaiba;
            case EXPERT:
            case LAWYER:
                return restAPIForExpert;
            case CZFW:
            case LIVE_RECORD:
            default:
                throw new KbException(KbCode.ILLEGAL_ARGUMENT, "KbEndpoint not supported" + endpoint);
        }
    }
}
