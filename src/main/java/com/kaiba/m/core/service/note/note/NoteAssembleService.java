package com.kaiba.m.core.service.note.note;

import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.LoadingCache;
import com.kaiba.lib.base.constant.KbEndpoint;
import com.kaiba.lib.base.constant.KbHttpHeaders;
import com.kaiba.lib.base.constant.note.NoteOrder;
import com.kaiba.lib.base.constant.note.NoteReviewState;
import com.kaiba.lib.base.domain.note.NoteCommentModel;
import com.kaiba.lib.base.domain.note.NoteGrainFlag;
import com.kaiba.lib.base.domain.note.NoteModel;
import com.kaiba.lib.base.service.INoteService;
import com.kaiba.lib.base.util.ServletRequestUtils;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.time.Duration;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version NoteAssembleService, v0.1 2023/9/12 11:31 daopei Exp $
 **/
@Slf4j
@Service
@RequiredArgsConstructor
public class NoteAssembleService {


    private static final Duration REVIEW_LIST_TIME_EXTENT_DEFAULT = Duration.ofDays(30);
    private static final int REVIEW_LIST_SIZE_MAX = 10;
    private static final int REVIEW_LIST_VERSION_THRESHOLD = 69000;
    private static final Set<Integer> REVIEW_LIST_ENABLED_ENDPOINT = new HashSet<>(4);

    static {
        REVIEW_LIST_ENABLED_ENDPOINT.add(KbEndpoint.KAIBA_FLUTTER.getValue());
        REVIEW_LIST_ENABLED_ENDPOINT.add(KbEndpoint.H5.getValue());
    }

    @NonNull
    private final INoteService noteService;
    @NonNull
    private final NoteReviewCacheService noteReviewCacheService;

    //审核贴缓存,命中率较低使用内存方式.
    private final LoadingCache<String, NoteModel> noteReviewCache = Caffeine.newBuilder()
            .expireAfterWrite(Duration.ofMinutes(60))
            .build(this::getNoteReviewById);

    /**
     * 列表混入待审核
     * @param queryCreator 是否查询本人帖子列表
     */
    public List<NoteModel> blendReviewByThread(
            List<NoteModel> list, String threadId, Integer userId,
            long grainFlag, boolean threadCheck, boolean queryCreator, Integer page, Integer pageSize) {
        //不需要校验是否存在板块则直接查询
        if (threadCheck && threadId == null) {
            return list;
        } else {
            return blendReview(list, threadId, userId, grainFlag, queryCreator, page, pageSize);
        }
    }

    /**
     * 根据创建人混入待审核仅作者可见
     */
    public List<NoteModel> blendReviewByCreator(
            List<NoteModel> list, String threadId, Integer creatorUserId, Integer viewUserId,
            long grainFlag, boolean threadCheck, Integer page, Integer pageSize) {
        if (creatorUserId == null || viewUserId == null) {
            return list;
        }
        //发帖用户和查询用户非同一人不混入
        if (!creatorUserId.equals(viewUserId)) {
            return list;
        }
        return blendReviewByThread(list, threadId, creatorUserId, grainFlag, threadCheck, true, page, pageSize);
    }

    /**
     * 待审核帖子ID替换为审核ID，查询详情页使用
     */
    public void blendNoteUserReviewId(NoteModel noteModel) {
        if (noteModel == null) {
            return;
        }
        if (noteModel.getNoteReviewState() == null) {
            return;
        }
        if (noteModel.getNoteReviewState() == NoteReviewState.NOT_REVIEWED.getValue()) {
            noteModel.setId(noteModel.getNoteReviewId());
        }
    }

    /**
     * 将用户未审核通过评论添加到评论列表中
     *
     * @param list   审核通过评论列表
     * @param page   帖子当前页
     * @param noteId 帖子id
     * @param userId 用户id
     * @return 数据
     */
    public List<NoteCommentModel> blendCommentReview(
            List<NoteCommentModel> list, Integer page, Integer pageSize, String noteId, Integer userId) {
        if (userId == null || list == null || list.size() == 0) {
            return list;
        }
        List<NoteCommentModel> reviews = noteService
                .getCommentReviewListByUser(userId, userId, noteId, 1, 20).getData();
        if (reviews == null || reviews.size() == 0) {
            return list;
        }
        Long firstTime = list.get(0).getCreateTime();
        Long lastTime = list.get(list.size() - 1).getCreateTime();
        reviews = reviews
                .stream()
                .filter(t -> {
                    if (page == 1) {
                        return list.size() != pageSize || t.getCreateTime() > lastTime;
                    }
                    return list.size() < pageSize
                            ? t.getCreateTime() < firstTime
                            : t.getCreateTime() > lastTime && t.getCreateTime() < firstTime;
                })
                .collect(Collectors.toList());
        list.addAll(reviews);
        list.sort((c1, c2) -> (int) (c2.getCreateTime() - c1.getCreateTime()));
        return list;
    }

    /**
     * noteReview -> noteModel
     */
    public NoteModel blendNoteReview(String noteReviewId, Integer userId) {
        if (noteReviewId == null || userId == null) {
            return null;
        }
        NoteModel noteModel = noteReviewCache.get(noteReviewId);
        if (noteModel == null) {
            return null;
        }
        //非发帖者为空
        if (!Objects.equals(noteModel.getUserId(), userId)) {
            return null;
        }
        return noteModel;
    }

    // -----------------------------------------------------------------------------------

    private List<NoteModel> blendReview(
            List<NoteModel> list,
            String threadId,
            Integer userId,
            long grainFlag,
            boolean queryCreator,
            Integer page,
            Integer pageSize
    ) {
        if (userId == null || list == null) {
            return list;
        }
        NoteGrainFlag grain = new NoteGrainFlag(grainFlag);
        NoteOrder noteOrder = grain.getNoteOrder();
        // 如果排序非指定, 则列表不混入用户自己的未过审贴
        if (noteOrder != NoteOrder.CREATE_TIME_DESC && noteOrder != NoteOrder.REFRESH_TIME_DESC) {
            return list;
        }
        HttpServletRequest request = ServletRequestUtils.getCurrentRequest();
        if (request == null) {
            return list;
        }
        Integer endpoint = KbHttpHeaders.KB_EP.getValidIntegerHeaderOrNull(request);
        if (endpoint == null || !REVIEW_LIST_ENABLED_ENDPOINT.contains(endpoint)) {
            // 只有部分终端需要开启混入自己审核帖的功能
            return list;
        }
        // 客户端类型才进行版本控制
        if (endpoint == KbEndpoint.KAIBA_FLUTTER.getValue()) {
            Integer v = KbHttpHeaders.KB_VC.getValidIntegerHeaderOrNull(request);
            // 如果版本不符, 则列表不混入用户自己的未过审贴
            if (v == null || v < REVIEW_LIST_VERSION_THRESHOLD) {
                return list;
            }
        }

        String token = KbHttpHeaders.KB_TOKEN.getValidHeaderOrNull(request);
        // 如果未携带用户token, 则列表不混入用户自己的未过审贴.
        if (token == null) {
            return list;
        }

        // 确定获取审核帖列表的时间范围. 根据入参获取最大时间.
        long et = System.currentTimeMillis() / 1000;
        if (page > 1) {
            List<NoteModel> lastPageList;
            if (queryCreator) {
                lastPageList = noteService.getNoteListByCreatorIdForC(userId, userId, threadId, grainFlag, page - 1, pageSize).data().orElse(new ArrayList<>());
            } else {
                lastPageList = noteService.getNoteListByThread(threadId, userId, grainFlag, page - 1, pageSize, null).data().orElse(new ArrayList<>());
            }
            if (lastPageList.size() >= 1) {
                et = lastPageList.get(lastPageList.size() - 1).getCreateTimeMS() / 1000;
            } else {
                et = 0L;
            }
        }
        // 确定获取审核帖列表的时间范围. 找到该帖子列表的最小创建时间.
        long st = list.stream()
                .filter(t -> BooleanUtils.isNotTrue(t.getIsTop()))
                .filter(t -> BooleanUtils.isNotTrue(t.getIsHot()))
                .map(NoteModel::getCreateTime)
                .filter(Objects::nonNull)
                .min(Long::compareTo)
                .orElse(0L);

        if (st == 0) {
            st = et - REVIEW_LIST_TIME_EXTENT_DEFAULT.getSeconds();
        }
        // 如果起止时间间隔已经大于阈值, 则不再向列表混入未审核贴.
        if (et - st <= 0) {
            return list;
        }
        // 只取该用户的前 10 个未审核贴,带板块带走缓存,纯用户纬度的直接走db查询
        List<NoteModel> reviewList;
        if (grain.isAllowCache() && threadId != null) {
            reviewList = noteReviewCacheService.getNoteModel(threadId, userId, st, et);
        } else {
            reviewList = noteService.getNoteReviewListByUser(userId, threadId, st, et, 1, REVIEW_LIST_SIZE_MAX).getData();
        }

        //填充noteId,剔除主列表重复
        filterRepeatNoteReview(list, reviewList);
        if (reviewList == null || reviewList.size() == 0) {
            return list;
        }


        list.addAll(reviewList);
        if (noteOrder == NoteOrder.REFRESH_TIME_DESC) {
            list.sort(REFRESH_TIME_COMPARATOR);
        } else {
            list.sort(CREATE_TIME_COMPARATOR);
        }

        //置顶恢复
        List<NoteModel> topList = list.stream()
                .filter(t -> BooleanUtils.isTrue(t.getIsTop()))
                .collect(Collectors.toList());
        if (topList.size() <= 0) {
            return list;
        }
        List<NoteModel> resultList = new ArrayList<>(topList);
        List<String> topIdList = topList.stream().map(NoteModel::getId).collect(Collectors.toList());
        for (NoteModel noteModel : list) {
            if (noteModel.getId() != null && topIdList.contains(noteModel.getId())) {
                continue;
            }
            resultList.add(noteModel);
        }
        return resultList;
    }


    private static final Comparator<NoteModel> CREATE_TIME_COMPARATOR = (c1, c2) -> {
        int compareTop = compareNoteModelIsTop(c1, c2);
        if (compareTop == 0) {
            return (int) (c2.getCreateTime() - c1.getCreateTime());
        } else {
            return compareTop;
        }
    };

    private static final Comparator<NoteModel> REFRESH_TIME_COMPARATOR = (c1, c2) -> {
        int compareTop = compareNoteModelIsTop(c1, c2);
        if (compareTop == 0) {
            return (int) (c2.getRefreshTime() - c1.getRefreshTime());
        } else {
            return compareTop;
        }
    };

    private static int compareNoteModelIsTop(NoteModel c1, NoteModel c2) {
        boolean t1 = c1.getIsTop() != null && c1.getIsTop();
        boolean t2 = c2.getIsTop() != null && c2.getIsTop();
        if (t1 && !t2) {
            return 1;
        } else if (!t1 && t2) {
            return -1;
        } else {
            return 0;
        }
    }

    /**
     * 移除重复已经审核通过的帖子
     */
    private void filterRepeatNoteReview(List<NoteModel> main, List<NoteModel> review) {
        if (review == null || review.size() == 0) {
            return;
        }
        List<String> reviewIds = main.stream().map(NoteModel::getNoteReviewId).distinct().collect(Collectors.toList());
        review.removeIf(noteModel -> reviewIds.contains(noteModel.getNoteReviewId()));
    }

    private NoteModel getNoteReviewById(String noteReviewId) {
        return noteService.getNoteReviewById(noteReviewId).dataIgnoreError().orElse(null);
    }
}
