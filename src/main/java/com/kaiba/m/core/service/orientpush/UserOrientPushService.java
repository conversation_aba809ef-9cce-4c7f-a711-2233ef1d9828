package com.kaiba.m.core.service.orientpush;

import com.kaiba.lib.base.constant.KbModule;
import com.kaiba.lib.base.domain.news.article.ArticleModel;
import com.kaiba.lib.base.domain.push.PushModel;
import com.kaiba.lib.base.response.KbCode;
import com.kaiba.lib.base.response.KbException;
import com.kaiba.lib.base.service.INeoNewsArticleService;
import com.kaiba.lib.base.service.IPushService;
import com.kaiba.lib.base.util.StringUtils;
import com.kaiba.lib.base.util.appaction.AppActionType;
import com.kaiba.m.core.constant.orientpush.OPScope;
import com.kaiba.m.core.domain.orientpush.OrientPush;
import com.kaiba.m.core.domain.orientpush.UserOrientPush;
import com.kaiba.m.core.domain.orientpush.UserOrientTarget;
import com.kaiba.m.core.repository.orientpush.UserOrientPushRepository;
import com.kaiba.m.core.repository.orientpush.UserOrientPushTargetRepository;
import com.kaiba.m.core.service.news.article.NewsArticleModelHelper;
import com.kaiba.m.core.util.PageUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 业务聚合的指定用户定向推送服务
 * <AUTHOR>
 * @version UserOrientPushService, v0.1 2025/5/13 11:12 daopei Exp $
 **/
@Slf4j
@Service
public class UserOrientPushService {

    private final UserOrientPushRepository userOrientPushRepository;
    private final UserOrientPushTargetRepository userOrientPushTargetRepository;
    private final OrientPushService orientPushService;
    private final IPushService pushService;
    private final INeoNewsArticleService articleService;
    @Value("${kb.host.page}")
    private String pageHost;


    public UserOrientPushService(
            UserOrientPushRepository userOrientPushRepository,
            UserOrientPushTargetRepository userOrientPushTargetRepository,
            OrientPushService orientPushService,
            INeoNewsArticleService articleService,
            IPushService pushService
    ) {
        this.orientPushService = orientPushService;
        this.userOrientPushRepository = userOrientPushRepository;
        this.userOrientPushTargetRepository = userOrientPushTargetRepository;
        this.articleService = articleService;
        this.pushService = pushService;
    }


    /**
     * 根据业务标识获取推送基础信息列表
     * @param biz
     * @param ref1
     * @param page
     * @param pageSize
     * @return
     */
    public Page<PushModel> getPushBaseList(String biz, String ref1, Integer page, Integer pageSize) {
        Pageable pageable = PageUtils.ofDefault(page, pageSize);
        Page<UserOrientPush> pageList = userOrientPushRepository.findByBizAndRef1(biz, ref1, pageable);
        List<PushModel> pushModels = pushService.getPushListByIdIn(pageList.getContent().stream().map(UserOrientPush::getPushId).toArray(String[]::new)).dataOrThrow();
        Map<String, PushModel> pushMap = pushModels.stream().collect(Collectors.toMap(PushModel::getId, Function.identity(), (o1, o2) -> o1));
        return pageList.map(userOrientPush -> pushMap.get(userOrientPush.getPushId()));
    }


    /**
     * 文章稿件定向推送
     * @param articleId
     * @param group
     * @param createUserId
     * @param tags
     * @param title 推送标题（为空使用文章标题推送）
     * @param subTitle 推送副标题（为空使用文章副标题推送 ）
     */
    public void articleOrientPush(String articleId, String group, Integer createUserId, List<String> tags, String title, String subTitle) {
        ArticleModel articleModel = articleService.getArticleById(articleId, false, false).dataOrThrow();
        List<UserOrientTarget> targets = getUserListByTags(tags);
        if (targets.isEmpty()) {
            log.info("article orientPush no target, articleId:{},tags:{}", articleId, tags);
            throw new KbException(KbCode.ILLEGAL_STATE, "无可推送用户");
        }
        List<Integer> pushUserIds = targets.stream().map(UserOrientTarget::getUserId).collect(Collectors.toList());
        OrientPush orientPush = map2OrientPush(articleModel, group, createUserId, pushUserIds, title, subTitle);
        //创建定向推送
        orientPush = orientPushService.createOrientPush(orientPush);
        //用户定向推送记录
        UserOrientPush userOrientPush = new UserOrientPush();
        userOrientPush.setOrientPushId(orientPush.getId());
        userOrientPush.setPushId(orientPush.getPushId());
        userOrientPush.setBiz(KbModule.NEWS_NEO.name());
        userOrientPush.setRef1(articleId);
        userOrientPush.setCreateUserId(createUserId);
        userOrientPush.setUserIds(pushUserIds);
        userOrientPush.setCreateTime(System.currentTimeMillis());
        userOrientPushRepository.save(userOrientPush);

        //执行定向推送
        orientPushService.push(orientPush.getId());
    }

    public UserOrientTarget upsertUser(Integer operatorUserId, Integer userId, List<String> tags) {
        log.info("upsertUser: userId:{}, tags:{}, operatorId:{}", userId, tags, operatorUserId);
        return userOrientPushTargetRepository.upsert(userId, tags);
    }


    // ---------------------------------------------------


    /**
     * 定向推送模型转换
     * @param newsArticle
     * @param group
     * @param createUserId
     * @param userIds
     * @param title 推送标题（为空使用文章标题推送）
     * @param subTitle 推送副标题（为空使用文章副标题推送）
     * @return
     */
    private OrientPush map2OrientPush(ArticleModel newsArticle, String group, Integer createUserId, List<Integer> userIds, String title, String subTitle) {
        String pushTitle = StringUtils.isEmpty(title) ? newsArticle.getTitle() : title;
        String pushSubTitle = StringUtils.isEmpty(subTitle) ? newsArticle.getSubtitle() : subTitle;

        AppActionType action = AppActionType.PAGE_NEWS_DETAIL;
        HashMap<String, Object> params = new HashMap<>();
        if (Objects.equals(AppActionType.PAGE_WX_MINI_PROGRAM.name(), newsArticle.getAction())) {
            action = AppActionType.PAGE_WX_MINI_PROGRAM;
            params.put("wxMiniUserName",
                    String.valueOf(newsArticle.getActionParams().get("wxMiniUserName")));
            params.put("wxMiniPath",
                    String.valueOf(newsArticle.getActionParams().get("wxMiniPath")));
        } else {
            params.put("url", NewsArticleModelHelper.assembleArticleUrl(pageHost,
                    newsArticle.getId(), newsArticle.getRenderer(), group));
            params.put("title", pushTitle);
            params.put("siteId", newsArticle.getSiteId().toString());
            params.put("pushType", "news");
            params.put("id", newsArticle.getId());
            params.put("EventObjectId", "news-" + newsArticle.getId());
            params.put("SelfObjectId", "news-" + newsArticle.getId());
            params.put("EventChannelClassName", "新闻资讯");
            params.put("EventChannelClassId", "NEWS");
        }

        OrientPush orientPush = new OrientPush();
        orientPush.setAction(action.getAction());
        orientPush.setActionParams(params);
        orientPush.setTitle(pushTitle);
        if (pushSubTitle == null) {
            orientPush.setSubTitle("点击查看");
        } else {
            orientPush.setSubTitle(pushSubTitle);
        }
        orientPush.setScope(OPScope.USERS.getValue());
        orientPush.setUserIds(userIds);
        orientPush.setCreateUserId(createUserId);
        return orientPush;
    }

    /**
     * 根据标签获取全部用户列表
     * @param tags
     * @return
     */
    private List<UserOrientTarget> getUserListByTags(List<String> tags) {
        int page = 0;
        int pageSize = 500;
        boolean hasNext;

        List<UserOrientTarget> result = new ArrayList<>();
        do {
            Pageable p = PageRequest.of(page, pageSize);
            Page<UserOrientTarget> targets = userOrientPushTargetRepository.findByTagsIn(tags, p);
            hasNext = targets.hasNext();

            result.addAll(targets.getContent());
            page++;
        } while (hasNext);

        return result;
    }
}
