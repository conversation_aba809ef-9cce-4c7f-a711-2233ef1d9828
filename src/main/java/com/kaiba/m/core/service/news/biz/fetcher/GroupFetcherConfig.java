package com.kaiba.m.core.service.news.biz.fetcher;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

import java.util.List;

/**
 * author: lyux
 * date: 2024-10-22
 */
@Data
@ToString
@NoArgsConstructor
class GroupFetcherConfig {

    /** 唯一标识, 用于手动操作时的入参 */
    private String key;

    /** 是否启用 */
    private boolean enabled;

    /** 数据获取模式. {@link GroupFetcherMode} */
    private GroupFetcherMode mode;

    /** 数量. */
    private int count;

    /** 目标分组 */
    private String targetGroup;

    /** 数据源分组 */
    private List<String> sourceGroups;

    public static GroupFetcherConfigBuilder on() {
        return new GroupFetcherConfigBuilder();
    }

    public static final class GroupFetcherConfigBuilder {
        private final GroupFetcherConfig config;

        private GroupFetcherConfigBuilder() {
            config = new GroupFetcherConfig();
            config.setEnabled(true);
        }

        public GroupFetcherConfigBuilder key(String key) {
            config.setKey(key);
            return this;
        }

        public GroupFetcherConfigBuilder enabled(boolean enabled) {
            config.setEnabled(enabled);
            return this;
        }

        public GroupFetcherConfigBuilder mode(GroupFetcherMode mode) {
            config.setMode(mode);
            return this;
        }

        public GroupFetcherConfigBuilder count(int count) {
            config.setCount(count);
            return this;
        }

        public GroupFetcherConfigBuilder targetGroup(String targetGroup) {
            config.setTargetGroup(targetGroup);
            if (config.getKey() == null) {
                config.setKey(targetGroup);
            }
            return this;
        }

        public GroupFetcherConfigBuilder sourceGroups(List<String> sourceGroups) {
            config.setSourceGroups(sourceGroups);
            return this;
        }

        public GroupFetcherConfig build() {
            return config;
        }
    }
}
