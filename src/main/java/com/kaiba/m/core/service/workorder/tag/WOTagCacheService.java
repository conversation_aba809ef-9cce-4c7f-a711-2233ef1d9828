package com.kaiba.m.core.service.workorder.tag;

import com.github.benmanes.caffeine.cache.CacheLoader;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.LoadingCache;
import com.google.gson.reflect.TypeToken;
import com.kaiba.lib.base.util.GsonUtils;
import com.kaiba.m.core.domain.workorder.WOTag;
import com.kaiba.m.core.domain.workorder.WOTaggedCase;
import com.kaiba.m.core.repository.workorder.WOTagRepository;
import com.kaiba.m.core.repository.workorder.WOTaggedCaseRepository;
import com.kaiba.m.core.service.workorder.WOModelUtil;
import lombok.extern.slf4j.Slf4j;
import org.checkerframework.checker.nullness.qual.NonNull;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.redis.connection.StringRedisConnection;
import org.springframework.data.redis.core.RedisCallback;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.util.*;
import java.util.stream.Collectors;

/**
 * author: lyux
 * date: 2024-05-31
 */
@Slf4j
@Service
public class WOTagCacheService {

    private static final Long FAKE_TAG_CODE = Long.MAX_VALUE;
    private static final String REDIS_CASE_TAG_CACHE_KEY = "java_core_wo_tagged_";

    private final WOTagRepository tagRepository;
    private final WOTaggedCaseRepository taggedRepository;
    private final StringRedisTemplate redisTemplate;

    WOTagCacheService(
            WOTagRepository tagRepository,
            WOTaggedCaseRepository taggedRepository,
            StringRedisTemplate redisTemplate
    ) {
        this.tagRepository = tagRepository;
        this.taggedRepository = taggedRepository;
        this.redisTemplate = redisTemplate;
    }

    public Optional<WOTag> getTagByCode(Long code) {
        if (code == null || code <= 0 || FAKE_TAG_CODE.equals(code)) {
            return Optional.empty();
        } else {
            WOTag model = tagByCodeCache.get(code);
            if (model == null || model.getCode() == 0 || FAKE_TAG_CODE.equals(model.getCode())) {
                return Optional.empty();
            } else {
                return Optional.of(model);
            }
        }
    }

    public Optional<WOTag> getTagByKey(String key) {
        Long code = tagCodeByKeyCache.get(key);
        if (code == null || code <= 0 || FAKE_TAG_CODE.equals(code)) {
            return Optional.empty();
        } else {
            return getTagByCode(code);
        }
    }

    public Map<String, Long> getTagByKeys(Collection<String> keys) {
        if (keys == null || keys.isEmpty()) {
            return Collections.emptyMap();
        } else {
            return tagCodeByKeyCache.getAll(keys);
        }
    }

    public Map<Long, WOTag> getTagMapByCodes(Collection<Long> codes) {
        if (codes == null || codes.isEmpty()) {
            return Collections.emptyMap();
        } else {
            return tagByCodeCache.getAll(codes);
        }
    }

    public List<WOTag> getTagListByCodes(Collection<Long> codes) {
        Map<Long, WOTag> map = getTagMapByCodes(codes);
        if (map == null || map.isEmpty()) {
            return Collections.emptyList();
        } else {
            return codes.stream().map(map::get).filter(Objects::nonNull).collect(Collectors.toList());
        }
    }

    public List<String> getToppedCaseListByCode(Long code) {
        List<String> list = toppedCaseByTagCache.get(code);
        return list == null ? Collections.emptyList() : list;
    }

    public void resetCaseTagCache(String caseId, List<Long> tags) {
        String cacheKey = REDIS_CASE_TAG_CACHE_KEY + caseId;
        if (tags == null) {
            tags = new ArrayList<>();
        }
        String value = GsonUtils.getGson().toJson(tags);
        redisTemplate.opsForValue().set(cacheKey, value);
    }

    public List<Long> getTaggedByCase(String caseId) {
        String cacheKey = REDIS_CASE_TAG_CACHE_KEY + caseId;
        String value = redisTemplate.opsForValue().get(cacheKey);
        if (value == null) {
            List<Long> tags = getTagByCaseFromDB(caseId).stream().map(WOTaggedCase::getTag).collect(Collectors.toList());
            resetCaseTagCache(caseId, tags);
            log.debug("miss case tag query load from db, caseId:{}, tags:{}", caseId, GsonUtils.getGson().toJson(tags));
            return tags;
        }
        return GsonUtils.getGson().fromJson(value, new TypeToken<List<Long>>() {}.getType());
    }

    public Map<String, List<Long>> getTaggedByCaseList(List<String> caseIds) {
        if (caseIds == null || caseIds.isEmpty()) {
            return Collections.emptyMap();
        }
        List<String> cacheValues = redisTemplate.executePipelined((RedisCallback<Object>) connection -> {
            StringRedisConnection stringRedisConn = (StringRedisConnection) connection;
            for (String caseId : caseIds) {
                stringRedisConn.get(REDIS_CASE_TAG_CACHE_KEY + caseId);
            }
            return null;
        }).stream().map(o -> (String) o).collect(Collectors.toList());
        Map<String, List<Long>> result = new HashMap<>();
        List<String> missCaseIds = new ArrayList<>();
        for (int i = 0; i < caseIds.size(); i++) {
            String key = caseIds.get(i);
            String json = cacheValues.get(i);
            if (json != null) {
                List<Long> list = GsonUtils.getGson().fromJson(json, new TypeToken<List<Long>>() {}.getType());
                result.put(key, list);
            } else {
                missCaseIds.add(key);
            }
        }
        //未命中换成从数据库查询
        if (!missCaseIds.isEmpty()) {
            for (String missCaseId : missCaseIds) {
                List<Long> tags = getTagByCaseFromDB(missCaseId).stream().map(WOTaggedCase::getTag).collect(Collectors.toList());
                resetCaseTagCache(missCaseId, tags);
                result.put(missCaseId, tags);
                log.debug("miss case tag query load from db batch, caseId:{}, tags:{}", missCaseId, GsonUtils.getGson().toJson(tags));
            }
        }
        return result;
    }

    // -----------------------------------------------------------------

    private List<WOTaggedCase> getTagByCaseFromDB(String caseId) {
        return taggedRepository.getByCaseIdOrderByIdDesc(caseId, WOModelUtil.p(1, 200));
    }

    // -----------------------------------------------------------------

    private static WOTag trimTag(WOTag tag) {
        tag.setId(null);
        //todo 确认为什么要隐藏key
//        tag.setKey(null);
        tag.setPer(null);
        tag.setBiz(null);
        tag.setDesc(null);
        tag.setLevel(null);
        tag.setUpdateTime(null);
        tag.setCreateTime(null);
        return tag;
    }

    private final LoadingCache<Long, WOTag> tagByCodeCache = Caffeine.newBuilder()
            .expireAfterWrite(Duration.ofMinutes(15))
            .refreshAfterWrite(Duration.ofSeconds(73))
            .build(new CacheLoader<Long, WOTag>() {
                @Override
                public WOTag load(@NonNull Long tagCode) {
                    return tagRepository.findFirstByCode(tagCode)
                            .map(WOTagCacheService::trimTag)
                            .orElseGet(() -> {
                                WOTag tag = new WOTag();
                                tag.setCode(FAKE_TAG_CODE);
                                return tag;
                            });
                }

                @Override
                public WOTag reload(@NonNull Long key, @NonNull WOTag oldValue) {
                    WOTag model = load(key);
                    return model == null ? oldValue : model;
                }

                @Override
                public @NonNull Map<Long, WOTag> loadAll(@NonNull Iterable<? extends Long> codes) {
                    Set<Long> codeSet = new HashSet<>();
                    codes.forEach(codeSet::add);
                    return tagRepository.getByCodeIn(codeSet).stream()
                            .map(WOTagCacheService::trimTag)
                            .collect(Collectors.toMap(WOTag::getCode, t -> t, (t1, t2) -> t1));
                }
            });

    private final LoadingCache<Long, List<String>> toppedCaseByTagCache = Caffeine.newBuilder()
            .expireAfterWrite(Duration.ofMinutes(3))
            .build(new CacheLoader<Long, List<String>>() {
                @Override
                public List<String> load(@NonNull Long tag) {
                    return taggedRepository.getByTagOrderBySeqDesc(tag, PageRequest.of(0, 30)).stream()
                            .filter(tagged -> tagged.getIdx() != null)
                            .map(WOTaggedCase::getCaseId)
                            .collect(Collectors.toList());
                }
            });

    private final LoadingCache<String, Long> tagCodeByKeyCache = Caffeine.newBuilder()
            .expireAfterWrite(Duration.ofMinutes(3))
            .build(new CacheLoader<String, Long>() {
                @Override
                public Long load(@NonNull String tagKey) {
                    return tagRepository.findFirstByKey(tagKey)
                            .map(WOTag::getCode).orElse(FAKE_TAG_CODE);
                }
            });

}
