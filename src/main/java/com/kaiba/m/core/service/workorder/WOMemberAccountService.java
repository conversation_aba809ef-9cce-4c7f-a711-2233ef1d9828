package com.kaiba.m.core.service.workorder;

import com.kaiba.lib.base.constant.KbProperties;
import com.kaiba.lib.base.constant.sms.SmsCodeMark;
import com.kaiba.lib.base.domain.user.LoginUserModel;
import com.kaiba.lib.base.domain.user.UserModel;
import com.kaiba.lib.base.middleware.jwt.KbJWTKeyProvider;
import com.kaiba.lib.base.response.KbCode;
import com.kaiba.lib.base.response.KbEntity;
import com.kaiba.lib.base.response.KbException;
import com.kaiba.lib.base.service.ISmsService;
import com.kaiba.lib.base.service.IUserAccountService;
import com.kaiba.m.core.domain.workorder.WOTeam;
import com.kaiba.m.core.domain.workorder.WOTeamMember;
import io.jsonwebtoken.*;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.security.PrivateKey;
import java.security.PublicKey;
import java.time.Duration;
import java.util.Date;

/**
 * author: lyux
 * date: 2023-11-09
 */
@Slf4j
@Service
public class WOMemberAccountService {

    private static final String REGISTER_CACHE_PREFIX = "wo_register_";
    private static final String CHANGE_PWD_CACHE_PREFIX = "wo_change_pwd_";
    private static final String CHANGE_PWD_PAYLOAD_ID = "wo_change_pwd_fake_id";
    private static final Duration REGISTER_TOKEN_EXPIRE = Duration.ofMinutes(120);
    private static final Duration CHANGE_PWD_TOKEN_EXPIRE = Duration.ofMinutes(120);

    private final IUserAccountService accountService;
    private final ISmsService smsService;
    private final WOTeamService teamService;
    private final StringRedisTemplate redisTemplate;
    private final PrivateKey privateKey;
    private final JwtParser parser;

    public WOMemberAccountService(
            IUserAccountService accountService,
            ISmsService smsService,
            WOTeamService teamService,
            StringRedisTemplate redisTemplate
    ) throws Exception {
        this.accountService = accountService;
        this.smsService = smsService;
        this.teamService = teamService;
        this.redisTemplate = redisTemplate;
        this.privateKey = KbJWTKeyProvider
                .getEC256PrivateKeyByResource("act_jwt_cert/jwt_private_pkcs8");
        PublicKey publicKey = KbJWTKeyProvider
                .getEC256PublicKeyByResource("act_jwt_cert/jwt_public_key.pem");
        this.parser = Jwts.parserBuilder()
                .setAllowedClockSkewSeconds(10)
                .setSigningKey(publicKey)
                .build();
    }

    // ----------------------------------------------------
    // 登录逻辑

    public KbEntity<LoginUserModel> loginCmsByAccount(String account, String passwordMd5) {
        return accountService.loginCms(null, account, passwordMd5);
    }

    public KbEntity<LoginUserModel> loginCmsByMobile(String mobile, String vcode) {
        return accountService.loginCmsByMobile(null, mobile, vcode);
    }

    public KbEntity<Void> sendSMSCode(Integer userId, String mobile, SmsCodeMark mark) {
        if (accountService.getUserBasicByMobile(mobile).data().filter(user -> user.getUserId().equals(userId)).isPresent()) {
            return new KbEntity<>(KbCode.REQUEST_IGNORED, "使用用户绑定手机号时不必进行验证", null);
        } else {
            return smsService
                    .sendSmsCode(mobile, 300, mark.getMark())
                    .map(str -> null);
        }
    }

    public boolean verifyVCode(Integer userId, String mobile, SmsCodeMark mark, String vCode) {
        if (accountService.getUserBasicByMobile(mobile).data().filter(user -> user.getUserId().equals(userId)).isPresent()) {
            return true;
        } else if (vCode == null) {
            return false;
        } else {
            return smsService.checkSmsCode(mobile, vCode, mark.getMark()).dataOrThrow();
        }
    }

    // ----------------------------------------------------
    // 注册用户逻辑

    public AccInfo getAccInfoByToken(String token) {
        ActPayload payload = parseJWTOrThrow(token);
        AccInfo info = AccInfo.fromPayload(payload);
        if (info.code == CODE_OK) {
            String cacheKey;
            if (payload.id.equals(CHANGE_PWD_PAYLOAD_ID)) {
                cacheKey = CHANGE_PWD_CACHE_PREFIX + token;
            } else {
                cacheKey = REGISTER_CACHE_PREFIX + token;
            }
            String teamName = redisTemplate.opsForValue().get(cacheKey);
            if (teamName == null) {
                return AccInfo.error(CODE_TOKEN_ABSENT, "[1101] 令牌不存在或已使用, 请联系管理员");
            } else {
                info.setTeamName(teamName);
                return info;
            }
        } else {
            return info;
        }
    }

    public String createRegisterToken(WOTeam team, String account) {
        if (accountService.getUserBasicByLoginName(account).data().isPresent()) {
            throw new KbException(KbCode.RESOURCE_ALREADY_EXIST).r("用户名已存在").li();
        }
        long now = System.currentTimeMillis();
        long expireAt = now + REGISTER_TOKEN_EXPIRE.toMillis();
        String token = Jwts.builder()
                .setId(team.getId())
                .setSubject(account)
                .setIssuedAt(new Date(now))
                .setExpiration(new Date(expireAt))
                .signWith(privateKey, SignatureAlgorithm.ES256)
                .compact();
        String cacheKey = REGISTER_CACHE_PREFIX + token;
        redisTemplate.opsForValue().set(cacheKey, team.getName(), REGISTER_TOKEN_EXPIRE.multipliedBy(2));
        return token;
    }

    public AccInfo registerTeamMember(String token, String passwordMD5, String name, String mobile) {
        ActPayload payload = parseJWTOrThrow(token);
        AccInfo info = AccInfo.fromPayload(payload);
        if (info.code != CODE_OK) {
            return info;
        }
        String cacheKey = REGISTER_CACHE_PREFIX + token;
        String teamName = redisTemplate.opsForValue().get(cacheKey);
        info.setTeamName(teamName);
        if (teamName == null) {
            return AccInfo.error(CODE_TOKEN_ABSENT, "[1002] 令牌不存在或已使用, 请联系管理员");
        }
        redisTemplate.delete(token);
        String teamId = payload.id;
        String account = payload.subject;
        String userMobile = null;
        if (mobile != null && !accountService.getUserBasicByMobile(mobile).data().isPresent()) {
            log.info("register team member, mobile exists or invalid: " + mobile + ", just ignore");
            userMobile = mobile;
        }
        UserModel user = accountService
                .createByAccount(account, passwordMD5, KbProperties.HANGZHOU_SITE_ID, userMobile, name)
                .dataOrThrow();
        WOTeam team = teamService.getTeamById(teamId)
                .orElseThrow(() -> new KbException(KbCode.RESOURCE_NOT_FOUND).li());
        WOTeamMember member = new WOTeamMember();
        member.setMobile(mobile);
        member.setUserId(user.getUserId());
        member.setRealName(name);
        member.setTeamId(teamId);
        teamService.createTeamMember(team, member);
        return info;
    }

    // ----------------------------------------------------
    // 修改密码逻辑

    public String createChangePwdToken(WOTeam team, WOTeamMember member) {
        String account = accountService.getUserBasicById(member.getUserId()).map(UserModel::getLoginName).dataOrThrow();
        long now = System.currentTimeMillis();
        long expireAt = now + REGISTER_TOKEN_EXPIRE.toMillis();
        String token = Jwts.builder()
                .setId(CHANGE_PWD_PAYLOAD_ID)
                .setSubject(account)
                .setIssuedAt(new Date(now))
                .setExpiration(new Date(expireAt))
                .signWith(privateKey, SignatureAlgorithm.ES256)
                .compact();
        String cacheKey = CHANGE_PWD_CACHE_PREFIX + token;
        redisTemplate.opsForValue().set(cacheKey, team.getName(), CHANGE_PWD_TOKEN_EXPIRE.multipliedBy(2));
        return token;
    }

    public AccInfo changeAccountPwd(String token, String passwordMD5) {
        ActPayload payload = parseJWTOrThrow(token);
        if (!CHANGE_PWD_PAYLOAD_ID.equals(payload.id)) {
            return AccInfo.error(CODE_TOKEN_MISUSE, "[1102] 令牌类型错误");
        }
        AccInfo info = AccInfo.fromPayload(payload);
        if (info.code != CODE_OK) {
            return info;
        }
        String cacheKey = CHANGE_PWD_CACHE_PREFIX + token;
        String teamName = redisTemplate.opsForValue().get(cacheKey);
        info.setTeamName(teamName);
        if (teamName == null) {
            return AccInfo.error(CODE_TOKEN_ABSENT, "[1103] 令牌不存在或已使用, 请联系管理员");
        }
        redisTemplate.delete(token);
        String account = payload.subject;
        accountService.changePasswordByAccount(account, passwordMD5).check();
        return info;
    }

    // ----------------------------------------------------

    private ActPayload parseJWTOrThrow(String jwt) {
        Jws<Claims> jws;
        try {
            jws = parser.parseClaimsJws(jwt);
        } catch (ExpiredJwtException expireE) {
            log.debug("token expired. " + expireE.getMessage());
            return new ActPayload(CODE_TOKEN_EXPIRED, "令牌已过期");
        } catch (MalformedJwtException malformedE) {
            log.info("token invalid. " + malformedE.getMessage() + ": " + jwt);
            return new ActPayload(CODE_TOKEN_INVALID, "无效的令牌(1)");
        } catch (InvalidClaimException claimE) {
            log.info("token invalid. " + claimE.getMessage() + ": " + claimE.getClaimName() + " -> " + claimE.getClaimValue());
            return new ActPayload(CODE_TOKEN_INVALID, "无效的令牌(2)");
        }
        Claims claims = jws.getBody();
        String id = claims.getId();
        Date expireAt = claims.getExpiration();
        String subject = claims.getSubject();
        Date issuedAt = claims.getIssuedAt();
        return new ActPayload(id, subject, expireAt.getTime(), issuedAt == null ? 0 : issuedAt.getTime());
    }

    private static class ActPayload {
        /** 解析结果 */
        private final int resultCode;
        /** 解析结果提示 */
        private final String resultMsg;
        /** 对应 jwt accessId */
        private final String id;
        /** 对应 jwt subject, 可为空 */
        private final String subject;
        /** token 过期时间点. 单位毫秒. */
        private final long expireAt;
        /** token 签发时间点. 单位毫秒. */
        private final long issuedAt;

        ActPayload(String id, String subject, long expireAt, long issuedAt) {
            this.id = id;
            this.subject = subject;
            this.expireAt = expireAt;
            this.issuedAt = issuedAt;
            this.resultCode = CODE_OK;
            this.resultMsg = "OK";
        }

        ActPayload(int code, String msg) {
            this.id = null;
            this.subject = null;
            this.expireAt = 0;
            this.issuedAt = 0;
            this.resultCode = code;
            this.resultMsg = msg;
        }
    }

    // ----------------------------------------------------

    private static final int CODE_OK = 0;
    private static final int CODE_TOKEN_ABSENT = 1;
    private static final int CODE_TOKEN_INVALID = 2;
    private static final int CODE_TOKEN_EXPIRED = 3;
    private static final int CODE_TOKEN_MISUSE = 4;

    @Data
    @ToString
    @NoArgsConstructor
    public static class AccInfo {
        private Integer code;
        private String message;
        private String teamName;
        private String account;
        private Long issuedAt;
        private Long expireAt;

        private static AccInfo error(int code, String msg) {
            AccInfo info = new AccInfo();
            info.setCode(code);
            info.setMessage(msg);
            return info;
        }

        private static AccInfo fromPayload(ActPayload payload) {
            AccInfo info = new AccInfo();
            if (payload.resultCode == CODE_OK) {
                info.setAccount(payload.subject);
                info.setIssuedAt(payload.issuedAt);
                info.setExpireAt(payload.expireAt);
                info.setMessage("OK");
                info.setCode(CODE_OK);
                return info;
            } else {
                info.setCode(payload.resultCode);
                info.setMessage(payload.resultMsg);
            }
            return info;
        }
    }

}
