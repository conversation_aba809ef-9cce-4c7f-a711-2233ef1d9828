package com.kaiba.m.core.service.news.pool.knowledge;

import com.kaiba.lib.base.domain.news.pool.knowledge.KnowledgeCateQueryModel;
import com.kaiba.lib.base.domain.news.pool.knowledge.KnowledgeCategoryModel;
import org.springframework.data.domain.Page;

/**
 * Description: 知识库分类Service层查询接口
 * Author: ZM227
 * Date: 2025/2/10 16:32
 */
public interface IKnowledgeCateQueryService {

    Page<KnowledgeCategoryModel> queryKnowledgeCategory(KnowledgeCateQueryModel queryModel);
}
