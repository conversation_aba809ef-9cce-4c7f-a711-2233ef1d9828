package com.kaiba.m.core.service.contact.weight;

import com.kaiba.m.core.domain.contact.Contact;
import com.kaiba.m.core.domain.contact.ContactGroup;
import org.springframework.stereotype.Component;

/**
 * author: lyux
 * date: 2021-12-16
 */
@Component
public class SimpleWeightPolicy implements IWeightPolicy {

    @Override
    public long priority(ContactGroup group) {
        return -1;
    }

    @Override
    public long calculateWeight(Contact contact) {
        if (contact.getRefreshTime() == null) {
            return contact.getCreateTime();
        } else {
            return contact.getRefreshTime();
        }
    }
}
