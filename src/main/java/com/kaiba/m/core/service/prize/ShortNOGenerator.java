package com.kaiba.m.core.service.prize;

import com.kaiba.lib.base.response.KbCode;
import com.kaiba.lib.base.response.KbException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;

import java.util.Calendar;
import java.util.concurrent.TimeUnit;

/**
 * author: lyux
 * date: 20-1-8
 */
@Slf4j
public class ShortNOGenerator {

    private final String cacheKeyPrefix;
    private final StringRedisTemplate redisTemplate;

    public ShortNOGenerator(String cacheKeyPrefix, StringRedisTemplate redisTemplate) {
        this.cacheKeyPrefix = cacheKeyPrefix;
        this.redisTemplate = redisTemplate;
    }

    /**
     * 用以生成一个在时限内尽量不重复的数字序列.
     * 如果所生成的序列在 15 分钟内不超过 999, 则该序会在在8位以内.
     * 目前算法下, 这个承诺不重复的时限为 2 年.
     */
    public String generateNO() {
        Calendar calendar = Calendar.getInstance();
        String part1 = generatePart1(calendar);
        String part2 = generatePart2(calendar);
        String part3 = generatePart3(part2);
        return part1 + part2 + part3;
    }

    /**
     * 用以生成一个在时限内尽量不重复的数字序列.
     * 如果所生成的序列在 15 分钟内不超过 999, 则该序会在在8位以内.
     * 目前算法下, 这个承诺不重复的时限为 2 年.
     */
    public String generateNO(String key) {
        Calendar calendar = Calendar.getInstance();
        String part1 = generatePart1(calendar);
        String part2 = generatePart2(calendar);
        String part3 = generatePart3(part2 + "_" + key);
        return part1 + part2 + part3;
    }

    /**
     * 生成序列的第一部分, 占用 3 位.
     * 以最近的一个偶数年算起, 到当前的天数. 比如:
     * 2020.01.15, 则返回 015.
     * 2021.02.01, 则返回 365 + 32 = 397.
     */
    private static String generatePart1(Calendar calendar) {
        int year = calendar.get(Calendar.YEAR);
        int dayOfYear = calendar.get(Calendar.DAY_OF_YEAR);
        if (year % 2 == 0) {
            return getLengthFixedNo(dayOfYear, 3);
        } else {
            return getLengthFixedNo(365 + dayOfYear, 3);
        }
    }

    /**
     * 生成序列的第二部分, 占用 2 位.
     * 以 15 分钟为间隔, 将一天的 1440 分钟分为 96 份, 返回当前时间在这 96 个区间中的哪一个.
     */
    private static String generatePart2(Calendar calendar) {
        int hour = calendar.get(Calendar.HOUR_OF_DAY);
        int minute = calendar.get(Calendar.MINUTE);
        int minuteOfDay = hour * 60 + minute;
        int no = minuteOfDay / 15;
        return getLengthFixedNo(no, 2);
    }

    /**
     * 生成序列的第三部分, 占用 3 位.
     * 该部分使用 redis 生成一个顺序计数.
     * 该部分为保证序列唯一的最主要部分, 因此少数情况下, 该部分有可能超过规定位数.
     */
    private String generatePart3(String cacheKeySuffix) {
        String cacheKey = cacheKeyPrefix + cacheKeySuffix;
        Long sequence =  redisTemplate.opsForValue().increment(cacheKey, 1);
        if (null == sequence) {
            throw new KbException(KbCode.ILLEGAL_STATE, "fail to generate sequence");
        }
        if (sequence == 1) {
            redisTemplate.expire(cacheKey, 2, TimeUnit.HOURS);
        }
        int no = sequence.intValue();
        return getLengthFixedNo(no, 3);
    }

    private static String getLengthFixedNo(int number, int minDigitCount) {
        return String.format("%0"+minDigitCount+"d", number);
    }

}
