package com.kaiba.m.core.service.banner;

import com.kaiba.lib.base.constant.banner.BannerState;
import com.kaiba.lib.base.domain.urlmap.UrlMapperModel;
import com.kaiba.lib.base.response.KbCode;
import com.kaiba.lib.base.response.KbException;
import com.kaiba.lib.base.service.IUrlMapperService;
import com.kaiba.lib.base.util.appaction.AppActionModel;
import com.kaiba.lib.base.util.appaction.AppActionType;
import com.kaiba.m.core.domain.banner.Banner;
import com.kaiba.m.core.domain.banner.BannerModule;
import com.kaiba.m.core.repository.banner.BannerModuleRepository;
import com.kaiba.m.core.repository.banner.BannerRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.util.*;

@Slf4j
@Service
public class BannerService {

    private final BannerRepository bannerRepository;
    private final BannerModuleRepository bannerModuleRepository;
    private final IUrlMapperService urlMapperService;
    private final String bannerDetailUrl;

    public BannerService(
            @Value("${kaiba.host.page}") String pageHost,
            BannerRepository bannerRepository,
            BannerModuleRepository bannerModuleRepository,
            IUrlMapperService urlMapperService
    ) {
        this.bannerRepository = bannerRepository;
        this.bannerModuleRepository = bannerModuleRepository;
        this.urlMapperService = urlMapperService;
        this.bannerDetailUrl = pageHost + "/banner/detail?bannerId=";
    }

    public BannerModule createBannerModuleByBody(BannerModule bannerModule) {
        if (bannerModule.getId() != null) {
            throw new KbException(KbCode.ILLEGAL_ARGUMENT, "banner module id must be null on create: " + bannerModule).li();
        }
        if (null == bannerModule.getModule()) {
            throw new KbException(KbCode.ILLEGAL_ARGUMENT, "module must not be null on create: " + bannerModule).li();
        }
        bannerModule.setCreateTime(System.currentTimeMillis());
        bannerModule = bannerModuleRepository.insert(bannerModule);
        log.info("bannerModule create: " + bannerModule);
        return bannerModule;
    }

    public BannerModule updateBannerModuleByBody(BannerModule bannerModule) {
        if (null == bannerModule.getId()) {
            throw new KbException(KbCode.RESOURCE_NOT_FOUND, "banner module id null on update: " + bannerModule).li();
        }
        if (null == bannerModule.getModule()) {
            throw new KbException(KbCode.ILLEGAL_ARGUMENT, "module must not be null on update: " + bannerModule).li();
        }
        bannerModule = bannerModuleRepository.updateBannerModule(bannerModule);
        log.info("bannerModule update: " + bannerModule);
        return bannerModule;
    }

    public Banner createBannerByBody(Banner banner) {
        if (banner.getId() != null) {
            throw new KbException(KbCode.ILLEGAL_ARGUMENT, "banner id must be null on create: " + banner).li();
        }
        verifyBannerOnCreate(banner);
        if (null == banner.getCreateTime()) {
            banner.setCreateTime(System.currentTimeMillis());
        }
        if (null == banner.getState()) {
            banner.setState(BannerState.INIT.getValue());
        }
        if (null != banner.getAction() && banner.getAction().equals(AppActionType.PAGE_WEB.getAction())) {
            Map<String, Object> actionParams = banner.getActionParams();
            String originalUrl = actionParams.get("url").toString();
            banner.setOriginalUrl(originalUrl);
            boolean needDecorate = actionParams.get("decorateUrl") == null ? false : (Boolean) actionParams.get("decorateUrl");
            UrlMapperModel urlMapperModel = urlMapperService.createUrlMapper(originalUrl, banner.getSiteId(),
                    needDecorate, null).dataOrThrow();
            banner.setMapperKey(urlMapperModel.getKey());
            actionParams.put("url", urlMapperModel.getMapUrl());
            banner.setActionParams(actionParams);
        }
        banner = bannerRepository.insert(banner);
        log.info("banner create: " + banner);
        if (null == banner.getAction() && null != banner.getContent()) {
            String originalUrl = bannerDetailUrl + banner.getId();
            banner.setOriginalUrl(originalUrl);
            UrlMapperModel urlMapperModel = urlMapperService.createUrlMapper(originalUrl, banner.getSiteId(),
                    false, null).dataOrThrow();
            banner.setMapperKey(urlMapperModel.getKey());
            Map<String, Object> actionParams = new HashMap<>();
            actionParams.put("url", urlMapperModel.getMapUrl());
            banner.setAction(AppActionType.PAGE_WEB.getAction());
            banner.setActionParams(actionParams);
            banner = bannerRepository.updateBanner(banner);
            log.info("banner update: " + banner);
        }
        return banner;
    }

    public Banner updateBannerByBody(Banner banner) {
        if (null == banner.getId()) {
            throw new KbException(KbCode.RESOURCE_NOT_FOUND, "banner id null on update: " + banner).li();
        }
        verifyBannerOnCreate(banner);
        banner = bannerRepository.updateBanner(banner);
        log.info("banner update: " + banner);
        return banner;
    }

    public Banner updateBannerState(String bannerId, BannerState newState, Integer homeOrder) {
        Banner banner = bannerRepository.findById(bannerId).orElseThrow(() ->
                new KbException(KbCode.RESOURCE_NOT_FOUND, "banner is not find: " + bannerId).li());
        log.info("banner state change, id: " + bannerId + ", " + banner.getState() + " -> " + newState.getValue());
        if (banner.getState() != null && banner.getState() == newState.getValue()) {
            return banner;
        }
        BannerState oldState = BannerState.valueOf(banner.getState()).orElseGet(() -> {
            log.error("banner state invalid, assume it is INIT. " + banner);
            return BannerState.INIT;
        });
        switch (oldState) {
            case INIT:
            case SIGNED:
            case PAUSE:
            case ONLINE:
                return bannerRepository.updateBannerStateAndHomeOrder(
                        bannerId, newState.getValue(), homeOrder);
            case OFFLINE:
            default:
                throw new KbException(KbCode.ILLEGAL_STATE, "current state is not be update: " + banner)
                        .setReadableMessage("当前状态不可修改").li();
        }
    }

    public Banner updateBannerHomeOrder(String bannerId, Integer homeOrder) {
        Banner banner = bannerRepository.findById(bannerId).orElseThrow(() ->
                new KbException(KbCode.RESOURCE_NOT_FOUND, "banner is not find: " + bannerId).li());
        List<Integer> states = Arrays.asList(
                BannerState.SIGNED.getValue(),
                BannerState.ONLINE.getValue());
        bannerRepository.findFirstByModuleAndSiteIdAndStateInAndHomeOrderAndEndTimeGreaterThanAndStartTimeLessThan(
                banner.getModule(), banner.getSiteId(), states, homeOrder, banner.getStartTime(), banner.getEndTime())
                .ifPresent(existedBanner -> {
                    throw new KbException(KbCode.RESOURCE_NOT_FOUND, "banner is not be found")
                            .setReadableMessage("当前屏位已占，请查看相同屏位是否有时间重叠").li();
                });
        return bannerRepository.updateBannerHomeOrder(bannerId, homeOrder);
    }

    public Page<Banner> getOnlineBannerList(String module, Integer siteId, Integer page, Integer pageSize) {
        int p = page == null ? 0 : page - 1;
        int ps = pageSize == null || pageSize > 100 ? 15 : pageSize;
        Pageable pageable = PageRequest.of(p, ps);
        long currentTime = System.currentTimeMillis();
        return bannerRepository.getBannerList(
                module, siteId, BannerState.ONLINE.getValue(), currentTime, currentTime, pageable);
    }

    public Page<Banner> getBannerList(List<Integer> states, Integer page, Integer pageSize) {
        int p = page == null ? 0 : page - 1;
        int ps = pageSize == null || pageSize > 100 ? 15 : pageSize;
        Pageable pageable = PageRequest.of(p, ps);
        return bannerRepository.findAllByStateIn(states, pageable);
    }

    public Page<Banner> getBannerList(
            String module, String key, Integer siteId, Integer[] states, Integer homeOrder,
            Long startTime, Long endTime,
            Integer page, Integer pageSize) {
        int p = page == null ? 0 : page - 1;
        int ps = pageSize == null || pageSize > 100 ? 15 : pageSize;
        Pageable pageable = PageRequest.of(p, ps);
        return bannerRepository.getBannerList(module, key, siteId, states, homeOrder, startTime, endTime, pageable);
    }

    public Optional<BannerModule> getBannerModuleById(String bannerModuleId) {
        return bannerModuleRepository.findById(bannerModuleId);
    }

    public Optional<Banner> getBannerById(String bannerId) {
        return bannerRepository.findById(bannerId);
    }

    private void verifyBannerOnCreate(Banner banner) {
        if (banner == null) {
            throw new KbException(KbCode.ILLEGAL_ARGUMENT, "banner should note be null").li();
        }

        if (banner.getSiteId() == null) {
            throw new KbException(KbCode.ILLEGAL_ARGUMENT, "site id should note be null: " + banner)
                    .setReadableMessage("电台不能为空").li();
        }

        if (banner.getTitle() == null) {
            throw new KbException(KbCode.ILLEGAL_ARGUMENT, "title should note be null: " + banner)
                    .setReadableMessage("标题不能为空").li();
        }

        if (banner.getModule() == null) {
            throw new KbException(KbCode.ILLEGAL_ARGUMENT, "module should note be null: " + banner)
                    .setReadableMessage("广告来源不能为空").li();
        }

        if (banner.getStartTime() == null) {
            throw new KbException(KbCode.ILLEGAL_ARGUMENT, "start time should note be null: " + banner)
                    .setReadableMessage("开始时间不能为空").li();
        }

        if (banner.getEndTime() == null) {
            throw new KbException(KbCode.ILLEGAL_ARGUMENT, "end time should note be null: " + banner)
                    .setReadableMessage("结束时间不能为空").li();
        }

        if (banner.getBanner() == null) {
            throw new KbException(KbCode.ILLEGAL_ARGUMENT, "banner should note be null: " + banner)
                    .setReadableMessage("广告图不能为空").li();
        }

        if (banner.getStartTime() > banner.getEndTime()) {
            throw new KbException(KbCode.ILLEGAL_ARGUMENT, "banner start time should be less than end time: " + banner)
                    .setReadableMessage("开始时间不能大于结束时间").li();
        }

        if (banner.getAction() != null) {
            new AppActionModel(banner.getAction(), banner.getActionParams()).validate();
        }
    }

}
