package com.kaiba.m.core.service.shipment;

import com.kaiba.lib.base.assembler.Mapper;
import com.kaiba.lib.base.constant.KbModule;
import com.kaiba.lib.base.domain.shipment.ShipmentModel;
import com.kaiba.lib.base.lang.verifier.F;
import com.kaiba.lib.base.lang.verifier.Verifier;
import com.kaiba.lib.base.lang.verifier.VerifierBuilder;
import com.kaiba.lib.base.middleware.adminlogrecorder.AdminLogAction;
import com.kaiba.lib.base.middleware.adminlogrecorder.AdminLogRecorder;
import com.kaiba.lib.base.response.KbCode;
import com.kaiba.lib.base.response.KbException;
import com.kaiba.lib.base.service.IAdminLogService;
import com.kaiba.m.core.domain.shipment.Shipment;
import com.kaiba.m.core.repository.shipment.ShipmentRepository;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @version ShipmentService, v0.1 2025/3/6 15:55 daopei Exp $
 **/
@Service
public class ShipmentService {

    private final ShipmentRepository shipmentRepository;
    private final AdminLogRecorder adminLogRecorder;

    public ShipmentService(
            ShipmentRepository shipmentRepository,
            IAdminLogService adminLogService
    ) {
        this.shipmentRepository = shipmentRepository;
        this.adminLogRecorder = AdminLogRecorder.builder(adminLogService)
                .module(KbModule.PRIZE)
                .unit("SHIPMENT", "奖品物流信息日志")
                .create();
    }

    public Optional<Shipment> getShipmentById(String shipmentId) {
        return shipmentRepository.findById(shipmentId);
    }

    public List<Shipment> getShipmentByIds(List<String> shipmentIds) {
        return shipmentRepository.findByIdIn(shipmentIds);
    }


    public Shipment createShipment(ShipmentModel model) {
        Shipment shipment = Mapper.map(model, Shipment.class);
        SHIPMENT_VERIFIER.verify(shipment);
        shipment = shipmentRepository.save(shipment);
        adminLogRecorder.on().act(AdminLogAction.CREATE).ref1(shipment.getId()).snapshot(model).add();
        return shipment;
    }

    public Shipment updateShipment(ShipmentModel model) {
        if (model.getId() == null) {
            throw new KbException(KbCode.ILLEGAL_ARGUMENT, "missing shipment id").li();
        }
        Shipment shipment = Mapper.map(model, Shipment.class);
        SHIPMENT_VERIFIER.verify(shipment);
        shipment = shipmentRepository.save(shipment);
        adminLogRecorder.on().act(AdminLogAction.UPDATE).ref1(shipment.getId()).snapshot(model).add();
        return shipment;
    }





    // -----------------------------------------------------------------------------------------------------------------


    static final Verifier<Shipment> SHIPMENT_VERIFIER = new VerifierBuilder<Shipment>()
            .and(F.intF(Shipment::getUserId).notNull())
            .and(F.str(Shipment::getExpressNumber).notNull())
            .and(F.str(Shipment::getConsigneePhone).notNull())
            .create();
}
