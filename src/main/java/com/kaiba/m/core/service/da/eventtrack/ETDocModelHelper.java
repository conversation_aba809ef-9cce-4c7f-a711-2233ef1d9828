package com.kaiba.m.core.service.da.eventtrack;

import com.kaiba.m.core.domain.da.eventtrack.ETDoc;

import java.util.Set;

/**
 * <AUTHOR>
 * @version ETDocModelHelper, v0.1 2025/4/8 10:42 daopei Exp $
 **/
public class ETDocModelHelper {

    public static ETDoc createByAll(String biz, String unit, String ref1, String ref2, String ref3, Integer siteId, String title, String channel, String depart, Set<String> modules) {
        ETDoc doc = new ETDoc();
        doc.setBiz(biz);
        doc.setUnit(unit);
        doc.setRef1(ref1);
        doc.setRef2(ref2);
        doc.setRef3(ref3);
        doc.setSiteId(siteId);
        doc.setTitle(title);
        doc.setChannel(channel);
        doc.setDepart(depart);
        doc.setModules(modules);
        return doc;
    }

    public static ETDoc createByAll(String biz, String unit, String ref1, String ref2, String ref3, Integer siteId, String title, String channel, String depart, Set<String> modules, Long docCreateTime) {
        ETDoc doc = createByAll(biz, unit, ref1, ref2, ref3, siteId, title, channel, depart, modules);
        doc.setDocCreateTime(docCreateTime);
        return doc;
    }
    public static ETDoc createByRef1(String biz, String unit, String ref1, Integer siteId, String title) {
        return createByAll(biz, unit, ref1, null, null, siteId, title, null, null, null);
    }
    public static ETDoc createByRef1(String biz, String ref1, Integer siteId, String title) {
        return createByAll(biz, null, ref1, null, null, siteId, title, null, null, null);
    }


}
