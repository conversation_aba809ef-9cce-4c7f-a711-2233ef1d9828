package com.kaiba.m.core.service.floatview;

import com.kaiba.lib.base.constant.floatview.FloatIconPosition;
import com.kaiba.lib.base.constant.floatview.FloatViewState;
import com.kaiba.lib.base.response.KbCode;
import com.kaiba.lib.base.response.KbException;
import com.kaiba.m.core.domain.floatview.FloatIcon;
import com.kaiba.m.core.repository.floatview.FloatIconRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.util.Optional;

/**
 * author: lyux
 * date: 19-12-25
 */
@Slf4j
@Service
public class FloatIconService {

    private final FloatIconRepository floatIconRepository;

    public FloatIconService(FloatIconRepository floatIconRepository) {
        this.floatIconRepository = floatIconRepository;
    }

    public FloatIcon createFloatIcon(FloatIcon floatIcon) {
        log.info("create float icon: " + floatIcon);
        verifyData(floatIcon);
        long currentTime = System.currentTimeMillis() / 1000;
        floatIcon.setState(FloatViewState.PREPARE.getValue());
        floatIcon.setCreateTime(currentTime);
        floatIcon.setUpdateTime(currentTime);
        return floatIconRepository.insert(floatIcon);
    }

    public FloatIcon updateState(String id, FloatViewState state) {
        FloatIcon floatIcon = floatIconRepository.findById(id).orElseThrow(() ->
                new KbException(KbCode.FLOAT_ICON_NOT_EXISTS, "float icon not found: " + id).li());
        return updateState(floatIcon, state);
    }

    public FloatIcon updateState(FloatIcon floatIcon, FloatViewState state) {
        log.info("update float icon state: " + floatIcon + ", to " + state);
        FloatViewState oldState = FloatViewState.valueOf(floatIcon.getState()).orElseThrow(() ->
                new KbException(KbCode.ILLEGAL_STATE, "float icon state wrong: " + floatIcon));
        if (!oldState.isStateChangeAllowed(state)) {
            throw new KbException(KbCode.ILLEGAL_STATE,
                    "float icon state change not allowed: " + floatIcon + ", to state: " + state).li();
        }
        return floatIconRepository.updateState(floatIcon.getId(), state);
    }

    public void stopOnlineExcept(FloatIcon floatIcon) {
        floatIconRepository.stopOnlineExcept(floatIcon.getSiteId(), floatIcon.getId(), floatIcon.getMark());
    }

    public FloatIcon updateData(FloatIcon floatIcon) {
        log.info("update float icon: " + floatIcon);
        verifyData(floatIcon);
        return floatIconRepository.updateData(floatIcon);
    }

    public void deleteById(String floatIconId) {
        FloatIcon floatIcon = floatIconRepository.findById(floatIconId).orElseThrow(() ->
                new KbException(KbCode.REQUEST_PARAM_INVALID, "float icon not exists: " + floatIconId).ld());
        if (floatIcon.getState() != FloatViewState.PREPARE.getValue()) {
            throw new KbException(KbCode.ILLEGAL_STATE, "delete allowed only for state prepare").ld();
        }
        floatIconRepository.deleteById(floatIconId);
    }

    public Optional<FloatIcon> getById(String id) {
        return floatIconRepository.findById(id);
    }

    public Optional<FloatIcon> getLastBySiteIdOrderByScheduleTime(Integer siteId, FloatViewState state) {
        return floatIconRepository.findFirstBySiteIdAndStateOrderByScheduledStartTimeDesc(siteId, state.getValue());
    }

    public Optional<FloatIcon> getLastBySiteId(Integer siteId, String mark, Integer[] states) {
        if (null == states) {
            if (null == mark) {
                return floatIconRepository.findFirstBySiteIdOrderByUpdateTimeDesc(siteId);
            } else {
                return floatIconRepository.findFirstBySiteIdAndMarkOrderByUpdateTimeDesc(siteId, mark);
            }
        } else {
            if (null == mark) {
                return floatIconRepository.findFirstBySiteIdAndStateInOrderByUpdateTimeDesc(siteId, states);
            } else {
                return floatIconRepository.findFirstBySiteIdAndMarkAndStateInOrderByUpdateTimeDesc(siteId, mark, states);
            }
        }
    }

    Page<FloatIcon> getPageByStates(Integer[] states, Integer page, Integer pageSize) {
        Pageable pageable = createPageable(page, pageSize);
        return floatIconRepository.findByStateInOrderByUpdateTimeDesc(states, pageable);
    }

    public Page<FloatIcon> getPageBySiteId(Integer siteId, String mark, Integer[] states, Integer page, Integer pageSize) {
        Pageable pageable = createPageable(page, pageSize);
        if (null == states) {
            if (null == mark) {
                return floatIconRepository.findBySiteIdOrderByUpdateTimeDesc(siteId, pageable);
            } else {
                return floatIconRepository.findBySiteIdAndMarkOrderByUpdateTimeDesc(siteId, mark, pageable);
            }
        } else {
            if (null == mark) {
                return floatIconRepository.findBySiteIdAndStateInOrderByUpdateTimeDesc(siteId, states, pageable);
            } else {
                return floatIconRepository.findBySiteIdAndMarkAndStateInOrderByUpdateTimeDesc(siteId, mark, states, pageable);
            }
        }
    }

    private static void verifyData(FloatIcon floatIcon) {
        if (floatIcon.getSiteId() == null) {
            throw new KbException(KbCode.ILLEGAL_ARGUMENT, "need siteId. " + floatIcon).li();
        }
        if (floatIcon.getMark() == null) {
            throw new KbException(KbCode.ILLEGAL_ARGUMENT, "need mark. " + floatIcon).li();
        }
        if (floatIcon.getIcon() == null) {
            throw new KbException(KbCode.ILLEGAL_ARGUMENT, "need an icon. " + floatIcon).li();
        }
        if (floatIcon.getAction() == null) {
            throw new KbException(KbCode.ILLEGAL_ARGUMENT, "need action. " + floatIcon).li();
        }
        if (floatIcon.getPosition() == null) {
            floatIcon.setPosition(FloatIconPosition.LEFT_BOTTOM.getPosition());
        } else {
            FloatIconPosition.valueOfPosition(floatIcon.getPosition()).orElseThrow(() ->
                    new KbException(KbCode.ILLEGAL_ARGUMENT, "wrong position value. " + floatIcon).li());
        }
        if (floatIcon.getSizeRatio() == null) {
            floatIcon.setSizeRatio(20);
        } else if (floatIcon.getSizeRatio() < 1 || floatIcon.getSizeRatio() > 100) {
            throw new KbException(KbCode.ILLEGAL_ARGUMENT, "sizeRatio should be [1, 100]. " + floatIcon).li();
        }
        if (floatIcon.getEnableDrag() == null) {
            floatIcon.setEnableDrag(true);
        }
        if (floatIcon.getOnlyForLogin() == null) {
            floatIcon.setOnlyForLogin(false);
        }
        if (floatIcon.getScheduledEndTime() == null) {
            throw new KbException(KbCode.ILLEGAL_ARGUMENT, "need endTime. " + floatIcon).li();
        }
        long current = System.currentTimeMillis() / 1000;
        if (floatIcon.getScheduledEndTime() <= current) {
            throw new KbException(KbCode.ILLEGAL_ARGUMENT, "endTime should be greater then current timestamp. " + floatIcon).li();
        }
        if (floatIcon.getScheduledStartTime() != null) {
            if (floatIcon.getScheduledStartTime() > floatIcon.getScheduledEndTime()) {
                throw new KbException(KbCode.ILLEGAL_ARGUMENT, "endTime should be greater then startTime. " + floatIcon).li();
            }
        }
    }

    private static Pageable createPageable(Integer page, Integer pageSize) {
        int p = (page == null || page < 1) ? 0 : page - 1;
        int ps = (pageSize == null || pageSize > 100) ? 15 : pageSize;
        return PageRequest.of(p, ps);
    }

}
