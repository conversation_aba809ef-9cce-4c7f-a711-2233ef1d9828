package com.kaiba.m.core.service.push.agent.gt;

import com.getui.push.v2.sdk.api.PushApi;
import com.getui.push.v2.sdk.common.ApiResult;
import com.getui.push.v2.sdk.dto.CommonEnum;
import com.getui.push.v2.sdk.dto.req.Audience;
import com.getui.push.v2.sdk.dto.req.Condition;
import com.getui.push.v2.sdk.dto.req.message.PushDTO;
import com.getui.push.v2.sdk.dto.res.TaskIdDTO;
import com.kaiba.lib.base.constant.push.PushRange;
import com.kaiba.lib.base.domain.push.PushModel;
import com.kaiba.m.core.domain.push.PushTarget;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @version GTPushTagStrategy, v0.1 2024/2/2 14:00 daopei Exp $
 **/
@Service
public class GTPushTagStrategy implements GTPushStrategy {

    @Override
    public boolean support(PushRange range) {
        return range == PushRange.SOURCE || range == PushRange.SITE;
    }

    @Override
    public GTPushResult execute(PushApi pushApi, PushModel model, List<PushTarget> targetList) {
        Audience audience = audience(model);
        PushDTO<Audience> pushDTO = new PushDTO<>();
        GTPushHelper.wrapperRequest(pushDTO, model, audience);
        ApiResult<TaskIdDTO> result = pushApi.pushByTag(pushDTO);
        String taskId = GTPushHelper.parseTaskId(result);
        return GTPushHelper.map2Result(result, taskId);
    }


    private Audience audience(PushModel model) {
        PushRange range = PushRange.valueOf(model.getRange()).orElse(null);
        Audience audience = new Audience();
        if (range == PushRange.SOURCE) {
            int source = model.targetIdAsInteger(0);
            Condition condition = new Condition();
            condition.setKey("custom_tag");
            condition.addValue("source_" + source);
            condition.setOptType(CommonEnum.OptTypeEnum.TYPE_AND.type);
            audience.addCondition(condition);
            return audience;
        }
        if (range == PushRange.SITE) {
            int siteId = model.targetIdAsInteger(0);
            Condition siteCondition = new Condition();
            siteCondition.setKey("custom_tag");
            siteCondition.addValue("siteId_" + siteId);
            siteCondition.setOptType(CommonEnum.OptTypeEnum.TYPE_AND.type);
            audience.addCondition(siteCondition);
            return audience;
        }
        return null;
    }
}
