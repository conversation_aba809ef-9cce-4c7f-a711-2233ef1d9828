package com.kaiba.m.core.service.opensearch.query;

import com.kaiba.lib.base.response.KbCode;
import com.kaiba.lib.base.response.KbException;
import com.kaiba.lib.base.util.StringUtils;
import com.kaiba.m.core.constant.opensearch.SearchIndexEnum;

import java.util.ArrayList;
import java.util.List;

/**
 * 搜索查询
 * <AUTHOR>
 * @date 2023/08/03 15:37
 **/
public class SearchQuery {

    private static final String DELIMITER = " AND ";
    private final List<String> list = new ArrayList<>(4);

    public static SearchQuery on() {
        return new SearchQuery();
    }

    public SearchQuery searchQuery(String query, Boolean isManager) {
        if(query == null || StringUtils.isEmpty(query.trim())) {
            return this;
        }

        String index = Boolean.TRUE.equals(isManager) ?
                SearchIndexEnum.DEFAULT_HIDDEN.getIndex() :
                SearchIndexEnum.DEFAULT.getIndex();
        list.add(index + "'" + query + "'");
        return this;
    }

    public SearchQuery searchId(String query) {
        if(query == null || StringUtils.isEmpty(query.trim())) {
            return this;
        }

        list.add(SearchIndexEnum.ID.getIndex() + "'" + query + "'");
        return this;
    }

    public SearchQuery searchUser(String query) {
        if(query == null || StringUtils.isEmpty(query.trim())) {
            return this;
        }

        list.add(SearchIndexEnum.USER.getIndex() + "'" + query + "'");
        return this;
    }

    public SearchQuery searchArray(String element) {
        if(StringUtils.isEmpty(element)) {
            return this;
        }

        list.add(SearchIndexEnum.ARRAY.getIndex() + "'" + element + "'");
        return this;
    }

    public SearchQuery searchTime(Long start, Long end) {
        if(start == null || end == null) {
            return this;
        }

        list.add(SearchIndexEnum.TIME.getIndex() + "[" + start + ", " + end + "]");
        return this;
    }

    public String create() {
        String queryParam = String.join(DELIMITER, list);
        if(StringUtils.isEmpty(queryParam)) {
            throw new KbException(KbCode.REQUEST_PARAM_MISSING)
                .setReadableMessage("搜索必须包含关键词、时间、模块中的至少一项").li();
        }
        return queryParam;
    }
}
