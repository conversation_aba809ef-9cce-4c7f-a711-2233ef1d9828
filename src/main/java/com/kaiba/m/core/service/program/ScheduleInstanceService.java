package com.kaiba.m.core.service.program;

import com.google.common.collect.Lists;
import com.kaiba.lib.base.constant.program.RebroadcastType;
import com.kaiba.lib.base.response.KbCode;
import com.kaiba.lib.base.response.KbException;
import com.kaiba.lib.base.util.GsonUtils;
import com.kaiba.lib.base.util.StringUtils;
import com.kaiba.m.core.domain.program.ScheduleInstance;
import com.kaiba.m.core.repository.program.ScheduleInstanceRepository;
import com.kaiba.m.core.service.program.rebroadcast.IRebroadcastHandle;
import com.kaiba.m.core.service.program.rebroadcast.RebroadcastFactory;
import lombok.extern.slf4j.Slf4j;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * author wangsj
 * date 2020-09-01
 */
@Slf4j
@Service
public class ScheduleInstanceService {

    private final ScheduleInstanceRepository instanceRepository;
    private final ScheduleInstanceCacheService instanceCacheService;
    private final RebroadcastFactory rebroadcastFactory;

    public ScheduleInstanceService(ScheduleInstanceRepository instanceRepository, ScheduleInstanceCacheService instanceCacheService
            , RebroadcastFactory rebroadcastFactory) {
        this.instanceRepository = instanceRepository;
        this.instanceCacheService = instanceCacheService;
        this.rebroadcastFactory = rebroadcastFactory;
    }

    public void createOneDayInstance(List<ScheduleInstance> instanceList) {
        validateOneDayInstanceList(instanceList);

        List<ScheduleInstance> broadcastList = new ArrayList<>(instanceList.size());
        List<ScheduleInstance> rebroadcastList = new ArrayList<>(3);
        for (ScheduleInstance instance : instanceList) {
            if (instance.getRebroadcast()) {
                rebroadcastList.add(instance);
            } else {
                broadcastList.add(instance);
            }
        }
        instanceRepository.saveAll(broadcastList);

        generateRebroadcast(rebroadcastList);
        instanceRepository.saveAll(rebroadcastList);
    }

    public void upsertInstanceList(List<ScheduleInstance> newList) {
        Long now = System.currentTimeMillis() / 1000;
        Integer siteId = newList.get(0).getSiteId();
        int[] day = calculateDay(newList.get(0).getStart());

        List<ScheduleInstance> fromDBList = instanceRepository.findAllBySiteIdAndStartBetween(siteId, day[0], day[1]);
        List<ScheduleInstance> toDBList = Lists.newLinkedList(fromDBList);
        Map<String, ScheduleInstance> newMap = newList.stream()
                .filter(t->!StringUtils.isEmpty(t.getId()))
                .collect(Collectors.toMap(ScheduleInstance::getId,t->t));
        Set<String> toDeleteIds=new HashSet<>();
        //删除新列表中不存在的排班
        toDBList.removeIf(instance->{
            if (Objects.isNull(newMap.get(instance.getId()))){
                toDeleteIds.add(instance.getId());
                return true;
            }else{
                return false;
            }
        });
        //修改两边列表都存在的元素
        toDBList.forEach(instance -> {
            ScheduleInstance changed = newMap.get(instance.getId());
            instance.setStart(changed.getStart());
            instance.setEnd(changed.getEnd());
            instance.setEmceeId(changed.getEmceeId());
            instance.setUpdateUser(changed.getUpdateUser());
            instance.setUpdateTime(now);
            instance.setIsDefault(false);
            instance.setIsEmpty(changed.getIsEmpty());
        });
        //添加新增元素
        toDBList.addAll(
                newList.stream()
                .filter(instance->StringUtils.isEmpty(instance.getId()))
                .peek(instance->{
                    instance.setIsDefault(false);
                    instance.setUpdateTime(now);
                    instance.setCreateUser(instance.getUpdateUser());
                    instance.setCreateTime(now);
                }).collect(Collectors.toList())
        );
        validateOneDayInstanceList(toDBList);

        log.info("upsertInstanceList. toDeleteIds:{}, toDBList:{}, fromDBList:{}", GsonUtils.getGson().toJson(toDeleteIds), GsonUtils.getGson().toJson(toDBList), GsonUtils.getGson().toJson(fromDBList));
        instanceRepository.deleteAllByIdIn(toDeleteIds);
        instanceRepository.saveAll(toDBList);
        instanceCacheService.deleteSiteTodayList(toDBList.get(0).getSiteId());
    }

    public void createOrEditInstanceItem(ScheduleInstance instance) {
        if (!instanceRepository.findOverlapped(instance.getSiteId(), instance.getStart(), instance.getEnd(), instance.getId())) {
            instanceRepository.save(instance);
            instanceCacheService.deleteInstance(instance.getId(), instance.getSiteId());
        } else {
            throw new KbException(KbCode.SCHEDULE_OVERLAPPED);
        }
    }

    public void deleteTodayScheduleCache(Integer siteId) {
        instanceCacheService.deleteSiteTodayList(siteId);
    }

    public void deleteInstanceItem(String id, Integer siteId) {
        instanceRepository.deleteById(id);
        instanceCacheService.deleteInstance(id, siteId);
    }

    public void deleteOneDayInstance(Integer siteId, Long day) {
        int[] range = calculateDay(day);
        log.info("deleteOneDayInstance execute inner. range:{}", range);
        instanceRepository.deleteAllBySiteIdAndStartBetween(siteId, range[0], range[1]);
    }

    public void save(ScheduleInstance instance) {
        instanceRepository.save(instance);
        instanceCacheService.deleteInstance(instance.getId(), instance.getSiteId());
    }

    public void updateReboradcast(List<ScheduleInstance> list) {
        instanceRepository.saveAll(list);
        list.forEach(instanceCacheService::setInstance);
    }

    public Optional<ScheduleInstance> getDetailById(String id) {
        Integer view = instanceCacheService.view(id);
        Optional<ScheduleInstance> cache = instanceCacheService.getInstance(id);
        if (!cache.isPresent()) {
            Optional<ScheduleInstance> instance = instanceRepository.findById(id);
            instance.ifPresent(t -> {
                Integer praise = instanceCacheService.getPraiseCount(t.getId());
                if (t.getView() == null || view > t.getView()) {
                    t.setView(t.getView());
                }
                if (t.getPraise() == null || praise > t.getPraise()) {
                    t.setPraise(praise);
                }
                instanceCacheService.setInstance(t);
            });
            return instance;
        } else {
            cache.ifPresent(t -> t.setView(view));
            return cache;
        }
    }

    public Optional<ScheduleInstance> getCurrentSchedule(Integer siteId) {
        long now = System.currentTimeMillis() / 1000;
        Optional<ScheduleInstance> optional = instanceRepository.findFirstBySiteIdAndStartLessThanEqualAndEndGreaterThan(siteId, now, now);
        if (optional.isPresent()) {
            return optional;
        }
        long[] today = ScheduleInstanceCacheService.todayStartAndEnd();
        List<ScheduleInstance> list = instanceRepository.findAllBySiteIdAndStartBetween(siteId, (int) today[0], (int) today[1]);
        if (list == null || list.isEmpty()) {
            return Optional.empty();
        }
        return Optional.of(now < list.get(0).getStart() ? list.get(0) : list.get(list.size() - 1));
    }

    public List<ScheduleInstance> getTodayListBySiteId(Integer siteId) {
        List<ScheduleInstance> list = instanceCacheService.getTodayListBySiteId(siteId);
        if (list.size() == 0) {
            long[] today = ScheduleInstanceCacheService.todayStartAndEnd();
            list = instanceRepository.findAllBySiteIdAndStartBetween(siteId, (int) today[0], (int) today[1]);
            if (list.size() == 0) {
                log.error("site without a valid schedule instance list: " + siteId);
            } else {
                instanceCacheService.setSiteTodayList(list);
            }
        }
        return list;
    }

    public List<ScheduleInstance> getTodayRebroadcast() {
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        int start = (int) (calendar.getTimeInMillis() / 1000);
        return instanceRepository.findAllByStartBetweenAndRebroadcastIsTrue(start, start + 86400);
    }

    public Integer praise(String id, Integer count) {
        count = count > 15 ? 15 : count;
        return instanceCacheService.praise(id, count);
    }

    public Integer getPraiseCount(String id) {
        Integer praise = instanceCacheService.getPraiseCount(id);
        if (praise < 0) {
            Optional<ScheduleInstance> optional = instanceRepository.findById(id);
            if (optional.isPresent()) {
                Integer count = optional.get().getPraise() == null ? 0 : optional.get().getPraise();
                instanceCacheService.setPraise(id, count);
                return count;
            } else {
                instanceCacheService.setPraise(id, 0);
                return 0;
            }
        }
        return praise;
    }

    public Integer getViewCount(String id) {
        Integer view = instanceCacheService.getViewCount(id);
        if (view < 0) {
            Optional<ScheduleInstance> optional = instanceRepository.findById(id);
            if (optional.isPresent()) {
                Integer count = optional.get().getView() == null ? 0 : optional.get().getView();
                instanceCacheService.setView(id, count);
                return count;
            } else {
                instanceCacheService.setView(id, 0);
                return 0;
            }
        }
        return view;
    }

    public void cachePersist() {
        List<String> idList = new ArrayList<>(instanceCacheService.getChangedId());
        int limit = 500;
        for (int start = 0, end = limit; start < idList.size(); start += limit, end += limit) {
            end = Math.min(idList.size(), end);
            List<String> subIdList = idList.subList(start, end);
            List<ScheduleInstance> instanceList = instanceRepository.findAllByIdIn(subIdList);
            Map<String, Integer> praiseMap = instanceCacheService.getPraiseCountByIds(subIdList);
            Map<String, Integer> viewMap = instanceCacheService.getViewCountByIds(subIdList);
            instanceList.forEach(t -> {
                if (viewMap.get(t.getId()) > t.getView()) {
                    t.setView(viewMap.get(t.getId()));
                }
                if (praiseMap.get(t.getId()) > t.getPraise()) {
                    t.setPraise(praiseMap.get(t.getId()));
                }
            });
            instanceRepository.saveAll(instanceList);
            instanceCacheService.removeChangedId(subIdList);
        }
    }

    public List<ScheduleInstance> getListByStart(int startTime, int endTime) {
        return instanceRepository.findAllByStartBetween(startTime, endTime);
    }
    public List<ScheduleInstance> getListBySiteId(Integer siteId, int startTime, int endTime) {
        return instanceRepository.findAllBySiteIdAndStartBetween(siteId, startTime, endTime);
    }

    public List<ScheduleInstance> getListByProgramId(String programId, int startTime, int endTime) {
        return instanceRepository.findAllByProgramIdAndStartBetween(programId, startTime, endTime);
    }

    public Page<ScheduleInstance> getPageByTime(int startTime, int endTime, Integer page, Integer pageSize) {
        int p = page == null ? 0 : page - 1;
        int ps = pageSize == null ? 20 : pageSize;
        return instanceRepository.findAllByStartBetween(startTime, endTime, PageRequest.of(p, ps));
    }

    public List<ScheduleInstance> getListByTime(int startTime, int endTime, Integer page, Integer pageSize) {
        int p = page == null ? 0 : page - 1;
        int ps = pageSize == null ? 20 : pageSize;
        return instanceRepository.getAllByStartBetween(startTime, endTime, PageRequest.of(p, ps));
    }

    public List<ScheduleInstance> getListByIdIn(Collection<String> ids) {
        return instanceRepository.findAllByIdIn(ids);
    }

    public List<ScheduleInstance> getDistinctByStartBetween(int startTime, int endTime) {
        return instanceRepository.findDistinctByStartBetween(startTime, endTime);
    }

    public boolean existBySiteIdAndTimeBetween(Integer siteId, int start, int end) {
        return instanceRepository.existsBySiteIdAndStartBetween(siteId, start, end);
    }

    public boolean existByProgramIdAndStartGreaterThan(String programId, int start) {
        return instanceRepository.existsByProgramIdAndStartGreaterThan(programId, start);
    }

    public boolean existByEmceeIdAndStartGreaterThan(String emceeId, int start) {
        return instanceRepository.existsByEmceeIdAndStartGreaterThan(emceeId, start);
    }

    public boolean existByThreadId(String threadId) {
        return instanceRepository.existsByThreadId(threadId);
    }

    public Integer[] checkPreAndNext(String id, String programId, int start) {
        int pI, nI;
        Integer value = instanceCacheService.getPreNextById(id);
        if (value < 0) {
            boolean pre = instanceRepository.existsByProgramIdAndStartLessThan(programId, start);
            boolean next = instanceRepository.existsByProgramIdAndStartGreaterThan(programId, start);
            pI = pre ? 1 : 0;
            nI = next ? 1 : 0;
            instanceCacheService.setPreNextById(id, pI, nI);
        } else {
            pI = value / 10;
            nI = value % 10;
        }
        return new Integer[]{pI, nI};
    }

    public Optional<ScheduleInstance> getLastByProgramId(String programId) {
        return instanceRepository.findFirstByProgramIdOrderByStartDesc(programId);
    }

    public Optional<ScheduleInstance> getLastBroadcastByProgramIdUntil(String programId, Integer time) {
        return instanceRepository.findFirstByProgramIdAndStartLessThanAndRebroadcastIsFalseOrderByStartDesc(programId, time);
    }

    public Optional<ScheduleInstance> getLastByProgramIdUntil(String programId, Integer time) {
        return instanceRepository.findFirstByProgramIdAndStartLessThanOrderByStartDesc(programId, time);
    }

    public Optional<ScheduleInstance> getFirstByProgramIdSince(String programId, Integer start) {
        return instanceRepository.findFirstByProgramIdAndStartGreaterThanEqualOrderByStart(programId, start);
    }

    public void clockOn(String id, List<String> emceeList) {
        ScheduleInstance instance = instanceRepository.findById(id).orElseThrow(()->new KbException(KbCode.RESOURCE_NOT_FOUND).li());
        if (instance.getEnd() < System.currentTimeMillis() / 1000) {
            throw new KbException(KbCode.ILLEGAL_STATE).li().setReadableMessage("本期节目已结束，禁止修改");
        }
        instance.setEmceeId(emceeList.stream().distinct().collect(Collectors.toList()));
        instanceRepository.save(instance);
        instanceCacheService.setInstance(instance);
    }

    public static int[] calculateDay(long timestamp) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTimeInMillis(timestamp * 1000);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        int dayStart = (int) (calendar.getTimeInMillis() / 1000);
        int dayEnd = dayStart + 86400;
        int dayOfWeek = calendar.get(Calendar.DAY_OF_WEEK) - 1;
        dayOfWeek = dayOfWeek == 0 ? 7 : dayOfWeek;
        return new int[]{dayStart, dayEnd, dayOfWeek};
    }

    private void validateOneDayInstanceList(List<ScheduleInstance> instanceList) {
        if (!instanceList.isEmpty()) {
            instanceList.sort(Comparator.comparing(ScheduleInstance::getStart));
            int[] day1 = calculateDay(instanceList.get(0).getStart());
            int[] day2 = calculateDay(instanceList.get(instanceList.size()-1).getEnd()-1);
            if (day1[0]!=day2[0]){
                throw new KbException(KbCode.REQUEST_PARAM_INVALID, "不是同一天的排班");
            }
            for (int i = 0; i < instanceList.size(); i++) {
                ScheduleInstance schedule = instanceList.get(i);
                Integer start = (i == instanceList.size() - 1) ? Integer.MAX_VALUE : instanceList.get(i + 1).getStart();
                if (schedule.getEnd().compareTo(start) > 0) {
                    log.info("overlapped siteId : " + schedule.getSiteId() + " start : " + schedule.getStart());
                    throw new KbException(KbCode.SCHEDULE_OVERLAPPED);
                }
                if (!schedule.getRebroadcast() && schedule.getEmceeId().isEmpty()) {
                    log.info("emcee empty siteId : " + schedule.getSiteId() + " start : " + schedule.getStart());
                    throw new KbException(KbCode.REQUEST_PARAM_INVALID, "emceeId is Empty");
                }
                if (StringUtils.isEmpty(schedule.getProgramId())) {
                    log.info("program empty siteId : " + schedule.getSiteId() + " start : " + schedule.getStart());
                    throw new KbException(KbCode.REQUEST_PARAM_INVALID, "programId is Empty");
                }

                // TODO: 2023-12-29
                // 杭州台话里话外节目有两个金主赞助, 因此要求在不同时段节目标题显示为两个, 但节目互动区期望还是同一个.
                ScheduleInstance previous = i == 0 ? null : instanceList.get(i - 1);
                if (previous != null
                        && "5aeea44f43bf713c915de4fe".equals(previous.getProgramId()) // 话里话外
                        && "658a484100eddc41b1da251f".equals(schedule.getProgramId())) // 针锋相对
                {
                    schedule.setThreadId(previous.getThreadId());
                }
            }
        }
    }

    private void generateRebroadcast(List<ScheduleInstance> list) {
        list.forEach(rebroadcast -> {
            RebroadcastType rebroadcastType = RebroadcastType.valueOfType(rebroadcast.getRebroadcastType())
                    .orElse(RebroadcastType.PREVIOUS_ONE);
            IRebroadcastHandle handle = rebroadcastFactory.getRebroadcast(rebroadcastType);
            ScheduleInstance broadcast = handle.getBroadcastInstance(
                    this, rebroadcast.getProgramId(), rebroadcast.getStart())
                    .orElseThrow(() -> new KbException(KbCode.RESOURCE_NOT_FOUND,
                            "rebroadcast error,programId: " + rebroadcast.getProgramId()
                                    + " siteId: " + rebroadcast.getSiteId() + " start: " + rebroadcast.getStart()));
            rebroadcast.setRebroadcastId(broadcast.getId());
            rebroadcast.setThreadId(broadcast.getThreadId());
            rebroadcast.setBoardThreadId(broadcast.getBoardThreadId());
            rebroadcast.setBroadcastThreadId(broadcast.getBroadcastThreadId());
            rebroadcast.setRewardThreadId(broadcast.getRewardThreadId());
        });
    }
}
