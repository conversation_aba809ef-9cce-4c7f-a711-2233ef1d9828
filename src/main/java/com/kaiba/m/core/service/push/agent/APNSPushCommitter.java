package com.kaiba.m.core.service.push.agent;

import com.eatthepath.pushy.apns.ApnsClient;
import com.eatthepath.pushy.apns.PushNotificationResponse;
import com.eatthepath.pushy.apns.util.SimpleApnsPushNotification;
import com.eatthepath.pushy.apns.util.TokenUtil;
import com.kaiba.lib.base.constant.KbEndpoint;
import com.kaiba.lib.base.constant.KbPushChannel;
import com.kaiba.lib.base.domain.push.PushModel;
import com.kaiba.lib.base.response.KbCode;
import com.kaiba.lib.base.response.KbException;
import com.kaiba.lib.base.util.GsonUtils;
import com.kaiba.lib.base.util.StringUtils;
import com.kaiba.m.core.domain.push.PushTarget;
import com.kaiba.m.core.service.push.agent.ios.IOSPayloadBuilder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;

import java.util.Collection;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Semaphore;
import java.util.concurrent.TimeUnit;

/**
 * author: lyux
 * date: 2023-09-26
 */
@Slf4j
public class APNSPushCommitter {

    private final ApnsClient apnsClient;
    private final Semaphore semaphore;
    private static boolean FIRST_RUN = true;


    public APNSPushCommitter(ApnsClient apnsClient) {
        this.apnsClient = apnsClient;
        this.semaphore = new Semaphore(10_000);
    }

    @Async
    public void doPushAsync(PushModel model, Collection<PushTarget> targets){
        targets.forEach(target-> doPush(model, target, null));
    }

    public void doPush(PushModel model, PushTarget pushTarget, IOSPushAgent.BadTokenCallback c) {
        if (StringUtils.isEmpty(model.getSubTitle())){
            model.setSubTitle(model.getTitle());
        }
        if (StringUtils.isEmpty(pushTarget.getPushToken())) {
            log.warn("no valid ios device token for " + model);
            return;
        }
        // 极光渠道包含自推送的IOS渠道
        if (pushTarget.getPushChannel() != null && pushTarget.getPushChannel() != KbPushChannel.JIGUANG.getValue()) {
            log.info("user push not support channel " + pushTarget);
            return;
        }
        final String token = TokenUtil.sanitizeTokenString(pushTarget.getPushToken());
        if (StringUtils.isEmpty(token)) {
            log.warn("no valid apns device token for " + model);
            return;
        }
        final KbEndpoint endpoint = KbEndpoint
                .valueOf(model.getEndpoint())
                .orElseThrow(() -> new KbException(KbCode.ILLEGAL_ARGUMENT));

        final String payload = new IOSPayloadBuilder()
                .setAlertTitle(model.getTitle())
                .setSound("default")
                .setAlertBody(model.getSubTitle())
                .addCustomProperty("type", "6") //此处主要为了兼容老版本推送的type设置，后续取消掉
                .addCustomProperty("action", model.getAction())
                .addCustomProperty("actionParams", model.getActionParams())
                .setCustomPropertyInAps(true)
                .buildWithDefaultMaximumLength();

        try {
            if (semaphore.tryAcquire(30, TimeUnit.SECONDS)) {
                SimpleApnsPushNotification n = new SimpleApnsPushNotification(token, endpoint.getIosBundleId(), payload);
                final CompletableFuture<PushNotificationResponse<SimpleApnsPushNotification>> future = apnsClient
                        .sendNotification(n)
                        .whenComplete((response, cause) -> {
                            if (c != null
                                    && !response.isAccepted()
                                    && "BadDeviceToken".equals(response.getRejectionReason())) {
                                c.onBadToken(response.getPushNotification().getToken());
                            }
                            semaphore.release();
                        });
                if (FIRST_RUN){
                    // SimpleApnsPushNotification 有一定概率在首次推送时阻塞, 已向作者提起 issue.
                    // 经测试发现 future.get() 可以解决该问题, 故有此 workaround.
                    FIRST_RUN = false;
                    try {
                        future.get();
                    } catch (InterruptedException | ExecutionException ignore) {
                    }
                }
            } else {
                log.error("push ios blocked for more than 1 minute");
            }
        } catch (InterruptedException ignore) {
        }
    }

}
