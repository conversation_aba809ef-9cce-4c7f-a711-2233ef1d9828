package com.kaiba.m.core.service.da.sensors;

import com.kaiba.lib.base.assembler.Mapper;
import com.kaiba.lib.base.constant.da.sensors.KanbanAggrType;
import com.kaiba.lib.base.constant.da.sensors.KanbanColDataType;
import com.kaiba.lib.base.domain.da.sensors.kanban.KanbanColumn;
import com.kaiba.lib.base.domain.da.sensors.kanban.KanbanConfigModel;
import com.kaiba.lib.base.domain.da.sensors.kanban.KanbanDataModel;
import com.kaiba.lib.base.response.KbCode;
import com.kaiba.lib.base.response.KbException;
import com.kaiba.m.core.domain.da.sensors.kanban.ActDivData;
import com.kaiba.m.core.domain.da.sensors.kanban.DivData;
import com.kaiba.m.core.domain.da.sensors.kanban.KaibaDivData;
import com.kaiba.m.core.domain.da.sensors.kanban.PGCDivData;
import com.kaiba.m.core.domain.da.sensors.kanban.PGCDivDataBase;
import com.kaiba.m.core.domain.da.sensors.kanban.PageDivData;
import com.kaiba.m.core.domain.da.sensors.kanban.PlatformDivData;
import com.kaiba.m.core.repository.da.sensors.kanban.ActDivDataRepository;
import com.kaiba.m.core.repository.da.sensors.kanban.KaibaDataRepository;
import com.kaiba.m.core.repository.da.sensors.kanban.PGCDivDataRepository;
import com.kaiba.m.core.repository.da.sensors.kanban.PageDivDataRepository;
import com.kaiba.m.core.repository.da.sensors.kanban.PlatformDataRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/02/28 14:20
 */
@Slf4j
@Service
public class SensorsKanbanDataService {

    private final ActDivDataRepository actDataRepository;
    private final PageDivDataRepository pageDataRepository;
    private final PGCDivDataRepository pgcDataRepository;
    private final PlatformDataRepository platformDataRepository;

    private final KaibaDataRepository kaibaDataRepository;

    public SensorsKanbanDataService(
            ActDivDataRepository actDataRepository,
            PageDivDataRepository pageDataRepository,
            PGCDivDataRepository pgcDataRepository,
            PlatformDataRepository platformDataRepository,
            KaibaDataRepository kaibaDataRepository
    ) {
        this.actDataRepository = actDataRepository;
        this.pageDataRepository = pageDataRepository;
        this.pgcDataRepository = pgcDataRepository;
        this.platformDataRepository = platformDataRepository;
        this.kaibaDataRepository = kaibaDataRepository;
    }

    public void saveActData(ActDivData data) {
        actDataRepository.findByConfigIdAndSt(data.getConfigId(), data.getSt())
                .ifPresent(oldData -> data.setId(oldData.getId()));
        actDataRepository.save(data);
    }

    public void savePageData(PageDivData data) {
        pageDataRepository.findByConfigIdAndSt(data.getConfigId(), data.getSt())
                .ifPresent(oldData -> data.setId(oldData.getId()));
        pageDataRepository.save(data);
    }

    public void savePGCData(PGCDivData data) {
        pageDataRepository.findByConfigIdAndSt(data.getConfigId(), data.getSt())
                .ifPresent(oldData -> data.setId(oldData.getId()));
        pgcDataRepository.save(data);
    }

    public void savePlatformData(PlatformDivData data) {
        platformDataRepository.findByConfigIdAndSt(data.getConfigId(), data.getSt())
                .ifPresent(oldData -> data.setId(oldData.getId()));
        platformDataRepository.save(data);
    }

    public void saveKaibaData(KaibaDivData data) {
        kaibaDataRepository.findByConfigIdAndSt(data.getConfigId(), data.getSt())
                .ifPresent(oldData -> data.setId(oldData.getId()));
        kaibaDataRepository.save(data);
    }

    public KanbanDataModel getDataByConfig(KanbanConfigModel config, Long startTime, Long endTime) {
        KanbanDataModel data = Mapper.map(config, KanbanDataModel.class);

        List<Long> timeRow = SensorsKanbanUtils.timeSlice(config, startTime, endTime);
        List<List<Long>> result = config.getColumns().stream()
                .map(column -> getValueByColumnConfig(column, timeRow, startTime, endTime))
                .collect(Collectors.toList());
        data.setRows(transpose(result));
        data.setRowDefine(timeRow);
        return data;
    }

    private List<Long> getValueByColumnConfig(KanbanColumn column, List<Long> timeRow, Long startTime, Long endTime) {
        // 参数非空检查
        if (column == null || timeRow == null) {
            throw new KbException(KbCode.ILLEGAL_ARGUMENT).r("Column and timeRow must not be null.").li();
        }
        // 开始时间和结束时间检查
        if (startTime > endTime) {
            throw new KbException(KbCode.ILLEGAL_ARGUMENT).r("Start time must be less than or equal to end time.").li();
        }

        KanbanColDataType type = KanbanColDataType.resolveByName(column.getType())
                .orElseThrow(() -> new KbException(KbCode.ILLEGAL_ARGUMENT).r("Invalid column type.").li());

        KanbanAggrType aggrType = KanbanAggrType.resolveByName(column.getAlgorithm())
                .orElseThrow(() -> new KbException(KbCode.ILLEGAL_ARGUMENT).r("Invalid column algorithm.").li());

        List<Long> rows = new ArrayList<>(timeRow.size());
        switch (type) {
            case ACTIVITY_ACT:
                fillRowsWithMatchedData(rows, timeRow, actDataRepository.findByConfigIdSt(column.getConfigId(), startTime, endTime), mapFunctionAct(aggrType));
                break;
            case PAGE_VISIT:
                fillRowsWithMatchedData(rows, timeRow, pageDataRepository.findByConfigIdSt(column.getConfigId(), startTime, endTime), mapFunctionPage(aggrType));
                break;
            case PLATFORM_DEVICE:
                fillRowsWithMatchedData(rows, timeRow, platformDataRepository.findByConfigIdSt(column.getConfigId(), startTime, endTime), PlatformDivData::getDevice);
                break;
            case PLATFORM_DAILY_ACTIVE:
                fillRowsWithMatchedData(rows, timeRow, platformDataRepository.findByConfigIdSt(column.getConfigId(), startTime, endTime), PlatformDivData::getDaily);
                break;
            case KAIBA_DOWNLOAD:
                fillRowsWithMatchedData(rows, timeRow, kaibaDataRepository.findByConfigIdSt(column.getConfigId(), startTime, endTime), KaibaDivData::getDownload);
                break;
            case KAIBA_REGISTER:
                fillRowsWithMatchedData(rows, timeRow, kaibaDataRepository.findByConfigIdSt(column.getConfigId(), startTime, endTime), KaibaDivData::getRegister);
                break;
            default:
                fillRowsWithMatchedData(rows, timeRow, pgcDataRepository.findByConfigIdSt(column.getConfigId(), startTime, endTime), mapFunction(type, aggrType));
                break;
        }

        return rows;
    }

    private <T extends DivData> void fillRowsWithMatchedData(List<Long> rows, List<Long> timeRow, List<T> dataList, Function<T, Long> valueExtractor) {
        Map<Long, T> dataMap = dataList.stream().collect(Collectors.toMap(DivData::getSt, Function.identity(), (o1, o2) -> o2));
        for (Long rowDefine : timeRow) {
            T dv = dataMap.get(rowDefine);
            rows.add(dv == null ? 0L : Optional.ofNullable(valueExtractor.apply(dv)).orElse(0L));
        }
    }

    private Function<ActDivData, Long> mapFunctionAct(KanbanAggrType aggrType) {
        switch (aggrType) {
            case SUM:
                return ActDivData::getPv;
            case SUM_DISTINCT:
                return ActDivData::getUv;
            default:
                return d -> 0L;
        }
    }

    private Function<PageDivData, Long> mapFunctionPage(KanbanAggrType aggrType) {
        switch (aggrType) {
            case SUM:
                return PageDivData::getPv;
            case SUM_DISTINCT:
                return PageDivData::getUv;
            default:
                return d -> 0L;
        }
    }

    private Function<PGCDivData, Long> mapFunction(KanbanColDataType type, KanbanAggrType aggrType) {
        return data -> {
            PGCDivDataBase base = mapAggrTypeFunction(aggrType).apply(data);
            if (base == null) {
                return 0L;
            } else {
                return mapColType2PGCFunction(type).apply(base);
            }
        };
    }


    private Function<PGCDivData, PGCDivDataBase> mapAggrTypeFunction(KanbanAggrType aggrType) {
        switch (aggrType) {
            case SUM:
                return PGCDivData::getSum;
            case COUNT:
                return PGCDivData::getCount;
            case AVG:
                return PGCDivData::getAvg;
            case SUM_DISTINCT:
                return PGCDivData::getSumDistinct;
            default:
                return PGCDivData::getSum;
        }
    }

    private Function<PGCDivDataBase, Long> mapColType2PGCFunction(KanbanColDataType type) {
        switch (type) {
            case PGC_VIEW:
                return PGCDivDataBase::getViewCount;
            case PGC_SHARE:
                return PGCDivDataBase::getShareCount;
            case PGC_LIKE:
                return PGCDivDataBase::getLikeCount;
            case PGC_COMMENT:
                return PGCDivDataBase::getCommentCount;
            case PGC_REPLY:
                return PGCDivDataBase::getReplyCount;
            case PGC_NOTE:
                return PGCDivDataBase::getNoteCount;
            case PGC_NOTE_TEXT:
                return PGCDivDataBase::getNoteText;
            case PGC_NOTE_IMAGE:
                return PGCDivDataBase::getNoteImage;
            case PGC_NOTE_VIDEO:
                return PGCDivDataBase::getNoteVideo;
            case PGC_NOTE_AUDIO:
                return PGCDivDataBase::getNoteAudio;
            case PGC_COMMENT_TEXT:
                return PGCDivDataBase::getCommentText;
            default:
                return PGCDivDataBase -> 0L;
        }
    }


    private List<List<Long>> transpose(List<List<Long>> data) {
        List<List<Long>> result = new ArrayList<>(data.get(0).size());
        for (int i = 0; i < data.get(0).size(); i++) {
            List<Long> columnValue = new ArrayList<>(data.size());
            for (List<Long> column : data) {
                columnValue.add(column.get(i));
            }
            result.add(columnValue);
        }
        return result;
    }
}
