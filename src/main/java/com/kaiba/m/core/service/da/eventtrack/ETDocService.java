package com.kaiba.m.core.service.da.eventtrack;

import com.kaiba.lib.base.response.KbCode;
import com.kaiba.lib.base.response.KbException;
import com.kaiba.m.core.domain.da.eventtrack.ETDoc;
import com.kaiba.m.core.domain.da.eventtrack.ETDocDepart;
import com.kaiba.m.core.domain.da.eventtrack.ETDocExclude;
import com.kaiba.m.core.domain.da.eventtrack.ETDocModule;
import com.kaiba.m.core.domain.da.eventtrack.ETDocModuleArticleRule;
import com.kaiba.m.core.repository.da.eventtrack.ETDocDepartRepository;
import com.kaiba.m.core.repository.da.eventtrack.ETDocExcludeRepository;
import com.kaiba.m.core.repository.da.eventtrack.ETDocModuleArticleRuleRepository;
import com.kaiba.m.core.repository.da.eventtrack.ETDocModuleRepository;
import com.kaiba.m.core.repository.da.eventtrack.ETDocRepository;
import com.kaiba.m.core.model.da.eventtrack.ETDocQueryModel;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Optional;

/**
 * 标准稿件基础服务
 * <AUTHOR>
 * @version ETDocService, v0.1 2025/4/3 14:20 daopei Exp $
 **/
@Service
public class ETDocService {


    private final ETDocRepository etDocRepository;

    private final ETDocModuleRepository etDocModuleRepository;

    private final ETDocDepartRepository etDocDepartRepository;

    private final ETDocExcludeRepository etDocExcludeRepository;

    private final ETDocModuleArticleRuleRepository articleRuleRepository;

    public ETDocService(
            ETDocRepository etDocRepository,
            ETDocModuleRepository etDocModuleRepository,
            ETDocDepartRepository etDocDepartRepository,
            ETDocExcludeRepository etDocExcludeRepository,
            ETDocModuleArticleRuleRepository articleRuleRepository
    ) {
        this.etDocRepository = etDocRepository;
        this.etDocModuleRepository = etDocModuleRepository;
        this.etDocDepartRepository = etDocDepartRepository;
        this.etDocExcludeRepository = etDocExcludeRepository;
        this.articleRuleRepository = articleRuleRepository;
    }

    // 标准稿件 ------------------------------------------------------------------------

    public List<ETDoc> getDocByIds(List<String> docIds) {
        if (docIds == null || docIds.isEmpty()) {
            return Collections.emptyList();
        }
        return etDocRepository.findByDocIdIn(docIds);
    }

    public Page<ETDoc> getDocPage(ETDocQueryModel query) {
        if (query == null) {
            throw new KbException(KbCode.ILLEGAL_ARGUMENT, "query is null");
        }
        return etDocRepository.getPageQuery(query);
    }

    public Optional<ETDoc> getDocById(String docId) {
        if (docId == null) {
            throw new KbException(KbCode.ILLEGAL_ARGUMENT, "query is null");
        }
        return etDocRepository.findByDocId(docId);
    }

    public ETDoc upsertDoc(ETDoc doc) {
        if (doc == null) {
            throw new KbException(KbCode.ILLEGAL_ARGUMENT, "doc is null");
        }
        if (doc.getBiz() == null || doc.getRef1() == null) {
            throw new KbException(KbCode.ILLEGAL_ARGUMENT, "doc.biz or doc.ref1 is null");
        }
        doc.setDocId(doc.buildDocId());
        return etDocRepository.upsertByOVR(doc, true);
    }

    public ETDoc updateDocChannel(String docId, String channel) {
        if (docId == null || channel == null) {
            throw new KbException(KbCode.ILLEGAL_ARGUMENT, "docId or channel is null");
        }
        ETDoc doc = etDocRepository.findByDocId(docId).orElseThrow(() -> new KbException(KbCode.RESOURCE_NOT_FOUND, "doc not found").li());
        doc.setChannel(channel);
        return etDocRepository.upsertByOVR(doc, true);
    }

    public ETDoc updateDocCreateTime(String docId, Long docCreateTime) {
        if (docId == null) {
            throw new KbException(KbCode.ILLEGAL_ARGUMENT, "docId or docCreateTime is null");
        }
        etDocRepository.findByDocId(docId).orElseThrow(() -> new KbException(KbCode.RESOURCE_NOT_FOUND, "doc not found").li());
        return etDocRepository.updateDocCreateTime(docId, docCreateTime);
    }

    public ETDoc updateDocOVR(String docId, boolean autoUpdateOVR) {
        if (docId == null) {
            throw new KbException(KbCode.ILLEGAL_ARGUMENT, "docId is null");
        }
        etDocRepository.findByDocId(docId).orElseThrow(() -> new KbException(KbCode.RESOURCE_NOT_FOUND, "doc not found").li());
        return etDocRepository.updateOVR(docId, autoUpdateOVR);
    }

    // 稿件标签 ------------------------------------------------------------------------

    public Page<ETDocModule> getModulePage(String module, String name, String category, Integer page, Integer pageSize) {
        return etDocModuleRepository.getPageByQuery(module, name, category, p(page, pageSize, "createTime"));
    }

    public Optional<ETDocModule> getModuleByModule(String module) {
        return etDocModuleRepository.findByModule(module);
    }


    public ETDocModule upsertModule(ETDocModule module) {
        if (module == null) {
            throw new KbException(KbCode.ILLEGAL_ARGUMENT, "module is null");
        }
        if (module.getModule() == null) {
            throw new KbException(KbCode.ILLEGAL_ARGUMENT, "module.module is null");
        }
        return etDocModuleRepository.upsert(module);
    }

    public void upsertModuleBatch(List<ETDocModule> moduleList) {
        for (ETDocModule etDocModule : moduleList) {
            upsertModule(etDocModule);
        }
    }

    // 稿件标签规则 ------------------------------------------------------------------------

    public Page<ETDocModuleArticleRule> getModuleArticleRulePage(String groupKey, Integer page, Integer pageSize) {
        Pageable pageable = p(page, pageSize, "createTime");
        if (groupKey == null) {
            return articleRuleRepository.findAll(pageable);
        } else {
            return articleRuleRepository.findByGroupKey(groupKey, pageable);
        }
    }

    public ETDocModuleArticleRule upsertModuleArticleRule(ETDocModuleArticleRule rule) {
        if (rule == null) {
            throw new KbException(KbCode.ILLEGAL_ARGUMENT, "rule is null");
        }
        if (rule.getGroupKey() == null) {
            throw new KbException(KbCode.ILLEGAL_ARGUMENT, "rule.groupKey is null");
        }
        return articleRuleRepository.upsert(rule);
    }

    /**
     *
     * @param groupKey
     */
    public void deleteModuleArticleRule(String groupKey) {
        if (groupKey == null) {
            throw new KbException(KbCode.ILLEGAL_ARGUMENT, "groupKey is null");
        }
        articleRuleRepository.deleteByGroupKey(groupKey);
    }

    // 稿件部门 ------------------------------------------------------------------------

    public ETDocDepart upsertDepart(ETDocDepart depart) {
        if (depart == null) {
            throw new KbException(KbCode.ILLEGAL_ARGUMENT, "depart is null");
        }
        if (depart.getDepart() == null || depart.getChannel() == null) {
            throw new KbException(KbCode.ILLEGAL_ARGUMENT, "depart or channel is null");
        }
        return etDocDepartRepository.upsert(depart);
    }

    public Page<ETDocDepart> getDepartPage(String channel, String name, List<String> departList, Integer page, Integer pageSize) {
        return etDocDepartRepository.getPageQuery(channel, name, departList, p(page, pageSize, "createTime"));
    }

    // 稿件黑名单 ------------------------------------------------------------------------

    public ETDocExclude upsertDocExclude(ETDocExclude exclude) {
        if (exclude == null) {
            throw new KbException(KbCode.ILLEGAL_ARGUMENT, "exclude is null");
        }
        if (exclude.getBiz() == null || exclude.getRef1() == null) {
            throw new KbException(KbCode.ILLEGAL_ARGUMENT, "exclude.biz or exclude.ref1 is null");
        }
        exclude.setDocId(exclude.buildDocId());
        return etDocExcludeRepository.upsert(exclude);
    }

    public Page<ETDocExclude> getExcludePage(ETDocQueryModel query) {
        return etDocExcludeRepository.getPageQuery(query);
    }



    private static Pageable p(Integer page, Integer pageSize) {
        int p = page == null ? 0 : page - 1;
        int ps = pageSize == null ? 20 : pageSize;
        return PageRequest.of(p, ps);
    }

    private static Pageable p(Integer page, Integer pageSize, String orderField) {
        int p = page == null ? 0 : page - 1;
        int ps = pageSize == null ? 20 : pageSize;
        return PageRequest.of(p, ps, Sort.by(Sort.Direction.DESC, orderField));
    }
}
