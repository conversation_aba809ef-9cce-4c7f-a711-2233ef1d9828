package com.kaiba.m.core.service.appcomponent.textticker;


import com.kaiba.lib.base.assembler.Mapper;
import com.kaiba.lib.base.domain.appwidget.textticker.TextTickerDataCreateModel;
import com.kaiba.lib.base.domain.appwidget.textticker.TextTickerDataModel;
import com.kaiba.lib.base.domain.appwidget.textticker.TextTickerDataUpdateModel;
import com.kaiba.lib.base.domain.appwidget.textticker.TextTickerInstanceModel;
import com.kaiba.lib.base.lang.verifier.F;
import com.kaiba.lib.base.lang.verifier.Verifier;
import com.kaiba.lib.base.lang.verifier.VerifierBuilder;
import com.kaiba.lib.base.util.appaction.AppActionUtils;
import com.kaiba.m.core.domain.appwidget.textticker.TextTicker;
import com.kaiba.m.core.domain.appwidget.textticker.TextTickerInstance;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version AppTextTickerModelHelper, v0.1 2023/7/18 17:32 daopei Exp $
 **/
public class AppTextTickerModelHelper {


    public static TextTickerInstance model2Instance(TextTickerInstanceModel model) {
        return Mapper.map(model, TextTickerInstance.class);
    }

    public static TextTickerInstanceModel instance2Model(TextTickerInstance instance) {
        return Mapper.map(instance, TextTickerInstanceModel.class);
    }


    public static TextTickerDataModel data2Model(TextTicker data) {
        return Mapper.map(data, TextTickerDataModel.class);
    }

    public static List<TextTickerDataModel> data2ModelList(List<TextTicker> dataList) {
        return dataList.stream().map(AppTextTickerModelHelper::data2Model).collect(Collectors.toList());
    }

    public static TextTicker createModel2Data(TextTickerDataCreateModel model) {
        return Mapper.map(model,TextTicker.class);
    }

    public static TextTickerDataModel data2ModelForClient(TextTicker data) {
        if (data == null) {
            return null;
        }
        TextTickerDataModel model = new TextTickerDataModel();
        model.setId(data.getId());
        model.setIdx(data.getIdx());
        model.setTitle(data.getTitle());
        model.setAction(data.getAction());
        model.setActionParams(data.getActionParams());
        model.setAttr(data.getAttr());
        model.setInstanceId(data.getInstanceId());
        return model;
    }

    static final Verifier<TextTickerInstanceModel> INSTANCE_VERIFIER = new VerifierBuilder<TextTickerInstanceModel>().defaultOrElseThrow()
            .and(F.str(TextTickerInstanceModel::getKey).notEmpty())
            .and(F.intF(TextTickerInstanceModel::getSiteId).notNull())
            .and(F.intF(TextTickerInstanceModel::getMaxLine).notNull())
            .and(F.intF(TextTickerInstanceModel::getAutoplay).notNull())
            .and(F.str(TextTickerInstanceModel::getName).notEmpty())
            .create();

    static final Verifier<TextTickerDataCreateModel> DATA_CREATE_VERIFIER = new VerifierBuilder<TextTickerDataCreateModel>().defaultOrElseThrow()
            .and(F.str(TextTickerDataCreateModel::getInstanceId).isObjectId())
            .and(AppActionUtils::verifyAction)
            .create();

    static final Verifier<TextTickerDataUpdateModel> DATA_UPDATE_VERIFIER = new VerifierBuilder<TextTickerDataUpdateModel>().defaultOrElseThrow()
            .and(F.str(TextTickerDataUpdateModel::getId).isObjectId())
            .and(AppActionUtils::verifyAction)
            .create();
}
