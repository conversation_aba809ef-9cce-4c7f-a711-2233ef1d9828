package com.kaiba.m.core.service.note.note;

import com.kaiba.lib.base.constant.note.NoteOrder;
import com.kaiba.m.core.domain.note.Note;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.*;

/**
 * author: lyux
 * date: 19-6-5
 *
 * 帖子列表的 id 缓存. 以 thread id 为 key 进行缓存. 目的是为了将板块新帖的分页信息缓存下来.
 */
@Slf4j
@Service
public class NoteIdListCacheService {

    private static final List<IdListCacheOrder> CACHE_ORDERS = Arrays.asList(
            new IdListCacheOrder(NoteOrder.REFRESH_TIME_DESC, Note::getRefreshTimeMS),
            new IdListCacheOrder(NoteOrder.CREATE_TIME_DESC, Note::getCreateTimeMS)
    );

    private final StringRedisTemplate stringRedisTemplate;
    private final Map<NoteOrder, IdListCache> cacheMap;

    public NoteIdListCacheService(StringRedisTemplate stringRedisTemplate) {
        this.stringRedisTemplate = stringRedisTemplate;
        this.cacheMap = new HashMap<>(CACHE_ORDERS.size());
    }

    @PostConstruct
    public void init() {
        for (IdListCacheOrder cacheOrder : CACHE_ORDERS) {
            cacheMap.put(cacheOrder.getNoteOrder(), new IdListCache(stringRedisTemplate, cacheOrder));
        }
    }

    boolean isNoteOrderSupported(NoteOrder noteOrder) {
        return cacheMap.containsKey(noteOrder);
    }

    private IdListCache getIdListCache(NoteOrder noteOrder) {
        IdListCache cache = cacheMap.get(noteOrder);
        if (cache == null) {
            throw new IllegalStateException("id cache do not support this order: " + noteOrder);
        }
        return cache;
    }

    boolean isPopulating(NoteOrder noteOrder, String threadId) {
        return getIdListCache(noteOrder).isPopulating(threadId);
    }

    void populate(NoteOrder noteOrder, String threadId, List<Note> noteList) {
        getIdListCache(noteOrder).populate(threadId, noteList);
    }

    void addNote(Note note) {
        cacheMap.values().forEach(cache -> cache.addNote(note));
    }

    void removeNote(Note note, String threadId) {
        cacheMap.values().forEach(cache -> cache.removeNote(note, threadId));
    }

    void invalid(String threadId) {
        cacheMap.values().forEach(cache -> cache.invalid(threadId));
    }

    void trim(String threadId) {
        cacheMap.values().forEach(cache -> cache.trim(threadId));
    }

    /**
     * 获取分页帖子 id 列表
     * @param threadId 板块 id
     * @param page 页码. 以 1 开始
     * @param pageSize 本页元素个数
     * @return 若缓存无效, 则返回 Optional.empty();
     *         若缓存有效, 不论元素个数是否为0, 返回 Optional.of(list)
     */
    Optional<List<String>> getNoteIdList(NoteOrder noteOrder, String threadId, int page, int pageSize) {
        return getIdListCache(noteOrder).getNoteIdList(threadId, page, pageSize);
    }

    /**
     * 获取分页帖子 id 列表
     * @param threadId 板块 id
     * @param lastNoteId 上一页的最后一条 noteId.
     * @param pageSize 本页元素个数
     * @return 若缓存无效, 则返回 Optional.empty();
     *         若缓存有效, 不论元素个数是否为0, 返回 Optional.of(list)
     */
    Optional<List<String>> getNoteIdListByLastNoteId(NoteOrder noteOrder, String threadId, String lastNoteId, int pageSize) {
        return getIdListCache(noteOrder).getNoteIdListByLastId(threadId, lastNoteId, pageSize);
    }

}
