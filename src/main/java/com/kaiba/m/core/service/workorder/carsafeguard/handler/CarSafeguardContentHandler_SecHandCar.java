package com.kaiba.m.core.service.workorder.carsafeguard.handler;

import com.kaiba.lib.base.assembler.Mapper;
import com.kaiba.lib.base.lang.verifier.F;
import com.kaiba.lib.base.lang.verifier.V;
import com.kaiba.lib.base.lang.verifier.Verifier;
import com.kaiba.lib.base.lang.verifier.VerifierBuilder;
import com.kaiba.m.core.domain.workorder.carsafeguard.CarSafeguardCaseContent;
import com.kaiba.m.core.domain.workorder.carsafeguard.CarSafeguardSecHandCarCase;
import com.kaiba.m.core.repository.workorder.carsafeguard.CarSafeguardCaseContentRepository;
import com.kaiba.m.core.repository.workorder.carsafeguard.CarSafeguardSecHandCarRepository;
import com.kaiba.m.core.service.workorder.carsafeguard.CarSafeguardBizType;
import com.kaiba.m.core.service.workorder.carsafeguard.CarSafeguardConstants;
import com.kaiba.m.core.service.workorder.carsafeguard.CarSafeguardUtils;
import com.kaiba.m.core.model.safeguard.CarSafeguardCaseContentModel;
import com.kaiba.m.core.model.safeguard.CarSafeguardSecHandCarCaseContentModel;
import com.kaiba.m.core.model.safeguard.request.CSSecHandCarCaseCreateModel;
import com.kaiba.m.core.model.safeguard.request.CarSafeguardCaseCreateModel;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version CarSafeguardContentHandler_SecHandCar, v0.1 2024/7/22 17:04 daopei Exp $
 **/
@Service
public class CarSafeguardContentHandler_SecHandCar implements CarSafeguardContentHandler{

    private final CarSafeguardSecHandCarRepository caseRepository;
    private final CarSafeguardCaseContentRepository contentRepository;


    public CarSafeguardContentHandler_SecHandCar(CarSafeguardSecHandCarRepository caseRepository,CarSafeguardCaseContentRepository contentRepository) {
        this.caseRepository = caseRepository;
        this.contentRepository = contentRepository;
    }

    @Override
    public boolean support(String contentType) {
        return CarSafeguardBizType.SECOND_HAND_CAR == CarSafeguardBizType.valueOf(contentType);
    }

    @Override
    public Class<CarSafeguardSecHandCarCaseContentModel> contentClass() {
        return CarSafeguardSecHandCarCaseContentModel.class;
    }

    @Override
    public CarSafeguardCaseContentModel getContentById(String id) {
        return caseRepository.findById(id)
                .map(c -> Mapper.map(c, CarSafeguardSecHandCarCaseContentModel.class))
                .orElse(null);
    }

    @Override
    public List<CarSafeguardCaseContentModel> getContentById(Collection<String> ids) {
        return caseRepository.findByIdIn(ids).stream()
                .map(c -> Mapper.map(c, CarSafeguardSecHandCarCaseContentModel.class))
                .collect(Collectors.toList());
    }

    @Override
    public <T extends CarSafeguardCaseCreateModel> CarSafeguardCaseContentModel createContent(T model) {
        secHandCarVerify.verify((CSSecHandCarCaseCreateModel)model);
        CarSafeguardSecHandCarCase entity = Mapper.map(model, CarSafeguardSecHandCarCase.class);
        //父类属性
        entity.setContent(model.getContent());
        entity.setContentType(CarSafeguardBizType.SECOND_HAND_CAR.name());
        entity.setUserId(model.getClientUserId());
        entity.setSource(model.getSource());
        entity.setImages(model.getImages());
        entity.setVideo(model.getVideo());
        //基础属性
        entity.setPersonal(CarSafeguardUtils.getACLStringDataListOrThrow(model));
        entity.setOriginResolverLack(model.getOriginResolverLack());
        entity.setCreateTime(System.currentTimeMillis());
        entity.setUpdateTime(System.currentTimeMillis());
        entity = caseRepository.save(entity);

        createContentIndex(entity.getId(), CarSafeguardBizType.SECOND_HAND_CAR.name());
        return map2Model(entity);
    }

    public void createContentIndex(String contentId, String contentType) {
        CarSafeguardCaseContent content = new CarSafeguardCaseContent();
        content.setContentId(contentId);
        content.setContentType(contentType);
        content.setCreateTime(System.currentTimeMillis());
        contentRepository.save(content);
    }

    private static CarSafeguardSecHandCarCaseContentModel map2Model(CarSafeguardSecHandCarCase entity) {
        CarSafeguardSecHandCarCaseContentModel model = new CarSafeguardSecHandCarCaseContentModel();
        model.setId(entity.getId());
        model.setContent(entity.getContent());
        model.setContentType(CarSafeguardBizType.SECOND_HAND_CAR.name());
        model.setCreateTime(entity.getCreateTime());
        model.setUpdateTime(entity.getUpdateTime());
        model.setPersonal(entity.getPersonal());
        model.setSource(entity.getSource());
        model.setUserId(entity.getUserId());
        model.setVideo(entity.getVideo());
        model.setImages(entity.getImages());
        model.setOriginResolverLack(entity.getOriginResolverLack());
        model.setSecHandCarRole(entity.getSecHandCarRole());
        model.setSecHandCarDealTime(entity.getSecHandCarDealTime());
        return model;
    }

    static Verifier<CSSecHandCarCaseCreateModel> secHandCarVerify = new VerifierBuilder<CSSecHandCarCaseCreateModel>().defaultOrElseThrow()
            .and(F.str(CSSecHandCarCaseCreateModel::getSecHandCarRole).in(CarSafeguardConstants.SEC_HAND_CAR_ROLE_LIST).r("请选择正确的身份"))
            .addVerifier(V.or(
                            F.str(CSSecHandCarCaseCreateModel::getOriginResolver).notNull(),
                            F.str(CSSecHandCarCaseCreateModel::getOriginResolverLack).notNull())
                    .r("请选择或者输入投诉对象"))
            .create();
}
