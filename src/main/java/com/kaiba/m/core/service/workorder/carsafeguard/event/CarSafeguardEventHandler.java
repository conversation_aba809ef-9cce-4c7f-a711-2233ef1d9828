package com.kaiba.m.core.service.workorder.carsafeguard.event;

import com.kaiba.m.core.domain.workorder.WOCase;
import com.kaiba.m.core.service.workorder.WOCaseOperator;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version CarSafeguardEventHandler, v0.1 2024/7/29 15:20 daopei Exp $
 **/
@Component
public class CarSafeguardEventHandler {


    private final WOCaseOperator operator;

    public CarSafeguardEventHandler(WOCaseOperator operator) {
        this.operator = operator;
    }

    public void reply(WOCase woCase, Integer userId, String team, String noteId, String message) {
        operator.reply(woCase, userId, team, noteId, message);
    }

    public void close(WOCase woCase, Integer userId, String team, String closeType, String closeText, String message, String uuid) {
        operator.close(woCase, userId, team, closeType, closeText, null, message, uuid);
    }
}
