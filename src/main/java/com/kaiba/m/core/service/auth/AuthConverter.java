package com.kaiba.m.core.service.auth;

import com.kaiba.lib.base.constant.auth.AuthScope;
import com.kaiba.lib.base.constant.auth.AuthType;
import com.kaiba.lib.base.domain.auth.AuthPermissionModel;
import com.kaiba.lib.base.domain.auth.AuthPermissionTemplateModel;
import com.kaiba.lib.base.domain.auth.AuthRoleModel;
import com.kaiba.lib.base.domain.auth.AuthRoleTemplateModel;
import com.kaiba.lib.base.response.KbCode;
import com.kaiba.lib.base.response.KbException;
import com.kaiba.lib.base.util.StringUtils;
import com.kaiba.m.core.domain.auth.AuthPermission;
import com.kaiba.m.core.domain.auth.AuthPermissionTemplate;
import com.kaiba.m.core.domain.auth.AuthRole;
import com.kaiba.m.core.domain.auth.AuthRoleTemplate;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.Collections;
import java.util.LinkedList;
import java.util.List;

/**
 * author: lyux
 * date: 19-8-27
 */
@Slf4j
public final class AuthConverter {

    public static List<AuthType> getAuthTypeListFromArray(Integer[] authTypes) {
        List<AuthType> authTypeList;
        if (null == authTypes || authTypes.length == 0) {
            authTypeList = Collections.singletonList(AuthType.ANY);
        } else {
            authTypeList = new ArrayList<>(authTypes.length);
            for (Integer t : authTypes) {
                AuthType type = AuthType.valueOf(t).orElseThrow(KbException.supplier(
                        KbCode.REQUEST_PARAM_INVALID, "auth type invalid: " + t));
                authTypeList.add(type);
            }
        }
        return authTypeList;
    }

    public static AuthPermissionModel permission2model(AuthPermission permission) {
        AuthPermissionModel model = new AuthPermissionModel();
        model.setPermission(permission.getPermission());
        model.setAuthTypes(permission.getAuthTypes());
        model.setAuthTypeDescription(permission.getAuthTypes().stream()
                .map(t -> AuthType.valueOf(t).orElse(AuthType.ANY).getDescription())
                .reduce((d1, d2) -> d1 + "," + d2)
                .orElse(AuthType.ANY.getDescription()));
        model.setScope(permission.getScope());
        model.setScopeName(AuthScope.valueOf(permission.getScope()).orElse(AuthScope.ALL).getDescription());
        model.setLevel(permission.getLevel());
        model.setDescription(permission.getDescription());
        model.setCreateTime(permission.getCreateTime());
        return model;
    }

    public static AuthRoleModel role2model(AuthRole role) {
        AuthRoleModel model = new AuthRoleModel();
        model.setRole(role.getRole());
        model.setPermissions(role.getPermissions());
        model.setCollideWith(role.getCollideWith());
        model.setDescription(role.getDescription());
        model.setCreateTime(role.getCreateTime());
        model.setFormat(role.getFormat());
        model.setReferenceId(role.getReferenceId());
        model.setScope(role.getScope());
        model.setScopeName(AuthScope.valueOf(role.getScope()).orElse(AuthScope.ALL).getDescription());
        return model;
    }

    public static AuthPermissionTemplateModel permissionTemplate2model(AuthPermissionTemplate template) {
        AuthPermissionTemplateModel model = new AuthPermissionTemplateModel();
        model.setId(template.getId());
        model.setFormat(template.getFormat());
        model.setAuthTypes(template.getAuthTypes());
        model.setAuthTypeDescription(template.getAuthTypes().stream()
                .map(t -> AuthType.valueOf(t).orElse(AuthType.ANY).getDescription())
                .reduce((d1, d2) -> d1 + "," + d2)
                .orElse(AuthType.ANY.getDescription()));
        model.setScope(template.getScope());
        model.setScopeName(AuthScope.valueOf(template.getScope()).orElse(AuthScope.ALL).getDescription());
        model.setLevel(template.getLevel());
        model.setDescription(template.getDescription());
        model.setCreateTime(template.getCreateTime());
        return model;
    }

    public static AuthRoleTemplateModel roleTemplate2model(AuthRoleTemplate template) {
        AuthRoleTemplateModel model = new AuthRoleTemplateModel();
        model.setId(template.getId());
        model.setFormat(template.getFormat());
        model.setScope(template.getScope());
        model.setScopeName(AuthScope.valueOf(template.getScope()).orElse(AuthScope.ALL).getDescription());
        model.setPermissionFormats(template.getPermissionFormats());
        model.setPermissions(template.getPermissions());
        model.setDescription(template.getDescription());
        model.setCreateTime(template.getCreateTime());
        return model;
    }

    public static void checkScopeFormat(String origin, AuthScope authScope, String format, String referenceId) {
        if (authScope.isNeedFormat()) {
            if (StringUtils.isEmpty(format)) {
                throw new KbException(KbCode.REQUEST_PARAM_INVALID, "format empty");
            }
            if (StringUtils.isEmpty(referenceId)) {
                throw new KbException(KbCode.REQUEST_PARAM_INVALID, "referenceId empty");
            }
            String formattedPermission = format.replace(authScope.getWildcard(), referenceId);
            if (!formattedPermission.equals(origin)) {
                throw new KbException(KbCode.REQUEST_PARAM_INVALID,
                        "malformed format: " + origin + " -> " + formattedPermission);
            }
        }
    }

    /**
     * 将一组权限中的父子级合并.
     * 如果一组权限中有如下三个权限:
     *
     * manage.user
     * manage.user.read
     * manage.user.edit
     *
     * 根据权限规则, 拥有第一个权限即可拥有第二个和第三个权限, 因此在向外返回用户权限列表时, 可以将这三个权限合并为一个, 即: manage.user
     * 本方法即根据父子权限规则合并相关权限, 返回的权限列表总是用户被分配到的最高权限.
     */
    public static List<String> combinePermissionList(List<String> permissions) {
        if (null == permissions || permissions.size() == 0) {
            return Collections.emptyList();
        }
        Collections.sort(permissions);
        LinkedList<String> result = new LinkedList<>();
        int size = permissions.size();
        int i = 0, j;
        while (i < size) {
            String p1 = permissions.get(i);
            result.add(p1);
            j = i;
            while (true) {
                j ++;
                if (j >= size - 1) {
                    break;
                }
                String p2 = permissions.get(j);
                if (!p2.startsWith(p1)) {
                    break;
                }
            }
            i = j;
        }
        return result;
    }

}
