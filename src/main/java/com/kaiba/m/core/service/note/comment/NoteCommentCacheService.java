package com.kaiba.m.core.service.note.comment;

import com.kaiba.m.core.domain.note.NoteComment;
import com.kaiba.m.core.service.note.NoteCacheConfig;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.connection.DefaultStringTuple;
import org.springframework.data.redis.connection.StringRedisConnection;
import org.springframework.data.redis.core.RedisCallback;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * author: lyux
 * date: 19-6-5
 */
@Slf4j
@Service
public class NoteCommentCacheService {

    private static final String PLACE_HOLDER_ELEMENT = "__place_holder__";
    private static final long PLACE_HOLDER_SCORE = Long.MAX_VALUE - 1;

    private final StringRedisTemplate stringRedisTemplate;
    private final Random random = new Random();

    public NoteCommentCacheService(StringRedisTemplate stringRedisTemplate) {
        this.stringRedisTemplate = stringRedisTemplate;
    }

    boolean isPopulating(String noteId) {
        String cacheKey = noteId2key(noteId);
        Set<String> set = stringRedisTemplate.opsForZSet().reverseRange(cacheKey, 0, 0);
        return set != null && set.size() != 0 && !set.contains(PLACE_HOLDER_ELEMENT);
    }

    void populate(String noteId, List<NoteComment> commentList) {
        if (noteId == null || commentList == null || commentList.size() == 0) {
            return;
        }
        List<NoteComment> cachedCommentList = commentList.size() > NoteCacheConfig.COMMENT_LIST_BY_NOTE_MAX_COUNT ?
                commentList.subList(0, NoteCacheConfig.COMMENT_LIST_BY_NOTE_MAX_COUNT) : commentList;
        final Set<StringRedisConnection.StringTuple> cacheData = cachedCommentList.stream()
                .filter(NoteCommentCacheService::isValidComment)
                .map(comment -> string2tuple(stringRedisTemplate, comment.getId(), comment.getCreateTime()))
                .collect(Collectors.toSet());
        cacheData.add(string2tuple(stringRedisTemplate, PLACE_HOLDER_ELEMENT, PLACE_HOLDER_SCORE));
        String cacheKey = noteId2key(noteId);
        stringRedisTemplate.executePipelined((RedisCallback<Object>) connection -> {
            StringRedisConnection conn = (StringRedisConnection) connection;
            conn.del(cacheKey);
            conn.zAdd(cacheKey, cacheData);
            conn.expire(cacheKey, NoteCacheConfig.COMMENT_LIST_BY_NOTE_EXPIRE.getSeconds());
            return null;
        });
    }

    void addComment(NoteComment comment) {
        addComments(Collections.singletonList(comment));
    }

    void addComments(List<NoteComment> comments) {
        stringRedisTemplate.execute((RedisCallback<Object>) connection -> {
            StringRedisConnection conn = (StringRedisConnection) connection;
            for (NoteComment comment: comments) {
                if (comment.getId() == null || comment.getNoteId() == null) {
                    continue;
                }
                String cacheKey = noteId2key(comment.getNoteId());
                String cacheData = comment.getId();
                if (conn.exists(cacheKey)) {
                    conn.zAdd(cacheKey, comment.getCreateTime(), cacheData);
                    conn.expire(cacheKey, NoteCacheConfig.COMMENT_LIST_BY_NOTE_EXPIRE.getSeconds());
                    if (random.nextInt(10) == 1) {
                        int trimThreshold = (int) (NoteCacheConfig.COMMENT_LIST_BY_NOTE_MAX_COUNT * 1.5f);
                        long count = conn.zCard(cacheKey);
                        if (NoteCacheConfig.CACHE_DEBUG) {
                            log.info("[cache_debug][cache] trim comment list for note: "
                                    + comment.getNoteId() + ", list size: " + count);
                        }
                        if (count > trimThreshold) {
                            conn.zRemRange(cacheKey, 0, count - NoteCacheConfig.COMMENT_LIST_BY_NOTE_MAX_COUNT - 1);
                        }
                    }
                }
            }
            return conn.closePipeline();
        });
    }

    void invalid(String noteId) {
        String cacheKey = noteId2key(noteId);
        stringRedisTemplate.delete(cacheKey);
    }

    /**
     * 获取分页评论列表
     * @param noteId 板块 id
     * @param page 页码. 以 1 开始
     * @param pageSize 本页元素个数
     */
    CacheResult getCommentListByNoteId(String noteId, int page, int pageSize) {
        if (NoteCacheConfig.CACHE_DEBUG) {
            log.info("[cache_debug][cache] get comment page: " + noteId + ", [" + page + "," + pageSize + "]");
        }
        if (page <= 0) {
            page = 1;
        }
        if (pageSize <= 0) {
            pageSize = 15;
        }
        int indexLast = page * pageSize;
        if (indexLast > NoteCacheConfig.COMMENT_LIST_BY_NOTE_MAX_COUNT) {
            return EMPTY_FAILED_RESULT;
        }
        String cacheKey = noteId2key(noteId);
        // redis zRange 返回元素包含下标两端, 即 [0, indexLast] 闭集. 此处总是从头开始取, 以便取出占位数据.
        Set<String> dataSet = stringRedisTemplate.opsForZSet().reverseRange(cacheKey, 0, indexLast);
        return data2result(dataSet, page, pageSize);
    }

    /**
     * 获取分页评论列表
     * @param noteIdList 板块 id 列表
     * @param page 页码. 以 1 开始
     * @param pageSize 本页元素个数
     */
    Map<String, CacheResult> getCommentListByNoteIdIn(List<String> noteIdList, int page, int pageSize) {
        if (NoteCacheConfig.CACHE_DEBUG) {
            log.info("[cache_debug][cache] get comment page by note id list: " +
                    noteIdList + ", [" + page + "," + pageSize + "]");
        }
        if (page <= 0) {
            page = 1;
        }
        if (pageSize <= 0) {
            pageSize = 15;
        }
        int indexLast = page * pageSize;
        if (indexLast >= NoteCacheConfig.COMMENT_LIST_BY_NOTE_MAX_COUNT) {
            // 所请求数据已超过缓存最大值, 视为缓存有效, 但数据为空
            return emptyMapResult(noteIdList);
        }
        List<Object> redisResult;
        try {
            // size of 'ids' and 'dataList' are always the same:
            // redis will fill coordinate position of 'dataList' with null if cache can not be found.
            redisResult = stringRedisTemplate.executePipelined((RedisCallback<Object>) connection -> {
                StringRedisConnection conn = (StringRedisConnection) connection;
                for (String noteId : noteIdList) {
                    conn.zRevRange(noteId2key(noteId), 0, indexLast);
                }
                return null;
            });
        } catch (Exception e) {
            redisResult = Collections.emptyList();
            log.error("get note list by id from redis fail. " +
                    "noteIds: " + String.join(",", noteIdList) + ", p: " + page + ", ps: " + pageSize, e);
        }
        if (redisResult.size() != noteIdList.size()) {
            // redis 返回结果集长度与请求集长度不同, 数据有误.
            return Collections.emptyMap();
        } else {
            Map<String, CacheResult> map = new HashMap<>(noteIdList.size());
            for (int i = 0; i < noteIdList.size(); i ++) {
                String noteId = noteIdList.get(i);
                Set<String> strCommentSet = (Set<String>) redisResult.get(i);
                map.put(noteId, data2result(strCommentSet, page, pageSize));
            }
            return map;
        }
    }

    private static CacheResult data2result(Set<String> dataSet, int page, int pageSize) {
        if (dataSet == null) {
            // 数据集为空, 视为缓存无效
            return EMPTY_FAILED_RESULT;
        }
        if (!dataSet.remove(PLACE_HOLDER_ELEMENT)) {
            // 不包含占位数据, 视为缓存无效
            return EMPTY_FAILED_RESULT;
        }
        int size = dataSet.size();
        int subListBegin = (page - 1) * pageSize;
        if (size == 0 || size <= subListBegin) {
            return EMPTY_SUCCESS_RESULT;
        }
        return new CacheResult(true, new ArrayList<>(dataSet).subList(subListBegin, size));
    }

    private static String noteId2key(String noteId) {
        return NoteCacheConfig.COMMENT_LIST_BY_NOTE_KEY + noteId;
    }

    private static Map<String, CacheResult> emptyMapResult(List<String> noteIdList) {
        return noteIdList.stream().collect(Collectors.toMap(id -> id, id -> EMPTY_SUCCESS_RESULT));
    }

    private static boolean isValidComment(NoteComment comment) {
        return comment != null && comment.getId() != null && comment.getCreateTime() != null;
    }

    private static StringRedisConnection.StringTuple string2tuple(
            StringRedisTemplate stringRedisTemplate, String data, double score) {
        return new DefaultStringTuple(
                stringRedisTemplate.getStringSerializer().serialize(data), data, score);
    }

    private static final CacheResult EMPTY_SUCCESS_RESULT = new CacheResult(true, Collections.emptyList());
    private static final CacheResult EMPTY_FAILED_RESULT = new CacheResult(false, Collections.emptyList());

    @Getter
    static class CacheResult {
        private final boolean hasCache;
        private final List<String> commentList;

        CacheResult(boolean hasCache, List<String> commentList) {
            this.hasCache = hasCache;
            this.commentList = commentList;
        }
    }

}
