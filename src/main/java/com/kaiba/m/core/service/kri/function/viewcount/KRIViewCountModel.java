package com.kaiba.m.core.service.kri.function.viewcount;

/**
 * author: lyux
 * date: 2025-02-26
 */
public class KRIViewCountModel {





    // ------------------------------------------------------

//    private static final Range P_VIRTUAL = new Range(100_000_000_000_000_000L, 100_000_000_000_000_000L); // 18
//    private static final Range P_DATE = new Range(100_000_000_000_000L, 10_000_000_000_000_000L); // 15~17
//    private static final Range P_YESTERDAY_COUNT = new Range(10_000_000L, 10_000_000_000_000L); // 8~14
//    private static final Range P_TODAY_COUNT = new Range(1L, 1_000_000L); // 1~7
//
//    private static class Range {
//        private final long s;
//        private final long t;
//
//        private Range(long s, long t) {
//            this.s = s;
//            this.t = t;
//        }
//    }

    private static class CacheModel {
        private final long payload;
        private final long virtual;
        private final long day;
        private final long yCount;
        private final long tCount;

        public CacheModel(long payload) {
            this.payload = payload;
            this.tCount = payload % P_TODAY_COUNT_E;
            this.yCount = (payload % P_YESTERDAY_COUNT_E) / P_YESTERDAY_COUNT_S;
            this.day = (payload % P_DATE_E) / P_DATE_S;
            this.virtual = payload / P_VIRTUAL;
        }



    }

    private static final long P_VIRTUAL = 100_000_000_000_000_000L; // 18
    private static final long P_DATE_E = 10_000_000_000_000_000L; // 17
    private static final long P_DATE_S = 100_000_000_000_000L; // 15
    private static final long P_YESTERDAY_COUNT_E = 10_000_000_000_000L; // 14
    private static final long P_YESTERDAY_COUNT_S = 10_000_000L; // 8
    private static final long P_TODAY_COUNT_E = 1_000_000L; // 7

}
