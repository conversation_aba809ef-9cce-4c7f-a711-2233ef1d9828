package com.kaiba.m.core.service.urlmap;

import com.kaiba.lib.base.response.KbCode;
import com.kaiba.lib.base.response.KbException;
import com.kaiba.m.core.domain.urlmap.UrlMapperCategory;
import com.kaiba.m.core.model.urlmapper.CategoryQueryDTO;
import com.kaiba.m.core.repository.urlmapper.UrlMapperCategoryRepository;
import com.kaiba.m.core.repository.urlmapper.UrlMapperRepository;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * Description: 短链分类Service层服务
 * Author: ZM227
 * Date: 2025/2/20 15:58
 */
@Slf4j
@Service
public class UrlMapperCategoryService {

    @Resource
    private UrlMapperCategoryRepository categoryRepository;
    @Resource
    private UrlMapperRepository mapperRepository;
    @Resource
    private UrlMapperService urlMapperService;
    @Resource
    private UrlCounterService urlCounterService;

    private static final String MAPPER_NAME_PREFIX = "渠道%d";

    @Transactional(rollbackFor = Exception.class)
    public UrlMapperCategory createUrlMapperCategory(String url, String name, String channelKey,
        Long uvEndTime,
        int count) {
        long current = System.currentTimeMillis() / 1000;
        if (Objects.isNull(uvEndTime)) {
            uvEndTime = current + TimeUnit.DAYS.toSeconds(45);
        }
        UrlMapperCategory category = new UrlMapperCategory();
        category.setUrl(url);
        category.setName(name);
        category.setState(1);
        category.setChannelKey(channelKey);
        category.setUvEndTime(uvEndTime);
        category.setCreateTime(current);
        category.setUpdateTime(current);
        category = categoryRepository.insert(category);
        if (count > 0) {
            for (int i = 0; i < count; i++) {
                urlMapperService.createUrlMapper(url, String.format(MAPPER_NAME_PREFIX, i + 1),
                    category.getId(), uvEndTime);
            }
        }
        return category;
    }

    @Transactional(rollbackFor = Exception.class)
    public UrlMapperCategory updateCategoryUrl(String categoryId, String url, String channelKey,
        String name, Long uvEndTime, Boolean syncUrl) {
        Optional<UrlMapperCategory> categoryOptional = categoryRepository.findFirstById(categoryId);
        if (!categoryOptional.isPresent()) {
            throw new KbException(KbCode.RESOURCE_NOT_FOUND,
                "mapper category not exist, id: " + categoryId);
        }
        UrlMapperCategory category = categoryOptional.get();
        category.setUrl(url);
        category.setChannelKey(channelKey);
        category.setName(name);
        category.setUvEndTime(uvEndTime);
        category = categoryRepository.updateById(category);
        if (syncUrl) {
            mapperRepository.updateUrlByCategoryId(url, categoryId, uvEndTime);
            mapperRepository.findAllByCategoryId(categoryId)
                .forEach(mapper -> urlCounterService.invalidateCache(mapper.getKey()));
        }
        return category;
    }


    /**
     * 通过分类id逻辑删除分类
     *
     * @param categoryId   分类id
     * @param deleteMapper 是否同步删除UrlMapper
     */
    @Transactional(rollbackFor = Exception.class)
    public void deleteCategory(String categoryId, boolean deleteMapper) {
        Optional<UrlMapperCategory> categoryOptional = categoryRepository.findFirstById(categoryId);
        if (!categoryOptional.isPresent()) {
            throw new KbException(KbCode.RESOURCE_NOT_FOUND,
                "mapper category not exist, id: " + categoryId);
        }
        UrlMapperCategory category = categoryOptional.get();
        category.setState(0);
        categoryRepository.updateById(category);
        if (deleteMapper) {
            mapperRepository.updateStateByCategoryId(0, categoryId);
        }
    }

    public UrlMapperCategory getCategoryById(String categoryId) {
        return categoryRepository.findFirstById(categoryId).orElse(null);
    }

    public Page<UrlMapperCategory> findAllByCondition(CategoryQueryDTO queryDTO) {
        return categoryRepository.findAllByCondition(queryDTO);
    }

}
