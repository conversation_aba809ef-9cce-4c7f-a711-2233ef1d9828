package com.kaiba.m.core.service.news.pool.knowledge;

import com.kaiba.lib.base.domain.news.pool.knowledge.BasedAnswerModel;
import com.kaiba.lib.base.domain.news.pool.knowledge.BasedAnswerQueryModel;
import com.kaiba.lib.base.domain.news.pool.knowledge.KnowledgeLogModel;
import com.kaiba.lib.base.domain.news.pool.knowledge.KnowledgeLogQueryModel;
import com.kaiba.m.core.domain.knowledge.KnowledgeQuestionLog;
import java.util.Optional;
import org.springframework.data.domain.Page;

/**
 * Description: 问答日志查询Service接口
 * Author: ZM227
 * Date: 2025/1/6 17:45
 */
public interface IQuestionLogQueryService {

    Optional<KnowledgeQuestionLog> findQuestionLogById(String logCode);

    /**
     * 根据条件查询问答日志
     * @param queryModel 问答日志查询条件
     * @return 返回日志记录
     */
    Page<KnowledgeLogModel> findLogsByCondition(KnowledgeLogQueryModel queryModel);

}
