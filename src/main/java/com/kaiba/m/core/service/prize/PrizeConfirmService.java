package com.kaiba.m.core.service.prize;

import com.google.gson.JsonSyntaxException;
import com.google.gson.reflect.TypeToken;
import com.kaiba.lib.base.constant.KbEndpoint;
import com.kaiba.lib.base.constant.KbHttpHeaders;
import com.kaiba.lib.base.constant.KbHttpParams;
import com.kaiba.lib.base.constant.KbSignType;
import com.kaiba.lib.base.constant.prize.PrizeState;
import com.kaiba.lib.base.domain.prize.PrizeConfirmResult;
import com.kaiba.lib.base.lang.collections.BuilderMap;
import com.kaiba.lib.base.middleware.KbRes;
import com.kaiba.lib.base.middleware.apivalidator.AuthKbSignChecker;
import com.kaiba.lib.base.response.KbCode;
import com.kaiba.lib.base.response.KbEntity;
import com.kaiba.lib.base.response.KbException;
import com.kaiba.m.core.domain.prize.DispatchMethod;
import com.kaiba.m.core.domain.prize.Prize;
import com.kaiba.m.core.repository.prize.PrizeRepository;
import com.kaiba.m.core.util.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

import java.util.HashMap;
import java.util.Map;

/**
 * author: lyux
 * date: 19-11-12
 */
@Slf4j
@Service
public class PrizeConfirmService {

    private static final String DISTRIBUTED_LOCK_MARK_PREFIX = "prize_confirm_";

    private final PrizeRepository prizeRepository;
    private final DispatchMethodService dispatchMethodService;
    private final RestTemplate restTemplate;
    private final KbRes kbRes;

    public PrizeConfirmService(
            PrizeRepository prizeRepository,
            DispatchMethodService dispatchMethodService,
            RestTemplate restTemplate,
            KbRes kbRes
    ) {
        this.prizeRepository = prizeRepository;
        this.dispatchMethodService = dispatchMethodService;
        this.restTemplate = restTemplate;
        this.kbRes = kbRes;
    }

    public KbEntity<Prize> confirmPrize(String prizeId, Integer userId, Long v) {
        Prize prize = prizeRepository.findById(prizeId).orElseThrow(() -> new KbException(
                KbCode.PRIZE_NOT_EXISTS, "prize not exists. id: " + prizeId).li());
        if (prize.getState() == PrizeState.GIVE_UP.getValue()) {
            return error("过期或放弃的奖品无法再次确认领取");
        }
        if (!prize.getV().equals(v)) {
            return error("确认失败, 可能有其他人在确认奖品, 请刷新后再试");
        }
        if (userId != null && !userId.equals(prize.getUserId())) {
            return error("奖品非该用户所有");
        }
        DispatchMethod dispatchMethod = dispatchMethodService
                .getById(prize.getDispatchMethodId(), false)
                .orElseThrow(() -> new KbException(
                        KbCode.RESOURCE_NOT_FOUND, "dispatch method not exists for: " + prize).li());
        String api = dispatchMethod.getConfirmApi();
        if (api == null) {
            return error("该奖品未设置确认规则");
        }

        // 根据回调接口设置, 请求远端进行奖品确认. 此处只能假设远端接口实现了幂等性, 否则可能出现奖品超发情况.
        KbEntity<PrizeConfirmResult> confirmResultEntity = requestConfirmResult(prize, api);
        if (!confirmResultEntity.isOk() || confirmResultEntity.getData() == null) {
            // 请求失败
            return confirmResultEntity.map(r -> null);
        } else {
            // 将确认结果更新到数据库奖品表
            PrizeConfirmResult result = confirmResultEntity.getData();
            boolean updateAsClaimed = (prize.getState() == PrizeState.NOT_CLAIMED.getValue()
                    && result.getAsClaimed() != null && result.getAsClaimed());
            Prize confirmedPrize = prizeRepository.updateConfirmStateAsConfirmed(prize.getId(), result, updateAsClaimed, v);
            log.info("confirm prize result: " + result + ", prize : " + confirmedPrize);
            if (confirmedPrize == null) {
                return error("确认失败, 奖品状态已变更, 可刷新后再试");
            } else {
                return kbRes.ok(confirmedPrize);
            }
        }
    }

    private KbEntity<PrizeConfirmResult> requestConfirmResult(Prize prize, String api) {
        // 准备请求参数
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
        headers.set(KbHttpHeaders.KB_EP.getHeaderName(), Integer.toString(KbEndpoint.BACKEND.getValue()));
        Map<String, String> params = new BuilderMap<String, String>()
                .putValue("prizeId", prize.getId())
                .putValue("userId", prize.getUserId().toString())
                .putValue(KbHttpParams.KB_SIGN_TIME.getName(), Long.toString(System.currentTimeMillis()));
        Map<String, String[]> signParams = new HashMap<>();
        params.forEach((key, value) -> signParams.put(key, new String[] { value }));
        String sign = AuthKbSignChecker.generateSignature(signParams, KbSignType.CALLBACK.getSalt());
        params.put(KbHttpParams.KB_SIGN.getName(), sign);
        MultiValueMap<String, String> paramMap = new LinkedMultiValueMap<>();
        paramMap.setAll(params);
        HttpEntity<MultiValueMap<String, String>> requestEntity = new HttpEntity<>(paramMap, headers);

        // 发起请求
        KbEntity<PrizeConfirmResult> entity;
        try {
            ResponseEntity<String> response = restTemplate.exchange(api, HttpMethod.POST, requestEntity, String.class);
            int status = response.getStatusCodeValue();
            String r = response.getBody();
            log.info("confirm prize, request api: " + api + ", result: " + r + ", status: " + status);
            if (status != HttpStatus.OK.value()) {
                return kbRes.err(KbCode.PRIZE_CONFIRM_FAIL, null, "确认失败, 申请奖品失败");
            }
            entity = JsonUtils.getGson().fromJson(r, new TypeToken<KbEntity<PrizeConfirmResult>>() {}.getType());
        } catch (JsonSyntaxException jse) {
            log.info("confirm prize, request api fail 11: " + api, jse);
            return kbRes.err(KbCode.PRIZE_CONFIRM_FAIL, null, "确认失败, 奖品数据格式错误");
        } catch (Exception e) {
            log.info("confirm prize, request api fail 22: " + api, e);
            return kbRes.err(KbCode.PRIZE_CONFIRM_FAIL, null, "确认奖品失败");
        }

        // 验证返回数据
        if (entity == null) {
            return kbRes.err(KbCode.PRIZE_CONFIRM_FAIL, null, "确认失败, 未获得奖品数据(1)");
        } else if (!entity.isOk()) {
            return entity;
        } else if (entity.getData() == null) {
            return kbRes.err(KbCode.PRIZE_CONFIRM_FAIL, null, "确认失败, 未获得奖品数据(2)");
        }

        // 根据 prize 信息修正 result
        PrizeConfirmResult result = entity.getData();
        if (result.getLinks() != null && prize.getLinks() != null) {
            result.getLinks().addAll(prize.getLinks());
        }
        if (result.getAttrs() != null && prize.getAttrs() != null) {
            // 使用新属性覆盖老属性
            prize.getAttrs().putAll(result.getAttrs());
            result.setAttrs(prize.getAttrs());
        }

        return entity;
    }

    private KbEntity<Prize> error(String message) {
        return kbRes.err(KbCode.PRIZE_CONFIRM_FAIL, null, message);
    }

    private static String getDistributedLockMark(String prizeId) {
        return DISTRIBUTED_LOCK_MARK_PREFIX + prizeId;
    }

}
