package com.kaiba.m.core.service.wx.task;

import com.kaiba.m.core.repository.wx.WxOpenAppBlockUserRepository;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version WxOpenAppBlockUserJob, v0.1 2024/6/4 11:49 daopei Exp $
 **/
@Slf4j
@Component
public class WxOpenAppBlockUserJob {

    private final WxOpenAppBlockUserRepository blockUserRepository;

    public WxOpenAppBlockUserJob(WxOpenAppBlockUserRepository blockUserRepository) {
        this.blockUserRepository = blockUserRepository;
    }


    /**
     * 清理已经结束屏蔽期但是状态还未改变的用户状态
     * before : state = 1, endTime <= now
     * after : state = 0
     *
     * @param param
     * @return
     */
    @XxlJob("wx-block-user-clean")
    public ReturnT<String> blockUserClean(String param) {
        blockUserRepository.cleanBlockState(System.currentTimeMillis());
        return ReturnT.SUCCESS;
    }


}
