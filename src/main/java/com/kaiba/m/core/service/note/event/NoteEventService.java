package com.kaiba.m.core.service.note.event;

import com.kaiba.lib.base.domain.note.NoteModel;
import com.kaiba.lib.base.domain.user.UserModel;
import com.kaiba.m.core.domain.note.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationListener;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * author: lyux
 * date: 2020-04-24
 */
@Slf4j
@Service
public class NoteEventService implements ApplicationListener<ApplicationReadyEvent> {

    private final ApplicationContext appContext;
    private volatile List<INoteEventReceiver> receivers = new ArrayList<>();

    public NoteEventService(ApplicationContext appContext) {
        this.appContext = appContext;
    }

    @Override
    public void onApplicationEvent(@NonNull ApplicationReadyEvent event) {
        Map<String, INoteEventReceiver> beans = appContext.getBeansOfType(INoteEventReceiver.class);
        Collection<INoteEventReceiver> receiverCollection = beans.values();
        List<INoteEventReceiver> receiverList = new ArrayList<>(beans.size());
        for (INoteEventReceiver receiver : receiverCollection) {
            log.info("collectEventHandlers, " +
                    "INoteEventReceiver: " + receiver.getClass().getName() + ", enabled: " + receiver.isEnabled());
            if (receiver.isEnabled()) {
                receiverList.add(receiver);
            }
        }
        this.receivers = receiverList;
    }

    public void onNoteAdd(@NonNull Note note, @Nullable NoteReview review) {
        try {
            for (INoteEventReceiver receiver : receivers) {
                receiver.onNoteAdd(note, review);
            }
        } catch (Exception e) {
            log.warn("note event handler exception", e);
        }
    }

    public void onNoteCreated(@NonNull Note note, @Nullable NoteModel noteModel, UserModel owner, String reviewId, @NonNull NoteCreateOrigin origin) {
        try {
            for (INoteEventReceiver receiver : receivers) {
                receiver.onNoteCreated(note, noteModel, owner, reviewId, origin);
            }
        } catch (Exception e) {
            log.warn("note event handler exception", e);
        }
    }

    public void onNoteReviewCreated(@NonNull NoteReview noteReview) {
        try {
            for (INoteEventReceiver receiver : receivers) {
                receiver.onNoteReviewCreated(noteReview);
            }
        } catch (Exception e) {
            log.warn("note event handler exception", e);
        }
    }

    public void onNoteReviewRefused(@NonNull NoteReview noteReview) {
        try {
            for (INoteEventReceiver receiver : receivers) {
                receiver.onNoteReviewRefused(noteReview);
            }
        } catch (Exception e) {
            log.warn("note event handler exception", e);
        }
    }

    public void onNoteDeleted(@NonNull Note note, Integer deleteUserId, boolean softDelete) {
        try {
            for (INoteEventReceiver receiver : receivers) {
                receiver.onNoteDeleted(note, deleteUserId, softDelete);
            }
        } catch (Exception e) {
            log.warn("note event handler exception", e);
        }
    }

    public void onNotePraiseUpdated(@NonNull Note note, UserModel praiser, boolean praise) {
        try {
            for (INoteEventReceiver receiver : receivers) {
                receiver.onNotePraiseUpdated(note, praiser, praise);
            }
        } catch (Exception e) {
            log.warn("note event handler exception", e);
        }
    }

    public void onNoteReferenceUpdated(@NonNull Note note) {
        try {
            for (INoteEventReceiver receiver : receivers) {
                receiver.onNoteReferenceUpdated(note);
            }
        } catch (Exception e) {
            log.warn("note event handler exception", e);
        }
    }

    public void onThreadCreated(@NonNull NoteThread thread) {
        try {
            for (INoteEventReceiver receiver : receivers) {
                receiver.onThreadCreated(thread);
            }
        } catch (Exception e) {
            log.warn("note event handler exception", e);
        }
    }

    public void onThreadDataUpdated(@NonNull NoteThread oldThread, @NonNull NoteThread updatedThread) {
        try {
            for (INoteEventReceiver receiver : receivers) {
                receiver.onThreadDataUpdated(oldThread, updatedThread);
            }
        } catch (Exception e) {
            log.warn("note event handler exception", e);
        }
    }

    public void onThreadNoteTopUpdated(@NonNull Note note, @NonNull String threadId, boolean top) {
        try {
            for (INoteEventReceiver receiver : receivers) {
                receiver.onThreadNoteTopUpdated(note, threadId, top);
            }
        } catch (Exception e) {
            log.warn("note event handler exception", e);
        }
    }

    public void onThreadNoteHotUpdated(
            @NonNull Note note, @NonNull String threadId, String title, String cover, boolean hot) {
        try {
            for (INoteEventReceiver receiver : receivers) {
                receiver.onThreadNoteHotUpdated(note, threadId, title, cover, hot);
            }
        } catch (Exception e) {
            log.warn("note event handler exception", e);
        }
    }

    public void onCommentAdd(
            @NonNull Note note, @NonNull NoteComment comment, NoteCommentReview review) {
        try {
            for (INoteEventReceiver receiver : receivers) {
                receiver.onCommentAdd(note, comment, review);
            }
        } catch (Exception e) {
            log.warn("note event handler exception", e);
        }
    }

    public void onCommentCreated(
            @NonNull Note note, @NonNull NoteComment comment,
            UserModel owner, UserModel toUser,
            String reviewId, @NonNull NoteCreateOrigin origin) {
        try {
            for (INoteEventReceiver receiver : receivers) {
                receiver.onCommentCreated(note, comment, owner, toUser, reviewId, origin);
            }
        } catch (Exception e) {
            log.warn("note event handler exception", e);
        }
    }

    public void onCommentReviewCreated(Note note, @NonNull NoteCommentReview commentReview) {
        try {
            for (INoteEventReceiver receiver : receivers) {
                receiver.onCommentReviewCreated(note, commentReview);
            }
        } catch (Exception e) {
            log.warn("note event handler exception", e);
        }
    }

    public void onCommentReviewRefused(@NonNull NoteCommentReview commentReview) {
        try {
            for (INoteEventReceiver receiver : receivers) {
                receiver.onCommentReviewRefused(commentReview);
            }
        } catch (Exception e) {
            log.warn("note event handler exception", e);
        }
    }

    public void onCommentDeleted(
            @NonNull Note note, @NonNull NoteComment comment, Integer deleteUserId, boolean softDelete) {
        try {
            for (INoteEventReceiver receiver : receivers) {
                receiver.onCommentDeleted(note, comment, deleteUserId, softDelete);
            }
        } catch (Exception e) {
            log.warn("note event handler exception", e);
        }
    }

}
