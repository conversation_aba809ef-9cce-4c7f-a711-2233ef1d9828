package com.kaiba.m.core.service.news.videoclip;

import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.LoadingCache;
import com.kaiba.lib.base.assembler.Mapper;
import com.kaiba.lib.base.constant.news.videoclip.VideoClipOrientation;
import com.kaiba.lib.base.constant.news.videoclip.VideoClipState;
import com.kaiba.lib.base.constant.tmuyun.*;
import com.kaiba.lib.base.domain.common.Image;
import com.kaiba.lib.base.domain.common.ShareModel;
import com.kaiba.lib.base.domain.common.Video;
import com.kaiba.lib.base.domain.news.videoclip.VideoClipModel;
import com.kaiba.lib.base.domain.news.videoclip.VideoClipStatModel;
import com.kaiba.lib.base.domain.news.videoclip.VideoCreateModel;
import com.kaiba.lib.base.domain.da.sensors.channel.SensorsChannelModel;
import com.kaiba.lib.base.domain.tmuyun.tmuyun.ArticleApiModel;
import com.kaiba.lib.base.lang.verifier.F;
import com.kaiba.lib.base.lang.verifier.Verifier;
import com.kaiba.lib.base.lang.verifier.VerifierBuilder;
import com.kaiba.lib.base.response.KbCode;
import com.kaiba.lib.base.response.KbException;
import com.kaiba.lib.base.service.ISensorsChannelService;
import com.kaiba.lib.base.service.ITmuyunService;
import com.kaiba.lib.base.util.StringUtils;
import com.kaiba.m.core.domain.news.videoclip.VideoClip;
import com.kaiba.m.core.domain.news.videoclip.VideoClipFuncFlag;
import com.kaiba.m.core.middleware.qiniu.QiniuManager;
import com.kaiba.m.core.repository.news.videoclip.VideoClipRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/07/05 13:46
 **/
@Slf4j
@Service
public class VideoClipService {

    private static final Long MULTI_BATCH = 100_000_000L;

    @Value("${kaiba.host.page}")
    private String host;

    private static final String MEDIA_PREFIX = "https://static.kaiba315.com.cn/";

    private static final VideoClipModel FAKE_LAST_VIDEO = getFakeLastVideoClip();

    private final LoadingCache<Integer, Long> onlineCountCache = Caffeine.newBuilder()
        .expireAfterWrite(Duration.ofMinutes(15))
        .refreshAfterWrite(Duration.ofMinutes(1))
        .build(this::getClipCountBySiteId);

    private final VideoClipRepository clipRepository;
    private final VideoClipCacheService cacheService;
    private final ITmuyunService tmuyunService;
    private final QiniuManager qiniuManager;
    private final ISensorsChannelService channelService;

    public VideoClipService(
        VideoClipRepository clipRepository,
        VideoClipCacheService cacheService,
        ITmuyunService tmuyunService,
        QiniuManager qiniuManager,
        ISensorsChannelService channelService
    ) {
        this.clipRepository = clipRepository;
        this.cacheService = cacheService;
        this.tmuyunService = tmuyunService;
        this.qiniuManager = qiniuManager;
        this.channelService = channelService;
    }

    public void batchReplaceVideoClipImage(Long st, Long et) {
        for (int i = 1; i < 100; i++) {
            Pageable pageable = createPageable(i, 100);
            List<VideoClip> list = clipRepository.findAllByCreateTimeBetween(st, et, pageable);
            if (list.isEmpty()) { return; }
            list.forEach(t -> {
                generateQiniuImageAndVideo(t);
                clipRepository.updateImageAndVideo(t);
            });
        }
    }

    public void replaceImageUrlById(String clipId) {
        clipRepository.findById(clipId).ifPresent(t -> {
            generateQiniuImageAndVideo(t);
            clipRepository.updateImageAndVideo(t);
        });
    }

    public void syncDataFromHoolo(List<VideoCreateModel> createModels) {
        if(createModels.isEmpty()) { return; }
        long time = System.currentTimeMillis();
        List<VideoClip> list = new ArrayList<>();
        createModels.sort(Comparator.comparingLong(VideoCreateModel::getExtCreateTime));
        long order = 0L;
        long lastBatch = 0L;
        for (VideoCreateModel createModel : createModels) {
            VideoClip clip = create2Clip(createModel);
            clip.setSiteId(9);
            clip.setUserId(8);
            clip.setOrientation(VideoClipOrientation.HORIZONTAL.getValue());
            clip.setState(VideoClipState.ONLINE.getValue());
            clip.setCreateTime(createModel.getExtCreateTime());
            clip.setUpdateTime(createModel.getExtCreateTime());
            clip.setSyncTime(time);
            generateQiniuImageAndVideo(clip);
            list.add(clip);
            long currBatch = Long.parseLong(Instant.ofEpochMilli(createModel.getExtCreateTime())
                .atZone(ZoneId.systemDefault()).toLocalDate().format(DateTimeFormatter.ofPattern("yyyyMMdd"))) * MULTI_BATCH;
            if (currBatch != lastBatch) {
                long batchEnd = currBatch + MULTI_BATCH;
                order = clipRepository.findFirstByIdx(currBatch, batchEnd).map(VideoClip::getIdx).orElse(currBatch);
                lastBatch = currBatch;
            }
            order = calculateNextOrder(order);
            clip.setIdx(order);
        }
        clipRepository.saveAll(list);
    }

    public VideoClipModel createByBody(VideoCreateModel createModel) {
        verifyCreate(createModel);
        VideoClip clip = create2Clip(createModel);
        clip.setState(VideoClipState.INIT.getValue());
        long time = System.currentTimeMillis();
        clip.setCreateTime(time);
        clip.setUpdateTime(time);
        generateQiniuImageAndVideo(clip);
        long orderStart = Long.parseLong(LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"))) * MULTI_BATCH;
        long batchEnd = orderStart + MULTI_BATCH;
        long order = clipRepository.findFirstByIdx(orderStart, batchEnd).map(VideoClip::getIdx).orElse(orderStart);
        clip.setIdx(calculateNextOrder(order));
        return clip2Model(clipRepository.save(clip));
    }

    public VideoClipModel getLastVideoClip(Integer siteId) {
        List<VideoClipModel> list = getVideoClipListFromCache(siteId, 1, 1).getContent();
        return list.isEmpty() ? FAKE_LAST_VIDEO : list.get(0);
    }

    public VideoClipModel getVideoClipById(Boolean allowCache, String clipId) {
        if(allowCache != null && allowCache) {
            return getVideoClipByIdFromCache(clipId);
        }
        return getVideoClipByIdFromDB(clipId);
    }

    public VideoClipModel getVideoClipByIdFromCache(String clipId) {
        VideoClipModel model = cacheService.getVideoClipById(clipId);
        if(model.getId() == null) {
            VideoClip videoClip = clipRepository.findById(clipId).orElse(new VideoClip());
            model = clip2Model(videoClip);
            cacheService.cacheVideoClip(model);

            Long likeCount = cacheService.getVideoClipLikeCount(clipId);
            VideoClipStatModel statModel = new VideoClipStatModel();
            statModel.setLike(likeCount);
            model.setStatModel(statModel);
            //设置所属频率频道
            if (model.getChannelKey() != null) {
                SensorsChannelModel channelModel = channelService.getByKeyFromCache(model.getChannelKey())
                        .dataIgnoreError().orElse(null);
                model.setChannel(channelModel);
            }
        }
        return model;
    }

    public VideoClipModel getVideoClipByIdFromDB(String clipId) {
        VideoClip videoClip = clipRepository.findById(clipId).orElseThrow(() -> new KbException(KbCode.RESOURCE_NOT_FOUND)
            .setReadableMessage("未找到对应数据").li());

        VideoClipModel model = clip2Model(videoClip);
        Long likeCount = cacheService.getVideoClipLikeCount(clipId);
        VideoClipStatModel statModel = new VideoClipStatModel();
        statModel.setLike(likeCount);
        model.setStatModel(statModel);
        //设置所属频率频道
        if (model.getChannelKey() != null) {
            SensorsChannelModel channelModel = channelService.getByKeyFromCache(model.getChannelKey())
                    .dataIgnoreError().orElse(null);
            model.setChannel(channelModel);
        }
        return model;
    }

    public Page<VideoClipModel> getVideoClipList(
        Integer siteId, Integer state, Boolean allowCache, Integer page, Integer pageSize
    ) {
        Page<VideoClipModel> result;
        if(allowCache != null && allowCache) {
            result = getVideoClipListFromCache(siteId, page, pageSize);
        } else {
            result = getVideoClipListFromDB(siteId, state, page, pageSize);
        }
        for (VideoClipModel t : result) {
            if (t.getChannelKey() != null) {
                t.setChannel(channelService.getByKeyFromCache(t.getChannelKey()).dataIgnoreError().orElse(null));
            }
        }
        return result;
    }

    public Page<VideoClipModel> getVideoClipListByTimeRange(
        Integer siteId, Integer state, Long startTime, Long endTime, Integer page, Integer pageSize
    ) {
        Pageable p = createPageable(page, pageSize);
        if (state == null) {
            return clipRepository
                    .findAllBySiteIdAndCreateTimeBetweenOrderByIdDesc(siteId, startTime, endTime, p)
                    .map(this::clip2Model);
        } else {
            return clipRepository
                    .findAllBySiteIdAndStateAndCreateTimeBetweenOrderByIdDesc(siteId, state, startTime, endTime, p)
                    .map(this::clip2Model);
        }
    }

    public Page<VideoClipModel> getVideoClipListFromCache(
        Integer siteId, Integer page, Integer pageSize
    ) {
        Pageable pageable = createPageable(page, pageSize);
        Long onlineCount = onlineCountCache.get(siteId);
        long cacheCount = Math.min(onlineCount == null ? 0L : onlineCount, cacheService.getCacheLimit());
        if ((long) pageable.getPageNumber() * pageable.getPageSize() > cacheCount) {
            return new PageImpl<>(Collections.emptyList(), pageable, cacheCount);
        }

        List<String> ids = getVideoClipIds(siteId, pageable);
        VideoClipCacheService.CacheHitModel listModel = cacheService.getVideoClipListByIds(ids);
        if(listModel.getAllHit()) {
            return new PageImpl<>(listModel.getHitList(), pageable, cacheCount);
        }

        List<VideoClip> clipList = clipRepository.findAllByIdIn(listModel.getNoHitIds());
        Map<String, VideoClipModel> noHitMap = clipList
            .stream()
            .map(this::clip2Model)
            .collect(Collectors.toMap(VideoClipModel::getId, o -> o, (o1, o2) -> o1));
        cacheService.cacheVideoClip(noHitMap);
        listModel.getHitMap().putAll(noHitMap);
        return new PageImpl<>(ids.stream()
            .map(t -> listModel.getHitMap().get(t))
            .collect(Collectors.toList()), pageable, cacheCount);
    }

    public Page<VideoClipModel> getVideoClipListFromDB(
        Integer siteId, Integer state, Integer page, Integer pageSize
    ) {
        if(state == null) {
            return clipRepository.findAllBySiteIdOrderByIdDesc(siteId, createPageable(page, pageSize))
                .map(this::clip2Model);
        }
        return clipRepository.findAllBySiteIdAndStateOrderByIdDesc(siteId, state, createPageable(page, pageSize))
            .map(this::clip2Model);
    }

    public List<String> getVideoClipIds(Integer siteId, Pageable pageable) {
        int start = pageable.getPageNumber() * pageable.getPageSize();
        int end = (pageable.getPageNumber() + 1) * pageable.getPageSize() - 1;
        List<String> ids = cacheService.getVideoClipIds(siteId, start, end);
        if(ids == null || ids.isEmpty()) {
            ids = clipRepository.findAllBySiteIdAndState(siteId, VideoClipState.ONLINE.getValue(), createPageable(1, cacheService.getCacheLimit()))
                .stream()
                .map(VideoClip::getId)
                .collect(Collectors.toList());
            cacheService.cacheVideoClipIds(siteId, ids);
            ids = ids.subList(start, Math.min(end + 1, ids.size()));
        }
        return ids;
    }

    public VideoClipModel updateAsOffline(String clipId) {
        return clip2Model(clipRepository.updateAsOffline(clipId));
    }

    public VideoClipModel updateAsOnline(String clipId) {
        VideoClip videoClip = clipRepository.updateAsOnline(clipId);
        tmuyunService.sendArticle(clip2Article(videoClip));
        return clip2Model(videoClip);
    }

    public VideoClipModel updateVideoClip(VideoClipModel model) {
        verifyModel(model);
        return clip2Model(clipRepository.updateVideoClip(model2Clip(model)));
    }

    public void updateChannel(String id, String channelKey) {
        clipRepository.updateChannel(id, channelKey);
    }

    public void updateSeq(String clipId, Long batch, Integer seq) {
        clipRepository.findById(clipId).ifPresent(clip ->
            clipRepository.updateIdx(clip.getId(), batch * MULTI_BATCH + seq));
    }

    public void like(String clipId) {
        cacheService.incrLikeCount(clipId);
    }

    // ------------------------------------------------

    private ArticleApiModel clip2Article(VideoClip clip) {
        ArticleApiModel model = new ArticleApiModel();
        String refType = TmuyunRefType.HOOLO.toString().toLowerCase();
        model.setPubId(refType + "-" + clip.getId());
        model.setRefId(clip.getId());
        model.setDocTitle(clip.getTitle());
        model.setStatus(TmuyunStatus.ADD.getValue());
        model.setDocType(TmuyunDocType.VIDEO.getValue());
        model.setDocChannel("本地新闻");
        model.setMediaOriginal(clip.getSiteId() == 9 ? TmuyunOriginal.ORIGINAL.getValue() : TmuyunOriginal.NON_ORIGINAL.getValue());
        model.setCreateTime(clip.getCreateTime());
        model.setPubTime(clip.getUpdateTime());
        model.setProductType(TmuyunProductType.APP.getValue());
        model.setRefType(refType);
        model.setSourceSite("开吧客户端");
        model.setContent(StringUtils.isEmpty(clip.getDesc()) ? clip.getTitle() : clip.getDesc());
        model.setMediaUrl(clip.getVideo().getVideoUrl());
        model.setPubUrl(host + "/video-clip/" + clip.getId());
        model.setUserIds(Collections.singletonList(clip.getUserId()));
        return model;
    }

    private VideoClipModel clip2Model(VideoClip clip) {
        VideoClipModel model = processFlag(Mapper.map(clip, VideoClipModel.class));
        model.setFuncFlag(null);
        model.setCover(model.getImage().getImageUrl());
        model.setUrl(model.getVideo().getVideoUrl());
        ShareModel shareModel = new ShareModel();
        shareModel.setTitle(clip.getTitle());
        shareModel.setContent(clip.getDesc());
        shareModel.setImageUrl(model.getCover());
        shareModel.setUrl("https://page.kaiba315.com.cn/video-clip/" + clip.getId());
        model.setShareModel(shareModel);
        parseVideoClipIdx(model);
        return model;
    }

    private VideoClip create2Clip(VideoCreateModel model) {
        VideoClip videoClip = Mapper.map(model, VideoClip.class);
        videoClip.setFuncFlag(generateFlag(model.getShowLike()));
        return videoClip;
    }

    private VideoClip model2Clip(VideoClipModel model) {
        VideoClip videoClip = Mapper.map(model, VideoClip.class);
        videoClip.setFuncFlag(generateFlag(model.getShowLike()));
        return videoClip;
    }

    private void generateQiniuImageAndVideo(VideoClip videoClip) {
        if (videoClip != null && videoClip.getImage() != null) {
            if (videoClip.getHooloImage() == null) {
                Image hooloImage = new Image();
                hooloImage.setUrl(videoClip.getImage().getUrl());
                videoClip.setHooloImage(hooloImage);
            }
            String imageUrl = videoClip.getImage().getImageUrl();
            if (imageUrl != null && !imageUrl.startsWith(MEDIA_PREFIX)) {
                try {
                    String key = qiniuManager.uploadUrl(imageUrl, 500 * 1000 * 1000L);
                    videoClip.getImage().setUrl(MEDIA_PREFIX + key);
                } catch (Exception e) {
                    log.error("video clip replace image url error: {}, {}", videoClip, e.getMessage());
                }
            }
        }
        if (videoClip != null && videoClip.getVideo() != null) {
            if (videoClip.getHooloVideo() == null) {
                videoClip.setHooloVideo(videoClip.getVideo());
            }
            Integer videoLen = videoClip.getVideo().getVideoLen();
            String videoUrl = videoClip.getVideo().getVideoKey();
            if (videoUrl != null && !videoUrl.startsWith(MEDIA_PREFIX)) {
                try {
                    String videoKey = qiniuManager.uploadUrl(videoUrl, 5 * 1000 * 1000 * 1000L);
                    Video qiniuVideo = new Video();
                    qiniuVideo.setVideoKey(MEDIA_PREFIX + videoKey);
                    qiniuVideo.setVideoThumbKey(videoClip.getImage().getImageUrl());
                    qiniuVideo.setVideoLen(videoLen);
                    videoClip.setVideo(qiniuVideo);
                } catch (Exception e) {
                    log.error("video clip replace video url error: {}, {}", videoClip, e.getMessage());
                }
            }
        }
    }

    private void verifyCreate(VideoCreateModel createModel) {
        CREATE_VERIFIER.verify(createModel);
        VIDEO_VERIFIER.verify(createModel.getVideo());
        IMAGE_VERIFIER.verify(createModel.getImage());
    }

    private void verifyModel(VideoClipModel model) {
        VIDEO_MODEL_VERIFIER.verify(model);
        VIDEO_VERIFIER.verify(model.getVideo());
        IMAGE_VERIFIER.verify(model.getImage());
    }

    private static Long calculateNextOrder(Long lastOrder) {
        return (lastOrder / 10 + 1) * 10;
    }

    private static void parseVideoClipIdx(VideoClipModel model) {
        if (model.getIdx() != null && model.getIdx() > 0) {
            model.setBatch(Long.toString(model.getIdx() / MULTI_BATCH));
            model.setSeq((int) (model.getIdx() % MULTI_BATCH));
        }
    }
    
    private static Pageable createPageable(Integer page, Integer pageSize) {
        int p = (page == null || page < 1) ? 0 : page - 1;
        int ps = (pageSize == null) ? 15 : pageSize;
        return PageRequest.of(p, ps);
    }

    private static Long generateFlag(Integer showLike) {
        return VideoClipFuncFlag.Builder.on()
            .setShowLike(showLike)
            .create();
    }

    private static VideoClipModel getFakeLastVideoClip() {
        return new VideoClipModel();
    }

    private static VideoClipModel processFlag(VideoClipModel model) {
        if(model.getFuncFlag() != null) {
            VideoClipFuncFlag.Builder builder = VideoClipFuncFlag.parse(model.getFuncFlag());
            model.setShowLike(builder.getShowLike());
        }
        return model;
    }

    private Long getClipCountBySiteId(Integer siteId) {
        return clipRepository.countBySiteIdAndState(siteId, VideoClipState.ONLINE.getValue());
    }

    private static final Verifier<Image> IMAGE_VERIFIER = new VerifierBuilder<Image>()
            .defaultOrElseThrow()
            .and(F.str(Image::getImageUrl).isUrl().r("无效的封面地址"))
            .create();

    private static final Verifier<Video> VIDEO_VERIFIER = new VerifierBuilder<Video>()
            .defaultOrElseThrow()
            .and(F.intF(Video::getVideoLen).gt(0).r("未输入视频时长"))
            .and(F.str(Video::getVideoUrl).isUrl().r("无效的视频地址"))
            .create();

    private static final Verifier<VideoCreateModel> CREATE_VERIFIER = new VerifierBuilder<VideoCreateModel>()
            .defaultOrElseThrow()
            .and(F.intF(VideoCreateModel::getSiteId).notNull().r("电台不能为空"))
            .and(F.intF(VideoCreateModel::getUserId).notEmpty().r("用户id不可为空"))
            .and(F.intF(VideoCreateModel::getOrientation).enums(VideoClipOrientation.values()))
            .and(F.str(VideoCreateModel::getTitle).notEmpty().r("未输入标题"))
            .create();

    private static final Verifier<VideoClipModel> VIDEO_MODEL_VERIFIER = new VerifierBuilder<VideoClipModel>()
            .defaultOrElseThrow()
            .and(F.intF(VideoClipModel::getSiteId).notNull().r("电台不能为空"))
            .and(F.str(VideoClipModel::getId).notEmpty().r("视频id不可为空"))
            .and(F.intF(VideoClipModel::getUserId).notNull().r("用户id不可为空"))
            .and(F.intF(VideoClipModel::getState).enums(VideoClipState.values()))
            .and(F.intF(VideoClipModel::getOrientation).enums(VideoClipOrientation.values()))
            .and(F.str(VideoClipModel::getTitle).notEmpty().r("未输入标题"))
            .create();
}
