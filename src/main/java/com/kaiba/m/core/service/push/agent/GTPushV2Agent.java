package com.kaiba.m.core.service.push.agent;

import com.getui.push.v2.sdk.ApiHelper;
import com.getui.push.v2.sdk.GtApiConfiguration;
import com.getui.push.v2.sdk.api.PushApi;
import com.getui.push.v2.sdk.api.UserApi;
import com.getui.push.v2.sdk.common.ApiResult;
import com.getui.push.v2.sdk.dto.req.UserDTO;
import com.kaiba.lib.base.constant.push.PushRange;
import com.kaiba.lib.base.domain.push.PushModel;
import com.kaiba.lib.base.response.KbCode;
import com.kaiba.lib.base.response.KbException;
import com.kaiba.lib.base.util.GsonUtils;
import com.kaiba.m.core.domain.push.PushTarget;
import com.kaiba.m.core.repository.push.PushRepository;
import com.kaiba.m.core.service.push.agent.gt.GTPushResult;
import com.kaiba.m.core.service.push.agent.gt.GTPushStrategy;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 个推推送服务，新版本
 * tips:对应个推[开吧-埋点测试demo] 应用
 *
 * <AUTHOR>
 * @version $Id: GTPushV2Agent, v 0.1 2025/6/5 11:26 daopei Exp $
 **/
@Slf4j
@Component
public class GTPushV2Agent {

    private final String appId = "xzc9chqi1s5bPYs3SWtPm2";
    private final String appKey = "ftBYia8mOa8icbTAPZEro7";
    private final String masterSecret = "NWHTrfk4Q09ZPuSiiBjWK2";

    GtApiConfiguration configuration;
    PushApi pushApi;
    UserApi userApi;
    @NonNull
    private final List<GTPushStrategy> strategies;
    @NonNull
    private final PushRepository pushRepository;

    public GTPushV2Agent(
            @NonNull List<GTPushStrategy> strategies,
            @NonNull PushRepository pushRepository
    ) {
        this.strategies = strategies;
        this.pushRepository = pushRepository;

        this.configuration = new GtApiConfiguration();
        this.configuration.setAppId(appId);
        this.configuration.setAppKey(appKey);
        this.configuration.setMasterSecret(masterSecret);
        ApiHelper apiHelper = ApiHelper.build(this.configuration);
        this.pushApi = apiHelper.creatApi(PushApi.class);
        this.userApi = apiHelper.creatApi(UserApi.class);
    }

    private GTPushStrategy getStrategy(PushRange range) {
        for (GTPushStrategy strategy : strategies) {
            if (strategy.support(range)) {
                return strategy;
            }
        }
        throw new KbException(KbCode.ERROR).r("个推推送未找到匹配策略,range:" + range.name());
    }


    public void push(PushModel model, List<PushTarget> targetList) {
        PushRange range = PushRange.valueOf(model.getRange()).orElseThrow(
                () -> new KbException(KbCode.ILLEGAL_ARGUMENT, "unknown range: " + model.getRange()));
        GTPushResult taskResult = getStrategy(range).execute(pushApi, model, targetList);
        pushRepository.updateGtPushNResult(model.getId(), taskResult.getOriginResult(), taskResult.getTaskId());
    }

    /**
     * 批量添加用户标签
     * result : key为cid，value为结果，true表示成功，否则失败
     * @param tag 标签值
     * @param cidSet {@link PushTarget#getPushToken()} 推送用户CID 列表
     */
    public void addUserTagBatch(String tag, Set<String> cidSet) {
        UserDTO userDTO = UserDTO.build();
        userDTO.setCid(cidSet);
        ApiResult<Map<String, String>> result = userApi.usersBindTag(tag, userDTO);
        log.info("addUserTagBatch result:{}", GsonUtils.getGson().toJson(result));
    }

    /**
     * 批量删除用户标签
     * @param tag 标签值
     * @param cidSet {@link PushTarget#getPushToken()} 推送用户CID 列表
     */
    public void deleteUserTagBatch(String tag, Set<String> cidSet) {
        UserDTO userDTO = UserDTO.build();
        userDTO.setCid(cidSet);
        ApiResult<Map<String, String>> result = userApi.deleteUsersTag(tag, userDTO);
        log.info("deleteUserTagBatch result:{}", GsonUtils.getGson().toJson(result));
    }

}
