package com.kaiba.m.core.service.workorder.carsafeguard.handler;

import com.kaiba.m.core.model.safeguard.CarSafeguardCaseContentModel;
import com.kaiba.m.core.model.safeguard.request.CarSafeguardCaseCreateModel;

import java.util.Collection;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @version CarSafeguardContentHandler, v0.1 2024/7/22 14:54 daopei Exp $
 **/
public interface CarSafeguardContentHandler {


    boolean support(String contentType);

    CarSafeguardCaseContentModel getContentById(String id);

    List<CarSafeguardCaseContentModel> getContentById(Collection<String> ids);

    <T extends CarSafeguardCaseCreateModel> CarSafeguardCaseContentModel createContent(T model);

    <T extends CarSafeguardCaseContentModel> Class<T> contentClass();

    default <T extends CarSafeguardCaseCreateModel> List<Long> obtainTag(T model) {
        return Collections.emptyList();
    }


}
