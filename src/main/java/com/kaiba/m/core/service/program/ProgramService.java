package com.kaiba.m.core.service.program;

import com.kaiba.lib.base.constant.program.ProgramThreadType;
import com.kaiba.lib.base.response.KbCode;
import com.kaiba.lib.base.response.KbException;
import com.kaiba.m.core.domain.program.Program;
import com.kaiba.m.core.domain.program.ProgramThread;
import com.kaiba.m.core.domain.program.TopicTmuTitlePrefix;
import com.kaiba.m.core.repository.program.ProgramRepository;
import com.kaiba.m.core.repository.program.ProgramThreadRepository;
import com.kaiba.m.core.repository.program.TopicTmuTitlePrefixRepository;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * author wangsj
 * date 2020-09-01
 */
@Service
public class ProgramService {

    private final ProgramRepository programRepository;
    private final ProgramThreadRepository programThreadRepository;
    private final TopicTmuTitlePrefixRepository topicTmuTitlePrefixRepository;

    private final ProgramCacheService programCacheService;

    public ProgramService(
            ProgramRepository programRepository,
            ProgramThreadRepository programThreadRepository,
            TopicTmuTitlePrefixRepository topicTmuTitlePrefixRepository,
            ProgramCacheService programCacheService
    ) {
        this.programRepository = programRepository;
        this.programThreadRepository = programThreadRepository;
        this.topicTmuTitlePrefixRepository = topicTmuTitlePrefixRepository;
        this.programCacheService = programCacheService;
    }

    public Optional<ProgramThread> getProgramThreadBySiteAndType(Integer siteId, ProgramThreadType programThreadType) {
        return programThreadRepository.findFirstBySiteIdAndType(siteId, programThreadType.getValue());
    }

    public ProgramThread createProgramThread(Integer siteId, String threadId, Integer creatorId, ProgramThreadType threadType) {
        ProgramThread programThread = new ProgramThread();
        programThread.setSiteId(siteId);
        programThread.setThreadId(threadId);
        programThread.setType(threadType.getValue());
        programThread.setCreatorId(creatorId);
        programThread.setCreateTime(System.currentTimeMillis() / 1000);
        return programThreadRepository.save(programThread);
    }

    public void save(Program program) {
        program.setModifyTime(System.currentTimeMillis() / 1000);
        if (program.getReserved() != null  && program.getReserved() == 1) {
            programRepository.findFirstBySiteIdAndReserved(program.getSiteId(), 1).ifPresent(t -> {
                if (!t.getId().equals(program.getId())) {
                    throw new KbException(KbCode.REQUEST_PARAM_INVALID).li().setReadableMessage("已经存在空白档节目");
                }
            });
        }
        programRepository.save(program);
        if (program.getOnline().equals(1)) {
            programCacheService.setProgram(program);
        }
    }

    public Optional<Program> getReservedProgramBySiteId(Integer siteId) {
        return programRepository.findFirstBySiteIdAndReserved(siteId, 1);
    }

    public void changeOnlineOfProgram(String id, Integer userId, Integer isActivity) {
        Optional<Program> optional = programRepository.updateOnlineById(id, userId, isActivity);
        optional.ifPresent(program -> {
            programCacheService.deleteProgram(program.getId());
        });
    }

    public Optional<Program> getBySiteIdAndName(Integer siteId, String name) {
        return programRepository.findBySiteIdAndName(siteId, name);
    }

    public Optional<Program> getProgramById(String id) {
        return getProgramById(id, true);
    }

    public Optional<Program> getProgramById(String id, boolean cached) {
        if (cached) {
            Optional<Program> cache = programCacheService.getProgram(id);
            if (cache.isPresent()) {
                return cache;
            }
        }
        Optional<Program> optional = programRepository.findById(id);
        optional.ifPresent(programCacheService::setProgram);
        return optional;
    }

    public Map<String, Program> getProgramMapByIdIn(Set<String> ids) {
        Map<String, Program> map = programCacheService.getProgramByIdIn(ids);
        Set<String> missed = new HashSet<>();
        for (String id : ids) {
            if (map.get(id) == null) {
                missed.add(id);
            }
        }
        Iterable<Program> list = programRepository.findAllById(missed);
        programCacheService.setProgramList(list);
        for (Program program : list) {
            map.put(program.getId(), program);
        }
        return map;
    }

    public Page<Program> getProgramBySiteIdAndOnline(Integer siteId, Integer online, Integer page, Integer pageSize) {
//        if (online == 1) {
//            Map<String, Program> map = programCacheService.getSiteProgram(siteId);
//            if (map.size() > 0) {
//                return new ArrayList<>(map.values());
//            } else {
//                Page<Program> programList = programRepository.findAllBySiteIdAndOnline(siteId, online, PageRequest.of(page - 1, pageSize));
//                programCacheService.setSiteProgram(siteId, programList);
//                return programList;
//            }
//        } else {
//        }
        return programRepository.findAllBySiteIdAndOnline(siteId, online, PageRequest.of(page - 1, pageSize));
    }

    public void createOrUpdateTopicTmuTitlePrefix(String programId, List<String> suffix) {
        TopicTmuTitlePrefix prefix = new TopicTmuTitlePrefix();
        prefix.setProgramId(programId);
        prefix.setPrefix(suffix);
        topicTmuTitlePrefixRepository.save(prefix);
    }

    public List<String> getTopicTmuTitlePrefixByProgramId(String programId) {
        List<String> prefixList = topicTmuTitlePrefixRepository.findFirstByProgramId(programId)
                .map(TopicTmuTitlePrefix::getPrefix)
                .orElse(Collections.emptyList());
        List<String> retList = new ArrayList<>(prefixList.size() + TOPIC_TMU_TITLE_PREFIX_COMMON_LIST.size());
        retList.addAll(prefixList);
        retList.addAll(TOPIC_TMU_TITLE_PREFIX_COMMON_LIST);
        return retList;
    }

    private static final List<String> TOPIC_TMU_TITLE_PREFIX_COMMON_LIST = Arrays.asList(
            "一路有你",
            "爱上开吧",
            "生活美的很杭州",
            "吧友互动",
            "精彩段子",
            "车上时光",
            "一路畅行",
            "经典时刻"
    );

}
