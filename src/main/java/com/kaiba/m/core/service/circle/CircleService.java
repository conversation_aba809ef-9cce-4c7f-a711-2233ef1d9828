package com.kaiba.m.core.service.circle;

import com.kaiba.lib.base.constant.circle.CircleState;
import com.kaiba.lib.base.constant.circle.CircleType;
import com.kaiba.lib.base.domain.note.NoteThreadModel;
import com.kaiba.lib.base.response.KbCode;
import com.kaiba.lib.base.response.KbException;
import com.kaiba.lib.base.service.INoteService;
import com.kaiba.m.core.domain.circle.CircleThread;
import com.kaiba.m.core.repository.circle.CircleThreadRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * author: lyux
 * date: 19-5-28
 */
@Slf4j
@Service
public class CircleService {

    private final CircleThreadRepository circleThreadRepository;
    private final INoteService noteService;

    public CircleService(
            CircleThreadRepository circleThreadRepository,
            INoteService noteService
    ) {
        this.circleThreadRepository = circleThreadRepository;
        this.noteService = noteService;
    }

    public Optional<CircleThread> getFirstCircleBySiteAndType(Integer siteId, CircleType type) {
        return circleThreadRepository.findFirstBySiteIdAndType(siteId, type.getValue());
    }

    public Optional<CircleThread> getMainCircleBySite(Integer siteId) {
        return getFirstCircleBySiteAndType(siteId, CircleType.PLAZA);
    }

    public List<CircleThread> getCircleThreadListBySite(Integer siteId, Integer page, Integer pageSize) {
        Pageable pageable = PageRequest.of(page == null ? 0 : page - 1, pageSize == null ? 20 : pageSize);
        return circleThreadRepository
                .findBySiteIdOrderByIdAsc(siteId, pageable).getContent().stream()
                .sorted(CIRCLE_THREAD_COMPARATOR)
                .collect(Collectors.toList());
    }

    public CircleThread addThreadToCircle(
            Integer userId, Integer siteId, String threadId, CircleType circleType, Integer order) {
        List<CircleThread> cList = getCircleThreadListBySite(siteId, 1, 10);
        HashSet<String> threadIdSet = new HashSet<>(cList.size() + 1);
        threadIdSet.add(threadId);

        for (CircleThread c : cList) {
            // 如果所要添加的板块已经被添加进圈子, 则视为请求成功.
            if (c.getThreadId().equals(threadId)) {
                return c;
            }
            // 如果要添加的圈子是广场, 则检查广场是否已经存在
            if (circleType == CircleType.PLAZA && c.getType().equals(CircleType.PLAZA.getValue())) {
                throw new KbException(KbCode.ILLEGAL_STATE,
                        "circle already exists. siteId=" + siteId + ", threadId=" + c.getId())
                        .setReadableMessage("广场已存在");
            }
            threadIdSet.add(c.getThreadId());
        }

        Map<String, NoteThreadModel> threadMap = noteService.getThreadMapByIdSet(threadIdSet).dataOrThrow();
        NoteThreadModel thread = threadMap.get(threadId);

        if (thread == null) {
            throw new KbException(KbCode.NOTE_THREAD_NOT_EXISTS, "thread not exists: " + threadId);
        }

        for (CircleThread c : cList) {
            if (thread.getTitle().equals(threadMap.get(c.getThreadId()).getTitle())) {
                throw new KbException(KbCode.ILLEGAL_STATE,
                        "circle already exists. siteId=" + siteId +
                                ", threadId=" + threadId +
                                ", threadTitle=" + thread.getTitle())
                        .setReadableMessage("同名板块已存在");
            }
        }

        CircleThread module = new CircleThread();
        module.setCreatorId(userId);
        module.setSiteId(siteId);
        module.setThreadId(threadId);
        module.setType(circleType.getValue());
        module.setState(CircleState.SHOW.getValue());
        module.setCreateTime(System.currentTimeMillis() / 1000);
        if (null == order) {
            module.setOrder(CircleType.PLAZA == circleType ? 1 : 2);
        } else {
            module.setOrder(order);
        }
        return circleThreadRepository.save(module);
    }

    public void removeThreadToCircle(Integer userId, Integer siteId, String threadId) {
        CircleThread module = circleThreadRepository
                .findFirstByThreadId(threadId)
                .orElseThrow(KbException.supplier(KbCode.CIRCLE_THREAD_NOT_EXISTS));
        if (module.getType() == CircleType.PLAZA.getValue()) {
            throw new KbException(KbCode.ILLEGAL_STATE, "plaza thread cannot be removed!")
                    .setReadableMessage("不能移除广场");
        }
        circleThreadRepository.deleteById(module.getId());
    }

    public void updateCircleThreadOrder(Integer siteId, String threadId, int order) {
        circleThreadRepository.updateOrder(siteId, threadId, order);
    }

    public void updateCircleThreadState(Integer siteId, String threadId, CircleState state) {
        circleThreadRepository.updateState(siteId, threadId, state);
    }

    // ----------------------------------------------------------------------

    private static final Comparator<CircleThread> CIRCLE_THREAD_COMPARATOR = (o1, o2) -> {
        if (o1.getType() != null && o1.getType() == CircleType.PLAZA.getValue()) {
            return -1;
        }
        if (o2.getType() != null && o2.getType() == CircleType.PLAZA.getValue()) {
            return 1;
        }
        int order1 = o1.getOrder() == null ? 0 : o1.getOrder();
        int order2 = o2.getOrder() == null ? 0 : o2.getOrder();
        return order1 - order2;
    };

}
