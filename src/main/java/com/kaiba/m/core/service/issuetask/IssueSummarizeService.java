package com.kaiba.m.core.service.issuetask;

import com.kaiba.lib.base.constant.issue.*;
import com.kaiba.lib.base.domain.issue.IssueModel;
import com.kaiba.lib.base.response.KbCode;
import com.kaiba.lib.base.response.KbException;
import com.kaiba.lib.base.service.IIssueService;
import com.kaiba.m.core.domain.issuetask.IssueTask;
import com.kaiba.m.core.service.issuetask.sms.SmsSender;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

/**
 * author: lyux
 * date: 18-10-13
 */
@Slf4j
@RefreshScope
@Service
public class IssueSummarizeService {

    private static final int CHECK_ISSUE_STEP = 50;
    private static final long ISSUE_TASK_DURATION_OFFSET = TimeUnit.SECONDS.toSeconds(30);

    private final IIssueService issueService;
    private final IssueTaskService issueTaskService;
    private final SmsSender smsSender;

    @Autowired
    public IssueSummarizeService(
            IIssueService issueService,
            IssueTaskService issueTaskService,
            SmsSender smsSender
    ) {
        this.issueService = issueService;
        this.issueTaskService = issueTaskService;
        this.smsSender = smsSender;
    }

    @Async
    public void checkActiveIssues() {
        log.info("check issue all");
        for (int i = 1; ; i++) {
            List<IssueModel> list = issueService
                    .getIssueListByState(
                            IssueState.ACTIVE_ISSUE_STATES, i, CHECK_ISSUE_STEP, 1,
                            IssueMessageSort.ALL_DESC.getValue())
                    .getData();
            if (null == list || list.isEmpty()) {
                break;
            }
            list.forEach(issue -> {
                try {
                    checkIssue(issue);
                } catch (Exception e) {
                    log.warn("check active issue fail for " + issue.getId(), e);
                }
            });
            if (list.size() < CHECK_ISSUE_STEP) {
                break;
            }
        }
    }

    @Async
    public void checkIssue(String issueId) {
        issueService.getIssueBasic(issueId).data().ifPresent(this::checkIssue);
    }

    private void checkIssue(IssueModel issue) {
        long current = System.currentTimeMillis() / 1000;
        IssueState currentState = IssueState.valueOf(issue.getState()).orElseThrow(KbException.supplier(KbCode.ILLEGAL_STATE));
        IssueState state = IssueState.valueOf(issue.getState()).orElseThrow(KbException.supplier(KbCode.ILLEGAL_STATE));
        if (state == IssueState.INIT) {
            if (issue.getTransactionId() != null) {
                issueService.issueStateToPeering(issue.getId(), issue.getTransactionId()).check();
                currentState = IssueState.PEERING;
                issueTaskService.taskStart(issue);
            } else {
                long sinceCreate = current - getLong(issue.getCreateTime());
                if (sinceCreate > IssueConfigs.issueInitDuration) {
                    log.debug("check issue, change state: " + issue.getId() + ", " + state + " -> " + IssueState.CANCELED);
                    issueService.issueStateToCancel(
                            issue.getId(), null, CancelReason.PAY_TIME_UP.getValue(), "支付超时").check();
                    currentState = IssueState.CANCELED;
                }
            }

        } else if (state == IssueState.PEERING) {
            currentState = checkPeeringIssue(issue);

        } else if (state == IssueState.WAITING) {
            Long messageCount = issueService
                    .getMessageCount(issue.getId(), MessageSenderType.EXPERT.getValue())
                    .getData();
            if (null != messageCount && messageCount > 0 && issue.getExpert() != null) {
                log.debug("check issue, change state: " + issue.getId() + ", " + state + " -> " + IssueState.SERVING);
                issueService.issueStateToServing(issue.getId(), issue.getExpert().getId()).check();
                currentState = IssueState.SERVING;
            } else {
                long sinceWaiting = current - getLong(issue.getUpdateTime());
                if (sinceWaiting > IssueConfigs.issueWaitingDuration) {
                    log.debug("check issue, change state: " + issue.getId() + ", " + state + " -> " + IssueState.CANCELED);
                    issueService.issueStateToCancel(
                            issue.getId(), null, CancelReason.EXPERT_NO_RESPONSE.getValue(), "技师未应答").check();
                    currentState = IssueState.CANCELED;
                }
            }

        } else if (state == IssueState.SERVING) {
            long sinceServing = current - getLong(issue.getStartTime());
            if (sinceServing > issue.getDuration()) {
                log.debug("check issue, change state: " + issue.getId() + ", " + state + " -> " + IssueState.SERVED);
                issueService.issueStateToServed(issue.getId()).check();
                currentState = IssueState.SERVED;
            }
        } else if (state == IssueState.SERVED) {
            long sinceEnd = current - getLong(issue.getEndTime());
            if (sinceEnd > IssueConfigs.issueServedDuration) {
                log.debug("check issue, change state: " + issue.getId() + ", " + state + " -> " + IssueState.SEALED);
                issueService.issueStateToSealed(issue.getId()).check();
                currentState = IssueState.SEALED;
            }
        }
        smsSender.trySend(issue, currentState);
    }

    private IssueState checkPeeringIssue(IssueModel issue) {
        long current = System.currentTimeMillis() / 1000;
        long timeout = issue.getTaskType() == null || issue.getTaskType() == IssueTaskType.DISPATCH.getValue() ?
                IssueConfigs.issuePeeringDispatchDuration : IssueConfigs.issuePeeringSpecifyDuration;
        IssueState state = IssueState.valueOf(issue.getState()).orElseThrow(KbException.supplier(KbCode.ILLEGAL_STATE));

        // check peering timeout
        if (current - getLong(issue.getUpdateTime()) > timeout) {
            log.debug("check peering issue, dispatch timeout, change state: " + issue.getId() + ", " +
                    state + " -> " + IssueState.CANCELED);
            issueTaskService.taskCancelBySystem(issue);
            return IssueState.CANCELED;
        }

        // check task
        Optional<IssueTask> op = issueTaskService.getTaskByIssue(issue.getId());
        if (!op.isPresent()) {
            log.debug("check peering issue, no task found. give it a try. issue: " + issue.getId());
            issueTaskService.taskStart(issue);
            return IssueState.PEERING;
        }

        IssueTask task = op.get();
        Integer taskState = task.getState();
        if (taskState == IssueTaskState.ON_GOING.getValue()) {
            long duration = issue.getTaskType() == IssueTaskType.DISPATCH.getValue() ?
                    IssueConfigs.taskDurationDispatch :
                    IssueConfigs.taskDurationSpecify;
            if (current - getLong(task.getUpdateTime() / 1000) > duration + ISSUE_TASK_DURATION_OFFSET) {
                log.debug("check peering issue, " + taskState + ", task exceed duration, try recover. task: " + task.getId());
                issueTaskService.taskRecoverFromOnGoing(issue);
            }

        } else if (taskState == IssueTaskState.NO_EXPERT.getValue() ||
                taskState == IssueTaskState.REFUSED.getValue() ||
                taskState == IssueTaskState.TIMEOUT.getValue()) {
            long retryDelay = issue.getTaskType() == IssueTaskType.DISPATCH.getValue() ?
                    IssueConfigs.taskRetryDelayDispatch : IssueConfigs.taskRetryDelaySpecify;
            if (current - getLong(task.getUpdateTime() / 1000) > retryDelay) {
                log.debug("check peering issue, " + taskState + ", retry peering. task: " + task.getId());
                issueTaskService.taskStart(issue);
            }
        } else if (taskState == IssueTaskState.ACCEPTED.getValue()) {
            log.debug("check peering issue, task already accepted, change state: " + issue.getId() + ", " +
                    state + " -> " + IssueState.WAITING);
            issueService.issueStateToWaiting(issue.getId(), task.getExpertId());
            Long messageCount = issueService
                    .getMessageCount(issue.getId(), MessageSenderType.EXPERT.getValue())
                    .getData();
            if (null != messageCount && messageCount > 0) {
                log.debug("check peering issue, already responded, change state: " + issue.getId() + ", " +
                        state + " -> " + IssueState.SERVING);
                issueService.issueStateToServing(issue.getId(), null).check();
                return IssueState.SERVING;
            }

        } else if (taskState == IssueTaskState.CANCEL.getValue()) {
            log.debug("check peering issue, task already canceled, change state: " + issue.getId() + ", " +
                    state + " -> " + IssueState.CANCELED);
            issueService.issueStateToCancel(
                    issue.getId(), null, CancelReason.OTHER.getValue(), "问答已被取消").check();
            return IssueState.CANCELED;
        }
        return IssueState.PEERING;
    }

    private static long getLong(Long value) {
        return null == value ? 0L : value;
    }

}
