package com.kaiba.m.core.service.note.event.handler;

import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.LoadingCache;
import com.kaiba.lib.base.domain.note.NoteCommentModel;
import com.kaiba.lib.base.domain.note.NoteModel;
import com.kaiba.lib.base.domain.user.UserModel;
import com.kaiba.m.core.domain.circle.CircleThread;
import com.kaiba.m.core.domain.note.Note;
import com.kaiba.m.core.domain.note.NoteComment;
import com.kaiba.m.core.domain.program.ScheduleInstance;
import com.kaiba.m.core.domain.replydict.ReplyContent;
import com.kaiba.m.core.domain.replydict.ReplyKey;
import com.kaiba.m.core.service.circle.CircleService;
import com.kaiba.m.core.service.note.NoteConverter;
import com.kaiba.m.core.service.note.comment.NoteCommentService;
import com.kaiba.m.core.service.note.event.INoteEventReceiver;
import com.kaiba.m.core.service.note.event.NoteCreateOrigin;
import com.kaiba.m.core.service.note.note.NoteService;
import com.kaiba.m.core.service.program.ScheduleInstanceService;
import com.kaiba.m.core.service.replydict.ReplyConstant;
import com.kaiba.m.core.service.replydict.ReplyService;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.util.TextUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 自动回复功能
 * Created by shenxl on 2020/9/17
 */
@Slf4j
@Component
class AutoReplyHandler implements INoteEventReceiver {
    private static final Duration CACHE_EXPIRE_CIRCLE_THREAD = Duration.ofMinutes(10);

    private final ReplyService replyDictService;
    private final NoteService noteService;
    private final NoteConverter noteConverter;
    private final NoteCommentService noteCommentService;
    private final Integer robotUserId;
    private final CircleService circleService;
    private final ScheduleInstanceService scheduleInstanceService;

    private final LoadingCache<Integer, String> circleThreadChecker = Caffeine.newBuilder()
            .expireAfterWrite(CACHE_EXPIRE_CIRCLE_THREAD)
            .build(this::getCircleMainThreadId);

    private final LoadingCache<Integer, List<String>> programThreadChecker = Caffeine.newBuilder()
            .expireAfterWrite(CACHE_EXPIRE_CIRCLE_THREAD)
            .build(this::getProgramThreadIds);

    public AutoReplyHandler(
            ReplyService replyDictService,
            NoteService noteService,
            NoteConverter noteConverter,
            NoteCommentService noteCommentService,
            CircleService circleService,
            @Value("${kaiba.auto-reply.robot}") Integer robotUserId,
            ScheduleInstanceService scheduleInstanceService) {
        this.replyDictService = replyDictService;
        this.noteService = noteService;
        this.noteConverter = noteConverter;
        this.noteCommentService = noteCommentService;
        this.circleService = circleService;
        this.robotUserId = robotUserId;
        this.scheduleInstanceService = scheduleInstanceService;
    }

    @Override
    public boolean isEnabled() {
        return true;
    }

    @Override
    public void onNoteCreated(
            @NonNull Note note, @Nullable NoteModel noteModel,
            UserModel owner, String reviewId, @NonNull NoteCreateOrigin origin) {
        if (origin == NoteCreateOrigin.DELETE_RECOVER
                || noteModel == null
                || TextUtils.isEmpty(note.getContent())
                || note.getThreads() == null
                || note.getSiteId() == null) {
            return;
        }

        String mainCircleThreadId = circleThreadChecker.get(note.getSiteId());
        if (mainCircleThreadId == null || !note.getThreads().contains(mainCircleThreadId)) {
            // 暂时只限定在车友圈和节目（今日排版）内生效
            List<String> programThreadIds = programThreadChecker.get(note.getSiteId());
            if (programThreadIds == null || !programThreadIds.contains(note.getOriginThreadId())) {
                return;
            }
        }

        List<NoteComment> comments = new ArrayList<>();

        List<ReplyKey> keys = replyDictService
                .getReplyKeyListBySiteId(note.getSiteId(), 1, ReplyConstant.KEY_MAX_CACHE_COUNT, true).getContent();
        for (ReplyKey key : keys) {
            if (note.getContent().equals(key.getKey())) {
                if (!TextUtils.isEmpty(key.getGroupId()) && isGroupIdAvailable(key.getGroupId())) {
                    Optional<ReplyContent> replyContent = replyDictService.findFirstBySiteIdAndGroupId(note.getSiteId(), key.getGroupId(), true);
                    replyContent.ifPresent(content -> {
                        NoteComment comment = new NoteComment();
                        comment.setNoteId(note.getId());
                        comment.setSiteId(note.getSiteId());
                        comment.setUserId(robotUserId);
                        comment.setContent(content.getTitle());
                        comment.genReplyLink(content.getLink());
                        comment.setThreads(note.getThreads());
                        comments.add(comment);
                    });
                }
                break;
            }
        }

        noteModel.setCommentCount(noteModel.getCommentCount() == null ? (long) comments.size() : noteModel.getCommentCount() + (long) comments.size());
        noteCommentService.commentsCreate(comments);
        List<NoteCommentModel> commentModels = noteConverter.commentList2model(comments);
        if (noteModel.getCommentList() == null) {
            noteModel.setCommentList(commentModels);
        } else {
            noteModel.getCommentList().addAll(commentModels);
        }
        noteService.noteIncreaseCommentCount(note, commentModels.size());
    }

    private static boolean isGroupIdAvailable(String groupId) {
        if (ReplyConstant.GROUP_TYPES.contains(groupId)) {
            Calendar cal = Calendar.getInstance();
            int w = cal.get(Calendar.DAY_OF_WEEK) - 1;
            if (w < 0) {
                w = 0;
            }
            return w == ReplyConstant.GROUP_TYPES.indexOf(groupId);
        }
        return true;
    }

    private String getCircleMainThreadId(Integer siteId) {
        return circleService.getMainCircleBySite(siteId)
                .map(CircleThread::getThreadId)
                .orElse(null);
    }

    private List<String> getProgramThreadIds(Integer siteId) {
        return scheduleInstanceService.getTodayListBySiteId(siteId).stream()
                .map(ScheduleInstance::getThreadId)
                .collect(Collectors.toList());
    }
}