package com.kaiba.m.core.service.contact.weight;

import com.kaiba.m.core.domain.contact.Contact;
import com.kaiba.m.core.domain.contact.ContactGroup;

/**
 * author: lyux
 * date: 2021-12-13
 *
 * 排序字段 weight 的生成策略. 一般一类联系人适用一种策略, 比如模板地址.
 */
public interface IWeightPolicy {

    /** 根据组设置确定本策略的适用优先级. 会选取按返回值逆序排序的第一个策略. */
    long priority(ContactGroup group);

    /** 生成 {@link Contact#getWeight()} 数值 */
    long calculateWeight(Contact contact);

}

