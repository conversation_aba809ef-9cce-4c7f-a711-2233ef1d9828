package com.kaiba.m.core.service.safeguard;

import com.kaiba.lib.base.response.KbCode;
import com.kaiba.lib.base.response.KbException;
import com.kaiba.m.core.domain.safeguard.Safeguard;
import com.kaiba.m.core.domain.safeguard.SafeguardThread;
import com.kaiba.m.core.repository.safeguard.SafeguardRepository;
import com.kaiba.m.core.repository.safeguard.SafeguardThreadRepository;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2019/8/5
 */
@Service
public class SafeguardService {

    private final SafeguardThreadRepository safeguardThreadRepository;
    private final SafeguardRepository safeguardRepository;

    public SafeguardService(
        SafeguardThreadRepository safeguardThreadRepository,
        SafeguardRepository safeguardRepository
    ) {
        this.safeguardThreadRepository = safeguardThreadRepository;
        this.safeguardRepository = safeguardRepository;
    }

    public Optional<SafeguardThread> getSafeguardThread() {
        List<SafeguardThread> safeguardThreadList = safeguardThreadRepository.findAll(Sort.by(Sort.Direction.DESC, "createTime"));
        if (safeguardThreadList != null && safeguardThreadList.size() > 0) {
            return Optional.of(safeguardThreadList.get(0));
        }
        return Optional.empty();
    }

    public SafeguardThread createSafeguardThread(String threadId, Integer userId) {
        Optional<SafeguardThread> safeguardThread = getSafeguardThread();
        if (safeguardThread.isPresent()) {
            throw new KbException(KbCode.ILLEGAL_STATE,
                    "safeguard thread already exists. threadId=" + safeguardThread.get().getThreadId() + ", safeguardThread id=" + safeguardThread.get().getId())
                    .setReadableMessage("维权板块已存在，请勿重复创建");
        }
        SafeguardThread model = new SafeguardThread();
        model.setThreadId(threadId);
        model.setCreatorId(userId);
        model.setCreateTime(System.currentTimeMillis() / 1000);
        return safeguardThreadRepository.save(model);
    }

    public Optional<Safeguard> getSafeguardById(String id) {
        return safeguardRepository.findById(id);
    }

    public Safeguard updateSafeguardNoteId(String id, String noteId) {
        return safeguardRepository.updateNoteId(id, noteId);
    }

    public Long countSafeguardResolvedCount() {
        return safeguardRepository.countSafeguardResolvedCount();
    }

    public Long countSafeguardProcessingCount() {
        return safeguardRepository.countSafeguardProcessingCount();
    }
}