package com.kaiba.m.core.service.push.agent;

import com.eatthepath.pushy.apns.ApnsClient;
import com.eatthepath.pushy.apns.PushNotificationResponse;
import com.eatthepath.pushy.apns.util.SimpleApnsPushNotification;
import com.eatthepath.pushy.apns.util.TokenUtil;
import com.kaiba.lib.base.constant.KbEndpoint;
import com.kaiba.lib.base.domain.push.PushModel;
import com.kaiba.lib.base.response.KbCode;
import com.kaiba.lib.base.response.KbException;
import com.kaiba.lib.base.util.StringUtils;
import com.kaiba.m.core.domain.push.PushTarget;
import com.kaiba.m.core.service.push.agent.ios.IOSApnsClientProvider;
import com.kaiba.m.core.service.push.agent.ios.IOSPayloadBuilder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.env.Environment;
import org.springframework.core.task.TaskExecutor;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.Collection;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Semaphore;

/**
 * author: Jason
 * date: 18-12-7
 */
@Slf4j
@Component
public class IOSPushAgent {

    @Resource
    private Environment env;
    private boolean isProduct;
    private IOSApnsClientProvider apnsClientProvider;
    private final static int SEMAPHORE_PERMITS = 10_000;
    private Semaphore semaphore = new Semaphore(SEMAPHORE_PERMITS);
    private static boolean FIRST_RUN = true;

    public IOSPushAgent() {
        apnsClientProvider = new IOSApnsClientProvider();
    }

    @PostConstruct
    private void init() {
        isProduct = false;
        for (String profile : env.getActiveProfiles()) {
            if ("product".equals(profile)) {
                isProduct = true;
                break;
            }
        }
    }

    public void push(PushModel model, PushTarget pushTarget) {
        push(model, pushTarget, null);
    }

    @Async
    public void push(PushModel model, Collection<PushTarget> targets){
        targets.forEach(target->{
            push(model, target, null);
        });
    }

    public void push(PushModel model, PushTarget pushTarget, BadTokenCallback c) {
        if (StringUtils.isEmpty(model.getSubTitle())){
            model.setSubTitle(model.getTitle());
        }
        try {
            semaphore.acquire();
        } catch (InterruptedException ignore) {
        }
        final KbEndpoint endpoint = KbEndpoint
                .valueOf(model.getEndpoint())
                .orElseThrow(KbException.supplier(KbCode.ILLEGAL_ARGUMENT));
        ApnsClient apnsClient = apnsClientProvider.get(endpoint, isProduct).orElse(null);
        if (null == apnsClient) {
            log.warn("no valid ApnsClient for " + model);
            return;
        }

        final String payload = new IOSPayloadBuilder()
                .setAlertTitle(model.getTitle())
                .setSound("default")
                .setAlertBody(model.getSubTitle())
                .addCustomProperty("type", "6")//此处主要为了兼容老版本推送的type设置，后续取消掉
                .addCustomProperty("action", model.getAction())
                .addCustomProperty("actionParams", model.getActionParams())
                .setCustomPropertyInAps(true)
                .buildWithDefaultMaximumLength();
        if (StringUtils.isEmpty(pushTarget.getPushToken())) {
            log.warn("no valid ios device token for " + model);
            return;
        }
        final String token = TokenUtil.sanitizeTokenString(pushTarget.getPushToken());
        if (StringUtils.isEmpty(token)) {
            log.warn("no valid apns device token for " + model);
            return;
        }

        SimpleApnsPushNotification n = new SimpleApnsPushNotification(token, endpoint.getIosBundleId(), payload);
        final CompletableFuture<PushNotificationResponse<SimpleApnsPushNotification>> future = apnsClient.sendNotification(n)
                .whenComplete((response, cause) -> {
                    if (c != null && !response.isAccepted() && "BadDeviceToken".equals(response.getRejectionReason())) {
                        c.onBadToken(response.getPushNotification().getToken());
                    }
                    semaphore.release();
                });
        if (FIRST_RUN){ // 此处一言难尽，勿喷、细品
            FIRST_RUN = false;
            try {
                future.get();
            } catch (InterruptedException | ExecutionException ignore) {
            }
        }
    }


    public interface BadTokenCallback {
        void onBadToken(String badToken);
    }


}
