package com.kaiba.m.core.service.wx;

import com.kaiba.lib.base.rte.RelativeTimeExpression;
import com.kaiba.lib.base.util.GsonUtils;
import com.kaiba.m.core.domain.user.User;
import com.kaiba.m.core.domain.wx.WxOpenAppUser;
import com.kaiba.m.core.domain.wx.WxOpenAppUserAuthNotice;
import com.kaiba.m.core.repository.wx.WxOpenAppUserAuthNoticeRepository;
import com.kaiba.m.core.repository.wx.WxOpenAppUserRepository;
import com.kaiba.m.core.service.user.UserService;
import com.kaiba.m.core.util.redis.StringHolderCache;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.time.Duration;

/**
 * 微信开放平台-开吧用户服务
 *
 * <AUTHOR>
 * @version WxOpenUserService, v0.1 2024/5/29 15:44 daopei Exp $
 **/
@Slf4j
@Service
public class WxOpenUserService {

    private final static String AUTH_URL_NOTICE_RTE = "^d+30d";
    private final static String WX_OPEN_USER_CACHE_KEY = "java_wx_open_user_";
    private final static String WX_OPEN_USER_UNION_CACHE_KEY = "java_wx_open_user_unionId_";
    private final static String WX_OPEN_USER_AUTH_NOTICE_CACHE_KEY = "java_wx_open_user_auth_notice_";

    private final UserService userService;
    private final WxOpenAppUserAuthNoticeRepository userAuthRecordRepository;
    private final WxOpenAppUserRepository userRepository;
    private final StringRedisTemplate redisTemplate;
    private final StringHolderCache stringHolderCache;


    public WxOpenUserService(
            UserService userService,
            WxOpenAppUserAuthNoticeRepository userAuthRecordRepository,
            WxOpenAppUserRepository userRepository,
            StringRedisTemplate redisTemplate
    ) {
        this.userService = userService;
        this.userAuthRecordRepository = userAuthRecordRepository;
        this.userRepository = userRepository;
        this.redisTemplate = redisTemplate;
        this.stringHolderCache = new StringHolderCache(redisTemplate);
    }

    public Integer getUserByUnionIdCache(String unionId) {
        if (unionId == null) {
            return null;
        }
        StringHolderCache.Result result = stringHolderCache.get(WX_OPEN_USER_UNION_CACHE_KEY + unionId);
        if (result.isExists()) {
            if (result.getData() == null) {
                return null;
            }
            return Integer.parseInt(result.getData());
        }
        Integer userId = userService.getByWXUnionId(unionId).map(User::getId).orElse(null);
        stringHolderCache.set(WX_OPEN_USER_UNION_CACHE_KEY + unionId, userId == null ? null : userId.toString(), Duration.ofMinutes(60).getSeconds());
        return userId;
    }

    public void authNotice(String openId, Integer siteId) {
        WxOpenAppUserAuthNotice authNotice = userAuthRecordRepository.upsert(openId, siteId, AUTH_URL_NOTICE_RTE, System.currentTimeMillis());
        redisTemplate.opsForValue().set(WX_OPEN_USER_AUTH_NOTICE_CACHE_KEY + openId, GsonUtils.getGson().toJson(authNotice));
    }

    public boolean needNoticeByOpenId(String openId) {
        String cacheValue = redisTemplate.opsForValue().get(WX_OPEN_USER_AUTH_NOTICE_CACHE_KEY + openId);
        if (cacheValue == null) {
            return true;
        }
        WxOpenAppUserAuthNotice userAuth = GsonUtils.getGson().fromJson(cacheValue, WxOpenAppUserAuthNotice.class);
        long nextNoticeTime = RelativeTimeExpression.calculate(userAuth.getNoticeTime(), userAuth.getNoticeRTE());
        return nextNoticeTime < System.currentTimeMillis();
    }

    public void addOpenUser(Integer siteId, String openId, String unionId, Integer userId) {
        log.info("addOpenUser: siteId={}, openId={}, unionId={}, userId={}", siteId, openId, unionId, userId);
        if (openId == null) {
            return;
        }
        userRepository.upsert(siteId, openId, unionId, userId);
        redisTemplate.delete(WX_OPEN_USER_CACHE_KEY + openId);
    }

    public Integer getOpenUserByOpenIdCache(String openId) {
        if (openId == null) {
            return null;
        }
        StringHolderCache.Result result = stringHolderCache.get(WX_OPEN_USER_CACHE_KEY + openId);
        if (result.isExists()) {
            if (result.getData() == null) {
                return null;
            }
            return Integer.parseInt(result.getData());
        }
        Integer userId = userRepository.findByOpenId(openId).map(WxOpenAppUser::getUserId).orElse(null);
        stringHolderCache.set(WX_OPEN_USER_CACHE_KEY + openId, userId == null ? null : userId.toString(), Duration.ofMinutes(60).getSeconds());
        return userId;
    }
}
