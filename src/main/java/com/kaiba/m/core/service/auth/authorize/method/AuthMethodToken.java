package com.kaiba.m.core.service.auth.authorize.method;

import com.kaiba.lib.base.constant.auth.AuthType;
import com.kaiba.lib.base.domain.auth.AuthResult;
import com.kaiba.lib.base.response.KbCode;
import com.kaiba.m.core.service.auth.authorize.IAuthMethod;
import com.kaiba.m.core.util.SpringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.env.Environment;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

/**
 * author: lyux
 * date: 19-8-14
 */
@Component
@Slf4j
public class AuthMethodToken implements IAuthMethod {

    @Resource
    private Environment env;

    private final StringRedisTemplate stringRedisTemplate;

    private boolean isSkipCheck;

    public AuthMethodToken(StringRedisTemplate stringRedisTemplate) {
        this.stringRedisTemplate = stringRedisTemplate;
    }

    @PostConstruct
    private void init() {
        isSkipCheck = SpringUtils.isProfile(env, SpringUtils.PROFILE_LOCAL);
        log.info("auth check token, isSkip: " + isSkipCheck);
    }

    @Override
    public AuthType getAuthType() {
        return AuthType.TOKEN;
    }

    @Override
    public AuthResult checkAuth(Integer userId, String permission, HttpServletRequest request) {
        if (null == request) {
            return AuthResult.asRequireFurtherAuth(permission, getAuthType());
        }
        String token = request.getParameter("token");
        if (null == userId) {
            log.info("auth check token, invalid userId");
            return AuthResult.asAuthFail(permission, KbCode.AUTH_FAIL_TOKEN_CHECK_FAIL, getAuthType());
        }
        if (null == token) {
            log.info("auth check token, null token, for userId: " + userId);
            return AuthResult.asAuthFail(permission, KbCode.AUTH_FAIL_TOKEN_INVALID, getAuthType());
        } else if (token.length() != 32) {
            log.info("auth check token, length invalid, for userId: " + userId + ", token: " + token);
            return AuthResult.asAuthFail(permission, KbCode.AUTH_FAIL_TOKEN_INVALID, getAuthType());
        }
        if (isSkipCheck) {
            return AuthResult.asPass(permission, getAuthType());
        }

        try {
            String cacheKey = getTokenRedisKey(userId);
            String cachedToken = stringRedisTemplate.opsForValue().get(cacheKey);
            if (null == cachedToken) {
                log.info("auth check token, token expired for " + userId);
                return AuthResult.asAuthFail(permission, KbCode.AUTH_FAIL_TOKEN_EXPIRED, getAuthType());
            }
            if (!cachedToken.equals(token)) {
                log.info("auth check token, token invalid for " + userId + ", " + token + " -> " + cachedToken);
                return AuthResult.asAuthFail(permission, KbCode.AUTH_FAIL_TOKEN_INVALID, getAuthType());
            }
        } catch (Exception e) {
            log.info("auth check token, encounter exception: " + userId + " -> " + token, e);
            return AuthResult.asAuthFail(permission, KbCode.AUTH_FAIL_TOKEN_CHECK_FAIL, getAuthType());
        }
        return AuthResult.asPass(permission, getAuthType());
    }

    private static String getTokenRedisKey(Integer userId) {
        return "kb_user_token_" + userId;
    }

}
