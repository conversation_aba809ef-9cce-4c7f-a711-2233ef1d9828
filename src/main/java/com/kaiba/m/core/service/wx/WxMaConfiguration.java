package com.kaiba.m.core.service.wx;

import cn.binarywang.wx.miniapp.api.WxMaService;
import cn.binarywang.wx.miniapp.api.impl.WxMaServiceImpl;
import cn.binarywang.wx.miniapp.config.WxMaConfig;
import cn.binarywang.wx.miniapp.config.impl.WxMaDefaultConfigImpl;
import cn.binarywang.wx.miniapp.config.impl.WxMaRedisBetterConfigImpl;
import cn.binarywang.wx.miniapp.config.impl.WxMaRedisConfigImpl;
import cn.binarywang.wx.miniapp.config.impl.WxMaRedisConnectionConfigImpl;
import com.kaiba.lib.base.constant.wx.WXEntity;
import com.kaiba.lib.base.constant.wx.WXEntityEnum;
import lombok.NonNull;
import me.chanjar.weixin.common.redis.RedisTemplateWxRedisOps;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.core.StringRedisTemplate;

import java.util.HashMap;
import java.util.Map;

/**
 * 微信小程序面向对象配置
 * 可通过Bean<WxMaService>来来使用微信接口
 * accessToken,ticket等密钥默认使用redis存储
 *
 * <AUTHOR>
 * @version WxMaConfiguration, v0.1 2024/6/5 16:35 daopei Exp $
 **/
@Configuration
public class WxMaConfiguration {

    private final StringRedisTemplate redisTemplate;

    public WxMaConfiguration(StringRedisTemplate redisTemplate) {
        this.redisTemplate = redisTemplate;
    }

    /**
     * WxMaConfig配置项说明
     * appId | 微信小程序appId | 必填,基础配置
     * secret | 微信小程序secret | 必填,基础配置
     * token | 微信小程序消息接收 | 非必填,消息接收时使用
     * aesKey | 微信小程序消息接收 | 非必填,消息接收时使用
     *
     * @return
     */
    @Bean
    public WxMaService getWxMaService() {
        WxMaService maService = new WxMaServiceImpl();
        RedisTemplateWxRedisOps wxRedisOps = new RedisTemplateWxRedisOps(redisTemplate);
        WxMaRedisBetterConfigImpl miniConfig = new WxMaRedisBetterConfigImpl(wxRedisOps, "wx_ma");
        //注册报名小程序配置,目前业务仅用于获取accessToken
        WXEntity entity = WXEntityEnum.MINI_SIGN_UP.getEntity();
        miniConfig.setAppid(entity.getAppId());
        miniConfig.setSecret(entity.getSecret());

        Map<String, WxMaConfig> configMap = new HashMap<>();
        configMap.put(entity.getAppId(), miniConfig);
        maService.setMultiConfigs(configMap);
        return maService;
    }
}
