package com.kaiba.m.core.service.push.targetprovider;

import com.kaiba.lib.base.domain.push.PushModel;
import com.kaiba.m.core.domain.push.PushTarget;
import com.kaiba.m.core.service.push.PushTargetService;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.lang.NonNull;

import java.util.Iterator;
import java.util.List;

@Slf4j
@ToString
public class SiteTargetProvider extends BaseTargetProvider {
    private static final int PAGE_SIZE = 1000;
    private PushTargetService pushTargetService;
    private Integer siteId;
    private String deviceType;
    private Integer endPoint;

    private Iterator<PushTarget> pushTargetIterator = new Iterator<PushTarget>() {
        int page;
        Iterator<PushTarget> targetIterator;

        @Override
        public boolean hasNext() {
            if (targetIterator == null || !targetIterator.hasNext()) {
                if (page>0){
                    pushedOnePage(page);
                }
                page = getNextPage();
                if (page <= 0) {
                    return false;
                }
                targetIterator = pushTargetService.getAllBySiteIdAndDeviceTypeAndEndpoint(siteId, deviceType, endPoint, page, PAGE_SIZE).iterator();
            }
            return targetIterator.hasNext();
        }

        @Override
        public PushTarget next() {
            return targetIterator.next();
        }
    };

    public SiteTargetProvider(PushTargetService pushTargetService, PushModel pushModel, String deviceType, StringRedisTemplate redisTemplate) {
        this.pushTargetService = pushTargetService;
        this.siteId = pushModel.targetIdAsInteger(0);
        this.deviceType = deviceType;
        this.endPoint = pushModel.getEndpoint();
        this.pushId = pushModel.getId();
        this.redisTemplate = redisTemplate;
    }

    @NonNull
    @Override
    public Iterator<PushTarget> iterator() {
        return pushTargetIterator;
    }

    @Override
    protected int getTotalPage() {
        int totalCount = pushTargetService.countBySiteIdAndDeviceTypeAndEndpoint(siteId, deviceType, endPoint);
        return (int) Math.ceil((double) totalCount / PAGE_SIZE);
    }
}
