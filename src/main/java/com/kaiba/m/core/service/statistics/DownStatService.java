package com.kaiba.m.core.service.statistics;

import com.kaiba.lib.base.domain.data.datav.DatavTimeBucketStatModel;
import com.kaiba.m.core.domain.statistics.DownStat;
import com.kaiba.m.core.repository.statistics.DownStatRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;


@Slf4j
@Service
public class DownStatService {
    private final DownStatRepository downStatRepository;

    public DownStatService(DownStatRepository downStatRepository) {
        this.downStatRepository = downStatRepository;
    }

    public DownStat add(DownStat downStat) {
        downStat.setTime(System.currentTimeMillis() / 1000);
        return downStatRepository.findById(downStat.getId()).orElseGet(() -> downStatRepository.insert(downStat));
    }

    public List<DatavTimeBucketStatModel> aggrByTimeBetween(Integer siteId, Long st, Long et, Long interval) {
        if (siteId == null) {
            return downStatRepository.aggrByTimeBetweenAndInterval(st, et, interval);
        }
        return downStatRepository.aggrSiteByTimeBetweenAndInterval(siteId, st, et, interval);
    }

    public List<DatavTimeBucketStatModel> aggrByDay(Integer siteId, Long st, Long et) {
        if (siteId == null) {
            return downStatRepository.aggrCountByDay(st, et);
        }
        return downStatRepository.aggrSiteCountByDay(siteId, st, et);
    }
}
