package com.kaiba.m.core.service.videolive;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/7/20
 */
public final class VideoliveConfig {

    private VideoliveConfig() {}

    public static final int PRAISE_MAX_EACH = 20;
    /** 直播间默认人数 */
    public static final int LIVE_DEFAULT_USER_NUMBER = 10;

    // ---------------------------------------

    private static final List<Integer> LIVE_DEFAULT_USER_IDS = Arrays.asList(
        4562610, 4562609, 4562608, 4562606, 4562605, 4562604, 4562603, 4562602, 4562601, 4562600,
        4562599, 4562598, 4562597, 4562596, 4562595, 4562649, 4562647, 4562646, 4562645, 4562644,
        4562643, 4562641, 4562640, 4562639, 4562638, 4562637, 4562636, 4562635, 4562634, 4562633,
        4562632, 4562630, 4562629, 4562628, 4562627, 4562626, 4562625, 4562624, 4562623, 4562622,
        4562621, 4562620, 4562619, 4562618, 4562617, 4562616, 4562615, 4562614, 4562613, 4562612
    );

    public static List<Integer> getRandomUserIds(int count) {
        if (count <= 0 || count > LIVE_DEFAULT_USER_IDS.size()) {
            count = 10;
        }
        List<Integer> shuffledList = new ArrayList<>(LIVE_DEFAULT_USER_IDS);
        Collections.shuffle(shuffledList);
        return shuffledList.subList(0, count);
    }
}
