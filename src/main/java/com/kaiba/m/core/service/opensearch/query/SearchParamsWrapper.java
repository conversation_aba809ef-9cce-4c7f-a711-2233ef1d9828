package com.kaiba.m.core.service.opensearch.query;

import com.aliyun.opensearch.sdk.generated.search.Rank;
import lombok.Getter;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/08/04 15:51
 **/
@Getter
public class SearchParamsWrapper {

    private static final Integer USER_ID_DEFAULT = 8;

    private String queryParams;
    private String filter;
    private Rank rank;
    private Integer userId;

    private SearchQuery searchQuery;
    private SearchFilter searchFilter;
    private SearchRank searchRank;
    private List<String> fetchFields;

    public static SearchParamsWrapper on() {
        SearchParamsWrapper builder = new SearchParamsWrapper();
        builder.searchQuery = SearchQuery.on();
        builder.searchFilter = SearchFilter.on();
        builder.searchRank = SearchRank.on();
        builder.fetchFields = Collections.emptyList();
        builder.userId = USER_ID_DEFAULT;
        return builder;
    }

    public SearchParamsWrapper searchQuery(SearchQuery searchQuery) {
        this.searchQuery = searchQuery;
        return this;
    }

    public SearchParamsWrapper searchFilter(SearchFilter searchFilter) {
        this.searchFilter = searchFilter;
        return this;
    }

    public SearchParamsWrapper searchRank(SearchRank searchRank) {
        this.searchRank = searchRank;
        return this;
    }

    public SearchParamsWrapper setFetchFields(Boolean isManager) {
        this.fetchFields = Boolean.TRUE.equals(isManager) ? RESULT_LIST_BY_MANAGER : RESULT_LIST_BY_USER;
        return this;
    }

    public SearchParamsWrapper setUserId(Integer userId) {
        this.userId = userId;
        return this;
    }

    // ------------------------------------------

    public SearchParamsWrapper create() {
        this.queryParams = searchQuery.create();
        this.filter = searchFilter.create();
        this.rank = searchRank.create();
        return this;
    }

    /** 用户搜索返回内容结果集字段 */
    private static final List<String> RESULT_LIST_BY_USER = Arrays.asList(
            "create_time", "type",
            "title", "content",
            "user_id", "site_id",
            "text", "category",
            "status", "user_name",
            "avatar", "ref_id",
            "visible", "extra_search",
            "extra_unsearch", "signature",
            "extra_field1", "extra_field2"
    );

    /** 管理员搜索返回内容字段 */
    private static final List<String> RESULT_LIST_BY_MANAGER = Arrays.asList(
            "create_time", "type",
            "title", "content",
            "user_id", "site_id",
            "text", "category",
            "status", "user_name",
            "avatar", "ref_id",
            "visible", "extra_search",
            "extra_hidden", "extra_unsearch",
            "signature", "extra_field1",
            "extra_field2"
    );

    /** 搜索用户信息返回内容字段 */
    private static final List<String> USER_LIST_BY_USER = Arrays.asList(
            "user_id", "user_name",
            "avatar", "signature"
    );
}
