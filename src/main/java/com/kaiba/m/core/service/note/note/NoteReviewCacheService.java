package com.kaiba.m.core.service.note.note;

import com.kaiba.lib.base.domain.note.NoteModel;
import com.kaiba.lib.base.domain.note.NoteThreadModel;
import com.kaiba.lib.base.util.GsonUtils;
import com.kaiba.m.core.domain.note.NoteReview;
import com.kaiba.m.core.service.note.NoteConverter;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.connection.StringRedisConnection;
import org.springframework.data.redis.core.RedisCallback;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.retry.annotation.Retryable;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version NoteReviewCacheService, v0.1 2023/9/15 10:21 daopei Exp $
 **/
@Slf4j
@Service
@RequiredArgsConstructor
public class NoteReviewCacheService {

    private final static String NOTE_REVIEW_LIST_CACHE_KEY = "java_note_review_thread_user_cache_";
    private final static String NOTE_REVIEW_CACHE_KEY = "java_note_review_cache_";

    private final static Duration CACHE_LIST_EXPIRE = Duration.ofDays(7);
    private final static Duration CACHE_EXPIRE = Duration.ofDays(30);

    private final static Integer CACHE_SIZE = 20;
    private final static Integer CACHE_LIST_MAX_SIZE = 50;

    @NonNull
    private StringRedisTemplate redisTemplate;
    @NonNull
    private final NoteConverter noteConverter;

    @Async
    @Retryable
    public void addByNoteReview(NoteReview noteReview) {
        NoteModel noteModel = noteConverter.noteReview2model(noteReview);
        add(noteModel);
    }

    private void add(NoteModel noteModel) {
        redisTemplate.opsForValue().set(NOTE_REVIEW_CACHE_KEY + noteModel.getNoteReviewId(), GsonUtils.getGson().toJson(noteModel), CACHE_EXPIRE);
        List<String> keys = threadUserKeyList(noteModel.getThreads().stream().map(NoteThreadModel::getId).collect(Collectors.toList()), noteModel.getUserId());
        redisTemplate.executePipelined((RedisCallback<Object>) connection -> {
            StringRedisConnection conn = (StringRedisConnection) connection;
            for (String key : keys) {
                conn.lRem(key, 0, noteModel.getNoteReviewId());
                conn.lPush(key, noteModel.getNoteReviewId());
                conn.expire(key, CACHE_LIST_EXPIRE.getSeconds());
                Long size = conn.lLen(key);
                if (size != null && size > CACHE_LIST_MAX_SIZE) {
                    conn.lTrim(key, 0, CACHE_LIST_MAX_SIZE - 1);
                }
            }
            return null;
        });
    }

    public void delete(List<String> threadIds, Integer userId, String noteReviewId) {
        if (noteReviewId == null) {
            return;
        }
        List<String> keys = threadUserKeyList(threadIds, userId);
        for (String threadKey : keys) {
            if (redisTemplate.hasKey(threadKey)) {
                redisTemplate.opsForList().remove(threadKey, 0, noteReviewId);
            }
        }
        if (redisTemplate.hasKey(NOTE_REVIEW_CACHE_KEY + noteReviewId)) {
            redisTemplate.delete(NOTE_REVIEW_CACHE_KEY + noteReviewId);
        }
    }

    public List<NoteModel> getNoteReview(String threadId, Integer userId, Long startTime, Long endTime) {
        if (threadId == null || userId == null) {
            return Collections.emptyList();
        }
        String key = threadUserKey(threadId, userId);
        List<String> listValue = redisTemplate.opsForList().range(key, 0, CACHE_SIZE);
        if (listValue == null || listValue.isEmpty()) {
            return Collections.emptyList();
        }
        List<NoteModel> noteReviews = new ArrayList<>();
        for (String noteReviewId : listValue) {
            String noteReviewStr = redisTemplate.opsForValue().get(NOTE_REVIEW_CACHE_KEY + noteReviewId);
            if (noteReviewStr != null) {
                noteReviews.add(GsonUtils.getGson().fromJson(noteReviewStr, NoteModel.class));
            }
        }
        noteReviews.sort(noteReviewComparator);
        if (startTime != null && endTime != null) {
            return noteReviews.stream().filter(nr -> nr.getCreateTime() > startTime && nr.getCreateTime() < endTime).collect(Collectors.toList());
        } else {
            return noteReviews;
        }
    }

    public List<NoteModel> getNoteModel(String threadId, Integer userId, Long startTime, Long endTime) {
        return getNoteReview(threadId, userId, startTime, endTime);
    }


    //------------------------------------------------------------------------


    private String threadUserKey(String threadId, Integer userId) {
        return NOTE_REVIEW_LIST_CACHE_KEY + threadId + "_" + userId;
    }

    private List<String> threadUserKeyList(List<String> threadIds, Integer userId) {
        return threadIds.stream().map(s -> threadUserKey(s, userId)).collect(Collectors.toList());
    }

    private static Comparator<NoteModel> noteReviewComparator =
            (o1, o2) ->
                    (int) (o2.getCreateTime() - o1.getCreateTime());

}
