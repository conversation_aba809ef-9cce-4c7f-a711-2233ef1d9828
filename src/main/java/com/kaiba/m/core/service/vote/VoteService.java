package com.kaiba.m.core.service.vote;

import com.kaiba.lib.base.constant.user.UserScoreConst;
import com.kaiba.lib.base.constant.vote.VoteCountStyle;
import com.kaiba.lib.base.constant.vote.VoteStyle;
import com.kaiba.m.core.domain.Vote.Vote;
import com.kaiba.m.core.domain.Vote.VoteOption;
import com.kaiba.m.core.domain.Vote.VoteUserOption;
import com.kaiba.m.core.repository.vote.VoteOptionRepository;
import com.kaiba.m.core.repository.vote.VoteRepository;
import com.kaiba.m.core.repository.vote.VoteUserOptionRepository;
import com.kaiba.m.core.service.user.UserScoreService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * <AUTHOR>
 * @date 2019/10/14
 */
@Service
public class VoteService {

    private final VoteRepository voteRepository;
    private final VoteOptionRepository voteOptionRepository;
    private final VoteUserOptionRepository voteUserOptionRepository;

    private final UserScoreService userScoreService;

    @Autowired
    public VoteService(VoteRepository voteRepository, VoteOptionRepository voteOptionRepository, VoteUserOptionRepository voteUserOptionRepository, UserScoreService userScoreService) {
        this.voteRepository = voteRepository;
        this.voteOptionRepository = voteOptionRepository;
        this.voteUserOptionRepository = voteUserOptionRepository;
        this.userScoreService = userScoreService;
    }

    public Vote createVote(Vote vote) {
        vote.setCreateTime(System.currentTimeMillis() / 1000);
        if (vote.getStyle() == null) {
            vote.setStyle(VoteStyle.LIST.name());
        }
        if (vote.getCountStyle() == null) {
            vote.setCountStyle(VoteCountStyle.DIGIT.name());
        }
        if (vote.getVoteScale() == null) {
            vote.setVoteScale(DEFAULT_VOTE_SCALE);
        }
        if (vote.getShowPreVote() == null) {
            vote.setShowPreVote(false);
        }
        if (vote.getAllowModify() == null) {
            vote.setAllowModify(false);
        }
        return voteRepository.insert(vote);
    }

    public void removeVoteOption(String voteOptionId) {
        voteOptionRepository.deleteById(voteOptionId);
    }

    public Vote updateVoteScale(String voteId, Integer voteScale) {
        return voteRepository.updateVoteScale(voteId, voteScale);
    }

    public Optional<Vote> getVoteById(String voteId) {
        return voteRepository.findById(voteId);
    }

    public List<Vote> getVoteIn(String[] voteIds) {
        if (null == voteIds || voteIds.length == 0) {
            return Collections.emptyList();
        } else {
            return voteRepository.findByIdIn(voteIds);
        }
    }

    public List<VoteOption> createVoteOption(String voteId, String[] options) {
        List<VoteOption> voteOptionList = new ArrayList<>(options.length);
        for (String option : options) {
            VoteOption opt = new VoteOption();
            opt.setVoteId(voteId);
            opt.setTitle(option);
            voteOptionList.add(opt);
        }
        return voteOptionRepository.saveAll(voteOptionList);
    }

    public List<VoteOption> getVoteOptionByVoteId(String voteId) {
        return voteOptionRepository.findByVoteId(voteId);
    }

    public List<VoteOption> getVoteOptionInVoteIds(String[] voteIds) {
        if (null == voteIds || voteIds.length == 0) {
            return Collections.emptyList();
        } else {
            return voteOptionRepository.findByVoteIdIn(voteIds);
        }
    }


    public VoteUserOption saveVoteUserOption(String voteId, Integer userId, String[] options) {
        VoteUserOption voteUserOption = new VoteUserOption();
        voteUserOption.setVoteId(voteId);
        voteUserOption.setUserId(userId);
        voteUserOption.setOptions(Arrays.asList(options));
        voteUserOption.setCreateTime(System.currentTimeMillis() / 1000);
        //TODO 允许用户修改的投票 会存在重复加积分的问题
        userScoreService.addScore(userId, UserScoreConst.TOPIC_VOTE, null);
        return voteUserOptionRepository.vote(voteUserOption);
    }

    public Optional<VoteUserOption> getVoteUserOptionByVoteAndUser(String voteId, Integer userId) {
        return voteUserOptionRepository.findByVoteIdAndUserId(voteId, userId);
    }

    public Vote updateVote(Vote vote) {
        return voteRepository.updateVote(vote);
    }

    public List<VoteOption> updateVoteOption(List<VoteOption> options) {
        return voteOptionRepository.saveAll(options);
    }

    private final int DEFAULT_VOTE_SCALE = 3;

}
