package com.kaiba.m.core.service.appcomponent.home;

import com.kaiba.lib.base.assembler.Mapper;
import com.kaiba.lib.base.constant.apphome.AppHomeTabType;
import com.kaiba.lib.base.constant.applego.AppComponentState;
import com.kaiba.lib.base.constant.common.KbImageFormat;
import com.kaiba.lib.base.domain.apphome.AppHomeFrameModel;
import com.kaiba.lib.base.domain.apphome.AppHomeFrameTab;
import com.kaiba.lib.base.domain.apphome.AppHomeFrameUpdateModel;
import com.kaiba.lib.base.lang.verifier.F;
import com.kaiba.lib.base.lang.verifier.Verifier;
import com.kaiba.lib.base.lang.verifier.VerifierBuilder;
import com.kaiba.m.core.domain.apphome.AppHomeFrame;

import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @version AppHomeFrameModelHelper, v0.1 2023/8/1 10:32 daopei Exp $
 **/
public class AppHomeFrameModelHelper {


    public static AppHomeFrame model2Instance(AppHomeFrameModel model) {
        return Mapper.map(model,AppHomeFrame.class);
    }

    public static AppHomeFrameModel instance2Model(AppHomeFrame instance) {
        AppHomeFrameModel model = Mapper.map(instance,AppHomeFrameModel.class);
        fillTabKey(model);
        return model;
    }

    public static AppHomeFrame updateModel2Instance(AppHomeFrameUpdateModel model) {
        return Mapper.map(model,AppHomeFrame.class);
    }

    static final Verifier<AppHomeFrame> INSTANCE_CREATE_VERIFIER = new VerifierBuilder<AppHomeFrame>().defaultOrElseThrow()
            .and(F.intF(AppHomeFrame::getSiteId).notNull())
            .and(F.intF(AppHomeFrame::getState).notNull())
            .and(F.or(F.str(AppHomeFrame::getHeadBkgImage).notNull(), F.str(AppHomeFrame::getHeadBkgColor).notNull()))
            .and(F.or(F.str(AppHomeFrame::getFootBkgImage).notNull(), F.str(AppHomeFrame::getFootBkgColor).notNull()))
            .ifNotNull(F.str(AppHomeFrame::getHeadBkgImage).isUrl())
            .ifNotNull(F.str(AppHomeFrame::getFootBkgImage).isUrl())
            .ifNotNull(F.str(AppHomeFrame::getHeadBkgColor).match(Pattern.compile("^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{8})$")))
            .ifNotNull(F.str(AppHomeFrame::getFootBkgColor).match(Pattern.compile("^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{8})$")))
            .and(F.obj(AppHomeFrame::getTab1).notNull())
            .and(F.obj(AppHomeFrame::getTab2).notNull())
            .and(F.obj(AppHomeFrame::getTab3).notNull())
            .and(F.obj(AppHomeFrame::getTab4).notNull())
            .create();

    static final Verifier<AppHomeFrameTab> INSTANCE_FRAME_TAB_VERIFIER = new VerifierBuilder<AppHomeFrameTab>().defaultOrElseThrow()
            .and(F.str(AppHomeFrameTab::getIconOff).isUrl())
            .and(F.str(AppHomeFrameTab::getIconOn).isUrl())
            .and(F.str(AppHomeFrameTab::getType).notNull())
            .and(F.str(AppHomeFrameTab::getIconOffFormat).enums(KbImageFormat.values()).r("未知图片类型"))
            .and(F.str(AppHomeFrameTab::getIconOnFormat).enums(KbImageFormat.values()).r("未知图片类型"))
            .and(F.str(AppHomeFrameTab::getTitleOn).notNull())
            .and(F.str(AppHomeFrameTab::getTitleOff).notNull())
            .and(F.str(AppHomeFrameTab::getTextColorOn).match(Pattern.compile("^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{8})$")))
            .and(F.str(AppHomeFrameTab::getTextColorOff).match(Pattern.compile("^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{8})$")))
            .create();


    static final Verifier<AppHomeFrame> INSTANCE_UPDATE_VERIFIER = new VerifierBuilder<AppHomeFrame>().defaultOrElseThrow()
            .and(F.str(AppHomeFrame::getId).notNull())
            .and(F.or(F.str(AppHomeFrame::getHeadBkgImage).notNull(), F.str(AppHomeFrame::getHeadBkgColor).notNull()))
            .and(F.or(F.str(AppHomeFrame::getFootBkgImage).notNull(), F.str(AppHomeFrame::getFootBkgColor).notNull()))
            .ifNotNull(F.str(AppHomeFrame::getHeadBkgImage).isUrl())
            .ifNotNull(F.str(AppHomeFrame::getFootBkgImage).isUrl())
            .ifNotNull(F.str(AppHomeFrame::getHeadBkgColor).match(Pattern.compile("^#\\w+")))
            .ifNotNull(F.str(AppHomeFrame::getFootBkgColor).match(Pattern.compile("^#\\w+")))
            .and(F.obj(AppHomeFrame::getTab1).notNull())
            .and(F.obj(AppHomeFrame::getTab2).notNull())
            .and(F.obj(AppHomeFrame::getTab3).notNull())
            .and(F.obj(AppHomeFrame::getTab4).notNull())
            .create();

    static final Verifier<AppHomeFrame> INSTANCE_STATE_VERIFIER = new VerifierBuilder<AppHomeFrame>().defaultOrElseThrow()
            .and(F.intF(AppHomeFrame::getState).noteEq(AppComponentState.ONLINE.getValue()))
            .create();

    private static void fillTabKey(AppHomeFrameModel model) {
        if (model.getTab1() != null && model.getTab1().getTabKey() == null) {
            String defaultTabKey = AppHomeTabType.resolveByNameIgnoreCase(model.getTab1().getType())
                    .map(AppHomeTabType::getDefaultTabKey)
                    .orElse(null);
            model.getTab1().setTabKey(defaultTabKey);
        }
        if (model.getTab2() != null && model.getTab2().getTabKey() == null) {
            String defaultTabKey = AppHomeTabType.resolveByNameIgnoreCase(model.getTab2().getType())
                    .map(AppHomeTabType::getDefaultTabKey)
                    .orElse(null);
            model.getTab2().setTabKey(defaultTabKey);
        }
        if (model.getTab3() != null && model.getTab3().getTabKey() == null) {
            String defaultTabKey = AppHomeTabType.resolveByNameIgnoreCase(model.getTab3().getType())
                    .map(AppHomeTabType::getDefaultTabKey)
                    .orElse(null);
            model.getTab3().setTabKey(defaultTabKey);
        }
        if (model.getTab4() != null && model.getTab4().getTabKey() == null) {
            String defaultTabKey = AppHomeTabType.resolveByNameIgnoreCase(model.getTab4().getType())
                    .map(AppHomeTabType::getDefaultTabKey)
                    .orElse(null);
            model.getTab4().setTabKey(defaultTabKey);
        }
        if (model.getTab5() != null && model.getTab5().getTabKey() == null) {
            String defaultTabKey = AppHomeTabType.resolveByNameIgnoreCase(model.getTab5().getType())
                    .map(AppHomeTabType::getDefaultTabKey)
                    .orElse(null);
            model.getTab5().setTabKey(defaultTabKey);
        }
        if (model.getTab6() != null && model.getTab6().getTabKey() == null) {
            String defaultTabKey = AppHomeTabType.resolveByNameIgnoreCase(model.getTab6().getType())
                    .map(AppHomeTabType::getDefaultTabKey)
                    .orElse(null);
            model.getTab6().setTabKey(defaultTabKey);
        }
    }
}
