package com.kaiba.m.core.service.appcomponent.home;

import com.github.benmanes.caffeine.cache.CacheLoader;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.LoadingCache;
import com.kaiba.lib.base.constant.KbModule;
import com.kaiba.lib.base.constant.apphome.AppHomeTabBubbleState;
import com.kaiba.lib.base.domain.apphome.AppTabBubbleModel;
import com.kaiba.lib.base.middleware.adminlogrecorder.AdminLogAction;
import com.kaiba.lib.base.middleware.adminlogrecorder.AdminLogRecorder;
import com.kaiba.lib.base.rte.RelativeTimeExpression;
import com.kaiba.lib.base.service.IAdminLogService;
import com.kaiba.m.core.domain.apphome.AppTabBubble;
import com.kaiba.m.core.model.app.AppTabBubbleParam;
import com.kaiba.m.core.repository.appcomponent.home.AppTabBubbleRepository;
import lombok.extern.slf4j.Slf4j;
import org.checkerframework.checker.nullness.qual.NonNull;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.util.*;
import java.util.stream.Collectors;

/**
 * author: lyux
 * date: 2023-10-10
 */
@Slf4j
@Service
public class AppTabBubbleCacheService {

    private final AppTabBubbleRepository bubbleRepository;
    private final LoadingCache<Integer, SiteBubbleCacheModel> cache;
    private final AdminLogRecorder adminLogRecorder;

    public AppTabBubbleCacheService(
            AppTabBubbleRepository bubbleRepository,
            IAdminLogService adminLogService
    ) {
        this.bubbleRepository = bubbleRepository;
        this.adminLogRecorder = AdminLogRecorder
                .builder(adminLogService)
                .module(KbModule.APP_LEGO)
                .unit(AppHomeConsts.ADMIN_LOG_UNIT_TAB_BUBBLE, "首页框架 TAB 栏气泡")
                .create();
        this.cache = Caffeine.newBuilder()
                .expireAfterWrite(Duration.ofMinutes(15))
                .refreshAfterWrite(Duration.ofSeconds(11))
                .build(new CacheLoader<Integer, SiteBubbleCacheModel>() {
                    @Override
                    public SiteBubbleCacheModel load(@NonNull Integer siteId) {
                        return generateCache(siteId, true);
                    }

                    @Override
                    public SiteBubbleCacheModel reload(@NonNull Integer siteId, @NonNull SiteBubbleCacheModel oldValue) {
                        return generateCache(siteId, false);
                    }
                });
    }

    public Optional<AppTabBubbleModel> getOnlineBubbleByTab(Integer siteId, String tab) {
        SiteBubbleCacheModel model = cache.get(siteId);
        if (model == null) {
            return Optional.empty();
        } else {
            return Optional.ofNullable(model.apiBubbleByTabMap.get(tab));
        }
    }

    public Optional<AppTabBubbleModel> getOnlineBubbleByTabAndClearRecord(
            Integer siteId, String tab, AppTabBubbleParam.ClearRecord record) {
        SiteBubbleCacheModel model = cache.get(siteId);
        if (model == null || model.bubbleList.size() == 0) {
            return Optional.empty();
        } else {
            AppTabBubble bubble = model.bubbleByTabMap.get(tab);
            if (determineBubbleByClearRecord(bubble.getId(), bubble.getReappearRTE(), record)) {
                return Optional.ofNullable(model.apiBubbleByTabMap.get(bubble.getTab()));
            } else {
                return Optional.empty();
            }
        }
    }

    public List<AppTabBubbleModel> getOnlineBubbleList(Integer siteId) {
        SiteBubbleCacheModel model = cache.get(siteId);
        if (model == null || model.bubbleList.size() == 0) {
            return Collections.emptyList();
        } else {
            return model.apiBubbleList;
        }
    }

    public List<AppTabBubbleModel> getOnlineBubbleListByClearRecord(AppTabBubbleParam param) {
        SiteBubbleCacheModel model = cache.get(param.getSiteId());
        if (model == null || model.bubbleList.size() == 0) {
            return Collections.emptyList();
        } else if (param.getClearRecords() == null || param.getClearRecords().size() == 0) {
            return model.apiBubbleList;
        } else {
            List<AppTabBubbleModel> retList = new LinkedList<>();
            Map<String, AppTabBubbleParam.ClearRecord> recordMap = param.getClearRecords().stream().collect(
                    Collectors.toMap(AppTabBubbleParam.ClearRecord::getTab, record -> record, (r1, r2) -> r1));
            for (AppTabBubble bubble : model.bubbleList) {
                AppTabBubbleParam.ClearRecord record = recordMap.get(bubble.getTab());
                if (determineBubbleByClearRecord(bubble.getId(), bubble.getReappearRTE(), record)) {
                    retList.add(model.apiBubbleByTabMap.get(bubble.getTab()));
                }
            }
            return retList;
        }
    }

    // -------------------------------------------------------------------

    private boolean determineBubbleByClearRecord(
            String bubbleId, String reappearRTE, AppTabBubbleParam.ClearRecord record) {
        if (record == null || record.getId() == null || record.getClearTime() == null) {
            return true;
        } else if (!record.getId().equals(bubbleId)) {
            return true;
        } else if (reappearRTE != null) {
            long time = RelativeTimeExpression.calculate(record.getClearTime(), reappearRTE);
            return System.currentTimeMillis() > time;
        } else {
            return false;
        }
    }

    private SiteBubbleCacheModel generateCache(Integer siteId, boolean transmitExpired) {
        List<AppTabBubble> list = bubbleRepository
                .findBySiteIdAndStateOrderByIdDesc(siteId, AppHomeTabBubbleState.ONLINE.name());
        if (list.size() == 0) {
            return new SiteBubbleCacheModel(null);
        }
        if (transmitExpired) {
            List<AppTabBubble> trimmedList = new LinkedList<>();
            for (AppTabBubble bubble : list) {
                if (bubble.getEndTime() == null) {
                    trimmedList.add(bubble);
                    continue;
                }
                long now = System.currentTimeMillis();
                if (bubble.getEndTime() + AppHomeConsts.TAB_BUBBLE_EXPIRE_OFFSET < now) {
                    // 距离结束时间已超过指定时间, 下线该气泡
                    AppTabBubble updated = bubbleRepository
                            .updateStateById(bubble.getId(), AppHomeTabBubbleState.OFFLINE.name());
                    log.info("tab bubble state offline by system: " + updated);
                    adminLogRecorder.on()
                            .act(AdminLogAction.UPDATE_STATE, "自动下线实例")
                            .bySystem().ref1(updated.getId()).add();
                } else {
                    trimmedList.add(bubble);
                }
            }
            return new SiteBubbleCacheModel(trimmedList);
        } else {
            return new SiteBubbleCacheModel(list);
        }
    }

    // -------------------------------------------------------------------

    private static class SiteBubbleCacheModel {
        private final List<AppTabBubble> bubbleList;
        private final Map<String, AppTabBubble> bubbleByTabMap;
        private final List<AppTabBubbleModel> apiBubbleList;
        private final Map<String, AppTabBubbleModel> apiBubbleByTabMap;

        public SiteBubbleCacheModel(List<AppTabBubble> bubbleList) {
            if (bubbleList == null || bubbleList.size() == 0) {
                this.bubbleList = Collections.emptyList();
                this.bubbleByTabMap = Collections.emptyMap();
                this.apiBubbleList = Collections.emptyList();
                this.apiBubbleByTabMap = Collections.emptyMap();
            } else {
                long now = System.currentTimeMillis();
                this.bubbleList = new ArrayList<>(bubbleList.size());
                this.bubbleByTabMap = new HashMap<>(bubbleList.size());
                this.apiBubbleList = new ArrayList<>(bubbleList.size());
                this.apiBubbleByTabMap = new HashMap<>(bubbleList.size());
                for (AppTabBubble bubble : bubbleList) {
                    long startTime = bubble.getStartTime() == null ? 0 : bubble.getStartTime();
                    long endTime = bubble.getEndTime() == null ? Long.MAX_VALUE : bubble.getEndTime();
                    if (now > startTime && now < endTime) {
                        if (this.bubbleByTabMap.get(bubble.getTab()) == null) {
                            this.bubbleList.add(bubble);
                            this.bubbleByTabMap.put(bubble.getTab(), bubble);
                            AppTabBubbleModel apiModel = new AppTabBubbleModel();
                            apiModel.setId(bubble.getId());
                            apiModel.setTab(bubble.getTab());
                            apiModel.setText(bubble.getText());
                            this.apiBubbleList.add(apiModel);
                            this.apiBubbleByTabMap.putIfAbsent(bubble.getTab(), apiModel);
                        }
                    }
                }
            }
        }
    }

}
