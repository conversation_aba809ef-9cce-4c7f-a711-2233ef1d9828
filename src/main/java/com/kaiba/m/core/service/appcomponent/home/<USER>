package com.kaiba.m.core.service.appcomponent.home;

import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.LoadingCache;
import com.kaiba.lib.base.constant.applego.AppComponentState;
import com.kaiba.lib.base.domain.apphome.AppHomeFrameModel;
import com.kaiba.m.core.middleware.instantcache.KbInstantCaffeineService;
import com.kaiba.m.core.repository.appcomponent.home.AppHomeFrameRepository;
import org.springframework.stereotype.Component;

import java.time.Duration;
import java.util.Optional;

/**
 * <AUTHOR>
 * @version AppHomeFrameCacheService, v0.1 2023/8/1 11:52 daopei Exp $
 **/
@Component
public class AppHomeFrameCacheService {


    private final AppHomeFrameRepository appHomeFrameRepository;

    private final LoadingCache<Integer, AppHomeFrameModel> cache;
    private final LoadingCache<Integer, AppHomeFrameModel> betaCache;

    public AppHomeFrameCacheService(
            AppHomeFrameRepository appHomeFrameRepository,
            KbInstantCaffeineService kbInstantCaffeineService
    ) {
        this.appHomeFrameRepository = appHomeFrameRepository;
        this.cache = kbInstantCaffeineService.registerInstantCache("app_home_frame_cache",
                Caffeine.newBuilder()
                        .expireAfterWrite(Duration.ofMinutes(30))
                        .refreshAfterWrite(Duration.ofMinutes(3))
                        .build(this::getOnlineInstanceFromDB));
        this.betaCache = kbInstantCaffeineService.registerInstantCache("app_home_frame_beta_cache",
                Caffeine.newBuilder()
                        .expireAfterWrite(Duration.ofMinutes(30))
                        .refreshAfterWrite(Duration.ofMinutes(3))
                        .build(this::getBetaInstanceFromDB));
    }

    /**
     * 查询上线状态的实例数据
     */
    public Optional<AppHomeFrameModel> getOnlineInstanceBySiteId(Integer siteId) {
        return Optional.ofNullable(cache.get(siteId));
    }

    public Optional<AppHomeFrameModel> getBetaInstanceBySiteId(Integer siteId) {
        return Optional.ofNullable(betaCache.get(siteId));
    }


    private AppHomeFrameModel getOnlineInstanceFromDB(Integer siteId) {
        return appHomeFrameRepository
                .findFirstBySiteIdAndStateOrderByOnlineTimeDesc(siteId, AppComponentState.ONLINE.getValue())
                .map(AppHomeFrameModelHelper::instance2Model)
                .orElse(null);
    }

    private AppHomeFrameModel getBetaInstanceFromDB(Integer siteId) {
        return appHomeFrameRepository
                .findFirstBySiteIdAndStateOrderByCreateTimeDesc(siteId, AppComponentState.BETA.getValue())
                .map(AppHomeFrameModelHelper::instance2Model)
                .orElse(null);
    }
}
