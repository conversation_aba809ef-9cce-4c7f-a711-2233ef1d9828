package com.kaiba.m.core.service.actmix;

import com.kaiba.lib.base.constant.actmix.ItemState;
import com.kaiba.lib.base.constant.actmix.ItemType;
import com.kaiba.lib.base.domain.common.Image;
import com.kaiba.lib.base.response.KbCode;
import com.kaiba.lib.base.response.KbException;
import com.kaiba.lib.base.util.StringUtils;
import com.kaiba.lib.base.util.appaction.AppActionModel;
import com.kaiba.m.core.domain.actmix.ActMixItem;
import com.kaiba.m.core.repository.actmix.ActMixItemRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/11/08 11:35
 **/
@Slf4j
@Service
public class ActMixItemService {

    private static final Integer TIME_BASE_LINE = 1664553600;

    private final ActMixItemRepository itemRepository;

    public ActMixItemService(
        ActMixItemRepository itemRepository
    ) {
        this.itemRepository = itemRepository;
    }

    public ActMixItem addItemByBody(ActMixItem item) {
        verifyItem(item);
        long time = System.currentTimeMillis();
        item.setViewCount(0);
        item.setState(ItemState.INIT.getValue());
        item.setCreateTime(time);
        item.setUpdateTime(time);
        Map<String, Long> tabOrders = item.getTabs().stream().collect(Collectors.toMap(s -> s, value -> 0L));
        item.setTabOrders(tabOrders);
        return itemRepository.save(item);
    }

    public Page<ActMixItem> getItemList(
        Integer siteId, String tabId, Integer type, Integer[] state,  Integer page, Integer pageSize
    ) {
        Sort sortBy = Sort.by(Sort.Order.desc("tabOrders." + tabId),
            Sort.Order.asc("state"), Sort.Order.desc("updateTime"));
        Pageable pageable = createPageable(page, pageSize, sortBy);
        return itemRepository.findBySiteIdAndTabIdAndStateAndType(siteId, tabId, type, state, pageable);
    }

    public Optional<ActMixItem> getItemById(String itemId) {
        return itemRepository.findById(itemId);
    }

    public void incrViewCount(String itemId) {
        itemRepository.incrViewCount(itemId, 1);
    }

    public ActMixItem updateItemOrderById(String tabId, String itemId, Long manualOrder) {
        long tabOrder = System.currentTimeMillis() / 1000 - TIME_BASE_LINE;
        if (manualOrder != 0) {
            tabOrder = manualOrder + tabOrder;
        }
        return itemRepository.updateOrder(tabId, itemId, tabOrder);
    }

    public ActMixItem resetItemOrderById(String tabId, String itemId) {
        return itemRepository.updateOrder(tabId, itemId, 0L);
    }

    public ActMixItem updateItemStateAsPreview(String itemId) {
        return itemRepository.updateItemStateAsPreview(itemId);
    }

    public ActMixItem updateItemStateAsOnGoing(String itemId) {
        return itemRepository.updateItemStateAsOnGoing(itemId);
    }

    public ActMixItem updateItemStateAsEnded(String itemId) {
        return itemRepository.updateItemStateAsEnded(itemId);
    }

    public ActMixItem updateItemStateAsArchived(String itemId) {
        return itemRepository.updateItemStateAsArchived(itemId);
    }

    public ActMixItem updateItemById(ActMixItem item) {
        verifyItem(item);
        return itemRepository.updateItemById(item);
    }

    @Async
    public void updateItemStateByBatch() {
        long time = System.currentTimeMillis();
        itemRepository.findAllByStateAndStartTimeLessThanEqual(ItemState.PREVIEW.getValue(), time)
            .forEach(t -> updateItemStateAsOnGoing(t.getId()));
        itemRepository.findAllByStateAndEndTimeLessThanEqual(ItemState.ON_GOING.getValue(), time)
            .forEach(t -> updateItemStateAsEnded(t.getId()));
    }

    // ---------------------------------------------------

    private void verifyItem(ActMixItem item) {
        if (item == null) {
            throw new KbException(KbCode.ILLEGAL_ARGUMENT, "item should not be null").li();
        }

        if(item.getState() != null && item.getState() == ItemState.ARCHIVED.getValue()) {
            throw new KbException(KbCode.ILLEGAL_ARGUMENT, "data has already been archived")
                .setReadableMessage("数据已归档").li();
        }

        if(item.getSiteId() == null) {
            throw new KbException(KbCode.ILLEGAL_ARGUMENT, "siteId should not be null").li();
        }

        if(item.getTabs() == null || item.getTabs().isEmpty()) {
            throw new KbException(KbCode.ILLEGAL_ARGUMENT, "tabs should not be null").li();
        }

        if(StringUtils.isEmpty(item.getTitle())) {
            throw new KbException(KbCode.ILLEGAL_ARGUMENT, "title should not be null").li();
        }

        if (item.getCover() == null) {
            throw new KbException(KbCode.ILLEGAL_ARGUMENT, "cover should not be null")
                .setReadableMessage("封面不能为空").li();
        }

        if(item.getVisibilityFlag1() == null) {
            throw new KbException(KbCode.ILLEGAL_ARGUMENT, "flag should not be null").li();
        }

        Image cover = item.getCover();
        if(cover.getWidth() == null || cover.getHeight() == null || cover.getWidth() == 0 || cover.getHeight() == 0) {
            throw new KbException(KbCode.ILLEGAL_ARGUMENT, "cover must contain width and height value")
                .setReadableMessage("封面图宽高不能为空").li();
        }

        if(item.getType() == null || !ItemType.valueOf(item.getType()).isPresent()) {
            throw new KbException(KbCode.ILLEGAL_ARGUMENT, "type illegal").li();
        }

        if (!StringUtils.isEmpty(item.getAction())) {
            new AppActionModel(item.getAction(), item.getActionParams()).validate();
        }
    }

    private static Pageable createPageable(Integer page, Integer pageSize, Sort sort) {
        int p = (page == null || page < 1) ? 0 : page - 1;
        int ps = (pageSize == null) ? 15 : pageSize;
        return PageRequest.of(p, ps, sort);
    }

}
