package com.kaiba.m.core.service.appcomponent.textticker;

import com.kaiba.lib.base.assembler.Mapper;
import com.kaiba.lib.base.constant.KbModule;
import com.kaiba.lib.base.constant.appwidget.WidgetItemState;
import com.kaiba.lib.base.domain.appwidget.textticker.TextTickerDataCreateModel;
import com.kaiba.lib.base.domain.appwidget.textticker.TextTickerDataUpdateModel;
import com.kaiba.lib.base.domain.appwidget.textticker.TextTickerIdxUpdateModel;
import com.kaiba.lib.base.domain.appwidget.textticker.TextTickerInstanceModel;
import com.kaiba.lib.base.middleware.adminlogrecorder.AdminLogRecorder;
import com.kaiba.lib.base.response.KbCode;
import com.kaiba.lib.base.response.KbException;
import com.kaiba.lib.base.service.IAdminLogService;
import com.kaiba.m.core.domain.appwidget.textticker.TextTicker;
import com.kaiba.m.core.domain.appwidget.textticker.TextTickerInstance;
import com.kaiba.m.core.repository.appcomponent.textticker.AppTextTickerInstanceRepository;
import com.kaiba.m.core.repository.appcomponent.textticker.AppTextTickerRepository;
import com.kaiba.m.core.util.PageUtils;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Optional;


/**
 * <AUTHOR>
 * @version AppTextTickerService, v0.1 2023/7/18 17:09 daopei Exp $
 **/
@Service
@Slf4j
public class AppTextTickerService {

    public final static String TEXT_TICKER_UNIT_INSTANCE = "text_ticker_instance";
    public final static String TEXT_TICKER_UNIT_DATA = "text_ticker_data";

    private final AppTextTickerInstanceRepository appTextTickerInstanceRepository;
    private final AppTextTickerRepository appTextTickerRepository;
    private final AdminLogRecorder adminLogRecorder;

    public AppTextTickerService(
            @NonNull AppTextTickerInstanceRepository appTextTickerInstanceRepository,
            @NonNull AppTextTickerRepository appTextTickerRepository,
            @NonNull AppTextTickerCacheService appTextTickerCacheService,
            IAdminLogService adminLogService
    ) {
        this.appTextTickerInstanceRepository = appTextTickerInstanceRepository;
        this.appTextTickerRepository = appTextTickerRepository;
        this.adminLogRecorder = AdminLogRecorder
                .builder(adminLogService)
                .module(KbModule.APP_TEXT_TICKER)
                .registerUnit(TEXT_TICKER_UNIT_INSTANCE, "词条实例管理")
                .registerUnit(TEXT_TICKER_UNIT_DATA, "词条数据管理")
                .create();
        ;
    }

    public TextTickerInstance createInstance(TextTickerInstanceModel model) {
        AppTextTickerModelHelper.INSTANCE_VERIFIER.verify(model);
        TextTickerInstance instance = AppTextTickerModelHelper.model2Instance(model);
        instance.setCreateTime(System.currentTimeMillis());
        instance = appTextTickerInstanceRepository.save(instance);
        adminLogRecorder.on(TEXT_TICKER_UNIT_INSTANCE).desc("创建词条实例").ref1(instance.getId()).add();
        return instance;
    }

    public TextTickerInstance updateInstance(TextTickerInstanceModel model) {
        AppTextTickerModelHelper.INSTANCE_VERIFIER.verify(model);
        TextTickerInstance instance = appTextTickerInstanceRepository.findById(model.getId())
                .orElseThrow(() -> new KbException(KbCode.RESOURCE_NOT_FOUND).r("实例数据未找到").li());
        adminLogRecorder.on(TEXT_TICKER_UNIT_INSTANCE).desc("修改词条实例").ref1(instance.getId()).snapshot(instance).add();
        return appTextTickerInstanceRepository.update(Mapper.map(model, instance));
    }


    public TextTickerInstance updateAutoplay(String instanceId, Integer autoplay) {
        TextTickerInstance existedInstance = appTextTickerInstanceRepository.findById(instanceId)
                .orElseThrow(() -> new KbException(KbCode.RESOURCE_NOT_FOUND).r("实例数据未找到").li());
        TextTickerInstance instance = appTextTickerInstanceRepository.updateAutoplay(instanceId, autoplay);
        adminLogRecorder.on(TEXT_TICKER_UNIT_INSTANCE)
                .desc(String.format("修改词条实例自动播放时间, {} -> {}", existedInstance.getAutoplay(), autoplay))
                .ref1(instance.getId()).add();
        return instance;
    }

    public void deleteInstance(String instanceId) {
        if (appTextTickerRepository.existsByInstanceId(instanceId)) {
            throw new KbException(KbCode.ILLEGAL_STATE, "instance still contains text ticker, cannot delete")
                    .r("实例中尚存在 textTicker, 不能删除实例").li();
        }
        appTextTickerInstanceRepository.deleteById(instanceId);
        adminLogRecorder.on(TEXT_TICKER_UNIT_INSTANCE).desc("删除词条实例").ref1(instanceId).add();
    }


    public Optional<TextTickerInstance> getInstance(Integer siteId, String instanceId, String instanceKey) {
        if (instanceId != null) {
            return appTextTickerInstanceRepository.findById(instanceId);
        }
        if (instanceKey != null) {
            return appTextTickerInstanceRepository.findFirstByKeyAndSiteId(instanceKey, siteId);
        }
        return Optional.empty();
    }

    public Page<TextTickerInstance> getInstanceBySite(Integer siteId, Pageable pageable) {
        return appTextTickerInstanceRepository.findBySiteId(siteId, pageable);
    }


    //-----------------------

    public TextTicker createData(TextTickerDataCreateModel model) {
        AppTextTickerModelHelper.DATA_CREATE_VERIFIER.verify(model);
        TextTicker textTicker = AppTextTickerModelHelper.createModel2Data(model);
        textTicker.setState(WidgetItemState.PREPARE.getValue());
        textTicker = appTextTickerRepository.save(textTicker);
        adminLogRecorder.on(TEXT_TICKER_UNIT_DATA).desc("创建词条数据")
                .ref1(textTicker.getId()).ref2(model.getInstanceId()).add();
        return appTextTickerRepository.save(textTicker);
    }

    public TextTicker updateData(TextTickerDataUpdateModel model) {
        AppTextTickerModelHelper.DATA_UPDATE_VERIFIER.verify(model);
        TextTicker textTicker = appTextTickerRepository.findById(model.getId())
                .orElseThrow(() -> new KbException(KbCode.REQUEST_PARAM_MISSING).r("text ticker 数据未找到").li());
        TextTicker updated = appTextTickerRepository.updateData(Mapper.map(model, textTicker));
        adminLogRecorder.on(TEXT_TICKER_UNIT_DATA).desc("更新词条数据")
                .ref1(textTicker.getId()).ref2(textTicker.getInstanceId()).snapshot(textTicker).add();
        return updated;
    }

    public void deleteData(String dataId) {
        TextTicker textTicker = appTextTickerRepository.findById(dataId)
                .orElseThrow(() -> new KbException(KbCode.REQUEST_PARAM_MISSING).r("text ticker 数据未找到").li());
        appTextTickerRepository.deleteById(dataId);
        adminLogRecorder.on(TEXT_TICKER_UNIT_DATA).desc("删除词条数据")
                .ref1(textTicker.getId()).ref2(textTicker.getInstanceId()).snapshot(textTicker).add();
    }

    public void updateIdx(TextTickerIdxUpdateModel idxUpdateModel) {
        if (CollectionUtils.isEmpty(idxUpdateModel.getIdxList())) {
            return;
        }
        appTextTickerRepository.updateIdx(idxUpdateModel);
        adminLogRecorder.on(TEXT_TICKER_UNIT_DATA).desc("更新词条数据排序").info(idxUpdateModel).add();
    }

    public TextTicker updateState(String dataId, WidgetItemState state) {
        TextTicker textTicker = appTextTickerRepository.findById(dataId).orElseThrow(() -> new KbException(KbCode.RESOURCE_NOT_FOUND).r("text ticker 不存在").li());
        assertStateChange(textTicker.getState(), state.getValue());
        TextTicker updated = appTextTickerRepository.updateState(dataId, state.getValue());
        adminLogRecorder.on(TEXT_TICKER_UNIT_DATA).desc(String.format("更新词条数据状态, {} -> {}", textTicker.getState(), state.getValue()))
                .ref1(dataId).ref2(textTicker.getInstanceId()).add();
        return updated;
    }

    public TextTicker updateScheduleTime(String dataId, Long scheduledStartTime, Long scheduledEndTime) {
        TextTicker textTicker = appTextTickerRepository.findById(dataId).orElseThrow(() -> new KbException(KbCode.RESOURCE_NOT_FOUND).r("text ticker 不存在").li());
        TextTicker updated = appTextTickerRepository.updateScheduledTime(dataId, scheduledStartTime, scheduledEndTime);
        adminLogRecorder.on(TEXT_TICKER_UNIT_DATA)
                .desc(String.format("更新词条数据计划时间,oldValue:{} - {} -> newValue:{} - {}",
                        textTicker.getScheduledStartTime(),
                        textTicker.getScheduledEndTime(),
                        scheduledStartTime,
                        scheduledEndTime))
                .ref1(dataId).ref2(textTicker.getInstanceId()).add();
        return updated;
    }

    public Page<TextTicker> getByInstance(Integer siteId, String instanceId, String instanceKey, Integer state, Integer page, Integer pageSize) {
        if (instanceId == null && instanceKey != null) {
            TextTickerInstance instance = appTextTickerInstanceRepository.findFirstByKeyAndSiteId(instanceKey, siteId)
                    .orElseThrow(() -> new KbException(KbCode.RESOURCE_NOT_FOUND, "cannot found key " + instanceKey).r("未找到对应key的实例"));
            instanceId = instance.getId();
        }
        return appTextTickerRepository.findByParams(instanceId, state, PageUtils.ofDefault(page, pageSize));
    }

    public Optional<TextTicker> getByInstanceAndRefId(Integer siteId, String refId, String instanceId, String instanceKey) {
        if (instanceId == null && instanceKey != null) {
            TextTickerInstance instance = appTextTickerInstanceRepository.findFirstByKeyAndSiteId(instanceKey, siteId)
                    .orElseThrow(() -> new KbException(KbCode.RESOURCE_NOT_FOUND, "cannot found key " + instanceKey).r("未找到对应key的实例"));
            instanceId = instance.getId();
        }
        return appTextTickerRepository.findFirstByInstanceIdAndRefId(instanceId, refId);
    }

    // --------------
    private void assertStateChange(Integer source, Integer dest) {
        WidgetItemState fromState = WidgetItemState.resolveByValue(source).orElseThrow(() -> new KbException(KbCode.ILLEGAL_ARGUMENT, "unknown state: " + source).r("错误的状态值"));
        WidgetItemState toState = WidgetItemState.resolveByValue(dest).orElseThrow(() -> new KbException(KbCode.ILLEGAL_ARGUMENT, "unknown state: " + dest).r("错误的状态值"));
        if (!fromState.isStateChangeAllowed(toState)) {
            throw new KbException(KbCode.ILLEGAL_STATE, "state change not allowed").r("不被允许的状态变更").li();
        }
    }

}
