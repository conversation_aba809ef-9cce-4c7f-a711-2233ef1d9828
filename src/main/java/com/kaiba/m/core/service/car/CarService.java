package com.kaiba.m.core.service.car;

import com.kaiba.m.core.domain.car.Car;
import com.kaiba.m.core.domain.car.CarBrand;
import com.kaiba.m.core.repository.car.CarBrandRepository;
import com.kaiba.m.core.repository.car.CarRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.*;
import java.util.concurrent.locks.ReentrantLock;
import java.util.stream.Collectors;

/**
 * author: lyux
 * date: 18-9-7
 * TODO: invalidate cache when receive update event from mq
 */
@Slf4j
@Service
public class CarService {

    private static final Comparator<Car> carComparator = Comparator.comparing(Car::getCode);
    private static final Comparator<CarBrand> carBrandComparator = Comparator.comparing(CarBrand::getCode);

    private final CarRepository carRepository;
    private final CarBrandRepository carBrandRepository;

    private Map<String, Car> cars = Collections.emptyMap();
    private Map<String, List<Car>> carBrandMap = Collections.emptyMap();
    private Map<String, CarBrand> carBrands = Collections.emptyMap();
    private List<Car> carList = Collections.emptyList();
    private List<CarBrand> carBrandList = Collections.emptyList();

    private final ReentrantLock lock = new ReentrantLock();

    @Autowired
    public CarService(CarRepository carRepository, CarBrandRepository carBrandRepository) {
        this.carRepository = carRepository;
        this.carBrandRepository = carBrandRepository;
    }

    @PostConstruct
    private void init() {
        final ReentrantLock lock = this.lock;
        if (lock.tryLock()) {
            try {
                List<Car> carList = carRepository.findAll();
                carList.sort(carComparator);
                Map<String, Car> cars = new HashMap<>(carList.size());
                carList.forEach(car -> cars.put(car.getCode(), car));

                List<CarBrand> carBrandList = carBrandRepository.findAll();
                carBrandList.sort(carBrandComparator);
                Map<String, CarBrand> carBrands = new HashMap<>(carBrandList.size());
                carBrandList.forEach(brand -> carBrands.put(brand.getCode(), brand));

                Map<String, List<Car>> carBrandMap = carList.stream().collect(Collectors.groupingBy(Car::getBrandCode));
                this.cars = cars;
                this.carList = carList;
                this.carBrands = carBrands;
                this.carBrandList = carBrandList;
                this.carBrandMap = carBrandMap;
            } finally {
                lock.unlock();
            }
        }
    }

    public Optional<CarBrand> getCarBrand(String brandCode) {
        return null != brandCode ? Optional.ofNullable(carBrands.get(brandCode)) : Optional.empty();
    }

    public Optional<CarBrand> getCarBrandByCarCode(String carCode) {
        return getCar(carCode).flatMap(car -> getCarBrand(car.getBrandCode()));
    }

    public List<CarBrand> getCarBrandListIn(String[] brandCodeList) {
        if (null == brandCodeList || brandCodeList.length == 0) {
            return Collections.emptyList();
        }
        return Arrays.stream(brandCodeList)
                .map(code -> carBrands.get(code))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    public List<CarBrand> getCarBrandList() {
        return carBrandList;
    }

    public Optional<Car> getCar(String carCode) {
        return null != carCode ? Optional.ofNullable(cars.get(carCode)) : Optional.empty();
    }

    public List<Car> getCarListIn(String[] carCodeList) {
        if (null == carCodeList || carCodeList.length == 0) {
            return Collections.emptyList();
        }
        return Arrays.stream(carCodeList)
                .map(code -> cars.get(code))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    public List<Car> getCarListByBrand(String carBrand) {
        if (carBrand == null) {
            return getCarList();
        }
        return carBrandMap.get(carBrand);
    }

    public List<Car> getCarList() {
        return carList;
    }

    @Async
    public void invalidate() {
        init();
    }

}
