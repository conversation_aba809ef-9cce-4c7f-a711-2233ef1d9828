package com.kaiba.m.core.service.news.pool.knowledge;

import com.kaiba.lib.base.domain.news.pool.knowledge.BasedAnswerModel;
import com.kaiba.lib.base.domain.news.pool.knowledge.BasedAnswerQueryModel;
import com.kaiba.lib.base.domain.news.pool.knowledge.KnowledgeBaseModel;
import java.util.List;
import java.util.Optional;
import org.springframework.data.domain.Page;

/**
 * Description: 知识库常见回复查询Service接口
 * Author: ZM227
 * Date: 2025/1/22 17:17
 */
public interface IBasedAnswerQueryService {

    /**
     * 根据问题全匹配答案，答案在有效期内
     * @param baseId 知识库Id
     * @param categoryIds 分类Ids，可以为空
     * @param tag 关键字
     * @return 常见回复
     */
    BasedAnswerModel queryBasedAnswer(String baseId, List<String> categoryIds, String tag);

    /**
     * 根据知识库问题id查询常见回复
     * @param answerId 问题id
     * @return 常见回复
     */
    BasedAnswerModel findBasedAnswerById(String answerId);

    Page<BasedAnswerModel> findAnswersByCondition(BasedAnswerQueryModel queryModel);

}
