package com.kaiba.m.core.service.tmuyun;

import com.kaiba.lib.base.domain.tmuyun.tmuyun.ArticleApiModel;
import com.kaiba.lib.base.util.StringUtils;
import com.kaiba.m.core.util.JsonUtils;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.client.SimpleClientHttpRequestFactory;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

/**
 * <AUTHOR>
 * @date 2022/07/21 11:02
 **/
@Slf4j
@Service
public class TmuyunApiService {

    private static final String PATH_ARTICLE = "/api/article";
    private static final String APP_ID = "1e15673646ce206a58f2196ada7fa232";
    private static final String APP_SECRET = "1d8f92b01881b1d5063512c1270c44ef";

    private final String tmuSendArticleApi;
    private final RestTemplate restTemplate;

    public TmuyunApiService(@Value("${kaiba.tmuyun.send-article-url}") String tmuApiUrl) {
        this.tmuSendArticleApi = tmuApiUrl + PATH_ARTICLE;
        this.restTemplate = new RestTemplate(new SimpleClientHttpRequestFactory());
    }

    public TrackingResult sendArticle(ArticleApiModel model) {
        String result = post(tmuSendArticleApi, JsonUtils.getHtmlGson().toJson(model));
        log.info("tmuyun post data result: " + result);
        return JsonUtils.toModel(result, TrackingResult.class);
    }

    // -------------------------------------------------

    private String post(String url, String data) {
        long current = System.currentTimeMillis();
        HttpHeaders headers = new HttpHeaders();
        headers.set("x-ppd-appId", APP_ID);
        headers.set("x-ppd-timestamp", String.valueOf(current));
        headers.set("x-ppd-signature", md5(current));
        headers.setContentType(MediaType.APPLICATION_JSON);
        return restTemplate.postForEntity(url,
            new HttpEntity<>(data, headers), String.class).getBody();
    }

    private String md5(Long current) {
        return StringUtils.toMd5(APP_ID + current + APP_SECRET);
    }

    // -------------------------------------------------

    @Data
    @ToString
    @NoArgsConstructor
    public static class TrackingResult {
        private Integer code;
        private String msg;
    }
}
