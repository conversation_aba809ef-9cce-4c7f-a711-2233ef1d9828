package com.kaiba.m.core.service.program.rebroadcast;

import com.kaiba.m.core.domain.program.ScheduleInstance;
import com.kaiba.m.core.service.program.ScheduleInstanceService;
import org.springframework.stereotype.Component;

import java.util.Optional;

/**
 * author wangsj
 * date 2020-09-01
 */
@Component
public class PreviousOneBroadcast implements IRebroadcastHandle {

    @Override
    public Optional<ScheduleInstance> getBroadcastInstance(ScheduleInstanceService instanceService,String programId, Integer rebroadcastTime) {
        return instanceService.getLastBroadcastByProgramIdUntil(programId, rebroadcastTime);
    }
}
