package com.kaiba.m.core.service.note.comment;

import com.kaiba.m.core.domain.note.NoteComment;
import com.kaiba.m.core.service.note.NoteCacheConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * author: lyux
 * date: 19-6-11
 */
@Slf4j
@Component
public class NoteCommentCachePopulator {

    private final NoteCommentCacheService cacheService;
    private final NoteCommentDBService dbService;

    public NoteCommentCachePopulator(NoteCommentCacheService cacheService, NoteCommentDBService dbService) {
        this.cacheService = cacheService;
        this.dbService = dbService;
    }

    @Async
    public void populate(String noteId) {
        if (!cacheService.isPopulating(noteId)) {
            if (NoteCacheConfig.CACHE_DEBUG) {
                log.info("[cache_debug][db] populate comment list for: "
                        + noteId + ", runtime: " + Thread.currentThread().getId());
            }
            List<NoteComment> commentList = dbService
                    .getCommentListByNoteId(noteId, 1, NoteCacheConfig.COMMENT_LIST_BY_NOTE_MAX_COUNT + 1);
            cacheService.populate(noteId, commentList);
        }
    }

    @Async
    public void populateList(List<String> noteIdList) {
        for (String noteId : noteIdList) {
            populate(noteId);
        }
    }
}
