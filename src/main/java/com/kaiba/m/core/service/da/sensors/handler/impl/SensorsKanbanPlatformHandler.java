package com.kaiba.m.core.service.da.sensors.handler.impl;

import com.kaiba.lib.base.constant.da.sensors.KanbanColConfigType;
import com.kaiba.lib.base.constant.da.sensors.KanbanColDataType;
import com.kaiba.lib.base.constant.da.sensors.KanbanDivState;
import com.kaiba.m.core.domain.da.sensors.kanban.DivConfigPlatform;
import com.kaiba.m.core.controller.da.sensors.model.SensorsKanbanTimeRangeModel;
import com.kaiba.m.core.service.da.sensors.SensorsKanbanDataService;
import com.kaiba.m.core.service.da.sensors.SensorsKanbanApiService;
import com.kaiba.m.core.service.da.sensors.SensorsKanbanConfigService;
import com.kaiba.m.core.service.da.sensors.handler.SensorsKanbanHandler;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @version SensorsKanbanPlatformHandler, v0.1 2024/3/22 11:34 daopei Exp $
 **/
@RequiredArgsConstructor
@Service
public class SensorsKanbanPlatformHandler implements SensorsKanbanHandler {

    @NonNull
    private final SensorsKanbanDataService dataService;
    @NonNull
    private final SensorsKanbanApiService apiService;
    @NonNull
    private final SensorsKanbanConfigService configService;

    @Override
    public KanbanColConfigType getConfigType() {
        return KanbanColConfigType.PLATFORM;
    }

    @Override
    public boolean support(KanbanColDataType dataType) {
        return dataType.getConfigType() == KanbanColConfigType.PLATFORM;
    }

    @Override
    public void loadData(SensorsKanbanTimeRangeModel timeRange) {
        List<DivConfigPlatform> configList = configService.getDivConfigPlatformByParam(
                null, null, null,
                timeRange.getStrategy().name(),
                KanbanDivState.ACTIVE.name(),
                timeRange.getSt(),
                1,
                200).toList();

        for (DivConfigPlatform config : configList) {
            dataService.savePlatformData(apiService.queryPlatformDivData(config, timeRange));
        }
    }


    @Override
    public void loadDataByIds(List<String> configIds, List<SensorsKanbanTimeRangeModel> times) {
        List<DivConfigPlatform> config = configService.getDivConfigPlatformByIdAndState(configIds, KanbanDivState.ACTIVE.name());
        for (SensorsKanbanTimeRangeModel time : times) {
            processConfig(config, time);
        }
    }

    private void processConfig(List<DivConfigPlatform> configList, SensorsKanbanTimeRangeModel timeRange) {
        for (DivConfigPlatform config : configList) {
            dataService.savePlatformData(apiService.queryPlatformDivData(config, timeRange));
        }
    }
}
