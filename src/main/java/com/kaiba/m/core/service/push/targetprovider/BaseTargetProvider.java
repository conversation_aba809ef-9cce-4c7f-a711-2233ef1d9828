package com.kaiba.m.core.service.push.targetprovider;

import com.kaiba.lib.base.response.KbCode;
import com.kaiba.lib.base.response.KbException;
import com.kaiba.m.core.domain.push.PushTarget;
import com.kaiba.m.core.service.push.IPushTargetProvider;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Iterator;
import java.util.List;
import java.util.concurrent.TimeUnit;

@Slf4j
public class BaseTargetProvider implements IPushTargetProvider {
    String PUSHING_LIST_KEY_PREFIX = "pushing_list_";
    String PUSHING_LIST_COUNT_KEY_PREFIX = "pushing_list_count_";
    String PUSHED_LIST_KEY_PREFIX = "pushed_list_";
    String pushId;
    StringRedisTemplate redisTemplate;

    int getNextPage() {
        String popped = redisTemplate.opsForList().rightPop(PUSHING_LIST_KEY_PREFIX + pushId);
        return popped == null ? -1 : Integer.parseInt(popped);
    }

    @Override
    public void initSegmentList() {
        String key = PUSHING_LIST_KEY_PREFIX + pushId;
        if (redisTemplate.hasKey(key)){
            return;
        }
        int totalPage = getTotalPage();
        if (totalPage > 0) {
            List<String> pageList = new ArrayList<>(totalPage);
            for (int i = 1; i <= totalPage; i++) {
                pageList.add(String.valueOf(i));
            }
            redisTemplate.opsForList().leftPushAll(key, pageList);
            redisTemplate.expire(key, 86400, TimeUnit.SECONDS);
            redisTemplate.opsForValue().set(PUSHING_LIST_COUNT_KEY_PREFIX + pushId, String.valueOf(pageList.size()));
        } else {
            log.info("no push target exist with parameters : " + this.toString());
        }
    }

    protected void pushedOnePage(int page) {
        if (redisTemplate.opsForList().leftPush(PUSHED_LIST_KEY_PREFIX + pushId, String.valueOf(page)) == 1){
            redisTemplate.expire(PUSHED_LIST_KEY_PREFIX + pushId, 86400, TimeUnit.SECONDS);
        }
    }

    @Override
    public boolean finished() {
        int pushed = redisTemplate.opsForList().size(PUSHED_LIST_KEY_PREFIX + pushId).intValue();
        int total = Integer.parseInt(redisTemplate.opsForValue().get(PUSHING_LIST_COUNT_KEY_PREFIX + pushId));
        return pushed >= total;
    }

    protected int getTotalPage() {
        return 0;
    }

    @Override
    public Iterator<PushTarget> iterator() {
        return Collections.emptyIterator();
    }
}
