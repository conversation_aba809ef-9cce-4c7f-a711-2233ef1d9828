package com.kaiba.m.core.service.dynamic;

import com.google.gson.*;
import com.kaiba.lib.base.domain.dynamic.DynamicEventModel;
import com.kaiba.lib.base.middleware.KbEventReceiver;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.lang.reflect.Type;

/**
 * author: lyux
 * date: 18-9-29
 */
@Slf4j
@Component
public class AMQPDynamicEventReceiver extends KbEventReceiver<DynamicEventModel> {
    private static Gson compatibleDeserializerGson = new GsonBuilder()
            .registerTypeAdapter(int.class, new IntegerCompatibleDeserializer())
            .create();

    private final DynamicService dynamicService;

    @Autowired
    public AMQPDynamicEventReceiver(DynamicService dynamicService) {
        this.dynamicService = dynamicService;
    }

    @Override
    public void onReceiveEvent(DynamicEventModel data) {
        dynamicService.addEvent(data);
    }

    @Override
    public Class<DynamicEventModel> getEventType() {
        return DynamicEventModel.class;
    }

    @Override
    protected Gson getGson() {
        return compatibleDeserializerGson;
    }

    private static class IntegerCompatibleDeserializer implements JsonDeserializer<Integer> {
        @Override
        public Integer deserialize(JsonElement json, Type typeOfT, JsonDeserializationContext context) throws JsonParseException {
            try {
                return json.getAsInt();
            } catch (Exception e) {
                return null;
            }
        }
    }
}
