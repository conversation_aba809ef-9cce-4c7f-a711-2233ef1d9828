package com.kaiba.m.core.service.user.blacklist;

import com.kaiba.lib.base.response.KbCode;
import com.kaiba.lib.base.response.KbException;
import com.kaiba.m.core.domain.user.UserBlacklist;
import com.kaiba.m.core.repository.user.UserBlacklistRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;

import java.util.Set;
import java.util.stream.Collectors;

/**
 * author: lyux
 * date: 2022-03-09
 */
@Slf4j
@Service
public class UserBlacklistService {

    private final UserBlacklistRepository userBlacklistRepository;
    private final BlacklistCacheFactory blacklistCacheFactory;

    public UserBlacklistService(
            UserBlacklistRepository userBlacklistRepository,
            BlacklistCacheFactory blacklistCacheFactory) {
        this.userBlacklistRepository = userBlacklistRepository;
        this.blacklistCacheFactory = blacklistCacheFactory;
    }

    public Set<Integer> getBlacklist(Integer userId, boolean allowCache) {
        if (allowCache) {
            return blacklistCacheFactory.getBlacklist(userId);
        } else {
            return userBlacklistRepository
                    .getByUid(userId, PageRequest.of(0, UserBlacklistConsts.USER_BLACKLIST_MAX)).stream()
                    .map(UserBlacklist::getBid)
                    .collect(Collectors.toSet());
        }
    }

    public boolean isInBlacklist(Integer userId, Integer targetUserId, boolean allowCache) {
        if (allowCache) {
            return blacklistCacheFactory.isInBlacklist(userId, targetUserId);
        } else {
            return userBlacklistRepository.existsByUidAndBid(userId, targetUserId);
        }
    }

    public void addToBlacklist(Integer userId, Integer targetUserId) {
        if (userBlacklistRepository.countByUid(userId) + 1 > UserBlacklistConsts.USER_BLACKLIST_MAX) {
            throw new KbException(KbCode.ILLEGAL_ARGUMENT, "user blacklist exceed max")
                    .r("最多允许设置 " + UserBlacklistConsts.USER_BLACKLIST_MAX + " 个黑名单用户").li();
        }
        userBlacklistRepository.addToBlacklist(userId, targetUserId);
        blacklistCacheFactory.addToBlacklist(userId, targetUserId);
    }

    public void removeFromBlacklist(Integer userId, Integer targetUserId) {
        userBlacklistRepository.deleteByUidAndBid(userId, targetUserId);
        blacklistCacheFactory.removeFromBlacklist(userId, targetUserId);
    }

}
