package com.kaiba.m.core.service.floatview;

import com.kaiba.lib.base.constant.floatview.FloatViewState;
import com.kaiba.m.core.domain.floatview.FloatIcon;
import com.kaiba.m.core.domain.floatview.FloatWindow;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * author: lyux
 * date: 20-1-10
 */
@Slf4j
@Component
public class FloatViewStateTransitionCronJob {

    private final FloatIconService floatIconService;
    private final FloatWindowService floatWindowService;

    public FloatViewStateTransitionCronJob(FloatIconService floatIconService, FloatWindowService floatWindowService) {
        this.floatIconService = floatIconService;
        this.floatWindowService = floatWindowService;
    }

    // 每隔 1 分钟
    @XxlJob("float-view-state-transition")
    public ReturnT<String> stateTransition(String param) {

        // check float icon
        for (int i = 1; ; i ++) {
            Page<FloatIcon> page = floatIconService.getPageByStates(FloatViewState.ACTIVE_STATES, i, 100);
            List<FloatIcon> list = page.getContent();
            for (FloatIcon f : list) {
                checkFloatIconState(f);
            }
            if (page.isLast()) {
                break;
            }
        }

        // check float window
        for (int i = 1; ; i ++) {
            Page<FloatWindow> page = floatWindowService.getPageByStates(FloatViewState.ACTIVE_STATES, i, 100);
            List<FloatWindow> list = page.getContent();
            for (FloatWindow f : list) {
                checkFloatWindowState(f);
            }
            if (page.isLast()) {
                break;
            }
        }

        return ReturnT.SUCCESS;
    }

    private void checkFloatIconState(FloatIcon f) {
        long current = System.currentTimeMillis() / 1000;
        if (f.getState() == FloatViewState.ONLINE.getValue()) {
            if (f.getScheduledEndTime() == null) {
                return;
            }
            if (current > f.getScheduledEndTime()) {
                log.info("about to stop float icon due to scheduled end time reached: " + f);
                floatIconService.updateState(f, FloatViewState.SEALED);
            }
        } else if (f.getState() == FloatViewState.SIGNED.getValue()) {
            if (f.getScheduledStartTime() == null) {
                return;
            }
            if (current > f.getScheduledStartTime()) {
                log.info("about to start float icon due to scheduled start time reached: " + f);
                floatIconService.stopOnlineExcept(f);
                floatIconService.updateState(f, FloatViewState.ONLINE);
            }
        }
    }

    private void checkFloatWindowState(FloatWindow f) {
        long current = System.currentTimeMillis() / 1000;
        if (f.getState() == FloatViewState.ONLINE.getValue()) {
            if (f.getScheduledEndTime() == null) {
                return;
            }
            if (current > f.getScheduledEndTime()) {
                log.info("about to stop float window due to scheduled end time reached: " + f);
                floatWindowService.updateState(f, FloatViewState.SEALED);
            }
        } else if (f.getState() == FloatViewState.SIGNED.getValue()) {
            if (f.getScheduledStartTime() == null) {
                return;
            }
            if (current > f.getScheduledStartTime()) {
                log.info("about to start float window due to scheduled start time reached: " + f);
                floatWindowService.stopOnlineExcept(f);
                floatWindowService.updateState(f, FloatViewState.ONLINE);
            }
        }
    }

}
