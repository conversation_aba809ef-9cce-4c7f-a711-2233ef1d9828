package com.kaiba.m.core.service.da.sensors;

import com.kaiba.lib.base.constant.KbModule;
import com.kaiba.lib.base.domain.da.sensors.kanban.*;
import com.kaiba.lib.base.lang.verifier.F;
import com.kaiba.lib.base.lang.verifier.Verifier;
import com.kaiba.lib.base.lang.verifier.VerifierBuilder;

/**
 * <AUTHOR>
 * @version SensorsKanbanHelper, v0.1 2024/3/11 10:26 daopei Exp $
 **/
public class SensorsKanbanHelper {

    public static final Verifier<KanbanColumn> COLUMN_VERIFIER = new VerifierBuilder<KanbanColumn>().defaultOrElseThrow()
            .and(F.str(KanbanColumn::getName).notNull())
            .and(F.str(KanbanColumn::getConfigId).notNull())
            .create();

    public static final Verifier<KanbanConfigModel> CONFIG_MODEL_VERIFIER = new VerifierBuilder<KanbanConfigModel>().defaultOrElseThrow()
            .and(F.str(KanbanConfigModel::getName).notNull())
            .and(F.str(KanbanConfigModel::getKey).notNull())
            .and(F.list(KanbanConfigModel::getColumns).each(c -> COLUMN_VERIFIER))
            .create();

    public static final Verifier<KanbanConfigGroupModel> CONFIG_GROUP_MODEL_VERIFIER = new VerifierBuilder<KanbanConfigGroupModel>().defaultOrElseThrow()
            .and(F.str(KanbanConfigGroupModel::getName).notNull())
            .and(F.str(KanbanConfigGroupModel::getKey).notNull())
            .create();


    public static final Verifier<DivConfigActModel> DIV_CONFIG_ACT_MODEL_VERIFIER = new VerifierBuilder<DivConfigActModel>().defaultOrElseThrow()
            .and(F.str(DivConfigActModel::getName).notNull())
            .and(F.str(DivConfigActModel::getStrategy).notNull())
            .and(F.str(DivConfigActModel::getState).notNull())
            .and(F.longF(DivConfigActModel::getStartTime).notNull())
            .and(F.longF(DivConfigActModel::getEndTime).notNull())
            .create();
    public static final Verifier<DivConfigPageModel> DIV_CONFIG_PAGE_MODEL_VERIFIER = new VerifierBuilder<DivConfigPageModel>().defaultOrElseThrow()
            .and(F.str(DivConfigPageModel::getName).notNull())
            .and(F.str(DivConfigPageModel::getStrategy).notNull())
            .and(F.str(DivConfigPageModel::getState).notNull())
            .and(F.longF(DivConfigPageModel::getStartTime).notNull())
            .and(F.longF(DivConfigPageModel::getEndTime).notNull())
            .create();
    public static final Verifier<DivConfigPGCModel> DIV_CONFIG_PGC_MODEL_VERIFIER = new VerifierBuilder<DivConfigPGCModel>().defaultOrElseThrow()
            .and(F.str(DivConfigPGCModel::getName).notNull())
            .and(F.str(DivConfigPGCModel::getStrategy).notNull())
            .and(F.str(DivConfigPGCModel::getState).notNull())
            .and(F.longF(DivConfigPGCModel::getStartTime).notNull())
            .and(F.longF(DivConfigPGCModel::getEndTime).notNull())
            .and(F.str(DivConfigPGCModel::getBiz).enums(KbModule.values()).r("未知的业务类型"))
            .create();

    public static final Verifier<DivConfigPlatformModel> DIV_CONFIG_PLATFORM_MODEL_VERIFIER = new VerifierBuilder<DivConfigPlatformModel>().defaultOrElseThrow()
            .and(F.str(DivConfigPlatformModel::getName).notNull())
            .and(F.str(DivConfigPlatformModel::getStrategy).notNull())
            .and(F.str(DivConfigPlatformModel::getState).notNull())
            .and(F.longF(DivConfigPlatformModel::getStartTime).notNull())
            .and(F.longF(DivConfigPlatformModel::getEndTime).notNull())
            .create();

    public static final Verifier<DivConfigKaibaModel> DIV_CONFIG_KAIBA_MODEL_VERIFIER = new VerifierBuilder<DivConfigKaibaModel>().defaultOrElseThrow()
            .and(F.str(DivConfigKaibaModel::getName).notNull())
            .and(F.str(DivConfigKaibaModel::getStrategy).notNull())
            .and(F.str(DivConfigKaibaModel::getState).notNull())
            .and(F.longF(DivConfigKaibaModel::getStartTime).notNull())
            .and(F.longF(DivConfigKaibaModel::getEndTime).notNull())
            .create();

}
