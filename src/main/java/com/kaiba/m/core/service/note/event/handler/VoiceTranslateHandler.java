package com.kaiba.m.core.service.note.event.handler;

import com.kaiba.lib.base.domain.note.NoteModel;
import com.kaiba.lib.base.domain.user.UserModel;
import com.kaiba.m.core.domain.note.Note;
import com.kaiba.m.core.domain.note.NoteReview;
import com.kaiba.m.core.service.note.NoteVoiceTranslator;
import com.kaiba.m.core.service.note.event.INoteEventReceiver;
import com.kaiba.m.core.service.note.event.NoteCreateOrigin;
import lombok.extern.slf4j.Slf4j;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Component;

/**
 * author: lyux
 * date: 2020-10-13
 */
@Slf4j
@Component
public class VoiceTranslateHandler implements INoteEventReceiver {

    private final NoteVoiceTranslator noteVoiceTranslator;

    public VoiceTranslateHandler(NoteVoiceTranslator noteVoiceTranslator) {
        this.noteVoiceTranslator = noteVoiceTranslator;
    }

    @Override
    public void onNoteCreated(
            @NonNull Note note, NoteModel noteModel, UserModel owner, String reviewId, @NonNull NoteCreateOrigin origin) {
        if (origin == NoteCreateOrigin.USER_ADD || origin == NoteCreateOrigin.REVIEW_ACCEPTED) {
            if (note.getAudio() != null && note.getAudio().getVoiceTrans() == null) {
                if (NoteVoiceTranslator.DEBUG_LOG) {
                    log.info("onNoteCreated, about to translate audio: " + note.getId() + ", audio: " + note.getAudio());
                }
                noteVoiceTranslator.tryTranslateAudioForNoteAsync(note.getId(), null, note.getAudio());
            }
        }
    }

    @Override
    public void onNoteReviewCreated(@NonNull NoteReview noteReview) {
        noteVoiceTranslator.tryTranslateAudioForNoteAsync(null, noteReview.getId(), noteReview.getAudio());
    }

}
