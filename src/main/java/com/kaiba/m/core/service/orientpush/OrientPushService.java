package com.kaiba.m.core.service.orientpush;

import com.kaiba.lib.base.constant.push.PushRange;
import com.kaiba.lib.base.domain.push.PushModel;
import com.kaiba.lib.base.response.KbCode;
import com.kaiba.lib.base.response.KbException;
import com.kaiba.lib.base.service.IPushService;
import com.kaiba.lib.base.util.appaction.AppActionModel;
import com.kaiba.m.core.constant.orientpush.OPScope;
import com.kaiba.m.core.domain.orientpush.OrientPush;
import com.kaiba.m.core.repository.orientpush.OrientPushRepository;
import com.kaiba.m.core.util.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import com.kaiba.lib.base.constant.push.PushType;
import com.kaiba.lib.base.constant.KbEndpoint;

import java.util.Optional;

@Service
@Slf4j
public class OrientPushService {

    private final OrientPushRepository orientPushRepository;
    private final IPushService pushService;

    public OrientPushService(
            OrientPushRepository orientPushRepository,
            IPushService pushService
    ) {
        this.orientPushRepository = orientPushRepository;
        this.pushService = pushService;
    }

    public OrientPush createOrientPush(OrientPush orientPush) {
        verifyOnCreate(orientPush);
        OPScope scope = OPScope.valueOf(orientPush.getScope()).orElseThrow(() ->
                new KbException(KbCode.ILLEGAL_ARGUMENT, "wrong applicable scope: " + orientPush).li());
        int range = 0;
        String targetId = null;
        switch (scope) {
            case PLATFORM:
                range = PushRange.ALL.getValue();
                break;
            case SITE: {
                range = PushRange.SITE.getValue();
                targetId = String.valueOf(orientPush.getPushSiteId());
                break;
            }
            case USERS: {
                range = PushRange.CUSTOM.getValue();
                break;
            }
        }
        PushModel pushModel = createPushModel(orientPush, targetId, range);
        orientPush.setPushId(pushModel.getId());
        orientPush.setCreateTime(System.currentTimeMillis());
        return orientPushRepository.insert(orientPush);
    }

    public void push(String orientPushId) {
        OrientPush orientPush = orientPushRepository.findById(orientPushId).orElseThrow(() ->
                new KbException(KbCode.RESOURCE_NOT_FOUND, "OrientationPush is not find: " + orientPushId).li());
        OPScope scope = OPScope.valueOf(orientPush.getScope()).orElseThrow(() ->
                new KbException(KbCode.ILLEGAL_ARGUMENT, "wrong applicable scope: " + orientPush).li());
        switch (scope) {
            case PLATFORM:
            case SITE:
                pushService.push(orientPush.getPushId()).check();
                break;
            case USERS: {
                pushService.specifyPushByUserIdIn(
                        orientPush.getPushId(), orientPush.getUserIds().toArray(new Integer[0])).check();
                break;
            }
        }
        orientPushRepository.updateAsPushed(orientPushId);
    }

    public void removePush(String id) {
        orientPushRepository.updateAsSoftDeleted(id);
    }

    public PushModel createPushModel(OrientPush orientPush, String targetId, Integer range) {
        return pushService.add(orientPush.getTitle(), orientPush.getSubTitle(), orientPush.getAction()
                , JsonUtils.getGson().toJson(orientPush.getActionParams()), targetId, PushType.OTHER.getValue(), range, null,
                KbEndpoint.KAIBA.getValue(), orientPush.getCreateUserId(), System.currentTimeMillis() / 1000 + 86400).dataOrThrow();
    }

    public Page<OrientPush> getOrientPushPage(
            String title, Long startTime, Long endTime, Integer scope, Integer page, Integer pageSize) {
        int p = page == null ? 0 : page - 1;
        int ps = pageSize == null || pageSize > 100 ? 15 : pageSize;
        Pageable pageable = PageRequest.of(p, ps);
        return orientPushRepository.getOrientPushList(title, startTime,endTime, scope, pageable);
    }

    public Optional<OrientPush> getOrientPushById(String id) {
        return orientPushRepository.findById(id);
    }

    private void verifyOnCreate(OrientPush orientPush) {
        if (orientPush == null) {
            throw new KbException(KbCode.ILLEGAL_ARGUMENT, "orientationPush should note be null").li();
        }
        if (orientPush.getId() != null) {
            throw new KbException(KbCode.ILLEGAL_ARGUMENT, "OrientPush id must be null on create: " + orientPush).li();
        }
        if (orientPush.getTitle() == null) {
            throw new KbException(KbCode.ILLEGAL_ARGUMENT, "title should note be null: " + orientPush)
                    .setReadableMessage("标题不能为空").li();
        }
        OPScope scope = OPScope.valueOf(orientPush.getScope()).orElseThrow(() ->
                new KbException(KbCode.ILLEGAL_ARGUMENT, "wrong applicable scope: " + orientPush).li());
        switch (scope) {
            case SITE: {
                if (orientPush.getPushSiteId() == null) {
                    throw new KbException(KbCode.ILLEGAL_ARGUMENT, "orientationPush push siteId note be null: " + orientPush)
                            .setReadableMessage("当适用范围为电台时,电台标识不能为空").li();
                }
                break;
            }
            case USERS: {
                if (orientPush.getUserIds() == null || orientPush.getUserIds().size() == 0) {
                    throw new KbException(KbCode.ILLEGAL_ARGUMENT, "orientationPush user list empty: " + orientPush)
                            .setReadableMessage("当适用范围为选择用户ID时,用户标识不能为空").li();
                }
                break;
            }
        }
        if (orientPush.getAction() != null) {
            new AppActionModel(orientPush.getAction(), orientPush.getActionParams()).validate();
        }
    }

}
