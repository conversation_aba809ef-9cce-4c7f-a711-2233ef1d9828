package com.kaiba.m.core.service.note.note;

import com.kaiba.lib.base.constant.note.NoteOrder;
import com.kaiba.lib.base.response.KbCode;
import com.kaiba.lib.base.response.KbException;
import com.kaiba.m.core.domain.note.Note;
import com.kaiba.m.core.service.note.NoteCacheConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.connection.DefaultStringTuple;
import org.springframework.data.redis.connection.StringRedisConnection;
import org.springframework.data.redis.core.RedisCallback;
import org.springframework.data.redis.core.StringRedisTemplate;

import java.util.*;
import java.util.stream.Collectors;

/**
 * author: lyux
 * date: 19-6-5
 *
 * 帖子列表的 id 缓存. 以 thread id 为 key 进行缓存. 目的是为了将板块新帖的分页信息缓存下来.
 */
@Slf4j
class IdListCache {

    private static final String PLACE_HOLDER_ELEMENT = "__place_holder__";
    private static final long PLACE_HOLDER_SCORE = Long.MAX_VALUE - 1;

    private final StringRedisTemplate stringRedisTemplate;
    private final IdListCacheOrder idListCacheOrder;
    private final Random random = new Random();

    IdListCache(StringRedisTemplate stringRedisTemplate, IdListCacheOrder idListCacheOrder) {
        this.stringRedisTemplate = stringRedisTemplate;
        this.idListCacheOrder = idListCacheOrder;
    }

    boolean isPopulating(String threadId) {
        String cacheKey = threadId2key(threadId, idListCacheOrder);
        Set<String> set = stringRedisTemplate.opsForZSet().reverseRange(cacheKey, 0, 0);
        return set != null && set.size() != 0 && !set.contains(PLACE_HOLDER_ELEMENT);
    }

    NoteOrder getSupportedOrder() {
        return idListCacheOrder.noteOrder;
    }

    void populate(String threadId, List<Note> noteList) {
        if (noteList == null) {
            throw new KbException(KbCode.ILLEGAL_ARGUMENT, "note list empty");
        }
        List<Note> cachedNoteList = noteList.size() > NoteCacheConfig.NOTE_ID_LIST_BY_THREAD_INIT_COUNT ?
                noteList.subList(0, NoteCacheConfig.NOTE_ID_LIST_BY_THREAD_INIT_COUNT) : noteList;
        final Set<StringRedisConnection.StringTuple> cacheData = cachedNoteList.stream()
                .filter(note -> note != null && note.getId() != null)
                .map(note -> string2tuple(stringRedisTemplate, note.getId(), idListCacheOrder.getOrderValue(note)))
                .collect(Collectors.toSet());
        cacheData.add(string2tuple(stringRedisTemplate, PLACE_HOLDER_ELEMENT, PLACE_HOLDER_SCORE));
        String cacheKey = threadId2key(threadId, idListCacheOrder);
        stringRedisTemplate.executePipelined((RedisCallback<Object>) connection -> {
            StringRedisConnection conn = (StringRedisConnection) connection;
            conn.del(cacheKey);
            conn.zAdd(cacheKey, cacheData);
            conn.expire(cacheKey, NoteCacheConfig.NOTE_ID_LIST_BY_THREAD_EXPIRE.getSeconds());
            return null;
        });
    }

    void addNote(Note note) {
        if (note == null || note.getId() == null || note.getThreads() == null || note.getThreads().size() == 0) {
            return;
        }
        stringRedisTemplate.execute((RedisCallback<Object>) connection -> {
            StringRedisConnection conn = (StringRedisConnection) connection;
            for (String threadId : note.getThreads()) {
                String cacheKey = threadId2key(threadId, idListCacheOrder);
                if (conn.exists(cacheKey)) {
                    conn.zAdd(cacheKey, idListCacheOrder.getOrderValue(note), note.getId());
                    if (random.nextInt(10) == 1) {
                        tryTrim(conn, threadId);
                    }
                }
            }
            return conn.closePipeline();
        });
    }

    void removeNote(Note note, String threadId) {
        if (note == null || note.getId() == null) {
            return;
        }
        String cacheKey = threadId2key(threadId, idListCacheOrder);
        stringRedisTemplate.opsForZSet().remove(cacheKey, note.getId());
    }

    void invalid(String threadId) {
        String cacheKey = threadId2key(threadId, idListCacheOrder);
        stringRedisTemplate.delete(cacheKey);
    }

    void trim(String threadId) {
        String cacheKey = threadId2key(threadId, idListCacheOrder);
        int trimThreshold = NoteCacheConfig.NOTE_ID_LIST_BY_THREAD_TRIM_THRESHOLD;
        stringRedisTemplate.execute((RedisCallback<Object>) connection -> {
            StringRedisConnection conn = (StringRedisConnection) connection;
            long count = conn.zCard(cacheKey);
            if (count > trimThreshold) {
                conn.zRemRange(cacheKey, 0, count - NoteCacheConfig.NOTE_ID_LIST_BY_THREAD_INIT_COUNT - 1);
            }
            return conn.closePipeline();
        });
    }

    private void tryTrim(StringRedisConnection conn, String threadId) {
        String cacheKey = threadId2key(threadId, idListCacheOrder);
        int trimThreshold = NoteCacheConfig.NOTE_ID_LIST_BY_THREAD_TRIM_THRESHOLD;
        long count = conn.zCard(cacheKey);
        if (NoteCacheConfig.CACHE_DEBUG) {
            log.info("[cache_debug][cache] trim note id list for thread: "
                    + threadId + ", order: " + idListCacheOrder.getNoteOrder() + ", list size: " + count);
        }
        if (count > trimThreshold) {
            conn.zRemRange(cacheKey, 0, count - NoteCacheConfig.NOTE_ID_LIST_BY_THREAD_INIT_COUNT - 1);
        }
    }

    /**
     * 获取分页帖子 id 列表
     * @param threadId 板块 id
     * @param page 页码. 以 1 开始
     * @param pageSize 本页元素个数
     * @return 若缓存无效, 则返回 Optional.empty();
     *         若缓存有效, 不论元素个数是否为0, 返回 Optional.of(list)
     */
    @SuppressWarnings("unchecked")
    Optional<List<String>> getNoteIdList(String threadId, int page, int pageSize) {
        if (NoteCacheConfig.CACHE_DEBUG) {
            log.info("[cache_debug][cache] get note id page: " +
                    threadId + ", order: " + idListCacheOrder.getNoteOrder() + ", [" + page + "," + pageSize + "]");
        }
        if (page <= 0) {
            page = 1;
        }
        if (pageSize <= 0) {
            pageSize = 15;
        }
        int pageIndex = page * pageSize;
        if (pageIndex > NoteCacheConfig.NOTE_ID_LIST_BY_THREAD_INIT_COUNT) {
            // 所请求数据已超过缓存最大值, 视为缓存有效, 但数据为空
            return Optional.of(Collections.emptyList());
        }
        String cacheKey = threadId2key(threadId, idListCacheOrder);
        List<Object> redisResult;
        try {
            if (page == 1) {
                redisResult = stringRedisTemplate.executePipelined((RedisCallback<Object>) connection -> {
                    StringRedisConnection conn = (StringRedisConnection) connection;
                    conn.zRevRange(cacheKey, 0, pageIndex);
                    return null;
                });
            } else {
                int startIndex = (page - 1) * pageSize + 1;
                redisResult = stringRedisTemplate.executePipelined((RedisCallback<Object>) connection -> {
                    StringRedisConnection conn = (StringRedisConnection) connection;
                    conn.zRevRange(cacheKey, 0, 0);
                    conn.zRevRange(cacheKey, startIndex, pageIndex);
                    return null;
                });
            }
        } catch (Exception e) {
            log.error("get noteId list by threadId from redis fail: " + threadId, e);
            return Optional.empty();
        }

        if (redisResult.size() == 0) {
            return Optional.empty();
        } else {
            List<String> idList = new ArrayList<>(pageSize + 1);
            for (Object obj : redisResult) {
                idList.addAll((Set<String>) obj);
            }
            if (idList.size() == 0 || !PLACE_HOLDER_ELEMENT.equals(idList.remove(0))) {
                // 不包含占位数据说明数据有误, 视为缓存无效
                return Optional.empty();
            } else {
                return Optional.of(idList);
            }
        }
    }

    /**
     * 获取分页帖子 id 列表
     * @param threadId 板块 id
     * @param lastNoteId 上一页的最后一条 noteId.
     * @param pageSize 本页元素个数
     * @return 若缓存无效, 则返回 Optional.empty();
     *         若缓存有效, 不论元素个数是否为0, 返回 Optional.of(list)
     */
    Optional<List<String>> getNoteIdListByLastId(String threadId, String lastNoteId, int pageSize) {
        if (NoteCacheConfig.CACHE_DEBUG) {
            log.info("[cache_debug][cache] get note id page by lastNoteId: " +
                    threadId + ", order: " + idListCacheOrder.getNoteOrder() + ", [" + lastNoteId + "," + pageSize + "]");
        }
        if (lastNoteId == null) {
            return Optional.empty();
        }
        if (pageSize <= 0) {
            pageSize = 15;
        }
        String cacheKey = threadId2key(threadId, idListCacheOrder);
        Long rank = stringRedisTemplate.opsForZSet().reverseRank(cacheKey, lastNoteId);
        if (rank == null || rank == 0) {
            // 因为占位符的存在, 所以 rank 为 0 说明或者数据错误, 或者占位符尚未生成. 视为缓存无效.
            return Optional.empty();
        } else {
            Set<String> idSet = stringRedisTemplate.opsForZSet()
                    .reverseRange(cacheKey, rank + 1, rank + pageSize);
            if (idSet != null && idSet.size() > pageSize / 2) {
                // 若从缓存取出的帖数大于所要求长度的一半, 则视为缓存有效.
                // 因为是通过 lastNoteId 的方式取缓存, 因此不足页不会引起问题
                return Optional.of(new ArrayList<>(idSet));
            } else {
                return Optional.empty();
            }
        }
    }

    private static String threadId2key(String threadId, IdListCacheOrder order) {
        return NoteCacheConfig.NOTE_ID_LIST_BY_THREAD_KEY + order.getNoteOrder().getSortName() + "_" + threadId;
    }

    @SuppressWarnings("ConstantConditions")
    private static StringRedisConnection.StringTuple string2tuple(
            StringRedisTemplate stringRedisTemplate, String data, double score) {
        return new DefaultStringTuple(
                stringRedisTemplate.getStringSerializer().serialize(data), data, score);
    }

}
