package com.kaiba.m.core.service.route;

import com.kaiba.lib.base.assembler.Mapper;
import com.kaiba.lib.base.constant.route.KbRouteRuleScope;
import com.kaiba.lib.base.constant.route.KbRouteRuleState;
import com.kaiba.lib.base.constant.route.KbRouteState;
import com.kaiba.lib.base.domain.route.RouteQueryModel;
import com.kaiba.lib.base.response.KbCode;
import com.kaiba.lib.base.response.KbException;
import com.kaiba.lib.base.service.IAuthService;
import com.kaiba.lib.base.service.IAuthTemplateService;
import com.kaiba.lib.base.util.StringUtils;
import com.kaiba.m.core.domain.route.KbAuthRule;
import com.kaiba.m.core.domain.route.KbDispatchRule;
import com.kaiba.m.core.domain.route.KbMatchRule;
import com.kaiba.m.core.domain.route.KbRoute;
import com.kaiba.m.core.repository.route.AuthRuleRepository;
import com.kaiba.m.core.repository.route.DispatchRuleRepository;
import com.kaiba.m.core.repository.route.MatchRuleRepository;
import com.kaiba.m.core.repository.route.RouteRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.regex.Pattern;

/**
 * author: lyux
 * date: 2021-03-25
 */
@Slf4j
@Service
public class RouteService {

    private final RouteRepository routeRepository;
    private final MatchRuleRepository matchRuleRepository;
    private final DispatchRuleRepository dispatchRuleRepository;
    private final AuthRuleRepository authRuleRepository;
    private final IAuthService authService;
    private final IAuthTemplateService authTemplateService;

    public RouteService(
            RouteRepository routeRepository,
            MatchRuleRepository matchRuleRepository,
            DispatchRuleRepository dispatchRuleRepository,
            AuthRuleRepository authRuleRepository,
            IAuthService authService,
            IAuthTemplateService authTemplateService
    ) {
        this.routeRepository = routeRepository;
        this.matchRuleRepository = matchRuleRepository;
        this.dispatchRuleRepository = dispatchRuleRepository;
        this.authRuleRepository = authRuleRepository;
        this.authService = authService;
        this.authTemplateService = authTemplateService;
    }

    // ----------------------------------------------------------------
    // route

    public KbRoute createRoute(KbRoute route) {
        if (route.getId() != null) {
            throw new KbException(KbCode.ILLEGAL_ARGUMENT).r("路由配置 - 创建操作不可传递具体 ID").li();
        }
        if (route.getState() == null) {
            route.setState(KbRouteState.ENABLED.getValue());
        }
        long current = System.currentTimeMillis();
        route.setCreateTime(current);
        route.setUpdateTime(current);
        RouteVerifier.verifyRoute(route);
        verifyPermissions(route.getPermissions());
        KbRoute created = routeRepository.insert(route);
        log.info("route created: " + created);
        return created;
    }

    public KbRoute updateRoute(KbRoute route, boolean updateNullFields) {
        if (route.getId() == null) {
            throw new KbException(KbCode.ILLEGAL_ARGUMENT).r("路由配置 - 更新操作需要 ID").li();
        }
        if (route.getPermissions() != null && route.getPermissions().size() != 0) {
            verifyPermissions(route.getPermissions());
        }
        KbRoute tobeUpdated;
        if (updateNullFields) {
            tobeUpdated = route;
        } else {
            tobeUpdated = getRouteById(route.getId())
                    .orElseThrow(() -> new KbException(KbCode.RESOURCE_NOT_FOUND).li());
            Mapper.map(route, tobeUpdated);
        }
        RouteVerifier.verifyRoute(tobeUpdated);
        tobeUpdated.setUpdateTime(System.currentTimeMillis());
        KbRoute updated = routeRepository.save(tobeUpdated);
        log.info("route updated: " + updated);
        return updated;
    }

    public void updateRouteState(String id, KbRouteState state) {
        KbRoute route = routeRepository.findById(id).orElseThrow(
                () -> new KbException(KbCode.REQUEST_PARAM_INVALID).li());
        if (route.getState() == state.getValue()) {
            return;
        }
        route.setState(state.getValue());
        RouteVerifier.verifyRoute(route);
        routeRepository.updateState(id, state);
    }

    public void deleteRoute(String id) {
        routeRepository.deleteById(id);
    }

    public Optional<KbRoute> getRouteById(String id) {
        return routeRepository.findById(id);
    }

    public List<KbRoute> getRouteListByIdIn(String[] ids) {
        if (ids == null || ids.length == 0) {
            return Collections.emptyList();
        } else {
            return routeRepository.findByIdIn(ids);
        }
    }

    public Page<KbRoute> getRoutePageByPath(String path, Integer page, Integer pageSize) {
        return routeRepository.findByPaths(path, routePageable(page, pageSize));
    }

    public Page<KbRoute> getRoutePageByPathStartWith(String path, Integer page, Integer pageSize) {
        return routeRepository.findByPathsRegex(
                "(?i)" + StringUtils.escapeRegex(path), routePageable(page, pageSize));
    }

    public Page<KbRoute> getRoutePage(Integer[] states, Integer page, Integer pageSize) {
        if (states == null || states.length == 0) {
            return routeRepository.findAllBy(routePageable(page, pageSize));
        } else {
            return routeRepository.findByStateIn(states, routePageable(page, pageSize));
        }
    }

    public Page<KbRoute> getRoutePageByModel(RouteQueryModel model) {
        return routeRepository.getListWithQueryModel(model);
    }

    // ----------------------------------------------------------------
    // match rule

    public KbMatchRule createMatchRule(KbMatchRule matchRule) {
        if (matchRule.getId() != null) {
            throw new KbException(KbCode.ILLEGAL_ARGUMENT).r("匹配规则 - 创建操作不可传递具体 ID");
        }
        if (matchRule.getScope() == null) {
            matchRule.setScope(KbRouteRuleScope.COMMON.getValue());
        }
        if (matchRule.getState() == null) {
            matchRule.setState(KbRouteRuleState.ENABLED.getValue());
        }
        long current = System.currentTimeMillis();
        matchRule.setCreateTime(current);
        matchRule.setUpdateTime(current);
        RouteVerifier.verifyMatchRule(matchRule);
        KbMatchRule created = matchRuleRepository.insert(matchRule);
        log.info("match rule created: " + created);
        return created;
    }

    public KbMatchRule updateMatchRule(KbMatchRule matchRule, boolean updateNullFields) {
        if (matchRule.getId() == null) {
            throw new KbException(KbCode.ILLEGAL_ARGUMENT).r("匹配规则 - 更新操作需要 ID");
        }
        KbMatchRule tobeUpdated;
        if (updateNullFields) {
            tobeUpdated = matchRule;
        } else {
            tobeUpdated = getMatchRuleById(matchRule.getId())
                    .orElseThrow(() -> new KbException(KbCode.RESOURCE_NOT_FOUND).li());
            Mapper.map(matchRule, tobeUpdated);
        }
        RouteVerifier.verifyMatchRule(tobeUpdated);
        tobeUpdated.setUpdateTime(System.currentTimeMillis());
        KbMatchRule updated = matchRuleRepository.save(tobeUpdated);
        log.info("match rule updated: " + updated);
        return updated;
    }

    public void updateMatchRuleState(String id, KbRouteRuleState state) {
        matchRuleRepository.updateState(id, state);
    }

    public void deleteMatchRule(String id) {
        if (routeRepository.existsByMatchRuleIds(id)) {
            throw new KbException(KbCode.ILLEGAL_STATE, "match rule still in use: " + id).r("尚有路由引用该匹配规则").li();
        }
        matchRuleRepository.deleteById(id);
    }

    public Optional<KbMatchRule> getMatchRuleById(String id) {
        return matchRuleRepository.findById(id);
    }

    public List<KbMatchRule> getMatchRuleListByIdIn(String[] ids) {
        if (ids == null || ids.length == 0) {
            return Collections.emptyList();
        } else {
            return matchRuleRepository.findByIdIn(ids);
        }
    }

    public Page<KbMatchRule> getMatchRulePage(Integer state, Integer scope, Integer page, Integer pageSize) {
        if (state == null && scope == null) {
            return matchRuleRepository.findByOrderByIdDesc(pageable(page, pageSize));
        } else if (state == null) {
            return matchRuleRepository.findByScopeOrderByIdDesc(scope, pageable(page, pageSize));
        } else if (scope == null) {
            return matchRuleRepository.findByStateOrderByIdDesc(state, pageable(page, pageSize));
        } else {
            return matchRuleRepository.findByStateAndScopeOrderByIdDesc(state, scope, pageable(page, pageSize));
        }
    }

    public Page<KbMatchRule> getIpMatchRulePage(Integer scope, Integer page, Integer pageSize) {
        if (scope == null) {
            return matchRuleRepository.findByIpExistsOrderByIdDesc(pageable(page, pageSize));
        } else {
            return matchRuleRepository.findByIpExistsAndScopeOrderByIdDesc(scope, pageable(page, pageSize));
        }
    }

    public Page<KbMatchRule> getHeaderMatchRulePage(Integer scope, Integer page, Integer pageSize) {
        if (scope == null) {
            return matchRuleRepository.findByHeaderExistsOrderByIdDesc(pageable(page, pageSize));
        } else {
            return matchRuleRepository.findByHeaderExistsAndScopeOrderByIdDesc(scope, pageable(page, pageSize));
        }
    }

    public Page<KbMatchRule> getCookieMatchRulePage(Integer scope, Integer page, Integer pageSize) {
        if (scope == null) {
            return matchRuleRepository.findByCookieExistsOrderByIdDesc(pageable(page, pageSize));
        } else {
            return matchRuleRepository.findByCookieExistsAndScopeOrderByIdDesc(scope, pageable(page, pageSize));
        }
    }

    public Page<KbMatchRule> getMethodMatchRulePage(Integer scope, Integer page, Integer pageSize) {
        if (scope == null) {
            return matchRuleRepository.findByMethodExistsOrderByIdDesc(pageable(page, pageSize));
        } else {
            return matchRuleRepository.findByMethodExistsAndScopeOrderByIdDesc(scope, pageable(page, pageSize));
        }
    }

    // ----------------------------------------------------------------
    // dispatch rule

    public KbDispatchRule createDispatchRule(KbDispatchRule dispatchRule) {
        if (dispatchRule.getId() != null) {
            throw new KbException(KbCode.ILLEGAL_ARGUMENT).r("派发规则 - 创建操作不可传递具体 ID");
        }
        if (dispatchRule.getScope() == null) {
            dispatchRule.setScope(KbRouteRuleScope.COMMON.getValue());
        }
        long current = System.currentTimeMillis();
        dispatchRule.setCreateTime(current);
        dispatchRule.setUpdateTime(current);
        RouteVerifier.verifyDispatchRule(dispatchRule);
        KbDispatchRule created = dispatchRuleRepository.insert(dispatchRule);
        log.info("dispatch rule created: " + created);
        return created;
    }

    public KbDispatchRule updateDispatchRule(KbDispatchRule dispatchRule, boolean updateNullFields) {
        if (dispatchRule.getId() == null) {
            throw new KbException(KbCode.ILLEGAL_ARGUMENT).r("派发规则 - 更新操作需要 ID");
        }
        KbDispatchRule tobeUpdated;
        if (updateNullFields) {
            tobeUpdated = dispatchRule;
        } else {
            tobeUpdated = getDispatchRuleById(dispatchRule.getId())
                    .orElseThrow(() -> new KbException(KbCode.RESOURCE_NOT_FOUND).li());
            Mapper.map(dispatchRule, tobeUpdated);
        }
        RouteVerifier.verifyDispatchRule(tobeUpdated);
        tobeUpdated.setUpdateTime(System.currentTimeMillis());
        KbDispatchRule updated = dispatchRuleRepository.save(tobeUpdated);
        log.info("dispatch rule updated: " + updated);
        return updated;
    }

    public void deleteDispatchRule(String id) {
        if (routeRepository.existsByDispatchRuleId(id)) {
            throw new KbException(KbCode.ILLEGAL_STATE, "still in use").r("尚有路由引用该派发规则").li();
        }
        dispatchRuleRepository.deleteById(id);
    }

    public Optional<KbDispatchRule> getDispatchRuleById(String id) {
        return dispatchRuleRepository.findById(id);
    }

    public List<KbDispatchRule> getDispatchRuleListByIdIn(String[] ids) {
        if (ids == null || ids.length == 0) {
            return Collections.emptyList();
        } else {
            return dispatchRuleRepository.findByIdIn(ids);
        }
    }

    public Page<KbDispatchRule> getDispatchRulePage(Integer scope, Integer page, Integer pageSize) {
        if (scope == null) {
            return dispatchRuleRepository.findByOrderByNameAsc(pageable(page, pageSize));
        } else {
            return dispatchRuleRepository.findByScopeOrderByNameAsc(scope, pageable(page, pageSize));
        }
    }

    // ----------------------------------------------------------------
    // auth rule

    public KbAuthRule createAuthRule(KbAuthRule authRule) {
        if (authRule.getId() != null) {
            throw new KbException(KbCode.ILLEGAL_ARGUMENT).r("鉴权规则 - 创建操作不可传递具体 ID");
        }
        if (authRule.getScope() == null) {
            authRule.setScope(KbRouteRuleScope.COMMON.getValue());
        }
        if (authRule.getState() == null) {
            authRule.setState(KbRouteRuleState.ENABLED.getValue());
        }
        long current = System.currentTimeMillis();
        authRule.setCreateTime(current);
        authRule.setUpdateTime(current);
        RouteVerifier.verifyAuthRule(authRule);
        KbAuthRule created = authRuleRepository.insert(authRule);
        log.info("auth rule created: " + created);
        return created;
    }

    public KbAuthRule updateAuthRule(KbAuthRule authRule, boolean updateNullFields) {
        if (authRule.getId() == null) {
            throw new KbException(KbCode.ILLEGAL_ARGUMENT).r("鉴权规则 - 更新操作需要 ID");
        }
        KbAuthRule tobeUpdated;
        if (updateNullFields) {
            tobeUpdated = authRule;
        } else {
            tobeUpdated = getAuthRuleById(authRule.getId())
                    .orElseThrow(() -> new KbException(KbCode.RESOURCE_NOT_FOUND).li());
            Mapper.map(authRule, tobeUpdated);
        }
        RouteVerifier.verifyAuthRule(tobeUpdated);
        tobeUpdated.setUpdateTime(System.currentTimeMillis());
        KbAuthRule updated = authRuleRepository.save(tobeUpdated);
        log.info("auth rule updated: " + updated);
        return updated;
    }

    public void updateAuthRuleState(String id, KbRouteRuleState state) {
        matchRuleRepository.updateState(id, state);
    }

    public void deleteAuthRule(String id) {
        if (routeRepository.existsByAuthRuleIds(id)) {
            throw new KbException(KbCode.ILLEGAL_STATE, "auth rule still in use: " + id).r("尚有路由引用该鉴权规则").li();
        }
        authRuleRepository.deleteById(id);
    }

    public Optional<KbAuthRule> getAuthRuleById(String id) {
        return authRuleRepository.findById(id);
    }

    public List<KbAuthRule> getAuthRuleListByIdIn(String[] ids) {
        if (ids == null || ids.length == 0) {
            return Collections.emptyList();
        } else {
            return authRuleRepository.findByIdIn(ids);
        }
    }

    public Page<KbAuthRule> getAuthRulePage(Integer state, Integer scope, Integer page, Integer pageSize) {
        if (state == null && scope == null) {
            return authRuleRepository.findByOrderByIdDesc(pageable(page, pageSize));
        } else if (state == null) {
            return authRuleRepository.findByScopeOrderByIdDesc(scope, pageable(page, pageSize));
        } else if (scope == null) {
            return authRuleRepository.findByStateOrderByIdDesc(state, pageable(page, pageSize));
        } else {
            return authRuleRepository.findByStateAndScopeOrderByIdDesc(state, scope, pageable(page, pageSize));
        }
    }

    public Page<KbAuthRule> getAuthRulePageByMethod(Integer method, Integer scope, Integer page, Integer pageSize) {
        if (scope == null) {
            return authRuleRepository.findByMethodOrderByIdDesc(method, pageable(page, pageSize));
        } else {
            return authRuleRepository.findByMethodAndScopeOrderByIdDesc(method, scope, pageable(page, pageSize));
        }
    }

    // ----------------------------------------------------------------

    private static final Pattern PERMISSION_WILDCARD_SITE_PATTEN = Pattern.compile("\\$\\{siteId}");
    private static final Pattern PERMISSION_WILDCARD_USER_PATTEN = Pattern.compile("\\$\\{userId}");
    private static final Pattern PERMISSION_WILDCARD_PATTEN = Pattern.compile("\\$\\{\\w+}");

    private void verifyPermissions(List<String> permissions) {
        if (permissions == null || permissions.size() == 0) {
            return;
        }
        Set<String> permissionSet = new HashSet<>(permissions.size());
        Set<String> permissionTemplateSet = new HashSet<>(permissions.size());
        for (String permission: permissions) {
            if (PERMISSION_WILDCARD_SITE_PATTEN.matcher(permission).find()) {
                permissionTemplateSet.add(permission);
            } else if (PERMISSION_WILDCARD_USER_PATTEN.matcher(permission).find()) {
                permissionTemplateSet.add(permission);
            } else if (PERMISSION_WILDCARD_PATTEN.matcher(permission).find()) {
                throw new KbException(KbCode.ILLEGAL_ARGUMENT).r("暂时只支持配置 ${siteId} 和 ${userId} 两种通配权限").li();
            } else {
                permissionSet.add(permission);
            }
        }
        if (permissions.size() != (permissionSet.size() + permissionTemplateSet.size())) {
            throw new KbException(KbCode.ILLEGAL_ARGUMENT, "权限列表中有重复的权限").li();
        }
        if (permissionSet.size() != 0) {
            int validPermissionSize = authService
                    .getPermissionModelListIn(permissions.toArray(new String[0]))
                    .data().map(List::size).orElse(0);
            if (validPermissionSize != permissionSet.size()) {
                throw new KbException(KbCode.ILLEGAL_ARGUMENT, "未找到部分权限定义, 请核对权限列表").li();
            }
        }
        if (permissionTemplateSet.size() != 0) {
            int validPermissionTemplateSize = authTemplateService
                    .getPermissionTemplateListByFormatIn(permissionTemplateSet.toArray(new String[0]))
                    .data().map(List::size).orElse(0);
            if (validPermissionTemplateSize != permissionTemplateSet.size()) {
                throw new KbException(KbCode.ILLEGAL_ARGUMENT, "未找到部分通配权限定义, 请核对权限列表").li();
            }
        }
    }

    private static Pageable pageable(Integer page, Integer pageSize) {
        int p = page == null ? 0 : page - 1;
        int ps = pageSize == null ? 20 : pageSize;
        return PageRequest.of(p, ps);
    }

    private static Pageable routePageable(Integer page, Integer pageSize) {
        int p = page == null ? 0 : page - 1;
        int ps = pageSize == null ? 20 : pageSize;
        return PageRequest.of(p, ps, Sort.by(Sort.Direction.ASC, "paths"));
    }

}
