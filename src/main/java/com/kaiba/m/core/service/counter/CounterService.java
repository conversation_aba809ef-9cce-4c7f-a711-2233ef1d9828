package com.kaiba.m.core.service.counter;

import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.LoadingCache;
import com.kaiba.lib.base.domain.counter.VirtualExpressionUtil;
import com.kaiba.lib.base.response.KbCode;
import com.kaiba.lib.base.response.KbException;
import com.kaiba.lib.base.rte.RelativeTimeExpression;
import com.kaiba.m.core.domain.counter.KbCounter;
import com.kaiba.m.core.domain.counter.KbCounterUser;
import com.kaiba.m.core.domain.counter.KbCounterVirtual;
import com.kaiba.m.core.middleware.instantcache.KbInstantCaffeineService;
import com.kaiba.m.core.repository.counter.CounterRepository;
import com.kaiba.m.core.repository.counter.CounterVirtualRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * author: lyux
 * date: 2021-05-26
 */
@Slf4j
@Service
public class CounterService {

    private static final String CACHE_ID_PLACEHOLDER = "none";

    private final UserCountService userCountService;
    private final TotalCountService totalCountService;
    private final CounterRepository counterRepository;
    private final CounterVirtualRepository virtualRepository;
    private final StringRedisTemplate redisTemplate;
    private final LoadingCache<String, CounterSetting> settingCacheById;
    private final LoadingCache<String, String> idCacheByKey;

    public CounterService(
            TotalCountService totalCountService,
            CounterRepository counterRepository,
            StringRedisTemplate redisTemplate,
            KbInstantCaffeineService instantCaffeineService,
            UserCountService userCountService,
            CounterVirtualRepository virtualRepository
    ) {
        this.totalCountService = totalCountService;
        this.counterRepository = counterRepository;
        this.redisTemplate = redisTemplate;
        this.userCountService = userCountService;
        this.virtualRepository = virtualRepository;
        this.settingCacheById = instantCaffeineService
                .registerInstantCache("counter_setting", Caffeine.newBuilder()
                .expireAfterWrite(Duration.ofMinutes(5))
                .build(this::createCounterSetting));
        // 不直接建立 key 值为索引的实例缓存, 是为了分布式各节点缓存过期时的一致性.
        // 因此只需要注册 settingCacheById 为实时缓存即可.
        this.idCacheByKey = Caffeine.newBuilder()
                .expireAfterWrite(Duration.ofMinutes(10))
                .build(key -> counterRepository.findFirstByKey(key)
                        .map(KbCounter::getId)
                        .orElse(CACHE_ID_PLACEHOLDER));
    }

    // -------------------------------------------------------------

    public Optional<CounterSetting> getSettingById(String counterId) {
        return Optional.ofNullable(settingCacheById.get(counterId))
                .filter(counter -> counter.getId() != null);
    }

    public Optional<CounterSetting> getSettingByKey(String key) {
        String counterId = idCacheByKey.get(key);
        if (counterId == null || CACHE_ID_PLACEHOLDER.equalsIgnoreCase(counterId)) {
            return Optional.empty();
        } else {
            return getSettingById(counterId);
        }
    }

    public long incrCount(Integer userId, CounterSetting setting, long count) {
        if (!userCountService.checkUserLimit(userId, setting, count)) {
            throw new KbException(KbCode.ILLEGAL_STATE, "counter user exceed limit").r("已超出限额").ld();
        }

        // 延时将计数数据写入数据库
        totalCountService.addCount(setting.getId(), count);
        if (setting.isRecordUser() && userId != null) {
            userCountService.recordUserCount(userId, setting, count);
        }

        // 将计数存入缓存
        String cacheKey = CounterConst.getCountCacheKey(setting.getId());
        Long result = redisTemplate.opsForValue().increment(cacheKey, count);
        if (result == null || result == count) {
            // 缓存不存在或值为0, 可能是因为尚未初始化, 也可能是因为缓存过期导致的, 因此这里尝试以数据库中的计数初始化缓存
            KbCounter counter = counterRepository.increaseCount(setting.getId(), count);
            if (counter == null) {
                throw new KbException(KbCode.RESOURCE_NOT_FOUND, "counter not exists: " + setting.getId()).li();
            }
            redisTemplate.opsForValue().set(cacheKey, counter.getCount().toString(), setting.getCountCacheExpire());
            return counter.getCount();
        } else {
            return result;
        }
    }

    public long incrVirtualCount(CounterSetting setting, long count) {
        String virtualId;
        long virtualCount;
        if (setting.getVirtual() == null) {
            // 该计数器实例的虚拟计数规则尚未开启, 新建
            KbCounterVirtual virtual = createVirtual(
                    setting.getId() + "_virtual", count, null, null);
            counterRepository.updateVirtualId(setting.getId(), virtual.getId());
            // 虚拟计数尚未开启, 设置虚拟计数即为开启虚拟计数. 驱逐 settings 缓存使之更新.
            settingCacheById.invalidate(setting.getId());
            virtualId = virtual.getId();
            virtualCount = count;
        } else {
            virtualId = setting.getVirtual().getId();
            KbCounterVirtual increasedVirtual = virtualRepository.incrVirtualCount(virtualId, count);
            virtualCount = increasedVirtual == null || increasedVirtual.getVirtualCount() == null ?
                    0 : increasedVirtual.getVirtualCount();
        }
        String cacheKey = CounterConst.getVirtualCountCacheKey(virtualId);
        redisTemplate.opsForValue().set(cacheKey, Long.toString(virtualCount), setting.getCountCacheExpire());
        return virtualCount;
    }

    public long getDisplayCount(CounterSetting setting) {
        long virtualCount = getVirtualCount(setting);
        return setting.getDisplayCount(getActualCount(setting), virtualCount);
    }

    public long getActualCount(CounterSetting setting) {
        String cacheKey = CounterConst.getCountCacheKey(setting.getId());
        String str = redisTemplate.opsForValue().get(cacheKey);
        if (str != null) {
            try {
                return Long.parseLong(str);
            } catch (NumberFormatException e) {
                log.error("getCount() malformed cache: " + str);
            }
        }
        long count = counterRepository.findById(setting.getId()).map(KbCounter::getCount).orElse(0L);
        String value = Long.toString(count);
        redisTemplate.opsForValue().set(cacheKey, value, setting.getCountCacheExpire());
        return count;
    }

    public long getVirtualCount(CounterSetting setting) {
        if (setting.getVirtual() == null) {
            return 0;
        } else {
            String virtualId = setting.getVirtual().getId();
            String cacheKey = CounterConst.getVirtualCountCacheKey(virtualId);
            String str = redisTemplate.opsForValue().get(cacheKey);
            if (str != null) {
                try {
                    return Long.parseLong(str);
                } catch (NumberFormatException e) {
                    log.error("getCount() malformed cache: " + str);
                }
            }
            long count = virtualRepository.findById(virtualId).map(KbCounterVirtual::getVirtualCount).orElse(0L);
            String value = Long.toString(count);
            redisTemplate.opsForValue().set(cacheKey, value, setting.getCountCacheExpire());
            return count;
        }
    }

    // -------------------------------------------------------------

    public KbCounter createCounter(KbCounter counter) {
        if (counter.getId() != null) {
            throw new KbException(KbCode.ILLEGAL_ARGUMENT, "id should be null in create operation").li();
        }
        verifyCounter(counter);
        if (counter.getKey() != null && counterRepository.existsByKey(counter.getKey())) {
            // 因为 key 可为空, 所以使用唯一索引无法满足这个需求, 必须通过查询确认重复性
            throw new KbException(KbCode.ILLEGAL_ARGUMENT, "counter key already existed: " + counter.getKey()).li();
        }
        long current = System.currentTimeMillis();
        counter.setCreateTime(current);
        counter.setUpdateTime(current);
        return counterRepository.insert(counter);
    }

    public KbCounter getOrCreateCounterByKey(KbCounter counter) {
        if (counter.getId() != null) {
            throw new KbException(KbCode.ILLEGAL_ARGUMENT, "id should be null").li();
        }
        if (counter.getKey() == null) {
            throw new KbException(KbCode.ILLEGAL_ARGUMENT, "key should not be null").li();
        }
        return counterRepository.findFirstByKey(counter.getKey())
                .orElseGet(() -> {
                    verifyCounter(counter);
                    long current = System.currentTimeMillis();
                    counter.setCreateTime(current);
                    counter.setUpdateTime(current);
                    return counterRepository.save(counter);
                });
    }

    public KbCounter updateCounter(KbCounter counter) {
        if (counter.getId() == null) {
            throw new KbException(KbCode.ILLEGAL_ARGUMENT, "id should not be null in update operation").li();
        }
        verifyCounter(counter);
        counter.setUpdateTime(System.currentTimeMillis());
        return counterRepository.update(counter);
    }

    public void clearCounter(String counterId, String secret) {
        if (!CounterConst.SECRET.equals(secret)) {
            throw new KbException(KbCode.REQUEST_PARAM_INVALID, "wrong secret").li();
        }
        KbCounter counter = counterRepository.setCount(counterId, 0);
        if (counter == null) {
            throw new KbException(KbCode.RESOURCE_NOT_FOUND).li();
        }
        String cacheKey = CounterConst.getCountCacheKey(counterId);
        redisTemplate.delete(cacheKey);
        if (counter.getRecordUser() != null && counter.getRecordUser()) {
            userCountService.clearUserCountByCounter(counterId, secret);
        }
    }

    public KbCounter updateCounterVirtualId(String counterId, String virtualId) {
        return counterRepository.updateVirtualId(counterId, virtualId);
    }

    public Optional<KbCounter> getCounterById(String counterId) {
        return counterRepository.findById(counterId);
    }

    public Optional<KbCounter> getCounterByKey(String key) {
        return counterRepository.findFirstByKey(key);
    }

    public List<KbCounter> getCounterListByIdIn(String[] counterIds) {
        return counterRepository.findByIdIn(counterIds);
    }

    public Page<KbCounter> getCounterPage(Integer page, Integer pageSize) {
        int p = page == null ? 0 : page - 1;
        int ps = pageSize == null ? 20 : pageSize;
        return counterRepository.findAllByOrderByIdDesc(PageRequest.of(p, ps));
    }

    // -------------------------------------------------------------

    public KbCounterVirtual createVirtual(
            String key, Long virtualCount, String virtualExpression, String description) {
        long vc = virtualCount == null ? 0 : virtualCount;
        if (key == null) {
            return createVirtual(vc, virtualExpression, description);
        } else {
            return virtualRepository.upsertByKey(key, vc, virtualExpression);
        }
    }

    public KbCounterVirtual createVirtual(Long virtualCount, String virtualExpression, String description) {
        long current = System.currentTimeMillis();
        long vc = virtualCount == null ? 0 : virtualCount;
        KbCounterVirtual tobeCreatedVirtual = new KbCounterVirtual();
        tobeCreatedVirtual.setVirtualCount(vc);
        tobeCreatedVirtual.setExpression(virtualExpression);
        tobeCreatedVirtual.setDescription(description);
        tobeCreatedVirtual.setCreateTime(current);
        tobeCreatedVirtual.setUpdateTime(current);
        verifyVirtual(tobeCreatedVirtual);
        return virtualRepository.insert(tobeCreatedVirtual);
    }

    public KbCounterVirtual updateVirtual(String virtualId, Long virtualCount, String expression, String description) {
        return virtualRepository.update(virtualId, virtualCount, expression, description);
    }

    public KbCounterVirtual updateVirtualCount(String virtualId, long virtualCount) {
        return virtualRepository.setVirtualCount(virtualId, virtualCount);
    }

    public KbCounterVirtual increaseVirtualCount(String virtualId, long count) {
        return virtualRepository.incrVirtualCount(virtualId, count);
    }

    public Optional<KbCounterVirtual> getVirtualById(String id) {
        return virtualRepository.findById(id);
    }

    public List<KbCounterVirtual> getVirtualListByIdIn(String[] ids) {
        return virtualRepository.findByIdIn(ids);
    }

    public Optional<KbCounterVirtual> getVirtualByKey(String key) {
        return virtualRepository.findFirstByKey(key);
    }

    // -------------------------------------------------------------

    public long getUserCountFromCache(Integer userId, CounterSetting setting) {
        return userCountService.getUserCountFromCache(userId, setting);
    }

    public Page<KbCounterUser> getUserCountRecordFromDB(Integer userId, String counterId, Integer page, Integer pageSize) {
        return userCountService.getUserCountRecordFromDB(userId, counterId, page, pageSize);
    }

    // -------------------------------------------------------------

    private void flushCounterCacheToDB(String counterId) {
        String cacheKey = CounterConst.getCountCacheKey(counterId);
        String str = redisTemplate.opsForValue().get(cacheKey);
        if (str == null) {
            log.error("flushCounterCacheToDB(), cache empty: " + cacheKey);
        } else {
            counterRepository.setCount(counterId, Long.parseLong(str));
        }
    }

    private CounterSetting createCounterSetting(String counterId) {
        return counterRepository.findById(counterId)
                .map(counter -> {
                    KbCounterVirtual virtual = null;
                    if (counter.getVirtualId() != null) {
                        virtual = virtualRepository.findById(counter.getVirtualId()).orElse(null);
                    }
                    return new CounterSetting(counter, virtual);
                })
                .orElse(new CounterSetting());
    }

    private static void verifyCounter(KbCounter counter) {
        if (counter.getId() == null) {
            counter.setCount(0L);
        }
        if (counter.getUidLimit() == null) {
            counter.setUidLimit(0L);
        } else if (counter.getUidLimit() < 0) {
            throw new KbException(KbCode.ILLEGAL_ARGUMENT, "uidLimit cannot be negative: " + counter.getUidLimit())
                    .r("用户计数上限必须非负").li();
        }
        long current = System.currentTimeMillis();
        if (counter.getUidLimitResetRTE() != null) {
            long result = RelativeTimeExpression.calculate(current, counter.getUidLimitResetRTE());
            if (result - current < CounterConst.RTE_MIN.toMillis()) {
                throw new KbException(KbCode.ILLEGAL_ARGUMENT,
                        "uidLimitResetRTE min limit: " + counter.getUidLimitResetRTE())
                        .r("用户计数上限重置时间不得短于 " + CounterConst.RTE_MIN.getSeconds() + " 秒").li();
            }
        }
        if (counter.getEndTime() == null) {
            counter.setEndTime(CounterConst.END_TIME_DEFAULT.toMillis());
        } else if (counter.getEndTime() - current < CounterConst.END_TIME_MIN.toMillis()) {
            throw new KbException(KbCode.ILLEGAL_ARGUMENT, "endTime too near: " + counter.getEndTime())
                    .r("结束时间距离当前时间太近或小于当前时间").li();
        }
    }

    private static void verifyVirtual(KbCounterVirtual virtual) {
        if (virtual.getKey() == null) {
            virtual.setKey(UUID.randomUUID().toString());
        }
        if (virtual.getVirtualCount() == null) {
            virtual.setVirtualCount(0L);
        }
        if (virtual.getExpression() != null) {
            VirtualExpressionUtil.calculate(
                    virtual.getExpression(), virtual.getKey().hashCode(), 100, virtual.getVirtualCount());
        }
    }

}
