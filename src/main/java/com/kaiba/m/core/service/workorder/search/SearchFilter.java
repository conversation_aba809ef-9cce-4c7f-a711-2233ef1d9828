package com.kaiba.m.core.service.workorder.search;

import java.util.ArrayList;
import java.util.List;

/**
 * 用于内容筛选,类似SQL中的where, 基于正排索引搜索效率较差,使用方式灵活支持多种表达式和函数
 * <AUTHOR>
 * @version SearchFilter, v0.1 2024/7/30 18:34 daopei Exp $
 **/
public class SearchFilter {


    private List<String> filters = new ArrayList<>();


    public SearchFilter equals(String field, String value) {
        filters.add(field + " = " + value);
        return this;
    }

    public SearchFilter notEquals(String field, String value) {
        filters.add(field + " != " + value);
        return this;
    }

    public SearchFilter greaterThan(String field, String value) {
        filters.add(field + " > " + value);
        return this;
    }

    public SearchFilter lessThan(String field, String value) {
        filters.add(field + " < " + value);
        return this;
    }

    public SearchFilter greaterThanOrEquals(String field, String value) {
        filters.add(field + " >= " + value);
        return this;
    }

    public SearchFilter lessThanOrEquals(String field, String value) {
        filters.add(field + " <= " + value);
        return this;
    }

    public SearchFilter in(String field, List<String> value) {
        filters.add("in(" + field + ", \"" + String.join("|", value) + "\")" );
        return this;
    }

    public SearchFilter notIn(String field, List<String> value) {
        filters.add("notin(" + field + ", \"" + String.join("|", value) + "\")" );
        return this;
    }

    public String toFilter() {
        return String.join(" AND ", filters);
    }

}
