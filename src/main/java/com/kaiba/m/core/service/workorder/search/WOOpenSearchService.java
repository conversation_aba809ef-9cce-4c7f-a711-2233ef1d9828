package com.kaiba.m.core.service.workorder.search;

import com.aliyun.opensearch.DocumentClient;
import com.aliyun.opensearch.OpenSearchClient;
import com.aliyun.opensearch.sdk.generated.OpenSearch;
import com.aliyun.opensearch.sdk.generated.commons.OpenSearchClientException;
import com.aliyun.opensearch.sdk.generated.commons.OpenSearchException;
import com.aliyun.opensearch.sdk.generated.document.Command;
import com.aliyun.opensearch.sdk.generated.document.DocumentConstants;
import com.google.gson.reflect.TypeToken;
import com.kaiba.lib.base.assembler.Mapper;
import com.kaiba.lib.base.domain.common.Audio;
import com.kaiba.lib.base.domain.common.Image;
import com.kaiba.lib.base.domain.common.Video;
import com.kaiba.lib.base.domain.workorder.WOCaseContentBrief;
import com.kaiba.lib.base.domain.workorder.WOCaseSearchResult;
import com.kaiba.lib.base.service.IUserService;
import com.kaiba.lib.base.util.GsonUtils;
import com.kaiba.m.core.domain.workorder.WOCase;
import com.kaiba.m.core.domain.workorder.WOTaggedCase;
import com.kaiba.m.core.service.workorder.tag.WOTagConsts;
import com.kaiba.m.core.util.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;

/**
 * author: lyux
 * date: 2024-05-31
 */
@Slf4j
@Service
public class WOOpenSearchService {

    private final DocumentClient documentClient;
    private final IUserService userService;

    @Value("${kaiba.opensearch.wo.app_name}")
    public String OPEN_SEARCH_APP_NAME;


    public WOOpenSearchService(
            @Value("${kaiba.opensearch.accessKey}") String accessKey,
            @Value("${kaiba.opensearch.secret}") String secret,
            @Value("${kaiba.opensearch.host}") String searchHost,
            IUserService userService
    ) {
        this.userService = userService;
        OpenSearchClient serviceClient = new OpenSearchClient(new OpenSearch(accessKey, secret, searchHost));
        this.documentClient = new DocumentClient(serviceClient);
    }

    public void createDoc(WOCase woCase, WOCaseContentBrief brief, String searchable) {
        WOOpenSearchDocument doc = toDoc(woCase, brief);
        doc.setSearchable(searchable);
        documentClient.add(Mapper.asMap(doc));
        try {
            documentClient.commit(OPEN_SEARCH_APP_NAME, WOTagConsts.OPEN_SEARCH_TABLE_NAME);
        } catch (OpenSearchException|OpenSearchClientException e) {
            log.error("createDoc fail: " + woCase.getId(), e);
        }
    }

    public void updateDocByCase(WOCase woCase) {
        WOOpenSearchDocument doc = toDoc(woCase, null);
        documentClient.update(Mapper.asMap(doc));
        try {
            documentClient.commit(OPEN_SEARCH_APP_NAME, WOTagConsts.OPEN_SEARCH_TABLE_NAME);
        } catch (OpenSearchException|OpenSearchClientException e) {
            log.error("updateDoc fail: " + woCase.getId(), e);
        }
    }

    public void updateDocTag(String caseId, Function<String, List<WOTaggedCase>> taggedProvider) {
        Set<Long> tags = new HashSet<>();
        taggedProvider.apply(caseId).forEach(tagged ->
                tags.addAll(WOTagConsts.obtainTagCodesWithParent(tagged.getTag())));
        WOOpenSearchDocument doc = new WOOpenSearchDocument();
        doc.setCase_id(caseId);
        if (tags.isEmpty()) {
            doc.setTags(Collections.singleton(0L));
        } else {
            doc.setTags(tags);
        }
        documentClient.update(Mapper.asMap(doc));
        try {
            documentClient.commit(OPEN_SEARCH_APP_NAME, WOTagConsts.OPEN_SEARCH_TABLE_NAME);
        } catch (OpenSearchException|OpenSearchClientException e) {
            log.error("updateDoc tag fail: " + caseId, e);
        }
    }

    public void deleteDoc(String caseId) {
        WOOpenSearchDocument doc = new WOOpenSearchDocument();
        doc.setCase_id(caseId);
        documentClient.remove(Mapper.asMap(doc));
        try {
            documentClient.commit(OPEN_SEARCH_APP_NAME, WOTagConsts.OPEN_SEARCH_TABLE_NAME);
        } catch (OpenSearchException|OpenSearchClientException e) {
            log.error("delete fail: " + caseId, e);
        }
    }

    private WOOpenSearchDocument toDoc(WOCase woCase, WOCaseContentBrief brief) {
        WOOpenSearchDocument doc = new WOOpenSearchDocument();
        doc.setCase_id(woCase.getId());
        doc.setBiz(woCase.getBiz());
        doc.setConfig_id(woCase.getConfigId());
        doc.setContent_id(woCase.getContentId());
        doc.setContent_type(woCase.getContentType());
        doc.setUser_id(woCase.getUserId());
        doc.setClient_user_id(woCase.getClientUserId());
        if (woCase.getClientMobile() != null && woCase.getClientMobile().length() > 6) {
            String mobile = woCase.getClientMobile();
            doc.setClient_mobile_md5(WOTagConsts.str2md5(mobile));
            doc.setClient_mobile_tail(mobile.substring(mobile.length() - 4));
        }
        doc.setClient_name(woCase.getClientName());
        doc.setOrigin(woCase.getOrigin());
        doc.setState(woCase.getState());
        doc.setBiz_state(woCase.getBizState());
        doc.setResolver(woCase.getResolver());
        doc.setOrigin_resolver(woCase.getOriginResolver());
        doc.setPending_team(woCase.getPendingTeam());
        doc.setResolver_list(woCase.getResolverList());
        doc.setClose_type(woCase.getCloseType());
        doc.setClose_text(woCase.getCloseText());
        doc.setRating(woCase.getRating());
        doc.setUser_rating(woCase.getUserRating());
        doc.setStat_rating(woCase.getStatRating());
        doc.setUpdate_time(woCase.getUpdateTime());
        doc.setClose_time(woCase.getCloseTime());
        doc.setCreate_time(woCase.getCreateTime());

        if (woCase.getAcl() != null) {
            doc.setAcl(GsonUtils.getGson().toJson(woCase.getAcl()));
        }

        if (woCase.getClientUserId() != null) {
            userService.getBasic(woCase.getClientUserId()).data().ifPresent(user -> {
                if (doc.getClient_name() == null) {
                    doc.setClient_name(user.getUserName());
                }
                doc.setClient_user_avatar(user.getAvatar());
            });
        }

        if (brief != null) {
            if (brief.getContent() != null) {
                doc.setContent_text(brief.getContent());
            }
            if (brief.getTitle() != null) {
                doc.setContent_title(brief.getTitle());
            }
            if (brief.getSubtitle() != null) {
                doc.setContent_subtitle(brief.getSubtitle());
            }
            if (brief.getImages() != null) {
                doc.setContent_image_count(brief.getImages().size());
                doc.setContent_image_json(JsonUtils.getGson().toJson(brief.getImages()));
            }
            if (brief.getAudio() != null) {
                doc.setContent_audio_count(1);
                doc.setContent_audio_json(JsonUtils.getGson().toJson(brief.getAudio()));
            }
            if (brief.getVideo() != null) {
                doc.setContent_video_count(1);
                doc.setContent_video_json(JsonUtils.getGson().toJson(brief.getVideo()));
            }
        }

        return doc;
    }

    // ------------------------------------------------------------

    private static class DocAction {

        /**
         * 字段名: {@link DocumentConstants#DOC_KEY_CMD}
         * 字段值: {@link Command}
         */
        private final String cmd;

        /** {@link DocumentConstants#DOC_KEY_FIELDS} */
        private final WOOpenSearchDocument fields;

        private DocAction(String cmd, WOOpenSearchDocument fields) {
            this.cmd = cmd;
            this.fields = fields;
        }
    }

}
