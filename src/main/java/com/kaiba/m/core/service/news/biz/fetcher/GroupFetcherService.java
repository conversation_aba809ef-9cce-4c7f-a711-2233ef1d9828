package com.kaiba.m.core.service.news.biz.fetcher;

import com.kaiba.lib.base.assembler.Mapper;
import com.kaiba.lib.base.constant.KbModule;
import com.kaiba.lib.base.domain.news.article.ArticleModel;
import com.kaiba.lib.base.domain.news.article.ArticleStatModel;
import com.kaiba.lib.base.domain.da.sensors.pgc.SensorsPGCObjUserActCountModel;
import com.kaiba.lib.base.domain.da.sensors.pgc.SensorsPGCObjUserActModel;
import com.kaiba.lib.base.service.ISensorsService;
import com.kaiba.m.core.domain.news.article.NewsArticle;
import com.kaiba.m.core.domain.news.pool.bygroup.IdsGroup;
import com.kaiba.m.core.domain.news.pool.bygroup.IdsGroupArticle;
import com.kaiba.m.core.service.news.article.NewsArticleService;
import com.kaiba.m.core.service.news.pool.bygroup.NewsGroupService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

/**
 * author: lyux
 * date: 2024-10-21
 */
@Slf4j
@Service
public class GroupFetcherService {

    private static final List<GroupFetcherConfig> DAILY_FETCHER_LIST = Collections.singletonList(
            GroupFetcherConfig.on() // in杭州主页瀑布流
                    .targetGroup("inhz_main_news")
                    .sourceGroups(GroupFetcherConsts.COUNTY_GROUP_LIST)
                    .mode(GroupFetcherMode.OVERALL).count(3)
                    .build()
    );

    private static final List<GroupFetcherConfig> WEEKLY_FETCHER_LIST = Collections.singletonList(
            GroupFetcherConfig.on() // in杭州7日top
                    .targetGroup("inhz_main_page_7top")
                    .sourceGroups(GroupFetcherConsts.COUNTY_GROUP_LIST)
                    .mode(GroupFetcherMode.OVERALL).count(20)
                    .build()
    );

    // -----------------------------------------------------------------

    private final NewsGroupService groupService;
    private final NewsArticleService articleService;
    private final ISensorsService sensorsService;

    public GroupFetcherService(
            NewsGroupService groupService,
            NewsArticleService articleService,
            ISensorsService sensorsService
    ) {
        this.groupService = groupService;
        this.articleService = articleService;
        this.sensorsService = sensorsService;
    }

    // -----------------------------------------------------------------

    public List<ArticleModel> fetchArticleByConfigKey(String configKey, long st, long et) {
        for (GroupFetcherConfig config : DAILY_FETCHER_LIST) {
            if (configKey.equals(config.getKey())) {
                return fetchArticleByConfig(config, st, et);
            }
        }
        for (GroupFetcherConfig config : WEEKLY_FETCHER_LIST) {
            if (configKey.equals(config.getKey())) {
                return fetchArticleByConfig(config, st, et);
            }
        }
        return Collections.emptyList();
    }

    private List<ArticleModel> fetchArticleByConfig(GroupFetcherConfig config, long st, long et) {
        if (config.getTargetGroup() == null) {
            log.error("fetch article from config " + config.getKey() + ", missing target");
            return Collections.emptyList();
        }
        if (config.getSourceGroups() == null || config.getSourceGroups().isEmpty()) {
            log.error("fetch article from config " + config.getKey() + ", missing source");
            return Collections.emptyList();
        }
        IdsGroup targetGroup = groupService.getGroup(null, config.getTargetGroup()).orElse(null);
        if (targetGroup == null) {
            log.error("fetch article from config " + config.getKey()
                    + ", target group not found: " + config.getTargetGroup());
            return Collections.emptyList();
        }
        List<ArticleViewScore> list = new LinkedList<>();
        switch (config.getMode()) {
            case EACH:
                for (String sourceGroup : config.getSourceGroups()) {
                    TreeSet<ArticleViewScore> eachScores = new TreeSet<>(Comparator.comparingLong(o -> o.score));
                    fetchArticleFromGroup(sourceGroup, config.getCount(), st, et, eachScores);
                    list.addAll(eachScores);
                }
                break;
            case OVERALL:
                TreeSet<ArticleViewScore> scores = new TreeSet<>(Comparator.comparingLong(o -> o.score));
                for (String sourceGroup : config.getSourceGroups()) {
                    fetchArticleFromGroup(sourceGroup, config.getCount(), st, et, scores);
                }
                list.addAll(scores);
                break;
        }
        if (list.isEmpty()) {
            log.info("fetch article from config " + config.getKey() + ", no suitable article found");
            return Collections.emptyList();
        }
        Set<String> articleIds = list.stream().map(as -> as.articleId).collect(Collectors.toSet());
        Map<String, NewsArticle> articleMap = articleService.getArticleMapByIdIn(articleIds);
        List<ArticleModel> modelList = new ArrayList<>(articleIds.size());
        for (ArticleViewScore as : list) {
            NewsArticle article = articleMap.get(as.articleId);
            groupService.upsertArticleToGroup(article, targetGroup, null);
            article.setContent(null);
            ArticleModel model = Mapper.map(article, ArticleModel.class);
            model.setStat(new ArticleStatModel());
            model.getStat().setViewRCount(as.viewCount);
            modelList.add(model);
        }
        return modelList;
    }

    private void fetchArticleFromGroup(String groupKey, int max, long st, long et, TreeSet<ArticleViewScore> scores) {
        for (int i = 1; ; i ++) {
            Page<IdsGroupArticle> igaPage = groupService
                    .getGroupArticlePageByGroupAndCreateTimeBetween(groupKey, st, et, i, 200);
            igaPage.forEach(iga -> {
                SensorsPGCObjUserActModel act = new SensorsPGCObjUserActModel();
                act.setBusiness(KbModule.NEWS_NEO.name());
                act.setRef1(iga.getArticleId());
                long vc = sensorsService.kbPGCCountByCacheRT(act).data()
                        .map(SensorsPGCObjUserActCountModel::getViewCount).orElse(0L);
                long score = vc * 10_000_000_000L + (iga.getCreateTime() - st) / 1000;
                ArticleViewScore as = new ArticleViewScore(iga.getArticleId(), vc, -score);
                scores.add(as);
                if (scores.size() > max) {
                    scores.pollLast();
                }
            });
            if (igaPage.isLast()) {
                break;
            }
        }
    }

    @XxlJob("neo-news-in-hangzhou-daily")
    public ReturnT<String> traverseDaily(String param) {
        long st = LocalDate.now().minusDays(1).atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli();
        long et = st + 86400_000L;
        for (GroupFetcherConfig config : DAILY_FETCHER_LIST) {
            try {
                fetchArticleByConfig(config, st, et);
            } catch (Exception e) {
                log.error("fetch article for " + config.getKey() + " fail", e);
            }
        }
        return ReturnT.SUCCESS;
    }

    @XxlJob("neo-news-in-hangzhou-weekly")
    public ReturnT<String> traverseWeekly(String param) {
        long st = LocalDate.now().minusWeeks(1).atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli();
        long et = st + 7 * 86400_000L;
        for (GroupFetcherConfig config : WEEKLY_FETCHER_LIST) {
            try {
                fetchArticleByConfig(config, st, et);
            } catch (Exception e) {
                log.error("fetch article for " + config.getKey() + " fail", e);
            }
        }
        return ReturnT.SUCCESS;
    }

    // -----------------------------------------------------------------

    @EqualsAndHashCode
    private static class ArticleViewScore {
        private final String articleId;
        private final long viewCount;
        private final long score;

        private ArticleViewScore(String articleId, long viewCount, long score) {
            this.articleId = articleId;
            this.viewCount = viewCount;
            this.score = score;
        }
    }

}
