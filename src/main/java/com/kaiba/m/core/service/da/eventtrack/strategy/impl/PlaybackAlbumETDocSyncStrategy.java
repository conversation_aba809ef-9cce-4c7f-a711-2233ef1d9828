package com.kaiba.m.core.service.da.eventtrack.strategy.impl;

import com.kaiba.lib.base.constant.KbModule;
import com.kaiba.lib.base.constant.news.NewsChannel;
import com.kaiba.lib.base.domain.common.KbTimeRange;
import com.kaiba.m.core.constant.da.eventtrack.ETDocSyncStrategyType;
import com.kaiba.m.core.domain.da.eventtrack.ETDoc;
import com.kaiba.m.core.domain.playback.PlaybackAlbum;
import com.kaiba.m.core.repository.da.eventtrack.ETDocRepository;
import com.kaiba.m.core.repository.da.eventtrack.ETDocSyncCursorRepository;
import com.kaiba.m.core.service.da.eventtrack.ETDocModelHelper;
import com.kaiba.m.core.service.da.eventtrack.strategy.model.ETDocSyncQueryModel;
import com.kaiba.m.core.service.playback.PlaybackAlbumService;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.Set;

/**
 * 回放专辑. 数据量较小全量同步
 * <AUTHOR>
 * @version PlaybackAlbumETDocSyncStrategy, v0.1 2025/4/9 14:01 daopei Exp $
 **/
@Service
public class PlaybackAlbumETDocSyncStrategy extends AbstractETDocSyncStrategy{

    private final PlaybackAlbumService playbackAlbumService;

    //我的汽车有话说节目ID
    private final static String carTalkProgram = "5d5fa8f2f4bbdc0001b0b5e8";


    public PlaybackAlbumETDocSyncStrategy(
            ETDocRepository etDocRepository,
            ETDocSyncCursorRepository etDocSyncCursorRepository,
            PlaybackAlbumService playbackAlbumService
    ) {
        super(etDocRepository, etDocSyncCursorRepository);
        this.playbackAlbumService = playbackAlbumService;
    }

    @Override
    public void syncAll() {
        initDocByPageExecutor(this::execute, null);
    }

    @Override
    public void syncByTimeRange(KbTimeRange time) {
        initDocByPageExecutor(this::execute, null);
    }

    @Override
    public String getStrategy() {
        return ETDocSyncStrategyType.PLAYBACK_ALBUM.name();
    }


    private boolean execute(ETDocSyncQueryModel query) {
        Page<PlaybackAlbum> albums = playbackAlbumService.getAlbumList(null, null, query.getPage(), query.getPageSize());
        for (PlaybackAlbum album : albums.getContent()) {
            ETDoc doc = ETDocModelHelper.createByAll(
                    KbModule.PLAYBACK.name(), "ALBUM",
                    album.getId(), null, null,
                    album.getSiteId(), album.getTitle(),
                    getChannelBySite(album.getSiteId()), null, getModuleByAlbumId(album.getId()),
                    album.getCreateTime() * 1000);
            saveETDoc(doc);
        }
        return albums.hasNext();
    }

    private String getChannelBySite(Integer siteId) {
        if (siteId != null && siteId == 9) {
            return NewsChannel.FM_918.name();
        }
        return null;
    }

    /**
     * 汽车维权标签填充
     * @param albumId
     * @return
     */
    private Set<String> getModuleByAlbumId(String albumId) {
        if (albumId == null || albumId.isEmpty()) {
            return null;
        }
        if (albumId.equals(carTalkProgram)) {
            return Collections.singleton("da_hangbangxia");
        }
        return null;
    }

}
