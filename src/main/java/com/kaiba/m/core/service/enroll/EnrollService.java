package com.kaiba.m.core.service.enroll;

import com.kaiba.lib.base.constant.enroll.EnrollMarkType;
import com.kaiba.lib.base.constant.enroll.EnrollModuleType;
import com.kaiba.lib.base.constant.enroll.EnrollState;
import com.kaiba.lib.base.constant.enroll.EnrollType;
import com.kaiba.lib.base.domain.enroll.EnrollModel;
import com.kaiba.lib.base.domain.enroll.EnrollModuleDataAggr;
import com.kaiba.lib.base.domain.enroll.EnrollModuleModel;
import com.kaiba.lib.base.response.KbCode;
import com.kaiba.lib.base.response.KbException;
import com.kaiba.m.core.domain.enroll.Enroll;
import com.kaiba.m.core.domain.enroll.EnrollModule;
import com.kaiba.m.core.domain.enroll.EnrollData;
import com.kaiba.m.core.model.enroll.EnrollEmceeScoreModel;
import com.kaiba.m.core.repository.enroll.EnrollDataRepository;
import com.kaiba.m.core.repository.enroll.EnrollModuleRepository;
import com.kaiba.m.core.repository.enroll.EnrollRepository;
import com.kaiba.m.core.service.enroll.mark.IMarkProvider;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2019/12/30
 */
@Slf4j
@Service
public class EnrollService {

    private final EnrollRepository enrollRepository;
    private final EnrollModuleRepository enrollModuleRepository;
    private final EnrollDataRepository enrollDataRepository;
    private final List<IMarkProvider> markProviderList;
    private final Map<EnrollMarkType, IMarkProvider> markProviders = new HashMap<>();

    @PostConstruct
    private void init() {
        for (IMarkProvider m : markProviderList) {
            markProviders.put(m.getType(), m);
        }
    }

    public EnrollService(EnrollRepository enrollRepository, EnrollModuleRepository enrollModuleRepository,
                         EnrollDataRepository enrollDataRepository, List<IMarkProvider> markProviderList) {
        this.enrollRepository = enrollRepository;
        this.enrollModuleRepository = enrollModuleRepository;
        this.enrollDataRepository = enrollDataRepository;
        this.markProviderList = markProviderList;
    }

    public Enroll createEnroll(EnrollModel model) {
        verifyEnrollOnCreate(model);
        Enroll enroll = new Enroll();
        enroll.setCreatorId(model.getCreatorId());
        enroll.setSiteId(model.getSiteId());
        enroll.setTitle(model.getTitle());
        if (null != model.getIntroduce()) {
            enroll.setIntroduce(model.getIntroduce());
        }
        if (null != model.getCover()) {
            enroll.setCover(model.getCover());
        }
        if (null != model.getBackgroundColor()) {
            enroll.setBackgroundColor(model.getBackgroundColor());
        }
        if (null != model.getBackgroundImageKey()) {
            enroll.setBackgroundImageKey(model.getBackgroundImageKey());
        }
        if (null != model.getTextColor()) {
            enroll.setTextColor(model.getTextColor());
        }
        if(null == model.getType()){
            enroll.setType(EnrollType.ENROLL.getValue());
        }else{
            enroll.setType(model.getType());
        }
        enroll.setMarkType(model.getMarkType());
        enroll.setStartTime(model.getStartTime());
        enroll.setEndTime(model.getEndTime());
        enroll.setState(EnrollState.INIT.getValue());
        enroll.setCreateTime(System.currentTimeMillis() / 1000);
        enroll.setPreamble(model.getPreamble());
        enroll.setAgreement(model.getAgreement());
        Enroll result = enrollRepository.insert(enroll);
        createEnrollModule(model.getEnrollModules().stream()
                .map(enrollModuleModel -> model2enrollModule(enrollModuleModel, result.getId())).collect(Collectors.toList()));
        return result;
    }

    public Enroll updateEnroll(EnrollModel model) {
        verifyEnrollOnCreate(model);
        String enrollId = model.getId();
        List<EnrollModule> enrollModulesList = new ArrayList<>(model.getEnrollModules().size());
        Set<String> oldModuleIds = enrollModuleRepository.findAllByEnrollId(enrollId).stream()
                .map(EnrollModule::getId)
                .collect(Collectors.toSet());
        for (EnrollModuleModel module : model.getEnrollModules()) {
            if (module.getId() != null) {
                oldModuleIds.remove(module.getId());
                //id不为空就更新
                enrollModulesList.add(updateEnrollModule(module));
            } else {
                //id为空就新建
                enrollModulesList.add(createEnrollModule(module, enrollId));
            }
        }
        //删除的module要在数据库删除
        enrollModuleRepository.deleteByIdIn(oldModuleIds);
        long updateTime = System.currentTimeMillis() / 1000;
        enrollRepository.updateEnrollModule(enrollId, enrollModulesList);
        Enroll result = enrollRepository.updateEnroll(updateTime, model.getCreatorId(), model.getSiteId(), enrollId, model.getTitle()
                , model.getIntroduce(), model.getCover(), model.getStartTime(), model.getEndTime()
                , model.getBackgroundColor(), model.getBackgroundImageKey(), model.getTextColor()
                , model.getPreamble(), model.getAgreement());
        log.info("enroll updated: " + result);
        return result;
    }

    public Enroll updateEnrollState(String enrollId, Integer state) {
        if (enrollId == null) {
            throw new KbException(KbCode.ILLEGAL_ARGUMENT, "enrollId should not be null").li();
        }
        return enrollRepository.updateEnrollState(enrollId, state);
    }

    public void deleteEnroll(String id) {
        enrollRepository.deleteById(id);
    }

    private EnrollModule createEnrollModule(EnrollModuleModel module, String enrollId) {
        EnrollModule enrollModule = model2enrollModule(module, enrollId);
        EnrollModule result = enrollModuleRepository.insert(enrollModule);
        log.info("enrollModule created: " + result);
        return result;
    }

    private void createEnrollModule(List<EnrollModule> enrollModuleList) {
        if (enrollModuleList == null || enrollModuleList.size() == 0) {
            throw new KbException(KbCode.ILLEGAL_ARGUMENT, "enrollModuleList is null");
        }
        for (EnrollModule enrollModule : enrollModuleList) {
            if (enrollModule.getId() != null) {
                throw new KbException(KbCode.ILLEGAL_ARGUMENT, "update not allowed here, id should be null: " + enrollModule);
            }
            long current = System.currentTimeMillis() / 1000;
            enrollModule.setCreateTime(current);
        }
        enrollModuleRepository.insert(enrollModuleList);
    }

    private EnrollModule updateEnrollModule(EnrollModuleModel module) {
        EnrollModule result = enrollModuleRepository.updateEnrollModule(module.getId(), module.getType(), module.getMust(), module.getMultiVote(), module.getTitle(),
                module.getAttr());
        log.info("enrollModule updated: " + result);
        return result;
    }

    public List<EnrollData> createEnrollData(EnrollMarkType markType, List<EnrollData> enrollDataList) {
        if (enrollDataList == null || enrollDataList.size() == 0) {
            return Collections.emptyList();
        }
        String mark = generateMark(markType, enrollDataList);
        for (EnrollData enrollData : enrollDataList) {
            if (enrollData.getId() != null) {
                throw new KbException(KbCode.ILLEGAL_ARGUMENT, "update not allowed here, id should be null: " + enrollData);
            }
            verifyEnrollDataOnCreateOrUpdate(enrollData);
            long current = System.currentTimeMillis() / 1000;
            enrollData.setCreateTime(current);
            enrollData.setMark(mark);
        }
        List<EnrollData> result = enrollDataRepository.insert(enrollDataList);
        log.info("enroll data list created: " + result);
        return result;
    }

    public List<EnrollData> updateEnrollData(EnrollMarkType markType, List<EnrollData> enrollDataList) {
        if (enrollDataList == null || enrollDataList.size() == 0) {
            return Collections.emptyList();
        }
        String mark = generateMark(markType, enrollDataList);
        List<EnrollData> enrollDataResult = new ArrayList<>(enrollDataList.size());
        for (EnrollData enrollData : enrollDataList) {
            verifyEnrollDataOnCreateOrUpdate(enrollData);
            if (enrollData.getId() != null) {
                enrollDataResult.add(enrollDataRepository.updateEnrollData(enrollData));
            } else {
                long current = System.currentTimeMillis() / 1000;
                enrollData.setCreateTime(current);
                enrollData.setMark(mark);
                enrollDataResult.add(enrollDataRepository.insert(enrollData));
            }
        }
        log.info("enroll data list updated: " + enrollDataResult);
        return enrollDataResult;
    }

    public boolean existsByEnrollIdAndMark(String enrollId, String mark) {
        return enrollDataRepository.existsByEnrollIdAndMark(enrollId, mark);
    }

    Page<Enroll> getEnrollList(Integer[] states, Integer page, Integer pageSize) {
        if (states == null || states.length == 0) {
            return enrollRepository.findByOrderByCreateTimeDesc(createPageable(page, pageSize));
        } else {
            return enrollRepository.findByStateInOrderByCreateTimeDesc(states, createPageable(page, pageSize));
        }
    }

    public Optional<Enroll> findById(String id) {
        return enrollRepository.findById(id);
    }

    public Page<Enroll> getEnrollList(Integer siteId, Integer state, Integer type,String title, Integer page, Integer pageSize) {
        return enrollRepository.getEnrollList(siteId, state,type, title, createPageable(page, pageSize));
    }

    public Optional<EnrollModule> getEnrollModuleById(String id) {
        return enrollModuleRepository.findById(id);
    }

    public List<EnrollModule> getEnrollModuleListByEnrollId(String enrollId){
        return enrollModuleRepository.findAllByEnrollId(enrollId);
    }

    public List<EnrollModuleDataAggr> getAggrListByUserIdAndEnrollId(String enrollId, Integer userId) {
        return enrollDataRepository.aggrEnrollModuleDataByUserAndEnrollId(enrollId, userId);
    }

    public EnrollDataRepository.EnrollModuleDataAggrWrapper getAggrListByEnrollIdAndUserId(String enrollId, Integer createId,
                                                                                           Integer page, Integer pageSize) {
        int p = page == null ? 0 : page - 1;
        int ps = pageSize == null ? 15 : pageSize;
        return enrollDataRepository.getAggrListByEnrollIdAndUserId(enrollId, createId, p, ps);
    }

    public Page<EnrollData> getEnrollDataList(String enrollId, String moduleId, String mark, Integer userId, Integer page, Integer pageSize) {
        return enrollDataRepository.getEnrollDataList(enrollId, moduleId, mark, userId, createPageable(page, pageSize));
    }

    public List<EnrollData> getByEnrollIdAndMark(String enrollId, String mark) {
        return enrollDataRepository.findByEnrollIdAndMark(enrollId, mark);
    }

    public List<EnrollData> getEnrollDataListByModuleId(String moduleId) {
        return enrollDataRepository.findAllByModuleId(moduleId);
    }

    public Boolean isEnrolled(Integer userId, String id) {
        return enrollDataRepository.findFirstByEnrollIdAndUserId(id, userId).isPresent();
    }

    // for emcee survey export ----------------------------

    public Page<EnrollEmceeScoreModel> getAggrListByEnrollIdAndType(String enrollId, Integer type, Integer p, Integer ps) {
        return enrollDataRepository.aggrEnrollDataByEnrollIdAndType(enrollId, type, p, ps);
    }

    public Page<EnrollData> getByEnrollIdAndType(String enrollId, Integer type, Integer p, Integer ps) {
        return enrollDataRepository.findByEnrollIdAndTypeAndTextNotNull(enrollId, type, createPageable(p, ps));
    }

    // ----------------------------------------------------

    private static Pageable createPageable(Integer page, Integer pageSize) {
        int p = (page == null || page < 1) ? 0 : page - 1;
        int ps = (pageSize == null || pageSize > 100) ? 15 : pageSize;
        return PageRequest.of(p, ps);
    }

    private void verifyEnrollDataOnCreateOrUpdate(EnrollData enrollData) {
        EnrollModule enrollModule = enrollModuleRepository.findById(enrollData.getModuleId()).orElseThrow(
                () -> new KbException(KbCode.ENROLL_NOT_EXIST, "enroll module not exist: " + enrollData.getModuleId()).li());
        EnrollModuleType enrollModuleType = EnrollModuleType.valueOf(enrollData.getType()).orElseThrow(() ->
                new KbException(KbCode.ILLEGAL_ARGUMENT, "wrong enrollModule type: " + enrollData).li());
        switch (enrollModuleType) {
            case SINGLE_TEXT:
            case MOBILE:
            case EMAIL:
            case CAR_NO:
            case ID_CARD:
            case MULTI_TEXT:
                if (enrollModule.getMust() == 1 && null == enrollData.getText()) {
                    throw new KbException(KbCode.ILLEGAL_ARGUMENT, "text should not be null: " + enrollData);
                }
                break;
            case AUDIO:
                if (enrollModule.getMust() == 1 && null == enrollData.getAudio()) {
                    throw new KbException(KbCode.ILLEGAL_ARGUMENT, "audio should not be null: " + enrollData);
                }
                break;
            case VIDEO:
                if (enrollModule.getMust() == 1 && null == enrollData.getVideo()) {
                    throw new KbException(KbCode.ILLEGAL_ARGUMENT, "video should not be null: " + enrollData);
                }
                break;
            case IMAGE:
                if (enrollModule.getMust() == 1 && enrollData.getImages().size() == 0) {
                    throw new KbException(KbCode.ILLEGAL_ARGUMENT, "images should not be null: " + enrollData);
                }
                break;
            case CHECK_BOX:
                if (enrollModule.getMust() == 1 && null == enrollData.getChoice()) {
                    throw new KbException(KbCode.ILLEGAL_ARGUMENT, "choice should not be null: " + enrollData);
                }
                break;
            case RATE:
                if (enrollModule.getMust() == 1 && null == enrollData.getRating()) {
                    throw new KbException(KbCode.ILLEGAL_ARGUMENT, "rating should not be null: " + enrollData);
                }
                break;
        }
    }

    public String generateMark(EnrollMarkType markType, List<EnrollData> enrollDataList) {
        IMarkProvider markProvider = markProviders.get(markType);
        if (markProvider == null) {
            throw new KbException(KbCode.ILLEGAL_ARGUMENT, "mark type not supported yet: " + markType).li();
        }
        return markProvider.getMark(enrollDataList);
    }

    private void verifyEnrollOnCreate(EnrollModel enroll) {
        if (enroll == null) {
            throw new KbException(KbCode.ILLEGAL_ARGUMENT, "enroll should note be null").li();
        }

        if (enroll.getCreatorId() == null) {
            throw new KbException(KbCode.ILLEGAL_ARGUMENT, "user id should note be null: " + enroll).li();
        }

        if (enroll.getSiteId() == null) {
            throw new KbException(KbCode.ILLEGAL_ARGUMENT, "site id should note be null: " + enroll).li();
        }

        if (enroll.getTitle() == null) {
            throw new KbException(KbCode.ILLEGAL_ARGUMENT, "title should note be null: " + enroll).li();
        }

        if (enroll.getStartTime() == null) {
            throw new KbException(KbCode.ILLEGAL_ARGUMENT, "start time should note be null: " + enroll).li();
        }

        if (enroll.getEndTime() == null) {
            throw new KbException(KbCode.ILLEGAL_ARGUMENT, "end time should note be null: " + enroll).li();
        } else {
            if (enroll.getEndTime() < System.currentTimeMillis() / 1000) {
                throw new KbException(KbCode.ILLEGAL_ARGUMENT, "结束时间不能小于当前时间").li();
            }
        }

        if (enroll.getMarkType() == null) {
            throw new KbException(KbCode.ILLEGAL_ARGUMENT, "mark type should note be null: " + enroll).li();
        } else {
            EnrollMarkType markType = EnrollMarkType.valueOf(enroll.getMarkType()).orElseThrow(() -> new KbException(
                    KbCode.REQUEST_PARAM_INVALID, "mark type invalid: " + enroll).li());
            switch (markType) {
                case MOBILE:
                case PROGRAM:
                    throw new KbException(KbCode.ILLEGAL_ARGUMENT, "暂未支持该类型").li();
            }
        }
    }

    private static EnrollModule model2enrollModule(EnrollModuleModel enrollModuleModel, String enrollId) {
        EnrollModule module = new EnrollModule();
        module.setMust(enrollModuleModel.getMust());
        module.setType(enrollModuleModel.getType());
        module.setTitle(enrollModuleModel.getTitle());
        module.setOrder(enrollModuleModel.getOrder());
        if (null != enrollModuleModel.getMultiVote()) {
            module.setMultiVote(enrollModuleModel.getMultiVote());
        }
        if (null != enrollModuleModel.getAttr()) {
            module.setAttr(enrollModuleModel.getAttr());
        }
        module.setEnrollId(enrollId);
        return module;
    }
}
