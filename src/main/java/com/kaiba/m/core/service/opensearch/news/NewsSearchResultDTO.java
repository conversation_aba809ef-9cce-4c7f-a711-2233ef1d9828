package com.kaiba.m.core.service.opensearch.news;

import java.util.List;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * Description: opensearch资讯查询结果DTO
 * Author: ZM227
 * Date: 2025/1/22 11:09
 */
@Data
@ToString
@NoArgsConstructor
public class NewsSearchResultDTO {

    private String status;
    private NewsResultInfo result;

    @Data
    @ToString
    public static class NewsResultInfo {

        private Integer total;
        private Integer num;
        private List<NewsResultItem> items;
    }

    @Data
    @ToString
    public static class NewsResultItem {
        private OpenSearchNewsDTO fields;
    }

}
