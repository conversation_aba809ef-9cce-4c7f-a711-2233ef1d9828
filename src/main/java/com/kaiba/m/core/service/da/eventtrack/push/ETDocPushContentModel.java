package com.kaiba.m.core.service.da.eventtrack.push;

import lombok.Data;

/**
 * 个推OC上传文件内容模型
 * <AUTHOR>
 * @version ETDocPushContentModel, v0.1 2025/4/16 10:56 daopei Exp $
 **/
@Data
public class ETDocPushContentModel {

    /** 文件内容 */
    private String content;

    /** 最后一份数据时间. 单位毫秒 */
    private Long lastDataTime;

    /** 数据量 */
    private Integer totalCount;


    public static ETDocPushContentModel of(
            String content,
            Long lastDataTime,
            Integer totalCount
    ) {
        ETDocPushContentModel model = new ETDocPushContentModel();
        model.setContent(content);
        model.setLastDataTime(lastDataTime);
        model.setTotalCount(totalCount);
        return model;
    }
}
