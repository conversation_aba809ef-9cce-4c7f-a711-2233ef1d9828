package com.kaiba.m.core.service.workorder;

import com.kaiba.lib.base.domain.workorder.WOAccess;
import com.kaiba.lib.base.domain.workorder.WOCaseContentBrief;
import com.kaiba.lib.base.domain.workorder.WOCaseCreateModel;
import com.kaiba.m.core.domain.workorder.WOCase;
import com.kaiba.m.core.domain.workorder.WOEvent;

import java.util.List;

/**
 * author: lyux
 * date: 2024-01-11
 *
 * 业务层操作工具箱
 */
public interface IWOCaseToolkit {

    WOCase createCaseByClient(WOCaseCreateModel model, WOCaseContentBrief content, Integer userId);

    WOCase createCaseByManager(WOCaseCreateModel model, WOCaseContentBrief content, Integer userId);

    WOCase updateCaseAcl(String caseId, List<WOAccess> acl);

    WOEvent updateEventAcl(String eventId, List<WOAccess> acl);

    WOEvent updateEventRemark(String eventId, String remark);

    void cancelDelayedCallback(String caseId, String mark);

    WOCase getCaseById(String caseId);

    WOEvent getEventById(String eventId);

    WOTeamContext getTeamContextByTeam(String biz, String team);

}
