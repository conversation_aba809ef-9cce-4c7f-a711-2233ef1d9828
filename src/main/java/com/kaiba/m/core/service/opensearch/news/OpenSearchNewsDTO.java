package com.kaiba.m.core.service.opensearch.news;

import com.google.gson.annotations.SerializedName;
import java.util.Arrays;
import java.util.List;
import lombok.Data;

/**
 * Description: OpenSearch的search_news节点DTO
 * Author: ZM227
 * Date: 2025/1/13 14:33
 */
@Data
public class OpenSearchNewsDTO {

    /**
     * 文章Id
     */
    @SerializedName("article_id")
    private String articleId;

    /**
     * 文章创建者
     */
    private Integer creator;

    /**
     * 电台Id
     */
    @SerializedName("site_id")
    private Integer siteId;

    /**
     * 内容所属频率频道
     */
    @SerializedName("channel_key")
    private String channelKey;

    /**
     * 频率频道的下属部门标识
     */
    @SerializedName("depart_key")
    private String departKey;

    /**
     * 所属分组
     */
    private List<String> groups;

    /**
     * 所属模块列表
     */
    @SerializedName("module_index")
    private List<String> moduleIndex;

    /**
     * 文章状态
     */
    private String state;

    /**
     * 详情页以何种形式渲染
     */
    private String renderer;

    /**
     * 标题
     */
    private String title;

    /**
     * 副标题
     */
    private String subtitle;

    /**
     * 封面
     */
    private List<String> covers;

    /**
     * 正文内容
     */
    private String content;

    /**
     * 签发者
     */
    private Integer issuer;

    /**
     * 上线时间
     */
    @SerializedName("online_time")
    private Integer onlineTime;

    /**
     * 创建时间
     */
    @SerializedName("create_time")
    private Integer createTime;

    /**
     * 播出时间
     */
    @SerializedName("release_time")
    private Integer releaseTime;

    /**
     * 知识点code
     */
    @SerializedName("knowledge_id")
    private String knowledgeId;

    /**
     * 知识点标题
     */
    @SerializedName("knowledge_title")
    private String knowledgeTitle;

    /**
     * 知识分类code
     */
    @SerializedName("category_id")
    private String categoryId;

    /**
     * 知识标签
     */
    @SerializedName("knowledge_tags")
    private List<String> knowledgeTags;

    /**
     * 知识状态
     */
    @SerializedName("knowledge_status")
    private Integer knowledgeStatus;
}
