package com.kaiba.m.core.service.note.comment;

import com.kaiba.lib.base.constant.note.NoteState;
import com.kaiba.lib.base.response.KbCode;
import com.kaiba.lib.base.response.KbException;
import com.kaiba.m.core.domain.note.NoteAggrByCommentUserId;
import com.kaiba.m.core.domain.note.NoteComment;
import com.kaiba.m.core.domain.note.NoteCommentDelete;
import com.kaiba.m.core.domain.note.NoteCommentReview;
import com.kaiba.m.core.repository.note.NoteCommentRepository;
import com.kaiba.m.core.service.note.NoteCacheConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * author: lyux
 * date: 19-5-28
 */
@Slf4j
@Service
public class NoteCommentService {

    private final NoteCommentCacheService cacheService;
    private final NoteCommentCacheByIdService cacheByIdService;
    private final NoteCommentDBService dbService;
    private final NoteCommentCachePopulator cachePopulator;

    public NoteCommentService(
            NoteCommentCacheService cacheService,
            NoteCommentCacheByIdService cacheByIdService,
            NoteCommentDBService dbService,
            NoteCommentCachePopulator cachePopulator
    ) {
        this.cacheService = cacheService;
        this.cacheByIdService = cacheByIdService;
        this.dbService = dbService;
        this.cachePopulator = cachePopulator;
    }

    public NoteComment commentCreate(NoteComment comment) {
        comment = dbService.commentCreate(comment);
        addCommentCache(comment);
        return comment;
    }

    private void addCommentCache(NoteComment comment) {
        cacheService.addComment(comment);
        cacheByIdService.setComment(comment);
    }

    public List<NoteComment> commentsCreate(List<NoteComment> comments) {
        comments = dbService.commentsCreate(comments);
        addCommentsCache(comments);
        return comments;
    }

    private void addCommentsCache(List<NoteComment> comments) {
        cacheService.addComments(comments);
        cacheByIdService.setCommentList(comments);
    }

    public Optional<NoteComment> getCommentById(String commentId) {
        return dbService.getCommentById(commentId);
    }

    public Optional<NoteComment> getCommentByIdAllowCache(String commentId) {
        Optional<NoteComment> opFromCache = cacheByIdService.getComment(commentId);
        if (opFromCache.isPresent()) {
            return opFromCache;
        } else {
            if (NoteCacheConfig.CACHE_DEBUG) {
                log.info("[cache_debug][db] get comment by id: " + commentId);
            }
            Optional<NoteComment> opFromDB = dbService.getCommentById(commentId);
            opFromDB.ifPresent(cacheByIdService::setComment);
            return opFromDB;
        }
    }

    public List<NoteComment> getCommentListIn(List<String> commentIds) {
        return dbService.getCommentListIn(commentIds);
    }

    public List<NoteComment> getCommentListInAllowCache(List<String> commentIds) {
        if (null == commentIds || commentIds.size() == 0) {
            return Collections.emptyList();
        } else {
            Map<String, NoteComment> map = getCommentMapInAllowCache(commentIds);
            return commentIds.stream().map(map::get).collect(Collectors.toList());
        }
    }

    public Map<String, NoteComment> getCommentMapInAllowCache(List<String> commentIds) {
        if (null == commentIds || commentIds.size() == 0) {
            return Collections.emptyMap();
        } else {
            List<NoteComment> cachedList = cacheByIdService.getCommentIn(commentIds);
            List<String> cacheMissList = null;

            Map<String, NoteComment> map = new HashMap<>(commentIds.size());
            for (int i = commentIds.size() - 1; i >= 0; i--) {
                NoteComment comment = cachedList.get(i);
                String id = commentIds.get(i);
                if (comment == null) {
                    if (cacheMissList == null) {
                        cacheMissList = new LinkedList<>();
                    }
                    cacheMissList.add(id);
                } else {
                    map.put(id, comment);
                }
            }

            if (cacheMissList != null) {
                if (NoteCacheConfig.CACHE_DEBUG) {
                    log.info("[cache_debug][db] get comment by id in: " + commentIds);
                }
                List<NoteComment> databaseList = dbService.getCommentListIn(cacheMissList);
                cacheByIdService.setCommentList(databaseList);
                for (NoteComment data : databaseList) {
                    if (data != null && data.getId() != null) {
                        map.put(data.getId(), data);
                    }
                }
            }

            return map;
        }
    }

    private List<NoteComment> getCommentListByIdsAllowCache(List<String> commentIds) {
        if (commentIds == null || commentIds.size() == 0) {
            return Collections.emptyList();
        }
        Map<String, NoteComment> comments = getCommentMapInAllowCache(commentIds);
        List<NoteComment> result = new ArrayList<>(commentIds.size());
        for (String commentId : commentIds) {
            NoteComment comment = comments.get(commentId);
            if (comment != null) {
                result.add(comment);
            }
        }
        return result;
    }

    public void updateCommentsThreads(String noteId, List<String> threads) {
        dbService.updateThreads(noteId, threads);
    }

    public Page<NoteComment> getCommentListByThread(String[] threads, Integer userId, Integer page, Integer pageSize) {
        return dbService.getCommentListByThread(threads, userId, page, pageSize);
    }

    public Page<NoteComment> getCommentListByNoteId(String noteId, Integer page, Integer pageSize) {
        return dbService.getCommentPageByNoteId(noteId, page, pageSize);
    }

    public List<NoteComment> getCommentListByNoteIdAllowCache(String noteId, Integer page, Integer pageSize) {
        NoteCommentCacheService.CacheResult cacheResult = cacheService.getCommentListByNoteId(noteId, page, pageSize);
        if (cacheResult.isHasCache()) {
            return getCommentListByIdsAllowCache(cacheResult.getCommentList());
        } else {
            cachePopulator.populate(noteId);
            return dbService.getCommentListByNoteId(noteId, page, pageSize);
        }
    }

    public Map<String, List<NoteComment>> getCommentListByNoteIdIn(
            List<String> noteIdList, Integer page, Integer pageSize) {
        List<List<NoteComment>> resultList = noteIdList.stream()
                .map(noteId -> dbService.getCommentListByNoteId(noteId, page, pageSize))
                .collect(Collectors.toList());
        Map<String, List<NoteComment>> map = new HashMap<>(noteIdList.size());
        for (int i = 0; i < noteIdList.size(); i++) {
            map.put(noteIdList.get(i), resultList.get(i));
        }
        return map;
    }

    public Map<String, List<NoteComment>> getCommentListByNoteIdInAllowCache(
            List<String> noteIdList, Integer page, Integer pageSize) {
        Map<String, NoteCommentCacheService.CacheResult> cacheResultMap =
                cacheService.getCommentListByNoteIdIn(noteIdList, page, pageSize);
        List<String> cacheMissingNoteIdList = null;
        Map<String, List<NoteComment>> ret = new HashMap<>(noteIdList.size());
        for (String noteId : noteIdList) {
            NoteCommentCacheService.CacheResult result = cacheResultMap.get(noteId);
            if (result == null || !result.isHasCache()) {
                if (cacheMissingNoteIdList == null) {
                    cacheMissingNoteIdList = new LinkedList<>();
                }
                cacheMissingNoteIdList.add(noteId);
            } else {
                ret.put(noteId, getCommentListByIdsAllowCache(result.getCommentList()));
            }
        }
        if (cacheMissingNoteIdList != null && cacheMissingNoteIdList.size() != 0) {
            cachePopulator.populateList(cacheMissingNoteIdList);
            for (String noteId : cacheMissingNoteIdList) {
                ret.put(noteId, dbService.getCommentListByNoteId(noteId, page, pageSize));
            }
        }
        return ret;
    }

    public Page<NoteComment> getCommentPageByUserId(Integer userId, Integer siteId, Integer page, Integer pageSize) {
        return dbService.getCommentPageByUserId(userId, siteId, page, pageSize);
    }

    public List<NoteComment> getCommentListByUserId(Integer userId, Integer siteId, Integer page, Integer pageSize) {
        return dbService.getCommentListByUserId(userId, siteId, page, pageSize);
    }

    public Page<NoteComment> getCommentList(String lastId, Integer page, Integer pageSize) {
        return dbService.getCommentList(lastId, page, pageSize);
    }

    public List<String> getNoteIdListByCommentUserId(Integer userId, Integer page, Integer pageSize) {
        return dbService.getAggrListByCommentUserId(userId, page, pageSize).stream()
                .map(NoteAggrByCommentUserId::getNoteId)
                .collect(Collectors.toList());
    }

    public NoteCommentRepository.PageCommentAggrModel getNoteCommentUserRankingByThreadId(
            String threadId, Long startTime, Long endTime, Integer page, Integer pageSize) {
        return dbService.aggrCommentByThreadId(threadId, startTime, endTime, page, pageSize);
    }

    public long getCountByNoteId(String noteId, Integer userId) {
        if (userId == null) {
            return dbService.getCountByNoteId(noteId);
        } else {
            return dbService.getCountByNoteIdAndUserId(noteId, userId);
        }
    }

    public long getCountByUserId(Integer userId, Long timeStart, Long timeEnd) {
        return dbService.countByUserId(userId, timeStart, timeEnd);
    }

    // ----------------------------------------------------------------------
    // 审核管理

    public NoteCommentReview commentCreateForReview(NoteComment comment) {
        return dbService.commentCreateForReview(comment);
    }

    public NoteComment commentReviewCreate(String commentId) {
        NoteComment comment = dbService.getCommentById(commentId).orElseThrow(() ->
                new KbException(KbCode.NOTE_COMMENT_NOT_EXISTS, "comment not exists: " + commentId).li());
        NoteCommentReview noteCommentReview = dbService.commentReviewCreate(comment);
        if (comment.getId() != null) {
            cacheByIdService.invalid(comment.getId());
        }

        if (comment.getNoteId() != null) {
            cacheService.invalid(comment.getNoteId());
        }
        return noteCommentReview.toNoteComment();
    }

    public NoteComment commentReviewAccept(String commentReviewId) {
        NoteComment comment = dbService.commentReviewAccept(commentReviewId);
        addCommentCache(comment);
        return comment;
    }

    public NoteCommentReview commentReviewRefused(String commentReviewId, Integer operatorId, String reason) {
        return dbService.commentReviewRefused(commentReviewId, operatorId, reason);
    }

    public Optional<NoteCommentReview> getCommentReviewById(String commentReviewId) {
        return dbService.getCommentReviewById(commentReviewId);
    }

    public Page<NoteCommentReview> getCommentReviewListByUserId(Integer userId, String noteId, Integer page, Integer pageSize) {
        return dbService.getCommentReviewListByUserId(userId, noteId, page, pageSize);
    }

    public Page<NoteCommentReview> getCommentReviewListBySite(Integer siteId, Integer page, Integer pageSize) {
        return dbService.getCommentReviewListBySite(siteId, page, pageSize);
    }

    public Page<NoteCommentReview> getCommentReviewListByNote(String noteId, Integer page, Integer pageSize) {
        return dbService.getCommentReviewListByNote(noteId, page, pageSize);
    }

    public Page<NoteCommentReview> getCommentReviewListByThread(String threadId, Integer page, Integer pageSize) {
        return dbService.getCommentReviewListByThread(threadId, page, pageSize);
    }

    public Page<NoteCommentReview> getCommentReviewListByThreadIn(List<String> threadIdList, Integer page, Integer pageSize) {
        return dbService.getCommentReviewListByThreadIn(threadIdList, page, pageSize);
    }

    public long getCommentReviewCount(Integer siteId, String[] threadIds) {
        return dbService.getCommentReviewCount(siteId, threadIds);
    }

    // ----------------------------------------------------------------------
    // 删除管理

    public void commentDelete(NoteComment comment, Integer operatorId, String remark) {
        dbService.commentDelete(comment, operatorId, remark);
        cacheService.invalid(comment.getNoteId());
    }

    public NoteComment commentRecover(String noteCommentDeleteId) {
        NoteComment comment = dbService.commentRecover(noteCommentDeleteId);
        if (comment != null) {
            addCommentCache(comment);
        }
        return comment;
    }

    public Page<NoteCommentDelete> getCommentDeleteListBySite(Integer siteId, Integer page, Integer pageSize) {
        return dbService.getCommentDeleteListBySite(siteId, page, pageSize);
    }

    public Optional<NoteCommentDelete> getCommentDeleteById(String noteCommentDeleteId) {
        return dbService.getCommentDeleteById(noteCommentDeleteId);
    }

    public Page<NoteCommentDelete> getCommentDeleteListByThread(List<String> threadIds, Integer page, Integer pageSize) {
        return dbService.getCommentDeleteListByThread(threadIds, page, pageSize);
    }

    public void updateCommentState(String commentId, NoteState state) {
        NoteComment comment = dbService.commentUpdateState(commentId, state);
        if (comment != null) {
            cacheByIdService.updateState(comment, state);
        }
    }

}
