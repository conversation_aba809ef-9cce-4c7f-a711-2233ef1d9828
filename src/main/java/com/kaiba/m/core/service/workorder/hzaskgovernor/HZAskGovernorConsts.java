package com.kaiba.m.core.service.workorder.hzaskgovernor;

import com.kaiba.lib.base.domain.workorder.hzaskgovernor.AGContentModel;
import com.kaiba.lib.base.lang.collections.BuilderMap;
import com.kaiba.m.core.domain.workorder.WOTeam;

import java.util.*;
import java.util.function.BiConsumer;

/**
 * author: lyux
 * date: 2024-01-22
 */
public class HZAskGovernorConsts {

    /** 保存在 {@link WOTeam#getAttr()} 中, 用来标识某个管局不参与统计排名 */
    public static final String TEAM_ATTR_IGNORE_RANK = "hz_ag_ignore_rank";

    /** 标签分类: 按用户选择的问题分类 */
    public static final String TAG_TYPE_TENDED = "client_tended";

    /** 标签分类: 按用户选择区域 */
    public static final String TAG_TYPE_REGION = "client_region";

    /** 标签分类: 按主持人选择的话题类型 */
    public static final String TAG_TYPE_TOPIC = "manager_topic";

    /** 标签分类: 客户端列表呈现 */
    public static final String TAG_TYPE_LIST = "to_c_list";

    // -----------------------------------------------------

    static final int CONTENT_MAX_TEXT = 1000;

    static final Set<String> VALID_TENDED_TYPE = Collections.unmodifiableSet(new HashSet<>(Arrays.asList(
            "教育卫生", "公共安全", "社会民生", "交改交通", "消费纠纷",
            "生态环保", "城市建设", "困难帮扶", "住保房管"
    )));

    static final Set<String> VALID_REGIONS = Collections.unmodifiableSet(new HashSet<>(Arrays.asList(
            "上城区", "拱墅区", "西湖区", "滨江区", "萧山区", "余杭区", "临平区",
            "钱塘区", "富阳区", "临安区", "桐庐县", "淳安县", "建德市"
    )));

    static final Set<String> VALID_EDUCATION = Collections.unmodifiableSet(new HashSet<>(Arrays.asList(
            "小学", "初中", "高中", "大专", "本科", "研究生"
    )));

    static final Set<String> VALID_AGE_RANGE = Collections.unmodifiableSet(new HashSet<>(Arrays.asList(
            "18～25", "26～35", "35～45", "46～55", "55以上"
    )));

    static final String DATA_KEY_PERSONAL_NAME = "personal_name";
    static final String DATA_KEY_PERSONAL_MOBILE = "personal_mobile";
    static final String DATA_KEY_PERSONAL_EMAIL = "personal_email";
    static final String DATA_KEY_PERSONAL_REGION = "personal_region";
    static final String DATA_KEY_PERSONAL_EDUCATION = "personal_education";
    static final String DATA_KEY_PERSONAL_AGE = "personal_age";
    static final String DATA_KEY_PERSONAL_LANDLINE_PHONE = "personal_landline_phone";

    static final Map<String, BiConsumer<AGContentModel, String>> PERSONAL_INFO_SETTER_MAP =
            Collections.unmodifiableMap(new BuilderMap<String, BiConsumer<AGContentModel, String>>()
                    .putValue(DATA_KEY_PERSONAL_NAME, AGContentModel::setPersonalName)
                    .putValue(DATA_KEY_PERSONAL_MOBILE, AGContentModel::setPersonalMobile)
                    .putValue(DATA_KEY_PERSONAL_EMAIL, AGContentModel::setPersonalEmail)
                    .putValue(DATA_KEY_PERSONAL_REGION, AGContentModel::setPersonalRegion)
                    .putValue(DATA_KEY_PERSONAL_EDUCATION, AGContentModel::setPersonalEducation)
                    .putValue(DATA_KEY_PERSONAL_AGE, AGContentModel::setPersonalAge)
                    .putValue(DATA_KEY_PERSONAL_LANDLINE_PHONE, AGContentModel::setPersonalLandlinePhone)
            );

}
