package com.kaiba.m.core.service.da.eventtrack.strategy.impl;

import com.kaiba.lib.base.constant.KbModule;
import com.kaiba.lib.base.constant.news.NewsChannel;
import com.kaiba.lib.base.domain.common.KbTimeRange;
import com.kaiba.m.core.constant.da.eventtrack.ETDocSyncStrategyType;
import com.kaiba.m.core.domain.da.eventtrack.ETDoc;
import com.kaiba.m.core.domain.education.Kid;
import com.kaiba.m.core.domain.education.recitation.Expert;
import com.kaiba.m.core.domain.education.recitation.Member;
import com.kaiba.m.core.repository.da.eventtrack.ETDocRepository;
import com.kaiba.m.core.repository.da.eventtrack.ETDocSyncCursorRepository;
import com.kaiba.m.core.repository.education.KidRepository;
import com.kaiba.m.core.repository.education.recitation.ExpertRepository;
import com.kaiba.m.core.repository.education.recitation.MemberRepository;
import com.kaiba.m.core.service.da.eventtrack.ETDocModelHelper;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 教育模块. 包含[918星少年小主播, 89朗诵团成员, 89朗诵团专家]
 * 仅需要全量同步
 * <AUTHOR>
 * @version EducationETDocSyncStrategy, v0.1 2025/4/9 18:10 daopei Exp $
 **/
@Service
public class EducationETDocSyncStrategy extends AbstractETDocSyncStrategy{

    private final KidRepository kidRepository;

    private final MemberRepository memberRepository;

    private final ExpertRepository expertRepository;


    public EducationETDocSyncStrategy(
            ETDocRepository etDocRepository,
            ETDocSyncCursorRepository etDocSyncCursorRepository,
            KidRepository kidRepository,
            MemberRepository memberRepository,
            ExpertRepository expertRepository
    )
    {
        super(etDocRepository, etDocSyncCursorRepository);
        this.kidRepository = kidRepository;
        this.memberRepository = memberRepository;
        this.expertRepository = expertRepository;
    }


    @Override
    public void syncAll() {
       executeAll();
    }

    @Override
    public void syncByTimeRange(KbTimeRange time) {
        executeAll();
    }

    @Override
    public String getStrategy() {
        return ETDocSyncStrategyType.EDUCATION.name();
    }

    public void executeAll() {
        List<Kid> kidList = kidRepository.findAll();
        List<Member> memberList = memberRepository.findAll();
        List<Expert> expertList = expertRepository.findAll();

        List<ETDoc> docs = new ArrayList<>();
        kidList.stream().map(EducationETDocSyncStrategy::mapperKid).forEach(docs::add);
        memberList.stream().map(EducationETDocSyncStrategy::mapperMember).forEach(docs::add);
        expertList.stream().map(EducationETDocSyncStrategy::mapperExpert).forEach(docs::add);

        for (ETDoc doc : docs) {
            saveETDoc(doc);
        }
    }

    private static ETDoc mapperKid(Kid kid) {
        return ETDocModelHelper.createByAll(
                KbModule.EDUCATION.name(), "hz_education_918_i_am_host_kid",
                kid.getId(), null, null,
                null, "918星少年小主播-" + kid.getRealName(),
                NewsChannel.FM_918.name(), null, null,
                kid.getCreateTime()
        );
    }

    private static ETDoc mapperMember(Member member) {
        return ETDocModelHelper.createByAll(
                KbModule.EDUCATION.name(), "hz_education_89_recitation_member",
                member.getId(), null, null,
                null, "89诵读团成员-" + member.getName(),
                NewsChannel.FM_89.name(), null, null,
                member.getCreateTime()
        );
    }

    private static ETDoc mapperExpert(Expert expert) {
        return ETDocModelHelper.createByAll(
                KbModule.EDUCATION.name(), "hz_education_89_recitation_expert",
                expert.getId(), null, null,
                null, "89诵读团专家-" + expert.getName(),
                NewsChannel.FM_89.name(), null, null,
                expert.getCreateTime()
        );
    }


}
