package com.kaiba.m.core.service.da.eventtrack.util;

import lombok.extern.slf4j.Slf4j;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.time.LocalDate;

/**
 * WebDAV工具类使用示例
 */
@Slf4j
public class WebDavExample {

    // WebDAV服务器URL
    private static final String WEBDAV_URL = "http://your-webdav-server.com/webdav/";
    // WebDAV用户名
    private static final String WEBDAV_USERNAME = "username";
    // WebDAV密码
    private static final String WEBDAV_PASSWORD = "password";

    /**
     * 示例1：上传本地文件到WebDAV服务器
     */
    public static void uploadLocalFile() {
        WebDavUtils webDavUtils = null;
        try {
            // 创建WebDAV工具类实例
            webDavUtils = new WebDavUtils(WEBDAV_URL, WEBDAV_USERNAME, WEBDAV_PASSWORD);
            
            // 本地文件路径
            String localFilePath = "/path/to/local/file.txt";
            
            // 创建按日期和小时的目录结构
            String remoteDir = webDavUtils.createDateHourDirectory();
            
            // 远程文件路径
            String remoteFilePath = remoteDir + "file.txt";
            
            // 上传文件
            webDavUtils.uploadFile(localFilePath, remoteFilePath);
            
            log.info("文件上传成功：{}", remoteFilePath);
        } catch (IOException e) {
            log.error("文件上传失败", e);
        } finally {
            // 关闭WebDAV客户端连接
            if (webDavUtils != null) {
                webDavUtils.close();
            }
        }
    }

    /**
     * 示例2：直接上传内容到WebDAV服务器
     */
    public static void uploadContent() {
        WebDavUtils webDavUtils = null;
        try {
            // 创建WebDAV工具类实例
            webDavUtils = new WebDavUtils(WEBDAV_URL, WEBDAV_USERNAME, WEBDAV_PASSWORD);
            
            // 要上传的内容
            String content = "这是要上传的内容\n第二行内容\n第三行内容";
            
            // 创建指定日期和小时的目录结构
            String remoteDir = webDavUtils.createDateHourDirectory(LocalDate.now(), 15); // 15点的目录
            
            // 远程文件路径
            String remoteFilePath = remoteDir + "content.txt";
            
            // 上传内容
            webDavUtils.uploadData(content.getBytes(), remoteFilePath);
            
            log.info("内容上传成功：{}", remoteFilePath);
        } catch (IOException e) {
            log.error("内容上传失败", e);
        } finally {
            // 关闭WebDAV客户端连接
            if (webDavUtils != null) {
                webDavUtils.close();
            }
        }
    }

    /**
     * 示例3：向远程文件追加内容
     */
    public static void appendToRemoteFile() {
        WebDavUtils webDavUtils = null;
        try {
            // 创建WebDAV工具类实例
            webDavUtils = new WebDavUtils(WEBDAV_URL, WEBDAV_USERNAME, WEBDAV_PASSWORD);
            
            // 创建按日期和小时的目录结构
            String remoteDir = webDavUtils.createDateHourDirectory();
            
            // 远程文件路径
            String remoteFilePath = remoteDir + "log.txt";
            
            // 追加多行内容
            for (int i = 0; i < 5; i++) {
                webDavUtils.appendLine(remoteFilePath, "这是第" + (i + 1) + "行追加的内容 - " + System.currentTimeMillis());
                Thread.sleep(1000); // 模拟间隔
            }
            
            log.info("内容追加成功：{}", remoteFilePath);
        } catch (IOException | InterruptedException e) {
            log.error("内容追加失败", e);
        } finally {
            // 关闭WebDAV客户端连接
            if (webDavUtils != null) {
                webDavUtils.close();
            }
        }
    }

    /**
     * 示例4：本地写入文件后上传
     */
    public static void writeLocalThenUpload() {
        WebDavUtils webDavUtils = null;
        try {
            // 创建临时文件
            File tempFile = File.createTempFile("webdav-", ".txt");
            String localFilePath = tempFile.getAbsolutePath();
            
            // 写入内容到本地文件
            StringBuilder content = new StringBuilder();
            for (int i = 0; i < 10; i++) {
                content.append("本地写入的第").append(i + 1).append("行内容\n");
            }
            Files.write(Paths.get(localFilePath), content.toString().getBytes());
            
            // 创建WebDAV工具类实例
            webDavUtils = new WebDavUtils(WEBDAV_URL, WEBDAV_USERNAME, WEBDAV_PASSWORD);
            
            // 创建按日期和小时的目录结构
            String remoteDir = webDavUtils.createDateHourDirectory();
            
            // 远程文件路径
            String remoteFilePath = remoteDir + "local_written.txt";
            
            // 上传文件
            webDavUtils.uploadFile(localFilePath, remoteFilePath);
            
            // 删除临时文件
            tempFile.delete();
            
            log.info("本地写入后上传成功：{}", remoteFilePath);
        } catch (IOException e) {
            log.error("本地写入后上传失败", e);
        } finally {
            // 关闭WebDAV客户端连接
            if (webDavUtils != null) {
                webDavUtils.close();
            }
        }
    }
}