package com.kaiba.m.core.service.circle;

import com.github.benmanes.caffeine.cache.CacheLoader;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.LoadingCache;
import com.kaiba.lib.base.assembler.Mapper;
import com.kaiba.lib.base.constant.circle.CircleAttrKeys;
import com.kaiba.lib.base.constant.circle.CircleCategoryType;
import com.kaiba.lib.base.constant.circle.CircleGetuiType;
import com.kaiba.lib.base.constant.circle.CircleState;
import com.kaiba.lib.base.constant.circle.CircleType;
import com.kaiba.lib.base.constant.note.NoteCommentOrder;
import com.kaiba.lib.base.constant.note.NoteOrder;
import com.kaiba.lib.base.domain.circle.CircleThreadModel;
import com.kaiba.lib.base.domain.circle.CircleTopicModel;
import com.kaiba.lib.base.domain.note.NoteGrainFlag;
import com.kaiba.lib.base.domain.note.NoteModel;
import com.kaiba.lib.base.domain.note.NoteThreadModel;
import com.kaiba.lib.base.lang.collections.KbColUtils;
import com.kaiba.lib.base.service.INoteService;
import com.kaiba.m.core.domain.circle.CircleCategory;
import com.kaiba.m.core.domain.circle.CircleThread;
import lombok.extern.slf4j.Slf4j;
import org.checkerframework.checker.nullness.qual.NonNull;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.util.*;
import java.util.stream.Collectors;

import static com.kaiba.m.core.service.circle.CircleModelHelper.circle2model;

/**
 * author: lyux
 * date: 23-03-15
 */
@Slf4j
@Service
public class CircleApiService {

    private static final String FAKE_CIRCLE_ID = "__FAKE_CIRCLE_ID__";

    private final CircleThreadService circleThreadService;
    private final INoteService noteService;
    private final CircleModelHelper circleModelHelper;

    public CircleApiService(
            CircleThreadService circleThreadService,
            INoteService noteService,
            CircleModelHelper circleModelHelper
    ) {
        this.circleThreadService = circleThreadService;
        this.noteService = noteService;
        this.circleModelHelper = circleModelHelper;
    }

    public List<CircleTopicModel> getTabCircleListBySite(Integer siteId) {
        List<CircleTopicModel> headList = getTabHeadListBySite(siteId);
        List<CircleTopicModel> tailList = getTabTailListBySite(siteId);
        List<CircleTopicModel> tabList = new ArrayList<>(headList.size() + tailList.size());
        tabList.addAll(headList);
        tabList.addAll(tailList);
        return tabList;
    }

    public List<CircleTopicModel> getTabHeadListBySite(Integer siteId) {
        List<String> list = tabHeadBySiteCache.get(siteId);
        if (list == null) {
            return Collections.emptyList();
        } else {
            Map<String, CircleTopicModel> map = topicByIdCache.getAll(list);
            return list.stream()
                    .map(map::get)
                    .filter(CircleApiService::filterTopic)
                    .map(CircleApiService::circleForFrontend)
                    .collect(Collectors.toList());
        }
    }

    public List<CircleTopicModel> getTabTailListBySite(Integer siteId) {
        List<String> list = tabTailBySiteCache.get(siteId);
        return getTopicListByIds(list);
    }

    public List<CircleTopicModel> getRecommendTopicCircleListBySite(Integer siteId) {
        List<String> list = recommendTopicBySiteCache.get(siteId);
        return getTopicListByIds(list);
    }

    /** page 从 0 开始 */
    public List<CircleTopicModel> getPersonalTopicCircleListBySite(Integer siteId, int page, int pageSize) {
        List<String> list = personalTopicBySiteCache.get(siteId);
        List<String> subList = KbColUtils.ListOpt.getSubListByPage(list, page, pageSize);
        return getTopicListByIds(subList);
    }

    /** page 从 0 开始 */
    public List<CircleTopicModel> getTopicCircleListBySite(Integer siteId, int page, int pageSize) {
        List<String> list = allTopicBySiteCache.get(siteId);
        List<String> subList = KbColUtils.ListOpt.getSubListByPage(list, page, pageSize);
        return getTopicListByIds(subList);
    }

    public List<CircleTopicModel> getTabUserSubListBySite(Integer siteId, Integer userId) {
        Page<CircleCategory> userSubPage = circleThreadService.getCategoryPageByUser(
                CircleCategoryType.TAB_USER_SUB, siteId, userId, 1, CircleCategoryType.TAB_USER_SUB.getMax());
        return category2topicList(userSubPage.getContent());
    }

    public List<CircleThreadModel> getTabLegacyListBySite(Integer siteId) {
        List<CircleThreadModel> list = tabLegacyBySiteCache.get(siteId);
        return list == null ? Collections.emptyList() : list;
    }

    public Optional<CircleTopicModel> getCircleById(String circleId) {
        CircleTopicModel circle = topicByIdCache.get(circleId);
        if (circle == null || circle.getCircleId() == null) {
            return Optional.empty();
        } else {
            return Optional.of(circle);
        }
    }

    public Optional<CircleTopicModel> getCircleByThreadId(String threadId) {
        String circleId = circleByThreadIdCache.get(threadId);
        if (circleId == null || FAKE_CIRCLE_ID.equals(circleId)) {
            return Optional.empty();
        } else {
            return getCircleById(circleId);
        }
    }

    public List<NoteModel> getRecommendNoteList(Integer siteId) {
        List<NoteModel> list = recommendNoteCache.get(siteId);
        return list == null ? Collections.emptyList() : list;
    }

    public List<NoteThreadModel> getPostSelectionByThreadId(String threadId) {
        List<NoteThreadModel> threadList = postSelectionByThreadCache.get(threadId);
        return threadList == null ? Collections.emptyList() : threadList;
    }

    public List<CircleTopicModel> category2topicList(List<CircleCategory> categoryList) {
        if (categoryList == null || categoryList.isEmpty()) {
            return Collections.emptyList();
        }
        List<String> circleIds = categoryList.stream()
                .map(CircleCategory::getCircleId)
                .collect(Collectors.toList());
        return getTopicListByIds(circleIds);
    }

    public List<CircleTopicModel> getTopicListByIds(List<String> idList) {
        if (idList == null || idList.isEmpty()) {
            return Collections.emptyList();
        } else {
            Map<String, CircleTopicModel> map = topicByIdCache.getAll(idList);
            return idList.stream()
                    .map(map::get)
                    .filter(CircleApiService::filterTopic)
                    .map(CircleApiService::circleForFrontend)
                    .collect(Collectors.toList());
        }
    }

    private static boolean filterTopic(CircleTopicModel topic) {
        return topic != null && topic.getCircleId() != null && topic.getThreadId() != null
                && (topic.getState() == null || topic.getState() == CircleState.SHOW.getValue());
    }

    private static CircleTopicModel circleForFrontend(CircleTopicModel circle) {
        if (circle == null || !circle.isShow()) {
            return null;
        }
        CircleTopicModel model = Mapper.map(circle, CircleTopicModel.class);
        model.setInterestedAvatars(null);
        model.setContent(null);
        model.setShare(null);
        model.setState(null);
        return model;
    }

    private List<String> getCircleIdListByCategory(Integer siteId, CircleCategoryType categoryType) {
        int pageSize = categoryType.getMax() > 0 ? categoryType.getMax() : 20;
        return getCircleIdListByCategory(siteId, categoryType, pageSize);
    }

    private List<String> getCircleIdListByCategory(Integer siteId, CircleCategoryType categoryType, int pageSize) {
        return circleThreadService
                .getCategoryListBySite(siteId, categoryType, 1, pageSize).stream()
                .map(CircleCategory::getCircleId)
                .collect(Collectors.toList());
    }

    // ----------------------------------------------------------------

    private final LoadingCache<Integer, List<String>> recommendTopicBySiteCache = Caffeine.newBuilder()
            .expireAfterWrite(Duration.ofMinutes(30))
            .refreshAfterWrite(Duration.ofSeconds(181))
            .build(new CacheLoader<Integer, List<String>>() {
                @Override
                public List<String> load(@NonNull Integer siteId) {
                    return getCircleIdListByCategory(siteId, CircleCategoryType.TOPIC_RECOMMEND);
                }

                @Override
                public List<String> reload(@NonNull Integer siteId, @NonNull List<String> oldValue) {
                    List<String> list = load(siteId);
                    return list.size() == 0 ? oldValue : list;
                }
            });

    private final LoadingCache<Integer, List<String>> personalTopicBySiteCache = Caffeine.newBuilder()
            .expireAfterWrite(Duration.ofMinutes(30))
            .refreshAfterWrite(Duration.ofSeconds(47))
            .build(new CacheLoader<Integer, List<String>>() {
                @Override
                public List<String> load(@NonNull Integer siteId) {
                    List<String> list = getCircleIdListByCategory(
                            siteId, CircleCategoryType.TOPIC_PERSONAL, 200);
                    Collections.shuffle(list);
                    return list;
                }

                @Override
                public List<String> reload(@NonNull Integer siteId, @NonNull List<String> oldValue) {
                    List<String> list = load(siteId);
                    return list.size() == 0 ? oldValue : list;
                }
            });

    private final LoadingCache<Integer, List<String>> tabHeadBySiteCache = Caffeine.newBuilder()
            .expireAfterWrite(Duration.ofMinutes(30))
            .refreshAfterWrite(Duration.ofSeconds(181))
            .build(new CacheLoader<Integer, List<String>>() {
                @Override
                public List<String> load(@NonNull Integer siteId) {
                    List<String> list = getCircleIdListByCategory(siteId, CircleCategoryType.TAB_HEAD);
                    if (list.size() == 0) {
                        CircleThread mainCircle = circleThreadService.getOrInitMainCircle(siteId);
                        circleThreadService.upsertSiteCircleCategory(
                                siteId, mainCircle.getId(), CircleCategoryType.TAB_LEGACY);
                        return Collections.singletonList(mainCircle.getId());
                    } else {
                        return list;
                    }
                }

                @Override
                public List<String> reload(@NonNull Integer siteId, @NonNull List<String> oldValue) {
                    List<String> list = load(siteId);
                    return list.size() == 0 ? oldValue : list;
                }
            });

    private final LoadingCache<Integer, List<String>> tabTailBySiteCache = Caffeine.newBuilder()
            .expireAfterWrite(Duration.ofMinutes(30))
            .refreshAfterWrite(Duration.ofSeconds(181))
            .build(new CacheLoader<Integer, List<String>>() {
                @Override
                public List<String> load(@NonNull Integer siteId) {
                    return getCircleIdListByCategory(siteId, CircleCategoryType.TAB_TAIL);
                }

                @Override
                public List<String> reload(@NonNull Integer siteId, @NonNull List<String> oldValue) {
                    List<String> list = load(siteId);
                    return list.size() == 0 ? oldValue : list;
                }
            });

    private final LoadingCache<Integer, List<String>> allTopicBySiteCache = Caffeine.newBuilder()
            .expireAfterWrite(Duration.ofMinutes(30))
            .refreshAfterWrite(Duration.ofSeconds(181))
            .build(new CacheLoader<Integer, List<String>>() {
                @Override
                public List<String> load(@NonNull Integer siteId) {
                    List<String> circleIds = new LinkedList<>();
                    int pageSize = 200;
                    for (int i = 1; ; i ++) {
                        List<CircleThread> circles = circleThreadService
                                .getCircleListBySite(siteId, CircleType.TOPIC.getValue(), i, pageSize);
                        List<String> list = circles.stream()
                                .filter(circle -> circle.getState() == null || circle.getState() == CircleState.SHOW.getValue())
                                .map(CircleThread::getId)
                                .collect(Collectors.toList());
                        circleIds.addAll(list);
                        if (circles.size() < pageSize) {
                            break;
                        }
                    }
                    return circleIds;
                }

                @Override
                public List<String> reload(@NonNull Integer siteId, @NonNull List<String> oldValue) {
                    List<String> list = load(siteId);
                    return list.size() == 0 ? oldValue : list;
                }
            });

    private final LoadingCache<Integer, List<CircleThreadModel>> tabLegacyBySiteCache = Caffeine.newBuilder()
            .expireAfterWrite(Duration.ofMinutes(30))
            .refreshAfterWrite(Duration.ofSeconds(181))
            .build(new CacheLoader<Integer, List<CircleThreadModel>>() {
                @Override
                public List<CircleThreadModel> load(@NonNull Integer siteId) {
                    int pageSize = CircleCategoryType.TAB_LEGACY.getMax();
                    if (pageSize <= 0) {
                        pageSize = 20;
                    }
                    Page<CircleCategory> categoryPage = circleThreadService.getCategoryPageBySite(
                            siteId, CircleCategoryType.TAB_LEGACY, 1, pageSize);
                    List<CircleThreadModel> list = category2circleList(categoryPage);
                    if (list.size() != 0) {
                        return list;
                    }
                    List<CircleTopicModel> topicList = getTabCircleListBySite(siteId);
                    if (topicList == null || topicList.size() == 0) {
                        CircleThread mainCircle = circleThreadService.getOrInitMainCircle(siteId);
                        circleThreadService.upsertSiteCircleCategory(
                                siteId, mainCircle.getId(), CircleCategoryType.TAB_LEGACY);
                        CircleThreadModel model = CircleModelHelper.circle2model(
                                mainCircle, noteService.getThreadById(mainCircle.getThreadId()).dataOrThrow());
                        return Collections.singletonList(model);
                    } else {
                        Set<String> threadIds = new HashSet<>(topicList.size());
                        for (CircleTopicModel topic : topicList) {
                            threadIds.add(topic.getThreadId());
                        }
                        Map<String, NoteThreadModel> threadMap = noteService.getThreadMapByIdSet(threadIds).dataOrThrow();
                        return topicList.stream()
                                .filter(CircleTopicModel::isShow)
                                .map(topic -> topic2circle(topic, threadMap.get(topic.getThreadId())))
                                .collect(Collectors.toList());
                    }
                }

                @Override
                public List<CircleThreadModel> reload(@NonNull Integer siteId, @NonNull List<CircleThreadModel> oldValue) {
                    List<CircleThreadModel> list = load(siteId);
                    return list.size() == 0 ? oldValue : list;
                }
            });

    private final LoadingCache<String, CircleTopicModel> topicByIdCache = Caffeine.newBuilder()
            .expireAfterWrite(Duration.ofMinutes(10))
            .refreshAfterWrite(Duration.ofSeconds(61))
            .build(new CacheLoader<String, CircleTopicModel>() {
                @Override
                public CircleTopicModel load(@NonNull String circleId) {
                    return circleThreadService.getCircleById(circleId)
                            .map(circle -> {
                                NoteThreadModel thead = noteService.getThreadById(circle.getThreadId()).getData();
                                CircleGetuiType getuiType = circleModelHelper.getCircleGetuiType(circleId, circle.getType());
                                return circle2topic(circle, thead, getuiType);
                            })
                            .map(topic -> {
                                if (topic.getNoteId() != null) {
                                    NoteModel note = noteService
                                            .getNoteById(topic.getNoteId(), null, null, GRAIN_TOPIC_NOTE_DETAIL.getFlag())
                                            .getData();
                                    topic.setContent(note);
                                }
                                return topic;
                            })
                            .orElseGet(CircleTopicModel::new);
                }

                @Override
                public @NonNull Map<String, CircleTopicModel> loadAll(
                        @NonNull Iterable<? extends @NonNull String> circleIds) {
                    Set<String> circleIdSet = new HashSet<>();
                    circleIds.forEach(circleIdSet::add);
                    List<CircleThread> circles = circleThreadService.getCircleListIn(circleIdSet);
                    Set<String> noteIds = new HashSet<>(circles.size());
                    Set<String> threadIds = new HashSet<>(circles.size());
                    for (CircleThread circle : circles) {
                        if (circle.getContentNoteId() != null) {
                            noteIds.add(circle.getContentNoteId());
                        }
                        threadIds.add(circle.getThreadId());
                    }
                    Map<String, NoteThreadModel> threadMap = noteService.getThreadMapByIdSet(threadIds).dataOrThrow();
                    Map<String, NoteModel> noteMap = noteService
                            .getNoteListIn(noteIds.toArray(new String[0]), null, null, GRAIN_TOPIC_NOTE_DETAIL.getFlag())
                            .data().orElse(Collections.emptyList()).stream()
                            .collect(Collectors.toMap(NoteModel::getId, n -> n, (n1, n2) -> n1));
                    Map<String, CircleTopicModel> map = new HashMap<>(circles.size());
                    for (CircleThread circle : circles) {
                        CircleGetuiType getuiType = circleModelHelper.getCircleGetuiType(circle.getId(), circle.getType());
                        CircleTopicModel topic = circle2topic(circle, threadMap.get(circle.getThreadId()), getuiType);
                        topic.setContent(noteMap.get(circle.getContentNoteId()));
                        map.put(circle.getId(), topic);
                    }
                    return map;
                }

                @Override
                public CircleTopicModel reload(@NonNull String circleId, @NonNull CircleTopicModel oldValue) {
                    CircleTopicModel topic = load(circleId);
                    if (topic == null) {
                        return oldValue;
                    } else {
                        long c1 = topic.getInterestedCount() == null ? 0 : topic.getInterestedCount();
                        long c2 = oldValue.getInterestedCount() == null ? 0 : oldValue.getInterestedCount();
                        if (c1 < c2) {
                            topic.setInterestedCount(oldValue.getInterestedCount());
                        }
                        return topic;
                    }
                }
            });

    private final LoadingCache<String, String> circleByThreadIdCache = Caffeine.newBuilder()
            .expireAfterWrite(Duration.ofMinutes(3))
            .build(new CacheLoader<String, String>() {
                @Override
                public String load(@NonNull String threadId) {
                    return circleThreadService.getCircleByThread(threadId)
                            .map(CircleThread::getId).orElse(FAKE_CIRCLE_ID);
                }
            });

    private final LoadingCache<Integer, List<NoteModel>> recommendNoteCache = Caffeine.newBuilder()
            .expireAfterWrite(Duration.ofMinutes(10))
            .refreshAfterWrite(Duration.ofSeconds(119))
            .build(new CacheLoader<Integer, List<NoteModel>>() {
                @Override
                public List<NoteModel> load(@NonNull Integer siteId) {
                    CircleThread circle = circleThreadService.getOrInitRecommendCircle(siteId);
                    return noteService
                            .getNoteListByThread(
                                    circle.getThreadId(), null, GRAIN_RECOMMEND_NOTE_LIST.getFlag(), 1, 5, null)
                            .dataOrThrow();
                }

                @Override
                public List<NoteModel> reload(@NonNull Integer siteId, @NonNull List<NoteModel> oldValue) {
                    List<NoteModel> list = load(siteId);
                    return list.size() == 0 ? oldValue : list;
                }
            });

    // 用户发帖时, 根据 threadId 向用户推荐可选择的关联板块
    private final LoadingCache<String, List<NoteThreadModel>> postSelectionByThreadCache = Caffeine.newBuilder()
            .expireAfterWrite(Duration.ofMinutes(30))
            .refreshAfterWrite(Duration.ofSeconds(181))
            .build(new CacheLoader<String, List<NoteThreadModel>>() {
                @Override
                public List<NoteThreadModel> load(@NonNull String threadId) {
                    Optional<CircleThread> opCircleThread = circleThreadService.getCircleByThread(threadId);
                    if (!opCircleThread.isPresent()) {
                        log.info("cannot find circle by threadId: " + threadId);
                        return Collections.emptyList();
                    }
                    CircleThread circleThread = opCircleThread.get();
                    if (circleThread.getType() == CircleType.PLAZA.getValue()) {
                        return getRecommendTopicCircleListBySite(circleThread.getSiteId()).stream()
                                .map(circle -> {
                                    NoteThreadModel thread = new NoteThreadModel();
                                    thread.setId(circle.getThreadId());
                                    thread.setTitle(circle.getTitle());
                                    return thread;
                                })
                                .collect(Collectors.toList());
                    }

                    List<NoteThreadModel> threadList = noteService
                            .getThreadListByRoute(threadId, 1, 10).getData();
                    if (threadList == null || threadList.size() == 0) {
                        return Collections.emptyList();
                    } else {
                        return threadList.stream()
                                .filter(thread -> thread.getAttr() != null && thread.getAttr().get(CircleAttrKeys.CIRCLE_TOPIC) != null)
                                .map(thread -> {
                                    String circleId = circleByThreadIdCache.get(thread.getId());
                                    if (circleId == null) {
                                        return null;
                                    }
                                    CircleTopicModel circle = topicByIdCache.get(circleId);
                                    if (circle == null || !circle.isShow()) {
                                        return null;
                                    }
                                    NoteThreadModel model = new NoteThreadModel();
                                    model.setId(thread.getId());
                                    model.setTitle(thread.getTitle());
                                    return model;
                                })
                                .filter(Objects::nonNull)
                                .collect(Collectors.toList());
                    }
                }

                @Override
                public List<NoteThreadModel> reload(@NonNull String threadId, @NonNull List<NoteThreadModel> oldValue) {
                    List<NoteThreadModel> list = load(threadId);
                    return list.size() == 0 ? oldValue : list;
                }
            });

    // ----------------------------------------------------------------

    private List<CircleThreadModel> category2circleList(Page<CircleCategory> categoryPage) {
        List<String> circleIds = categoryPage.getContent().stream()
                .map(CircleCategory::getCircleId)
                .collect(Collectors.toList());
        Set<String> threadIds = new HashSet<>(circleIds.size());
        List<CircleThread> circleList = circleThreadService.getCircleListIn(circleIds);
        Map<String, CircleThread> validCircles = new HashMap<>(circleList.size());
        for (CircleThread circle : circleList) {
            if (circle.getState() != null && circle.getState() == CircleState.HIDE.getValue()) {
                continue;
            }
            threadIds.add(circle.getThreadId());
            validCircles.put(circle.getId(), circle);
        }
        Map<String, NoteThreadModel> threadMap = noteService.getThreadMapByIdSet(threadIds).dataOrThrow();
        return circleIds.stream().map(circleId -> {
            CircleThread circle = validCircles.get(circleId);
            if (circle == null) {
                return null;
            } else {
                return circle2model(circle, threadMap.get(circle.getThreadId()));
            }
        }).filter(Objects::nonNull).collect(Collectors.toList());
    }

    public CircleTopicModel circle2topic(CircleThread circle, NoteThreadModel thread, CircleGetuiType getuiType) {
        CircleTopicModel topic = new CircleTopicModel();
        topic.setCircleId(circle.getId());
        topic.setThreadId(circle.getThreadId());
        topic.setSiteId(circle.getSiteId());
        topic.setTitle(circle.getTitle());
        topic.setNoteId(circle.getContentNoteId());
        topic.setInterestedCount(circle.getCircleViewCount());
        topic.setState(circle.getState());
        topic.setType(circle.getType());
        topic.setGetuiType(getuiType.getValue());
        if (thread != null) {
            topic.setStyle(thread.getStyle());
            topic.setTopMax(thread.getTopMax());
            topic.setHotMax(thread.getHotMax());
            topic.setHotTitle(thread.getHotTitle());
            topic.setHotPrefer(thread.getHotPrefer());
            topic.setCover1(thread.getAttrAsString(CircleAttrKeys.HOT_TOPIC_BKG_IMAGE_1));
            topic.setColor(thread.getAttrAsString(CircleAttrKeys.HOT_TOPIC_BKG_COLOR));
            topic.setAvatar(thread.getAttrAsString(CircleAttrKeys.TOPIC_AVATAR));
        }
        //存在圈子分享配置则构建分享内容
        if (circle.getCircleShareTitle() != null ||
                circle.getCircleShareContent() != null ||
                circle.getCircleShareImage() != null ||
                circle.getCircleShareUrl() != null
        ) {
            CircleThreadModel circleModel = Mapper.map(circle, CircleThreadModel.class);
            circleModel.setId(circle.getThreadId());
            circleModel.setCircleId(circle.getId());
            circleModel.setSiteId(circle.getSiteId());
            circleModel.setTitle(circle.getTitle());
            topic.setShare(circleModelHelper.createCircleShareModel(circleModel));
        }
        return topic;
    }

    public static CircleThreadModel topic2circle(CircleTopicModel topic, NoteThreadModel thread) {
        CircleThreadModel model = new CircleThreadModel();
        model.setId(topic.getThreadId());
        model.setCircleId(topic.getCircleId());
        model.setOrder(topic.getOrder());
        model.setTitle(topic.getTitle());
        model.setContentNoteId(topic.getNoteId());
        model.setSiteId(thread.getSiteId());
        model.setPostRule(thread.getPostRule());
        model.setCondition(thread.getCondition());
        model.setTopMax(thread.getTopMax());
        model.setHotMax(thread.getHotMax());
        model.setHotTitle(thread.getHotTitle());
        model.setHotPrefer(thread.getHotPrefer());
        model.setStyle(thread.getStyle());
        model.setAttr(thread.getAttr());
        return model;
    }

    public static final NoteGrainFlag GRAIN_RECOMMEND_NOTE_LIST = new NoteGrainFlag.Builder()
            .setNeedUserInfo(true)
            .setNeedContent(true)
            .setNeedLocation(false)
            .setNeedCountData(false)
            .setNeedLinks(false)
            .setNeedThreads(true)
            .setNeedOriginThread(true)
            .setNeedStickyCommentData(false)
            .setNeedCommentUserInfo(true)
            .setHideContentOnDelete(false)
            .setAllowCache(true)
            .setAsViewCount(false)
            .setNeedExtra(false)
            .setHonorAnonymous(true)
            .setHonorBlacklist(false)
            .setNeedCommentListSize(1)
            .setNeedPraiseListSize(1)
            .setNoteOrder(NoteOrder.REFRESH_TIME_DESC)
            .setCommentOrder(NoteCommentOrder.CREATE_TIME_DESC)
            .create();

    public static final NoteGrainFlag GRAIN_TOPIC_NOTE_DETAIL = new NoteGrainFlag.Builder()
            .setNeedUserInfo(true)
            .setNeedContent(true)
            .setNeedLocation(true)
            .setNeedCountData(true)
            .setNeedLinks(true)
            .setNeedThreads(true)
            .setNeedOriginThread(true)
            .setNeedStickyCommentData(true)
            .setNeedCommentUserInfo(true)
            .setHideContentOnDelete(true)
            .setAllowCache(true)
            .setAsViewCount(true)
            .setNeedExtra(true)
            .setNeedCommentListSize(0)
            .setNeedPraiseListSize(8)
            .setHonorAnonymous(true)
            .setHonorBlacklist(true)
            .setNoteOrder(NoteOrder.REFRESH_TIME_DESC)
            .setCommentOrder(NoteCommentOrder.CREATE_TIME_DESC)
            .create();

}
