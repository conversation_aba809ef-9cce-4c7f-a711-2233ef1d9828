package com.kaiba.m.core.service.note.note;

import com.kaiba.lib.base.constant.note.NoteState;
import com.kaiba.lib.base.domain.common.Audio;
import com.kaiba.lib.base.domain.common.Gallery;
import com.kaiba.lib.base.domain.note.NoteGrainFlag;
import com.kaiba.lib.base.domain.note.NoteLink;
import com.kaiba.lib.base.util.GsonUtils;
import com.kaiba.m.core.domain.note.Note;
import com.kaiba.m.core.service.note.NoteCacheConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.connection.StringRedisConnection;
import org.springframework.data.redis.core.RedisCallback;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * author: lyux
 * date: 19-6-4
 */
@Slf4j
@Service
public class NoteCacheService {

    private final StringRedisTemplate stringRedisTemplate;

    public NoteCacheService(StringRedisTemplate stringRedisTemplate) {
        this.stringRedisTemplate = stringRedisTemplate;
    }

    Optional<Note> getNoteById(String noteId, NoteGrainFlag grain) {
        if (NoteCacheConfig.CACHE_DEBUG) {
            log.info("[cache_debug][cache] get note: " + noteId);
        }
        String cacheKey = id2key(noteId);
        List<Object> dataList = stringRedisTemplate.opsForHash().multiGet(
                cacheKey, NoteCacheMapper.getFieldMapperByGrain(grain).getFieldObjectList());
        NoteCacheMapper.NoteCacheParser result = NoteCacheMapper.getNoteCacheParser(dataList, grain);
        String id = result.getValue("id");
        if (null == id) {
            return Optional.empty();
        } else if (!id.equals(noteId)) {
            stringRedisTemplate.delete(cacheKey);
            return Optional.empty();
        } else {
            return Optional.of(result.getCache());
        }
    }

    List<Note> getNoteListIn(List<String> noteIdList, NoteGrainFlag grain) {
        if (NoteCacheConfig.CACHE_DEBUG) {
            log.info("[cache_debug][cache] get note list: " + noteIdList);
        }
        if (null == noteIdList || noteIdList.isEmpty()) {
            return Collections.emptyList();
        }
        List<String> keys = noteIdList.stream().map(NoteCacheService::id2key).collect(Collectors.toList());
        String[] noteFieldKeys = NoteCacheMapper.getFieldMapperByGrain(grain).getFieldArray();
        List<Object> redisResult;
        try {
            // size of 'ids' and 'dataList' are always the same:
            // redis will fill coordinate position of 'dataList' with null if cache can not be found.
            redisResult = stringRedisTemplate.executePipelined((RedisCallback<Object>) connection -> {
                StringRedisConnection conn = (StringRedisConnection) connection;
                for (String key : keys) {
                    conn.hMGet(key, noteFieldKeys);
                }
                return null;
            });
        } catch (Exception e) {
            redisResult = Collections.emptyList();
            log.error("get note list by id from redis fail. " +
                    "noteIds: " + String.join(",", noteIdList) + ", grain: " + grain.getFlag(), e);
        }
        if (redisResult.size() != noteIdList.size()) {
            // something unexpected happens. just fill return list with null.
            ArrayList<Note> noteList = new ArrayList<>(noteIdList.size());
            for (int i = 0; i < noteIdList.size(); i ++) {
                noteList.add(null);
            }
            return noteList;
        } else {
            return redisResult.stream()
                    .map(data -> (List<String>) data)
                    .map(data -> NoteCacheMapper.getNote(data, grain))
                    .collect(Collectors.toList());
        }
    }

    void updateNote(Note note) {
        Map<String, String> value = NoteCacheMapper.note2map(note);
        String cacheKey = id2key(note.getId());
        stringRedisTemplate.execute((RedisCallback<Object>) connection -> {
            StringRedisConnection conn = (StringRedisConnection) connection;
            conn.hMSet(cacheKey, value);
            conn.expire(cacheKey, NoteCacheConfig.NOTE_CACHE_EXPIRE.getSeconds());
            return conn.closePipeline();
        });
    }

    void updateNoteForNone(String noteId) {
        Map<String, String> value = NoteCacheMapper.note2mapForNone();
        String cacheKey = id2key(noteId);
        stringRedisTemplate.execute((RedisCallback<Object>) connection -> {
            StringRedisConnection conn = (StringRedisConnection) connection;
            conn.hMSet(cacheKey, value);
            conn.expire(cacheKey, NoteCacheConfig.NOTE_CACHE_EXPIRE.getSeconds());
            return conn.closePipeline();
        });
    }

    void updateNoteAudio(String noteId, Audio audio) {
        String value = null == audio ? null : GsonUtils.getGson().toJson(audio);
        updateNoteField(noteId, "audio", value);
    }

    void updateNoteLinks(String noteId, List<NoteLink> noteLinks) {
        String value = (null == noteLinks || noteLinks.size() == 0) ? null : GsonUtils.getGson().toJson(noteLinks);
        updateNoteField(noteId, "links", value);
    }

    void updateNoteGallery(String noteId, Gallery gallery) {
        String value = gallery == null ? null : GsonUtils.getGson().toJson(gallery);
        updateNoteField(noteId, "gallery", value);
    }

    void updateNoteStickyComment(String noteId, String stickyCommentId) {
        updateNoteField(noteId, "stickyCommentId", stickyCommentId);
    }

    void updateNoteRemark(String noteId, String remark) {
        updateNoteField(noteId, "remark", remark);
    }

    void updateNoteAnonymous(String noteId, Boolean anonymous) {
        String value = anonymous == null ? null : anonymous.toString();
        updateNoteField(noteId, "isAnonymous", value);
    }

    void updateNoteState(String noteId, NoteState state) {
        updateNoteField(noteId, "state", Integer.toString(state.getValue()));
    }

    void updateNoteTopThreads(String noteId, List<String> threadList) {
        String value = (null == threadList || threadList.size() == 0) ? null : GsonUtils.getGson().toJson(threadList);
        updateNoteField(noteId, "topThreads", value);
    }

    void updateNoteHotThreads(String noteId, List<String> threadList) {
        String value = (null == threadList || threadList.size() == 0) ? null : GsonUtils.getGson().toJson(threadList);
        updateNoteField(noteId, "hotThreads", value);
    }

    void updateNoteThreads(String noteId, List<String> threadList) {
        String value = (null == threadList || threadList.size() == 0) ? null : GsonUtils.getGson().toJson(threadList);
        updateNoteField(noteId, "threads", value);
    }

    void updateNoteCommentCount(String noteId, Integer count) {
        updateNoteField(noteId, "commentCount", count == null ? null : count.toString());
    }

    void updateNotePraiseCount(String noteId, Integer count) {
        updateNoteField(noteId, "praiseCount", count == null ? null : count.toString());
    }

    void updateNoteExtra(String noteId, Map<String, String> extra) {
        String value = (null == extra || extra.size() == 0) ? null : GsonUtils.getGson().toJson(extra);
        updateNoteField(noteId, "extra", value);
    }

    private void updateNoteField(String noteId, String fieldName, String fieldValue) {
        String cacheKey = id2key(noteId);
        if (null == fieldValue) {
            stringRedisTemplate.opsForHash().delete(cacheKey, fieldName);
        } else {
            stringRedisTemplate.execute((RedisCallback<Object>) connection -> {
                StringRedisConnection conn = (StringRedisConnection) connection;
                if (conn.exists(cacheKey)) {
                    conn.hSet(cacheKey, fieldName, fieldValue);
                }
                return conn.closePipeline();
            });
        }
    }

    @Async
    void updateCacheMissingNoteListAsync(List<String> cacheMissingIdList, List<Note> noteList) {
        if (cacheMissingIdList == null || cacheMissingIdList.size() == 0) {
            return;
        }
        if (cacheMissingIdList.size() == noteList.size()) {
            updateNoteList(noteList);
        } else {
            for (String noteId : cacheMissingIdList) {
                Note note = null;
                for (Note n : noteList) {
                    if (noteId.equals(n.getId())) {
                        note = n;
                        break;
                    }
                }
                if (note == null || note.getId() == null) {
                    updateNoteForNone(noteId);
                } else {
                    updateNote(note);
                }
            }
        }
        updateNoteList(noteList);
    }

    void updateNoteList(List<Note> noteList) {
        if (noteList == null || noteList.size() == 0) {
            return;
        }
        List<Map<String, String>> noteMapList = noteList.stream()
                .filter(note -> note != null && note.getId() != null)
                .map(NoteCacheMapper::note2map)
                .collect(Collectors.toList());
        try {
            stringRedisTemplate.execute((RedisCallback<Object>) connection -> {
                StringRedisConnection conn = (StringRedisConnection) connection;
                for (Map<String, String> noteMap : noteMapList) {
                    String cacheKey = id2key(noteMap.get("id"));
                    conn.hMSet(cacheKey, noteMap);
                    conn.expire(cacheKey, NoteCacheConfig.NOTE_CACHE_EXPIRE.getSeconds());
                }
                return conn.closePipeline();
            });
        } catch (Exception e) {
            log.error("set user list to redis fail", e);
        }
    }

    void invalidate(String noteId) {
        stringRedisTemplate.delete(noteId);
    }

    void increaseCommentCount(Note note, int delta) {
        increaseCount(note, "commentCount", delta);
    }

    void increasePraiseCount(Note note, int delta) {
        increaseCount(note, "praiseCount", delta);
    }

    void increaseShareCount(Note note) {
        increaseCount(note, "shareCount", 1);
    }

    private void increaseCount(Note note, String field, int incr) {
        String cacheKey = id2key(note.getId());
        Boolean exists = stringRedisTemplate.hasKey(cacheKey);
        if (exists == null || !exists) {
            updateNote(note);
        }
        stringRedisTemplate.opsForHash().increment(cacheKey, field, incr);
    }

    private static String id2key(String noteId) {
        return NoteCacheConfig.NOTE_CACHE_KEY + noteId;
    }

}
