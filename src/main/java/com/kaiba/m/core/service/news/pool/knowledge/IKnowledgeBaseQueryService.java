package com.kaiba.m.core.service.news.pool.knowledge;

import com.kaiba.lib.base.domain.news.pool.knowledge.KnowledgeBaseModel;

/**
 * Description: 知识库实例查询Service层接口
 * Author: ZM227
 * Date: 2025/2/14 11:15
 */
public interface IKnowledgeBaseQueryService {

    /**
     * 通过知识库实例key查找
     * @param baseKey 知识库实例key
     * @return 知识库实例
     */
    KnowledgeBaseModel findBaseByKey(String baseKey);

}
