package com.kaiba.m.core.service.replydict;

import java.time.Duration;
import java.util.Arrays;
import java.util.List;

/**
 * Created by shenxl on 2020/10/23
 */
public class ReplyConstant {
    public static final int KEY_MAX_CACHE_COUNT = 100;
    static final String REPLY_KEY_BY_ID = "k_reply_key_by_id_";
    static final String PLACE_HOLDER_ELEMENT = "__place_holder__";
    static final long PLACE_HOLDER_SCORE = Long.MAX_VALUE - 1;
    static final Duration KEY_LIST_EXPIRE = Duration.ofMinutes(45);

    public static final List<String> GROUP_TYPES = Arrays.asList(
            "traffic_limit_sunday",
            "traffic_limit_monday",
            "traffic_limit_tuesday",
            "traffic_limit_wednesday",
            "traffic_limit_thursday",
            "traffic_limit_friday",
            "traffic_limit_saturday"
    );
}