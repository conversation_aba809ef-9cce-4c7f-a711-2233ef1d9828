package com.kaiba.m.core.service.tmuyun.dupname;

import com.kaiba.lib.base.constant.tmuyun.TmuyunRefType;
import com.kaiba.lib.base.domain.tmuyun.tmuyun.ArticleApiModel;

/**
 * <AUTHOR>
 * @date 2023/02/07 14:37
 **/
public interface ITmuyunArticleDupNameStrategy {

    default String chooseStrategy(ArticleApiModel model) {
        if (TmuyunRefType.nameOf(model.getRefType()).isPresent()) {
            TmuyunRefType tmuyunRefType = TmuyunRefType.nameOf(model.getRefType()).get();
            switch (tmuyunRefType) {
                case HOOLO:
                case EXT:
                    return getLowerSimpleName(TmuyunSimpleNameStrategy.class);
                case PROGRAM:
                    return getLowerSimpleName(TmuyunProgramNameStrategy.class);
                default:
                    return null;
            }
        }
        return null;
    }

    default String getLowerSimpleName(Class<?> clazz) {
        return clazz.getSimpleName().toLowerCase();
    }

    ArticleApiModel resolveDupName(ArticleApiModel model);

}
