package com.kaiba.m.core.service.tmuyun;

import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.LoadingCache;
import com.google.common.collect.Maps;
import com.google.gson.reflect.TypeToken;
import com.kaiba.lib.base.assembler.Mapper;
import com.kaiba.lib.base.constant.tmuyun.TmuyunSendResult;
import com.kaiba.lib.base.domain.tmuyun.tmuyun.ArticleApiModel;
import com.kaiba.lib.base.domain.tmuyun.tmuyun.ArticleGroupModel;
import com.kaiba.lib.base.response.KbCode;
import com.kaiba.lib.base.response.KbEntity;
import com.kaiba.lib.base.response.KbException;
import com.kaiba.lib.base.rte.RelativeTimeExpression;
import com.kaiba.lib.base.util.GsonUtils;
import com.kaiba.m.core.domain.tmuyun.ArticleGroup;
import com.kaiba.m.core.domain.tmuyun.ArticleSendRecord;
import com.kaiba.m.core.domain.tmuyun.ArticleSnapshot;
import com.kaiba.m.core.repository.tmuyun.TmuyunArticleGroupRepository;
import com.kaiba.m.core.repository.tmuyun.TmuyunArticleRepository;
import com.kaiba.m.core.repository.tmuyun.TmuyunArticleSnapshotRepository;

import io.micrometer.core.instrument.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Recover;
import org.springframework.retry.annotation.Retryable;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.time.Duration;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022-08-23
 */
@Slf4j
@Service
public class TmuArticleService {

    private static final String URL_ARTICLE_VIEW_COUNT = "http://kaiba-rush/tmuyun/stat/getArticleStatByPubIdIn?pubIds=";
    private static final ArticleSendRecord FAKE_RECORD = new ArticleSendRecord();
    private static final ArticleApiModel FAKE_ARTICLE = new ArticleApiModel();
    private static final String DAY_END_EXPRESSION = "^d+2d";

    private final LoadingCache<String, ArticleSendRecord> cache = Caffeine.newBuilder()
            .expireAfterWrite(Duration.ofMinutes(3))
            .build(this::getRecordByRefId);

    private final TmuyunApiService apiService;
    private final TmuyunArticleDupService dupNameStrategy;
    private final TmuyunArticleRepository articleRepository;
    private final TmuyunArticleSnapshotRepository snapshotRepository;
    private final TmuyunArticleGroupRepository articleGroupRepository;
    private final RestTemplate restTemplate;

    public TmuArticleService(
            TmuyunApiService apiService,
            TmuyunArticleDupService dupNameStrategy,
            TmuyunArticleRepository articleRepository,
            TmuyunArticleSnapshotRepository snapshotRepository,
            TmuyunArticleGroupRepository articleGroupRepository,
            RestTemplate restTemplate
    ) {
        this.apiService = apiService;
        this.dupNameStrategy = dupNameStrategy;
        this.articleRepository = articleRepository;
        this.snapshotRepository = snapshotRepository;
        this.articleGroupRepository = articleGroupRepository;
        this.restTemplate = restTemplate;
    }

    public void deleteArticleDraftByPubId(Integer userId, String pubId) {
        ArticleSendRecord sendRecord = articleRepository.deleteArticleDraftByPubId(pubId);
        log.info("delete article draft by user: " + userId + ", draft: " + sendRecord);
    }

    public ArticleGroupModel editArticleGroupKey(String id, String pubId, String desc) {
        return entity2Model(articleGroupRepository.updateGroupKey(id, pubId, desc));
    }

    public boolean existsArticle(ArticleApiModel data) {
        return articleRepository.existsByRefIdAndType(data.getRefId(), data.getRefType());
    }

    public boolean existsArticleByTitle(ArticleApiModel data) {
        return articleRepository.existsByArticleTitle(data.getDocTitle());
    }

    public boolean existsDraftByArticlePubId(String pubId) {
        return articleRepository.existsByArticlePubId(pubId);
    }

    public void recallArticle(ArticleApiModel data) {
        long current = System.currentTimeMillis();
        TmuyunApiService.TrackingResult result = apiService.sendArticle(data);
        if (result.getCode() == 0) {
            log.info("recall article from tmuyun, call api success: " + data.getRecordId() + ", " + result);
            articleRepository.updateAsRecall(data.getRecordId(), current);
        } else {
            log.error("recall article from tmuyun, call api fail: " + data.getRecordId() + ", " + result);
            throw new KbException(KbCode.ILLEGAL_STATE, "recall article api fail: " + result)
                .setReadableMessage("请求省宣接口失败").li();
        }
    }

    public ArticleApiModel saveArticle(ArticleApiModel data) {
        return saveArticleWithSendResult(data, TmuyunSendResult.WAITING.getValue());
    }

    public ArticleApiModel saveArticleDraft(ArticleApiModel data) {
        return saveArticleWithSendResult(data, TmuyunSendResult.DRAFT.getValue());
    }

    public ArticleGroupModel saveArticleGroup(String key, String pubId, String desc) {
        ArticleGroup group = new ArticleGroup();
        group.setKey(key);
        group.setPubId(pubId);
        group.setDesc(desc);
        return entity2Model(articleGroupRepository.saveOrUpdateByGroupKey(group));
    }

    @Async
    @Retryable(
        value = Exception.class, maxAttempts = 3, backoff = @Backoff(delay = 1000 * 15, multiplier = 3.0))
    public void sendArticle(ArticleApiModel data) {
        long current = System.currentTimeMillis();
        data.setCreateTime(current);
        data.setPubTime(current);
        TmuyunApiService.TrackingResult result = apiService.sendArticle(data);
        if (result.getCode() == 0) {
            log.info("send article to tmuyun, call api success: " + data.getRecordId() + ", " + result);
            articleRepository.updateAsSuccess(data.getRecordId(), current);
        } else {
            log.error("send article to tmuyun, call api fail: " + data.getRecordId() + ", " + result);
            articleRepository.updateAsFail(
                data.getRecordId(), result.getCode(), result.getMsg(), true);
            throw new KbException(KbCode.ILLEGAL_STATE, "call tmu send article api fail: " + result).li();
        }
    }

    public void sendUpdateArticle(ArticleApiModel lastData, ArticleApiModel newData) {
        long current = System.currentTimeMillis();
        TmuyunApiService.TrackingResult result = apiService.sendArticle(newData);
        if (result.getCode() == 0) {
            log.info("send update article to tmuyun, call api success: " + newData.getRecordId() + ", " + result);
            ArticleSnapshot snapshot = new ArticleSnapshot();
            snapshot.setRecordId(newData.getRecordId());
            snapshot.setLastData(lastData);
            snapshot.setData(newData);
            snapshot.setCreateTime(current);
            String snapshotId = snapshotRepository.save(snapshot).getId();
            articleRepository.updateAsUpdate(newData.getRecordId(), snapshotId, newData, current);
        } else {
            log.error("send update article to tmuyun, call api fail: " + newData.getRecordId() + ", " + result);
            throw new KbException(KbCode.ILLEGAL_STATE, "recall article api fail: " + result)
                .setReadableMessage("请求省宣接口失败").li();
        }
    }

    public ArticleApiModel getArticleByGroupKey(String key) {
        Optional<ArticleGroup> groupOptional = articleGroupRepository.findFirstByKey(key);
        if(groupOptional.isPresent()) {
            ArticleGroup group = groupOptional.get();
            String pubId = group.getPubId();

            String[] split = pubId.split("-");
            if (split.length != 2) {
                return FAKE_ARTICLE;
            }

            String refType = split[0];
            String refId = split[1];
            Optional<ArticleSendRecord> recordOptional = articleRepository.findFirstByRefIdAndTypeOrderByCreateTimeDesc(refId, refType);
            if(recordOptional.isPresent()) {
                return recordOptional.get().getArticle();
            }
        }
        return FAKE_ARTICLE;
    }

    public Page<ArticleSendRecord> getRecordList(
        Integer[] sendResult, String title, Long startTime, Long endTime, Integer page, Integer pageSize
    ) {
        return articleRepository.findAllByOrderByCreateTimeDesc(sendResult, title, startTime, endTime, page, pageSize);
    }

    public Long countSuccessArticle(Long startTime, Long endTime, Boolean isOriginal) {
        return articleRepository.countSuccessArticle(startTime, endTime, isOriginal);
    }

    public List<ArticleSendRecord> getRecordListByTmuPubTime(
        Long startTime, Long endTime, Integer page, Integer pageSize
    ) {
        int p = (page == null || page < 1) ? 0 : page - 1;
        int ps = (pageSize == null || pageSize > 100) ? 15 : pageSize;
        return articleRepository.findAllArticleByTmuPubTime(startTime, endTime, PageRequest.of(p, ps));
    }

    public Page<ArticleSendRecord> getRecordListByType(
            String lastId, Collection<String> types, Integer sendResult, Integer page, Integer pageSize
    ) {
        int p = page == null ? 0 : page - 1;
        int ps = pageSize == null ? 20 : pageSize;
        Pageable pageable = PageRequest.of(p, ps);
        if (lastId == null) {
            return articleRepository.findByTypeInAndSendResultOrderByIdDesc(types, sendResult, pageable);
        } else {
            return articleRepository.findByIdLessThanAndTypeInAndSendResultOrderByIdDesc(lastId, types, sendResult, pageable);
        }
    }

    public Page<ArticleGroupModel> getArticleGroupList(Integer page, Integer pageSize) {
        Page<ArticleGroup> pageData = articleGroupRepository.findAllByOrderByIdDesc(createPageable(page, pageSize));
        List<String> pubIds = pageData.stream().map(ArticleGroup::getPubId).collect(Collectors.toList());

        Map<String, ArticleSendRecord> articleMap = articleRepository.findAllByPubIdIn(pubIds)
                .stream()
                .collect(Collectors.toMap(t -> t.getArticle().getPubId(), o -> o, (o1, o2) -> o1));

        Map<String, Integer> viewMap = getArticleViewCount(pubIds);

        return pageData.map(t -> {
            ArticleGroupModel model = new ArticleGroupModel();
            ArticleSendRecord sendRecord = articleMap.get(t.getPubId());
            model.setId(t.getId());
            model.setKey(t.getKey());
            model.setDesc(t.getDesc());
            model.setPubId(t.getPubId());
            if(sendRecord != null) {
                model.setStatus(sendRecord.getSendResult());
                model.setValidTime(sendRecord.getTmuPubTime() == null ? 0 : RelativeTimeExpression.calculate(sendRecord.getTmuPubTime(), DAY_END_EXPRESSION));
            }
            model.setViewTotal(viewMap.getOrDefault(t.getPubId(), 0));
            return model;
        });
    }

    public List<ArticleSendRecord> getArticleListByPubIdIn(List<String> pubIds) {
        return articleRepository.findAllByPubIdIn(pubIds);
    }

    public Page<ArticleSendRecord> getSuccessRecordList(
        Integer[] sendResult, String title, Long startTime, Long endTime, Integer page, Integer pageSize
    ) {
        return articleRepository.findAllByOrderByCreateTimeDesc(sendResult, title, startTime, endTime, page, pageSize);
    }

    public List<ArticleSendRecord> getRecordHistoryList(String refId, String type) {
        return articleRepository.findAllByRefIdAndTypeOrderByCreateTimeDesc(refId, type);
    }

    public Optional<ArticleSendRecord> getRecordByRefIdAndType(String refId, String refType, boolean allowCache) {
        if (allowCache) {
            ArticleSendRecord record = cache.get(refType + "-" + refId);
            if (record == null || record.getId() == null) {
                return Optional.empty();
            } else {
                return Optional.of(record);
            }
        } else {
            return articleRepository.findFirstByRefIdAndTypeOrderByCreateTimeDesc(refId, refType);
        }
    }

    public Optional<ArticleSendRecord> getRecordByTitle(String title, TmuyunSendResult sendResult) {
        return articleRepository.getArticleByTitle(title, sendResult);
    }

    public ArticleSendRecord getRecordByPubId(String pubId) {
        return getRecordByRefId(pubId);
    }

    public ArticleSendRecord updateArticleDraftByPubId(Integer userId, ArticleApiModel model) {
        ArticleSendRecord record = articleRepository.updateArticleDraftByPubId(model);
        log.info("update article draft by user: " + userId + ", article: " + record);
        return record;
    }

    // -------------------------------------------------

    @Recover
    public void recover(ArticleApiModel data) {
        log.error("send article to tmuyun, call api cancel due to retry fail: " + data.getRecordId());
        articleRepository.updateAsCancel(data.getRecordId(), null, null);
    }

    private ArticleApiModel saveArticleWithSendResult(
        ArticleApiModel data, Integer sendResult
) {
        // 入库前进行重名判断
        checkArticleDupName(data);

        log.info("send article to tmuyun, prepare: " + data.getPubId());
        ArticleSendRecord record = new ArticleSendRecord();
        record.setRefId(data.getRefId());
        record.setType(data.getRefType());
        record.setArticle(data);
        record.setTmuApiOperation(data.getStatus());
        record.setSendResult(sendResult);
        record.setRetryCount(0);
        record.setCreateTime(System.currentTimeMillis());
        String recordId = articleRepository.insert(record).getId();
        data.setRecordId(recordId);
        return data;
    }

    private ArticleSendRecord getRecordByRefId(String pubId) {
        String[] split = pubId.split("-");
        if (split.length != 2) {
            return FAKE_RECORD;
        }
        String refType = split[0];
        String refId = split[1];
        return articleRepository.findFirstByRefIdAndTypeOrderByCreateTimeDesc(refId, refType).orElse(FAKE_RECORD);
    }

    private Map<String, Integer> getArticleViewCount(Collection<String> pubIds) {
        String uri = URL_ARTICLE_VIEW_COUNT + String.join(",", pubIds);
        String json = restTemplate.getForObject(uri, String.class);
        Map<String, Integer> viewMap = Maps.newHashMapWithExpectedSize(64);
        if (!StringUtils.isEmpty(json)) {
            KbEntity<List<ArticleGroupModel>> result = GsonUtils
                    .getGson()
                    .fromJson(json, new TypeToken<KbEntity<List<ArticleGroupModel>>>() {}.getType());
            viewMap.putAll(result.data()
                    .orElse(Collections.emptyList())
                    .stream()
                    .collect(Collectors.toMap(ArticleGroupModel::getPubId, ArticleGroupModel::getViewTotal)));
        }
        return viewMap;
    }

    private ArticleApiModel checkArticleDupName(ArticleApiModel model) {
        boolean exists = existsArticleByTitle(model);
        return exists ? dupNameStrategy.resolveDupName(model) : model;
    }

    private ArticleGroupModel entity2Model(ArticleGroup group) {
        return Mapper.map(group, ArticleGroupModel.class);
    }

    private static Pageable createPageable(Integer page, Integer pageSize) {
        int p = (page == null || page < 1) ? 0 : page - 1;
        int ps = (pageSize == null) ? 20 : pageSize;
        return PageRequest.of(p, ps);
    }
}
