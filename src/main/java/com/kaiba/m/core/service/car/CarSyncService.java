package com.kaiba.m.core.service.car;

import com.google.common.reflect.TypeToken;
import com.google.gson.JsonArray;
import com.google.gson.JsonObject;
import com.kaiba.lib.base.constant.car.CarRegion;
import com.kaiba.lib.base.constant.car.CarSize;
import com.kaiba.lib.base.domain.common.Image;
import com.kaiba.lib.base.util.GsonUtils;
import com.kaiba.lib.base.util.StringUtils;
import com.kaiba.m.core.domain.car.Car;
import com.kaiba.m.core.domain.car.CarBrand;
import com.kaiba.m.core.domain.car.CarBrandFactory;
import com.kaiba.m.core.middleware.qiniu.QiniuManager;
import com.kaiba.m.core.repository.car.CarBrandFactoryRepository;
import com.kaiba.m.core.repository.car.CarBrandRepository;
import com.kaiba.m.core.repository.car.CarRepository;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.json.JSONObject;
import org.springframework.http.client.SimpleClientHttpRequestFactory;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 车型车系数据同步服务
 * <AUTHOR>
 * @version CarSyncService, v0.1 2024/9/9 17:35 daopei Exp $
 **/
@Slf4j
@Service
public class CarSyncService {


    private final CarRepository carRepository;
    private final CarBrandRepository carBrandRepository;
    private final RestTemplate restTemplate;
    private final QiniuManager qiniuManager;

    private Map<String, CarBrandFactory> factoryNameMap;


//    private final static String brandUrl = "https://www.autohome.com.cn/js/index/findcarnew_branddata.js";
    private final static String brandUrl = "https://car-web-api.autohome.com.cn/car/brand/getbrand?sorttype=3";
    private final static String seriesUrl = "https://www.autohome.com.cn/ashx/index/GetHomeFindCar.ashx?type=1&v=1&brandid=";

    public CarSyncService(
            CarRepository carRepository,
            CarBrandRepository carBrandRepository,
            CarBrandFactoryRepository factoryRepository,
            QiniuManager qiniuManager
    ) {
        this.carRepository = carRepository;
        this.carBrandRepository = carBrandRepository;
        this.qiniuManager = qiniuManager;
        this.restTemplate = new RestTemplate(new SimpleClientHttpRequestFactory());
        this.factoryNameMap = factoryRepository.findAll().stream()
                .collect(Collectors.toMap(CarBrandFactory::getName, Function.identity(), (o1, o2) -> o1));
    }

    /**
     * json文件导入
     * @param content
     */
    public void loadByAutohome(String content) {
        List<AutohomeBrand> brands = GsonUtils.getGson().fromJson(content, new TypeToken<List<AutohomeBrand>>() {}.getType());
        saveCar(brands);
    }


    /**
     * 自动抓取数据
     */
    public void loadByAutohomeSource() {
        List<AutohomeOriginBrand> brands = getBrandJson();
        log.info("品牌数量: " + brands.size());
        List<AutohomeBrand> resultBrand = new ArrayList<>();
        for (AutohomeOriginBrand brand : brands) {
            AutohomeBrand autohomeBrand = getSeriesJson(brand);
            autohomeBrand.setCountry(brand.getCountry());
            resultBrand.add(autohomeBrand);
        }
        saveCar(resultBrand);
    }


    private void saveCar(List<AutohomeBrand> brands) {
        for (AutohomeBrand brand : brands) {
            CarBrand carBrand = carBrandRepository.findFirstByAutohomeId(brand.getBrandid())
                    .orElseGet(() -> initCarBrand(brand));
            for (AutohomeSeries series : brand.getSeries()) {
                Integer regionId = map2RegionId(brand.getCountry());
                Integer rightId = map2RightId(series.getSeriesplace());
                Integer sizeType = map2SizeType(series.getLevelName());

                Car car = carRepository.findCarByAutohomeId(series.getSeriesid()).orElse(null);
                if (car == null) {
                    //格式 brand + 00 + seriesId
                    String code = carBrand.getCode() + "00" + series.getSeriesid();
                    CarBrandFactory factory = factoryNameMap.get(series.getFactoryname());
                    String logoKey = "series_" + code;

                    Car carNew = new Car();
                    carNew.setAutohomeId(series.getSeriesid());
                    carNew.setBrandCode(carBrand.getCode());
                    carNew.setCode(code);
                    carNew.setName(series.getSeriesname());
                    carNew.setTypeName(series.getFactoryname());
                    carNew.setFactoryId(factory == null ? null : factory.getId());
                    carNew.setImage(logoKey);
                    carNew.setLogo(new Image(logoKey));
                    carNew.setCreateTime(System.currentTimeMillis()/1000);

                    float pl = series.getSeriesPriceMin() == null ? 0 : series.getSeriesPriceMin() / 10000.00f;
                    float ph = series.getSeriesPriceMax() == null ? 0 : series.getSeriesPriceMax() / 10000.00f;

                    carNew.setPriceLow(new BigDecimal(pl).setScale(2, RoundingMode.DOWN).floatValue());
                    carNew.setPriceTop(new BigDecimal(ph).setScale(2, RoundingMode.DOWN).floatValue());

                    carNew.setRegionId(regionId);
                    carNew.setRightId(rightId);
                    carNew.setSizeType(sizeType);

                    uploadMediaFromUrl(series.getSeriesImg(), logoKey);
                    carRepository.insert(carNew);
                    log.info("新增汽车: car:{}" , GsonUtils.getGson().toJson(carNew));
                } else {
                    //未设置 国别,级别,产权更新数据
                    if (car.getRegionId() == null) {
                        car.setRegionId(regionId);
                        car.setRightId(rightId);
                        car.setSizeType(sizeType);
                        carRepository.save(car);
                        log.info("修改汽车属性: car:{}" , GsonUtils.getGson().toJson(car));
                    }
                }
            }
        }
    }



    private List<AutohomeOriginBrand> getBrandJson() {
        String response = httpRequestGet(brandUrl);
//        String startStr = "var IndexFindCarBrand=";
//        String brandJson = response.subSequence(startStr.length(), response.length()).toString();

        JsonObject jsonObject = GsonUtils.getGson().fromJson(response, JsonObject.class);
        JsonObject result = jsonObject.getAsJsonObject("result");
        JsonArray brandJson = result.getAsJsonArray("brandlist");
        return GsonUtils.getGson().fromJson(brandJson, new TypeToken<List<AutohomeOriginBrand>>() {}.getType());
    }

    private AutohomeBrand getSeriesJson(AutohomeOriginBrand brand) {
        String seriesJson = httpRequestGet(seriesUrl + brand.getId());
        return parseSeriesJson(seriesJson, brand.getFirstletter());
    }

    private String httpRequestGet(String url) {
        return restTemplate.getForObject(url, String.class);
    }


    private CarBrand initCarBrand(AutohomeBrand brand) {
        CarBrand carBrand = new CarBrand();
        carBrand.setAutohomeId(brand.getBrandid());
        carBrand.setCode("90" + brand.getBrandid());
        carBrand.setName(brand.getBrandname());
        carBrand.setLetter(brand.getLetters());
        carBrand.setListOrder(0);
        String logoKey = "brand_" + carBrand.getCode();
        carBrand.setLogo(new Image(logoKey));
        carBrand.setCreateTime(System.currentTimeMillis()/1000);
        uploadMediaFromUrl(brand.getBrandlogo(), logoKey);
        log.info("新增汽车品牌: brand:{}", GsonUtils.getGson().toJson(carBrand));
        return carBrandRepository.insert(carBrand);
    }



    @Data
    class AutohomeOriginBrand {
        private Integer id;
        private String name;
        private String firstletter;
        private String logo;
        private String country;
    }

    public AutohomeBrand parseSeriesJson(String seriesJson, String brandLetter) {
        Map<String, Object> jsonMap = GsonUtils.getGson().fromJson(seriesJson, new TypeToken<Map<String, Object>>() {}.getType());
        Map<String, Object> result = ((Map<String, Object>) jsonMap.get("result"));

        AutohomeBrand brand = new AutohomeBrand();
        brand.setLetters(brandLetter);
        brand.setBrandname((String) result.get("brandname"));
        brand.setBrandid(((Double) result.get("brandid")).intValue());
        brand.setBrandlogo((String) result.get("brandlogo"));

        List<Map<String, Object>> factoryitems = (List<Map<String, Object>>) result.get("fctlist");
        List<AutohomeSeries> seriesList = new ArrayList<>();
        for (Map<String, Object> factoryItem : factoryitems) {
            String factoryname = (String) factoryItem.get("fctname");
            List<Map<String, Object>> seriesitems = (List<Map<String, Object>>) factoryItem.get("serieslist");
            for (Map<String, Object> seriesItem : seriesitems) {
                AutohomeSeries series = new AutohomeSeries();
                series.setFactoryname(factoryname);
                series.setLetters((String) seriesItem.get("fctPy"));
                series.setSeriesname((String) seriesItem.get("seriesName"));
                series.setSeriesid(((Double)seriesItem.get("seriesid")).intValue());
                series.setSeriesImg((String) seriesItem.get("seriesImg"));
                series.setSeriesPriceMin(((Double) seriesItem.get("seriesPriceMin")).intValue());
                series.setSeriesPriceMax(((Double) seriesItem.get("seriesPriceMax")).intValue());
                series.setLevelName((String) seriesItem.get("levelName"));
                series.setSeriesplace((String) seriesItem.get("seriesplace"));
                seriesList.add(series);
            }
        }
        brand.setSeries(seriesList);
        return brand;
    }

    private void uploadMediaFromUrl(String url, String fileKey) {
        try {
            qiniuManager.asyncFetch(url, fileKey, null, null);
        } catch (Exception e) {
            log.error("汽车图片同步获取失败, url:{}, fileKey:{}", url, fileKey);
        }
    }


    private static final Map<String, Integer> regionMap = Arrays.stream(CarRegion.values())
            .collect(Collectors.toMap(CarRegion::getName, CarRegion::getValue));
    private static final Map<String, Integer> sizeMap = Arrays.stream(CarSize.values())
            .collect(Collectors.toMap(CarSize::getName, CarSize::getValue));

    private static Integer map2RegionId(String country) {
        if (country == null) {
            return 999;
        }
        Integer regionId = regionMap.get(country);
        if (regionId == null) {
            return 999;
        } else {
            return regionId;
        }
    }

    private static Integer map2RightId(String place) {
        if (place == null) {
            return 999;
        }
        switch (place) {
            case "自主":
                return 1;
            case "进口":
                return 2;
            case "合资":
                return 3;
            case "独资":
                return 4;
            default:
                return 999;
        }
    }

    private static Integer map2SizeType(String size) {
        if (size == null) {
            return null;
        }
        if (size.contains("SUV")) {
            return CarSize.SUV.getValue();
        }
        if (size.contains("MPV")) {
            return CarSize.MPV.getValue();
        }
        return sizeMap.get(size);
    }

    @Data
    class AutohomeBrand {
        private String letters;
        private String brandname;
        private Integer brandid;
        private String brandlogo;
        private String country;

        private List<AutohomeSeries> series;
    }

    @Data
    class AutohomeSeries {
        private String factoryname;
        private String letters;
        private String seriesname;
        private Integer seriesid;
        private String seriesImg;
        private Integer seriesPriceMin;
        private Integer seriesPriceMax;

        /** 车型大小系列名称 size */
        private String levelName;
        /** 产权 right*/
        private String seriesplace;
        /** 国别 region */
        private String country;

    }
}
