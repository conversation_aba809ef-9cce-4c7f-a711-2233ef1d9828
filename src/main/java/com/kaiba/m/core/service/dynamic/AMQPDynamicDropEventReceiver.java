package com.kaiba.m.core.service.dynamic;

import com.google.gson.*;
import com.kaiba.lib.base.domain.dynamic.DynamicDropEventModel;
import com.kaiba.lib.base.middleware.KbEventReceiver;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.lang.reflect.Type;

/**
 * <AUTHOR>
 * @date 2019/3/14
 */
@Slf4j
@Component
public class AMQPDynamicDropEventReceiver extends KbEventReceiver<DynamicDropEventModel> {

    private static Gson compatibleDeserializerGson = new GsonBuilder()
            .registerTypeAdapter(int.class, new IntegerCompatibleDeserializer())
            .create();

    private final DynamicService dynamicService;

    @Autowired
    public AMQPDynamicDropEventReceiver(DynamicService dynamicService) {
        this.dynamicService = dynamicService;
    }

    @Override
    public void onReceiveEvent(DynamicDropEventModel data) {
        dynamicService.dropEvent(data);
    }

    @Override
    public Class<DynamicDropEventModel> getEventType() {
        return DynamicDropEventModel.class;
    }

    @Override
    protected Gson getGson() {
        return compatibleDeserializerGson;
    }

    private static class IntegerCompatibleDeserializer implements JsonDeserializer<Integer> {
        @Override
        public Integer deserialize(JsonElement json, Type typeOfT, JsonDeserializationContext context) throws JsonParseException {
            try {
                return json.getAsInt();
            } catch (Exception e) {
                return null;
            }
        }
    }
}
