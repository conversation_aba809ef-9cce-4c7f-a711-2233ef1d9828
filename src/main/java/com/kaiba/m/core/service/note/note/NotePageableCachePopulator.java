package com.kaiba.m.core.service.note.note;

import com.kaiba.lib.base.constant.note.NoteOrder;
import com.kaiba.m.core.domain.note.Note;
import com.kaiba.m.core.service.note.NoteCacheConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * author: lyux
 * date: 19-6-5
 */
@Slf4j
@Component
public class NotePageableCachePopulator {

    private final NoteDBService noteDBService;
    private final NoteCacheService noteCacheService;
    private final NoteIdListCacheService noteIdListCacheService;

    public NotePageableCachePopulator(
            NoteDBService noteDBService,
            NoteCacheService noteCacheService,
            NoteIdListCacheService noteIdListCacheService
    ) {
        this.noteDBService = noteDBService;
        this.noteCacheService = noteCacheService;
        this.noteIdListCacheService = noteIdListCacheService;
    }

    @Async
    public void populateNoteListByThreadId(NoteOrder noteOrder, String threadId) {
        if (noteIdListCacheService.isNoteOrderSupported(noteOrder)
                && !noteIdListCacheService.isPopulating(noteOrder, threadId)) {
            if (NoteCacheConfig.CACHE_DEBUG) {
                log.info("[cache_debug][db] populate note list for: "
                        + threadId + ", order: " + noteOrder + ", runtime: " + Thread.currentThread().getId());
            }
            Pageable pageable = PageRequest.of(
                    0, NoteCacheConfig.NOTE_ID_LIST_BY_THREAD_INIT_COUNT + 1, noteOrder.getSort());
            List<Note> noteList = noteDBService.getNoteListByThread(threadId, pageable);
            noteIdListCacheService.populate(noteOrder, threadId, noteList);
            noteCacheService.updateNoteList(noteList);
        }
    }

}
