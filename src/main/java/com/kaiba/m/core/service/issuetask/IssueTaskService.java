package com.kaiba.m.core.service.issuetask;

import com.google.gson.JsonSyntaxException;
import com.kaiba.lib.base.assembler.Mapper;
import com.kaiba.lib.base.constant.KbEndpoint;
import com.kaiba.lib.base.constant.issue.CancelReason;
import com.kaiba.lib.base.constant.issue.IssueTaskState;
import com.kaiba.lib.base.constant.issue.IssueTaskType;
import com.kaiba.lib.base.constant.push.PushRange;
import com.kaiba.lib.base.constant.push.PushType;
import com.kaiba.lib.base.domain.ampq.IssueTaskEvent;
import com.kaiba.lib.base.domain.issue.IssueModel;
import com.kaiba.lib.base.domain.issue.IssueTaskModel;
import com.kaiba.lib.base.response.KbCode;
import com.kaiba.lib.base.response.KbException;
import com.kaiba.lib.base.service.IIssueService;
import com.kaiba.lib.base.service.IPushService;
import com.kaiba.lib.base.util.appaction.AppActionType;
import com.kaiba.m.core.domain.issuetask.IssueTask;
import com.kaiba.m.core.domain.issuetask.WorkingExpert;
import com.kaiba.m.core.service.issuetask.expert.WorkingExpertService;
import com.kaiba.m.core.service.issuetask.task.IIssueTaskProvider;
import com.kaiba.m.core.service.issuetask.task.ITaskDurationProvider;
import com.kaiba.m.core.service.issuetask.task.ITaskEventCallback;
import com.kaiba.m.core.service.issuetask.task.ScheduledTaskChain;
import com.kaiba.m.core.util.JsonUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.listener.PatternTopic;
import org.springframework.data.redis.listener.RedisMessageListenerContainer;
import org.springframework.data.redis.listener.adapter.MessageListenerAdapter;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;

/**
 * author: lyux
 * date: 18-9-17
 */
@Slf4j
@Service
public class IssueTaskService implements
        ITaskEventCallback,
        IIssueTaskProvider,
        ITaskDurationProvider {

    private static final String REDIS_PUB_SUB_CHANNEL_PREFIX = "issue_task_";
    private static final String REDIS_KEY_TASK_START_LOCK = "issue_task_start_lock_";

    private final WorkingExpertService expertService;
    private final IssueTaskDBService databaseService;
    private final IIssueService issueService;
    private final IPushService pushService;
    private final StringRedisTemplate redisTemplate;

    private final ConcurrentHashMap<String, ScheduledTaskChain> issueTaskChains = new ConcurrentHashMap<>();
    private final ConcurrentHashMap<String, IssueTaskEventListener> eventListeners = new ConcurrentHashMap<>();

    private final String pubsubChannel;

    public IssueTaskService(
            WorkingExpertService expertService,
            IssueTaskDBService taskRecorder,
            IIssueService issueService,
            IPushService pushService,
            StringRedisTemplate redisTemplate,
            @Value("${spring.profiles.active}") String profile
    ) {
        this.expertService = expertService;
        this.databaseService = taskRecorder;
        this.issueService = issueService;
        this.pushService = pushService;
        this.redisTemplate = redisTemplate;
        this.pubsubChannel = REDIS_PUB_SUB_CHANNEL_PREFIX + profile;
    }

    // config redis pubsub
    @Bean
    public RedisMessageListenerContainer issueTaskRedisMessageListenerContainer(
            RedisConnectionFactory connectionFactory, MessageListenerAdapter issueTaskMessageListenerAdapter) {
        RedisMessageListenerContainer container = new RedisMessageListenerContainer();
        container.setConnectionFactory(connectionFactory);
        container.addMessageListener(
                issueTaskMessageListenerAdapter,
                new PatternTopic(pubsubChannel));
        return container;
    }

    // config redis pubsub
    @Bean
    public MessageListenerAdapter issueTaskMessageListenerAdapter() {
        return new MessageListenerAdapter(this, "onReceiveIssueTaskEvent");
    }

    // config redis pubsub
    public void onReceiveIssueTaskEvent(String msg) {
        log.info("receive issue task message from pubsub: " + msg);
        try {
            IssueTaskEvent event = JsonUtils.getGson().fromJson(msg, IssueTaskEvent.class);
            IssueTaskEventListener listener = eventListeners.remove(event.getIssueId());
            if (listener != null) {
                listener.onReceiveIssueTaskEvent(event);
            }
        } catch (JsonSyntaxException e) {
            log.warn("receive issue task message from pubsub, parse fail: " + msg, e);
        }
    }

    public void taskAcceptByExpert(String issueId, Integer expertId) {
        log.debug("task accept by expert, " + issueId + " | " + expertId);
        IssueModel issue = issueService.getIssueBasic(issueId).dataOrThrow();
        // we are not sure if the task is on this spring boot instance.
        // just send event to message queue to notify all.
        databaseService.taskAsAccept(issue, expertId);
        sendAsTaskAccepted(issueId, expertId);
        issueService.issueStateToWaiting(issueId, expertId);
    }

    public void taskRefuseByExpert(String issueId, Integer expertId) {
        log.debug("task refuse by expert, " + issueId + " | " + expertId);
        IssueModel issue = issueService.getIssueBasic(issueId).dataOrThrow();
        // we are not sure if the task is on this spring boot instance.
        // just send event to message queue to notify all.
        databaseService.taskAsRefuse(issue, expertId);
        sendAsTaskRefused(issueId, expertId);
        if (issue.getTaskType() == IssueTaskType.SPECIFY.getValue()) {
            sendAsTaskCanceled(issueId);
            issueService.issueStateToCancel(issue.getId(), null, CancelReason.EXPERT_REFUSED.getValue(), null);
        }
    }

    public void taskCancelByUser(String issueId, Integer userId) {
        log.debug("task cancel by user, " + issueId + " | " + userId);
        IssueModel issue = issueService.getIssueBasic(issueId).dataOrThrow();
        // we are not sure if the task is on this spring boot instance.
        // just send event to message queue to notify all.
        databaseService.taskAsCancel(issue);
        sendAsTaskCanceled(issueId);
        issueService.issueStateToCancel(issueId, userId, CancelReason.USER_CANCELED.getValue(), null);
    }

    public void taskCancelBySystem(IssueModel issue) {
        String issueId = issue.getId();
        log.debug("task cancel by system, " + issueId);
        // we are not sure if the task is on this spring boot instance.
        // just send event to message queue to notify all.
        databaseService.taskAsCancel(issue);
        sendAsTaskCanceled(issueId);
        issueService.issueStateToCancel(issueId, null, CancelReason.DISPATCH_TIMEOUT.getValue(), null).check();
    }

    public void taskStart(String issueId) {
        taskStart(issueService.getIssueBasic(issueId).dataOrThrow());
    }

    public void taskStart(IssueModel issue) {
        log.debug("task start, " + issue.getId());
        String redisKey = REDIS_KEY_TASK_START_LOCK + issue.getId();
        Boolean free = redisTemplate.opsForValue().setIfAbsent(redisKey, issue.getId(), 10, TimeUnit.SECONDS);
        if (free != null && free) {
            ScheduledTaskChain taskChain = new ScheduledTaskChain(issue, this, this, this);
            issueTaskChains.put(issue.getId(), taskChain);
            taskChain.start();
        } else {
            log.info("task start ignored, already starting. " + issue.getId());
        }
    }

    public void taskRecoverFromOnGoing(IssueModel issue) {
        log.debug("task recover from on going, " + issue.getId());
        IssueTask task = databaseService.getIssueTaskByIssue(issue.getId()).orElse(null);
        if (null == task || task.getState() != IssueTaskState.ON_GOING.getValue()) {
            throw new KbException(KbCode.ILLEGAL_STATE);
        }
        databaseService.taskAsTimeout(issue, task.getExpertId());
        taskStart(issue);
    }

    public void updateRefuseReason(String taskId, Integer expertId, Integer refuseReasonId, String refuseReasonContent) {
        databaseService.getIssueTaskById(taskId).ifPresent(task -> {
            if (!expertId.equals(task.getExpertId())) {
                throw new KbException(KbCode.AUTH_NOT_OWNER);
            }
            if (task.getState() != IssueTaskState.REFUSED.getValue()) {
                throw new KbException(KbCode.ILLEGAL_STATE, "refuse reason can only be added to refused tasks");
            }
            task.setRefuseReasonId(refuseReasonId);
            task.setRefuseReasonContent(refuseReasonContent);
            databaseService.updateRefuseReason(taskId, refuseReasonId, refuseReasonContent);
        });
    }

    public List<IssueTask> getTaskListByExpert(Integer expertId, Integer[] states, int page, int pageSize) {
        return databaseService.getIssueTaskListByExpert(expertId, states, page, pageSize);
    }

    public long getTaskCountByExpert(Integer expertId, Integer[] states, Long since) {
        return databaseService.getIssueTaskCountByExpert(expertId, states, since);
    }

    public Optional<IssueTask> getTaskByIssue(String issueId) {
        return databaseService.getIssueTaskByIssue(issueId);
    }

    public IssueTaskModel task2model(IssueTask task) {
        IssueTaskType type = IssueTaskType.valueOf(task.getType()).orElse(IssueTaskType.DISPATCH);
        IssueTaskState state = IssueTaskState.valueOf(task.getState())
                .orElseThrow(KbException.supplier(KbCode.ILLEGAL_ARGUMENT));
        IssueTaskModel model = Mapper.map(task, IssueTaskModel.class);
        model.setCreateTime(task.getCreateTime() / 1000);
        model.setUpdateTime(task.getUpdateTime() / 1000);
        model.setTimeout(IssueConfigs.taskDurationDispatch);
        model.setDescription(state.getDescription());
        return model;
    }

    // -------------------------------------------------

    public Map<String, Object> getRunningStatus() {
        Map<String, Object> status = new HashMap<>();
        status.put("runningTasks", databaseService
                .getIssueTaskCountByStates(new Integer[] { IssueTaskState.ON_GOING.getValue() }));
        status.put("workingExpertCount", expertService.getExpertCount());
        status.put("eventListenerCount", eventListeners.size());
        status.put("taskChainCount", issueTaskChains.size());
        return status;
    }

    @Override
    public void onTaskEvent(IssueModel issue, IssueTask task, IssueTaskState state) {
        Integer expertId = task.getExpertId();
        log.debug("on task event: " + state + ", issue: " + issue.getId() + ", expert: " + expertId);
        switch (state) {
            case ON_GOING: {
                eventListeners.put(issue.getId(), issueTaskChains.get(issue.getId()));
                notifyPeering(task);
                break;
            }
            case ACCEPTED: {
                expertService.acceptJob(expertId, issue.getId());
                issuePeeringFinished(issue.getId());
                break;
            }
            case REFUSED: {
                eventListeners.remove(issue.getId());
                expertService.refuseJob(expertId, issue.getId());
                break;
            }
            case TIMEOUT: {
                eventListeners.remove(issue.getId());
                databaseService.taskAsTimeout(issue, expertId);
                expertService.timeoutJob(expertId, issue.getId());
                break;
            }
            case NO_EXPERT: {
                eventListeners.remove(issue.getId());
                break;
            }
            case CANCEL: {
                issuePeeringFinished(issue.getId());
                break;
            }
        }
    }

    @Override
    public long getTaskDuration(IssueModel issue) {
        return issue.getTaskType() == IssueTaskType.DISPATCH.getValue() ?
                IssueConfigs.taskDurationDispatch : IssueConfigs.taskDurationSpecify;
    }

    @Override
    public IssueTask createTaskForIssue(IssueModel issue, IssueTask lastTask) {
        WorkingExpert expert = null;
        if (issue.getTaskType() == IssueTaskType.DISPATCH.getValue()) {
            expert = expertService.dispatchJob(issue).orElse(null);
        } else if (issue.getTaskType() == IssueTaskType.SPECIFY.getValue()) {
            if (null == lastTask) {
                expert = expertService.findExpertById(issue.getExpert().getId()).orElse(null);
            }
        }
        IssueTask task;
        if (null == expert) {
            task = databaseService.taskAsNoExpert(issue);
        } else {
            task = databaseService.taskAsOnGoing(issue, expert.getId());
        }
        log.debug("for issue: " + issue.getId() + ", get expert: " + (null == expert ? null : expert.getId()));
        return task;
    }

    private void issuePeeringFinished(String issueId) {
        issueTaskChains.remove(issueId);
        eventListeners.remove(issueId);
        expertService.issuePeeringFinished(issueId);
    }

    private void notifyPeering(IssueTask task) {
        Map<String,String> params = new HashMap<>();
        params.put("id",task.getIssueId());
        pushService.addAndPush(
                "您有新的问答派单", "", AppActionType.PAGE_ISSUE_DETAIL.getAction(),
                JsonUtils.getGson().toJson(params),task.getExpertId().toString(), PushType.ISSUE_DISPATCH.getValue(),
                PushRange.USER.getValue(),task.getIssueId(), KbEndpoint.EXPERT.getValue(),null,null);
    }

    private void sendAsTaskAccepted(String issueId, Integer expertId) {
        IssueTaskEvent event = new IssueTaskEvent(issueId, expertId, IssueTaskState.ACCEPTED.getValue());
        redisTemplate.convertAndSend(REDIS_PUB_SUB_CHANNEL_PREFIX, JsonUtils.getGson().toJson(event));
    }

    private void sendAsTaskRefused(String issueId, Integer expertId) {
        IssueTaskEvent event = new IssueTaskEvent(
                issueId, expertId, IssueTaskState.REFUSED.getValue());
        redisTemplate.convertAndSend(REDIS_PUB_SUB_CHANNEL_PREFIX, JsonUtils.getGson().toJson(event));
    }

    private void sendAsTaskCanceled(String issueId) {
        IssueTaskEvent event = new IssueTaskEvent(issueId, null, IssueTaskState.CANCEL.getValue());
        redisTemplate.convertAndSend(REDIS_PUB_SUB_CHANNEL_PREFIX, JsonUtils.getGson().toJson(event));
    }

}
