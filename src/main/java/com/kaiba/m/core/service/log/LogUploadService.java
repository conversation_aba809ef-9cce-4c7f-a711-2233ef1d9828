package com.kaiba.m.core.service.log;

import com.kaiba.lib.base.util.StringUtils;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.time.Duration;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @date 2023/05/08 10:26
 **/
@Slf4j
@Service
public class LogUploadService {

    private static final String REDIS_UPLOAD_CID_KEY = "java_core_log_upload_cid_";
    private static final String REDIS_UPLOAD_UID_KEY = "java_core_log_upload_uid_";

    private static final Duration EXPIRE_SHORT = Duration.ofHours(6);

    private final StringRedisTemplate redisTemplate;

    public LogUploadService(StringRedisTemplate redisTemplate) {
        this.redisTemplate = redisTemplate;
    }

    public void closeUploadSwitch(Integer uid) {
        String cacheKey = REDIS_UPLOAD_UID_KEY + uid;
        redisTemplate.delete(cacheKey);
    }

    public void closeUploadSwitch(String cid) {
        String cacheKey = REDIS_UPLOAD_CID_KEY + cid;
        redisTemplate.delete(cacheKey);
    }

    public void openUploadSwitch(Integer uid) {
        String cacheKey = REDIS_UPLOAD_UID_KEY + uid;
        redisTemplate.opsForValue().setBit(cacheKey, 0, true);
        redisTemplate.expire(cacheKey, EXPIRE_SHORT);
    }

    public void openUploadSwitch(String cid) {
        String cacheKey = REDIS_UPLOAD_CID_KEY + cid;
        redisTemplate.opsForValue().setIfAbsent(cacheKey, String.valueOf(System.currentTimeMillis()), EXPIRE_SHORT);
    }

    public boolean getUploadConfig(String cid, Integer userId) {
        if (userId != null) {
            String cacheKey = REDIS_UPLOAD_UID_KEY + userId;
            Boolean result = redisTemplate.opsForValue().getBit(cacheKey, 0);
            return Boolean.TRUE.equals(result);
        }
        String cidKey = REDIS_UPLOAD_CID_KEY + cid;
        String result = redisTemplate.opsForValue().get(cidKey);
        return !StringUtils.isEmpty(result);
    }
}
