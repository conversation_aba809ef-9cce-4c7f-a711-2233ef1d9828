package com.kaiba.m.core.service.appcomponent.lego;

import com.github.benmanes.caffeine.cache.CacheLoader;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.LoadingCache;
import com.kaiba.lib.base.assembler.Mapper;
import com.kaiba.lib.base.constant.KbProperties;
import com.kaiba.lib.base.constant.applego.AppComponentState;
import com.kaiba.lib.base.domain.applego.lego.LegoBlockModel;
import com.kaiba.lib.base.domain.applego.lego.LegoPageModel;
import com.kaiba.m.core.domain.applego.LegoPage;
import com.kaiba.m.core.middleware.instantcache.KbInstantCaffeineService;
import lombok.EqualsAndHashCode;
import lombok.extern.slf4j.Slf4j;
import org.checkerframework.checker.nullness.qual.NonNull;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.util.Optional;

/**
 * author: lyux
 * date: 2023-08-13
 */
@Slf4j
@Service
public class AppLegoCacheService {

    private final AppLegoService legoService;
    private final AppWidgetService widgetService;

    private final LoadingCache<String, AppLegoModelWrapper> legoByIdCache;
    private final LoadingCache<String, WidgetCacheModel> widgetByIdCache;
    private final LoadingCache<CacheKey, String> idByLatestCache = Caffeine.newBuilder()
            .expireAfterWrite(Duration.ofMinutes(15))
            .refreshAfterWrite(Duration.ofSeconds(61))
            .build(this::getLatestLegoPageId);

    public AppLegoCacheService(
            KbInstantCaffeineService instantCacheService,
            AppLegoService legoService,
            AppWidgetService widgetService
    ) {
        this.legoService = legoService;
        this.widgetService = widgetService;
        this.legoByIdCache = instantCacheService.registerInstantCache("app_lego_page_by_id",
                Caffeine.newBuilder()
                        .expireAfterWrite(Duration.ofMinutes(30))
                        .refreshAfterWrite(Duration.ofSeconds(201))
                        .build(legoByIdCacheLoader));
        this.widgetByIdCache = instantCacheService.registerInstantCache("app_lego_widget_by_id",
                Caffeine.newBuilder()
                        .expireAfterWrite(Duration.ofMinutes(15))
                        .refreshAfterWrite(Duration.ofSeconds(61))
                        .build(this::getWidgetsByPageFromDB));
    }

    public Optional<LegoPageModel> getOnlineLegoBySceneOrDefault(Integer siteId, String scene) {
        AppComponentState state = AppComponentState.ONLINE;
        Optional<LegoPageModel> op = getLegoByScene(siteId, scene, state);
        if (op.isPresent()) {
            return op;
        } else {
            return getLegoByScene(KbProperties.PLATFORM_SITE_ID, scene, state);
        }
    }

    public Optional<LegoPageModel> getLegoByScene(Integer siteId, String scene, AppComponentState state) {
        CacheKey cacheKey = new CacheKey(siteId, scene, state);
        String pageId = idByLatestCache.get(cacheKey);
        if (pageId == null || pageId.equals(FAKE_PAGE_ID)) {
            return Optional.empty();
        }
        return getLegoByPageId(pageId);
    }

    public Optional<LegoPageModel> getLegoByPageId(String pageId) {
        AppLegoModelWrapper wrapper = legoByIdCache.get(pageId);
        if (wrapper == null || wrapper.isEmpty()) {
            return Optional.empty();
        } else {
            WidgetCacheModel widgetCache = this.widgetByIdCache.get(pageId);
            if (!wrapper.isWidgetIdAttached) {
                wrapper.attachWidget(widgetCache);
            }
            LegoPageModel model = Mapper.map(wrapper.model, LegoPageModel.class);
            return Optional.of(model);
        }
    }

    public WidgetCacheModel getOnlineWidgetDataBySceneOrDefault(Integer siteId, String scene) {
        AppComponentState state = AppComponentState.ONLINE;
        WidgetCacheModel widgets = getWidgetDataByScene(siteId, scene, state);
        if (widgets.isEmpty()) {
            return getWidgetDataByScene(KbProperties.PLATFORM_SITE_ID, scene, state);
        } else {
            return widgets;
        }
    }

    public WidgetCacheModel getWidgetDataByScene(Integer siteId, String scene, AppComponentState state) {
        CacheKey cacheKey = new CacheKey(siteId, scene, state);
        String pageId = idByLatestCache.get(cacheKey);
        return getWidgetDataByPageId(pageId);
    }

    public WidgetCacheModel getWidgetDataByPageId(String pageId) {
        if (pageId == null || pageId.equals(FAKE_PAGE_ID)) {
            return new WidgetCacheModel();
        }
        AppLegoModelWrapper wrapper = legoByIdCache.get(pageId);
        if (wrapper == null) {
            return new WidgetCacheModel();
        } else {
            return widgetByIdCache.get(pageId);
        }
    }

    public void invalidate(Integer siteId, String scene, AppComponentState state) {
        CacheKey cacheKey = new CacheKey(siteId, scene, state);
        legoByIdCache.invalidate(cacheKey);
        widgetByIdCache.invalidate(cacheKey);
    }

    // --------------------------------------------------------------------

    private String getLatestLegoPageId(CacheKey key) {
        return legoService
                .getLatestPageBySiteIdAndScene(key.siteId, key.scene, key.state.getValue())
                .map(LegoPage::getId)
                .orElse(FAKE_PAGE_ID);
    }

    private WidgetCacheModel getWidgetsByPageFromDB(String pageId) {
        AppLegoModelWrapper wrapper = legoByIdCache.get(pageId);
        if (wrapper == null || wrapper.isEmpty() || wrapper.widgetKeySet == null || wrapper.widgetKeySet.isEmpty()) {
            return new WidgetCacheModel();
        }
        WidgetCacheModel cache = new WidgetCacheModel(wrapper.widgetKeySet.size());
        for (AppLegoModelWrapper.WidgetKey widgetKey : wrapper.widgetKeySet) {
            LegoBlockModel block = widgetKey.getBlock();
            widgetService.getWidgetDataByBlockId(block.getId(), widgetKey.getType()).ifPresent(cache::addWidget);
        }
        return cache;
    }

    private final CacheLoader<String, AppLegoModelWrapper> legoByIdCacheLoader = new CacheLoader<String, AppLegoModelWrapper>() {
        @Override
        public AppLegoModelWrapper load(@NonNull String pageId) {
            return legoService
                    .getPageById(pageId)
                    .map(legoService::getPageModelByPage)
                    .orElseGet(AppLegoModelWrapper::new);
        }

        @Override
        public AppLegoModelWrapper reload(@NonNull String pageId, @NonNull AppLegoModelWrapper oldValue) {
            AppLegoModelWrapper data = load(pageId);
            return data == null ? oldValue : data;
        }
    };

    @EqualsAndHashCode
    private static class CacheKey {
        private final Integer siteId;
        private final String scene;
        private final AppComponentState state;

        public CacheKey(Integer siteId, String scene, AppComponentState state) {
            this.siteId = siteId;
            this.scene = scene;
            this.state = state;
        }
    }

    private static final String FAKE_PAGE_ID = "__fake_page_id__";

}
