package com.kaiba.m.core.service.workorder.carsafeguard;

import com.kaiba.lib.base.domain.workorder.WOACLStringData;
import com.kaiba.lib.base.domain.workorder.WOAccess;
import com.kaiba.lib.base.response.KbCode;
import com.kaiba.lib.base.response.KbException;
import com.kaiba.lib.base.util.StringUtils;
import com.kaiba.m.core.model.safeguard.request.CarSafeguardCaseCreateModel;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @version CarSafeguardUtils, v0.1 2024/7/26 11:47 daopei Exp $
 **/
public class CarSafeguardUtils {

    public static List<WOACLStringData> getACLStringDataListOrThrow(CarSafeguardCaseCreateModel model) {
        List<WOACLStringData> list = new ArrayList<>();
        if (model.getPersonalName() == null || model.getPersonalName().isEmpty()) {
            throw new KbException(KbCode.ILLEGAL_ARGUMENT, "missing personal name").r("请输入您的姓名").li();
        }

        list.add(personalInfoToACLStringData(
                model.getPersonalName(), CarSafeguardConstants.DATA_KEY_PERSONAL_NAME, "姓名"));

        if (model.getPersonalMobile() == null || model.getPersonalMobile().isEmpty()) {
            throw new KbException(KbCode.ILLEGAL_ARGUMENT, "missing personal mobile").r("请输入您的手机").li();
        }
        if (!StringUtils.isValidMobile(model.getPersonalMobile().getData())) {
            throw new KbException(KbCode.ILLEGAL_ARGUMENT, "personal mobile invalid").r("请输入正确的手机号").li();
        }

        list.add(personalInfoToACLStringData(
                model.getPersonalMobile(), CarSafeguardConstants.DATA_KEY_PERSONAL_MOBILE, "手机"));

        return list;
    }

    private static WOACLStringData personalInfoToACLStringData(
            CarSafeguardCaseCreateModel.PersonalInfo info, String key, String name) {
        WOACLStringData model = new WOACLStringData();
        model.setKey(key);
        model.setName(name);
        model.setData(info.getData());
        if (info.getVisible() == null || info.getVisible()) {
            model.setAcl(Collections.singletonList(new WOAccess(true, true)
                    .addWho(WOAccess.Who.byCaseParticipant())));
        } else {
            model.setAcl(Collections.singletonList(new WOAccess(true, true)
                    .addWho(WOAccess.Who.byCaseClient())
                    .addWho(WOAccess.Who.byCaseManager())));
        }
        return model;
    }
}
