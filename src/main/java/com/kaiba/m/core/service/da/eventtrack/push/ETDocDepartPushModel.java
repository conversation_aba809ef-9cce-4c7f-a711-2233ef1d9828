package com.kaiba.m.core.service.da.eventtrack.push;

import lombok.Data;

/**
 * <AUTHOR>
 * @version ETDocDepartPushModel, v0.1 2025/4/11 10:43 daopei Exp $
 **/
@Data
public class ETDocDepartPushModel {

    /** 主键ID */
    private String id;

    /** 部门. 部门是单位的下属组织. */
    private String depart;

    /** 部门名称. */
    private String departName;

    /** 所属单位. */
    private String channel;

    /** 创建时间. yyyyMMdd HH:mm:ss */
    private String createTime;

    /** 修改时间. yyyy-MM-dd HH:mm:ss*/
    private String updateTime;
}
