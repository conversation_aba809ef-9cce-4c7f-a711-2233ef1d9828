package com.kaiba.m.core.service.artmap;

import com.google.common.collect.Lists;
import com.kaiba.lib.base.domain.artmap.PerformanceModel;
import com.kaiba.m.core.domain.artmap.CalendarPerformance;
import com.kaiba.m.core.domain.artmap.Performance;
import com.kaiba.m.core.domain.education.recitation.enums.CommonStatusEnum;
import com.kaiba.m.core.model.artmap.CalenderPerformanceModel;
import com.kaiba.m.core.model.artmap.dto.CalendarPerformQueryDTO;
import com.kaiba.m.core.model.artmap.dto.PerformanceQueryDTO;
import com.kaiba.m.core.repository.artmap.CalendarPerformanceRepository;
import com.kaiba.m.core.repository.artmap.PerformanceRepository;
import com.kaiba.m.core.service.artmap.mapper.ArtMapServiceMapping;
import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.stereotype.Service;

/**
 * Description: 演艺演出查询Service
 * Author: ZM227
 * Date: 2025/6/13 9:55
 */
@Service
public class PerformanceQueryService {

    @Resource
    private PerformanceRepository performanceRepository;
    @Resource
    private ArtMapServiceMapping artMapServiceMapping;
    @Resource
    private CalendarPerformanceRepository calendarPerformanceRepository;

    public List<CalenderPerformanceModel> queryFrontPerformanceList() {
        List<CalenderPerformanceModel> resList = Lists.newLinkedList();
        PerformanceQueryDTO queryDTO = new PerformanceQueryDTO();
        queryDTO.setPerformanceDateStart(
            LocalDate.now().atStartOfDay(ZoneId.systemDefault()).toInstant());
        queryDTO.setPerformanceDateEnd(
            LocalDate.now().plusDays(7).atStartOfDay(ZoneId.systemDefault()).toInstant());
        Page<Performance> page = performanceRepository.queryPerformanceList(queryDTO);
        // 生成Map_1，kv分别是performanceCode和performance
        Map<String, Performance> performanceMap = page.getContent().stream()
            .collect(Collectors.toMap(
                Performance::getPerformanceCode,
                performance -> performance,
                (existing, replacement) -> replacement
            ));
        List<String> performanceCodeList = page.getContent().stream()
            .map(Performance::getPerformanceCode)
            .collect(Collectors.toList());
        CalendarPerformQueryDTO calendarQueryDTO = new CalendarPerformQueryDTO();
        calendarQueryDTO.setPerformanceDateStart(
            LocalDate.now().atStartOfDay(ZoneId.systemDefault()).toInstant());
        calendarQueryDTO.setPerformanceDateEnd(
            LocalDate.now().plusDays(7).atStartOfDay(ZoneId.systemDefault()).toInstant());
        calendarQueryDTO.setPerformanceCodes(performanceCodeList);
        Page<CalendarPerformance> calendarPerformanceList = calendarPerformanceRepository.queryCalendarPerformanceListCodeAndDateGroup(
            calendarQueryDTO);
        // 遍历calendarPerformanceList,以日历维度performanceDate聚合成Map,kv分别是performanceDate和CalendarPerformance的List
        Map<LocalDate, List<CalendarPerformance>> calendarPerformanceMap = calendarPerformanceList.stream()
            .collect(Collectors.groupingBy(
                cal -> cal.getPerformanceDate().atZone(ZoneId.systemDefault()).toLocalDate()));
        LocalDate today = LocalDate.now();
        // 7天占位并填充数据
        for (int i = 0; i < 7; i++) {
            if (resList.size() >= 7) {
                break;
            }
            CalenderPerformanceModel model = new CalenderPerformanceModel();
            model.setPlayingTime(
                today.atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli());
            if (calendarPerformanceMap.containsKey(today)) {
                List<PerformanceModel> performances = calendarPerformanceMap.get(today).stream()
                    .filter(cal -> performanceMap.containsKey(cal.getPerformanceCode()))
                    .map(cal -> artMapServiceMapping.performanceDomainToModel(cal, performanceMap.get(cal.getPerformanceCode())))
                    .sorted(Comparator
                        .comparing(PerformanceModel::getSeq, Comparator.nullsLast(Comparator.naturalOrder()))
                        .thenComparing(
                            PerformanceModel::getPerformanceStartTime,
                            Comparator.nullsLast(Comparator.reverseOrder())
                        )
                    )
                    .limit(6)
                    .collect(Collectors.toList());
                model.setPerformances(performances);
            }
            resList.add(model);
            // 继续填充下一天的数据
            today = today.plusDays(1);
        }
        return resList;
    }

    public Page<PerformanceModel> queryPerformanceList(CalendarPerformQueryDTO queryDTO) {
        Page<CalendarPerformance> page = calendarPerformanceRepository.queryCalendarPerformanceListCodeAndDateGroup(
            queryDTO);
        if (CollectionUtils.isEmpty(page.getContent())) {
            return new PageImpl<>(Collections.emptyList(), page.getPageable(),
                page.getTotalElements());
        }
        // 再查询出基本信息
        List<String> performanceCodeList = page.getContent().stream()
            .map(CalendarPerformance::getPerformanceCode).collect(Collectors.toList());
        Map<String, Performance> performanceList = performanceRepository.findAllByPerformanceCodeInAndStatusIs(
            performanceCodeList, CommonStatusEnum.VALID.getCode()).stream().collect(
            Collectors.toMap(Performance::getPerformanceCode, Function.identity(),
                (existing, replacement) -> replacement));
        return page.map(calendarPerformance -> artMapServiceMapping.performanceDomainToModel(
            calendarPerformance, performanceList.get(calendarPerformance.getPerformanceCode())));
    }

    public Page<PerformanceModel> queryVenuesPerformanceList(CalendarPerformQueryDTO queryDTO) {
        Page<CalendarPerformance> page = calendarPerformanceRepository.queryCalendarPerformanceListCodeGroup(
            queryDTO);
        if (CollectionUtils.isEmpty(page.getContent())) {
            return new PageImpl<>(Collections.emptyList(), page.getPageable(),
                page.getTotalElements());
        }
        // 再查询出基本信息
        List<String> performanceCodeList = page.getContent().stream()
            .map(CalendarPerformance::getPerformanceCode).collect(Collectors.toList());
        Map<String, Performance> performanceList = performanceRepository.findAllByPerformanceCodeInAndStatusIs(
            performanceCodeList, CommonStatusEnum.VALID.getCode()).stream().collect(
            Collectors.toMap(Performance::getPerformanceCode, Function.identity(),
                (existing, replacement) -> replacement));
        return page.map(calendarPerformance -> artMapServiceMapping.performanceDomainToModel(
            calendarPerformance, performanceList.get(calendarPerformance.getPerformanceCode())));
    }

    public Map<Long, Integer> queryRangeCount(Long startTime, Long endTime) {
        CalendarPerformQueryDTO queryDTO = new CalendarPerformQueryDTO();
        queryDTO.setPerformanceDateStart(Instant.ofEpochMilli(startTime));
        queryDTO.setPerformanceDateEnd(Instant.ofEpochMilli(endTime));
        Page<CalendarPerformance> page = calendarPerformanceRepository.queryCalendarPerformanceListCodeAndDateGroup(
            queryDTO);
        // 按照日期分组
        Map<Instant, List<CalendarPerformance>> performanceDateMap = page.getContent().stream()
            // 按LocalDate分组，收集对应的Performance列表
            .collect(Collectors.groupingBy(CalendarPerformance::getPerformanceDate));
        return performanceDateMap.entrySet().stream().collect(
            Collectors.toMap(entry -> entry.getKey().toEpochMilli(),
                entry -> entry.getValue().size()));
    }
}