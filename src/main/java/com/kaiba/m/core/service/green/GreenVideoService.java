package com.kaiba.m.core.service.green;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.aliyuncs.DefaultAcsClient;
import com.aliyuncs.IAcsClient;
import com.aliyuncs.exceptions.ClientException;
import com.aliyuncs.green.model.v20180509.VideoSyncScanRequest;
import com.aliyuncs.http.FormatType;
import com.aliyuncs.http.HttpResponse;
import com.aliyuncs.profile.DefaultProfile;
import com.aliyuncs.profile.IClientProfile;
import com.google.gson.internal.LinkedTreeMap;
import com.google.gson.reflect.TypeToken;
import com.kaiba.lib.base.util.GsonUtils;
import com.kaiba.lib.base.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.client.SimpleClientHttpRequestFactory;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import javax.annotation.PostConstruct;
import java.io.UnsupportedEncodingException;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2021/6/15
 */
@Service
@Slf4j
public class GreenVideoService {

    private static final String ALI_GREEN_SUGGESTION_PASS = "pass";
    private static final String ALI_GREEN_SUGGESTION_REVIEW = "review";
    private static final String ALI_GREEN_SUGGESTION_BLOCK = "block";
    private static final String CALLBACK_HOST = "";
    private static final List<String> SCAN_SCENES = Arrays.asList("porn", "terrorism", "ad", "live", "logo");

    @Value("${ali.green.access_key_id}")
    private String accessKeyId;

    @Value("${ali.green.access_key_secret}")
    private String accessKeySecret;

    @Value("${ali.green.region_id}")
    private String accessRegionId;

    private IAcsClient recognitionClient;

    private final RestTemplate restTemplate;

    public GreenVideoService() {
        SimpleClientHttpRequestFactory factory = new SimpleClientHttpRequestFactory();
        factory.setReadTimeout(2000);
        factory.setConnectTimeout(2000);
        this.restTemplate = new RestTemplate(factory);
    }

    @PostConstruct
    public void init() {
        IClientProfile profile = DefaultProfile.getProfile(accessRegionId, accessKeyId, accessKeySecret);
        recognitionClient = new DefaultAcsClient(profile);
    }

    /**
     * todo 目前仅支持七牛云视频
     * url 访问不通、超时、获取不到avinfo.duration（0，600） 则直接返回false
     *
     * @param url
     * @return
     */
    public boolean scan(String url) {
        if (StringUtils.isEmpty(url)) {
            return true;
        }
        //单位：秒
        double duration = 0;

        try {
            String responseString = restTemplate.getForObject(url + "?avinfo", String.class);
            Map<String, Object> res = GsonUtils.getGson().fromJson(responseString, new TypeToken<Map<String, Object>>() {
            }.getType());
            LinkedTreeMap<String, Object> format = (LinkedTreeMap<String, Object>) res.get("format");
            duration = Double.parseDouble(format.getOrDefault("duration", 0).toString());
        } catch (Exception e) {
            log.error("GreenVideoService get avinfo error,error  :" + e);
        }
        if (duration == 0 || duration > 600) {
            return false;
        }

        List<Map<String, Object>> tasks = new ArrayList<>();
        Map<String, Object> task = new LinkedHashMap<>();
        task.put("dataId", UUID.randomUUID().toString());
        List<Map<String, Object>> frames = new ArrayList<>();

        int i = 1;
        while (i <= duration) {
            Map<String, Object> frame = new LinkedHashMap<>();
            frame.put("offset", i);
            frame.put("url", url + "?vframe/jpg/offset/" + i);
            frames.add(frame);
            i++;
        }

        task.put("frames", frames);
        tasks.add(task);

        JSONObject data = new JSONObject();
        data.put("bizType","default");
        data.put("scenes", SCAN_SCENES);
        data.put("tasks", tasks);

        HttpResponse httpResponse;
        try {
            httpResponse = recognitionClient.doAction(createVideoSyncScanRequest(data));
        } catch (ClientException | UnsupportedEncodingException e) {
            log.error("GreenVideoService video scan fail for " + url + " due to exception", e);
            return false;
        }
        if (!httpResponse.isSuccess()) {
            log.error("response not success. status:" + httpResponse.getStatus());
        }
        JSONObject scrResponse = JSON.parseObject(org.apache.commons.codec.binary.StringUtils.newStringUtf8(httpResponse.getHttpContent()));

        int requestCode = scrResponse.getIntValue("code");
        JSONArray taskResults = scrResponse.getJSONArray("data");
        if (200 != requestCode) {
            log.error("the whole video scan request failed. response:" + JSON.toJSONString(scrResponse));
            return false;
        }

        for (Object taskResult : taskResults) {
            int taskCode = ((JSONObject) taskResult).getIntValue("code");
            JSONArray sceneResults = ((JSONObject) taskResult).getJSONArray("results");
            if (200 == taskCode) {
                for (Object sceneResult : sceneResults) {
                    String scene = ((JSONObject) sceneResult).getString("scene");
                    String suggestion = ((JSONObject) sceneResult).getString("suggestion");
                    //todo 可根据具体情况允许部分场景下的类别通过以提高通过率
                    if (!ALI_GREEN_SUGGESTION_PASS.equals(suggestion)) {
                        log.info(url + "scene = [" + scene + "],suggestion = [" + suggestion + "]");
                        return false;
                    }
                }
            } else {
                log.error("task process fail. task response:" + JSON.toJSONString(taskResult));
                return false;
            }
        }
        return true;
    }

    private VideoSyncScanRequest createVideoSyncScanRequest(JSONObject data) throws UnsupportedEncodingException {
        VideoSyncScanRequest videoSyncScanRequest = new VideoSyncScanRequest();
        videoSyncScanRequest.setAcceptFormat(FormatType.JSON);
        videoSyncScanRequest.setMethod(com.aliyuncs.http.MethodType.POST);
        videoSyncScanRequest.setHttpContent(data.toJSONString().getBytes("UTF-8"), "UTF-8", FormatType.JSON);
        videoSyncScanRequest.setConnectTimeout(3000);
        videoSyncScanRequest.setReadTimeout(10000);
        return videoSyncScanRequest;
    }
}

