package com.kaiba.m.core.service.education.recitation;

import com.alibaba.nacos.common.utils.CollectionUtils;
import com.alibaba.nacos.common.utils.StringUtils;
import com.kaiba.lib.base.domain.education.KidModel;
import com.kaiba.lib.base.response.KbCode;
import com.kaiba.lib.base.response.KbException;
import com.kaiba.lib.base.service.INeoNewsByGroupService;
import com.kaiba.m.core.domain.education.recitation.Member;
import com.kaiba.m.core.domain.education.recitation.enums.AgeTypeEnum;
import com.kaiba.m.core.repository.education.recitation.MemberRepository;
import com.kaiba.m.core.service.education.EducationK12KidService;
import com.kaiba.m.core.service.education.recitation.mapper.RecitationMapping;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import javax.annotation.Resource;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;

/**
 * Description: 成员管理Service
 * Author: ZM227
 * Date: 2024/8/2 10:04
 */
@Service
public class MemberService {

    @Resource
    private MemberRepository memberRepository;
    @Resource
    private MemberQueryService memberQueryService;
    @Resource
    private EducationK12KidService educationK12KidService;
    @Resource
    private RecitationMapping recitationMapping;
    @Resource
    private INeoNewsByGroupService groupService;

    private static final String GROUP_KEY_PREFIX = "education_recitation_member_group_";

    public Member createMember(Member member) {
        long current = System.currentTimeMillis();
        member.setMemberCode(GeneCodeUtils.INSTANCE.geneCode());
        member.setUpdateTime(current);
        member.setCreateTime(current);
        member = memberRepository.insert(member);
        if (StringUtils.isNotBlank(member.getId())) {
            // 插入成功则同步学生信息
            member.setKid(syncEducationKid(member));
            // 插入groupKey信息
            String groupKey = GROUP_KEY_PREFIX + member.getMemberCode();
            groupService.createGroup(groupKey, 9, null, groupKey, null, null, null, null);
            member.setGroupKey(GROUP_KEY_PREFIX + member.getMemberCode());
            updateMember(member);
        }
        return member;
    }

    public Member updateMember(Member member) {
        Member dbMember = memberQueryService.findExistMember(member.getMemberCode(),
            member.getName(), member.getConnectPhone());
        if (Objects.isNull(dbMember)) {
            throw new KbException(KbCode.RESOURCE_NOT_FOUND);
        }
        member.setKid(syncEducationKid(member));
        return memberRepository.updateByMemberCode(member);
    }

    public void updateMemberSortList(List<Member> members) {
        if (CollectionUtils.isEmpty(members)) {
            return;
        }
        for (Member member : members) {
            Member update = new Member();
            update.setMemberCode(member.getMemberCode());
            update.setPinned(member.getPinned());
            update.setSortValue(member.getSortValue());
            memberRepository.updateByMemberCode(update);
        }
    }

    private String syncEducationKid(Member member) {
        if (member.getAgeType() == AgeTypeEnum.ADULT) {
            return null;
        }
        KidModel kidModel = null;
        // 查询研学存在的学生
        if (StringUtils.isNotBlank(member.getKid())) {
            Optional<KidModel> modelOptional = educationK12KidService.getKidById(member.getKid());
            if (modelOptional.isPresent()) {
                kidModel = modelOptional.get();
            }
        } else {
            Page<KidModel> kidModels = educationK12KidService.getKidList(member.getName(),
                member.getConnectPhone(), null, 1, 1);
            // 不存在则新增
            if (!kidModels.isEmpty()) {
                kidModel = kidModels.getContent().get(0);
            }
        }
        // 不存在则新增
        if (Objects.isNull(kidModel)) {
            kidModel = recitationMapping.memberToKidModelAdd(member);
            return educationK12KidService.addKid(kidModel).getId();
        } else {
            // 存在则更新
            recitationMapping.memberToKidModelUpdate(kidModel, member);
            return educationK12KidService.editKid(kidModel).getId();
        }
    }
}
