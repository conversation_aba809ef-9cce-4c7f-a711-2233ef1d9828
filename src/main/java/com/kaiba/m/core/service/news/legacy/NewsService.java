package com.kaiba.m.core.service.news.legacy;

import com.kaiba.lib.base.constant.news.NewsMqEventType;
import com.kaiba.lib.base.constant.news.NewsType;
import com.kaiba.lib.base.domain.news.NewsMqEventModel;
import com.kaiba.lib.base.response.KbCode;
import com.kaiba.lib.base.response.KbException;
import com.kaiba.m.core.domain.news.legacy.News;
import com.kaiba.m.core.domain.news.legacy.NewsDeleted;
import com.kaiba.m.core.domain.news.legacy.NewsRush;
import com.kaiba.m.core.domain.news.legacy.SiteNewsThread;
import com.kaiba.m.core.middleware.amqpsender.NewsEventSender;
import com.kaiba.m.core.repository.news.legacy.NewsDeleteRepository;
import com.kaiba.m.core.repository.news.legacy.NewsRepository;
import com.kaiba.m.core.repository.news.legacy.NewsRushRepository;
import com.kaiba.m.core.repository.news.legacy.SiteNewsThreadRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class NewsService {

    private final NewsCacheService newsCacheService;
    private final NewsRepository newsRepository;
    private final NewsDeleteRepository newsDeleteRepository;
    private final SiteNewsThreadRepository siteNewsThreadRepository;
    private final NewsRushRepository newsRushRepository;

    private final NewsEventSender newsEventSender;

    @Autowired
    public NewsService(
            NewsRepository newsRepository,
            NewsCacheService newsCacheService,
            NewsDeleteRepository newsDeleteRepository,
            SiteNewsThreadRepository siteNewsThreadRepository,
            NewsRushRepository newsRushRepository,
            NewsEventSender newsEventSender
    ) {
        this.newsRepository = newsRepository;
        this.newsCacheService = newsCacheService;
        this.newsDeleteRepository = newsDeleteRepository;
        this.siteNewsThreadRepository = siteNewsThreadRepository;
        this.newsRushRepository = newsRushRepository;
        this.newsEventSender = newsEventSender;
    }

    public News add(News news) {
        news.setIsSign(0);
        news.setIsTop(0);
        news.setViewCount(0);
        long currentTime = System.currentTimeMillis() / 1000;
        news.setCreateTime(currentTime);
        news.setLastUpdateTime(currentTime);
        return newsRepository.save(news);
    }

    public News edit(News news) {
        news.setLastUpdateTime(System.currentTimeMillis() / 1000);
        if (news.getIsSign() == 1 && news.getSignTime() != null) {
            newsCacheService.setSimpleNewsCache(news);
        }
        return newsRepository.save(news);
    }

    public News save(News news) {
        return newsRepository.save(news);
    }

    public void updateChannel(String newsId, String channelKey) {
        newsRepository.updateChannel(newsId, channelKey);
    }

    public void switchComment(String id, Integer open) {
        newsRepository.switchComment(id, open);
    }

    public void switchDisplay(String id, Integer display) {
        News news = newsRepository.switchDisplay(id, display);
        newsCacheService.setSimpleNewsCache(news);
    }

    public void switchCommentBySite(Integer siteId, Integer open) {
        newsRepository.switchCommentBySite(siteId, open);
    }

    public void switchPraise(String id, Integer open) {
        newsRepository.switchPraise(id, open);
    }

    public void switchTop(String id, Integer isTop) {
        newsRepository.findById(id).ifPresent(news -> {
            if (isTop == 1) {
                if (news.getIsSign()!=1){
                    throw new KbException(KbCode.ILLEGAL_STATE,"只能置顶签发后的资讯").setLevel(KbException.LEVEL_INFO);
                }
                Integer count = newsRepository.countBySiteIdAndIsTop(news.getSiteId(), 1);
                if (count >= 10) {
                    throw new KbException(KbCode.NEWS_TOP_FULL).setLevel(KbException.LEVEL_INFO);
                }
            }
            news.setIsTop(isTop);
            newsRepository.save(news);
            newsCacheService.setSimpleNewsCache(news);
        });
    }

    public News sign(String id, Integer userId) {
        News news = newsRepository.findById(id).orElseThrow(
                () -> new KbException(KbCode.RESOURCE_NOT_FOUND).li());
        news.setIsSign(1);
        news.setSignUser(userId);
        news.setSignTime(System.currentTimeMillis() / 1000);
        news.setLastUpdateTime(System.currentTimeMillis() / 1000);
        News updated = newsRepository.save(news);
        newsCacheService.setSimpleNewsCache(news);
        return updated;
    }

    public void cancelSign(String id) {
        newsRepository.findById(id).ifPresent(news -> {
            news.setIsSign(0);
            news.setLastUpdateTime(System.currentTimeMillis() / 1000);
            newsRepository.save(news);
            newsCacheService.delete(news.getSiteId(), news.getId());
        });
    }

    public void praise(String id) {
        newsRepository.praise(id, 1);
    }

    public void praiseMqSend(String id, Integer userId) {
        NewsMqEventModel eventModel = new NewsMqEventModel();
        eventModel.setNewsId(id);
        eventModel.setUserId(userId);
        eventModel.setEventType(NewsMqEventType.NEWS_PRAISE.name());
        newsEventSender.send(eventModel);
    }

    public void attachQuickReplyCategoryToNews(String newsId, String qrCategoryId) {
        newsRepository.attachQuickReplyCategoryToNews(newsId, qrCategoryId);
    }

    public void removeQuickReplyCategoryToNews(String newsId) {
        newsRepository.removeQuickReplyCategoryToNews(newsId);
    }

    public Optional<News> getNewsById(String id) {
        return newsRepository.findById(id);
    }

    public void updateNewsThreadId(String newsId, String threadId) {
        newsRepository.updateNewsThreadId(newsId, threadId);
    }

    public News viewNewsById(String id) {
        News news = newsRepository.viewNews(id);
        if (news != null) {
            newsCacheService.incrViewCount(id);
        }
        return news;
    }

    public void rewardNews(String id) {
        newsRepository.reward(id);
    }

    //TODO 等待二期改造
    public void onPush(String id, Integer userId) {
        newsRepository.onPush(id, userId);
    }

    public Page<News> searchSiteNews(
        Integer siteId, String title, Integer pushed, Integer start, Integer end, Integer isDisplay, Integer type, Integer createUser, String hcrt, String channelKey, Integer page, Integer pageSize
    ) {
        return newsRepository.search(siteId, title, pushed, start, end, isDisplay, type, createUser, hcrt, channelKey,
                PageRequest.of(page - 1, pageSize, Sort.Direction.DESC, "isTop", "createTime"));
    }

    public List<News> getTodayApprovedNewsListBySiteId(Integer siteId) {
        Set<String> idsCache = newsCacheService.getTodaySimpleListBySiteId(siteId);
        Map<String, News> newsMap = new HashMap<>();
        Set<String> missIds = new HashSet<>();
        if (!idsCache.isEmpty()) {
            idsCache.forEach(id -> {
                News news = newsCacheService.getSimpleNews(id);
                if (news == null) {
                    missIds.add(id);
                } else {
                    newsMap.put(id, news);
                }
            });
            if (!missIds.isEmpty()) {
                List<News> newsMissed = newsRepository.findByIdIn(missIds);
                newsMissed.forEach(news -> {
                    newsCacheService.setSimpleNewsCache(news);
                    newsMap.put(news.getId(), news);
                });
            }
        } else {
            Page<News> newsPage = newsRepository.findBySiteIdAndIsSign(siteId, 1, PageRequest.of(0, 15, Sort.Direction.DESC, "isTop", "signTime"));
            newsPage.forEach(news -> {
                newsCacheService.setSimpleNewsCache(news);
                newsMap.put(news.getId(), news);
                idsCache.add(news.getId());
            });
        }
        return idsCache.stream().map(newsMap::get).filter(Objects::nonNull).collect(Collectors.toList());
    }

    public Page<News> getApprovedNewsBySiteId(Integer siteId, Integer page, Integer pageSize) {
        return newsRepository.findBySiteIdAndIsSign(siteId, 1, PageRequest.of(page - 1, pageSize, Sort.Direction.DESC, "isTop", "signTime"));
    }

    public Page<News> getApproveNewsBySiteIdAndType(Integer siteId, NewsType type, Integer page, Integer pageSize) {
        return newsRepository.findBySiteIdAndIsSignAndType(siteId, 1, type.getValue(),
                PageRequest.of(page - 1, pageSize, Sort.Direction.DESC, "isTop", "signTime"));
    }

    public List<String> getRecommendListByType(Integer siteId, Integer type, Integer count) {
        List<Object> results = newsCacheService.getRecommendListBySiteIdAndType(siteId, type, count);
        List<String> list = (List<String>) results.get(0);
        Long expire = (Long) results.get(1);

        if(!list.isEmpty() && !newsCacheService.isExpired(expire)) {
            return list;
        }

        Long signTime = LocalDate.now().minusDays(2).atStartOfDay().atZone(ZoneId.systemDefault()).toEpochSecond();
        List<String> linkList = findRecommendList(siteId, type, 1, 2000, signTime, 0, count);
        if(linkList.size() < count) {
            linkList = findRecommendList(siteId, type, 1, null, signTime, 0, count);
        }

        if(linkList.size() < count) {
            linkList = findRecommendList(siteId, type, 1, null, null, 0, count);
        }

        if(!linkList.isEmpty()) {
            newsCacheService.setRecommendList(siteId, type, linkList, linkList.size() > count);
            return linkList.subList(0, Math.min(count, linkList.size()));
        }
        return Collections.emptyList();
    }

    public List<String> findRecommendList(
        Integer siteId, Integer type, Integer isSign, Integer viewCount, Long signTime, Integer p, Integer ps
    ) {
        return newsRepository.findRecommendList(siteId, type, isSign, viewCount, signTime,
                PageRequest.of(p, ps, Sort.Direction.DESC, "viewCount"))
            .stream().map(News::getId).collect(Collectors.toList());
    }

    public List<News> getNewsListByThreadIdIn(Collection<String> threadIds) {
        return newsRepository.findByThreadIdIn(threadIds);
    }

    public List<News> getNewsListOrderByUpdateTimeDesc(Integer page, Integer pageSize) {
        int p = page == null ? 0 : page - 1;
        int ps = pageSize == null ? 20 : pageSize;
        return newsRepository.findByOrderByLastUpdateTimeDesc(PageRequest.of(p, ps));
    }

    public void deleteById(String id, Integer userId) {
        newsRepository.findAndDeleted(id).ifPresent(news -> {
            NewsDeleted deleted = new NewsDeleted(news, userId);
            newsDeleteRepository.save(deleted);
            newsCacheService.delete(news.getSiteId(), id);
        });
    }

    public SiteNewsThread createThread(Integer siteId, String threadId, Integer base, Integer multiply) {
        SiteNewsThread thread = new SiteNewsThread();
        thread.setSiteId(siteId);
        thread.setThreadId(threadId);
        thread.setMultiply(multiply);
        thread.setBase(base);
        newsCacheService.setNewsSiteThread(thread);
        return siteNewsThreadRepository.save(thread);
    }

    public SiteNewsThread updateThread(SiteNewsThread thread) {
        newsCacheService.setNewsSiteThread(thread);
        return siteNewsThreadRepository.save(thread);
    }

    public SiteNewsThread createThread(Integer siteId, String threadId) {
        return createThread(siteId, threadId, 0, 1);
    }

    public SiteNewsThread updateSiteNewsThread(SiteNewsThread thread) {
        newsCacheService.setNewsSiteThread(thread);
        return siteNewsThreadRepository.save(thread);
    }

    public Optional<SiteNewsThread> getSiteNewsThreadBySiteId(Integer siteId) {
        SiteNewsThread thread = newsCacheService.getNewsSiteThread(siteId);
        if (thread != null) {
            return Optional.of(thread);
        } else {
            Optional<SiteNewsThread> optional = siteNewsThreadRepository.findBySiteId(siteId);
            optional.ifPresent(newsCacheService::setNewsSiteThread);
            return optional;
        }
    }

    public NewsRush addRushToNews(
        Integer userId, String newsId, String rushId, String title, Long startTime, Long endTime, String cover
    ) {
        NewsRush newsRush = NewsRush.builder()
            .userId(userId)
            .newsId(newsId)
            .rushId(rushId)
            .title(title)
            .startTime(startTime)
            .endTime(endTime)
            .cover(cover)
            .createTime(System.currentTimeMillis())
            .updateTime(System.currentTimeMillis())
            .build();
        return newsRushRepository.saveOrUpdate(newsRush);
    }

    public Optional<NewsRush> getRushByNewsId(String newsId) {
        return newsRushRepository.findFirstByNewsId(newsId);
    }

    public List<News> findAllByUpdateTimeBetween(Long st, Long et, Integer page, Integer pageSize) {
        return newsRepository.findAllByUpdateTimeBetween(st, et, PageRequest.of(page - 1, pageSize));
    }

    public List<News> getListByIdGtOrderAndSiteIdByIdAsc(String startId, Integer siteId, Integer pageSize) {
        return newsRepository.getListByIdGtOrderAndSiteIdByIdAsc(startId, siteId, pageSize);
    }
}