package com.kaiba.m.core.service.replydict;

import com.kaiba.lib.base.domain.replydict.ReplyDictContentModel;
import com.kaiba.lib.base.domain.replydict.ReplyKeyModel;
import com.kaiba.lib.base.response.KbCode;
import com.kaiba.lib.base.response.KbException;
import com.kaiba.m.core.domain.replydict.ReplyContent;
import com.kaiba.m.core.domain.replydict.ReplyKey;
import com.kaiba.m.core.repository.replydict.ReplyContentRepository;
import com.kaiba.m.core.repository.replydict.ReplyKeyRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class ReplyDBService {

    private final ReplyContentRepository replyContentRepository;
    private final ReplyKeyRepository replyKeyRepository;

    public ReplyDBService(ReplyContentRepository replyContentRepository, ReplyKeyRepository replyKeyRepository) {
        this.replyContentRepository = replyContentRepository;
        this.replyKeyRepository = replyKeyRepository;
    }

    public ReplyContent createReplyContentByBody(ReplyDictContentModel data) {
        ReplyContent replyContent = new ReplyContent();
        replyContent.setSiteId(data.getSiteId());
        replyContent.setCreateUserId(data.getCreateUserId());
        replyContent.setTitle(data.getTitle());
        replyContent.setLink(data.getLink());
        replyContent.setCover(data.getCover());
        replyContent.setCreateTime(System.currentTimeMillis() / 1000);
        String groupId = data.getGroupId();

        ReplyContent updateResult;
        if (null == groupId) {
            ReplyContent result = replyContentRepository.save(replyContent);
            updateResult =  replyContentRepository.updateGroupId(result.getId(), result.getId());
        } else {
            if (findFirstBySiteIdAndGroupId(replyContent.getSiteId(), groupId).isPresent()) {
                throw new KbException(KbCode.ILLEGAL_ARGUMENT).setReadableMessage("该类型自动回复已存在").li();
            } else {
                replyContent.setGroupId(groupId);
                updateResult = replyContentRepository.save(replyContent);
            }
        }
        return updateResult;
    }

    List<ReplyKey> createKeyModels(String groupId, List<ReplyKeyModel> replyKeys) {
        if (replyKeys != null) {
            return createReplyKeyByBody(replyKeys
                    .stream()
                    .map(replyKeyModel -> model2ReplyKey(replyKeyModel, groupId))
                    .collect(Collectors.toList()), groupId);
        } else {
            return Collections.emptyList();
        }
    }

    public ReplyContent updateReplyContentByBody(ReplyDictContentModel data) {
        ReplyContent replyContent = replyContentRepository.findFirstById(data.getId()).orElseThrow(()->new KbException(KbCode.RESOURCE_NOT_FOUND).li());
        replyContent.setLink(data.getLink());
        replyContent.setTitle(data.getTitle());
        replyContent.setCover(data.getCover());
        replyContent.setUpdateTime(System.currentTimeMillis() / 1000);
        List<ReplyKeyModel> newKeyList = data.getReplyKeyModels();
        Set<String> oldKeyIds = replyKeyRepository.findAllBySiteIdAndGroupIdOrderByCreateTimeDesc(replyContent.getSiteId(), replyContent.getGroupId(),createPageable(1,100))
                .stream()
                .map(ReplyKey::getId)
                .collect(Collectors.toSet());
        List<String> keyIds = new ArrayList<>();
        List<ReplyKey> newReplyKeys = new ArrayList<>();
        for (ReplyKeyModel replyKeyModel : newKeyList) {
            if (null == replyKeyModel.getId()) {
                ReplyKey replyKey = new ReplyKey();
                replyKey.setCreateTime(System.currentTimeMillis() / 1000);
                replyKey.setGroupId(replyContent.getGroupId());
                replyKey.setKey(replyKeyModel.getKey());
                replyKey.setCreateUserId(data.getCreateUserId());
                replyKey.setSiteId(replyContent.getSiteId());
                newReplyKeys.add(replyKey);
            } else {
                keyIds.add(replyKeyModel.getId());
            }
        }
        replyKeyRepository.insert(newReplyKeys);
        List<String> deleteIds = new ArrayList<>();
        for (String oldId : oldKeyIds) {
            if (!keyIds.contains(oldId)) {
                deleteIds.add(oldId);
            }
        }
        ReplyContent result = replyContentRepository.updateReplyContent(replyContent);
        replyKeyRepository.deleteByIdIn(deleteIds);
        return result;
    }

    public void deleteKeyByIdIn(List<String> ids) {
        replyKeyRepository.deleteByIdIn(ids);
    }

    long getKeyCountBySiteId(Integer siteId) {
        return replyKeyRepository.countBySiteId(siteId);
    }

    private static ReplyKey model2ReplyKey(ReplyKeyModel replyKeyModel, String groupId) {
        ReplyKey replyKey = new ReplyKey();
        replyKey.setSiteId(replyKeyModel.getSiteId());
        replyKey.setKey(replyKeyModel.getKey());
        replyKey.setGroupId(groupId);
        replyKey.setCreateTime(replyKeyModel.getCreateTime());
        replyKey.setCreateUserId(replyKeyModel.getCreateUserId());
        return replyKey;
    }

    private List<ReplyKey> createReplyKeyByBody(List<ReplyKey> replyKeys, String groupId) {
        if (replyKeys == null || replyKeys.size() == 0) {
            return Collections.emptyList();
        }
        for (ReplyKey replyKey : replyKeys) {
            if (replyKey.getId() != null) {
                throw new KbException(KbCode.ILLEGAL_ARGUMENT, "update not allowed here, id should be null: " + replyKey);
            }
            long current = System.currentTimeMillis() / 1000;
            replyKey.setCreateTime(current);
            replyKey.setGroupId(groupId);
            ReplyValidator.validateReplyKey(replyKey);
        }
        List<ReplyKey> result = replyKeyRepository.insert(replyKeys);
        log.info("reply key list created: " + result);
        return result;
    }

    public ReplyKey createReplyKey(ReplyKey replyKey) {
        replyKey.setCreateTime(System.currentTimeMillis() / 1000);
        return replyKeyRepository.save(replyKey);
    }

    public List<ReplyKey> createReplyKeyList(List<ReplyKey> replyKeys) {
        return replyKeyRepository.saveAll(replyKeys);
    }

    public void deleteReplyKeyById(String id) {
        replyKeyRepository.deleteById(id);
    }

    public void deleteReplyContentBySiteIdAndGroupId(Integer siteId, String groupId) {
        replyContentRepository.deleteAllBySiteIdAndGroupId(siteId, groupId);
    }

    public Optional<ReplyKey> findReplyKeyById(String id) {
        return replyKeyRepository.findFirstById(id);
    }

    public Optional<ReplyKey> findReplyKeyBySiteIdAndKey(Integer siteId, String key) {
        return replyKeyRepository.findFirstBySiteIdAndKey(siteId, key);
    }

    public Page<ReplyKey> getReplyKeyListBySiteId(Integer siteId, Integer page, Integer pageSize) {
        return replyKeyRepository.findBySiteId(siteId, createPageable(page, pageSize));
    }

    public List<ReplyKey> getReplyKeyListInByGroupIds(Integer siteId, List<String> groupIds) {
        return replyKeyRepository.findBySiteIdAndGroupIdIn(siteId, groupIds);
    }

    public Page<ReplyKey> getReplyKeyListByGroupId(Integer siteId, String groupId) {
        return replyKeyRepository.findAllBySiteIdAndGroupIdOrderByCreateTimeDesc(siteId, groupId,createPageable(1,100));
    }

    public List<ReplyKey> getReplyKeyListInByKeys(Integer siteId, List<String> keys) {
        if (null == keys || keys.size() == 0) {
            return Collections.emptyList();
        }
        return replyKeyRepository.findBySiteIdAndKeyIn(siteId, keys);
    }

    public Page<ReplyKey> getReplyKeyListBySiteIdAndGroupId(Integer siteId, String groupId, Integer page, Integer pageSize) {
        return replyKeyRepository.findBySiteIdAndGroupId(siteId, groupId, createPageable(page, pageSize));
    }

    public boolean existsBySiteIdAndKey(Integer siteId, String key) {
        return replyKeyRepository.existsBySiteIdAndKey(siteId, key);
    }

    public Page<ReplyContent> getReplyContentListBySiteId(Integer siteId, Integer page, Integer pageSize) {
        return replyContentRepository.findBySiteId(siteId, createPageable(page, pageSize));
    }

    public Optional<ReplyContent> findFirstBySiteIdAndGroupId(Integer siteId, String groupId) {
        return replyContentRepository.findFirstBySiteIdAndGroupId(siteId, groupId);
    }

    public Optional<ReplyContent> findReplyContentById(String id) {
        return replyContentRepository.findFirstById(id);
    }

    public void deleteReplyKeyBySiteIdAndGroupId(Integer siteId, String groupId) {
        replyKeyRepository.deleteAllBySiteIdAndGroupId(siteId, groupId);
    }

    public ReplyContent createReplyContent(ReplyContent replyContent) {
        String groupId = replyContent.getGroupId();
        if (null == groupId) {
            ReplyContent content = replyContentRepository.save(replyContent);
            return replyContentRepository.updateGroupId(content.getId(), content.getId());
        } else {
            if (findFirstBySiteIdAndGroupId(replyContent.getSiteId(), groupId).isPresent()) {
                throw new KbException(KbCode.ILLEGAL_ARGUMENT).setReadableMessage("该类型自动回复已存在").li();
            } else {
                return replyContentRepository.save(replyContent);
            }
        }
    }

    public ReplyContent updateReplyContent(ReplyContent replyContent) {
        return replyContentRepository.updateReplyContent(replyContent);
    }

    public ReplyContent updateReplyContentGroupIdById(String id, String groupId) {
        return replyContentRepository.updateGroupId(id, groupId);
    }

    private static Pageable createPageable(Integer page, Integer pageSize) {
        int p = (page == null || page < 1) ? 0 : page - 1;
        int ps = (pageSize == null || pageSize > 100) ? 15 : pageSize;
        return PageRequest.of(p, ps);
    }
}
