package com.kaiba.m.core.service.urlmap;

import com.kaiba.lib.base.constant.KbHttpHeaders;
import lombok.ToString;

import javax.servlet.http.HttpServletRequest;

/**
 * author: lyux
 * date: 20-1-18
 */
@ToString
class PVEvent {

    String key;
    String cid;
    String uid;
    String ip;
    int androidPv;
    int iosPv;
    int wxPv;
    int browserPv;

    static PVEvent createByKey(String key, HttpServletRequest request) {
        PVEvent event = createByRequest(request);
        event.key = key;
        return event;
    }

    static PVEvent createEmpty() {
        return new PVEvent();
    }

    private static PVEvent createByRequest(HttpServletRequest request) {
        PVEvent event = new PVEvent();

        event.cid = KbHttpHeaders.KB_CID.getValidHeaderOrNull(request);
        if (null == event.cid) {
            event.cid = request.getParameter("cid");
        }

        event.uid = KbHttpHeaders.KB_USER_ID.getValidHeaderOrNull(request);
        if (null == event.uid) {
            event.uid = request.getParameter("userId");
        }

        event.ip = request.getHeader("X-FORWARDED-FOR");
        if (null == event.ip) {
            event.ip = request.getRemoteAddr();
        }

        String kb_os = KbHttpHeaders.KB_OS.getValidHeaderOrNull(request);
        if ("Android-Web".equals(kb_os)) {
            event.androidPv = 1;
        } else if ("iOS-Web".equals(kb_os)) {
            event.iosPv = 1;
        } else if ("WX-Web".equals(kb_os)) {
            event.wxPv = 1;
        } else if ("Browser".equals(kb_os)) {
            event.browserPv = 1;
        }

        return event;
    }

    boolean isValid() {
        return key != null;
    }

}
