package com.kaiba.m.core.service.sms;

import com.kaiba.m.core.middleware.conditions.TestEnvironmentCondition;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.context.annotation.Conditional;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

import java.util.Arrays;

/**
 * author: lyux
 * date: 2022-05-12
 *
 * 测试环境跳过验证码验证逻辑
 */
@Slf4j
@Aspect
@Component
@Conditional(TestEnvironmentCondition.class)
public class BypassVCodeAspect {

    private final String profile;

    public BypassVCodeAspect(Environment env) {
        profile = Arrays.stream(env.getActiveProfiles())
                .reduce((a, b) -> a + "," + b).orElse("none");
        log.warn("environment [" + profile + "] will bypass sms code logic !!!!!!!!!!!!!!!");
    }

    @Around("execution(public boolean com.kaiba.m.core.service.sms.SmsService.checkSmsCode(..))")
    private Object aroundCheckSmsCodeMethod(ProceedingJoinPoint joinPoint) {
        Object[] args = joinPoint.getArgs();
        if ("9189".equals(args[1])) {
            log.warn("environment [" + profile + "] bypass check sms code");
            return true;
        } else {
            log.warn("environment [" + profile + "] bypass check sms code but code incorrect");
            return false;
        }
    }

    @Around("execution(public String com.kaiba.m.core.service.sms.SmsService.sendSmsCode(..))")
    public Object aroundSendSmsCodeMethod(ProceedingJoinPoint joinPoint) {
        log.warn("environment [" + profile + "] bypass send sms code");
        return "1234";
    }

}
