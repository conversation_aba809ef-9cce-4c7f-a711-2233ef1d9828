package com.kaiba.m.core.service.artmap;

import com.google.common.collect.Lists;
import com.kaiba.lib.base.domain.news.article.ArticleModel;
import com.kaiba.lib.base.domain.news.pool.ArticleListModel;
import com.kaiba.m.core.domain.artmap.Recommend;
import com.kaiba.m.core.domain.news.pool.bygroup.IdsGroup;
import com.kaiba.m.core.model.artmap.RecommendArticleListModel;
import com.kaiba.m.core.repository.artmap.RecommendRepository;
import com.kaiba.m.core.service.artmap.mapper.ArtMapServiceMapping;
import com.kaiba.m.core.service.news.article.NewsArticleModelHelper;
import com.kaiba.m.core.service.news.pool.bygroup.NewsGroupApiService;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;

/**
 * Description: 艺术地图每日推荐查询Service
 * Author: ZM227
 * Date: 2025/6/6 16:36
 */
@Service
public class RecommendQueryService {

    @Resource
    private RecommendRepository recommendRepository;
    @Resource
    private NewsGroupApiService idsByGroupApiService;
    @Resource
    private ArtMapServiceMapping artMapServiceMapping;

    @Value("${kb.core.art_map.default_recommend}")
    private String defaultRecommendCode;
    @Value("${kb.host.page}")
    String pageHost;

    public Recommend findRecommendByCode(String recommendCode) {
        return recommendRepository.findFirstByRecommendCode(recommendCode);
    }

    /**
     * 获取当天的推荐文章列表，伪随机
     *
     * @return 推荐列表
     */
    public RecommendArticleListModel queryRecommendArticleList() {
        // 查询当天的每日推荐信息，获取分组key
        LocalDateTime current = LocalDateTime.now();
        long currentMillis = current.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();
        Recommend recommend = recommendRepository.findFirstByStartTimeLessThanEqualAndEndTimeGreaterThanEqual(
            currentMillis, currentMillis);
        // 查询列表
        if (Objects.isNull(recommend)) {
            recommend = recommendRepository.findFirstByRecommendCode(defaultRecommendCode);
        }
        RecommendArticleListModel listModel = artMapServiceMapping.recommendDomainToListModel(recommend);
        IdsGroup group = idsByGroupApiService.getGroupByKeyOrThrow(recommend.getGroupKey());
        if (Objects.nonNull(group)) {
            ArticleListModel cached = idsByGroupApiService.getArticleList(group, null, null, 1, 20);
            for (ArticleModel article : cached.getArticles()) {
                NewsArticleModelHelper.assembleArticleAction(pageHost, article, group.getKey());
            }
            listModel.setArticles(sortArticles(cached.getArticles()));
        }
        return listModel;
    }

    public Page<Recommend> queryRecommendList(Integer page, Integer pageSize) {
        return recommendRepository.findAllByOrderByUpdateTimeDesc(PageRequest.of(page - 1, pageSize));
    }

    private List<ArticleModel> sortArticles(List<ArticleModel> articles) {
        // 1. 分离 idx 不为空和 idx 为空的元素
        List<ArticleModel> withIdx = articles.stream()
            .filter(article -> article.getIdx() != null)
            .collect(Collectors.toList());
        List<ArticleModel> withoutIdx = articles.stream()
            .filter(article -> article.getIdx() == null)
            .collect(Collectors.toList());
        // 2. 随机打乱 withoutIdx 列表
        Collections.shuffle(withoutIdx);
        // 3. 合并结果
        List<ArticleModel> result = Lists.newLinkedList(withIdx);
        result.addAll(withoutIdx);
        return result;
    }

}
