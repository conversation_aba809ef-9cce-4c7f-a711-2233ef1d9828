package com.kaiba.m.core.service.issue;

import com.kaiba.lib.base.constant.issue.ExpertApplicationState;
import com.kaiba.lib.base.util.StringUtils;
import com.kaiba.m.core.domain.issue.IssueExpert;
import com.kaiba.m.core.domain.issue.IssueExpertApplication;
import com.kaiba.m.core.domain.issue.IssueExpertFollowee;
import com.kaiba.m.core.repository.issue.IssueExpertApplicationRepository;
import com.kaiba.m.core.repository.issue.IssueExpertFolloweeRepository;
import com.kaiba.m.core.repository.issue.IssueExpertRepository;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

/**
 * author: lyux
 * date: 18-9-27
 */
@Service
public class IssueExpertService {

    private final IssueExpertRepository issueExpertRepository;
    private final IssueExpertFolloweeRepository issueExpertFolloweeRepository;
    private final IssueExpertApplicationRepository issueExpertApplicationRepository;

    public IssueExpertService(
            IssueExpertRepository issueExpertRepository,
            IssueExpertFolloweeRepository issueExpertFolloweeRepository,
            IssueExpertApplicationRepository issueExpertApplicationRepository) {
        this.issueExpertRepository = issueExpertRepository;
        this.issueExpertFolloweeRepository = issueExpertFolloweeRepository;
        this.issueExpertApplicationRepository = issueExpertApplicationRepository;
    }

    public List<IssueExpert> expertListIn(Integer[] expertIdList) {
        return issueExpertRepository.findByIdIn(expertIdList);
    }

    public List<IssueExpert> expertListAll(Integer page, Integer pageSize) {
        return issueExpertRepository.findAll(PageRequest.of(page - 1, pageSize)).getContent();
    }

    public List<IssueExpert> expertListBySpecial(String specialBrand, Integer page, Integer pageSize) {
        if (null == specialBrand) {
            return issueExpertRepository
                    .findAllByOrderByAnswerCountDesc(PageRequest.of(page - 1, pageSize))
                    .getContent();
        } else {
            return issueExpertRepository
                    .findBySpecialBrandOrderByAnswerCountDesc(specialBrand, PageRequest.of(page - 1, pageSize))
                    .getContent();
        }
    }

    public List<IssueExpert> expertListByFollower(Integer userId, Integer page, Integer pageSize) {
        return issueExpertRepository.findByIdIn(issueExpertFolloweeRepository
                .findAllByUserIdOrderByCreateTimeDesc(userId, PageRequest.of(page - 1, pageSize))
                .getContent().stream()
                .map(IssueExpertFollowee::getExpertId)
                .toArray(Integer[]::new));
    }

    public List<IssueExpert> expertListByRecommend() {
        return issueExpertRepository.findByRecommend(true);
    }

    public Page<IssueExpert> expertListPageable(Integer source, Integer level, Pageable pageable) {
        return issueExpertRepository.getList(source, level, pageable);
    }

    public Optional<IssueExpert> expertById(Integer expertId) {
        return issueExpertRepository.findById(expertId);
    }

    public List<IssueExpert> expertByName(String name) {
        return issueExpertRepository.findByUserName(name);
    }

    public IssueExpert expertCreate(IssueExpert expert) {
        return issueExpertRepository.insert(expert);
    }

    public IssueExpert expertUpdate(IssueExpert expert) {
        return issueExpertRepository.updateBasicInfo(expert);
    }

    public void followExpert(Integer expertId, Integer userId) {
        issueExpertFolloweeRepository.follow(expertId, userId);
    }

    public void unfollowExpert(Integer expertId, Integer userId) {
        issueExpertFolloweeRepository.unfollow(expertId, userId);
    }

    public void expertRecommend(Integer expertId, boolean recommend) {
        issueExpertRepository.updateRecommend(expertId, recommend);
    }

    public long followerCount(Integer expertId) {
        return issueExpertFolloweeRepository.countByExpertId(expertId);
    }

    public boolean isUserFollowed(Integer expertId, Integer userId) {
        return issueExpertFolloweeRepository.existsByExpertIdAndUserId(expertId, userId);
    }

    public void updateLogin(Integer expertId) {
        issueExpertRepository.updateLogin(expertId);
    }

    public void updateSummary(Integer expertId,
                              Integer answerCount, Integer praiseCount, Integer followerCount,
                              Double satisfactionRatio, Double speedRatio, Double qualityRatio) {
        issueExpertRepository.updateSummary(expertId,
                answerCount, praiseCount, followerCount,
                satisfactionRatio, speedRatio, qualityRatio);
    }

    // -------------------------------------------------

    public IssueExpertApplication applicationCreate(IssueExpertApplication application) {
        return issueExpertApplicationRepository.save(application);
    }

    public IssueExpertApplication applicationAccept(Integer operatorId, String applicationId) {
        return issueExpertApplicationRepository.updateState(
                operatorId, applicationId, ExpertApplicationState.ACCEPTED.getValue(), null);
    }

    public IssueExpertApplication applicationRefuse(Integer operatorId, String applicationId, String refuseReason) {
        return issueExpertApplicationRepository.updateState(
                operatorId, applicationId, ExpertApplicationState.REFUSED.getValue(), refuseReason);
    }

    public IssueExpertApplication applicationCancel(Integer operatorId, String applicationId) {
        return issueExpertApplicationRepository.updateState(
                operatorId, applicationId, ExpertApplicationState.CANCELED.getValue(), null);
    }

    public Optional<IssueExpertApplication> applicationById(String applicationId) {
        return issueExpertApplicationRepository.findById(applicationId);
    }

    public boolean applicationExistsByUserAndState(Integer userId, ExpertApplicationState state) {
        return issueExpertApplicationRepository.existsByUserIdAndState(userId, state.getValue());
    }

    public boolean applicationExistsByMobileAndState(String mobile, ExpertApplicationState state) {
        return issueExpertApplicationRepository.existsByMobileAndState(mobile, state.getValue());
    }

    public Page<IssueExpertApplication> applicationListByUser(Integer userId, Integer[] states, int page, int pageSize) {
        if (null == states || states.length == 0 || states.length == ExpertApplicationState.values().length) {
            return issueExpertApplicationRepository
                    .findByUserIdOrderByCreateTimeDesc(userId, PageRequest.of(page - 1, pageSize));
        } else {
            return issueExpertApplicationRepository
                    .findByUserIdAndStateInOrderByCreateTimeDesc(userId, states, PageRequest.of(page - 1, pageSize));
        }
    }

    public Page<IssueExpertApplication> applicationListByMobile(String mobile, Integer[] states, int page, int pageSize) {
        if (null == states || states.length == 0 || states.length == ExpertApplicationState.values().length) {
            return issueExpertApplicationRepository
                    .findByMobileOrderByCreateTimeDesc(mobile, PageRequest.of(page - 1, pageSize));
        } else {
            return issueExpertApplicationRepository
                    .findByMobileAndStateInOrderByCreateTimeDesc(mobile, states, PageRequest.of(page - 1, pageSize));
        }
    }

    public Page<IssueExpertApplication> applicationListByMobileOrName(String word, Integer[] states, int page, int pageSize) {
        if (StringUtils.isEmpty(word)) {
            return applicationList(states, page, pageSize);
        } else {
            return issueExpertApplicationRepository
                    .getListByMobileOrUserName(word, states, PageRequest.of(page - 1, pageSize));
        }
    }

    public Page<IssueExpertApplication> applicationList(Integer[] states, int page, int pageSize) {
        if (null == states || states.length == 0 || states.length == ExpertApplicationState.values().length) {
            return issueExpertApplicationRepository
                    .findByOrderByCreateTimeDesc(PageRequest.of(page - 1, pageSize));
        } else {
            return issueExpertApplicationRepository
                    .findByStateInOrderByCreateTimeDesc(states, PageRequest.of(page - 1, pageSize));
        }
    }

}
