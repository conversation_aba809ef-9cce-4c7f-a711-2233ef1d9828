package com.kaiba.m.core.service.education.recitation;

import com.alibaba.nacos.common.utils.Objects;
import com.kaiba.m.core.model.education.recitation.dto.MemberQueryDTO;
import com.kaiba.m.core.domain.education.recitation.Member;
import com.kaiba.m.core.repository.education.recitation.MemberRepository;
import javax.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;

/**
 * Description: 成员查询服务
 * Author: ZM227
 * Date: 2024/8/5 10:21
 */
@Service
public class MemberQueryService {

    @Resource
    private MemberRepository memberRepository;

    public Page<Member> conditionQueryMembers(MemberQueryDTO queryDTO) {
        return memberRepository.findAllByCondition(queryDTO);
    }

    public Member queryMemberDetail(String memberCode) {
        return memberRepository.findMemberByMemberCode(memberCode);
    }

    /**
     * 通过传参查询已存在的成员对象
     *
     * @param memberCode   成员Code
     * @param name         成员姓名，和联系电话配套使用
     * @param connectPhone 联系电话，和成员姓名配套使用
     * @return 成员信息
     */
    public Member findExistMember(String memberCode, String name, String connectPhone) {
        // 先通过逻辑主键查询
        if (StringUtils.isNotBlank(memberCode)) {
            Member dbMember = memberRepository.findMemberByMemberCode(memberCode);
            if (Objects.nonNull(dbMember)) {
                return dbMember;
            }
        }
        // 再通过姓名和联系电话查询
        if (StringUtils.isNotBlank(name) && StringUtils.isNotBlank(connectPhone)) {
            Member dbMember = memberRepository.findMemberByNameAndConnectPhone(name, connectPhone);
            if (Objects.nonNull(dbMember)) {
                return dbMember;
            }
        }
        return null;
    }

}
