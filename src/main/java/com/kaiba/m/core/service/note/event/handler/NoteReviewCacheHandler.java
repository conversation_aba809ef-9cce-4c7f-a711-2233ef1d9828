package com.kaiba.m.core.service.note.event.handler;

import com.kaiba.lib.base.domain.note.NoteModel;
import com.kaiba.lib.base.domain.user.UserModel;
import com.kaiba.m.core.domain.note.Note;
import com.kaiba.m.core.domain.note.NoteReview;
import com.kaiba.m.core.service.note.event.INoteEventReceiver;
import com.kaiba.m.core.service.note.event.NoteCreateOrigin;
import com.kaiba.m.core.service.note.note.NoteReviewCacheService;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @version NoteReviewCacheHandler, v0.1 2023/9/15 10:14 daopei Exp $
 **/
@Slf4j
@Component
@RequiredArgsConstructor
public class NoteReviewCacheHandler implements INoteEventReceiver {

    @NonNull
    private NoteReviewCacheService noteReviewCacheService;

    @Override
    public void onNoteReviewCreated(NoteReview noteReview) {
        try {
            noteReviewCacheService.addByNoteReview(noteReview);
        } catch (Exception e) {
            log.warn("noteReviewCache:note review create fail ,e:{}", e);
        }
    }

    @Override
    public void onNoteCreated(Note note, NoteModel noteModel, UserModel owner, String reviewId, NoteCreateOrigin origin) {
        try {
            noteReviewCacheService.delete(note.getThreads(), note.getUserId(), reviewId);
        } catch (Exception e) {
            log.warn("noteReviewCache:note create to delete note review fail ,e:{}", e);
        }
    }

    @Override
    public void onNoteReviewRefused(NoteReview noteReview) {
        try {
            noteReviewCacheService.delete(noteReview.getThreads(), noteReview.getUserId(), noteReview.getId());
        } catch (Exception e) {
            log.warn("noteReviewCache:note review refused fail ,e:{}", e);
        }
    }
}
