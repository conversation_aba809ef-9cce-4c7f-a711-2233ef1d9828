package com.kaiba.m.core.service.media;

import com.alibaba.fastjson.JSONObject;
import com.aliyun.vod20170321.models.CreateUploadVideoResponseBody;
import com.aliyun.vod20170321.models.GetPlayInfoResponseBody;
import com.aliyun.vod20170321.models.GetVideoInfoResponseBody;
import com.aliyun.vod20170321.models.GetVideoPlayAuthResponseBody;
import com.aliyun.vod20170321.models.RefreshUploadVideoResponseBody;
import com.kaiba.lib.base.assembler.Mapper;
import com.kaiba.lib.base.constant.KbModule;
import com.kaiba.lib.base.constant.media.AliyunMediaState;
import com.kaiba.lib.base.domain.media.AliyunMediaModel;
import com.kaiba.lib.base.domain.media.AliyunMediaUploadResult;
import com.kaiba.lib.base.middleware.adminlogrecorder.AdminLogAction;
import com.kaiba.lib.base.middleware.adminlogrecorder.AdminLogRecorder;
import com.kaiba.lib.base.response.KbCode;
import com.kaiba.lib.base.response.KbException;
import com.kaiba.lib.base.service.IAdminLogService;
import com.kaiba.lib.base.util.GsonUtils;
import com.kaiba.m.core.domain.media.AliyunMedia;
import com.kaiba.m.core.domain.media.AliyunMediaPlayInfo;
import com.kaiba.m.core.repository.media.AliyunMediaPlayInfoRepository;
import com.kaiba.m.core.repository.media.AliyunMediaRepository;
import lombok.NonNull;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.time.Instant;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version AliyunMediaService, v0.1 2024/1/9 17:29 daopei Exp $
 **/
@Service
public class AliyunMediaService {

    @NonNull
    private final AliyunMediaRepository aliyunMediaRepository;
    @NonNull
    private final AliyunMediaPlayInfoRepository playInfoRepository;
    @NonNull
    private final AliyunVODManager aliyunVODManager;
    private final AdminLogRecorder adminLogRecorder;


    public AliyunMediaService(
            @NonNull AliyunMediaRepository aliyunMediaRepository,
            @NonNull AliyunMediaPlayInfoRepository playInfoRepository,
            @NonNull AliyunVODManager aliyunVODManager,
            @NonNull IAdminLogService adminLogService
    ) {
        this.aliyunMediaRepository = aliyunMediaRepository;
        this.playInfoRepository = playInfoRepository;
        this.aliyunVODManager = aliyunVODManager;
        this.adminLogRecorder = AdminLogRecorder
                .builder(adminLogService)
                .module(KbModule.MEDIA)
                .unit(AliyunMediaConstant.ADMIN_UNIT_VIDEO, "媒资管理视频模块操作")
                .create();
    }

    public Page<AliyunMedia> getList(
            String title,
            String mediaId,
            String id,
            String tag,
            Integer state,
            List<String> aliyunStatus,
            Long uploadTimeStart,
            Long uploadTimeEnd,
            Integer creatorId,
            Integer page,
            Integer pageSize
    ) {
        return aliyunMediaRepository.getListByParam(title, mediaId, id, tag, state, aliyunStatus, uploadTimeStart, uploadTimeEnd, creatorId, p(page, pageSize));
    }

    public Optional<AliyunMedia> getMediaById(String id) {
        return aliyunMediaRepository.findById(id);
    }

    public List<AliyunMediaPlayInfo> getMediaPlayInfoByRefId(String refId) {
        return playInfoRepository.findAllByRefId(refId);
    }

    public String getMediaVideoPlayAuth(String id) {
        AliyunMedia media = aliyunMediaRepository.findById(id).orElseThrow(() -> new KbException(KbCode.RESOURCE_NOT_FOUND).li());
        GetVideoPlayAuthResponseBody responseBody = aliyunVODManager.getVideoPlayAuth(media.getMediaId());
        return responseBody.getPlayAuth();
    }

    public Optional<AliyunMedia> createMedia(AliyunMediaModel model) {
        AliyunMediaHelper.create_verify.verify(model);
        CreateUploadVideoResponseBody responseBody =
                aliyunVODManager.createUploadVideo(model.getFileName(), model.getTitle(), null);
        AliyunMedia media = Mapper.map(model, AliyunMedia.class);
        media.setMediaId(responseBody.videoId);
        media.setState(1);
        media.setAliyunStatus(AliyunMediaState.Uploading.name());
        media.setCreateTime(System.currentTimeMillis());
        media = aliyunMediaRepository.save(media);
        adminLogRecorder.on()
                .act(AdminLogAction.CREATE)
                .ref1(media.getId())
                .info(GsonUtils.getGson().toJson(media))
                .add();
        return Optional.of(media);
    }

    public void updateMedia(String id, String title, List<String> tags) {
        AliyunMedia media = aliyunMediaRepository.findById(id).orElseThrow(() -> new KbException(KbCode.RESOURCE_NOT_FOUND).li());
        aliyunVODManager.updateVideo(media.getMediaId(), title, list2String(tags));
        AliyunMedia newMedia = Mapper.map(media, AliyunMedia.class);
        newMedia.setTitle(title);
        newMedia.setTags(tags);
        newMedia.setUpdateTime(System.currentTimeMillis());
        aliyunMediaRepository.save(newMedia);
        adminLogRecorder.on()
                .act(AdminLogAction.UPDATE_DATA, String.format("标题变更:%s -> %s, 标签变更:%s -> %s", media.getTitle(), newMedia.getTitle(), media.getTags(), newMedia.getTags()))
                .ref1(id)
                .add();
    }

    public void deleteMedia(String id) {
        AliyunMedia media = aliyunMediaRepository.findById(id).orElse(null);
        if (media == null) {
            return;
        }
        aliyunVODManager.deleteVideo(media.getMediaId());
        aliyunMediaRepository.updateStateById(id, 0);
        adminLogRecorder.on()
                .act(AdminLogAction.DELETE)
                .ref1(id)
                .snapshot(GsonUtils.getGson().toJson(media))
                .add();
    }


    public AliyunMediaUploadResult refreshToken(String id) {
        AliyunMedia media = aliyunMediaRepository.findById(id).orElseThrow(() -> new KbException(KbCode.RESOURCE_NOT_FOUND).li());
        RefreshUploadVideoResponseBody responseBody = aliyunVODManager.refreshToken(media.getMediaId());
        AliyunMediaUploadResult result = new AliyunMediaUploadResult();
        result.setMediaId(media.getMediaId());
        result.setUploadAddress(responseBody.uploadAddress);
        result.setUploadAuth(responseBody.uploadAuth);
        result.setId(id);
        return result;
    }

    public void submitTranscodeJob(String id, String templateGroupId) {
        AliyunMedia media = aliyunMediaRepository.findById(id).orElseThrow(() -> new KbException(KbCode.RESOURCE_NOT_FOUND).li());
        if (AliyunMediaState.Transcoding.name().equals(media.getAliyunStatus())) {
            throw new KbException(KbCode.ILLEGAL_STATE).r("正常转码中,请稍后尝试").li();
        }
        String transcodeTaskId = aliyunVODManager.submitTranscodeJob(media.getMediaId(), templateGroupId);
        media.setTranscodeTaskId(transcodeTaskId);
        media.setAliyunStatus(AliyunMediaState.Transcoding.name());
        aliyunMediaRepository.save(media);
    }

    public void reloadPlayInfo(String id) {
        AliyunMedia media = aliyunMediaRepository.findById(id).orElseThrow(() -> new KbException(KbCode.RESOURCE_NOT_FOUND).li());
        reloadVideoPlayInfoByAliyunMedia(media.getMediaId());
    }


    public void callback(JSONObject json) {
        String eventType = json.getString("EventType");
        String mediaId = json.getString("VideoId");
        String status = json.getString("Status");
        if (!"success".equals(status)) {
            return;
        }

        if (AliyunMediaConstant.CALLBACK_EVENT_FILE_UPLOAD.equals(eventType)) {
            AliyunMediaFileUploadComplete event = GsonUtils.getGson().fromJson(json.toString(), AliyunMediaFileUploadComplete.class);
            fileUploadComplete(event);
        } else if (AliyunMediaConstant.CALLBACK_EVENT_STREAM_TRANSCODE.equals(eventType)) {
            AliyunMediaStreamTranscodeComplete event = GsonUtils.getGson().fromJson(json.toString(), AliyunMediaStreamTranscodeComplete.class);
            streamTranscodeComplete(event);
        } else if (AliyunMediaConstant.CALLBACK_EVENT_FULL_TRANSCODE.equals(eventType)) {
            reloadVideoPlayInfoByAliyunMedia(mediaId);
        }
    }


    /**
     * 重置阿里云视频播放列表
     *
     * @param mediaId
     */
    private void reloadVideoPlayInfoByAliyunMedia(String mediaId) {
        AliyunMedia media = aliyunMediaRepository.findByMediaId(mediaId).orElseThrow(() -> new KbException(KbCode.RESOURCE_NOT_FOUND).li());
        List<GetPlayInfoResponseBody.GetPlayInfoResponseBodyPlayInfoListPlayInfo> playInfoList =
                aliyunVODManager.getPlayInfo(media.getMediaId());
        if (playInfoList == null || playInfoList.isEmpty()) {
            return;
        }

        List<AliyunMediaPlayInfo> reloadPlayInfoList =
                playInfoList.stream()
                        .map(info -> convertFromAliyunResponse(media.getId(), info))
                        .collect(Collectors.toList());
        //清空播放数据后重新保存
        playInfoRepository.deleteAllByRefId(media.getId());
        playInfoRepository.saveAll(reloadPlayInfoList);
        reloadVideoInfoByAliyunMedia(mediaId);
    }

    private void reloadVideoInfoByAliyunMedia(String mediaId) {
        AliyunMedia media = aliyunMediaRepository.findByMediaId(mediaId).orElseThrow(() -> new KbException(KbCode.RESOURCE_NOT_FOUND).li());
        GetVideoInfoResponseBody.GetVideoInfoResponseBodyVideo videoInfo =
                aliyunVODManager.getVideoInfo(media.getMediaId());
        if (videoInfo == null) {
            return;
        }
        media.setCoverUrl(videoInfo.getCoverURL());
        media.setAliyunStatus(videoInfo.getStatus());
        media.setUploadTime(parseUTC2Timestamp(videoInfo.getCreationTime()));
        media.setSize(videoInfo.getSize());
        media.setDuration(String.valueOf(videoInfo.getDuration()));
        aliyunMediaRepository.save(media);
    }

    /**
     * 文件上传完成事件
     *
     * @param event
     */
    private void fileUploadComplete(AliyunMediaFileUploadComplete event) {
        AliyunMedia media = aliyunMediaRepository.findByMediaId(event.getVideoId()).orElseThrow(() -> new KbException(KbCode.RESOURCE_NOT_FOUND));
        GetVideoInfoResponseBody.GetVideoInfoResponseBodyVideo aliyunVideo = aliyunVODManager.getVideoInfo(event.getVideoId());
        media.setAliyunStatus(aliyunVideo.getStatus());
        media.setSize(event.getSize());
        media.setOriginFileUrl(event.getFileUrl());
        media.setUploadTime(parseUTC2Timestamp(aliyunVideo.getCreationTime()));
        media.setCoverUrl(aliyunVideo.getCoverURL());
        media.setDuration(String.valueOf(aliyunVideo.getDuration()));
        aliyunMediaRepository.save(media);
    }

    /**
     * 单清晰度转码完成事件
     *
     * @param event
     */
    private void streamTranscodeComplete(AliyunMediaStreamTranscodeComplete event) {
        AliyunMedia media = aliyunMediaRepository.findByMediaId(event.getVideoId()).orElseThrow(() -> new KbException(KbCode.RESOURCE_NOT_FOUND));
        AliyunMediaPlayInfo playInfo = Mapper.map(event, AliyunMediaPlayInfo.class);
        playInfo.setRefId(media.getId());
        playInfo.setMediaId(media.getMediaId());
        playInfoRepository.save(playInfo);
    }

    private AliyunMediaPlayInfo convertFromAliyunResponse(
            String refId,
            GetPlayInfoResponseBody.GetPlayInfoResponseBodyPlayInfoListPlayInfo sourcePlayInfo
    ) {
        AliyunMediaPlayInfo playInfo = new AliyunMediaPlayInfo();
        Mapper.map(sourcePlayInfo, playInfo);
        playInfo.setCreationTime(parseUTC2Timestamp(sourcePlayInfo.getCreationTime()));
        playInfo.setRefId(refId);
        playInfo.setFileUrl(sourcePlayInfo.getPlayURL());
        return playInfo;
    }

    /**
     * yyyy-MM-ddTHH:mm:ssZ
     *
     * @param utcTime
     * @return
     */
    private static Long parseUTC2Timestamp(String utcTime) {
        Instant instant = Instant.parse(utcTime);
        ZonedDateTime zonedDateTime = instant.atZone(ZoneId.systemDefault());
        return zonedDateTime.toInstant().toEpochMilli();
    }

    private static String list2String(List<String> list) {
        if (list == null || list.isEmpty()) {
            return null;
        }
        return String.join(",", list);
    }

    private static Pageable p(Integer page, Integer pageSize) {
        int p = page == null ? 0 : page - 1;
        int ps = pageSize == null ? 20 : pageSize;
        return PageRequest.of(p, ps);
    }
}
