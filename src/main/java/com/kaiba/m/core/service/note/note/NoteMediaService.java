package com.kaiba.m.core.service.note.note;

import com.kaiba.lib.base.constant.fuse.media.MediaAssetState;
import com.kaiba.lib.base.constant.note.NoteState;
import com.kaiba.lib.base.domain.fuse.media.MediaAssetModel;
import com.kaiba.lib.base.service.fuse.IMediaAssetService;
import com.kaiba.lib.base.util.StringUtils;
import com.kaiba.m.core.domain.note.Note;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.time.Duration;

/**
 * 帖子媒资处理器
 * <AUTHOR>
 * @version NoteMediaCallbackService, v0.1 2025/6/30 11:04 daopei Exp $
 **/
@Slf4j
@Service
public class NoteMediaService {

    private final NoteService noteService;
    private final IMediaAssetService mediaAssetService;
    private final StringRedisTemplate redisTemplate;

    public NoteMediaService(
            NoteService noteService,
            IMediaAssetService mediaAssetService,
            StringRedisTemplate redisTemplate
    ) {
        this.noteService = noteService;
        this.mediaAssetService = mediaAssetService;
        this.redisTemplate = redisTemplate;
    }


    /**
     * 媒资处理结束后置处理
     * @param mediaId
     */
    public void mediaFinish(String mediaId) {
        String cacheKey = getCacheKey(mediaId);

        String noteId = redisTemplate.opsForValue().get(cacheKey);
        if (noteId == null) {
            return;
        }

        Note note = noteService.getNoteById(noteId).orElse(null);
        if (note == null) {
            return;
        }

        if (note.getState() == NoteState.NORMAL.getValue()) {
            return;
        }

        noteService.noteUpdateState(noteId, NoteState.NORMAL);
        log.info("note media finish, noteId:{}", noteId);
    }

    /**
     * 帖子添加后置处理
     * @param note
     */
    public void noteAddAfter(Note note) {
        MediaAssetModel mediaAsset = mediaAssetService.getByMediaId(note.getMediaId(), false, false).dataOrThrow();
        if (mediaAsset == null) {
            log.warn("NoteMediaHandler.onNoteAdd: mediaAsset is null, mediaId: {}, noteId:{}", note.getMediaId(), note.getId());
            return;
        }

        //媒资已经处理完成 则忽略
        if (StringUtils.isEqual(mediaAsset.getState(), MediaAssetState.NORMAL.name())) {
            return;
        }

        //更新帖子状态，等待帖子视频处理完成
        noteService.noteUpdateState(note.getId(), NoteState.AUTHOR_ONLY);
        //记录帖子媒资关系用于媒资完成后索引帖子更新状态
        noteMediaBind(note.getId(), note.getMediaId());
    }

    /**
     * 帖子绑定媒资关系
     * @param noteId
     * @param mediaId
     */
    public void noteMediaBind(String noteId, String mediaId) {
        if (noteId == null || mediaId == null) {
            return;
        }
        String cacheKey = getCacheKey(mediaId);
        redisTemplate.opsForValue().set(cacheKey, noteId, Duration.ofDays(2));
        log.info("note media bind, noteId:{}, mediaId:{}", noteId, mediaId);
    }




    private String getCacheKey(String mediaId) {
        return "java_core_note_media_finish_" + mediaId;
    }
}
