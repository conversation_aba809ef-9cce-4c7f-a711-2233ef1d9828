package com.kaiba.m.core.service.wx;

import com.kaiba.m.core.domain.wx.WxOpenAppMessage;
import lombok.Builder;
import lombok.Data;
import me.chanjar.weixin.mp.bean.message.WxMpXmlOutMessage;

/**
 * <AUTHOR>
 * @version WxOpenMessageOutWrapper, v0.1 2024/5/30 16:51 daopei Exp $
 **/
@Builder
@Data
public class WxOpenMessageOutWrapper {


    /** 微信自动回复消息 */
    private WxMpXmlOutMessage out;

    /** 电台消息 */
    private WxOpenAppMessage siteMessage;
    /**
     * 电台关键字匹配
     **/
    private boolean siteKeyMatch;
    /**
     * 微信后台关键字匹配
     **/
    private boolean wxKeyMatch;
    /**
     * 电台渠道是否自动回复
     **/
    private boolean siteReply;
    /**
     * 电台渠道是否将消息转为发帖
     **/
    private boolean siteNoteCreate;
}
