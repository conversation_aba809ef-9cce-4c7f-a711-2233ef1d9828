package com.kaiba.m.core.service.wx.interceptor;

import com.google.common.collect.Lists;
import java.util.List;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.common.session.WxSessionManager;
import me.chanjar.weixin.mp.api.WxMpMessageInterceptor;
import me.chanjar.weixin.mp.api.WxMpService;
import me.chanjar.weixin.mp.bean.message.WxMpXmlMessage;
import org.springframework.stereotype.Component;

/**
 * Description: 微信知识库拦截器
 * Author: ZM227
 * Date: 2025/2/11 10:25
 */
@Component
@Slf4j
public class WxKnowledgeInterceptor implements WxMpMessageInterceptor {

    private static final List<String> KNOWLEDGE_MSG = Lists.newArrayList("wx5cd81c729e2e8951");

    @Override
    public boolean intercept(WxMpXmlMessage wxMessage, Map<String, Object> context,
        WxMpService wxMpService, WxSessionManager sessionManager) throws WxErrorException {
        log.info(
            "into knowledge interceptor appId: " + wxMpService.getWxMpConfigStorage().getAppId()
                + "&result:" + KNOWLEDGE_MSG.contains(
                wxMpService.getWxMpConfigStorage().getAppId()));
        return KNOWLEDGE_MSG.contains(wxMpService.getWxMpConfigStorage().getAppId());
    }
}
