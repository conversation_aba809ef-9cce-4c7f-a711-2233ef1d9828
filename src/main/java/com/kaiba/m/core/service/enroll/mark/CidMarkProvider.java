package com.kaiba.m.core.service.enroll.mark;

import com.kaiba.lib.base.constant.enroll.EnrollMarkType;
import com.kaiba.m.core.domain.enroll.EnrollData;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class CidMarkProvider implements IMarkProvider {

    @Override
    public EnrollMarkType getType() {
        return EnrollMarkType.CID;
    }

    @Override
    public String getMark(List<EnrollData> dataList) {
       return dataList.get(0).getCid();
    }

}
