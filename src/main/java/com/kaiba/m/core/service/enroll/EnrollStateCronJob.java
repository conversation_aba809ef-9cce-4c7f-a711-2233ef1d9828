package com.kaiba.m.core.service.enroll;

import com.kaiba.lib.base.constant.enroll.EnrollState;
import com.kaiba.lib.base.response.KbCode;
import com.kaiba.lib.base.response.KbException;
import com.kaiba.m.core.domain.enroll.Enroll;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Component;

import java.util.List;

import static com.kaiba.lib.base.constant.enroll.EnrollState.ACTIVE_STATES;

@Slf4j
@Component
public class EnrollStateCronJob {

    private final EnrollService enrollService;

    public EnrollStateCronJob(EnrollService enrollService) {
        this.enrollService = enrollService;
    }

    // 每隔 10 分钟
    @XxlJob("enroll-state-transition")
    public ReturnT<String> stateTransition(String param) {
        int pageSize = 100;
        for (int i = 1; ; i ++) {
            Page<Enroll> enrolls = enrollService
                    .getEnrollList(ACTIVE_STATES,i, pageSize);
            List<Enroll> enrollList = enrolls.getContent();
            for (Enroll enroll : enrollList) {
                checkEnroll(enroll);
            }
            if (enrolls.isLast()) {
                break;
            }
        }
        return ReturnT.SUCCESS;
    }

    private void checkEnroll(Enroll enroll) {
        try {
            EnrollState state = EnrollState.valueOf(enroll.getState()).orElseThrow(
                    () -> new KbException(KbCode.ILLEGAL_STATE, "wrong state: " + enroll));
            switch (state) {
                case INIT:
                    if (isCurrentAfterTime(enroll.getEndTime())) {
                        enrollService.updateEnrollState(enroll.getId(),EnrollState.CANCEL.getValue());
                    }
                    break;
                case ONLINE:
                    if (isCurrentAfterTime(enroll.getEndTime())) {
                        enrollService.updateEnrollState(enroll.getId(),EnrollState.SEALED.getValue());
                    }
                    break;
                case SEALED:
                    break;
            }
        } catch (Exception e) {
            log.error("traverse and check enroll state, check enroll fail for " + enroll, e);
        }
    }

    private static boolean isCurrentAfterTime(Long timeInSeconds) {
        if (null == timeInSeconds) {
            return false;
        }
        long current = System.currentTimeMillis() / 1000;
        return current > timeInSeconds;
    }
}
