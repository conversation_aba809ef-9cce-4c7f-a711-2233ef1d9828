package com.kaiba.m.core.service.appcomponent.actionholder;

import com.github.benmanes.caffeine.cache.CacheLoader;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.LoadingCache;
import com.kaiba.lib.base.assembler.Mapper;
import com.kaiba.lib.base.response.KbCode;
import com.kaiba.lib.base.response.KbException;
import com.kaiba.m.core.domain.appactionholder.AppActionHolder;
import com.kaiba.m.core.repository.appcomponent.actionholder.AppActionHolderRepository;
import lombok.extern.slf4j.Slf4j;
import org.checkerframework.checker.nullness.qual.NonNull;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.util.*;
import java.util.stream.Collectors;

/**
 * author: lyux
 * date: 2023-06-13
 */
@Slf4j
@Service
public class AppActionHolderService {

    private final AppActionHolderRepository actionHolderRepository;

    public AppActionHolderService(AppActionHolderRepository actionHolderRepository) {
        this.actionHolderRepository = actionHolderRepository;
    }

    public AppActionHolder createHolder(AppActionHolder holder) {
        verifyHolder(holder);
        if (actionHolderRepository.existsByKey(holder.getKey())) {
            throw new KbException(KbCode.RESOURCE_ALREADY_EXIST, "key already exists: " + holder.getKey())
                    .r("key已存在").li();
        }
        long now = System.currentTimeMillis();
        holder.setCreateTime(now);
        holder.setUpdateTime(now);
        return actionHolderRepository.insert(holder);
    }

    public AppActionHolder updateHolderData(AppActionHolder data) {
        AppActionHolder tobeUpdate = actionHolderRepository.findById(data.getId())
                .orElseThrow(() -> new KbException(KbCode.RESOURCE_NOT_FOUND).li());
        Mapper.map(data, tobeUpdate);
        verifyHolder(tobeUpdate);
        tobeUpdate.setActionMD5(tobeUpdate.generateActionMD5());
        tobeUpdate.setUpdateTime(System.currentTimeMillis());
        return actionHolderRepository.save(tobeUpdate);
    }

    public void deleteHolderById(String id) {
        AppActionHolder holder = actionHolderRepository.findById(id)
                .orElseThrow(() -> new KbException(KbCode.RESOURCE_NOT_FOUND).li());
        actionHolderRepository.deleteById(id);
    }

    public Optional<AppActionHolder> getCachedHolderByKey(String key) {
        AppActionHolder act = holderByKeyCache.get(key);
        return act == null || act.getId() == null ? Optional.empty() : Optional.of(act);
    }

    public Map<String, AppActionHolder> getCachedHolderMapByKeys(Collection<String> keys) {
        return holderByKeyCache.getAll(keys);
    }

    public List<AppActionHolder> getCachedHolderListByKeys(Collection<String> keys) {
        return new ArrayList<>(holderByKeyCache.getAll(keys).values());
    }

    public Optional<AppActionHolder> getHolderById(String id) {
        return actionHolderRepository.findById(id);
    }

    public Optional<AppActionHolder> getHolderByKey(String key) {
        return actionHolderRepository.findFirstByKey(key);
    }

    public List<AppActionHolder> getHolderListByKeys(Collection<String> keys) {
        return actionHolderRepository.findByKeyIn(keys);
    }

    public Page<AppActionHolder> getHolderPage(Integer page, Integer pageSize) {
        return actionHolderRepository.findByOrderByIdDesc(p(page, pageSize));
    }

    public Page<AppActionHolder> getHolderPageByAction(String action, Integer page, Integer pageSize) {
        return actionHolderRepository.findByActionOrderByIdDesc(action, p(page, pageSize));
    }

    public Page<AppActionHolder> getHolderPageByActionMD5(String actionMD5, Integer page, Integer pageSize) {
        return actionHolderRepository.findByActionMD5OrderByIdDesc(actionMD5, p(page, pageSize));
    }

    public Page<AppActionHolder> searchHolderPage(String keyword, Integer page, Integer pageSize) {
        return actionHolderRepository.search(keyword, p(page, pageSize));
    }

    public boolean isHolderExistsByKey(String key) {
        return actionHolderRepository.existsByKey(key);
    }

    public boolean isHolderExistsByActionMD5(String actionMD5) {
        return actionHolderRepository.existsByActionMD5(actionMD5);
    }

    private void verifyHolder(AppActionHolder holder) {
        if (holder.getKey() == null) {
            throw new KbException(KbCode.ILLEGAL_ARGUMENT).r("请填写 key 值").li();
        }
        if (holder.getName() == null) {
            throw new KbException(KbCode.ILLEGAL_ARGUMENT).r("请填写名称").li();
        }
        holder.validateAction();
        if (actionHolderRepository.existsByKey(holder.getKey())) {
            throw new KbException(KbCode.RESOURCE_ALREADY_EXIST, "key already exists: " + holder.getKey())
                    .r("key 已存在").li();
        }
    }

    private final LoadingCache<String, AppActionHolder> holderByKeyCache = Caffeine.newBuilder()
            .expireAfterWrite(Duration.ofMinutes(3))
            .build(new CacheLoader<String, AppActionHolder>() {
                @Override
                public AppActionHolder load(@NonNull String key) {
                    return actionHolderRepository.findFirstByKey(key).orElseGet(AppActionHolder::new);
                }

                @Override
                public @NonNull Map<@NonNull String, @NonNull AppActionHolder> loadAll(@NonNull Iterable<? extends @NonNull String> keys) throws Exception {
                    Set<String> keySet = new HashSet<>();
                    keys.forEach(keySet::add);
                    return actionHolderRepository.findByKeyIn(keySet).stream()
                            .collect(Collectors.toMap(AppActionHolder::getKey, act -> act, (a1, a2) -> a1));
                }
            });

    private static Pageable p(Integer page, Integer pageSize) {
        int p = (page == null || page < 1) ? 0 : page - 1;
        int ps = (pageSize == null) ? 20 : pageSize;
        return PageRequest.of(p, ps);
    }

}
