package com.kaiba.m.core.service.news.pool.knowledge.impl;

import com.kaiba.lib.base.domain.news.pool.knowledge.KnowledgeCreateModel;
import com.kaiba.lib.base.domain.news.pool.knowledge.KnowledgeModel;
import com.kaiba.lib.base.response.KbCode;
import com.kaiba.lib.base.response.KbException;
import com.kaiba.m.core.domain.knowledge.Knowledge;
import com.kaiba.m.core.domain.news.article.NewsArticle;
import com.kaiba.m.core.event.publisher.KnowledgeModifyPublisher;
import com.kaiba.m.core.repository.news.pool.knowledge.KnowledgeRepository;
import com.kaiba.m.core.service.news.article.NewsArticleService;
import com.kaiba.m.core.service.news.pool.knowledge.IKnowledgeService;
import com.kaiba.m.core.service.news.pool.knowledge.IKnowledgeSyncOpenSearchService;
import com.kaiba.m.core.service.news.pool.knowledge.mapper.KnowledgeMapping;
import java.util.Optional;
import javax.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

/**
 * Description: 知识条目Service层接口实现
 * Author: ZM227
 * Date: 2025/1/2 14:49
 */
@Service
public class KnowledgeServiceImpl implements IKnowledgeService {

    @Resource
    private NewsArticleService newsArticleService;
    @Resource
    private KnowledgeMapping knowledgeMapping;
    @Resource
    private KnowledgeRepository knowledgeRepository;
    @Resource
    private KnowledgeModifyPublisher knowledgeModifyPublisher;

    @Override
    public String addKnowledge(KnowledgeCreateModel createModel) {
        if (StringUtils.isNotBlank(createModel.getArticleId())) {
            Optional<NewsArticle> articleOptional = newsArticleService.getArticleById(
                createModel.getArticleId());
            if (!articleOptional.isPresent()) {
                throw new KbException(KbCode.RESOURCE_NOT_FOUND, "newsArticle not exist").r(
                    "关联文章不存在");
            }
            Knowledge knowledge = knowledgeRepository.insert(
                knowledgeMapping.createModelToDomain(createModel));
            // 发布变更事件
            knowledgeModifyPublisher.publishEvent(knowledge.getId());
            return knowledge.getId();
        } else {
            throw new KbException(KbCode.RESOURCE_NOT_FOUND, "newsArticle not exist").r(
                "关联文章不存在");
        }
    }

    @Override
    public boolean updateKnowledge(KnowledgeModel knowledgeModel) {
        if (StringUtils.isNotBlank(knowledgeModel.getArticleId())
            && !newsArticleService.getArticleById(knowledgeModel.getArticleId()).isPresent()) {
            throw new KbException(KbCode.RESOURCE_NOT_FOUND, "newsArticle not exist").r(
                "关联文章不存在");
        }
        knowledgeRepository.updateById(knowledgeMapping.knowledgeModelToDomain(knowledgeModel));
        // 发布变更事件
        knowledgeModifyPublisher.publishEvent(knowledgeModel.getKnowledgeId());
        return false;
    }

    @Override
    public void plusPopularity(String knowledgeId, int popNum) {
        if (StringUtils.isBlank(knowledgeId) || !knowledgeRepository.findById(knowledgeId)
            .isPresent()) {
            return;
        }
        knowledgeRepository.plusPopularity(knowledgeId, popNum);
    }
}
