package com.kaiba.m.core.util.redis;

import com.kaiba.lib.base.cache.LazyExpireCache;
import lombok.Builder;
import org.springframework.core.task.TaskExecutor;
import org.springframework.data.redis.core.StringRedisTemplate;

import java.time.Duration;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version ObjectHashCache, v0.1 2024/1/3 15:08 daopei Exp $
 **/
@Builder
public class ObjectHashCache<K> {


    private static final String DATA_PLACEHOLDER = "__none__";

    private final StringRedisTemplate stringRedisTemplate;

    /** 主缓存数据KEY */
    private final String cacheKey;
    /** 空缓存数据结构KEY */
    private final String cacheExpiredKey;
    /** 缓存过期时间 */
    private final Duration cacheExpire;
    /** 空缓存数据过期时间(当前时间内只会加载一次) */
    private final Duration cacheNoneExpire;
    /** 数据加载方式为异步时提供的线程池,为空则执行同步 */
    private final TaskExecutor dataProviderExecutor;

    private final Function<K, String> dataProvider;
    private final Function<K, String> dataHashKeyProvider;

    private final LazyExpireCache<Boolean> ttlLocalCache;

    public ObjectHashCache(
            StringRedisTemplate stringRedisTemplate,
            String cacheKey,
            String cacheExpiredKey,
            Duration cacheExpire,
            Duration cacheNoneExpire,
            TaskExecutor dataProviderExecutor,
            Function<K, String> dataProvider,
            Function<K, String> dataHashKeyProvider,
            LazyExpireCache<Boolean> ttlLocalCache
    ) {
        this.stringRedisTemplate = stringRedisTemplate;
        this.cacheKey = cacheKey;
        this.cacheExpiredKey = cacheExpiredKey;
        this.cacheExpire = cacheExpire;
        this.dataProvider = dataProvider;
        this.dataHashKeyProvider = dataHashKeyProvider;
        if (cacheNoneExpire == null) {
            this.cacheNoneExpire = Duration.ofHours(1);
        } else {
            this.cacheNoneExpire = cacheNoneExpire;
        }
        this.dataProviderExecutor = dataProviderExecutor;

        this.ttlLocalCache = new LazyExpireCache.Builder<Boolean>()
                .setExpireTime(Duration.ofHours(1).toMillis())
                .setSupplier(this::checkRedisCacheTTLSet)
                .setDataValidator(Objects::nonNull)
                .create();
    }


    public Map<K, String> getMapByList(List<K> keyList) {
        if (keyList == null || keyList.isEmpty()) {
            return Collections.emptyMap();
        }
        if (!ttlLocalCache.getData()) {
            initCacheExpired();
        }
        List<String> hashValueList = getByList(keyList);
        if (hashValueList.isEmpty()) {
            return Collections.emptyMap();
        }
        //遍历hashValueList找到为匹配的key,重新加载数据
        Map<K, String> keyDataMap = new HashMap<>();
        List<K> cacheMissKey = new ArrayList<>();
        List<K> cacheNoneKey = new ArrayList<>();
        for (int i = 0; i < keyList.size(); i++) {
            K key = keyList.get(i);
            String value = hashValueList.get(i);
            if (value == null) {
                cacheMissKey.add(key);
                continue;
            }
            if (DATA_PLACEHOLDER.equals(value)) {
                cacheNoneKey.add(key);
                continue;
            }
            keyDataMap.put(key, value);
        }

        //已经缓存的空数据查看时间判断是否需要重新刷新
        if (!cacheNoneKey.isEmpty()) {
            List<Long> cacheNoneExpiredList = getExpiredByList(cacheNoneKey);
            if (!cacheNoneExpiredList.isEmpty()) {
                for (int i = 0; i < cacheNoneExpiredList.size(); i++) {
                    K key = cacheNoneKey.get(i);
                    Long expiredTime = cacheNoneExpiredList.get(i);
                    if (expiredTime != null && System.currentTimeMillis() > expiredTime) {
                        cacheMissKey.add(key);
                    }
                }
            }
        }

        //未命中的数据进行缓存
        if (!cacheMissKey.isEmpty()) {
            boolean sync = dataProviderExecutor == null;
            for (K key : cacheMissKey) {
                //同步执行
                if (sync) {
                    String data = loadFromProvider(key);
                    if (data != null) {
                        keyDataMap.put(key, data);
                    }
                } else {
                    //异步查询落缓存
                    dataProviderExecutor.execute(() -> loadFromProvider(key));
                }
            }
        }

        return keyDataMap;
    }

    public Long incr(K key) {
        String hashKey = getHashKey(key);
        String hashValue = (String) stringRedisTemplate.opsForHash().get(cacheKey, hashKey);
        if (DATA_PLACEHOLDER.equals(hashValue) || hashValue == null) {
            return null;
        }
        return stringRedisTemplate.opsForHash().increment(cacheKey, hashKey, 1);
    }

    // -----------------------------

    /**
     * 判断是否已经设置了过期时间
     * @return true:key不存在或者已经设置了过期时间,false:未设置过期时间
     */
    private Boolean checkRedisCacheTTLSet() {
        Long ttl = stringRedisTemplate.getExpire(cacheKey);
        if (ttl == null) {
            return false;
        }
        //key 不存在时返回-2
        if (ttl == -2L) {
            return true;
        }
        //未设置过期时间返回-1
        if (ttl == -1L) {
            return false;
        }
        return true;
    }

    /**
     * 初始化缓存过期时间
     */
    private void initCacheExpired() {
        stringRedisTemplate.expire(cacheKey, cacheExpire);
        stringRedisTemplate.expire(cacheExpiredKey, cacheExpire);
        ttlLocalCache.refresh();
    }

    private String loadFromProvider(K key) {
        String hashKey = getHashKey(key);
        String data = dataProvider.apply(key);
        if (data == null) {
            long cacheNoneExpiredTimeMS = System.currentTimeMillis() + cacheNoneExpire.toMillis();
            stringRedisTemplate.opsForHash().put(cacheKey, hashKey, DATA_PLACEHOLDER);
            stringRedisTemplate.opsForHash().put(cacheExpiredKey, hashKey, Long.toString(cacheNoneExpiredTimeMS));
            return null;
        } else {
            stringRedisTemplate.opsForHash().put(cacheKey, hashKey, data);
            return data;
        }
    }

    private List<String> getByList(List<K> keyList) {
        List<Object> hashKeyL = keyList.stream().map(this::getHashKey).collect(Collectors.toList());
        List<Object> dataList = stringRedisTemplate.opsForHash().multiGet(cacheKey, hashKeyL);
        if (dataList.size() != keyList.size()) {
            return Collections.emptyList();
        }
        return dataList.stream().map(obj -> {
            if (obj == null) {
                return null;
            }
            return (String) obj;
        }).collect(Collectors.toList());
    }

    private List<Long> getExpiredByList(List<K> keyList) {
        List<Object> hashKeyL = keyList.stream().map(this::getHashKey).collect(Collectors.toList());
        List<Object> dataList = stringRedisTemplate.opsForHash().multiGet(cacheExpiredKey, hashKeyL);
        if (dataList.size() != keyList.size()) {
            return Collections.emptyList();
        }
        return dataList.stream().map(obj -> {
            if (obj == null) {
                return null;
            }
            return Long.parseLong((String) obj);
        }).collect(Collectors.toList());
    }

    private String getHashKey(K key) {
        return dataHashKeyProvider.apply(key);
    }


}
