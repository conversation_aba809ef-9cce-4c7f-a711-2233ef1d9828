package com.kaiba.m.core.util.redis;

import java.util.*;
import java.util.stream.Collectors;

/**
 * author: lyux
 * date: 19-6-4
 */
public abstract class AbsObjectListCache<T, ID> {

    public abstract List<T> getListFromDatabase(List<ID> idList);

    public abstract List<T> getListFromCache(List<ID> idList);

    public abstract void updateListToCache(List<T> dataList);

    public abstract ID getIdFromData(T data);

    public abstract boolean isDataValid(T data);

    public List<T> getListIn(List<ID> idList) {
        if (null == idList || idList.isEmpty()) {
            return Collections.emptyList();
        } else {
            // 尝试取缓存. 得到的 cachedList 和原 idList 长度相同, 顺序相同
            List<T> cachedList = getListFromCache(idList);
            List<ID> cacheMissList = null;

            // 检查命中的缓存和未命中的缓存
            Map<ID, T> map = new HashMap<>(idList.size());
            for (int i = idList.size() - 1; i >= 0; i --) {
                T data = cachedList.get(i);
                ID id = idList.get(i);
                if (data == null) {
                    if (cacheMissList == null) cacheMissList = new LinkedList<>();
                    cacheMissList.add(id);
                } else {
                    map.put(id, data);
                }
            }

            // 如果存在未命中的缓存, 则从数据库中取出, 并更新到缓存
            if (cacheMissList != null) {
                List<T> databaseList = getListFromDatabase(cacheMissList);
                updateListToCache(databaseList);
                for (T data : databaseList) {
                    if (isDataValid(data)) {
                        map.put(getIdFromData(data), data);
                    }
                }
            }

            // 以原顺序返回列表
            return idList.stream().map(map::get).collect(Collectors.toList());
        }
    }

}
