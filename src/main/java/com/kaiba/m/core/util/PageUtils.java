package com.kaiba.m.core.util;

import com.kaiba.lib.base.response.KbCode;
import com.kaiba.lib.base.response.KbException;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;

/**
 * <AUTHOR>
 * @version PageUtils, v0.1 2023/7/18 14:46 daopei Exp $
 **/
public class PageUtils {

    /**
     * @param page 从 1 开始
     * @param pageSize 默认为 20, 大于 2000 时会抛出异常
     */
    public static Pageable ofDefault(Integer page,Integer pageSize) {
        return ofDefault(page, pageSize, null);
    }

    /**
     * @param page 从 1 开始
     * @param pageSize 默认为 20, 大于 2000 时会抛出异常
     */
    public static Pageable ofDefault(Integer page, Integer pageSize, Sort sort) {
        int p = page == null ? 0 : page - 1;
        int ps;
        if (pageSize == null) {
            ps = 20;
        } else if (pageSize > 2000) {
            throw new KbException(KbCode.ILLEGAL_ARGUMENT, "page size should not exceed 2000");
        } else {
            ps = pageSize;
        }
        if (sort == null) {
            return PageRequest.of(p, ps);
        } else {
            return PageRequest.of(p, ps, sort);
        }
    }

    public static Pageable ofDefault(Integer pageSize, Sort sort) {
        int ps = pageSize == null ? 20 : pageSize;
        return PageRequest.of(0,ps,sort);
    }

}
