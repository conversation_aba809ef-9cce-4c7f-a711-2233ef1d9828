package com.kaiba.m.core.repository.da.sensors.kanban;

import com.kaiba.m.core.domain.da.sensors.kanban.PlatformDivData;
import com.kaiba.m.core.middleware.mongo.interfaces.KbMongoRepository;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @version PlatformDataRepository, v0.1 2024/3/18 14:25 daopei Exp $
 **/
@Repository
public interface PlatformDataRepository extends KbMongoRepository<PlatformDivData, String> {

    Optional<PlatformDivData> findByConfigIdAndSt(String configId, Long st);

    default List<PlatformDivData> findByConfigIdSt(String configId, Long sts, Long ste) {
        Criteria criteria = Criteria.where("configId").is(configId)
                .and("st").gte(sts).lt(ste);
        Query query = new Query(criteria);
        return mongo().find(query, PlatformDivData.class);
    }


}
