package com.kaiba.m.core.repository.banner;

import com.kaiba.lib.base.constant.banner.BannerState;
import com.kaiba.lib.base.response.KbCode;
import com.kaiba.lib.base.response.KbException;
import com.kaiba.m.core.domain.banner.Banner;
import com.kaiba.m.core.middleware.mongo.interfaces.KbMongoRepository;
import com.kaiba.m.core.middleware.mongo.utils.KbMongoBeanUpdate;
import com.kaiba.m.core.middleware.mongo.utils.KbMongoUpdate;
import org.bson.types.ObjectId;
import org.springframework.data.domain.*;
import org.springframework.data.mongodb.core.FindAndModifyOptions;
import org.springframework.data.mongodb.core.aggregation.*;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Repository;

import java.util.*;

@Repository
public interface BannerRepository extends KbMongoRepository<Banner, String> {

    Optional<Banner> findFirstByModuleAndSiteIdAndStateInAndHomeOrderAndEndTimeGreaterThanAndStartTimeLessThan(
            String module, Integer siteId, List<Integer> states, Integer homeOrder, Long startTime, Long endTime);

    Page<Banner> findAllByStateIn(List<Integer> states, Pageable pageable);

    default Page<Banner> getBannerList(String module, Integer siteId, Integer state, Long startTime, Long endTime,
                                       Pageable pageable) {
        Criteria criteria = new Criteria();
        criteria.and("module").is(module);
        if (null != siteId) {
            criteria.and("siteId").is(siteId);
        }
        if (null != state) {
            criteria.and("state").is(state);
        }
        if (null != startTime) {
            criteria.and("startTime").lte(startTime);
        }
        if (null != endTime) {
            criteria.and("endTime").gte(endTime);
        }
        Query query = new Query(criteria);
        query.with(Sort.by(Sort.Direction.ASC, "homeOrder"));
        query.with(Sort.by(Sort.Direction.DESC, "createTime"));
        long totalPage = mongo().count(query, Banner.class);
        List<Banner> list = mongo().find(query.with(pageable), Banner.class);
        return new PageImpl<>(list, pageable, totalPage);
    }

    default Page<Banner> getBannerList(
            String module, String key, Integer siteId, Integer[] states, Integer homeOrder,
            Long startTime, Long endTime, Pageable pageable) {
        Criteria criteria = new Criteria();
        criteria.and("module").is(module);
        if (null != siteId) {
            criteria.and("siteId").is(siteId);
        }
        if (null != states && states.length != 0) {
            criteria.and("state").in(Arrays.asList(states));
        }
        if (null != startTime) {
            criteria.and("startTime").lte(startTime);
        }
        if (null != endTime) {
            criteria.and("endTime").gte(endTime);
        }
        if (null != key) {
            criteria.and("title").regex(key);
        }
        if (null != homeOrder) {
            criteria.and("homeOrder").is(homeOrder);
        }

        MatchOperation match = new MatchOperation(criteria);

        // 优先根据状态排序，已开始、未开始、待签发，已下线
        Collection<Integer> stateList = Arrays.asList(
                BannerState.ONLINE.getValue(),
                BannerState.SIGNED.getValue(),
                BannerState.PAUSE.getValue(),
                BannerState.INIT.getValue(),
                BannerState.OFFLINE.getValue()
        );
        AddFieldsOperation addFields = Aggregation.addFields()
                .addField("stateSort")
                .withValue(ArrayOperators.arrayOf(stateList).indexOf("$state"))
                .build();

        List<Sort.Order> orderList = new ArrayList<>();
        orderList.add(new Sort.Order(Sort.Direction.ASC, "stateSort"));
        orderList.add(new Sort.Order(Sort.Direction.DESC, "startTime"));
        SortOperation sort = new SortOperation(Sort.by(orderList));

        SkipOperation skip = new SkipOperation(pageable.getOffset());
        LimitOperation limit = new LimitOperation(pageable.getPageSize());
        Aggregation aggregation = Aggregation.newAggregation(match, addFields, sort, skip, limit);

        Query query = new Query(criteria);
        long totalPage = mongo().count(query, Banner.class);
        List<Banner> list = mongo().aggregate(aggregation, "k_banner", Banner.class).getMappedResults();
        
        return new PageImpl<>(list, pageable, totalPage);
    }

    default Banner updateBanner(Banner banner) {
        long current = System.currentTimeMillis();
        return mongo().findAndModify(
                new Query(Criteria
                        .where("_id").is(new ObjectId(banner.getId()))),
                new KbMongoBeanUpdate(banner)
                        .setIfNotEmpty("title")
                        .setIfNotEmpty("action")
                        .setIfNotEmpty("actionParams")
                        .setIfNotEmpty("content")
                        .setIfNotEmpty("mapperKey")
                        .setIfNotEmpty("originalUrl")
                        .setIfNotEmpty("banner")
                        .setIfNotEmpty("bannerNotch")
                        .setIfNotNull("startTime")
                        .setIfNotNull("endTime")
                        .setIfNotNull("updateUserId")
                        .setIfNotNull("autoplayDuration")
                        .setIfNotNull("attrs")
                        .set("updateTime", current),
                new FindAndModifyOptions().returnNew(true).upsert(false),
                Banner.class);
    }

    default Banner updateBannerStateAndHomeOrder(String bannerId, Integer state, Integer homeOrder) {
        if (bannerId == null || state == null) {
            throw new KbException(KbCode.ILLEGAL_ARGUMENT);
        }
        return mongo().findAndModify(
                new Query(Criteria.where("_id").is(new ObjectId(bannerId))),
                new KbMongoUpdate()
                        .setIfNotNull("homeOrder", homeOrder)
                        .set("state", state)
                        .set("updateTime", System.currentTimeMillis()),
                new FindAndModifyOptions().returnNew(true),
                Banner.class);
    }

    default Banner updateBannerHomeOrder(String bannerId, Integer homeOrder) {
        if (bannerId == null || homeOrder == null) {
            throw new KbException(KbCode.ILLEGAL_ARGUMENT);
        }
        long current = System.currentTimeMillis();
        return mongo().findAndModify(
                new Query(Criteria.where("_id").is(new ObjectId(bannerId))),
                new Update().set("homeOrder", homeOrder).set("updateTime", current),
                new FindAndModifyOptions().returnNew(true),
                Banner.class);
    }

}
