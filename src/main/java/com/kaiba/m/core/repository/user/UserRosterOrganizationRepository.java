package com.kaiba.m.core.repository.user;

import com.kaiba.m.core.domain.user.UserRosterOrganization;
import com.kaiba.m.core.middleware.mongo.interfaces.KbMongoRepository;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Repository;

import java.util.Optional;

/**
 * <AUTHOR>
 * @version UserRosterOrganizationRepository, v0.1 2023/11/28 16:17 daopei Exp $
 **/
@Repository
public interface UserRosterOrganizationRepository extends KbMongoRepository<UserRosterOrganization, String> {

    Page<UserRosterOrganization> findByInstanceKeyOrderByCreateTimeDesc(String instanceKey, Pageable pageable);

    Page<UserRosterOrganization> findByInstanceKeyAndStateOrderByCreateTimeDesc(String instanceKey, Integer state, Pageable pageable);

    Optional<UserRosterOrganization> findFirstByNameAndInstanceKey(String name, String instanceKey);

}
