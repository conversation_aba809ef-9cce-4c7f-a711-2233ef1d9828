package com.kaiba.m.core.repository.issue;

import com.kaiba.m.core.domain.issue.IssueLawyer;
import com.kaiba.m.core.middleware.mongo.interfaces.KbMongoRepository;
import com.kaiba.m.core.middleware.mongo.utils.KbMongoBeanUpdate;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.core.FindAndModifyOptions;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * author: lyux
 * date: 18-10-17
 */
@Repository
public interface IssueLawyerRepository extends KbMongoRepository<IssueLawyer, Integer> {

    Page<IssueLawyer> findAllByOrderByAnswerCountDesc(Pageable pageable);

    List<IssueLawyer> findByIdIn(Integer[] expertIdList);

    default void updateLogin(Integer lawyerId) {
        mongo().findAndModify(
                new Query(Criteria.where("_id").is(lawyerId)),
                new Update().set("loginTime", System.currentTimeMillis() / 1000),
                IssueLawyer.class);
    }

    default IssueLawyer updateBasicInfo(IssueLawyer lawyer) {
        return mongo().findAndModify(
                new Query(Criteria.where("_id").is(lawyer.getId())),
                new KbMongoBeanUpdate(lawyer)
                        .setIfNotEmpty("userName")
                        .setIfNotEmpty("avatar")
                        .setIfNotEmpty("mobile")
                        .setIfNotEmpty("source")
                        .setIfNotEmpty("description")
                        .setIfNotEmpty("company")
                        .setIfNotNull("level")
                        .setIfNotEmpty("certificate")
                        .setIfNotEmpty("specialArea"),
                new FindAndModifyOptions().returnNew(true),
                IssueLawyer.class);
    }

}
