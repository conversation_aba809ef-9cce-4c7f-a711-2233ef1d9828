package com.kaiba.m.core.repository.da.getui;

import com.kaiba.m.core.domain.da.getui.GTAFocusContent;
import com.kaiba.m.core.middleware.mongo.interfaces.KbMongoRepository;
import org.bson.types.ObjectId;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.core.BulkOperations;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.data.util.Pair;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version GTAFocusContentRepository, v0.1 2024/12/18 09:54 daopei Exp $
 **/
@Repository
public interface GTAFocusContentRepository extends KbMongoRepository<GTAFocusContent, String> {

    Page<GTAFocusContent> findByState(Integer state, Pageable pageable);

    default void updateIdxBatch(List<GTAFocusContent> contentList) {
        BulkOperations ops = mongo().bulkOps(BulkOperations.BulkMode.ORDERED, GTAFocusContent.class);
        List<Pair<Query, Update>> updates = contentList.stream().
                map(dataIdxModel ->
                        Pair.of(new Query(Criteria.where("_id").is(new ObjectId(dataIdxModel.getId()))),
                                new Update().set("idx", dataIdxModel.getIdx())))
                .collect(Collectors.toList());
        ops.updateMulti(updates);
        ops.execute();
    }

}
