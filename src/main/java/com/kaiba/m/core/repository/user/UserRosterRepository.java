package com.kaiba.m.core.repository.user;

import com.kaiba.m.core.domain.user.UserRoster;
import com.kaiba.m.core.middleware.mongo.interfaces.KbMongoRepository;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.FindAndModifyOptions;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @version UserRosterRepository, v0.1 2023/11/15 18:02 daopei Exp $
 **/
@Repository
public interface UserRosterRepository extends KbMongoRepository<UserRoster, String> {

    List<UserRoster> findAllByInstanceKeyAndState(String instanceKey, Integer state);

    List<UserRoster> findByBindUserIdInAndInstanceKey(List<Integer> bindUserIdList, String instanceKey);

    Optional<UserRoster> findByMobileAndInstanceKey(String mobile, String instanceKey);
    Optional<UserRoster> findByBindUserIdAndInstanceKey(Integer bindUserId, String instanceKey);

    List<UserRoster> findByIdIn(List<String> ids);

    Page<UserRoster> findByInstanceKeyAndBindUserIdNull(String instanceKey, Pageable pageable);


    default Page<UserRoster> getPageByParam(
            String instanceKey,
            Integer userId,
            String userName,
            String mobile,
            Integer state,
            String organizationId,
            Pageable pageable
    ) {
        Criteria criteria = new Criteria();
        criteria.and("instanceKey").is(instanceKey);
        if (userId != null) {
            criteria.and("bindUserId").is(userId);
        }
        if (userName != null) {
            criteria.and("userName").is(userName);
        }
        if (mobile != null) {
            criteria.and("mobile").is(mobile);
        }
        if (state != null) {
            criteria.and("state").is(state);
        }
        if (organizationId != null) {
            criteria.and("organizationId").is(organizationId);
        }
        Query query = Query.query(criteria).with(Sort.by(Sort.Direction.DESC, "createTime"));
        long total = mongo().count(query, UserRoster.class);
        List<UserRoster> list = mongo().find(query.with(pageable), UserRoster.class);
        return new PageImpl<>(list, pageable, total);
    }

    default UserRoster upsert(UserRoster roster) {
        return mongo().findAndModify(
                new Query(Criteria.where("instanceKey").is(roster.getInstanceKey())
                        .and("mobile").is(roster.getMobile())),
                new Update()
                        .set("userName", roster.getUserName())
                        .set("state", 1)
                        .set("updateTime", System.currentTimeMillis())
                        .set("organizationId",roster.getOrganizationId()),
                FindAndModifyOptions.options().upsert(true).returnNew(true),
                UserRoster.class);
    }

    default UserRoster updateState(String id, Integer state) {
        return mongo().findAndModify(
                new Query(Criteria.where("_id").is(id)),
                new Update()
                        .set("state", state)
                        .set("updateTime", System.currentTimeMillis()),
                FindAndModifyOptions.options().upsert(false).returnNew(true),
                UserRoster.class);
    }

    default UserRoster updateVipLevel(String id, Integer vipLevel) {
        return mongo().findAndModify(
                new Query(Criteria.where("_id").is(id)),
                new Update()
                        .set("vipLevel", vipLevel)
                        .set("updateTime", System.currentTimeMillis()),
                FindAndModifyOptions.options().upsert(false).returnNew(true),
                UserRoster.class);
    }
}
