package com.kaiba.m.core.repository.program;

import com.kaiba.lib.base.domain.common.AppVersionRange;
import com.kaiba.m.core.domain.program.BroadcastAudio;
import com.kaiba.m.core.middleware.mongo.interfaces.KbMongoRepository;
import com.kaiba.m.core.middleware.mongo.utils.KbMongoUpdate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface BroadcastAudioRepository extends KbMongoRepository<BroadcastAudio, String> {

    List<BroadcastAudio> findAllBySiteId(Integer siteId);

    Optional<BroadcastAudio> findFirstBySiteIdAndRebroadcastAndIsDefaultIsTrue(Integer siteId, Boolean rebroadcast);

    List<BroadcastAudio> findAllBySiteIdAndRebroadcast(Integer siteId, Boolean rebroadcast);

    default void updateByChannel(String channel, String iOSPlayer, String androidPlayer, List<AppVersionRange> rangeList){
        mongo().updateMulti(
            new Query(Criteria.where("channel").is(channel)),
            new KbMongoUpdate()
                .setIfNotEmpty("iOSPlayer",iOSPlayer)
                .setIfNotEmpty("androidPlayer",androidPlayer)
                .setIfNotEmpty("rangeList",rangeList),
            BroadcastAudio.class
        );
    }

    List<BroadcastAudio> findAllByChannel(String channel);

}
