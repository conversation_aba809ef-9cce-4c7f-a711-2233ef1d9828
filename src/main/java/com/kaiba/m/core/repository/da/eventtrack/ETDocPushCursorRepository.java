package com.kaiba.m.core.repository.da.eventtrack;

import com.kaiba.m.core.domain.da.eventtrack.ETDocPushCursor;
import com.kaiba.m.core.middleware.mongo.interfaces.KbMongoRepository;
import com.kaiba.m.core.middleware.mongo.utils.KbMongoUpdate;
import org.springframework.data.mongodb.core.FindAndModifyOptions;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Repository;

import java.util.Optional;

/**
 * <AUTHOR>
 * @version ETDocPushCursorRepository, v0.1 2025/4/11 15:18 daopei Exp $
 **/
@Repository
public interface ETDocPushCursorRepository extends KbMongoRepository<ETDocPushCursor, String> {

    Optional<ETDocPushCursor> findByMark(String mark);

    default void refreshTime(String mark, Long timestamp) {
        mongo().findAndModify(
                new Query(Criteria.where("mark").is(mark)),
                new KbMongoUpdate()
                        .set("timestamp", timestamp)
                        .set("updateTime", System.currentTimeMillis())
                        .setOnInsert("createTime", System.currentTimeMillis())
                        .setOnInsert("mark", mark),
                FindAndModifyOptions.options().returnNew(true).upsert(true),
                ETDocPushCursor.class
        );
    }
}
