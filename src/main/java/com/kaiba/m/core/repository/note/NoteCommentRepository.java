package com.kaiba.m.core.repository.note;

import com.kaiba.lib.base.constant.note.NoteState;
import com.kaiba.m.core.domain.note.NoteAggrByCommentUserId;
import com.kaiba.m.core.domain.note.NoteComment;
import com.kaiba.m.core.middleware.mongo.interfaces.KbMongoRepository;
import com.mongodb.client.AggregateIterable;
import com.mongodb.client.MongoCollection;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.bson.Document;
import org.bson.types.ObjectId;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Repository;

import java.util.Arrays;
import java.util.Collections;
import java.util.LinkedList;
import java.util.List;

/**
 * author: lyux
 * date: 19-5-28
 */
@Repository
public interface NoteCommentRepository extends KbMongoRepository<NoteComment, String> {

    List<NoteComment> findByOrderByIdDesc(Pageable pageable);

    Page<NoteComment> findByIdLessThanOrderByIdDesc(String commentId, Pageable pageable);

    List<NoteComment> findByIdIn(List<String> noteIds);

    Page<NoteComment> findByNoteIdOrderByCreateTimeDesc(String noteId, Pageable pageable);

    List<NoteComment> getByNoteIdOrderByCreateTimeDesc(String noteId, Pageable pageable);

    Page<NoteComment> findByThreadsInOrderByCreateTimeDesc(String[] threads, Pageable pageable);

    Page<NoteComment> findByThreadsInAndUserIdOrderByCreateTimeDesc(String[] threads, Integer userId, Pageable pageable);

    Page<NoteComment> findByUserIdAndSiteIdOrderByCreateTimeDesc(Integer userId, Integer siteId, Pageable pageable);

    List<NoteComment> getByUserIdAndSiteIdOrderByCreateTimeDesc(Integer userId, Integer siteId, Pageable pageable);

    Page<NoteComment> findByUserIdOrderByCreateTimeDesc(Integer userId, Pageable pageable);

    List<NoteComment> getByUserIdOrderByCreateTimeDesc(Integer userId, Pageable pageable);

    long countByNoteId(String noteId);

    long countByNoteIdAndUserId(String noteId, Integer userId);

    long countByUserId(Integer userId);

    long countByUserIdAndCreateTimeBetween(Integer userId, Long timeStart, Long timeEnd);

    default long getCommentCount() {
        return mongo().getCollection("k_note_comment").estimatedDocumentCount();
    }

    default NoteComment updateSoftDelete(String commentId, boolean deleted) {
        return mongo().findAndModify(
                new Query(Criteria.where("_id").is(new ObjectId(commentId))),
                new Update()
                        .set("isSoftDeleted", deleted)
                        .set("state", deleted ? NoteState.SOFT_DELETE.getValue() : NoteState.NORMAL.getValue()),
                NoteComment.class);
    }

    default NoteComment updateState(String commentId, NoteState state) {
        return mongo().findAndModify(
                new Query(Criteria.where("_id").is(new ObjectId(commentId))),
                new Update().set("state", state.getValue()),
                NoteComment.class);
    }

    default void updateThreads(String noteId, List<String> threads) {
        mongo().findAndModify(
                new Query(Criteria.where("noteId").is(noteId)),
                new Update().set("threads", threads),
                NoteComment.class);
    }

    // -------------------------------------------------------

    /**
     * db.k_note_comment.aggregate([
     *      { $match: { userId: 6969329 } },
     *      { $group: { _id: "$noteId", lastComment: { $last: "$content" } }, lastCommentTime: {$last: "$createTime" }, count: {$sum: 1} },
     *      { $sort: { lastCommentTime: -1 } },
     *      { $skip: 0 },
     *      { $limit: 15 }
     * ])
     *
     * page start from 0.
     */
    default List<NoteAggrByCommentUserId> aggrNoteCommentByUser(Integer userId, Integer page, Integer pageSize) {
        MongoCollection<Document> collection = mongo().getCollection("k_note_comment");
        AggregateIterable<Document> result = collection.aggregate(Arrays.asList(
                new Document("$match",
                        new Document("userId", userId)
                ),
                new Document("$group",
                        new Document("_id", "$noteId")
                                .append("lastComment", new Document("$last", "$content"))
                                .append("lastCommentTime", new Document("$last", "$createTime"))
                                .append("count", new Document("$sum", 1))
                ),
                new Document("$sort", new Document("lastCommentTime", -1)),
                new Document("$skip", page * pageSize),
                new Document("$limit", pageSize)
        ));
        List<NoteAggrByCommentUserId> ret = new LinkedList<>();
        result.forEach(document -> {
            NoteAggrByCommentUserId item = new NoteAggrByCommentUserId();
            item.setNoteId((String) document.get("_id"));
            item.setLastComment((String) document.get("lastComment"));
            item.setLastCommentTime((Long) document.get("lastCommentTime"));
            item.setCommentCount((Integer) document.get("count"));
            ret.add(item);
        });
        return ret;
    }

    /**
     * db.k_note_comment.aggregate([
     *      {$match: {threads: {$in: [xxx, xxx]}, }},
     *      {$group: {_id: "$userId", count: {$sum: 1}}},
     *      {$sort: {count: -1}},
     *      {$facet: {total: [{$count: "total"}], data: [{$skip: 0}, {$limit: 15}]}},
     *      {$project: "total": "$total", "data": "$data"}
     * ])
     */
    default PageCommentAggrModel aggrCommentByThreadId(
            String threadId, Long startTime, Long endTime, Integer page, Integer pageSize) {
        MongoCollection<Document> collection = mongo().getCollection("k_note_comment");
        AggregateIterable<Document> result = collection.aggregate(Arrays.asList(
            new Document("$match",
                new Document("threads", new Document("$in", Collections.singletonList(threadId)))
                    .append("createTime", new Document("$gte", startTime).append("$lte", endTime))
            ),
            new Document("$group",
                new Document("_id", "$userId")
                    .append("count", new Document("$sum", 1))
            ),
            new Document("$sort", new Document("count", -1)),
            new Document("$facet",
                new Document("total", Collections.singletonList(new Document("$count", "total")))
                    .append("data", Arrays.asList(
                        new Document("$skip", page * pageSize),
                        new Document("$limit", pageSize)))
            )
        ));

        PageCommentAggrModel pageData = new PageCommentAggrModel();
        Document doc = result.first();
        List<Document> total = (List<Document>) doc.get("total");
        if (total.size() > 0) {
            pageData.setTotal(total.get(0).getInteger("total"));
            List<Document> data = (List<Document>) doc.get("data");
            pageData.setModels(new LinkedList<>());
            data.forEach(document -> {
                CommentAggrModel model = new CommentAggrModel();
                model.setThreadId(threadId);
                model.setUserId(Integer.parseInt(document.get("_id").toString()));
                model.setCount((Integer) document.get("count"));
                pageData.getModels().add(model);
            });
        }
        return pageData;
    }

    // -------------------------------------------------

    @Data
    @ToString
    @NoArgsConstructor
    class CommentAggrModel {
        private String threadId;
        private Integer userId;
        private Integer count;
    }

    @Data
    @ToString
    @NoArgsConstructor
    class PageCommentAggrModel {
        private List<CommentAggrModel> models;
        private Integer total;
    }
}
