package com.kaiba.m.core.repository.applego;

import com.kaiba.lib.base.constant.applego.WidgetType;
import com.kaiba.m.core.domain.applego.widget.WidgetBannerData;
import com.kaiba.m.core.domain.applego.widget.WidgetHomeRecommendListData;
import com.kaiba.m.core.middleware.mongo.interfaces.KbMongoRepository;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.List;
import java.util.Optional;

/**
 * author: lyux
 * date: 2023-08-11
 */
@Repository
public interface WidgetHomeRecommendListRepository extends
        KbMongoRepository<WidgetHomeRecommendListData, String>, IWidgetRepository<WidgetHomeRecommendListData> {

    @Override
    List<WidgetHomeRecommendListData> findByIdIn(Collection<String> widgetIds);

    @Override
    Optional<WidgetHomeRecommendListData> findFirstByBlockId(String blockId);

    @Override
    default WidgetType getWidgetType() {
        return WidgetType.HOME_RECOMMEND_LIST;
    }
}
