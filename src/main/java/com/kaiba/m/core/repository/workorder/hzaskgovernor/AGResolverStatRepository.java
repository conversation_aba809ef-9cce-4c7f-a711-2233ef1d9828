package com.kaiba.m.core.repository.workorder.hzaskgovernor;

import com.kaiba.m.core.domain.workorder.hzaskgovernor.AGResolverStat;
import com.kaiba.m.core.middleware.mongo.interfaces.KbMongoRepository;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.core.FindAndModifyOptions;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Repository;

import java.util.Optional;

/**
 * author: lyux
 * date: 2024-01-22
 */
@Repository
public interface AGResolverStatRepository extends KbMongoRepository<AGResolverStat, String> {

    String ONLY_ID = "{ '_id':1, 'teamId':1 }";

    Optional<AGResolverStat> findFirstByTeamId(String teamId);

    Page<AGResolverStat> getAllBy(Pageable pageable);

    @org.springframework.data.mongodb.repository.Query(fields = ONLY_ID)
    Page<AGResolverStat> getByRatingCountGreaterThanAndIsInRank(Integer ratingCount, boolean isInRank, Pageable pageable);

    @org.springframework.data.mongodb.repository.Query(fields = ONLY_ID)
    Page<AGResolverStat> getByIsInRankOrderByEventReplyCountDesc(boolean isInRank, Pageable pageable);

    default AGResolverStat upsert(AGResolverStat stat) {
        return mongo().findAndModify(
                new Query(Criteria.where("teamId").is(stat.getTeamId())),
                new Update()
                        .set("teamAbbr", stat.getTeamAbbr())
                        .set("isInRank", stat.getIsInRank())
                        .set("caseResolvedCount", stat.getCaseResolvedCount())
                        .set("caseCount", stat.getCaseCount())
                        .set("eventCount", stat.getEventCount())
                        .set("eventReplyCount", stat.getEventReplyCount())
                        .set("eventBackCount", stat.getEventBackCount())
                        .set("ratingCount", stat.getRatingCount())
                        .set("ratingSum", stat.getRatingSum())
                        .set("ratingAverage", stat.getRatingAverage())
                        .set("updateTime", System.currentTimeMillis()),
                FindAndModifyOptions.options().upsert(true).returnNew(true),
                AGResolverStat.class
        );
    }

}
