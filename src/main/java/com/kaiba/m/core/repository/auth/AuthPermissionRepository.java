package com.kaiba.m.core.repository.auth;

import com.kaiba.lib.base.constant.auth.AuthType;
import com.kaiba.lib.base.util.StringUtils;
import com.kaiba.m.core.domain.auth.AuthPermission;
import com.kaiba.m.core.middleware.mongo.interfaces.KbMongoRepository;
import com.kaiba.m.core.middleware.mongo.utils.KbMongoCriteria;
import org.springframework.data.domain.*;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Repository;

import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * author: lyux
 * date: 19-8-13
 */
@Repository
public interface AuthPermissionRepository extends KbMongoRepository<AuthPermission, String> {

    Optional<AuthPermission> findFirstByPermission(String permission);

    List<AuthPermission> findByPermissionInOrderByPermission(List<String> permissions);

    Page<AuthPermission> findAllByOrderByPermission(Pageable pageable);

    Page<AuthPermission> findByScopeOrderByPermission(Integer scope, Pageable pageable);

    Page<AuthPermission> findByScopeInOrderByPermission(List<Integer> scopes, Pageable pageable);

    Page<AuthPermission> findByScopeAndReferenceIdOrderByPermission(Integer scope, String referenceId, Pageable pageable);

    Page<AuthPermission> findByFormatOrderByPermission(String format, Pageable pageable);

    List<AuthPermission> findByFormatInOrderByPermission(List<String> formats);

    List<AuthPermission> findByScopeAndFormatInOrderByPermission(Integer scope, List<String> formats);

    Page<AuthPermission> findByLevelGreaterThanEqualOrderByPermission(Integer level, Pageable pageable);

    Page<AuthPermission> findByLevelLessThanEqualOrderByPermission(Integer level, Pageable pageable);

    long countByFormat(String format);

    void deleteByPermission(String permission);

    void deleteByFormat(String format);

    default AuthPermission updateAuthTypes(String permission, List<AuthType> authTypeList) {
        List<Integer> authTypes = (null == authTypeList || authTypeList.size() == 0) ?
                Collections.singletonList(AuthType.ANY.getValue()) :
                authTypeList.stream().map(AuthType::getValue).collect(Collectors.toList());
        return mongo().findAndModify(
                new Query(Criteria.where("permission").is(permission)),
                new Update().set("authTypes", authTypes),
                AuthPermission.class);
    }

    default AuthPermission updateLevel(String permission, Integer level) {
        return mongo().findAndModify(
                new Query(Criteria.where("permission").is(permission)),
                new Update().set("level", level),
                AuthPermission.class);
    }

    default AuthPermission updateFields(String permission, Integer level, List<Integer> authTypeList, String description) {
        List<Integer> authTypes = (null == authTypeList || authTypeList.size() == 0) ?
                Collections.singletonList(AuthType.ANY.getValue()) : authTypeList;
        return mongo().findAndModify(
                new Query(Criteria.where("permission").is(permission)),
                new Update()
                        .set("level", level)
                        .set("authTypes", authTypes)
                        .set("description", description),
                AuthPermission.class);
    }

    default Page<AuthPermission> searchForPage(
            Integer scope, String referenceId,
            String permissionRegex, String descriptionRegex,
            Integer page, Integer pageSize) {
        Query query = Query.query(new KbMongoCriteria()
                .criteriaIfNotEmpty("scope", scope)
                .criteriaIfNotEmpty("referenceId", referenceId)
                .regexIfNotEmpty("permission", StringUtils.escapeRegex(permissionRegex))
                .regexIfNotEmpty("description", StringUtils.escapeRegex(descriptionRegex)));
        int p = page == null ? 0 : page - 1;
        int ps = pageSize == null ? 20 : pageSize;
        Pageable pageable = PageRequest.of(p, ps, Sort.by(Sort.Direction.ASC, "permission"));
        long total = mongo().count(query, AuthPermission.class);
        List<AuthPermission> list = mongo().find(query.with(pageable), AuthPermission.class);
        return new PageImpl<>(list, pageable, total);
    }

}
