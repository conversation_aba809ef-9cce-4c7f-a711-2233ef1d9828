package com.kaiba.m.core.repository.kri.matcher;

import com.kaiba.lib.base.domain.kri.KbResource;
import com.kaiba.m.core.domain.kri.matcher.KRIValueMatcher;
import com.kaiba.m.core.middleware.mongo.interfaces.KbMongoRepository;
import org.springframework.data.domain.*;
import org.springframework.data.mongodb.core.FindAndModifyOptions;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * author: lyux
 * date: 2023-08-08
 */
@Repository
public interface KRIValueMatcherRepository extends KbMongoRepository<KRIValueMatcher, String> {

    Page<KRIValueMatcher> findByFactOrderByIdDesc(String fact, Pageable pageable);

    default List<KRIValueMatcher> matchListByRef(KbResource resource, String fact) {
        Criteria criteria = createRefCriteria(resource, fact);
        return mongo().find(new Query(criteria), KRIValueMatcher.class);
    }

    default Optional<KRIValueMatcher> matchFirstByFact(KbResource resource, String fact) {
        Criteria criteria = createRefCriteria(resource, fact);
        KRIValueMatcher matcher = mongo().findOne(new Query(criteria), KRIValueMatcher.class);
        return Optional.ofNullable(matcher);
    }

    default Page<KRIValueMatcher> getPageByFact(KbResource resource, String fact, int page, int pageSize) {
        Criteria criteria = createRefCriteria(resource, fact);
        Pageable pageable = PageRequest.of(page, pageSize, Sort.by(Sort.Direction.DESC, "_id"));
        List<KRIValueMatcher> list = mongo().find(new Query(criteria).with(pageable), KRIValueMatcher.class);
        long total = mongo().getCollection("kri_match_by_value").estimatedDocumentCount();
        return new PageImpl<>(list, pageable, total);
    }

    default KRIValueMatcher upsert(KbResource resource, String fact, String data) {
        Criteria criteria = createRefCriteria(resource, fact);
        return mongo().findAndModify(
                new Query(criteria),
                new Update()
                        .set("data", data)
                        .set("updateTime", System.currentTimeMillis()),
                FindAndModifyOptions.options().upsert(true).returnNew(true),
                KRIValueMatcher.class);
    }

    default Criteria createRefCriteria(KbResource resource, String fact) {
        return createRefCriteria(
                resource.getBiz(), resource.getUnit(),
                resource.getRef1(), resource.getRef2(), resource.getRef3(),
                fact);
    }

    default Criteria createRefCriteria(
            String biz, String unit, String ref1, String ref2, String ref3, String fact) {
        Criteria criteria = Criteria.where("biz").is(biz);
        if (ref1 != null) {
            criteria = criteria.and("ref1").is(ref1);
        }
        if (ref2 != null) {
            criteria = criteria.and("ref2").is(ref2);
        }
        if (ref3 != null) {
            criteria = criteria.and("ref3").is(ref3);
        }
        if (unit != null) {
            criteria = criteria.and("unit").is(unit);
        }
        if (fact != null) {
            criteria = criteria.and("fact").is(fact);
        }
        return criteria;
    }

}
