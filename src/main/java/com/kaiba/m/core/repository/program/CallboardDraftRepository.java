package com.kaiba.m.core.repository.program;

import com.kaiba.m.core.domain.program.CallboardDraft;
import com.kaiba.m.core.middleware.mongo.interfaces.KbMongoRepository;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * author: duanyf
 * date: 2024-07-18
 **/
@Repository
public interface CallboardDraftRepository extends KbMongoRepository<CallboardDraft, String> {

    default Page<CallboardDraft> getList(Integer siteId, Integer top, String type, String content, Integer status, Sort sortData, Pageable pageable) {
        Criteria criteria = new Criteria();
        criteria.and("siteId").is(siteId);
        if (top != null) {
            criteria.and("top").is(top);
        }
        if (type != null) {
            criteria.orOperator(
                    Criteria.where("type").is(type),
                    Criteria.where("type").is(Integer.parseInt(type))
            );
        }
        if (status != null) {
            criteria.and("status").is(status);
        }
        if (content != null) {
            criteria.and("content").regex(content);
        }
        Query query = Query.query(criteria).with(sortData);
        long count = mongo().count(query, CallboardDraft.class);
        List<CallboardDraft> list = mongo().find(query.with(pageable), CallboardDraft.class);
        return new PageImpl<>(list, pageable, count);
    }

    default CallboardDraft update(Integer siteId, String id, String type, String content) {
        return mongo().findAndModify(
                new Query(Criteria.where("id").is(id).and("siteId").is(siteId)),
                new Update().set("type", type).set("content", content),
                CallboardDraft.class);
    }

    default CallboardDraft updateTop(Integer siteId, String id, Integer top) {
        return mongo().findAndModify(
                new Query(Criteria.where("id").is(id).and("siteId").is(siteId)),
                new Update().set("top", top),
                CallboardDraft.class);
    }

    default CallboardDraft updateDraftStatus(Integer siteId, String id, Integer status) {
        return mongo().findAndModify(
                new Query(Criteria.where("id").is(id).and("siteId").is(siteId)),
                new Update().set("status", status),
                CallboardDraft.class);
    }
}
