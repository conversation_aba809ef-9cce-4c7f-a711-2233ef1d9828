package com.kaiba.m.core.repository.statistics;

import com.kaiba.lib.base.domain.data.datav.DatavTimeBucketStatModel;
import com.kaiba.m.core.domain.statistics.DownStat;
import com.kaiba.m.core.middleware.mongo.interfaces.KbMongoRepository;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.repository.Aggregation;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface DownStatRepository extends KbMongoRepository<DownStat,String> {

    @Aggregation(value = {
        "{$match: {time: {$gte: ?0, $lt: ?1}}}",
        "{$project: {time: 1, timeBucket: {$floor: {$divide: ['$time', ?2]}}}}",
        "{$group: {_id: '$timeBucket', count: {$sum: 1}}}",
        "{$project: {_id: 0, startTime: {$multiply: ['$_id', ?2]}, count: 1}}",
        "{$project: {startTime: 1, count: 1, endTime: {$add: ['$startTime', ?2]}}}"
    })
    List<DatavTimeBucketStatModel> aggrByTimeBetweenAndInterval(Long st, Long et, Long interval);

    @Aggregation(value = {
            "{$match: {siteId: ?0, time: {$gte: ?1, $lt: ?2}}}",
            "{$project: {time: 1, timeBucket: {$floor: {$divide: ['$time', ?3]}}}}",
            "{$group: {_id: '$timeBucket', count: {$sum: 1}}}",
            "{$project: {_id: 0, startTime: {$multiply: ['$_id', ?3]}, count: 1}}",
            "{$project: {startTime: 1, count: 1, endTime: {$add: ['$startTime', ?3]}}}"
    })
    List<DatavTimeBucketStatModel> aggrSiteByTimeBetweenAndInterval(Integer siteId, Long st, Long et, Long interval);

    @Aggregation(value = {
        "{$match: {time: {$gte: ?0, $lt: ?1}}}",
        "{$project: {timeBucket: {$add: [new Date(0), 28800000, {$multiply: ['$time', 1000]}]}}}",
        "{$group: {_id: {$dateToString: {format: '%Y-%m-%d', date: '$timeBucket'}}, count: {$sum: 1}}}",
        "{$project: {timeBucket: '$_id', count: 1}}",
    })
    List<DatavTimeBucketStatModel> aggrCountByDay(Long st, Long et);

    @Aggregation(value = {
            "{$match: {siteId: ?0, time: {$gte: ?1, $lt: ?2}}}",
            "{$project: {timeBucket: {$add: [new Date(0), 28800000, {$multiply: ['$time', 1000]}]}}}",
            "{$group: {_id: {$dateToString: {format: '%Y-%m-%d', date: '$timeBucket'}}, count: {$sum: 1}}}",
            "{$project: {timeBucket: '$_id', count: 1}}",
    })
    List<DatavTimeBucketStatModel> aggrSiteCountByDay(Integer siteId, Long st, Long et);

    //count by siteId and time range
    default long countBySiteIdAndTimeBetween(Integer siteId, Long startTime, Long endTime) {
        Criteria criteria = new Criteria();
        criteria.and("time").gte(startTime).lte(endTime);
        if (siteId != null) {
            criteria.and("siteId").is(siteId);
        }
        Query query = new Query(criteria);
        return mongo().count(query, DownStat.class);
    }
}
