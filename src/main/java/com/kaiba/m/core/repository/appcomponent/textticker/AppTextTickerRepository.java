package com.kaiba.m.core.repository.appcomponent.textticker;

import com.kaiba.lib.base.domain.appwidget.textticker.TextTickerIdxUpdateModel;
import com.kaiba.m.core.domain.appwidget.textticker.TextTicker;
import com.kaiba.m.core.middleware.mongo.interfaces.KbMongoRepository;
import com.kaiba.m.core.middleware.mongo.utils.KbMongoBeanUpdate;
import org.bson.types.ObjectId;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.core.BulkOperations;
import org.springframework.data.mongodb.core.FindAndModifyOptions;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.data.util.Pair;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version AppTextTickerRepository, v0.1 2023/7/17 16:52 daopei Exp $
 **/
@Repository
public interface AppTextTickerRepository extends KbMongoRepository<TextTicker, String> {

    List<TextTicker> findByInstanceId(String instanceId);

    Optional<TextTicker> findFirstByInstanceIdAndRefId(String instanceId, String refId);

    boolean existsByInstanceId(String instanceId);

    default Page<TextTicker> findByParams(String instanceId, Integer state, Pageable pageable) {
        Query query = new Query();
        if (instanceId != null) {
            query.addCriteria(Criteria.where("instanceId").is(instanceId));
        }
        if (state != null) {
            query.addCriteria(Criteria.where("state").is(state));
        }
        long totalCount = mongo().count(query, TextTicker.class);
        List<TextTicker> list = mongo().find(query.with(pageable), TextTicker.class);
        return new PageImpl<>(list, pageable, totalCount);
    }

    default void updateIdx(TextTickerIdxUpdateModel idxUpdateModel) {
        BulkOperations ops = mongo().bulkOps(BulkOperations.BulkMode.ORDERED, TextTicker.class);
        List<Pair<Query, Update>> updates = idxUpdateModel.getIdxList().stream().
                map(dataIdxModel ->
                        Pair.of(new Query(Criteria.where("_id").is(new ObjectId(dataIdxModel.getDataId()))),
                                new Update().set("idx", dataIdxModel.getIdx())))
                .collect(Collectors.toList());
        ops.updateMulti(updates);
        ops.execute();
    }

    default TextTicker updateState(String id, Integer state) {
        return mongo().findAndModify(
                new Query(Criteria.where("_id").is(new ObjectId(id))),
                new Update().set("state", state)
                        .set("updateTime", System.currentTimeMillis()),
                TextTicker.class
        );
    }

    default TextTicker updateScheduledTime(String id, Long scheduledStartTime, Long scheduledEndTime) {
        return mongo().findAndModify(
                new Query(Criteria.where("_id").is(new ObjectId(id))),
                new Update()
                        .set("scheduledStartTime", scheduledStartTime)
                        .set("scheduledEndTime", scheduledEndTime)
                        .set("updateTime", System.currentTimeMillis()),
                TextTicker.class);
    }

    default TextTicker updateData(TextTicker textTicker) {
        return mongo().findAndModify(
                Query.query(Criteria.where("_id").is(new ObjectId(textTicker.getId()))),
                new KbMongoBeanUpdate(textTicker)
                        .setIfNotNull("title")
                        .setIfNotEmpty("action")
                        .setIfNotNull("actionParams")
                        .setIfNotNull("attr")
                        .setIfNotNull("scheduledStartTime")
                        .setIfNotNull("scheduledEndTime")
                        .setIfNotNull("refId")
                        .set("updateTime",System.currentTimeMillis()),
                new FindAndModifyOptions().returnNew(true),
                TextTicker.class
        );
    }
}
