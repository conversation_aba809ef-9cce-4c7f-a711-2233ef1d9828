package com.kaiba.m.core.repository.floatview;

import com.kaiba.lib.base.constant.floatview.FloatViewState;
import com.kaiba.lib.base.response.KbCode;
import com.kaiba.lib.base.response.KbException;
import com.kaiba.m.core.domain.floatview.FloatIcon;
import com.kaiba.m.core.middleware.mongo.interfaces.KbMongoRepository;
import org.bson.types.ObjectId;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.core.FindAndModifyOptions;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Repository;

import java.util.Optional;

/**
 * author: lyux
 * date: 18-9-18
 */
@Repository
public interface FloatIconRepository extends KbMongoRepository<FloatIcon, String> {

    Optional<FloatIcon> findFirstBySiteIdAndMarkAndStateInOrderByUpdateTimeDesc(
            Integer siteId, String mark, Integer[] states);

    Optional<FloatIcon> findFirstBySiteIdAndMarkOrderByUpdateTimeDesc(
            Integer siteId, String mark);

    Optional<FloatIcon> findFirstBySiteIdAndStateInOrderByUpdateTimeDesc(
            Integer siteId, Integer[] states);

    Optional<FloatIcon> findFirstBySiteIdAndStateOrderByScheduledStartTimeDesc(
            Integer siteId, Integer state);

    Optional<FloatIcon> findFirstBySiteIdOrderByUpdateTimeDesc(
            Integer siteId);

    Page<FloatIcon> findByStateInOrderByUpdateTimeDesc(Integer[] states, Pageable pageable);

    Page<FloatIcon> findBySiteIdAndMarkAndStateInOrderByUpdateTimeDesc(
            Integer siteId, String mark, Integer[] states, Pageable pageable);

    Page<FloatIcon> findBySiteIdAndMarkOrderByUpdateTimeDesc(
            Integer siteId, String mark, Pageable pageable);

    Page<FloatIcon> findBySiteIdAndStateInOrderByUpdateTimeDesc(
            Integer siteId, Integer[] states, Pageable pageable);

    Page<FloatIcon> findBySiteIdOrderByUpdateTimeDesc(
            Integer siteId, Pageable pageable);

    default FloatIcon updateState(String id, FloatViewState state) {
        if (id == null || state == null) {
            throw new KbException(KbCode.ILLEGAL_ARGUMENT);
        }
        long current = System.currentTimeMillis() / 1000;
        Update update = new Update()
                .set("state", state.getValue())
                .set("updateTime", current);
        if (state == FloatViewState.ONLINE) {
            update.set("startTime", current);
        } else if (state == FloatViewState.SEALED) {
            update.set("endTime", current);
        }
        return mongo().findAndModify(
                new Query(Criteria.where("_id").is(new ObjectId(id))),
                update,
                FindAndModifyOptions.options().upsert(false).returnNew(true),
                FloatIcon.class);
    }

    default void stopOnlineExcept(Integer siteId, String exceptFloatIconId, String mark) {
        if (siteId == null) {
            throw new KbException(KbCode.ILLEGAL_ARGUMENT, "siteId null");
        }
        Criteria criteria = Criteria
                .where("siteId").is(siteId)
                .and("state").is(FloatViewState.ONLINE.getValue())
                .and("mark").is(mark);
        if (null != exceptFloatIconId) {
            criteria.and("_id").ne(new ObjectId(exceptFloatIconId));
        }
        long current = System.currentTimeMillis() / 1000;
        mongo().updateMulti(
                new Query(criteria),
                new Update()
                        .set("state", FloatViewState.SEALED.getValue())
                        .set("updateTime", current)
                        .set("endTime", current),
                FloatIcon.class);
    }

    default FloatIcon updateData(FloatIcon floatIcon) {
        if (floatIcon == null || floatIcon.getId() == null) {
            throw new KbException(KbCode.ILLEGAL_ARGUMENT);
        }
        Update update = new Update().set("updateTime", System.currentTimeMillis() / 1000);
        if (floatIcon.getIcon() != null) update.set("icon", floatIcon.getIcon());
        if (floatIcon.getScheduledStartTime() != null)
            update.set("scheduledStartTime", floatIcon.getScheduledStartTime());
        if (floatIcon.getScheduledEndTime() != null) update.set("scheduledEndTime", floatIcon.getScheduledEndTime());
        if (floatIcon.getOnlyForLogin() != null) update.set("onlyForLogin", floatIcon.getOnlyForLogin());
        if (floatIcon.getAction() != null) update.set("action", floatIcon.getAction());
        if (floatIcon.getActionParams() == null) {
            update.unset("actionParams");
        } else {
            update.set("actionParams", floatIcon.getActionParams());
        }
        if (floatIcon.getPosition() == null) {
            update.unset("position");
        } else {
            update.set("position", floatIcon.getPosition());
        }
        if (floatIcon.getSizeRatio() == null) {
            update.unset("sizeRatio");
        } else {
            update.set("sizeRatio", floatIcon.getSizeRatio());
        }
        if (floatIcon.getEnableDrag() == null) {
            update.unset("enableDrag");
        } else {
            update.set("enableDrag", floatIcon.getEnableDrag());
        }
        if (floatIcon.getDescription() == null) {
            update.unset("description");
        } else {
            update.set("description", floatIcon.getDescription());
        }
        return mongo().findAndModify(
                new Query(Criteria.where("_id").is(new ObjectId(floatIcon.getId()))),
                update,
                FindAndModifyOptions.options().upsert(false).returnNew(true),
                FloatIcon.class);
    }

}
