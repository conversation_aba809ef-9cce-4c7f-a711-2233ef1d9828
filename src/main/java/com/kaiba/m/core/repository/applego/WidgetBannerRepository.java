package com.kaiba.m.core.repository.applego;

import com.kaiba.lib.base.constant.applego.WidgetType;
import com.kaiba.m.core.domain.applego.widget.WidgetBannerData;
import com.kaiba.m.core.middleware.mongo.interfaces.KbMongoRepository;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.List;
import java.util.Optional;

/**
 * author: lyux
 * date: 2023-08-11
 */
@Repository
public interface WidgetBannerRepository extends
        KbMongoRepository<WidgetBannerData, String>, IWidgetRepository<WidgetBannerData> {

    @Override
    List<WidgetBannerData> findByIdIn(Collection<String> widgetIds);

    @Override
    Optional<WidgetBannerData> findFirstByBlockId(String blockId);

    @Override
    default WidgetType getWidgetType() {
        return WidgetType.BANNER;
    }
}
