package com.kaiba.m.core.repository.message;

import com.kaiba.m.core.domain.message.UserMessageReadMarker;
import com.kaiba.m.core.middleware.mongo.interfaces.KbMongoRepository;
import org.springframework.data.mongodb.core.FindAndModifyOptions;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Repository;

/**
 * author: lyux
 * date: 18-9-28
 */
@Repository
public interface UserMessageReadMarkerRepository extends KbMongoRepository<UserMessageReadMarker, Integer> {

    default void refreshUserReadMarker(Integer userId, String lastReadId) {
        mongo().findAndModify(
                new Query(Criteria.where("_id").is(userId)),
                new Update().set("lastReadId", lastReadId),
                new FindAndModifyOptions().upsert(true),
                UserMessageReadMarker.class);
    }

    default String getLastIdReadByUser(Integer userId) {
        UserMessageReadMarker marker = mongo().findOne(
                new Query(Criteria.where("_id").is(userId)),
                UserMessageReadMarker.class);
        return null == marker ? null : marker.getLastReadId();
    }

}
