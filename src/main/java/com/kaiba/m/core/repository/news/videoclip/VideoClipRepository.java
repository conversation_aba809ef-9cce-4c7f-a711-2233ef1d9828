package com.kaiba.m.core.repository.news.videoclip;

import com.kaiba.lib.base.constant.news.videoclip.VideoClipState;
import com.kaiba.m.core.domain.news.videoclip.VideoClip;
import com.kaiba.m.core.middleware.mongo.interfaces.KbMongoRepository;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.FindAndModifyOptions;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2023/07/05 14:05
 **/
@Repository
public interface VideoClipRepository extends KbMongoRepository<VideoClip, String> {

    String ID_FIELDS = "{id: 1}";
    String SORT_FIELDS = "{idx: -1, updateTime: -1}";

    List<VideoClip> findAllByCreateTimeBetween(Long startTime, Long endTime, Pageable pageable);

    @org.springframework.data.mongodb.repository.Query(fields = ID_FIELDS, sort = SORT_FIELDS)
    List<VideoClip> findAllBySiteIdAndState(Integer siteId, Integer state, Pageable pageable);

    long countBySiteIdAndState(Integer siteId, Integer state);

    @org.springframework.data.mongodb.repository.Query(sort = SORT_FIELDS)
    Page<VideoClip> findAllBySiteIdOrderByIdDesc(Integer siteId, Pageable pageable);

    @org.springframework.data.mongodb.repository.Query(sort = SORT_FIELDS)
    Page<VideoClip> findAllBySiteIdAndStateOrderByIdDesc(Integer siteId, Integer state, Pageable pageable);

    Page<VideoClip> findAllBySiteIdAndStateAndCreateTimeBetweenOrderByIdDesc(
            Integer siteId, Integer state, long startTime, long endTime, Pageable pageable);

    Page<VideoClip> findAllBySiteIdAndCreateTimeBetweenOrderByIdDesc(
            Integer siteId, long startTime, long endTime, Pageable pageable);

    List<VideoClip> findAllByIdIn(List<String> ids);

    default Optional<VideoClip> findFirstByIdx(long idxStart, long idxEnd) {
        return Optional.ofNullable(mongo().findOne(
            Query.query(Criteria.where("idx").gte(idxStart).lt(idxEnd))
                .with(Sort.by(Sort.Direction.DESC, "idx")).limit(1),
            VideoClip.class));
    }

    default void updateIdx(String clipId, Long idx) {
        mongo().findAndModify(
            Query.query(Criteria.where("id").is(clipId)),
            new Update().set("idx", idx),
            new FindAndModifyOptions().upsert(false).returnNew(true),
            VideoClip.class);
    }

    default VideoClip updateAsOffline(String clipId) {
        return mongo().findAndModify(
            Query.query(Criteria.where("id").is(clipId)),
            new Update()
                .set("state", VideoClipState.OFFLINE.getValue())
                .set("updateTime", System.currentTimeMillis()),
            new FindAndModifyOptions().upsert(false).returnNew(true),
            VideoClip.class);
    }

    default VideoClip updateAsOnline(String clipId) {
        return mongo().findAndModify(
            Query.query(Criteria.where("id").is(clipId)),
            new Update()
                .set("state", VideoClipState.ONLINE.getValue())
                .set("updateTime", System.currentTimeMillis()),
            new FindAndModifyOptions().upsert(false).returnNew(true),
            VideoClip.class);
    }

    default VideoClip updateVideoClip(VideoClip clip) {
        return mongo().findAndModify(
            Query.query(Criteria.where("id").is(clip.getId())),
            new Update()
                .set("video", clip.getVideo())
                .set("image", clip.getImage())
                .set("orientation", clip.getOrientation())
                .set("title", clip.getTitle())
                .set("desc", clip.getDesc())
                .set("funcFlag", clip.getFuncFlag())
                .set("state", VideoClipState.INIT.getValue())
                .set("updateTime", System.currentTimeMillis()),
            new FindAndModifyOptions().upsert(false).returnNew(true),
            VideoClip.class);
    }

    default void updateChannel(String id, String channelKey) {
        mongo().findAndModify(
                Query.query(Criteria.where("id").is(id)),
                new Update()
                        .set("channelKey", channelKey),
                VideoClip.class);
    }

    default void updateImageAndVideo(VideoClip clip) {
        mongo().findAndModify(
            Query.query(Criteria.where("id").is(clip.getId())),
            new Update()
                .set("hooloImage", clip.getHooloImage())
                .set("hooloVideo", clip.getHooloVideo())
                .set("image", clip.getImage())
                .set("video", clip.getVideo()),
            VideoClip.class);
    }
}
