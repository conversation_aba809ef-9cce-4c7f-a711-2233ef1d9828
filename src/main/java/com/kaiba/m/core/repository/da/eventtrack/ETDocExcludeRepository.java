package com.kaiba.m.core.repository.da.eventtrack;

import com.kaiba.lib.base.domain.common.KbTimeRange;
import com.kaiba.m.core.domain.da.eventtrack.ETDocExclude;
import com.kaiba.m.core.middleware.mongo.interfaces.KbMongoRepository;
import com.kaiba.m.core.middleware.mongo.utils.KbMongoUpdate;
import com.kaiba.m.core.model.da.eventtrack.ETDocQueryModel;
import com.kaiba.m.core.util.PageUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.FindAndModifyOptions;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @version ETDocExcludeRepository, v0.1 2025/4/11 15:27 daopei Exp $
 **/
@Repository
public interface ETDocExcludeRepository extends KbMongoRepository<ETDocExclude, String> {

    default Page<ETDocExclude> getPageQuery(ETDocQueryModel query) {
        Pageable pageable = PageUtils.ofDefault(query.getPage(), query.getPageSize(), Sort.by(Sort.Direction.DESC, "createTime"));
        Criteria criteria = new Criteria();
        if (query.getTitle() != null) {
            criteria.and("title").regex(query.getTitle());
        }
        if (query.getDocId() != null) {
            criteria.and("docId").is(query.getDocId());
        }
        if (query.getBiz() != null && !query.getBiz().isEmpty()) {
            criteria.and("biz").in(query.getBiz());
        }
        if (query.getUnit() != null && !query.getUnit().isEmpty()) {
            criteria.and("unit").in(query.getUnit());
        }
        if (query.getRef1() != null) {
            criteria.and("ref1").is(query.getRef1());
        }
        if (query.getRef2() != null) {
            criteria.and("ref2").is(query.getRef2());
        }
        if (query.getRef3() != null) {
            criteria.and("ref3").is(query.getRef3());
        }
        if (query.getSiteId() != null) {
            criteria.and("siteId").is(query.getSiteId());
        }
        if (query.getUpdateTimeRange() != null) {
            timeRangeCriteria(query.getUpdateTimeRange(), "updateTime", criteria);
        }
        Query queryQL = new Query(criteria);

        long count = mongo().count(queryQL, ETDocExclude.class);
        List<ETDocExclude> list = mongo().find(queryQL.with(pageable), ETDocExclude.class);
        return new PageImpl<>(list, pageable, count);
    }

    default ETDocExclude upsert(ETDocExclude exclude) {
        return mongo().findAndModify(
                new Query(Criteria.where("docId").is(exclude.getDocId())),
                new KbMongoUpdate()
                        .setOnInsert("biz", exclude.getBiz())
                        .setOnInsert("unit", exclude.getUnit())
                        .setOnInsert("ref1", exclude.getRef1())
                        .setOnInsert("ref2", exclude.getRef2())
                        .setOnInsert("ref3", exclude.getRef3())
                        .setOnInsert("siteId", exclude.getSiteId())
                        .setOnInsert("docId", exclude.getDocId())
                        .setOnInsert("createTime", exclude.getCreateTime())
                        .set("reason", exclude.getReason())
                        .set("title", exclude.getTitle())
                        .set("updateTime", exclude.getUpdateTime()),
                FindAndModifyOptions.options().returnNew(true).upsert(true),
                ETDocExclude.class
        );
    }


    default Criteria timeRangeCriteria(KbTimeRange timeRange, String field, Criteria criteria) {
        if (timeRange.isUnbounded()) {
            return criteria;
        }
        Criteria c = criteria.and(field);
        if (timeRange.getLower() != null) {
            if (timeRange.isLowerOpen()) {
                c.gt(timeRange.obtainLowerInMillis());
            } else {
                c.gte(timeRange.obtainLowerInMillis());
            }
        }
        if (timeRange.getUpper() != null) {
            if (timeRange.isUpperOpen()) {
                c.lt(timeRange.obtainUpperInMillis());
            } else {
                c.lte(timeRange.obtainUpperInMillis());
            }
        }
        return c;
    }
}
