package com.kaiba.m.core.repository.auth;

import com.kaiba.lib.base.util.StringUtils;
import com.kaiba.m.core.domain.auth.AuthPermission;
import com.kaiba.m.core.domain.auth.AuthRole;
import com.kaiba.m.core.domain.route.KbRoute;
import com.kaiba.m.core.middleware.mongo.interfaces.KbMongoRepository;
import com.kaiba.m.core.middleware.mongo.utils.KbMongoCriteria;
import org.springframework.data.domain.*;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;
import java.util.Set;

/**
 * author: lyux
 * date: 19-8-13
 */
@Repository
public interface AuthRoleRepository extends KbMongoRepository<AuthRole, String> {

    Optional<AuthRole> findFirstByRole(String role);

    List<AuthRole> findByRoleInOrderByRole(List<String> roles);

    Page<AuthRole> findAllByOrderByRole(Pageable pageable);

    Page<AuthRole> findByScopeOrderByRole(Integer scope, Pageable pageable);

    Page<AuthRole> findByScopeAndReferenceIdOrderByRole(Integer scope, String referenceId, Pageable pageable);

    Page<AuthRole> findByPermissionsOrderByRole(String permission, Pageable pageable);

    Page<AuthRole> findByPermissionsInOrderByRole(String[] permissions, Pageable pageable);

    Page<AuthRole> findByPermissionsInOrderByRole(List<String> permissions, Pageable pageable);

    Page<AuthRole> findByCollideWithInOrderByRole(String[] roles, Pageable pageable);

    Page<AuthRole> findByCollideWithInOrderByRole(List<String> roles, Pageable pageable);

    Page<AuthRole> findByFormatOrderByRole(String format, Pageable pageable);

    List<AuthRole> findByFormatInOrderByRole(List<String> formats);

    List<AuthRole> findByScopeAndFormatInOrderByRole(Integer scope, List<String> formats);

    long countByPermissions(String permission);

    long countByPermissionsIn(Set<String> permissions);

    long countByFormat(String format);

    void deleteByRole(String role);

    void deleteByFormat(String format);

    default AuthRole updateCollideWith(String role, List<String> collideWithRoles) {
        return mongo().findAndModify(
                new Query(Criteria.where("role").is(role)),
                new Update().set("collideWith", collideWithRoles),
                AuthRole.class);
    }

    default AuthRole updatePermissions(String role, List<String> permissions) {
        return mongo().findAndModify(
                new Query(Criteria.where("role").is(role)),
                new Update().set("permissions", permissions),
                AuthRole.class);
    }

    default AuthRole updateFields(String role, List<String> permissions, List<String> collideWithRoles, String description) {
        return mongo().findAndModify(
                new Query(Criteria.where("role").is(role)),
                new Update()
                        .set("permissions", permissions)
                        .set("collideWith", collideWithRoles)
                        .set("description", description),
                AuthRole.class);
    }

    default Page<AuthRole> searchForPage(
            Integer scope, String referenceId,
            String roleRegex, String descriptionRegex,
            Integer page, Integer pageSize) {
        Query query = Query.query(new KbMongoCriteria()
                .criteriaIfNotEmpty("scope", scope)
                .criteriaIfNotEmpty("referenceId", referenceId)
                .regexIfNotEmpty("role", StringUtils.escapeRegex(roleRegex))
                .regexIfNotEmpty("description", StringUtils.escapeRegex(descriptionRegex)));
        int p = page == null ? 0 : page - 1;
        int ps = pageSize == null ? 20 : pageSize;
        Pageable pageable = PageRequest.of(p, ps, Sort.by(Sort.Direction.ASC, "role"));
        long total = mongo().count(query, AuthRole.class);
        List<AuthRole> list = mongo().find(query.with(pageable), AuthRole.class);
        return new PageImpl<>(list, pageable, total);
    }

}
