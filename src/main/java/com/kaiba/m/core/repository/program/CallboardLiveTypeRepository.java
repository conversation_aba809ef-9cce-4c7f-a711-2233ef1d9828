package com.kaiba.m.core.repository.program;

import com.kaiba.m.core.domain.program.CallboardLiveType;
import com.kaiba.m.core.middleware.mongo.interfaces.KbMongoRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * author: duanyf
 * date: 2024-07-18
 **/
@Repository
public interface CallboardLiveTypeRepository extends KbMongoRepository<CallboardLiveType, String> {

    List<CallboardLiveType> findAllBySiteId(Integer siteId);
}
