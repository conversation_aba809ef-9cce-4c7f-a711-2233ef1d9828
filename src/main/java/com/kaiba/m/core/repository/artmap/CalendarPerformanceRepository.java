package com.kaiba.m.core.repository.artmap;

import com.google.common.collect.Lists;
import com.kaiba.m.core.domain.artmap.CalendarPerformance;
import com.kaiba.m.core.middleware.mongo.interfaces.KbMongoRepository;
import com.kaiba.m.core.model.artmap.dto.CalendarPerformQueryDTO;
import java.util.List;
import java.util.Objects;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.bson.Document;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.FindAndModifyOptions;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.aggregation.AggregationOperation;
import org.springframework.data.mongodb.core.aggregation.AggregationResults;
import org.springframework.data.mongodb.core.aggregation.GroupOperation;
import org.springframework.data.mongodb.core.aggregation.TypedAggregation;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Repository;

/**
 * Description: 艺术地图日历化演出管理Repository
 * Author: ZM227
 * Date: 2025/6/16 16:59
 */
@Repository
public interface CalendarPerformanceRepository extends
    KbMongoRepository<CalendarPerformance, String> {

    /**
     * 查询艺术地图演出列表
     * 以performanceCode和performanceDate分组并保留第一条
     *
     * @param queryDTO 查询条件
     * @return 查询结果
     */
    default Page<CalendarPerformance> queryCalendarPerformanceListCodeAndDateGroup(
        CalendarPerformQueryDTO queryDTO) {
        Criteria criteria = new Criteria();
        if (CollectionUtils.isNotEmpty(queryDTO.getPerformanceCodes())) {
            criteria.and("performanceCode").in(queryDTO.getPerformanceCodes());
        }
        if (Objects.nonNull(queryDTO.getPerformanceDateStart()) && Objects.nonNull(
            queryDTO.getPerformanceDateEnd())) {
            criteria.and("performanceDate").gte(queryDTO.getPerformanceDateStart())
                .lte(queryDTO.getPerformanceDateEnd());
        } else if (Objects.nonNull(queryDTO.getPerformanceDateStart())) {
            criteria.and("performanceDate").gte(queryDTO.getPerformanceDateStart());
        } else if (Objects.nonNull(queryDTO.getPerformanceDateEnd())) {
            criteria.and("performanceDate").lte(queryDTO.getPerformanceDateEnd());
        }
        if (StringUtils.isNotBlank(queryDTO.getVenuesCode())) {
            criteria.and("venuesCode").is(queryDTO.getVenuesCode());
        }
        if (Objects.nonNull(queryDTO.getStatus())) {
            criteria.and("status").is(queryDTO.getStatus());
        }
        // 构建聚合管道
        List<AggregationOperation> operations = Lists.newArrayList();
        // 匹配过滤
        operations.add(Aggregation.match(criteria));
        // 按performanceCode分组并保留第一条
        GroupOperation groupOperation = Aggregation.group("performanceCode", "performanceDate")
            .first(Aggregation.ROOT).as("firstDocument");
        operations.add(groupOperation);
        // 替换根文档为分组后的文档
        operations.add(Aggregation.replaceRoot("firstDocument"));
        //  计算去重后的总数
        TypedAggregation<CalendarPerformance> countAggregation = Aggregation.newAggregation(
            CalendarPerformance.class, Aggregation.match(criteria),
            Aggregation.group("performanceCode", "performanceDate"));
        AggregationResults<Document> countResults = mongo().aggregate(countAggregation,
            Document.class);
        long distinctTotal = countResults.getMappedResults().size();
        // 处理分页逻辑
        Pageable pageable = queryDTO.getPageable();
        if (Objects.isNull(pageable)) {
            pageable = PageRequest.of(0, Math.max(1, (int) distinctTotal));
        }
        //  添加分页操作
        operations.add(
            Aggregation.sort(Sort.by(Sort.Order.asc("seq"), Sort.Order.desc("startTime"))));
        operations.add(Aggregation.skip(pageable.getOffset()));
        operations.add(Aggregation.limit(pageable.getPageSize()));
        // 执行聚合查询
        TypedAggregation<CalendarPerformance> aggregation = Aggregation.newAggregation(
            CalendarPerformance.class, operations.toArray(new AggregationOperation[0]));
        AggregationResults<CalendarPerformance> results = mongo().aggregate(aggregation,
            CalendarPerformance.class);
        List<CalendarPerformance> distinctList = results.getMappedResults();
        // 返回分页结果
        return new PageImpl<>(distinctList, pageable, distinctTotal);
    }

    /**
     * 查询艺术地图演出列表
     * 以performanceCode分组并保留第一条
     *
     * @param queryDTO 查询条件
     * @return 查询结果
     */
    default Page<CalendarPerformance> queryCalendarPerformanceListCodeGroup(
        CalendarPerformQueryDTO queryDTO) {
        Criteria criteria = new Criteria();
        if (CollectionUtils.isNotEmpty(queryDTO.getPerformanceCodes())) {
            criteria.and("performanceCode").in(queryDTO.getPerformanceCodes());
        }
        if (Objects.nonNull(queryDTO.getPerformanceDateStart()) && Objects.nonNull(
            queryDTO.getPerformanceDateEnd())) {
            criteria.and("performanceDate").gte(queryDTO.getPerformanceDateStart())
                .lte(queryDTO.getPerformanceDateEnd());
        } else if (Objects.nonNull(queryDTO.getPerformanceDateStart())) {
            criteria.and("performanceDate").gte(queryDTO.getPerformanceDateStart());
        } else if (Objects.nonNull(queryDTO.getPerformanceDateEnd())) {
            criteria.and("performanceDate").lte(queryDTO.getPerformanceDateEnd());
        }
        if (StringUtils.isNotBlank(queryDTO.getVenuesCode())) {
            criteria.and("venuesCode").is(queryDTO.getVenuesCode());
        }
        if (Objects.nonNull(queryDTO.getStatus())) {
            criteria.and("status").is(queryDTO.getStatus());
        }
        // 构建聚合管道
        List<AggregationOperation> operations = Lists.newArrayList();
        // 匹配过滤
        operations.add(Aggregation.match(criteria));
        // 按performanceCode分组并保留第一条
        GroupOperation groupOperation = Aggregation.group("performanceCode").first(Aggregation.ROOT)
            .as("firstDocument");
        operations.add(groupOperation);
        // 替换根文档为分组后的文档
        operations.add(Aggregation.replaceRoot("firstDocument"));
        //  计算去重后的总数
        TypedAggregation<CalendarPerformance> countAggregation = Aggregation.newAggregation(
            CalendarPerformance.class, Aggregation.match(criteria),
            Aggregation.group("performanceCode"));
        AggregationResults<Document> countResults = mongo().aggregate(countAggregation,
            Document.class);
        long distinctTotal = countResults.getMappedResults().size();
        // 处理分页逻辑
        Pageable pageable = queryDTO.getPageable();
        if (Objects.isNull(pageable)) {
            pageable = PageRequest.of(0, Math.max(1, (int) distinctTotal));
        }
        //  添加分页操作
        operations.add(
            Aggregation.sort(Sort.by(Sort.Order.asc("seq"), Sort.Order.desc("startTime"))));
        operations.add(Aggregation.skip(pageable.getOffset()));
        operations.add(Aggregation.limit(pageable.getPageSize()));
        // 执行聚合查询
        TypedAggregation<CalendarPerformance> aggregation = Aggregation.newAggregation(
            CalendarPerformance.class, operations.toArray(new AggregationOperation[0]));
        AggregationResults<CalendarPerformance> results = mongo().aggregate(aggregation,
            CalendarPerformance.class);
        List<CalendarPerformance> distinctList = results.getMappedResults();
        // 返回分页结果
        return new PageImpl<>(distinctList, pageable, distinctTotal);
    }

    default CalendarPerformance updateByBizCode(CalendarPerformance performance) {
        if (Objects.isNull(performance)) {
            return null;
        }
        Update update = new Update();
        if (Objects.nonNull(performance.getPerformanceDate())) {
            update.set("performanceDate", performance.getPerformanceDate());
        }
        if (Objects.nonNull(performance.getStartTime())) {
            update.set("startTime", performance.getStartTime());
        }
        if (Objects.nonNull(performance.getVenuesCode())) {
            update.set("venuesCode", performance.getVenuesCode());
        }
        if (StringUtils.isNotBlank(performance.getTheaterName())) {
            update.set("theaterName", performance.getTheaterName());
        }
        if (Objects.nonNull(performance.getInventory())) {
            update.set("inventory", performance.getInventory());
        }
        if (Objects.nonNull(performance.getStatus())) {
            update.set("status", performance.getStatus());
        }
        update.set("updateTime", System.currentTimeMillis());
        return mongo().findAndModify(
            new Query(Criteria.where("bizCode").is(performance.getBizCode())), update,
            new FindAndModifyOptions().upsert(true).returnNew(true), CalendarPerformance.class);
    }

    boolean existsByBizCodeIs(String bizCode);

}
