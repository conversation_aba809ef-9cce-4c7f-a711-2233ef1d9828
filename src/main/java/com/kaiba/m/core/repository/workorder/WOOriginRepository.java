package com.kaiba.m.core.repository.workorder;

import com.kaiba.m.core.domain.SeqBasedIdxFirstOrder;
import com.kaiba.m.core.domain.workorder.WOCaseOrigin;
import com.kaiba.m.core.middleware.mongo.interfaces.KbMongoRepository;
import com.kaiba.m.core.middleware.mongo.utils.KbMongoUpdate;
import org.bson.types.ObjectId;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.core.BulkOperations;
import org.springframework.data.mongodb.core.FindAndModifyOptions;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.data.util.Pair;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * author: lyux
 * date: 2023-08-11
 */
@Repository
public interface WOOriginRepository extends KbMongoRepository<WOCaseOrigin, String> {

    Optional<WOCaseOrigin> findFirstByOriginAndBiz(String origin, String biz);

    List<WOCaseOrigin> getByIdIn(Collection<String> originIds);

    List<WOCaseOrigin> getByOriginInAndBiz(Collection<String> origin, String biz);

    List<WOCaseOrigin> getByBizOrderBySeqDesc(String biz, Pageable pageable);

    List<WOCaseOrigin> getByBizAndShowOnStatOrderBySeqDesc(String biz, boolean showOnStat, Pageable pageable);

    Page<WOCaseOrigin> findByBizOrderBySeqDesc(String biz, Pageable pageable);

    Page<WOCaseOrigin> findByBizAndShowOnCreateOrderBySeqDesc(String biz, boolean showOnCreate, Pageable pageable);

    default WOCaseOrigin updateData(String originId, String name, String desc) {
        return mongo().findAndModify(
                new Query(Criteria.where("_id").is(originId)),
                new KbMongoUpdate()
                        .setIfNotEmpty("name", name)
                        .setUnsetOnEmpty("desc", desc)
                        .set("updateTime", System.currentTimeMillis()),
                new FindAndModifyOptions().returnNew(true).upsert(false),
                WOCaseOrigin.class);
    }

    default void updateShowOnCreate(String originId, boolean show) {
        mongo().findAndModify(
                new Query(Criteria.where("_id").is(originId)),
                new KbMongoUpdate()
                        .set("showOnCreate", show)
                        .set("updateTime", System.currentTimeMillis()),
                new FindAndModifyOptions().returnNew(false).upsert(false),
                WOCaseOrigin.class);
    }

    default void updateShowOnStat(String originId, boolean show) {
        mongo().findAndModify(
                new Query(Criteria.where("_id").is(originId)),
                new KbMongoUpdate()
                        .set("showOnStat", show)
                        .set("updateTime", System.currentTimeMillis()),
                new FindAndModifyOptions().returnNew(false).upsert(false),
                WOCaseOrigin.class);
    }

    default void updateIdx(String originId, long seq, Long idx) {
        mongo().findAndModify(
                new Query(Criteria.where("_id").is(originId)),
                new KbMongoUpdate()
                        .setUnsetOnNull("idx", idx)
                        .set("seq", seq),
                new FindAndModifyOptions().upsert(false).returnNew(false),
                WOCaseOrigin.class);
    }

    default void updateIdxBatch(List<SeqBasedIdxFirstOrder> list) {
        BulkOperations ops = mongo().bulkOps(BulkOperations.BulkMode.ORDERED, WOCaseOrigin.class);
        List<Pair<Query, Update>> updates = list.stream().
                map(m -> Pair.of(
                        new Query(Criteria.where("_id").is(new ObjectId(m.getId()))),
                        new Update().set("seq", m.getSeq()).set("idx", m.getIdx())))
                .collect(Collectors.toList());
        ops.updateMulti(updates);
        ops.execute();
    }

}
