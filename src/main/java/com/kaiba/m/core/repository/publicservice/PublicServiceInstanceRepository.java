package com.kaiba.m.core.repository.publicservice;

import com.kaiba.m.core.domain.publicservice.PublicServiceInstance;
import com.kaiba.m.core.middleware.mongo.interfaces.KbMongoRepository;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.core.FindAndModifyOptions;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Repository;

import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2024/01/08 10:18
 */
@Repository
public interface PublicServiceInstanceRepository extends KbMongoRepository<PublicServiceInstance, String> {

    Optional<PublicServiceInstance> findFirstByInstanceKey(String instanceKey);

    default PublicServiceInstance updateBannerKey(String instanceKey, String bannerKey) {
        return mongo().findAndModify(
                new Query(Criteria.where("instanceKey").is(instanceKey)),
                new Update().set("bannerKey", bannerKey),
                new FindAndModifyOptions().returnNew(true),
                PublicServiceInstance.class);
    }
}
