package com.kaiba.m.core.repository.appversion;

import com.kaiba.m.core.domain.appversion.AppVersionVerify;
import com.kaiba.m.core.middleware.mongo.interfaces.KbMongoRepository;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * author: duanyf
 * date: 2024-06-25
 **/
@Repository
public interface AppVersionVerifyRepository extends KbMongoRepository<AppVersionVerify, String> {

    Optional<AppVersionVerify> findFirstById(String id);

    Optional<AppVersionVerify> findFirstByEndPointAndMarketAndVersionCode(Integer endPoint, String market, Integer versionCode);

    Page<AppVersionVerify> findByOrderByIdDesc(Pageable pageable);

    default Optional<AppVersionVerify> updateState(String id, Integer state) {
        return Optional.ofNullable(mongo().findAndModify(
                Query.query(Criteria.where("id").is(id)),
                new Update()
                        .set("state", state)
                        .set("updateTime", System.currentTimeMillis())
                ,
                AppVersionVerify.class));
    }

    default Page<AppVersionVerify> getList(
            Sort sortData,
            Integer endPoint,
            String market,
            Pageable pageable
    ) {
        Criteria criteria = new Criteria();
        if (endPoint != null) {
            criteria.and("endPoint").is(endPoint);
        }
        if (market != null) {
            criteria.and("market").is(market);
        }
        Query query = Query.query(criteria).with(sortData);
        long count = mongo().count(query, AppVersionVerify.class);
        List<AppVersionVerify> list = mongo().find(query.with(pageable), AppVersionVerify.class);
        return new PageImpl<>(list, pageable, count);
    }
}
