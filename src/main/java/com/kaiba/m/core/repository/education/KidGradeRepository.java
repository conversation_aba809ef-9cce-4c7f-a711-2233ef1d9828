package com.kaiba.m.core.repository.education;

import com.kaiba.m.core.domain.education.KidGradeRecord;
import com.kaiba.m.core.middleware.mongo.interfaces.KbMongoRepository;
import org.bson.Document;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.repository.Aggregation;

import java.util.*;

/**
 * <AUTHOR>
 * @date 2024/03/20 09:56
 */
public interface KidGradeRepository extends KbMongoRepository<KidGradeRecord, String> {

    Optional<KidGradeRecord> findFirstByKidIdOrderByGradeDesc(String kidId);

    Page<KidGradeRecord> findAllByKidId(String kidId, Pageable pageable);

    @Aggregation(pipeline = {
            "{$match: {kidId:{$in: ?0}}}",
            "{$sort: {grade: -1}}",
            "{$group: {_id: '$kidId', record:{$first: '$$ROOT'}}}"
    })
    List<Document> findFirstByKidIdInOrderByAgeDesc(Collection<String> kidIds);

    default Map<String, KidGradeRecord> getLatestRecordListByUserIdIn(List<String> kidIds) {
        List<Document> docs = findFirstByKidIdInOrderByAgeDesc(kidIds);
        Map<String, KidGradeRecord> recordMap = new HashMap<>();
        for (Document doc : docs) {
            KidGradeRecord kidGrade = new KidGradeRecord();
            String kidId = doc.getString("kidId");
            kidGrade.setAge(doc.getInteger("age"));
            kidGrade.setGrade((doc.getInteger("grade")));
            kidGrade.setPeriodType(doc.getString("periodType"));
            kidGrade.setSchoolId(doc.getString("schoolId"));
            kidGrade.setCreateTime(doc.getLong("createTime"));
            recordMap.put(kidId, kidGrade);
        }
        return recordMap;
    }
}
