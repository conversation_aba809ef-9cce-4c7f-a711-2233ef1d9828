package com.kaiba.m.core.repository.applego;

import com.kaiba.lib.base.constant.applego.WidgetType;
import com.kaiba.m.core.domain.applego.widget.WidgetTextTickerData;
import com.kaiba.m.core.middleware.mongo.interfaces.KbMongoRepository;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.List;
import java.util.Optional;

/**
 * author: lyux
 * date: 2023-08-11
 */
@Repository
public interface WidgetTextTickerRepository extends
        KbMongoRepository<WidgetTextTickerData, String>, IWidgetRepository<WidgetTextTickerData> {

    @Override
    List<WidgetTextTickerData> findByIdIn(Collection<String> widgetIds);

    @Override
    Optional<WidgetTextTickerData> findFirstByBlockId(String blockId);

    @Override
    default WidgetType getWidgetType() {
        return WidgetType.TEXT_TICKER;
    }
}
