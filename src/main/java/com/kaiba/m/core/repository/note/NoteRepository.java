package com.kaiba.m.core.repository.note;

import com.kaiba.lib.base.constant.note.NoteState;
import com.kaiba.lib.base.domain.common.Audio;
import com.kaiba.lib.base.domain.common.Image;
import com.kaiba.lib.base.domain.common.Video;
import com.kaiba.lib.base.domain.data.datav.DatavProgramNoteSummaryModel;
import com.kaiba.lib.base.response.KbCode;
import com.kaiba.lib.base.response.KbException;
import com.kaiba.lib.base.util.StringUtils;
import com.kaiba.m.core.domain.note.Note;
import com.kaiba.m.core.domain.note.NoteUserRanking;
import com.kaiba.m.core.middleware.mongo.interfaces.KbMongoRepository;
import com.kaiba.m.core.middleware.mongo.utils.KbMongoUpdate;
import com.mongodb.client.AggregateIterable;
import com.mongodb.client.MongoCollection;
import org.bson.Document;
import org.bson.types.ObjectId;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.FindAndModifyOptions;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.data.mongodb.repository.Aggregation;
import org.springframework.stereotype.Repository;

import java.util.*;

/**
 * author: lyux
 * date: 18-9-7
 */
@Repository
public interface NoteRepository extends KbMongoRepository<Note, String> {

    List<Note> findByIdIn(List<String> noteIds);

    List<Note> findByIdIn(String[] noteIds);

    // -------------------------------------------------------

    List<Note> findByOrderByIdDesc(Pageable pageable);

    List<Note> findByIdLessThanOrderByIdDesc(String noteId, Pageable pageable);

    Page<Note> findBySiteId(Integer siteId, Pageable pageable);

    Page<Note> findByUserId(Integer userId, Pageable pageable);

    List<Note> getByUserId(Integer userId, Pageable pageable);

    Page<Note> findByUserIdAndCreateTimeBetween(Integer userId, Long timeStart, Long timeEnd, Pageable pageable);

    List<Note> getByUserIdAndCreateTimeBetween(Integer userId, Long timeStart, Long timeEnd, Pageable pageable);

    Page<Note> findByUserIdAndSiteId(Integer userId, Integer siteId, Pageable pageable);

    List<Note> getByUserIdAndSiteId(Integer userId, Integer siteId, Pageable pageable);

    Page<Note> findByUserIdAndSiteIdAndCreateTimeBetween(Integer userId, Integer siteId, Long timeStart, Long timeEnd, Pageable pageable);

    List<Note> getByUserIdAndSiteIdAndCreateTimeBetween(Integer userId, Integer siteId, Long timeStart, Long timeEnd, Pageable pageable);

    Page<Note> findByUserIdAndThreads(Integer userId, String threadId, Pageable pageable);

    List<Note> getByUserIdAndThreads(Integer userId, String threadId, Pageable pageable);

    Page<Note> findByUserIdAndThreadsAndCreateTimeBetween(Integer userId, String threadId, Long timeStart, Long timeEnd, Pageable pageable);

    List<Note> getByUserIdAndThreadsAndCreateTimeBetween(Integer userId, String threadId, Long timeStart, Long timeEnd, Pageable pageable);

    default Page<Note> findByThreads(String threadId, Pageable pageable) {
        Criteria criteria = Criteria.where("threads").is(threadId);
        List<Note> list = mongo().find(Query.query(criteria)
                        .with(pageable.getSort())
                        .skip(pageable.getOffset())
                        .limit(pageable.getPageSize()),
                Note.class);
        long total = mongo().getCollection("k_note").estimatedDocumentCount();
        return new PageImpl<>(list, pageable, total);
    }

    List<Note> getByThreads(String threadId, Pageable pageable);

    Page<Note> findByThreadsIn(String[] threadIds, Pageable pageable);

    @org.springframework.data.mongodb.repository.Query(
            fields = "{ '_id':1, 'audio':1, 'video':1, 'images':1, 'content':1, 'refreshTimeMS':1, 'commentCount':1, 'praiseCount':1, 'viewCount':1, 'shareCount':1 }")
    List<Note> getByThreadsOrderByIdDesc(String threadId, Pageable pageable);

    @org.springframework.data.mongodb.repository.Query(
        fields = "{ '_id':1, 'audio':1, 'video':1, 'images':1, 'content':1, 'refreshTimeMS':1, 'commentCount':1, 'praiseCount':1, 'viewCount':1, 'shareCount':1 }")
    List<Note> findAllByThreadsAndCreateTimeGreaterThanOrderByIdDesc(String threadId, Long time, Pageable pageable);

    long countByThreads(String threadId);

    long countByOriginThreadId(String originThreadId);

    long countByThreadsAndCreateTimeBetween(String threadId, Long timeStart, Long timeEnd);

    long countByOriginThreadIdAndCreateTimeBetween(String originThreadId, Long timeStart, Long timeEnd);

    long countByThreadsIn(List<String> threadIds);

    long countByUserId(Integer userId);

    long countByUserIdAndCreateTimeBetween(Integer userId, Long timeStart, Long timeEnd);

    // -------------------------------------------------------

    Page<Note> findBySiteIdAndIdGreaterThan(Integer siteId, String lastId, Pageable pageable);

    Page<Note> findByUserIdAndIdGreaterThan(Integer userId, String lastId, Pageable pageable);

    Page<Note> findByUserIdAndSiteIdAndIdGreaterThan(Integer userId, Integer siteId, String lastId, Pageable pageable);

    Page<Note> findByUserIdAndThreadsAndIdGreaterThan(Integer userId, String threadId, String lastId, Pageable pageable);

    Page<Note> findByThreadsAndIdGreaterThan(String threadId, String lastId, Pageable pageable);

    Page<Note> findByThreadsInAndIdGreaterThan(List<String> threadIds, String lastId, Pageable pageable);

    Page<Note> findByThreadsInAndIdGreaterThan(String[] threadIds, String lastId, Pageable pageable);

    Page<Note> findByThreadsAndRefreshTimeGreaterThan(String threadId, long refreshTime, Pageable pageable);

    Page<Note> findByThreadsAndRefreshTimeLessThan(String threadId, long refreshTime, Pageable pageable);

    Page<Note> findByThreadsAndCreateTimeGreaterThan(String threadId, long createTime, Pageable pageable);

    Page<Note> findByThreadsAndCreateTimeLessThan(String threadId, long createTime, Pageable pageable);

    Page<Note> findByThreadsAndRefreshTimeMSGreaterThan(String threadId, long refreshTimeMS, Pageable pageable);

    Page<Note> findByThreadsAndRefreshTimeMSLessThan(String threadId, long refreshTimeMS, Pageable pageable);

    Page<Note> findByThreadsAndCreateTimeMSGreaterThan(String threadId, long createTimeMS, Pageable pageable);

    Page<Note> findByThreadsAndCreateTimeMSLessThan(String threadId, long createTimeMS, Pageable pageable);

    Page<Note> findByThreadsAndCreateTimeMSBetween(String threadId, long startMS, long endMS, Pageable pageable);

    // -------------------------------------------------------

    default void increaseCommentCount(String noteId, int incr) {
        mongo().findAndModify(
                new Query(Criteria.where("_id").is(new ObjectId(noteId))),
                new Update().inc("commentCount", incr),
                Note.class);
    }

    default void increasePraiseCount(String noteId, int incr) {
        mongo().findAndModify(
                new Query(Criteria.where("_id").is(new ObjectId(noteId))),
                new Update().inc("praiseCount", incr),
                Note.class);
    }

    default Note updateContentIfNotEmpty(
            String noteId, Audio audio, Video video, List<Image> images, String content,
            Double longitude, Double latitude, String street, String remark) {
        long current = System.currentTimeMillis();
        return mongo().findAndModify(
                new Query(Criteria.where("_id").is(new ObjectId(noteId))),
                new KbMongoUpdate()
                        .setIfNotNull("audio", audio)
                        .setIfNotNull("video", video)
                        .setIfNotEmpty("images", images)
                        .setIfNotEmpty("content", content)
                        .setIfNotNull("longitude", longitude)
                        .setIfNotNull("latitude", latitude)
                        .setIfNotEmpty("street", street)
                        .setIfNotEmpty("remark", remark)
                        .set("updateTime", current / 1000)
                        .set("updateTimeMS", current),
                new FindAndModifyOptions().returnNew(true).upsert(false),
                Note.class);
    }

    default Note updateAudio(String noteId, Audio audio) {
        return mongo().findAndModify(
                new Query(Criteria.where("_id").is(new ObjectId(noteId))),
                new Update().set("audio", audio),
                new FindAndModifyOptions().returnNew(true).upsert(false),
                Note.class);
    }

    default Note updateAudio(String noteId, String mp3Key, String amrKey, String translation, Integer translateStatus) {
        if (mp3Key == null && amrKey == null && translation == null && translateStatus == null) {
            throw new KbException(KbCode.ILLEGAL_ARGUMENT, "all update fields null");
        }
        return mongo().findAndModify(
                new Query(Criteria.where("_id").is(new ObjectId(noteId))),
                new KbMongoUpdate()
                        .setIfNotNull("audio.voiceMP3Key", mp3Key)
                        .setIfNotNull("audio.voiceAMRKey", amrKey)
                        .setIfNotNull("audio.voiceTrans", translation)
                        .setIfNotNull("audio.voiceTransStatus", translateStatus),
                new FindAndModifyOptions().returnNew(true).upsert(false),
                Note.class);
    }

    default void updateCounts(
            String noteId, Integer commentCount, Integer praiseCount, Integer viewCount, Integer shareCount) {
        if (null == commentCount && null == praiseCount && null == viewCount && null == shareCount) {
            return;
        }
        Update update = new Update();
        if (null != commentCount) {
            update.set("commentCount", commentCount);
        }
        if (null != praiseCount) {
            update.set("praiseCount", praiseCount);
        }
        if (null != viewCount) {
            update.set("viewCount", viewCount);
        }
        if (null != shareCount) {
            update.set("shareCount", shareCount);
        }
        mongo().findAndModify(new Query(Criteria.where("_id").is(new ObjectId(noteId))), update, Note.class);
    }

    default Note updateState(String noteId, NoteState state) {
        long current = System.currentTimeMillis();
        return mongo().findAndModify(
                new Query(Criteria.where("_id").is(new ObjectId(noteId))),
                new Update().set("state", state.getValue())
                    .set("updateTime", current / 1000)
                    .set("updateTimeMS", current),
                new FindAndModifyOptions().returnNew(true).upsert(false),
                Note.class);
    }

    default void updateRefreshTime(String noteId) {
        long current = System.currentTimeMillis();
        mongo().findAndModify(
                new Query(Criteria.where("_id").is(new ObjectId(noteId))),
                new Update()
                        .set("refreshTime", current / 1000)
                        .set("refreshTimeMS", current),
                Note.class);
    }

    default void updatePermission(String noteId, Boolean allowComment, Boolean allowPraise) {
        long current = System.currentTimeMillis();
        Update update = new Update()
                .set("updateTime", current / 1000)
                .set("updateTimeMS", current);
        if (null != allowComment) {
            update.set("isAllowComment", allowComment);
        }
        if (null != allowPraise) {
            update.set("isAllowPraise", allowPraise);
        }
        mongo().findAndModify(new Query(Criteria.where("_id").is(new ObjectId(noteId))), update, Note.class);
    }

    default void updateRemark(String noteId, String remark) {
        mongo().findAndModify(
                new Query(Criteria.where("_id").is(new ObjectId(noteId))),
                new Update().set("remark", remark),
                Note.class);
    }

    default void updateAnonymous(String noteId, boolean anonymous) {
        mongo().findAndModify(
                new Query(Criteria.where("_id").is(new ObjectId(noteId))),
                new Update().set("isAnonymous", anonymous),
                Note.class);
    }

    default Note updateExtra(String noteId, Map<String, String> extra) {
        return mongo().findAndModify(
                new Query(Criteria.where("_id").is(new ObjectId(noteId))),
                new Update()
                        .set("extra", extra)
                        .set("updateTime", System.currentTimeMillis() / 1000)
                        .set("updateTimeMS", System.currentTimeMillis()),
                Note.class);
    }

    default Note updateExtraKeyValue(String noteId, String key, String value) {
        return mongo().findAndModify(
                new Query(Criteria.where("_id").is(new ObjectId(noteId))),
                new KbMongoUpdate()
                        .setUnsetOnNull("extra." + key, value)
                        .set("updateTime", System.currentTimeMillis() / 1000)
                        .set("updateTimeMS", System.currentTimeMillis()),
                Note.class);
    }

    default Page<Note> getNoteListByContentOrStreet(String content, String street, String[] threadIds, Long startTime, Long endTime, Pageable pageable) {
        Query query = Query.query(Criteria.where("threads").in(Arrays.asList(threadIds)));
        if (!StringUtils.isEmpty(content) && !StringUtils.isEmpty(street)) {
            query.addCriteria(new Criteria().orOperator(
                    Criteria.where("content").regex(content),
                    Criteria.where("street").regex(street)
            ));
        } else if (!StringUtils.isEmpty(content)) {
            query.addCriteria(Criteria.where("content").regex(content));
        } else if (!StringUtils.isEmpty(street)) {
            query.addCriteria(Criteria.where("street").regex(street));
        }
        if (startTime != null && endTime != null) {
            query.addCriteria(Criteria.where("createTime").gte(startTime).lte(endTime));
        } else {
            if (startTime != null) {
                query.addCriteria(Criteria.where("createTime").gte(startTime));
            }
            if (endTime != null) {
                query.addCriteria(Criteria.where("createTime").lte(endTime));
            }
        }

        long total = mongo().count(query, Note.class);
        List<Note> noteList = mongo().find(query.with(pageable), Note.class);
        return new PageImpl<>(noteList, pageable, total);
    }

    /**
     * db.k_note.aggregate([
     * { $match: { threads: "5d149db386156300016f5ddb"  ,createTime : { $gt : 1577869977, $lte : 1591002777 }} },
     * { $group: {
     * _id: "$userId",
     * count: {$sum: 1}
     * } },
     * { $sort: { count: -1 } },
     * { $facet : {
     * metadata: [ { $count: "total" } ],
     * data: [ { $skip: 0 }, { $limit: 15 } ]
     * } }
     * ])
     */
    default NoteUserRanking getNoteUserRankingByThreadId(
            String threadId, Long startTime, Long endTime, Integer page, Integer pageSize) {
        MongoCollection<Document> collection = mongo().getCollection("k_note");
        AggregateIterable<Document> result = collection.aggregate(Arrays.asList(
                new Document("$match",
                        new Document("threads", threadId)
                                .append("createTime", new Document("$gt", startTime).append("$lte", endTime))
                ),
                new Document("$group",
                        new Document("_id", "$userId")
                                .append("count", new Document("$sum", 1))
                ),
                new Document("$sort", new Document("count", -1)),
                new Document("$facet",
                        new Document("metadata", Collections.singletonList(new Document("$count", "total")))
                                .append("data", Arrays.asList(
                                        new Document("$skip", page * pageSize),
                                        new Document("$limit", pageSize)))
                )
        ));
        NoteUserRanking ret = new NoteUserRanking();
        result.forEach(d -> {
            List<Document> metadata = (List<Document>) d.get("metadata");
            if (metadata.size() > 0) {
                ret.total = metadata.get(0).getInteger("total");
                List<Document> data = (List<Document>) d.get("data");
                ret.aggrs = new LinkedList<>();
                data.forEach(document -> {
                    NoteUserRanking.UserRanking ranking = new NoteUserRanking.UserRanking();
                    ranking.setUserId(Integer.parseInt(document.get("_id").toString()));
                    ranking.setCount((Integer) document.get("count"));
                    ret.aggrs.add(ranking);
                });
            } else {
                ret.total = 0;
                ret.aggrs = Collections.emptyList();
            }
        });
        return ret;
    }

    @org.springframework.data.mongodb.repository.Query(value = "{updateTime: {$gte: ?0, $lt: ?1}}")
    List<Note> getListByUpdateTimeBetween(Long st, Long et, Pageable pageable);

    // temporary praise ranking ---------------------------------

    default List<Note> getIdListByThreadOrderByPraiseCountDesc(String threadId, int limit) {
        Query query = Query
                .query(Criteria.where("threads").is(threadId))
                .with(Sort.by(Sort.Order.desc("praiseCount"), Sort.Order.asc("createTimeMS")))
                .limit(limit);
        query.fields().include("_id").include("praiseCount").include("userId").include("createTimeMS");
        return mongo().find(query, Note.class);
    }

    @org.springframework.data.mongodb.repository.Query(
            value = "{'threads': ?0}",
            sort = "{'praiseCount': -1, 'createTimeMS': 1}",
            fields = "{'id': 1, 'praiseCount': 1, 'userId': 1, 'createTimeMS': 1}")
    List<Note> findAllByThreadOrderByPraiseCountDesc(String threadId, Pageable pageable);

    @Aggregation(value = {
        "{$match: {threads: {$in: ?0}, createTime: {$gte : ?1, $lt : ?2}}}",
        "{$project: {_id: 1, noteCount: 1, commentCount: 1, praiseCount: 1, shareCount: 1, " +
            "images: {$cond: [{$gt: ['$images', null]},1,0]}, " +
            "video: {$cond: [{$gt: ['$video', null]},1,0]}, " +
            "audio: {$cond: [{$gt: ['$audio', null]},1,0]}, " +
            "timeRange: {$floor: {$divide: ['$createTime', 60]}}}}",
        "{$group: {_id: '$timeRange', noteCount: {$sum: 1}, commentCount: {$sum: '$commentCount'}," +
            " praiseCount: {$sum: '$praiseCount'}, shareCount: {$sum: '$shareCount'}, imagesCount: {$sum: '$images'}," +
            " videoCount: {$sum: '$video'}, audioCount: {$sum: '$audio'}}}",
        "{$project: {_id: 0, startTime: {$multiply: ['$_id', 60]}, noteCount: '$noteCount', commentCount: '$commentCount'," +
            " praiseCount: '$praiseCount', shareCount: '$shareCount', imagesCount: '$imagesCount'," +
            " videoCount: '$videoCount', audioCount: '$audioCount'}}"
    })
    List<DatavProgramNoteSummaryModel> aggrProgramNoteSummaryByMinute(String[] threadIds, Long startTime, Long endTime);

    @Aggregation(value = {
        "{$match: {threads: {$in: ?0}, createTime: {$gte : ?1, $lt : ?2}}}",
        "{$project: {_id: 1, viewCount: 1, commentCount: 1, praiseCount: 1, shareCount: 1, " +
            "images: {$cond: [{$gt: ['$images', null]},1,0]}, " +
            "video: {$cond: [{$gt: ['$video', null]},1,0]}, " +
            "audio: {$cond: [{$gt: ['$audio', null]},1,0]}, " +
            "timeRange: {$dateToString: {format: '%Y-%m-%d', date: {$add: [new Date(0), 28800000, {$multiply: ['$createTime', 1000]}]}}}}}",
        "{$group: {_id: '$timeRange', noteCount: {$sum: 1}, commentCount: {$sum: '$commentCount'}," +
            " praiseCount: {$sum: '$praiseCount'}, shareCount: {$sum: '$shareCount'}, imagesCount: {$sum: '$images'}," +
            " videoCount: {$sum: '$video'}, audioCount: {$sum: '$audio'}}}",
        "{$project: {_id: 0, date: '$_id', noteCount: '$noteCount', commentCount: '$commentCount'," +
            " praiseCount: '$praiseCount', shareCount: '$shareCount', imagesCount: '$imagesCount'," +
            " videoCount: '$videoCount', audioCount: '$audioCount'}}"
    })
    List<DatavProgramNoteSummaryModel> aggrProgramNoteSummaryByDay(String[] threadIds, Long startTime, Long endTime);
}