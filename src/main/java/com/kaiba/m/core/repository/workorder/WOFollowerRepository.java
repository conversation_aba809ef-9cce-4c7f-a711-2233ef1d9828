package com.kaiba.m.core.repository.workorder;

import com.kaiba.lib.base.domain.workorder.WOACLStringData;
import com.kaiba.m.core.domain.workorder.WOFollower;
import com.kaiba.m.core.middleware.mongo.interfaces.KbMongoRepository;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.core.FindAndModifyOptions;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * author: lyux
 * date: 2023-08-11
 */
@Repository
public interface WOFollowerRepository extends KbMongoRepository<WOFollower, String> {

    String ONLY_CASE_ID = "{ '_id':1, 'caseId':1 }";

    Optional<WOFollower> findFirstByUserIdAndAndCaseId(Integer userId, String caseId);

    Page<WOFollower> findByCaseIdOrderByIdDesc(String caseId, Pageable pageable);

    @org.springframework.data.mongodb.repository.Query(value = "{ 'userId' : ?0 }", fields = ONLY_CASE_ID)
    Page<WOFollower> getByUserIdOrderByIdDesc(Integer userId, Pageable pageable);

    @org.springframework.data.mongodb.repository.Query(value = "{ 'userId' : ?0 , 'biz' :  ?1}", fields = ONLY_CASE_ID)
    Page<WOFollower> getByUserIdAndBizOrderByIdDesc(Integer userId, String biz, Pageable pageable);

    long countByBiz(String biz);

    long countByCaseId(String caseId);

    boolean existsByUserIdAndCaseId(Integer userId, String caseId);

    void deleteByUserIdAndCaseId(Integer userId, String caseId);

    default WOFollower upsert(String caseId, String biz, Integer userId, String message, List<WOACLStringData> info) {
        Update update = new Update()
                .setOnInsert("caseId", caseId)
                .setOnInsert("userId", userId)
                .setOnInsert("biz", biz)
                .setOnInsert("createTime", System.currentTimeMillis());
        if (info != null && info.size() != 0) {
            update.set("info", info);
        }
        if (message != null) {
            update.set("message", message);
        }
        return mongo().findAndModify(
                new Query(Criteria.where("caseId").is(caseId).and("userId").is(userId)), update,
                new FindAndModifyOptions().returnNew(true).upsert(true),
                WOFollower.class);
    }

}
