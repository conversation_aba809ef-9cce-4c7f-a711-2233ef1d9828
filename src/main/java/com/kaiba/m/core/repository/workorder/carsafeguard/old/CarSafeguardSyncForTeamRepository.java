package com.kaiba.m.core.repository.workorder.carsafeguard.old;

import com.kaiba.m.core.domain.workorder.carsafeguard.old.CarSafeguardSyncForTeam;
import com.kaiba.m.core.middleware.mongo.interfaces.KbMongoRepository;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 * @version CarSafeguardSyncForTeamRepository, v0.1 2024/8/29 16:10 daopei Exp $
 **/
@Repository
public interface CarSafeguardSyncForTeamRepository extends KbMongoRepository<CarSafeguardSyncForTeam, Integer> {

}
