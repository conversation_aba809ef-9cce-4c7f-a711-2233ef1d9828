package com.kaiba.m.core.repository.workorder;

import com.kaiba.lib.base.constant.workorder.WOTagState;
import com.kaiba.m.core.domain.workorder.WOTag;
import com.kaiba.m.core.middleware.mongo.interfaces.KbMongoRepository;
import org.bson.types.ObjectId;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.core.FindAndModifyOptions;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.List;
import java.util.Optional;

/**
 * author: lyux
 * date: 2024-03-28
 */
@Repository
public interface WOTagRepository extends KbMongoRepository<WOTag, String> {

    Optional<WOTag> findFirstByCode(long code);

    Optional<WOTag> findFirstByKey(String key);

    Optional<WOTag> findFirstByParentAndName(long group, String name);

    List<WOTag> getByIdIn(Collection<String> tagIds);

    List<WOTag> getByCodeIn(Collection<Long> codes);

    List<WOTag> getByKeyIn(Collection<String> keys);

    List<WOTag> getByParentOrderByIdDesc(long parent, Pageable pageable);

    Page<WOTag> findByParentOrderByIdDesc(long parent, Pageable pageable);

    long countByParent(long group);

    default void updateKeyAsId(String tagId) {
        updateKey(tagId, tagId);
    }

    default void updateKey(String tagId, String key) {
        mongo().findAndModify(
                new Query(Criteria.where("_id").is(new ObjectId(tagId))),
                new Update().set("key", key),
                new FindAndModifyOptions().returnNew(false).upsert(false),
                WOTag.class);
    }

    default WOTag updateState(String tagId, WOTagState state) {
        return mongo().findAndModify(
                new Query(Criteria.where("_id").is(new ObjectId(tagId))),
                new Update().set("state", state.name()),
                new FindAndModifyOptions().returnNew(true).upsert(false),
                WOTag.class);
    }

    default WOTag updateName(String tagId, String name) {
        return mongo().findAndModify(
                new Query(Criteria.where("_id").is(new ObjectId(tagId))),
                new Update().set("name", name),
                new FindAndModifyOptions().returnNew(true).upsert(false),
                WOTag.class);
    }

    default WOTag updateDesc(String tagId, String desc) {
        return mongo().findAndModify(
                new Query(Criteria.where("_id").is(new ObjectId(tagId))),
                new Update().set("desc", desc),
                new FindAndModifyOptions().returnNew(true).upsert(false),
                WOTag.class);
    }

    default WOTag incrTagCount(long code) {
        return mongo().findAndModify(
                new Query(Criteria.where("code").is(code)),
                new Update().inc("count", 1),
                new FindAndModifyOptions().returnNew(true).upsert(false),
                WOTag.class);
    }

}
