package com.kaiba.m.core.repository.workorder.carsafeguard;

import com.kaiba.m.core.domain.workorder.carsafeguard.CarSafeguardCaseContent;
import com.kaiba.m.core.middleware.mongo.interfaces.KbMongoRepository;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @version CarSafeguard4SCaseContentRepository, v0.1 2024/7/12 16:34 daopei Exp $
 **/
@Repository
public interface CarSafeguardCaseContentRepository extends KbMongoRepository<CarSafeguardCaseContent, String> {

    Optional<CarSafeguardCaseContent> findByContentId(String contentId);
    List<CarSafeguardCaseContent> findByIdIn(Collection<String> ids);

    List<CarSafeguardCaseContent> findByContentIdIn(Collection<String> contentIds);
}
