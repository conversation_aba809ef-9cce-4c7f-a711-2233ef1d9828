package com.kaiba.m.core.repository.news.pool;

import com.kaiba.m.core.domain.news.pool.bytag.IdsTagGroup;
import com.kaiba.m.core.middleware.mongo.interfaces.KbMongoRepository;
import org.bson.types.ObjectId;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.core.FindAndModifyOptions;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Repository;

import java.util.Optional;

/**
 * author: lyux
 * date: 2022-09-07
 */
@Repository
public interface IdsTagGroupRepository extends KbMongoRepository<IdsTagGroup, String> {

    Optional<IdsTagGroup> findFirstByKey(String groupKey);

    Page<IdsTagGroup> findByOrderByIdDesc(Pageable pageable);

    Page<IdsTagGroup> findBySiteIdOrderByIdDesc(Integer siteId, Pageable pageable);

    default IdsTagGroup updateMax(String groupId, Integer max) {
        return mongo().findAndModify(
                new Query(Criteria.where("_id").is(new ObjectId(groupId))),
                new Update()
                        .set("max", max)
                        .set("updateTime", System.currentTimeMillis() / 1000),
                new FindAndModifyOptions().returnNew(true),
                IdsTagGroup.class);
    }

}
