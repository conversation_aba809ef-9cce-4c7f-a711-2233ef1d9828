package com.kaiba.m.core.repository.news.pool;

import com.kaiba.m.core.domain.news.pool.bygroup.GroupSelector;
import com.kaiba.m.core.middleware.mongo.interfaces.KbMongoRepository;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.core.FindAndModifyOptions;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;
import java.util.regex.Pattern;


/**
 * author: lyux
 * date: 2024-08-15
 */
@Repository
public interface GroupSelectorRepository extends KbMongoRepository<GroupSelector, String> {

    Optional<GroupSelector> findFirstBySelector(String selector);

    Page<GroupSelector> findByOrderByIdDesc(Pageable pageable);

    default Page<GroupSelector> searchByName(String keyword, Pageable pageable) {
        Pattern pattern = Pattern.compile("^.*" + keyword + ".*$", Pattern.CASE_INSENSITIVE);
        Criteria criteria = Criteria.where("name").regex(pattern);
        Query query = new Query(criteria);
        long total = mongo().count(query, GroupSelector.class);
        List<GroupSelector> list = mongo().find(query.with(pageable), GroupSelector.class);
        return new PageImpl<>(list, pageable, total);
    }

    default GroupSelector updateData(String selector, String name) {
        return mongo().findAndModify(
                new Query(Criteria.where("selector").is(selector)),
                new Update()
                        .set("name", name)
                        .set("updateTime", System.currentTimeMillis()),
                FindAndModifyOptions.options().upsert(false).returnNew(true),
                GroupSelector.class);
    }

}
