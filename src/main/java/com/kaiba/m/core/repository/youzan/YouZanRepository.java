package com.kaiba.m.core.repository.youzan;

import com.kaiba.m.core.domain.youzan.YouZanBindingState;
import com.kaiba.m.core.middleware.mongo.interfaces.KbMongoRepository;
import com.kaiba.m.core.middleware.mongo.utils.KbMongoUpdate;

import org.springframework.data.mongodb.core.FindAndModifyOptions;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Repository;

import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2022/07/06 14:45
 **/
@Repository
public interface YouZanRepository extends KbMongoRepository<YouZanBindingState, String> {

    /**
     * 根据用户id查找用户绑定状态
     * @param userId 用户id
     * @param mobile 手机号
     * @return 数据
     */
    Optional<YouZanBindingState> findFirstByUserIdAndMobile(Integer userId, String mobile);

    /**
     * 根据用户id查找用户绑定状态
     * @param userId 用户id
     * @return 数据
     */
    Optional<YouZanBindingState> findFirstByUserId(Integer userId);

    /**
     * 根据用户id查找用户绑定状态
     * @param mobile 手机号
     * @return 数据
     */
    Optional<YouZanBindingState> findFirstByMobile(String mobile);

    /**
     * 解除userid的绑定
     * @param userId 用户id
     * @param mobile 手机号
     * @return 数据
     */
    default YouZanBindingState bindYouZanStore(Integer userId, String mobile) {
        return mongo().findAndModify(
            new Query(Criteria.where("mobile").is(mobile).and("userId").exists(false)),
            new KbMongoUpdate()
                .set("userId", userId)
                .set("updateTime", System.currentTimeMillis()),
            new FindAndModifyOptions().returnNew(true).upsert(false),
            YouZanBindingState.class);
    }

    /**
     * 解除userid的绑定
     * @param userId 用户id
     * @return 数据
     */
    default YouZanBindingState unbindYouZanStore(Integer userId) {
        return mongo().findAndModify(
            new Query(Criteria.where("userId").is(userId)),
            new KbMongoUpdate()
                .setUnsetOnNull("userId", null)
                .set("updateTime", System.currentTimeMillis()),
            new FindAndModifyOptions().returnNew(true).upsert(false),
            YouZanBindingState.class);
    }

    /**
     * 修改openid
     * @param mobile 手机号
     * @param openId 有赞开放id
     * @return 数据
     */
    default YouZanBindingState updateOpenIdByMobile(String mobile, String openId) {
        return mongo().findAndModify(
            new Query(Criteria.where("mobile").is(mobile)),
            new KbMongoUpdate()
                .setIfNotNull("openId", openId)
                .set("updateTime", System.currentTimeMillis()),
            new FindAndModifyOptions().returnNew(true).upsert(false),
            YouZanBindingState.class);
    }

    /**
     * 修改绑定状态
     * @param userId 用户id
     * @param state 状态
     * @param mobile 手机号
     * @return 数据
     */
    default YouZanBindingState updateState(Integer userId, Integer state, String mobile) {
        return mongo().findAndModify(
            new Query(Criteria.where("mobile").is(mobile)),
            new KbMongoUpdate()
                .setIfNotNull("state", state)
                .setOnInsert("userId", userId)
                .set("updateTime", System.currentTimeMillis()),
            new FindAndModifyOptions().returnNew(true).upsert(true),
            YouZanBindingState.class);
    }
}
