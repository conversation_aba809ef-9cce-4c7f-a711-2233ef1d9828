package com.kaiba.m.core.repository.news.article;

import com.kaiba.lib.base.constant.news.article.NewsState;
import com.kaiba.lib.base.domain.common.KbTimeRange;
import com.kaiba.lib.base.domain.news.article.ArticleQueryModel;
import com.kaiba.lib.base.response.KbCode;
import com.kaiba.lib.base.response.KbException;
import com.kaiba.lib.base.util.StringUtils;
import com.kaiba.m.core.domain.news.article.NewsArticle;
import com.kaiba.m.core.middleware.mongo.interfaces.KbMongoRepository;
import com.kaiba.m.core.middleware.mongo.utils.KbMongoUpdate;
import org.bson.types.ObjectId;
import org.springframework.data.domain.*;
import org.springframework.data.mongodb.core.BulkOperations;
import org.springframework.data.mongodb.core.FindAndModifyOptions;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.data.util.Pair;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * author: lyux
 * date: 2022-09-07
 */
@Repository
public interface NewsArticleRepository extends KbMongoRepository<NewsArticle, String> {

    Page<NewsArticle> findBySiteIdOrderByIdDesc(Integer siteId, Pageable pageable);

    List<NewsArticle> findByChannelKeyOrderByIdDesc(String channelKey, Pageable pageable);

    List<NewsArticle> findByChannelKeyAndIdLessThanOrderByIdDesc(String channelKey, String lastId, Pageable pageable);

    List<NewsArticle> findByIdIn(Collection<String> ids);

    default NewsArticle updateState(String articleId, NewsState state, long time) {
        Update update = new Update()
                .set("state", state.name())
                .set("updateTime", time);
        switch (state) {
            case DRAFT:
                update.unset("signTime").unset("onlineTime").unset("archiveTime");
                break;
            case SIGNED:
                update.set("signTime", time).unset("onlineTime");
                break;
            case ONLINE:
                update.set("onlineTime", time);
                break;
            case ARCHIVED:
                update.set("archiveTime", time);
                break;
        }
        return mongo().findAndModify(
                new Query(Criteria.where("_id").is(new ObjectId(articleId))), update,
                new FindAndModifyOptions().returnNew(true),
                NewsArticle.class);
    }

    default void attachThreadId(String articleId, String threadId) {
        mongo().findAndModify(
                new Query(Criteria.where("_id").is(new ObjectId(articleId))),
                new Update().set("threadId", threadId),
                new FindAndModifyOptions().returnNew(false),
                NewsArticle.class);
    }

    default NewsArticle updateAttr(String id, Map<String, String> attr, boolean replace) {
        Update update = new Update().set("updateTime", System.currentTimeMillis());
        if (replace) {
            if (attr.isEmpty()) {
                update.unset("attr");
            } else {
                update.set("attr", attr);
            }
        } else {
            attr.forEach((k, v) -> {
                if (!StringUtils.isEmpty(k)) {
                    if (StringUtils.isEmpty(v)) {
                        update.unset("attr." + k);
                    } else {
                        update.set("attr." + k, v);
                    }
                }
            });
        }
        return mongo().findAndModify(
                new Query(Criteria.where("_id").is(new ObjectId(id))), update,
                FindAndModifyOptions.options().upsert(false).returnNew(true),
                NewsArticle.class);
    }

    default NewsArticle updateAttrKeyValue(String id, String key, String value) {
        if (StringUtils.isEmpty(key)) {
            throw new KbException(KbCode.ILLEGAL_ARGUMENT, "missing attr key");
        }
        return mongo().findAndModify(
                new Query(Criteria.where("_id").is(new ObjectId(id))),
                new KbMongoUpdate()
                        .setUnsetOnNull("attr." + key, value)
                        .set("updateTime", System.currentTimeMillis()),
                FindAndModifyOptions.options().upsert(false).returnNew(true),
                NewsArticle.class);
    }

    default NewsArticle removeAttrByKey(String id, String key) {
        if (StringUtils.isEmpty(key)) {
            throw new KbException(KbCode.ILLEGAL_ARGUMENT, "missing attr key");
        }
        return mongo().findAndModify(
                new Query(Criteria.where("_id").is(new ObjectId(id))),
                new Update()
                        .unset("attr." + key)
                        .set("updateTime", System.currentTimeMillis()),
                FindAndModifyOptions.options().upsert(false).returnNew(true),
                NewsArticle.class);
    }

    default void addModule(String articleId, String module) {
        mongo().findAndModify(
                new Query(Criteria.where("_id").is(new ObjectId(articleId))),
                new Update().addToSet("moduleIndex", module),
                new FindAndModifyOptions().returnNew(false),
                NewsArticle.class);
    }

    default void removeModule(String articleId, String module) {
        mongo().findAndModify(
                new Query(Criteria.where("_id").is(new ObjectId(articleId))),
                new Update().pull("moduleIndex", module),
                new FindAndModifyOptions().returnNew(false),
                NewsArticle.class);
    }

    default void addGroup(String articleId, String group) {
        mongo().findAndModify(
                new Query(Criteria.where("_id").is(new ObjectId(articleId))),
                new Update().addToSet("groups", group),
                new FindAndModifyOptions().returnNew(false),
                NewsArticle.class);
    }

    default void addGroupBatch(Map<String, String> param) {
        BulkOperations operations = mongo().bulkOps(BulkOperations.BulkMode.ORDERED, NewsArticle.class);
        List<Pair<Query, Update>> updates = param.entrySet().stream().
                map(m -> Pair.of(
                        new Query(Criteria.where("_id").is(new ObjectId(m.getKey()))),
                        new Update().addToSet("groups", m.getValue())))
                .collect(Collectors.toList());
        operations.updateMulti(updates);
        operations.execute();
    }

    default void removeGroup(String articleId, String group) {
        mongo().findAndModify(
                new Query(Criteria.where("_id").is(new ObjectId(articleId))),
                new Update().pull("groups", group),
                new FindAndModifyOptions().returnNew(false),
                NewsArticle.class);
    }

    default void updateGroups(String articleId, Set<String> groups) {
        mongo().findAndModify(
                new Query(Criteria.where("_id").is(new ObjectId(articleId))),
                new KbMongoUpdate().setUnsetOnEmpty("groups", groups),
                new FindAndModifyOptions().returnNew(false),
                NewsArticle.class);
    }

    default void updateChannel(String articleId, String channelKey, String departKey) {
        mongo().findAndModify(
                new Query(Criteria.where("_id").is(new ObjectId(articleId))),
                new KbMongoUpdate()
                        .setUnsetOnNull("departKey", departKey)
                        .setUnsetOnNull("channelKey", channelKey),
                new FindAndModifyOptions().returnNew(false),
                NewsArticle.class);
    }

    default void attachQuickReplyCategoryToNews(String articleId, String qrCategoryId) {
        mongo().findAndModify(
                new Query(Criteria.where("_id").is(articleId)),
                new Update().set("qrCategoryId", qrCategoryId),
                NewsArticle.class);
    }

    default void removeQuickReplyCategoryFromNews(String articleId) {
        mongo().findAndModify(
                new Query(Criteria.where("_id").is(articleId)),
                new Update().unset("qrCategoryId"),
                NewsArticle.class);
    }

    default Page<NewsArticle> getPageByQuery(ArticleQueryModel model) {
        if (null == model) {
            return Page.empty();
        }
        Criteria criteria = new Criteria();
        if (model.getSiteId() != null) {
            criteria = criteria.and("siteId").is(model.getSiteId());
        }
        if (model.getChannel() != null) {
            criteria = criteria.and("channelKey").is(model.getChannel());
        }
        if (model.getGroup() != null) {
            criteria = criteria.and("groups").is(model.getGroup());
        }
        if (model.getModule() != null) {
            criteria = criteria.and("moduleIndex").is(model.getModule());
        }
        if (model.getCreator() != null) {
            criteria = criteria.and("creator").is(model.getCreator());
        }
        if (model.getStates() != null) {
            if (model.getStates().size() == 1) {
                criteria = criteria.and("state").is(model.getStates().get(0));
            } else {
                criteria = criteria.and("state").in(model.getStates());
            }
        } else if (model.getState() != null) {
            criteria = criteria.and("state").is(model.getState());
        }

        if (model.getBusinessType() != null) {
            criteria = criteria.and("businessType").is(model.getBusinessType());
        }
        if (model.getThreadId() != null) {
            criteria = criteria.and("threadId").is(model.getThreadId());
        }
        if (model.getRenderers() != null) {
            if (model.getRenderers().size() == 1) {
                criteria = criteria.and("renderer").is(model.getRenderers().get(0));
            } else {
                criteria = criteria.and("renderer").in(model.getRenderers());
            }
        }
        if (model.getCreateTimeRange() != null) {
            criteria = timeRangeCriteria(model.getCreateTimeRange(), "createTime", criteria);
        } else if (model.getSignTimeRange() != null) {
            criteria = timeRangeCriteria(model.getSignTimeRange(), "signTime", criteria);
        } else if (model.getOnlineTimeRange() != null) {
            criteria = timeRangeCriteria(model.getOnlineTimeRange(), "onlineTime", criteria);
        } else if (model.getUpdateTimeRange() != null) {
            criteria = timeRangeCriteria(model.getUpdateTimeRange(), "updateTime", criteria);
        } else if (model.getReleaseTimeRange() != null) {
            criteria = timeRangeCriteria(model.getReleaseTimeRange(), "releaseTime", criteria);
        }
        if (model.getSearchBy() != null) {
            Pattern pattern = Pattern.compile("^.*" + model.getSearchBy() + ".*$", Pattern.CASE_INSENSITIVE);
            criteria = criteria.orOperator(
                    Criteria.where("title").regex(pattern),
                    Criteria.where("subtitle").regex(pattern));
        }
        int p = model.getPage() == null ? 0 : model.getPage() - 1;
        int ps = model.getPageSize() == null ? 20 : model.getPageSize();
        Pageable pageable = PageRequest.of(p, ps, Sort.Direction.DESC, "_id");
        Query query = new Query(criteria);
        long total = mongo().count(query, NewsArticle.class);
        List<NewsArticle> list = mongo().find(query.with(pageable), NewsArticle.class);
        return new PageImpl<>(list, pageable, total);
    }

    default Criteria timeRangeCriteria(KbTimeRange timeRange, String field, Criteria criteria) {
        if (timeRange.isUnbounded()) {
            return criteria;
        }
        Criteria c = criteria.and(field);
        if (timeRange.getLower() != null) {
            if (timeRange.isLowerOpen()) {
                c.gt(timeRange.obtainLowerInMillis());
            } else {
                c.gte(timeRange.obtainLowerInMillis());
            }
        }
        if (timeRange.getUpper() != null) {
            if (timeRange.isUpperOpen()) {
                c.lt(timeRange.obtainUpperInMillis());
            } else {
                c.lte(timeRange.obtainUpperInMillis());
            }
        }
        return c;
    }

    default NewsArticle updateTime(String id, Long createTime, Long updateTime) {
        Update update = new Update().set("updateTime", updateTime).set("createTime", createTime);
        return mongo().findAndModify(
            new Query(Criteria.where("_id").is(new ObjectId(id))), update,
            FindAndModifyOptions.options().upsert(false).returnNew(true),
            NewsArticle.class);
    }

}
