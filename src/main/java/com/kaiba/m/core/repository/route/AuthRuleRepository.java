package com.kaiba.m.core.repository.route;

import com.kaiba.lib.base.constant.route.KbRouteRuleState;
import com.kaiba.m.core.domain.route.KbAuthRule;
import com.kaiba.m.core.middleware.mongo.interfaces.KbMongoRepository;
import org.bson.types.ObjectId;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.core.FindAndModifyOptions;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * author: lyux
 * date: 2021-03-24
 */
@Repository
public interface AuthRuleRepository extends KbMongoRepository<KbAuthRule, String> {

    List<KbAuthRule> findByIdIn(String[] ids);

    Page<KbAuthRule> findByOrderByIdDesc(Pageable pageable);

    Page<KbAuthRule> findByStateOrderByIdDesc(Integer state, Pageable pageable);

    Page<KbAuthRule> findByScopeOrderByIdDesc(Integer scope, Pageable pageable);

    Page<KbAuthRule> findByStateAndScopeOrderByIdDesc(Integer state, Integer scope, Pageable pageable);

    Page<KbAuthRule> findByMethodOrderByIdDesc(Integer method, Pageable pageable);

    Page<KbAuthRule> findByMethodAndScopeOrderByIdDesc(Integer method, Integer scope, Pageable pageable);

    default void updateState(String id, KbRouteRuleState state) {
        mongo().findAndModify(
                new Query(Criteria.where("_id").is(new ObjectId(id))),
                new Update()
                        .set("state", state.getValue())
                        .set("updateTime", System.currentTimeMillis()),
                FindAndModifyOptions.options().upsert(false).returnNew(true),
                KbAuthRule.class);
    }

}
