package com.kaiba.m.core.repository.prize;

import com.kaiba.lib.base.constant.prize.PrizeConfirmState;
import com.kaiba.lib.base.constant.prize.PrizeGiveUpReason;
import com.kaiba.lib.base.constant.prize.PrizeState;
import com.kaiba.lib.base.domain.prize.PrizeConfirmResult;
import com.kaiba.lib.base.response.KbCode;
import com.kaiba.lib.base.response.KbException;
import com.kaiba.m.core.domain.prize.Prize;
import com.kaiba.m.core.middleware.mongo.interfaces.KbMongoRepository;
import com.kaiba.m.core.middleware.mongo.utils.KbMongoBeanUpdate;
import com.kaiba.m.core.middleware.mongo.utils.KbMongoUpdate;
import com.mongodb.client.result.UpdateResult;
import org.bson.types.ObjectId;
import org.springframework.data.domain.*;
import org.springframework.data.mongodb.core.FindAndModifyOptions;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * author: lyux
 * date: 19-10-25
 */
@Repository
public interface PrizeRepository extends KbMongoRepository<Prize, String> {

    Optional<Prize> findFirstByNoOrderByCreateTimeDesc(String no);

    Page<Prize> findByState(Integer state, Pageable pageable);

    Page<Prize> findByDispatchMethodIdIn(List<String> dispatchMethodIsList, Pageable pageable);

    List<Prize> findByReferenceId1(String referenceId1, Pageable pageable);

    List<Prize> findByReferenceId1AndIdLessThan(String referenceId1, String id, Pageable pageable);

    List<Prize> findByIdIn(String[] prizeIds);

    default long getCountWithPrize(Prize prize) {
        if (null == prize) {
            return 0;
        }
        return mongo().count(createQueryByPrize(prize), Prize.class);
    }

    default Page<Prize> getListWithPrize(Prize prize, Pageable pageable) {
        if (null == prize) {
            return Page.empty();
        }
        if (pageable == null) {
            pageable = PageRequest.of(0, 15, Sort.by(Sort.Direction.DESC, "_id"));
        }
        Query query = createQueryByPrize(prize);
        long total = mongo().count(query, Prize.class);
        List<Prize> prizeList = mongo().find(query.with(pageable), Prize.class);
        return new PageImpl<>(prizeList, pageable, total);
    }

    default Prize upsertByReferenceId(Prize prize) {
        if (null == prize) {
            throw new KbException(KbCode.ILLEGAL_ARGUMENT, "prize null");
        }
        if (null == prize.getReferenceId1() && null == prize.getReferenceId2() && null == prize.getReferenceId3()) {
            throw new KbException(KbCode.ILLEGAL_ARGUMENT, "prize reference null: " + prize);
        }
        Criteria criteria = new Criteria();
        if (prize.getReferenceId1() != null) {
            criteria.and("referenceId1").is(prize.getReferenceId1());
        }
        if (prize.getReferenceId2() != null) {
            criteria.and("referenceId2").is(prize.getReferenceId2());
        }
        if (prize.getReferenceId3() != null) {
            criteria.and("referenceId3").is(prize.getReferenceId3());
        }
        return mongo().findAndModify(
                new Query(criteria),
                new KbMongoBeanUpdate(prize).setAllField(),
                new FindAndModifyOptions().returnNew(true).upsert(true),
                Prize.class);
    }

    default Prize updateState(String prizeId, PrizeState state) {
        return mongo().findAndModify(
                new Query(Criteria.where("_id").is(new ObjectId(prizeId))),
                new Update().set("state", state.getValue()).set("updateTime", System.currentTimeMillis() / 1000),
                new FindAndModifyOptions().returnNew(true).upsert(false),
                Prize.class);
    }

    default Prize updateStateToDelivering(String prizeId, String address) {
        return mongo().findAndModify(
                new Query(Criteria.where("_id").is(new ObjectId(prizeId))),
                new Update()
                        .set("state", PrizeState.DELIVERY_IN_PROGRESS.getValue())
                        .set("userAddress", address)
                        .set("updateTime", System.currentTimeMillis() / 1000),
                new FindAndModifyOptions().returnNew(true).upsert(false),
                Prize.class);
    }

    default Prize giveUp(String prizeId, PrizeGiveUpReason reason) {
        return mongo().findAndModify(
                new Query(Criteria.where("_id").is(new ObjectId(prizeId))
                        .and("state").ne(PrizeState.CLAIMED.getValue())),
                new Update()
                        .set("state", PrizeState.GIVE_UP.getValue())
                        .set("giveUpReason", reason.getValue())
                        .set("updateTime", System.currentTimeMillis() / 1000),
                new FindAndModifyOptions().returnNew(true).upsert(false),
                Prize.class);
    }

    default long checkAndExpirePrize() {
        long current = System.currentTimeMillis() / 1000;
        UpdateResult result = mongo().updateMulti(
                new Query(Criteria
                        .where("expireTime").lt(current)
                        .and("state").is(PrizeState.NOT_CLAIMED.getValue())),
                new Update()
                        .set("state", PrizeState.GIVE_UP.getValue())
                        .set("giveUpReason", PrizeGiveUpReason.TIMEOUT.getValue())
                        .set("updateTime", current),
                Prize.class);
        return result.getMatchedCount();
    }

    default Prize writeOff(String prizeId, String transactionId, Integer writeOffUserId) {
        Update update = new Update()
                .set("state", PrizeState.CLAIMED.getValue())
                .set("updateTime", System.currentTimeMillis() / 1000);
        if (writeOffUserId != null) {
            update.set("writeOffUserId", writeOffUserId);
        }
        if (transactionId != null) {
            update.set("transactionId", transactionId);
        }
        return mongo().findAndModify(
                new Query(Criteria.where("_id").is(new ObjectId(prizeId))
                        .and("state").is(PrizeState.NOT_CLAIMED.getValue())),
                update,
                new FindAndModifyOptions().returnNew(true).upsert(false),
                Prize.class);
    }

    default Prize updateRedirectLink(String prizeId, String redirectLink) {
        return mongo().findAndModify(
                new Query(Criteria.where("_id").is(new ObjectId(prizeId))),
                new Update().set("redirectLink", redirectLink),
                new FindAndModifyOptions().returnNew(true).upsert(false),
                Prize.class);
    }

    default Prize updateShipmentId(String prizeId, String shipmentId) {
        return mongo().findAndModify(
                new Query(Criteria.where("_id").is(new ObjectId(prizeId))),
                new Update().set("shipmentId", shipmentId),
                new FindAndModifyOptions().returnNew(true).upsert(false),
                Prize.class);
    }

    default Prize updateAttrs(String prizeId, Map<String, String> attrs) {
        return mongo().findAndModify(
                new Query(Criteria.where("_id").is(new ObjectId(prizeId))),
                new Update()
                        .set("attrs", attrs)
                        .set("updateTime", System.currentTimeMillis() / 1000),
                Prize.class);
    }

    default Prize updateAttrsKeyValue(String prizeId, String key, String value) {
        return mongo().findAndModify(
                new Query(Criteria.where("_id").is(new ObjectId(prizeId))),
                new KbMongoUpdate()
                        .setUnsetOnNull("attrs." + key, value)
                        .set("updateTime", System.currentTimeMillis() / 1000),
                Prize.class);
    }

    default Prize updateTransactionId(String prizeId, String transactionId) {
        return mongo().findAndModify(
                new Query(Criteria.where("_id").is(new ObjectId(prizeId))),
                new Update().set("transactionId", transactionId),
                new FindAndModifyOptions().returnNew(true).upsert(false),
                Prize.class);
    }

    default Prize updateConfirmStateAsConfirming(String prizeId, Long v) {
        return mongo().findAndModify(
                new Query(Criteria
                        .where("_id").is(new ObjectId(prizeId))
                        .orOperator(
                                Criteria.where("confirmState").is(PrizeConfirmState.CONFIRM_PENDING.getValue()),
                                Criteria.where("confirmState").is(PrizeConfirmState.CONFIRM_ON_GOING.getValue())
                        )
                        .and("v").is(v)),
                new Update()
                        .set("confirmState", PrizeConfirmState.CONFIRM_ON_GOING.getValue())
                        .inc("v", 1),
                new FindAndModifyOptions().returnNew(true).upsert(false),
                Prize.class);
    }

    default Prize updateConfirmStateAsConfirmed(String prizeId, PrizeConfirmResult result, boolean updateAsClaimed, Long v) {
        Update update = new KbMongoBeanUpdate(result)
                .setIfNotNull("coupon")
                .setIfNotNull("referenceId1")
                .setIfNotNull("referenceId2")
                .setIfNotNull("referenceId3")
                .setIfNotNull("shipmentId")
                .setIfNotNull("userName")
                .setIfNotNull("userMobile")
                .setIfNotNull("userAddress")
                .setIfNotNull("redirectLink")
                .setIfNotNull("description")
                .setIfNotEmpty("links")
                .set("confirmState", PrizeConfirmState.CONFIRMED.getValue())
                .set("confirmTime", System.currentTimeMillis() / 1000)
                .inc("v", 1);
        if (updateAsClaimed) {
            update.set("state", PrizeState.CLAIMED.getValue())
                    .set("updateTime", System.currentTimeMillis() / 1000);
        }
        return mongo().findAndModify(
                new Query(Criteria
                        .where("_id").is(new ObjectId(prizeId))
                        .orOperator(
                                Criteria.where("confirmState").is(PrizeConfirmState.CONFIRM_PENDING.getValue()),
                                Criteria.where("confirmState").is(PrizeConfirmState.CONFIRM_ON_GOING.getValue())
                        )
                        .and("v").is(v)),
                update,
                new FindAndModifyOptions().returnNew(true).upsert(false),
                Prize.class);
    }

    default Prize updateUserInfo(
            String prizeId, Integer userId, String userName, String userMobile,
            String contactMobile, String addressId) {
        return mongo().findAndModify(
                new Query(Criteria.where("_id").is(new ObjectId(prizeId)).and("userId").is(userId)),
                new KbMongoUpdate()
                        .setIfNotEmpty("userName", userName)
                        .setIfNotEmpty("userMobile", userMobile)
                        .setIfNotEmpty("contactMobile", contactMobile)
                        .setIfNotEmpty("addressId", addressId),
                new FindAndModifyOptions().returnNew(true).upsert(false),
                Prize.class);
    }

    default Query createQueryByPrize(Prize prize) {
        if (null == prize) {
            throw new KbException(KbCode.ILLEGAL_ARGUMENT, "prize null");
        }
        Criteria criteria = new Criteria();
        if (prize.getSiteId() != null) {
            criteria.and("siteId").is(prize.getSiteId());
        }
        if (prize.getUserId() != null) {
            criteria.and("userId").is(prize.getUserId());
        }
        if (prize.getReferenceId1() != null) {
            criteria.and("referenceId1").is(prize.getReferenceId1());
        }
        if (prize.getReferenceId2() != null) {
            criteria.and("referenceId2").is(prize.getReferenceId2());
        }
        if (prize.getReferenceId3() != null) {
            criteria.and("referenceId3").is(prize.getReferenceId3());
        }
        if (prize.getState() != null) {
            criteria.and("state").is(prize.getState());
        }
        if (prize.getOrigin() != null) {
            criteria.and("origin").is(prize.getOrigin());
        }
        if (prize.getType() != null) {
            criteria.and("type").is(prize.getType());
        }
        if (prize.getDispatchMethodId() != null) {
            criteria.and("dispatchMethodId").is(prize.getDispatchMethodId());
        }
        if (prize.getSponsorId() != null) {
            criteria.and("sponsorId").is(prize.getSponsorId());
        }
        return Query.query(criteria);
    }

}
