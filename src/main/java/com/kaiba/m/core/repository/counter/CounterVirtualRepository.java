package com.kaiba.m.core.repository.counter;

import com.kaiba.m.core.domain.counter.KbCounterVirtual;
import com.kaiba.m.core.middleware.mongo.interfaces.KbMongoRepository;
import com.kaiba.m.core.middleware.mongo.utils.KbMongoUpdate;
import org.bson.types.ObjectId;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.core.FindAndModifyOptions;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;

import java.util.List;
import java.util.Optional;

/**
 * author: lyux
 * date: 2021-05-26
 */
public interface CounterVirtualRepository extends KbMongoRepository<KbCounterVirtual, String> {

    Page<KbCounterVirtual> findAllByOrderByIdDesc(Pageable pageable);

    Optional<KbCounterVirtual> findFirstByKey(String key);

    List<KbCounterVirtual> findByIdIn(String[] ids);

    boolean existsByKey(String key);

    default KbCounterVirtual upsertByKey(String key, long virtualCount, String expression) {
        long current = System.currentTimeMillis();
        return mongo().findAndModify(
                new Query(Criteria.where("key").is(key)),
                new Update()
                        .set("key", key)
                        .set("virtualCount", virtualCount)
                        .set("expression", expression)
                        .set("updateTime", current)
                        .set("createTime", current),
                new FindAndModifyOptions().upsert(true).returnNew(true),
                KbCounterVirtual.class);
    }

    default KbCounterVirtual setVirtualCount(String id, long virtualCount) {
        return mongo().findAndModify(
                Query.query(Criteria.where("_id").is(new ObjectId(id))),
                new Update().set("virtualCount", virtualCount),
                new FindAndModifyOptions().upsert(false).returnNew(true),
                KbCounterVirtual.class);
    }

    default KbCounterVirtual incrVirtualCount(String id, long virtualCount) {
        return mongo().findAndModify(
                Query.query(Criteria.where("_id").is(new ObjectId(id))),
                new Update().inc("virtualCount", virtualCount),
                new FindAndModifyOptions().upsert(false).returnNew(true),
                KbCounterVirtual.class);
    }

    default KbCounterVirtual update(
            String id, Long virtualCount, String expression, String description) {
        return mongo().findAndModify(
                Query.query(Criteria.where("_id").is(new ObjectId(id))),
                new KbMongoUpdate()
                        .setUnsetOnEmpty("expression", expression)
                        .setIfNotEmpty("virtualCount", virtualCount)
                        .setIfNotEmpty("description", description)
                        .set("updateTime", System.currentTimeMillis()),
                new FindAndModifyOptions().upsert(false).returnNew(true),
                KbCounterVirtual.class);
    }

}
