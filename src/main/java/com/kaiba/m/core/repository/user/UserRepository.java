package com.kaiba.m.core.repository.user;

import com.kaiba.lib.base.constant.user.UserRole;
import com.kaiba.lib.base.domain.data.datav.DatavTimeBucketStatModel;
import com.kaiba.m.core.domain.user.User;
import com.kaiba.m.core.middleware.mongo.interfaces.KbMongoRepository;
import com.kaiba.m.core.middleware.mongo.utils.KbMongoBeanUpdate;
import com.kaiba.m.core.middleware.mongo.utils.KbMongoUpdate;
import com.mongodb.client.MongoCollection;
import org.bson.Document;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.core.FindAndModifyOptions;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.data.mongodb.repository.Aggregation;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface UserRepository extends KbMongoRepository<User, Integer> {

    String BASIC_FIELDS = "{ " +
            "'_id':1, 'userName':1, 'avatar':1, 'sex':1, " +
            "'source':1, 'siteId':1, 'series':1, 'is_public':1, 'createtime':1, " +
            "'role':1, 'displayCred':1, 'displayCrest':1, 'displayPendants':1, 'pendantCount':1 }";
    String REVIEW_FIELDS = "{ '_id':1, 'userName':1, 'avatar':1, 'sex':1, 'signature':1, 'source':1, 'siteId':1, 'updateTime':1, 'createtime':1 }";
    String SENSITIVE_FIELDS = "{ '_id':1, 'userName':1, 'loginName':1, 'avatar':1, 'sex':1, 'siteId':1, 'mobile':1 }";

    @org.springframework.data.mongodb.repository.Query(value = "{ '_id' : ?0 }", fields = BASIC_FIELDS)
    Optional<User> getBasic(Integer userId);

    @org.springframework.data.mongodb.repository.Query(value = "{ '_id' : ?0 }", fields = SENSITIVE_FIELDS)
    Optional<User> getSensitive(Integer userId);

    @org.springframework.data.mongodb.repository.Query(value = "{}", fields = BASIC_FIELDS)
    Page<User> getBasicPageable(Pageable pageable);

    @org.springframework.data.mongodb.repository.Query(value = "{ 'mobile' : ?0 }", fields = BASIC_FIELDS)
    Optional<User> getBasicByMobile(String mobile);

    @org.springframework.data.mongodb.repository.Query(value = "{ 'userName' : ?0 }", fields = BASIC_FIELDS)
    Optional<User> getBasicByName(String name);

    @org.springframework.data.mongodb.repository.Query(value = "{ '_id' : ?0 }", fields = "{ '_id':1, 'mobile':1 }")
    Optional<User> getMobileById(Integer userId);

    Optional<User> getFirstByMobile(String mobile);

    Optional<User> getFirstByLoginName(String loginName);

    Optional<User> getFirstByWxId(String openId);

    Optional<User> getFirstByUnionId(String unionId);

    Optional<User> getFirstByQqId(String openId);

    @org.springframework.data.mongodb.repository.Query(value = "{ '_id' : { $in : ?0 } }", fields = BASIC_FIELDS)
    List<User> getBasicIn(Integer[] userIdList);

    @org.springframework.data.mongodb.repository.Query(value = "{ '_id' : { $in : ?0 } }", fields = BASIC_FIELDS)
    List<User> getBasicIn(Iterable<Integer> userIds);

    @org.springframework.data.mongodb.repository.Query(value = "{ '_id' : { $in : ?0 } }", fields = SENSITIVE_FIELDS)
    List<User> getSensitiveIn(Integer[] userIds);

    List<User> findByIdIn(List<Integer> userIdList);

    @org.springframework.data.mongodb.repository.Query(fields = REVIEW_FIELDS)
    List<User> findByOrderByUpdateTimeDesc(Pageable pageable);

    boolean existsByMobileAndIdNot(String mobile, Integer userId);

    boolean existsByUserNameOrLoginName(String userName, String loginName);

    boolean existsByLoginNameAndIdNot(String loginName, Integer userId);

    boolean existsByUserNameAndIdNot(String userName, Integer userId);

    default boolean existsCheckForNamesAndIdNot(String userName, String loginName, Integer userId) {
        return mongo().exists(Query.query(
                Criteria.where("_id").ne(userId)
                        .orOperator(
                                Criteria.where("userName").is(userName),
                                Criteria.where("loginName").is(loginName))),
                User.class
        );
    }

    long countByLoginTimeGreaterThan(long since);

    long countByCreateTimeLessThan(long time);

    long countBySiteIdAndCreateTimeLessThan(Integer siteId, long time);

    default long countBySiteIdAndCreateTimeBetween(Integer siteId, Long startTime, Long endTime) {
        Criteria criteria = new Criteria();
        criteria.and("createTime").gte(startTime).lte(endTime);
        if (siteId != null) {
            criteria.and("siteId").is(siteId);
        }
        return mongo().count(Query.query(criteria), User.class);
    }

    default Optional<User> getUserByAccount(String account) {
        User user = mongo().findOne(
                Query.query(new Criteria().orOperator(
                        Criteria.where("userName").is(account),
                        Criteria.where("mobile").is(account)
                )),
                User.class
        );
        return Optional.ofNullable(user);
    }

    default void updateLoginInfo(Integer userId,
                                 Integer origin, String deviceNumber, String deviceToken,
                                 Double longitude, Double latitude) {
        mongo().findAndModify(
                new Query(Criteria.where("_id").is(userId)),
                new KbMongoUpdate()
                        .setIfNotEmpty("lastLongitude", longitude)
                        .setIfNotEmpty("lastLatitude", latitude)
                        .setIfNotEmpty("deviceNumber", deviceNumber)
                        .setIfNotEmpty("deviceToken", deviceToken)
                        .setIfNotEmpty("origin", origin)
                        .set("logintime", System.currentTimeMillis() / 1000),
                User.class);
    }

    default void updateLogoff(Integer userId, Integer logoff) {
        mongo().findAndModify(
                new Query(Criteria.where("_id").is(userId)),
                new Update().set("logoff", logoff),
                User.class
        );
    }

    default void updateAsLogoff(Integer userId, String userName, String pwd) {
        mongo().findAndModify(
            Query.query(Criteria.where("_id").is(userId).and("logoff").is(1)),
            new Update()
                .set("mobile", "")
                .set("wxid", "")
                .set("qqid", "")
                .set("unionid", "")
                .set("avatar", "logo_default")
                .set("userName", userName)
                .set("password", pwd),
            User.class
        );
    }

    default Optional<User> setPassword(Integer userId, String passwordMd5) {
        User user = mongo().findAndModify(
                new Query(Criteria.where("_id").is(userId).and("setpass").is(1)),
                new Update().set("password", passwordMd5).set("setpass", 0),
                User.class);
        return Optional.ofNullable(user);
    }

    default User unSetCarNoOrSeries(Integer userId, String carNo, String series) {
        Update update = new Update();
        if(carNo != null && carNo.isEmpty()) {
            update.unset("carNo");
        }

        if(series != null && series.isEmpty()) {
            update.unset("series");
        }

        return mongo().findAndModify(
            new Query(Criteria.where("_id").is(userId)),
            update,
            User.class);
    }

    default User unsetAuthContent(Integer userId) {
        return mongo().findAndModify(
            new Query(Criteria.where("_id").is(userId)),
            new Update().unset("auth_content"),
            new FindAndModifyOptions().returnNew(true).upsert(false),
            User.class);
    }

    default User updateUserAuthContent(Integer userId, String authContent) {
        return mongo().findAndModify(
            new Query(Criteria.where("_id").is(userId)),
            new Update().set("auth_content", authContent),
            new FindAndModifyOptions().returnNew(true).upsert(false),
            User.class);
    }

    default void updateMobile(Integer userId, String mobile) {
        mongo().findAndModify(
                new Query(Criteria.where("_id").is(userId)),
                new Update().set("mobile", mobile),
                User.class);
    }

    default void updateQQOpenId(Integer userId, String openId) {
        mongo().findAndModify(
                new Query(Criteria.where("_id").is(userId)),
                new Update().set("qqid", openId),
                User.class);
    }

    default void updateWXUnionId(Integer userId, String openId, String unionId) {
        mongo().findAndModify(
                new Query(Criteria.where("_id").is(userId)),
                new Update().set("wxid", openId).set("unionid", unionId),
                User.class);
    }

    default User updateAvatar(Integer userId, String avatar) {
        return mongo().findAndModify(
                new Query(Criteria.where("_id").is(userId)),
                new Update().set("avatar", avatar),
                new FindAndModifyOptions().returnNew(true).upsert(false),
                User.class);
    }

    default void updatePassword(Integer userId, String passwordMd5) {
        mongo().findAndModify(
                new Query(Criteria.where("_id").is(userId)),
                new Update().set("password", passwordMd5).set("setpass", 0),
                User.class);
    }

    default User updateUserName(Integer userId, String userName) {
        return mongo().findAndModify(
                new Query(Criteria.where("_id").is(userId)),
                new Update().set("userName", userName),
                new FindAndModifyOptions().returnNew(true).upsert(false),
                User.class);
    }

    default User updateUserInfo(User user) {
        return mongo().findAndModify(
                new Query(Criteria.where("_id").is(user.getId())),
                new KbMongoBeanUpdate(user)
                        .setIfNotEmpty("loginName")
                        .setIfNotEmpty("userName")
                        .setIfNotEmpty("avatar")
                        .setIfNotEmpty("mobile")
                        .setIfNotEmpty("series")
                        .setIfNotEmpty("carNo")
                        .setIfNotEmpty("signature")
                        .setIfNotEmpty("background")
                        .setIfNotNull("pic")
                        .setIfNotNull("sex")
                        .setIfNotNull("source")
                        .setIfNotNull("siteId")
                        .setIfNotNull("origin")
                        .setIfNotEmpty("birthday")
                        .setWhen(user.getCityCode() != null, "citycode", user.getCityCode())
                        .setWhen(user.getIsPublic() != null, "is_public", user.getIsPublic())
                        .set("updateTime", System.currentTimeMillis() / 1000),
                new FindAndModifyOptions().returnNew(true),
                User.class);
    }

    default User updateUserRole(Integer userId, UserRole role) {
        return mongo().findAndModify(
                new Query(Criteria.where("_id").is(userId)),
                new Update().set("role", role.getValue()),
                new FindAndModifyOptions().returnNew(true),
                User.class);
    }

    default void updateScore(Integer userId, Integer scoreIncr) {
        mongo().findAndModify(
                new Query(Criteria.where("_id").is(userId)),
                new KbMongoUpdate()
                        .inc("score", scoreIncr == null ? 0 : scoreIncr)
                        .inc("scoreAvail", scoreIncr == null ? 0 : scoreIncr),
                User.class);
    }

    @org.springframework.data.mongodb.repository.Query(
        value = "{updateTime: {$gte: ?0, $lt: ?1}}",
        fields = BASIC_FIELDS, sort = "{updateTime: 1}")
    List<User> getListByUpdateTimeBetween(Long st, Long et, Pageable pageable);

    default void updateDisplayCred(Integer userId, String displayCred) {
        Update update = new Update();
        if (displayCred == null) {
            update.unset("displayCred");
        } else {
            update.set("displayCred", displayCred);
        }
        mongo().findAndModify(
                new Query(Criteria.where("_id").is(userId)),
                new Update().set("displayCred", displayCred),
                User.class);
    }

    default void updateDisplayCrest(Integer userId, String displayCrest) {
        Update update = new Update();
        if (displayCrest == null) {
            update.unset("displayCrest");
        } else {
            update.set("displayCrest", displayCrest);
        }
        mongo().findAndModify(
                new Query(Criteria.where("_id").is(userId)), update,
                User.class);
    }

    default void updateDisplayPendantsAndCount(Integer userId, List<String> pendents, int count) {
        Update update = new Update();
        if (pendents == null || pendents.isEmpty()) {
            update.unset("displayPendants");
        } else {
            update.set("displayPendants", pendents);
        }
        update.set("pendantCount", count);
        mongo().findAndModify(
                new Query(Criteria.where("_id").is(userId)), update,
                User.class);
    }

    default void updateDisplayPendants(Integer userId, List<String> pendents) {
        Update update = new Update();
        if (pendents == null || pendents.isEmpty()) {
            update.unset("displayPendants");
        } else {
            update.set("displayPendants", pendents);
        }
        mongo().findAndModify(
                new Query(Criteria.where("_id").is(userId)), update,
                User.class);
    }

    default void updatePendantCount(Integer userId, int count) {
        mongo().findAndModify(
                new Query(Criteria.where("_id").is(userId)),
                new Update().set("pendantCount", count),
                User.class);
    }

    default long countAllUser() {
        MongoCollection<Document> collection = mongo().getCollection("user");
        return collection.estimatedDocumentCount();
    }

    @Aggregation(value = {
        "{$match: {createtime: {$gte: ?0, $lt: ?1}}}",
        "{$project: {timeBucket: {$add: [new Date(0), 28800000, {$multiply: ['$createtime', 1000]}]}}}",
        "{$group: {_id: {$dateToString: {format: '%Y-%m-%d', date: '$timeBucket'}}, count: {$sum: 1}}}",
        "{$project: {timeBucket: '$_id', count: 1}}",
    })
    List<DatavTimeBucketStatModel> aggrCountByDay(Long st, Long et);

    @Aggregation(value = {
        "{$match: {siteId: ?0, createtime: {$gte: ?1, $lt: ?2}}}",
        "{$project: {timeBucket: {$add: [new Date(0), 28800000, {$multiply: ['$createtime', 1000]}]}}}",
        "{$group: {_id: {$dateToString: {format: '%Y-%m-%d', date: '$timeBucket'}}, count: {$sum: 1}}}",
        "{$project: {timeBucket: '$_id', count: 1}}",
    })
    List<DatavTimeBucketStatModel> aggrSiteCountByDay(Integer siteId, Long st, Long et);

    @Aggregation(value = {
        "{$match: {createtime: {$gte: ?0, $lt: ?1}}}",
        "{$project: {createtime: 1, timeBucket: {$floor: {$divide: ['$createtime', ?2]}}}}",
        "{$group: {_id: '$timeBucket', count: {$sum: 1}}}",
        "{$project: {_id: 0, startTime: {$multiply: ['$_id', ?2]}, count: 1}}",
        "{$project: {startTime: 1, count: 1, endTime: {$add: ['$startTime', ?2]}}}"
    })
    List<DatavTimeBucketStatModel> aggrUserIntervalCountByTimeBetween(Long st, Long et, Long interval);

    @Aggregation(value = {
        "{$match: {siteId: ?0, createtime: {$gte: ?1, $lt: ?2}}}",
        "{$project: {createtime: 1, timeBucket: {$floor: {$divide: ['$createtime', ?3]}}}}",
        "{$group: {_id: '$timeBucket', count: {$sum: 1}}}",
        "{$project: {_id: 0, startTime: {$multiply: ['$_id', ?3]}, count: 1}}",
        "{$project: {startTime: 1, count: 1, endTime: {$add: ['$startTime', ?3]}}}"
    })
    List<DatavTimeBucketStatModel> aggrSiteUserIntervalCountByTimeBetween(Integer siteId, Long st, Long et, Long interval);

}
