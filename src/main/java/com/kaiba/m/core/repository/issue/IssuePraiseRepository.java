package com.kaiba.m.core.repository.issue;

import com.kaiba.m.core.domain.issue.Issue;
import com.kaiba.m.core.domain.issue.IssueExpertFollowee;
import com.kaiba.m.core.domain.issue.IssuePraise;
import com.kaiba.m.core.middleware.mongo.interfaces.KbMongoRepository;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.core.FindAndModifyOptions;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Repository;

/**
 * author: lyux
 * date: 18-9-25
 */
@Repository
public interface IssuePraiseRepository extends KbMongoRepository<IssuePraise, Integer> {

    Page<IssuePraise> findAllByUserIdOrderByCreateTimeDesc(Integer userId, Pageable pageable);

    Page<IssuePraise> findAllByExpertIdOrderByCreateTimeDesc(Integer expertId, Pageable pageable);

    Page<IssuePraise> findAllByIssueIdOrderByCreateTimeDesc(String issueId, Pageable pageable);

    long countByExpertId(Integer expertId);

    long countByIssueId(String issueId);

    boolean existsByIssueIdAndUserId(String issueId, Integer userId);

    default void praise(Issue issue, Integer userId) {
        mongo().findAndModify(
                new Query(Criteria.where("issueId").is(issue.getId()).and("userId").is(userId)),
                new Update()
                        .set("issueId", issue.getId())
                        .set("expertId", issue.getExpertId())
                        .set("userId", userId)
                        .set("createTime", System.currentTimeMillis() / 1000),
                new FindAndModifyOptions().upsert(true),
                IssuePraise.class);
    }

    default void unpraise(Issue issue, Integer userId) {
        mongo().remove(
                new Query(Criteria.where("issueId").is(issue.getId()).and("userId").is(userId)),
                IssuePraise.class);
    }

}
