package com.kaiba.m.core.repository.videolive;

import com.kaiba.lib.base.constant.videolive.VideoLiveState;
import com.kaiba.m.core.domain.videolive.VideoLive;
import com.kaiba.m.core.middleware.mongo.interfaces.KbMongoRepository;
import org.bson.types.ObjectId;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Repository;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/5/27
 */
@Repository
public interface VideoliveRepository extends KbMongoRepository<VideoLive, String> {

    Page<VideoLive> findByStateInOrderByCreateTimeDesc(Integer[] status, Pageable pageable);

    Page<VideoLive> findByStateInAndPresetStartTimeIsLessThanEqualOrderByCreateTime(Integer[] status, Long time, Pageable pageable);

    Page<VideoLive> findByCreateTimeBetween(Long createTimeAfter, Long createTimeBefore, Pageable pageable);

    default Page<VideoLive> getActiveAndIsDisplayList(
        Integer siteId, Integer[] status, Pageable pageable
    ) {
        Criteria criteria = Criteria.where("siteId").is(siteId);
        if (status != null && status.length > 0) {
            criteria.and("state").in(Arrays.asList(status));
        }

        criteria.and("isDisplay").ne(false);
        Query query = Query.query(criteria);
        long totalCount = mongo().count(query, VideoLive.class);
        List<VideoLive> videoLiveList = mongo().find(query.with(pageable), VideoLive.class);
        return new PageImpl<>(videoLiveList, pageable, totalCount);
    }

    default Page<VideoLive> getVideoLiveBySiteIdAndStatus(Integer siteId, Integer[] status, Pageable pageable) {
        Criteria criteria = Criteria.where("siteId").is(siteId);
        if (status != null && status.length > 0) {
            criteria.and("state").in(Arrays.asList(status));
        }
        Query query = Query.query(criteria);
        long totalCount = mongo().count(query, VideoLive.class);
        List<VideoLive> videoLiveList = mongo().find(query.with(pageable), VideoLive.class);
        return new PageImpl<>(videoLiveList, pageable, totalCount);
    }

    default void updateLiveState(String videoliveId, Integer oldState, Integer state) {
        if (null == videoliveId || null == oldState || null == state) {
            return;
        }
        mongo().findAndModify(
                Query.query(Criteria.where("id").is(new ObjectId(videoliveId)).and("state").is(oldState)),
                Update.update("state", state),
                VideoLive.class);
    }

    default void updateLiveAsEnded(String videoliveId) {
        mongo().findAndModify(
                Query.query(Criteria.where("id").is(new ObjectId(videoliveId))
                        .and("state").is(VideoLiveState.STARTED.getValue())),
                new Update()
                        .set("state", VideoLiveState.ENDED.getValue())
                        .set("endTime", System.currentTimeMillis() / 1000),
                VideoLive.class);
    }

    default void updateVirtualConfig(String videoliveId, String expression, String description) {
        mongo().findAndModify(
                Query.query(Criteria.where("id").is(new ObjectId(videoliveId))),
                new Update().set("vcExpression", expression).set("vcDescription", description),
                VideoLive.class);
    }

    default void updateChannel(String videoliveId, String channelKey) {
        mongo().findAndModify(
                Query.query(Criteria.where("id").is(new ObjectId(videoliveId))),
                new Update()
                        .set("channelKey", channelKey),
                VideoLive.class);
    }
}
