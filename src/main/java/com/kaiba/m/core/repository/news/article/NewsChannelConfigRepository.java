package com.kaiba.m.core.repository.news.article;

import com.kaiba.m.core.domain.news.channel.ChannelConfig;
import com.kaiba.m.core.middleware.mongo.interfaces.KbMongoRepository;
import com.kaiba.m.core.middleware.mongo.utils.KbMongoUpdate;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.core.FindAndModifyOptions;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Repository;

import java.util.Optional;
import java.util.Set;

/**
 * author: lyux
 * date: 2023-08-03
 */
@Repository
public interface NewsChannelConfigRepository extends KbMongoRepository<ChannelConfig, String> {

    Optional<ChannelConfig> findFirstByChannel(String channel);

    Page<ChannelConfig> findByOrderByIdDesc(Pageable pageable);

    default ChannelConfig updateData(String channel, String name, Set<String> agent, String replyStyle) {
        return mongo().findAndModify(
                new Query(Criteria.where("channel").is(channel)),
                new KbMongoUpdate()
                        .setUnsetOnNull("replyStyle", replyStyle)
                        .setUnsetOnEmpty("agent", agent)
                        .set("name", name)
                        .set("updateTime", System.currentTimeMillis()),
                new FindAndModifyOptions().returnNew(true),
                ChannelConfig.class);
    }

    default ChannelConfig updateReplyStyle(String channel, String replyStyle) {
        return mongo().findAndModify(
                new Query(Criteria.where("channel").is(channel)),
                new KbMongoUpdate()
                        .setUnsetOnNull("replyStyle", replyStyle)
                        .set("updateTime", System.currentTimeMillis()),
                new FindAndModifyOptions().returnNew(true),
                ChannelConfig.class);
    }

    default void addAgent(String channel, String agent) {
        mongo().findAndModify(
                new Query(Criteria.where("channel").is(channel)),
                new Update().addToSet("agent", agent),
                new FindAndModifyOptions().returnNew(false),
                ChannelConfig.class);
    }

    default void removeAgent(String channel, String agent) {
        mongo().findAndModify(
                new Query(Criteria.where("channel").is(channel)),
                new Update().pull("agent", agent),
                new FindAndModifyOptions().returnNew(false),
                ChannelConfig.class);
    }

}
