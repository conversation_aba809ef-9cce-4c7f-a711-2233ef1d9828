package com.kaiba.m.core.repository.kri.matcher;

import com.kaiba.lib.base.constant.kri.KRIMatcherLogic;
import com.kaiba.lib.base.domain.kri.KbResource;
import com.kaiba.m.core.domain.kri.matcher.KRIExistMatcher;
import com.kaiba.m.core.middleware.mongo.interfaces.KbMongoRepository;
import org.springframework.data.domain.*;
import org.springframework.data.mongodb.core.FindAndModifyOptions;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * author: lyux
 * date: 2023-08-08
 */
@Repository
public interface KRIExistMatcherRepository extends KbMongoRepository<KRIExistMatcher, String> {

    Page<KRIExistMatcher> findByFactOrderByIdDesc(String fact, Pageable pageable);

    default Page<KRIExistMatcher> getPageByExist(KbResource resource, String fact, int page, int pageSize) {
        String biz = resource.getBiz();
        String unit = resource.getUnit();
        String ref1 = resource.getRef1();
        String ref2 = resource.getRef2();
        String ref3 = resource.getRef3();
        Criteria criteria;
        if (ref1 != null && ref2 != null && ref3 != null) {
            criteria = Criteria.where("biz").is(biz).and("ref1").is(ref1).and("ref2").is(ref2)
                    .and("ref3").is(KRIMatcherLogic.EXISTS.name());
        } else if (ref1 != null && ref2 != null) {
            criteria = Criteria.where("biz").is(biz).and("ref1").is(ref1)
                    .and("ref2").is(KRIMatcherLogic.EXISTS.name());
        } else if (ref1 != null) {
            criteria = Criteria.where("biz").is(biz)
                    .and("ref1").is(KRIMatcherLogic.EXISTS.name());
        } else {
            return Page.empty();
        }
        if (unit != null) {
            criteria = criteria.and("unit").is(unit);
        }
        if (fact != null) {
            criteria = criteria.and("fact").is(fact);
        }
        Pageable pageable = PageRequest.of(page, pageSize, Sort.by(Sort.Direction.DESC, "_id"));
        List<KRIExistMatcher> list = mongo().find(new Query(criteria).with(pageable), KRIExistMatcher.class);
        long total = mongo().getCollection("kri_match_by_exist").estimatedDocumentCount();
        return new PageImpl<>(list, pageable, total);
    }

    default Optional<KRIExistMatcher> getFirstByExistAndFact(KbResource resource, String fact) {
        String biz = resource.getBiz();
        String unit = resource.getUnit();
        String ref1 = resource.getRef1();
        String ref2 = resource.getRef2();
        Criteria criteria = Criteria.where("biz").is(biz).and("fact").is(fact);
        if (ref1 != null && ref2 != null) {
            criteria = criteria.and("ref1").is(ref1).and("ref2").is(ref2).and("ref3").is(KRIMatcherLogic.EXISTS.name());
        } else if (ref1 != null) {
            criteria = criteria.and("ref1").is(ref1).and("ref2").is(KRIMatcherLogic.EXISTS.name());
        } else {
            criteria = criteria.and("ref1").is(KRIMatcherLogic.EXISTS.name());
        }
        if (unit != null) {
            criteria = criteria.and("unit").is(unit);
        }
        return Optional.ofNullable(mongo().findOne(new Query(criteria), KRIExistMatcher.class));
    }

    default KRIExistMatcher upsert(KbResource resource, String fact, String data) {
        String biz = resource.getBiz();
        String unit = resource.getUnit();
        String ref1 = resource.getRef1();
        String ref2 = resource.getRef2();
        Criteria criteria = Criteria.where("biz").is(biz).and("fact").is(fact);
        Update update = new Update()
                .setOnInsert("biz", biz)
                .setOnInsert("fact", fact)
                .set("data", data)
                .set("updateTime", System.currentTimeMillis());
        if (unit != null) {
            criteria = criteria.and("unit").is(unit);
            update.setOnInsert("unit", unit);
        }
        if (ref1 != null && ref2 != null) {
            criteria = criteria.and("ref1").is(ref1).and("ref2").is(ref2).and("ref3").is(KRIMatcherLogic.EXISTS.name());
            update.setOnInsert("ref1", ref1).setOnInsert("ref2", ref2).setOnInsert("ref3", KRIMatcherLogic.EXISTS.name());
        } else if (ref1 != null) {
            criteria = criteria.and("ref1").is(ref1).and("ref2").is(KRIMatcherLogic.EXISTS.name());
            update.setOnInsert("ref1", ref1).setOnInsert("ref2", KRIMatcherLogic.EXISTS.name());
        } else {
            criteria = criteria.and("ref1").is(KRIMatcherLogic.EXISTS.name());
            update.setOnInsert("ref1", KRIMatcherLogic.EXISTS.name());
        }
        return mongo().findAndModify(
                new Query(criteria), update,
                FindAndModifyOptions.options().upsert(true).returnNew(true),
                KRIExistMatcher.class);
    }

}
