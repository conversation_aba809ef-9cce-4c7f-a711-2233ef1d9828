package com.kaiba.m.core.repository.wx;

import com.kaiba.m.core.domain.wx.WxOpenAppConfig;
import com.kaiba.m.core.middleware.mongo.interfaces.KbMongoRepository;
import org.springframework.data.mongodb.core.FindAndModifyOptions;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Repository;

import java.util.Optional;

/**
 * <AUTHOR>
 * @version WxConfigRepository, v0.1 2024/4/17 15:52 daopei Exp $
 **/
@Repository
public interface WxOpenAppConfigRepository extends KbMongoRepository<WxOpenAppConfig, Integer> {

    Optional<WxOpenAppConfig> findByAppId(String appId);

    default WxOpenAppConfig upsert(Integer siteId, String appId, Integer proxyUserId) {
        return mongo().findAndModify(
                new Query(Criteria.where("id").is(siteId)),
                new Update()
                        .setOnInsert("createTime", System.currentTimeMillis())
                        .setOnInsert("id", siteId)
                        .set("updateTime", System.currentTimeMillis())
                        .set("proxyUserId", proxyUserId)
                        .set("appId", appId),
                FindAndModifyOptions.options().upsert(true).returnNew(true),
                WxOpenAppConfig.class
        );
    }
}
