package com.kaiba.m.core.repository.auth;

import com.kaiba.lib.base.constant.auth.AuthType;
import com.kaiba.lib.base.util.StringUtils;
import com.kaiba.m.core.domain.auth.AuthPermissionTemplate;
import com.kaiba.m.core.middleware.mongo.interfaces.KbMongoRepository;
import com.kaiba.m.core.middleware.mongo.utils.KbMongoCriteria;
import org.springframework.data.domain.*;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Repository;

import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * author: lyux
 * date: 19-8-23
 */
@Repository
public interface AuthPermissionTemplateRepository extends KbMongoRepository<AuthPermissionTemplate, String> {

    Optional<AuthPermissionTemplate> findFirstByFormat(String format);

    Page<AuthPermissionTemplate> findByScopeOrderByFormat(Integer scope, Pageable pageable);

    List<AuthPermissionTemplate> findByFormatIn(List<String> formats);

    void deleteByFormat(String format);

    default void updateAuthType(String format, List<AuthType> authTypeList) {
        List<Integer> authTypes = (null == authTypeList || authTypeList.size() == 0) ?
                Collections.singletonList(AuthType.ANY.getValue()) :
                authTypeList.stream().map(AuthType::getValue).collect(Collectors.toList());
        mongo().findAndModify(
                new Query(Criteria.where("format").is(format)),
                new Update().set("authTypes", authTypes),
                AuthPermissionTemplate.class);
    }

    default void updateLevel(String format, Integer level) {
        mongo().findAndModify(
                new Query(Criteria.where("format").is(format)),
                new Update().set("level", level),
                AuthPermissionTemplate.class);
    }

    default void updateDescription(String format, String description) {
        mongo().findAndModify(
                new Query(Criteria.where("format").is(format)),
                new Update().set("description", description),
                AuthPermissionTemplate.class);
    }

    default Page<AuthPermissionTemplate> searchForPage(
            Integer scope, String formatRegex, String descriptionRegex,
            Integer page, Integer pageSize) {
        Query query = Query.query(new KbMongoCriteria()
                .criteriaIfNotEmpty("scope", scope)
                .regexIfNotEmpty("format", StringUtils.escapeRegex(formatRegex))
                .regexIfNotEmpty("description", StringUtils.escapeRegex(descriptionRegex)));
        int p = page == null ? 0 : page - 1;
        int ps = pageSize == null ? 20 : pageSize;
        Pageable pageable = PageRequest.of(p, ps, Sort.by(Sort.Direction.ASC, "format"));
        long total = mongo().count(query, AuthPermissionTemplate.class);
        List<AuthPermissionTemplate> list = mongo().find(query.with(pageable), AuthPermissionTemplate.class);
        return new PageImpl<>(list, pageable, total);
    }

}
