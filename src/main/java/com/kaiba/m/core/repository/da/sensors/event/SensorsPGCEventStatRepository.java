package com.kaiba.m.core.repository.da.sensors.event;

import com.kaiba.m.core.domain.da.sensors.event.SensorsPGCEventStat;
import com.kaiba.m.core.middleware.mongo.interfaces.KbMongoRepository;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Repository;

import java.util.Optional;

/**
 * <AUTHOR>
 * @version SensorsPGCEventStatRepository, v0.1 2025/2/28 10:22 daopei Exp $
 **/
@Repository
public interface SensorsPGCEventStatRepository extends KbMongoRepository<SensorsPGCEventStat, String> {

    default Optional<SensorsPGCEventStat> getByKey(String business, String ref1, String ref2, String ref3) {
        Criteria criteria = new Criteria();
        criteria.and("business").is(business);
        criteria.and("ref1").is(ref1);
        if (ref2 != null) {
            criteria.and("ref2").is(ref2);
        }
        if (ref3 != null) {
            criteria.and("ref3").is(ref3);
        }
        Query query = new Query(criteria);
        query.limit(1);
        return mongo().find(query, SensorsPGCEventStat.class).stream().findFirst();
    }

}
