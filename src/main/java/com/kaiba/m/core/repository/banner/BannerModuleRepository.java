package com.kaiba.m.core.repository.banner;

import com.kaiba.m.core.domain.banner.BannerModule;
import com.kaiba.m.core.middleware.mongo.interfaces.KbMongoRepository;
import com.kaiba.m.core.middleware.mongo.utils.KbMongoBeanUpdate;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.core.FindAndModifyOptions;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Repository;

@Repository
public interface BannerModuleRepository extends KbMongoRepository<BannerModule,String> {

    default BannerModule updateBannerModule(BannerModule bannerModule) {
        return mongo().findAndModify(
                new Query(Criteria
                        .where("_id").is(new ObjectId(bannerModule.getId()))),
                new KbMongoBeanUpdate(bannerModule)
                        .setIfNotNull("imageWidth")
                        .setIfNotNull("imageHeight")
                        .setIfNotNull("isImageRequire")
                        .setIfNotNull("imageWidthNotch")
                        .setIfNotNull("imageHeightNotch")
                        .setIfNotNull("isImageNotchRequire")
                        .set("updateTime", System.currentTimeMillis()),
                new FindAndModifyOptions().returnNew(true).upsert(false),
                BannerModule.class);
    }
}
