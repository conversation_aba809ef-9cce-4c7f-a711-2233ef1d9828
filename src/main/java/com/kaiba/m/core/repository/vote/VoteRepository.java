package com.kaiba.m.core.repository.vote;

import com.kaiba.m.core.domain.Vote.Vote;
import com.kaiba.m.core.middleware.mongo.interfaces.KbMongoRepository;
import org.springframework.data.mongodb.core.FindAndModifyOptions;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/10/14
 */
@Repository
public interface VoteRepository extends KbMongoRepository<Vote, String> {
    List<Vote> findByIdIn(String[] voteIds);

    default Vote updateVoteScale(String voteId, int voteScale) {
        return mongo().findAndModify(
                new Query(Criteria.where("_id").is(voteId)),
                new Update().set("voteScale", voteScale),
                new FindAndModifyOptions().returnNew(true),
                Vote.class);
    }

    default Vote updateVote(Vote vote) {
        return mongo().findAndModify(
                new Query(Criteria.where("_id").is(vote.getId())),
                new Update()
                        .set("title", vote.getTitle())
                        .set("multiVote", vote.getMultiVote())
                        .set("voteScale", vote.getVoteScale())
                        .set("startTime", vote.getStartTime())
                        .set("endTime", vote.getEndTime())
                        .set("style", vote.getStyle())
                        .set("countStyle", vote.getCountStyle())
                        .set("showPreVote", vote.getShowPreVote())
                        .set("allowModify", vote.getAllowModify()),
                new FindAndModifyOptions().returnNew(true),
                Vote.class);
    }
}
