package com.kaiba.m.core.repository.user;

import com.kaiba.m.core.domain.user.UserAccWX;
import com.kaiba.m.core.middleware.mongo.interfaces.KbMongoRepository;
import org.springframework.data.mongodb.core.FindAndModifyOptions;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface UserAccWXRepository extends KbMongoRepository<UserAccWX, String> {

    Optional<UserAccWX> findFirstByUserIdAndEntity(Integer userId, String entity);

    Optional<UserAccWX> findFirstByOpenId(String openId);

    List<UserAccWX> findByUserId(Integer userId);

    Optional<UserAccWX> findFirstByOpenIdAndEntity(String openId, String entity);

    boolean existsByUserIdAndEntity(Integer userId, String entity);

    default UserAccWX upsert(Integer userId, String entity, String openId) {
        return mongo().findAndModify(
                new Query(Criteria.where("userId").is(userId).and("entity").is(entity)),
                new Update()
                        .set("userId", userId)
                        .set("entity", entity)
                        .set("openId", openId)
                        .set("createTime", System.currentTimeMillis()),
                new FindAndModifyOptions().upsert(true).returnNew(true),
                UserAccWX.class);
    }

}
