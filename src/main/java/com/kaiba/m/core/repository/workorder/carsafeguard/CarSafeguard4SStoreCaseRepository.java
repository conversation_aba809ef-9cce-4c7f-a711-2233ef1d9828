package com.kaiba.m.core.repository.workorder.carsafeguard;

import com.kaiba.m.core.domain.workorder.carsafeguard.CarSafeguard4SStoreCase;
import com.kaiba.m.core.middleware.mongo.interfaces.KbMongoRepository;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @version CarSafeguard4SCaseDetailRepository, v0.1 2024/7/16 13:56 daopei Exp $
 **/
@Repository
public interface CarSafeguard4SStoreCaseRepository extends KbMongoRepository<CarSafeguard4SStoreCase, String> {



    List<CarSafeguard4SStoreCase> findByIdIn(Collection<String> contentIds);
}
