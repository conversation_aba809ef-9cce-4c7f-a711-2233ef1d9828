package com.kaiba.m.core.repository.workorder.carsafeguard.old;

import com.kaiba.m.core.domain.appwidget.textticker.TextTicker;
import com.kaiba.m.core.domain.workorder.carsafeguard.old.CarSafeguardSyncForComplainDeal;
import com.kaiba.m.core.middleware.mongo.interfaces.KbMongoRepository;
import org.bson.types.ObjectId;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @version CarSafeguardSyncForComplainDealRepository, v0.1 2024/9/4 10:02 daopei Exp $
 **/
@Repository
public interface CarSafeguardSyncForComplainDealRepository extends KbMongoRepository<CarSafeguardSyncForComplainDeal, Integer> {

    List<CarSafeguardSyncForComplainDeal> findByComplainIdOrderByIdAsc(String complainId);


    default List<CarSafeguardSyncForComplainDeal> getByComplainIdOrderByIdAsc(String complainId) {
        return mongo().find(
                new Query(Criteria.where("complainId").is(new ObjectId(complainId)))
                        .with(Sort.by(Sort.Direction.ASC, "id")),
                CarSafeguardSyncForComplainDeal.class
        );
    }
}
