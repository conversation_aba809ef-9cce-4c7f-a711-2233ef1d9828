package com.kaiba.m.core.repository.news.legacy;

import com.kaiba.m.core.domain.news.legacy.SiteNewsThread;
import com.kaiba.m.core.middleware.mongo.interfaces.KbMongoRepository;
import org.springframework.stereotype.Repository;

import java.util.Optional;
@Repository
public interface SiteNewsThreadRepository extends KbMongoRepository<SiteNewsThread, String> {
    Optional<SiteNewsThread> findBySiteId(Integer siteId);
}
