package com.kaiba.m.core.repository.da.sensors.virtual;

import com.kaiba.m.core.domain.da.sensors.virtual.SensorsVirtualExpression;
import com.kaiba.m.core.middleware.mongo.interfaces.KbMongoRepository;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @version VirtualExpressionRepository, v0.1 2023/12/28 16:41 daopei Exp $
 **/
@Repository
public interface SensorsVirtualExpressionRepository extends KbMongoRepository<SensorsVirtualExpression, String> {

    Optional<SensorsVirtualExpression> findFirstByBusinessAndContentKeyAndTypeAndState(String business, String contentKey, Integer type, Integer state);

    Optional<SensorsVirtualExpression> findFirstByBusinessAndTypeAndState(String business, Integer type, Integer state);

    Optional<SensorsVirtualExpression> findFirstByBusinessAndType(String business, Integer type);

    Optional<SensorsVirtualExpression> findFirstByBusinessAndContentKeyAndType(String business, String contentKey, Integer type);

    default Page<SensorsVirtualExpression> findByParam(
            String business,
            String contentKey,
            Integer type,
            Pageable pageable
    ) {
        Criteria criteria = new Criteria();
        if (business != null) {
            criteria.and("business").is(business);
        }
        if (contentKey != null) {
            criteria.and("contentKey").regex(contentKey);
        }
        if (type != null) {
            criteria.and("type").is(type);
        }
        Query query = new Query();
        query.addCriteria(criteria);
        query.with(pageable);
        long count = mongo().count(query, SensorsVirtualExpression.class);
        List<SensorsVirtualExpression> list = mongo().find(query, SensorsVirtualExpression.class);
        return new PageImpl<>(list, pageable, count);
    }

}
