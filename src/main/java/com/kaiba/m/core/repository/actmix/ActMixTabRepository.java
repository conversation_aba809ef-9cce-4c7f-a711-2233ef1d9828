package com.kaiba.m.core.repository.actmix;

import com.kaiba.m.core.domain.actmix.ActMixTab;
import com.kaiba.m.core.middleware.mongo.interfaces.KbMongoRepository;
import com.kaiba.m.core.middleware.mongo.utils.KbMongoUpdate;

import org.springframework.data.mongodb.core.FindAndModifyOptions;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/11/08 11:47
 **/
@Repository
public interface ActMixTabRepository extends KbMongoRepository<ActMixTab, String> {

    boolean existsBySiteIdAndName(Integer siteId, String name);

    List<ActMixTab> findAllBySiteIdOrderByOrderAsc(Integer siteId);

    List<ActMixTab> findAllBySiteIdAndStateOrderByOrderAsc(Integer siteId, Integer state);

    default ActMixTab updateTabById(ActMixTab tab) {
        long time = System.currentTimeMillis();
        return mongo().findAndModify(
            Query.query(Criteria.where("_id").is(tab.getId())),
            new KbMongoUpdate()
                .setIfNotEmpty("name", tab.getName())
                .setIfNotEmpty("order", tab.getOrder())
                .set("updateTime", time),
            new FindAndModifyOptions().returnNew(true),
            ActMixTab.class);
    }

    default ActMixTab updateTabStateById(String id, Integer state) {
        long time = System.currentTimeMillis();
        return mongo().findAndModify(
            Query.query(Criteria.where("_id").is(id)),
            new Update()
                .set("state", state)
                .set("updateTime", time),
            new FindAndModifyOptions().returnNew(true),
            ActMixTab.class);
    }
}
