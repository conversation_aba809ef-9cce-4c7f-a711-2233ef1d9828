package com.kaiba.m.core.repository.applego;

import com.kaiba.lib.base.constant.applego.WidgetType;
import com.kaiba.m.core.domain.applego.widget.WidgetVideoData;
import com.kaiba.m.core.middleware.mongo.interfaces.KbMongoRepository;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.List;
import java.util.Optional;

/**
 * author: lyux
 * date: 2024-09-04
 */
@Repository
public interface WidgetVideoRepository extends
        KbMongoRepository<WidgetVideoData, String>, IWidgetRepository<WidgetVideoData> {

    @Override
    List<WidgetVideoData> findByIdIn(Collection<String> widgetIds);

    @Override
    Optional<WidgetVideoData> findFirstByBlockId(String blockId);

    @Override
    default WidgetType getWidgetType() {
        return WidgetType.VIDEO;
    }
}
