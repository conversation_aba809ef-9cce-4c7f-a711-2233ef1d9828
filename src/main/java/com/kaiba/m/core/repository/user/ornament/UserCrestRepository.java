package com.kaiba.m.core.repository.user.ornament;

import com.kaiba.m.core.domain.user.ornament.UserCrest;
import com.kaiba.m.core.middleware.mongo.interfaces.KbMongoRepository;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.core.FindAndModifyOptions;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.List;
import java.util.Optional;

/**
 * author: lyux
 * date: 2025-04-10
 */
@Repository
public interface UserCrestRepository extends KbMongoRepository<UserCrest, String> {

    Optional<UserCrest> findFirstByCrest(String crest);

    Optional<UserCrest> findFirstByUserIdAndCrest(Integer userId, String crest);

    List<UserCrest> getByUserIdOrderByIdDesc(Integer userId, Pageable pageable);

    List<UserCrest> getByUserIdAndCrestIn(Integer userId, Collection<String> crests);

    Page<UserCrest> findByUserIdOrderByIdDesc(Integer userId, Pageable pageable);

    Page<UserCrest> findByCrestOrderByIdDesc(String crest, Pageable pageable);

    long countByUserIdAndCrest(Integer userId, String crest);

    void deleteByUserIdAndCrest(Integer userId, String crest);

    long countByExpireTimeLessThan(Long expireTime);

    void deleteByExpireTimeLessThan(Long expireTime);

    default UserCrest upsertData(Integer userId, String crest, Long expire) {
        long now = System.currentTimeMillis();
        Update update = new Update()
                .set("updateTime", now)
                .setOnInsert("userId", userId)
                .setOnInsert("crest", crest)
                .setOnInsert("createTime", now);
        if (expire != null) {
            update.set("expireTime", expire);
        }
        return mongo().findAndModify(
                new Query(Criteria.where("userId").is(userId).and("crest").is(crest)),
                update, FindAndModifyOptions.options().upsert(true).returnNew(true),
                UserCrest.class);
    }

    default void updateExpire(Integer userId, String crest, long expire) {
        mongo().findAndModify(
                new Query(Criteria.where("userId").is(userId).and("crest").is(crest)),
                new Update()
                        .set("expireTime", expire)
                        .set("updateTime", System.currentTimeMillis()),
                FindAndModifyOptions.options().upsert(false).returnNew(false),
                UserCrest.class);
    }

    default void unsetExpire(Integer userId, String crest) {
        mongo().findAndModify(
                new Query(Criteria.where("userId").is(userId).and("crest").is(crest)),
                new Update()
                        .unset("expireTime")
                        .set("updateTime", System.currentTimeMillis()),
                FindAndModifyOptions.options().upsert(false).returnNew(false),
                UserCrest.class);
    }

}
