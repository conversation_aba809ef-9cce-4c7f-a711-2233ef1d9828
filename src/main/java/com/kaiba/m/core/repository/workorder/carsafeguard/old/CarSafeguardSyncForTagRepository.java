package com.kaiba.m.core.repository.workorder.carsafeguard.old;

import com.kaiba.m.core.domain.workorder.carsafeguard.old.CarSafeguardSyncForTag;
import com.kaiba.m.core.middleware.mongo.interfaces.KbMongoRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @version CarSafeguardSyncForTagRepository, v0.1 2024/9/5 16:25 daopei Exp $
 **/
@Repository
public interface CarSafeguardSyncForTagRepository extends KbMongoRepository<CarSafeguardSyncForTag, String> {

    List<CarSafeguardSyncForTag> findByIdIn(List<String> ids);
}
