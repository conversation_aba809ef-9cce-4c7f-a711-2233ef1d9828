package com.kaiba.m.core.repository.news.pool;

import com.kaiba.m.core.domain.news.article.NewsArticle;
import com.kaiba.m.core.domain.news.pool.bytag.IdsTag;
import com.kaiba.m.core.domain.news.pool.bytag.IdsTagArticle;
import com.kaiba.m.core.middleware.mongo.interfaces.KbMongoRepository;
import com.kaiba.m.core.middleware.mongo.utils.KbMongoUpdate;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.core.FindAndModifyOptions;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * author: lyux
 * date: 2022-09-07
 */
@Repository
public interface IdsTagArticleRepository extends KbMongoRepository<IdsTagArticle, String> {

    Page<IdsTagArticle> findByArticleIdOrderBySeqDesc(String articleId, Pageable pageable);

    Page<IdsTagArticle> findByTagIdOrderBySeqDesc(String tagId, Pageable pageable);

    List<IdsTagArticle> getByArticleIdOrderBySeqDesc(String articleId, Pageable pageable);

    List<IdsTagArticle> getByTagIdOrderBySeqDesc(String tagId, Pageable pageable);

    void deleteByArticleIdAndTagId(String articleId, String tagId);

    default IdsTagArticle upsert(NewsArticle article, IdsTag tag, Long seq, Long idx) {
        return mongo().findAndModify(
                new Query(Criteria.where("articleId").is(article.getId()).and("tagId").is(tag.getId())),
                new KbMongoUpdate()
                        .setUnsetOnNull("idx", idx)
                        .set("seq", seq)
                        .setOnInsert("tagId", tag.getId())
                        .setOnInsert("tagName", tag.getName())
                        .setOnInsert("tagGroup", tag.getGroup())
                        .setOnInsert("articleId", article.getId())
                        .setOnInsert("createTime", System.currentTimeMillis()),
                new FindAndModifyOptions().upsert(true).returnNew(true),
                IdsTagArticle.class);
    }

}
