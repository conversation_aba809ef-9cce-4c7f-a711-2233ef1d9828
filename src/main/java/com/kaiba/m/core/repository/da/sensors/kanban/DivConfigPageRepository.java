package com.kaiba.m.core.repository.da.sensors.kanban;

import com.kaiba.m.core.domain.da.sensors.kanban.DivConfigPage;
import com.kaiba.m.core.middleware.mongo.interfaces.KbMongoRepository;
import com.kaiba.m.core.middleware.mongo.utils.KbMongoCriteria;
import org.bson.types.ObjectId;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.core.FindAndModifyOptions;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2024/02/28 14:22
 */
@Repository
public interface DivConfigPageRepository extends KbMongoRepository<DivConfigPage, String> {

    List<DivConfigPage> findByIdIn(List<String> ids);

    List<DivConfigPage> findByIdInAndState(List<String> ids, String state);

    Optional<DivConfigPage> findByMark(String mark);

    default Page<DivConfigPage> findByParam(
            String id,
            String name,
            String strategy,
            String state,
            Long executeTime,
            Pageable pageable) {
        KbMongoCriteria criteria = new KbMongoCriteria();
        criteria.regexIfNotEmpty("name", name)
                .criteriaIfNotNull("id", id)
                .criteriaIfNotNull("strategy", strategy)
                .criteriaIfNotNull("state", state);
        if (executeTime != null) {
            criteria.and("startTime").lte(executeTime)
                    .and("endTime").gt(executeTime);
        }
        Query query = new Query(criteria);
        long count = mongo().count(query, DivConfigPage.class);
        List<DivConfigPage> list = mongo().find(query.with(pageable), DivConfigPage.class);
        return new PageImpl<>(list, pageable, count);
    }

    default DivConfigPage update(DivConfigPage config) {
        Criteria criteria =
                Criteria.where("id").is(new ObjectId(config.getId()));
        Query query = new Query(criteria);
        Update update = new Update();
        update.set("name", config.getName());
        update.set("startTime", config.getStartTime());
        update.set("endTime", config.getEndTime());
        update.set("state", config.getState());
        return mongo().findAndModify(
                query, update,
                new FindAndModifyOptions().returnNew(true),
                DivConfigPage.class);
    }

}
