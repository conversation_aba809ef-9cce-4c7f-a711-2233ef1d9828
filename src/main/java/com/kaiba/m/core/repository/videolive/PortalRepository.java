package com.kaiba.m.core.repository.videolive;

import com.kaiba.m.core.domain.videolive.Portal;
import com.kaiba.m.core.middleware.mongo.interfaces.KbMongoRepository;
import org.bson.types.ObjectId;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/6/4
 */
@Repository
public interface PortalRepository extends KbMongoRepository<Portal, String> {

    List<Portal> findByVideoliveIdAndStateInOrderByStartTimeDescStateDesc(String videoliveId, Integer[] states);

    List<Portal> findByVideoliveIdOrderByStartTimeDescStateDesc(String videoliveId);

    Page<Portal> findByStateAndEndTimeLessThanEqual(Integer state, Long time, Pageable pageable);

    default void updatePortalState(String portalId, Integer state) {
        if (null == portalId || null == state) {
            return;
        }
        mongo().findAndModify(
                Query.query(Criteria.where("id").is(new ObjectId(portalId))),
                Update.update("state", state),
                Portal.class);
    }
}
