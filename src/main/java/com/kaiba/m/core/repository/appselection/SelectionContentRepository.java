package com.kaiba.m.core.repository.appselection;

import com.kaiba.lib.base.domain.appselection.SelectionCover;
import com.kaiba.m.core.domain.appselection.SelectionContent;
import com.kaiba.m.core.middleware.mongo.interfaces.KbMongoRepository;
import com.kaiba.m.core.middleware.mongo.utils.KbMongoUpdate;
import org.bson.types.ObjectId;
import org.springframework.data.domain.*;
import org.springframework.data.mongodb.core.FindAndModifyOptions;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * author: lyux
 * date: 2023-08-08
 */
@Repository
public interface SelectionContentRepository extends KbMongoRepository<SelectionContent, String> {

    List<SelectionContent> getByIdIn(Collection<String> ids);

    Page<SelectionContent> findBySiteIdOrderByIdDesc(Integer siteId, Pageable pageable);

    Page<SelectionContent> findBySiteIdAndOriginOrderByIdDesc(Integer siteId, String origin, Pageable pageable);

    default Page<SelectionContent> getPageByTargetExists(
            Integer siteId, boolean exists, String origin, String title, Integer page, Integer pageSize) {
        Criteria criteria = Criteria.where("siteId").is(siteId).and("targets.0").exists(exists);
        if (origin != null) {
            criteria.and("origin").is(origin);
        }
        if (title != null) {
            criteria.orOperator(
                Criteria.where("title").regex(title),
                Criteria.where("subTitle").regex(title)
            );
        }
        return getPageByCriteria(criteria, page, pageSize);
    }

    default Page<SelectionContent> getPageByQuery(
            Integer siteId, String target, String origin, String title, Integer page, Integer pageSize) {
        Criteria criteria = Criteria.where("siteId").is(siteId);
        if (target != null) {
            criteria.and("targets").is(target);
        }
        if (origin != null) {
            criteria.and("origin").is(origin);
        }
        if (title != null) {
            criteria.orOperator(
                Criteria.where("title").regex(title),
                Criteria.where("subTitle").regex(title)
            );
        }
        return getPageByCriteria(criteria, page, pageSize);
    }

    default List<SelectionContent> getListByRef(String ref1, String ref2, String ref3, String origin) {
        Criteria criteria = Criteria.where("ref1").is(ref1);
        if (ref2 != null) {
            criteria.and("ref2").is(ref2);
        }
        if (ref3 != null) {
            criteria.and("ref3").is(ref3);
        }
        criteria.and("origin").is(origin);
        return mongo().find(new Query(criteria), SelectionContent.class);
    }

    default void addTarget(String contentId, String target) {
        mongo().findAndModify(
                new Query(Criteria.where("_id").is(contentId)),
                new Update().addToSet("targets", target),
                new FindAndModifyOptions().upsert(false).returnNew(true),
                SelectionContent.class);
    }

    default void removeTarget(String contentId, String target) {
        mongo().findAndModify(
                new Query(Criteria.where("_id").is(contentId)),
                new Update().pull("targets", target),
                new FindAndModifyOptions().upsert(false).returnNew(true),
                SelectionContent.class);
    }

    default SelectionContent updateDisplayData(
            String contentId, String title, String subTitle,
            SelectionCover image1, SelectionCover image2, SelectionCover imageBelt,
            String action, Map<String, Object> actionParams
    ) {
        return mongo().findAndModify(
                new Query(Criteria.where("_id").is(contentId)),
                new KbMongoUpdate()
                        .setIfNotNull("title", title)
                        .setIfNotNull("subTitle", subTitle)
                        .setIfNotNull("image1", image1)
                        .setIfNotNull("image2", image2)
                        .setIfNotNull("imageBelt", imageBelt)
                        .setIfNotNull("action", action)
                        .setIfNotNull("actionParams", actionParams)
                        .set("updateTime", System.currentTimeMillis()),
                new FindAndModifyOptions().upsert(false).returnNew(true),
                SelectionContent.class);
    }

    default void incrPushCount(String contentId) {
        mongo().findAndModify(
                new Query(Criteria.where("_id").is(new ObjectId(contentId))),
                new Update()
                        .inc("pushCount", 1)
                        .set("pushTime", System.currentTimeMillis()),
                SelectionContent.class);
    }

    default Page<SelectionContent> getPageByCriteria(Criteria criteria, Integer page, Integer pageSize) {
        int p = page == null ? 0 : page - 1;
        int ps = pageSize == null ? 20 : pageSize;
        Pageable pageable = PageRequest.of(p, ps, Sort.Direction.DESC, "_id");
        Query query = new Query(criteria);
        long total = mongo().count(query, SelectionContent.class);
        List<SelectionContent> list = mongo().find(new Query(criteria).with(pageable), SelectionContent.class);
        return new PageImpl<>(list, pageable, total);
    }

}
