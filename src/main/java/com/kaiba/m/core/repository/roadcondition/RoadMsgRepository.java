package com.kaiba.m.core.repository.roadcondition;

import com.kaiba.m.core.domain.roadcondition.RoadMsg;
import com.kaiba.m.core.middleware.mongo.interfaces.KbMongoRepository;
import com.kaiba.m.core.model.roadcondition.RoadRankingModel;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.core.FindAndModifyOptions;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.data.mongodb.repository.Aggregation;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/05/21 17:45
 **/
@Repository
public interface RoadMsgRepository extends KbMongoRepository<RoadMsg, String> {

    List<RoadMsg> findBySiteIdAndCreatetimeGreaterThanOrderByCreatetimeDesc(
            Integer siteId, long createTime, Pageable pageable);

    @org.springframework.data.mongodb.repository.Query(
            value = "{siteId: ?0, source: ?1, createtime: {$lte: ?2}, endTime: {$gte: ?2}}",
            sort = "{createtime: -1}"
    )
    List<RoadMsg> findAllBySiteIdAndSource(Integer siteId, Integer source, Long time);

    @org.springframework.data.mongodb.repository.Query(
            value = "{source: ?0, location_name: {$regex: ?1}, createtime: {$gte: ?2}}",
            sort = "{createtime: -1}"
    )
    List<RoadMsg> findAllBySourceAndLocationRegex(Integer source, String search, Long time);

    @org.springframework.data.mongodb.repository.Query(
            value = "{source: ?0, createtime: {$lte: ?1}, endTime: {$gte: ?2}}",
            sort = "{createtime: -1}"
    )
    Page<RoadMsg> findAllBySourceAndTime(Integer source, Long time, Long endTime, Pageable pageable);

    @Aggregation(pipeline = {
            "{$match: {source: ?0, createtime: {$gte: ?1, $lte: ?2}}}",
            "{$group: {_id: '$userId', count: {$sum: 1}}}",
            "{$project: {'userId': '$_id', 'count': '$count'}}",
            "{$sort: {count: -1}}",
            "{$limit: 20}"
    })
    List<RoadRankingModel.RoadRankingItemModel> aggregateBySourceAndCreateTime(Integer source, Long time, Long endTime);

    default void incrEndTime(String id, long seconds) {
        mongo().findAndModify(
            new Query(Criteria.where("_id").is(id)),
            new Update().inc("endTime", seconds),
            new FindAndModifyOptions().upsert(false).returnNew(true),
            RoadMsg.class
        );
    }
}
