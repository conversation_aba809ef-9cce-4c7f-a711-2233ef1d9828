package com.kaiba.m.core.repository.shipment;

import com.kaiba.m.core.domain.shipment.Shipment;
import com.kaiba.m.core.middleware.mongo.interfaces.KbMongoRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @version ShipmentRepository, v0.1 2025/3/6 15:54 daopei Exp $
 **/
@Repository
public interface ShipmentRepository extends KbMongoRepository<Shipment, String> {

    List<Shipment> findByIdIn(List<String> ids);
}
