package com.kaiba.m.core.repository.workorder.carsafeguard;

import com.kaiba.m.core.domain.workorder.carsafeguard.CarSafeguardRideHailingCase;
import com.kaiba.m.core.middleware.mongo.interfaces.KbMongoRepository;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @version CarSafeguardInsuranceDetailRepository, v0.1 2024/7/16 16:24 daopei Exp $
 **/
@Repository
public interface CarSafeguardRideHailingRepository extends KbMongoRepository<CarSafeguardRideHailingCase, String> {

    List<CarSafeguardRideHailingCase> findByIdIn(Collection<String> ids);
}
