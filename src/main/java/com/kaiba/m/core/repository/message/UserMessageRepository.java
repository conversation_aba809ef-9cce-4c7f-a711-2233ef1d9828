package com.kaiba.m.core.repository.message;

import com.kaiba.m.core.domain.message.UserMessage;
import com.kaiba.m.core.middleware.mongo.interfaces.KbMongoRepository;
import org.bson.types.ObjectId;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Repository;

import java.util.Arrays;
import java.util.List;

/**
 * author: lyux
 * date: 18-9-28
 */
@Repository
public interface UserMessageRepository extends KbMongoRepository<UserMessage, String> {

    Sort SORT_BY_TIME_ASC = Sort.by(Sort.Direction.ASC, "createTime");
    Sort SORT_BY_TIME_DESC = Sort.by(Sort.Direction.DESC, "createTime");

    default List<UserMessage> getUnreadMessageList(Integer userId, String id, Pageable pageable) {
        return mongo().find(
                new Query(Criteria.where("userId").is(userId).and("_id").gt(new ObjectId(id)))
                        .with(SORT_BY_TIME_ASC)
                        .with(pageable),
                UserMessage.class);
    }

    default List<UserMessage> getUnreadMessageListByCreateTime(Integer userId, Long createTime, Pageable pageable) {
        return mongo().find(
                new Query(Criteria.where("userId").is(userId).and("createTime").gte(createTime))
                        .with(SORT_BY_TIME_ASC)
                        .with(pageable),
                UserMessage.class);
    }

    default List<UserMessage> getHistoryMessageList(Integer userId, Integer[] types, String id, Pageable pageable) {
        Criteria criteria = Criteria.where("userId").is(userId);
        if (null != id) {
            criteria.and("_id").lt(new ObjectId(id));
        }
        if (null != types && types.length != 0) {
            criteria.and("type").in(Arrays.asList(types));
        }
        return mongo().find(
                new Query(criteria).with(SORT_BY_TIME_DESC).with(pageable),
                UserMessage.class);
    }

}
