package com.kaiba.m.core.repository.user;

import com.kaiba.m.core.domain.user.UserRosterPermission;
import com.kaiba.m.core.middleware.mongo.interfaces.KbMongoRepository;
import com.kaiba.m.core.middleware.mongo.utils.KbMongoUpdate;
import org.springframework.data.mongodb.core.BulkOperations;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.aggregation.GroupOperation;
import org.springframework.data.mongodb.core.aggregation.MatchOperation;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Repository;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version UserRosterPermissionRepository, v0.1 2024/2/22 16:28 daopei Exp $
 **/
@Repository
public interface UserRosterPermissionRepository extends KbMongoRepository<UserRosterPermission, String> {

    List<UserRosterPermission> findByRosterId(String rosterId);

    List<UserRosterPermission> findByRosterIdIn(List<String> rosterId);

    default void bulkInsert(List<UserRosterPermission> list) {
        BulkOperations operations = mongo().bulkOps(BulkOperations.BulkMode.ORDERED, UserRosterPermission.class);
        for (UserRosterPermission p : list) {
            Query query = new Query(
                    Criteria.where("rosterId").is(p.getRosterId())
                            .and("menuId").is(p.getMenuId()));
            Update update = new KbMongoUpdate()
                    .setOnInsert("createTime", System.currentTimeMillis())
                    .set("updateTime", System.currentTimeMillis());
            operations.upsert(query, update);
        }
        operations.execute();
    }

    default void bulkDelete(List<UserRosterPermission> list) {
        BulkOperations operations = mongo().bulkOps(BulkOperations.BulkMode.ORDERED, UserRosterPermission.class);
        for (UserRosterPermission p : list) {
            Query query = new Query(
                    Criteria.where("rosterId").is(p.getRosterId())
                            .and("menuId").is(p.getMenuId()));
            operations.remove(query);
        }
        operations.execute();
    }

    default Map<String, Integer> groupByRosterIdIn(List<String> rosterIdList) {
        MatchOperation matchOperation = Aggregation.match(
                Criteria.where("rosterId").in(rosterIdList));
        GroupOperation sumGroup = Aggregation.group("rosterId")
                .first("rosterId").as("rosterId")
                .count().as("count");
        Aggregation aggregation = Aggregation.newAggregation(matchOperation, sumGroup);
        List<Map> mapList = mongo().aggregate(aggregation, UserRosterPermission.class, Map.class).getMappedResults();
        if (mapList.isEmpty()) {
            return Collections.emptyMap();
        }
        return mapList.stream().collect(
                Collectors.toMap(
                        map -> (String) map.get("rosterId"),
                        map -> (Integer) map.get("count")
                )
        );

    }
}
