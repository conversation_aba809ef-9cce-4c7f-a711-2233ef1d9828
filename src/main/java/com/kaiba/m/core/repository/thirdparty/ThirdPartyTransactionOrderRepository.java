package com.kaiba.m.core.repository.thirdparty;

import com.kaiba.lib.base.constant.thirdparty.TPTransactionOrderState;
import com.kaiba.m.core.domain.prize.Prize;
import com.kaiba.m.core.domain.thirdparty.TPTransactionOrder;
import com.kaiba.m.core.middleware.mongo.interfaces.KbMongoRepository;
import com.kaiba.m.core.middleware.mongo.utils.KbMongoBeanCriteria;
import com.kaiba.m.core.middleware.mongo.utils.KbMongoUpdate;
import org.springframework.data.domain.*;
import org.springframework.data.mongodb.core.FindAndModifyOptions;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Repository;

import java.util.Collections;
import java.util.List;
import java.util.Optional;

/**
 * author: lyux
 * date: 2020-09-02
 */
@Repository
public interface ThirdPartyTransactionOrderRepository extends KbMongoRepository<TPTransactionOrder, String> {

    Optional<TPTransactionOrder> findFirstByAccessIdAndBusinessId(
            String accessId, String businessId);

    Optional<TPTransactionOrder> findFirstByAccessIdAndBusinessIdAndBusinessType(
            String accessId, String businessId, String businessType);

    Optional<TPTransactionOrder> findFirstByTransactionId(String transactionId);

    Page<TPTransactionOrder> findByStateOrderByIdDesc(Integer state, Pageable page);

    Page<TPTransactionOrder> findByStateAndPayExpireTimeGreaterThanOrderByIdDesc(Integer state, Long expireTime, Pageable page);

    Page<TPTransactionOrder> findByAccessIdOrderByIdDesc(String accessId, Pageable page);

    Page<TPTransactionOrder> findByAccessIdAndStateOrderByIdDesc(String accessId, Integer state, Pageable page);

    Page<TPTransactionOrder> findByUserIdOrderByIdDesc(Integer userId, Pageable page);

    Page<TPTransactionOrder> findByUserIdAndStateOrderByIdDesc(Integer userId, Integer state, Pageable page);

    Page<TPTransactionOrder> findAllByOrderByIdDesc(Pageable page);

    default Optional<TPTransactionOrder> getFirstWithEntity(TPTransactionOrder order) {
        if (null == order) {
            return Optional.empty();
        } else {
            return Optional.ofNullable(mongo().findOne(createQueryByEntity(order), TPTransactionOrder.class));
        }
    }

    default Page<TPTransactionOrder> getPageWithEntity(TPTransactionOrder order, Pageable pageable) {
        if (null == order) {
            return Page.empty();
        } else {
            if (pageable == null) {
                pageable = PageRequest.of(0, 15, Sort.by(Sort.Direction.DESC, "_id"));
            }
            Query query = createQueryByEntity(order);
            long total = mongo().count(query, Prize.class);
            List<TPTransactionOrder> prizeList = mongo().find(query.with(pageable), TPTransactionOrder.class);
            return new PageImpl<>(prizeList, pageable, total);
        }
    }

    default List<TPTransactionOrder> getListWithEntity(TPTransactionOrder order, Pageable pageable) {
        if (null == order) {
            return Collections.emptyList();
        } else {
            if (pageable == null) {
                pageable = PageRequest.of(0, 15, Sort.by(Sort.Direction.DESC, "_id"));
            }
            return mongo().find(createQueryByEntity(order).with(pageable), TPTransactionOrder.class);
        }
    }

    default TPTransactionOrder updateStateAsPayed(TPTransactionOrder order, String transactionId) {
        return mongo().findAndModify(
                new Query(Criteria.where("_id").is(order.getId())
                        .and("state").is(TPTransactionOrderState.PENDING.getValue())
                        .and("v").is(order.getV())
                ),
                new Update()
                        .set("state", TPTransactionOrderState.PAYED.getValue())
                        .set("transactionId", transactionId)
                        .set("updateTime", System.currentTimeMillis() / 1000)
                        .inc("v", 1),
                new FindAndModifyOptions().returnNew(true).upsert(false),
                TPTransactionOrder.class);
    }

    default TPTransactionOrder updateStateAsCanceled(
            TPTransactionOrder order, Integer operatorId, int reasonType, String reason) {
        return mongo().findAndModify(
                new Query(Criteria.where("_id").is(order.getId())
                        .and("state").is(TPTransactionOrderState.PENDING.getValue())
                        .and("v").is(order.getV())
                ),
                new KbMongoUpdate()
                        .setIfNotEmpty("cancelUserId", operatorId)
                        .set("state", TPTransactionOrderState.CANCELED.getValue())
                        .set("cancelReasonType", reasonType)
                        .set("cancelReason", reason)
                        .set("updateTime", System.currentTimeMillis() / 1000)
                        .inc("v", 1),
                new FindAndModifyOptions().returnNew(true).upsert(false),
                TPTransactionOrder.class);
    }

    default Query createQueryByEntity(TPTransactionOrder order) {
        Criteria criteria = new KbMongoBeanCriteria(order)
                .criteriaIfNotNull("transactionId")
                .criteriaIfNotNull("accessId")
                .criteriaIfNotNull("businessId")
                .criteriaIfNotNull("businessType")
                .criteriaIfNotNull("userId")
                .criteriaIfNotNull("state")
                .criteriaIfNotNull("refId1")
                .criteriaIfNotNull("refId2")
                .criteriaIfNotNull("refId3");
        return new Query(criteria);
    }

}
