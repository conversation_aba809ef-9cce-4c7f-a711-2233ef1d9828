package com.kaiba.m.core.repository.education.recitation;

import com.kaiba.m.core.model.education.recitation.dto.ApplyRecordQueryDTO;
import com.kaiba.m.core.domain.education.recitation.ApplyRecord;
import com.kaiba.m.core.middleware.mongo.interfaces.KbMongoRepository;
import java.util.List;
import java.util.Objects;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Repository;

/**
 * Description: 报名记录repository
 * Author: ZM227
 * Date: 2024/8/7 17:35
 */
@Repository
public interface ApplyRecordRepository extends KbMongoRepository<ApplyRecord, String> {

    ApplyRecord findFirstByResourceCodeAndNameAndConnectPhone(String resourceCode, String name,
        String connectPhone);

    default Page<ApplyRecord> findAllByCondition(ApplyRecordQueryDTO queryDTO) {
        Criteria criteria = new Criteria();
        if (StringUtils.isNotBlank(queryDTO.getResourceCode())) {
            criteria.and("resourceCode").is(queryDTO.getResourceCode());
        }
        if (Objects.nonNull(queryDTO.getKbUserId())) {
            criteria.and("kbUserId").is(queryDTO.getKbUserId());
        }
        Query query = new Query(criteria);
        long total = mongo().count(query, ApplyRecord.class);
        List<ApplyRecord> list = mongo().find(query.with(queryDTO.getPageable()), ApplyRecord.class);
        return new PageImpl<>(list, queryDTO.getPageable(), total);
    }
}
