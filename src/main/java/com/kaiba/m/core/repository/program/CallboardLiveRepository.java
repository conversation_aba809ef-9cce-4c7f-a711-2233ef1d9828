package com.kaiba.m.core.repository.program;

import com.kaiba.m.core.constant.program.CallboardLiveStatusEnum;
import com.kaiba.m.core.domain.program.CallboardLive;
import com.kaiba.m.core.middleware.mongo.interfaces.KbMongoRepository;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * author: duanyf
 * date: 2024-07-18
 **/
@Repository
public interface CallboardLiveRepository extends KbMongoRepository<CallboardLive, String> {
    default Page<CallboardLive> getList(Integer siteId, Integer top, Integer status, Long startCreateTime, Long endCreateTime, Sort sortData, Pageable pageable) {
        Criteria criteria = new Criteria();
        criteria.and("siteId").is(siteId);
        if (top != null) {
            criteria.and("top").is(top);
        }
        if (status != null) {
            criteria.and("status").is(status);
        }
        if (startCreateTime != null || endCreateTime != null) {
            criteria = criteria.and("createTime");
            if (startCreateTime != null) {
                criteria.gte(startCreateTime);
            }
            if (endCreateTime != null) {
                criteria.lte(endCreateTime);
            }
        }
        Query query = Query.query(criteria).with(sortData);
        long count = mongo().count(query, CallboardLive.class);
        List<CallboardLive> list = mongo().find(query.with(pageable), CallboardLive.class);
        return new PageImpl<>(list, pageable, count);
    }

    default CallboardLive updateStatus(Integer siteId, String id, Integer status) {
        return mongo().findAndModify(
                new Query(Criteria.where("id").is(id)
                        .and("siteId").is(siteId)),
                new Update().set("status", status),
                CallboardLive.class);
    }

    default void updateStatusByUnplay(Integer siteId, Integer status) {
        mongo().updateMulti(
                new Query(Criteria.where("siteId").is(siteId)
                        .and("status").is(CallboardLiveStatusEnum.UNPLAY.getValue())),
                new Update().set("status", status),
                CallboardLive.class);
    }

    default CallboardLive updateTop(Integer siteId, String id, Integer top) {
        return mongo().findAndModify(
                new Query(Criteria.where("id").is(id)
                        .and("siteId").is(siteId)),
                new Update().set("top", top),
                CallboardLive.class);
    }
}
