package com.kaiba.m.core.repository.da.getui;

import com.kaiba.lib.base.response.KbCode;
import com.kaiba.lib.base.response.KbException;
import com.kaiba.m.core.controller.da.getui.model.GTAItemContentUsage;
import com.kaiba.m.core.domain.da.getui.GTAItemContent;
import com.kaiba.m.core.middleware.mongo.interfaces.KbMongoRepository;
import org.bson.types.ObjectId;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.core.FindAndModifyOptions;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Repository;

import java.util.Optional;

/**
 * <AUTHOR>
 * @version GTAFocusContentRepository, v0.1 2024/12/18 09:54 daopei Exp $
 **/
@Repository
public interface GTAItemContentRepository extends KbMongoRepository<GTAItemContent, String> {

    Optional<GTAItemContent> findFirstByUsage(String usage);

    Page<GTAItemContent> findByUsageOrderByIdxDesc(String usage, Pageable pageable);

    void deleteByUsage(String usage);

    default void updateDataById(GTAItemContent content) {
        mongo().findAndModify(
                new Query(Criteria.where("_id").is(new ObjectId(content.getId()))),
                new Update()
                        .set("contentId", content.getContentId())
                        .set("title", content.getTitle())
                        .set("image", content.getImage())
                        .set("idx", content.getIdx())
                        .set("updateTime", System.currentTimeMillis()),
                new FindAndModifyOptions().returnNew(false),
                GTAItemContent.class);
    }

    default void updateDataByContentId(GTAItemContent content) {
        mongo().findAndModify(
                new Query(Criteria.where("contentId").is(content.getContentId())),
                new Update()
                        .set("title", content.getTitle())
                        .set("image", content.getImage())
                        .set("idx", content.getIdx())
                        .set("updateTime", System.currentTimeMillis()),
                new FindAndModifyOptions().returnNew(false),
                GTAItemContent.class);
    }

    default GTAItemContent upsertDataByUsage(GTAItemContent content) {
        boolean isUnique = GTAItemContentUsage.resolveByName(content.getUsage())
                .map(GTAItemContentUsage::isUnique)
                .orElse(false);
        if (!isUnique) {
            throw new KbException(KbCode.ILLEGAL_ARGUMENT, "usage should be unique: " + content);
        }
        long now = System.currentTimeMillis();
        return mongo().findAndModify(
                new Query(Criteria.where("usage").is(content.getUsage())),
                new Update()
                        .set("contentId", content.getContentId())
                        .set("title", content.getTitle())
                        .set("image", content.getImage())
                        .set("updateTime", now)
                        .setOnInsert("idx", 1)
                        .setOnInsert("usage", content.getUsage())
                        .setOnInsert("createTime", now),
                new FindAndModifyOptions().upsert(true).returnNew(true),
                GTAItemContent.class);
    }

}
