package com.kaiba.m.core.repository.da.sensors.kanban;

import com.kaiba.m.core.domain.da.sensors.kanban.KanbanConfigGroup;
import com.kaiba.m.core.middleware.mongo.interfaces.KbMongoRepository;
import com.kaiba.m.core.middleware.mongo.utils.KbMongoCriteria;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.core.FindAndModifyOptions;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @version KanbanConfigGroupRepository, v0.1 2024/3/18 11:32 daopei Exp $
 **/
@Repository
public interface KanbanConfigGroupRepository extends KbMongoRepository<KanbanConfigGroup, String> {


    default KanbanConfigGroup update(KanbanConfigGroup config) {
        return mongo().findAndModify(
                new Query(Criteria.where("_id").is(config.getId())),
                new Update()
                        .set("name", config.getName())
                        .set("key", config.getKey())
                        .set("configIds", config.getConfigIds())
                        .set("updateTime", System.currentTimeMillis()),
                new FindAndModifyOptions().returnNew(true),
                KanbanConfigGroup.class
        );
    }


    default Page<KanbanConfigGroup> findByParam(
            String id,
            String name,
            String key,
            String configId,
            Pageable pageable
    ) {
        KbMongoCriteria criteria = new KbMongoCriteria();
        criteria.regexIfNotEmpty("name", name)
                .criteriaIfNotNull("id", id)
                .criteriaIfNotNull("key", key)
                .regexIfNotEmpty("configIds", configId);
        Query query = new Query(criteria);
        long count = mongo().count(query, KanbanConfigGroup.class);
        List<KanbanConfigGroup> list = mongo().find(query.with(pageable), KanbanConfigGroup.class);
        return new PageImpl<>(list, pageable, count);
    }

}
