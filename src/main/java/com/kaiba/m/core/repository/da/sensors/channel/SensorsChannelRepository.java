package com.kaiba.m.core.repository.da.sensors.channel;

import com.kaiba.m.core.domain.da.sensors.channel.SensorsChannel;
import com.kaiba.m.core.middleware.mongo.interfaces.KbMongoRepository;
import com.kaiba.m.core.middleware.mongo.utils.KbMongoUpdate;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.core.FindAndModifyOptions;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @version ChannelRepository, v0.1 2024/5/13 10:00 daopei Exp $
 **/
@Repository
public interface SensorsChannelRepository extends KbMongoRepository<SensorsChannel, String> {

    Optional<SensorsChannel> findByKey(String key);

    List<SensorsChannel> findByKeyIn(Collection<String> keys);

    default Page<SensorsChannel> getByParam(String key, String name, Pageable pageable) {
        Criteria criteria = new Criteria();
        if (key != null) {
            criteria.and("key").is(key);
        }
        if (name != null) {
            criteria.and("name").regex(name);
        }
        Query query = new Query(criteria);
        long count = mongo().count(query, SensorsChannel.class);
        List<SensorsChannel> list = mongo().find(new Query(criteria).with(pageable), SensorsChannel.class);
        return new PageImpl<>(list, pageable, count);
    }

    default SensorsChannel upsert(String key, String name, String userRosterOrgId, String shortName, String description) {
        return mongo().findAndModify(
                new Query(Criteria.where("key").is(key)),
                new KbMongoUpdate()
                        .setIfNotNull("name", name)
                        .setIfNotNull("userRosterOrgId", userRosterOrgId)
                        .setIfNotNull("shortName", shortName)
                        .setIfNotNull("description", description)
                        .setOnInsert("createTime", System.currentTimeMillis())
                        .set("updateTime", System.currentTimeMillis()),
                FindAndModifyOptions.options().returnNew(true).upsert(true),
                SensorsChannel.class
        );
    }
}
