package com.kaiba.m.core.repository.user;

import com.kaiba.m.core.domain.user.UserAddress;
import com.kaiba.m.core.middleware.mongo.interfaces.KbMongoRepository;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Repository;

import java.util.Optional;

/**
 * author: lyux
 * date: 2020-09-29
 */
@Repository
public interface UserAddressRepository extends KbMongoRepository<UserAddress, String> {

    void deleteByIdAndUserId(String id, Integer userId);

    long countByUserId(Integer userId);

    Optional<UserAddress> findFirstByUserId(Integer userId);

    Optional<UserAddress> findFirstByUserIdAndIsDefault(Integer userId, boolean isDefault);

    Page<UserAddress> findAllBy(Pageable pageable);

    Page<UserAddress> findByUserId(Integer userId, Pageable pageable);

}
