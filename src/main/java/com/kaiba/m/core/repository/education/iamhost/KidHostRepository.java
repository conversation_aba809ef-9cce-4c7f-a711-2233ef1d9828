package com.kaiba.m.core.repository.education.iamhost;

import com.kaiba.m.core.domain.education.iamhost.KidHost;
import com.kaiba.m.core.middleware.mongo.interfaces.KbMongoRepository;

import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.core.FindAndModifyOptions;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;

import java.util.Collection;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2024/03/20 09:56
 */
public interface KidHostRepository extends KbMongoRepository<KidHost, String> {

    Optional<KidHost> findFirstByKidId(String kidId);

    List<KidHost> findAllByNameRegex(String name, Pageable pageable);

    List<KidHost> findAllBySchoolNameRegex(String name, Pageable pageable);

    List<KidHost> findAllByKidIdIn(Collection<String> kidIds);

    default KidHost upsert(KidHost kidHost) {
        return mongo().findAndModify(
                new Query(Criteria.where("kidId").is(kidHost.getKidId())),
                new Update()
                        .setOnInsert("createTime", System.currentTimeMillis())
                        .set("name", kidHost.getName())
                        .set("schoolName", kidHost.getSchoolName())
                        .set("image", kidHost.getImage())
                        .set("video", kidHost.getVideo())
                        .set("updateTime", System.currentTimeMillis()),
                new FindAndModifyOptions().upsert(true).returnNew(true),
                KidHost.class);
    }

    default void updateNameAndSchoolName(String kidId, String name, String schoolName) {
        mongo().findAndModify(
            new Query(Criteria.where("kidId").is(kidId)),
            new Update()
                .set("name", name)
                .set("schoolName", schoolName),
            new FindAndModifyOptions().upsert(false).returnNew(true),
            KidHost.class);
    }
}
