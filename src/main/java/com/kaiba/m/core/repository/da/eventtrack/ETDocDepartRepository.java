package com.kaiba.m.core.repository.da.eventtrack;

import com.kaiba.m.core.domain.da.eventtrack.ETDocDepart;
import com.kaiba.m.core.middleware.mongo.interfaces.KbMongoRepository;
import com.kaiba.m.core.middleware.mongo.utils.KbMongoUpdate;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.core.FindAndModifyOptions;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @version ETDocDepartRepository, v0.1 2025/4/8 17:51 daopei Exp $
 **/
@Repository
public interface ETDocDepartRepository extends KbMongoRepository<ETDocDepart, String> {

    default Page<ETDocDepart> getPageQuery(String channel, String name, List<String> departList, Pageable pageable) {
        Criteria criteria = new Criteria();
        if (channel != null) {
            criteria.and("channel").is(channel);
        }
        if (name != null) {
            criteria.and("name").regex(name);
        }
        if (departList != null && !departList.isEmpty()) {
            criteria.and("depart").in(departList);
        }
        Query query = new Query(criteria);
        long total = mongo().count(query, ETDocDepart.class);
        List<ETDocDepart> list = mongo().find(query.with(pageable), ETDocDepart.class);
        return new PageImpl<>(list, pageable, total);
    }

    default ETDocDepart upsert(ETDocDepart depart) {
        return mongo().findAndModify(
                new Query(Criteria.where("depart").is(depart.getDepart()).and("channel").is(depart.getChannel())),
                new KbMongoUpdate()
                        .setOnInsert("depart", depart.getDepart())
                        .setOnInsert("channel", depart.getChannel())
                        .setOnInsert("createTime", System.currentTimeMillis())
                        .set("updateTime", System.currentTimeMillis())
                        .set("name", depart.getName()),
                FindAndModifyOptions.options().upsert(true).returnNew(true),
                ETDocDepart.class
        );
    }
}
