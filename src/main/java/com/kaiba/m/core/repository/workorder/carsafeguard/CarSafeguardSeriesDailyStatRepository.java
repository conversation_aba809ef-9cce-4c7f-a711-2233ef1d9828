package com.kaiba.m.core.repository.workorder.carsafeguard;

import com.kaiba.m.core.domain.workorder.carsafeguard.stat.CarSafeguardSeriesDailyStat;
import com.kaiba.m.core.middleware.mongo.interfaces.KbMongoRepository;
import com.kaiba.m.core.model.safeguard.stat.CarSafeguardSeriesStatModel;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.repository.Aggregation;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @version CarSafeguardSeriesDailyStatRepository, v0.1 2024/11/13 16:51 daopei Exp $
 **/
@Repository
public interface CarSafeguardSeriesDailyStatRepository extends KbMongoRepository<CarSafeguardSeriesDailyStat, String> {

    Optional<CarSafeguardSeriesDailyStat> findByCarSeriesAndDay(String carSeries, Integer day);


    @Aggregation(value = {
            "{$group: {_id: '$carSeries',totalCount: { $sum: '$count' },allTagCounts: {$push: '$tagCount'}}}",
            "{$unwind: {path: '$allTagCounts', preserveNullAndEmptyArrays: true}}",
            "{$project: {_id: 1, totalCount: 1, entries: {$map: {input: {$objectToArray: '$allTagCounts'}, as: 'entry', in: {tagKey: '$$entry.k', tagValue: '$$entry.v'}}}}}",
            "{$unwind: {path: '$entries', preserveNullAndEmptyArrays: true}}",
            "{$group: {_id: {carSeries: '$_id', tagKey: '$entries.tagKey'}, totalCount: {$first: '$totalCount'}, tagSum: {$sum: '$entries.tagValue'}}}",
            "{$group: {_id: '$_id.carSeries', carSeries: { $first: '$_id.carSeries' }, count: { $first: '$totalCount' }, tagStats: {$push: {tagCode: '$_id.tagKey', tagCount: '$tagSum'}}}}",
            "{$sort: {count: -1, carSeries: 1}}",
            "{$skip: ?0}",
            "{$limit: ?1}"
    })
    List<CarSafeguardSeriesStatModel> aggregateCarSeriesStats(Long offset, Integer pageSize);
}
