package com.kaiba.m.core.repository.workorder;

import com.kaiba.lib.base.constant.workorder.WOCaseState;
import com.kaiba.lib.base.constant.workorder.WOCloseReason;
import com.kaiba.lib.base.constant.workorder.WOIdentity;
import com.kaiba.lib.base.domain.workorder.WOAccess;
import com.kaiba.lib.base.domain.workorder.WOCaseListQueryModel;
import com.kaiba.m.core.domain.workorder.WOCase;
import com.kaiba.m.core.domain.workorder.aggregation.WOCaseRatingAggr;
import com.kaiba.m.core.domain.workorder.aggregation.WOCountAggr;
import com.kaiba.m.core.middleware.mongo.interfaces.KbMongoRepository;
import org.bson.types.ObjectId;
import org.springframework.data.domain.*;
import org.springframework.data.mongodb.core.FindAndModifyOptions;
import org.springframework.data.mongodb.core.aggregation.AggregationResults;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.data.mongodb.repository.Aggregation;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

/**
 * author: lyux
 * date: 2023-08-11
 */
@Repository
public interface WOCaseRepository extends KbMongoRepository<WOCase, String> {

    WOCase findFirstByHooloRefId(String hooloRefId);

    WOCase findFirstByOldSafeguard(String oldSafeguardId);

    Optional<WOCase> findFirstByBizAndOrigin(String biz, String origin);

    List<WOCase> getByIdIn(Collection<String> caseIds);

    Page<WOCase> findByCloseTimeGreaterThanAndRatingNullAndBizAndState(
            long time, String biz, String state, Pageable pageable);

    Page<WOCase> findByCloseTimeLessThanAndRatingNullAndBizAndState(
            long time, String biz, String state, Pageable pageable);

    Page<WOCase> findByClientMobileAndBizAndClientUserIdNull(String mobile, String biz, Pageable pageable);

    Page<WOCase> findByCreateTimeBetween(Long createTimeAfter, Long createTimeBefore, Pageable pageable);

    long countByBiz(String biz);

    long countByBizAndCreateTimeBetween(String biz, long st, long et);

    long countByClientMobileAndBizAndClientUserIdNull(String mobile, String biz);

    long countByBizAndState(String biz, String state);

    long countByCloseTimeGreaterThanAndBizAndState(long time, String biz, String state);

    long countByCloseTimeGreaterThanAndBizAndStateAndResolver(
            long time, String biz, String state, String resolver);

    long countByCloseTimeGreaterThanAndBizAndStateAndResolverListContaining(
            long time, String biz, String state, String resolver);

    @Aggregation(pipeline = {
            "{ $match: { closeTime: { $gt: ?0 }, statRating: { $gt: 0 }, biz: ?1, state: ?2, 'resolverList': { $in: [?3] } } }",
            "{ $group: { _id: 'resolver', ratingCount: { $sum: 1 }, ratingSum: { $sum: '$statRating' }, ratingAverage: { $avg: '$statRating' } } }"
    })
    AggregationResults<WOCaseRatingAggr> aggrStatRatingByCloseTimeGT(
            long time, String biz, String state, List<String> resolvers);

    @Aggregation(pipeline = {
            "{ $match: { biz: ?0 }}",
            "{ $group: { _id: '$state', count: { $sum: 1 } }}"
    })
    AggregationResults<WOCountAggr> aggrCaseCountGroupByState(String biz);

    @Aggregation(pipeline = {
            "{ $match: { biz: ?0 }}",
            "{ $group: { _id: '$origin', count: { $sum: 1 } }}"
    })
    AggregationResults<WOCountAggr> aggrCaseCountGroupByOrigin(String biz);

    @Aggregation(pipeline = {
            "{ $match: { createTime: {$gt: ?1, $lt: ?2}, biz: ?0 }}",
            "{ $group: { _id: '$origin', count: { $sum: 1 } }}"
    })
    AggregationResults<WOCountAggr> aggrCaseCountGroupByOrigin(String biz, long st, long et);

    @Aggregation(pipeline = {
            "{ $match: { biz: ?0 }}",
            "{ $group: { _id: '$closeType', count: { $sum: 1 } }}"
    })
    AggregationResults<WOCountAggr> aggrCaseCountGroupByCloseType(String biz);

    @Aggregation(pipeline = {
            "{ $match: { createTime: {$gt: ?1, $lt: ?2}, biz: ?0 }}",
            "{ $group: { _id: '$closeType', count: { $sum: 1 } }}"
    })
    AggregationResults<WOCountAggr> aggrCaseCountGroupByCloseType(String biz, long st, long et);

    default Page<WOCase> getListByQuery(WOCaseListQueryModel model) {
        if (null == model || model.getBiz() == null) {
            return Page.empty();
        }
        Criteria criteria = Criteria.where("biz").is(model.getBiz());
        if (model.getClientUserId() != null) {
            criteria.and("clientUserId").is(model.getClientUserId());
        }
        if (model.getClientMobile() != null) {
            criteria.and("clientMobile").is(model.getClientMobile());
        }
        if (model.getCurrentResolver() != null) {
            criteria.and("resolver").is(model.getCurrentResolver());
        } else if (model.getAnyResolver() != null) {
            criteria.and("resolverList").is(model.getAnyResolver());
        }
        if (model.getConfigId() != null) {
            criteria.and("configId").is(model.getConfigId());
        }
        if (model.getCloseType() != null) {
            criteria.and("closeType").is(model.getCloseType());
            if (model.getStates() == null) {
                model.setStates(Collections.singletonList(WOCaseState.CLOSED.name()));
            }
        }
        if (model.getStates() != null) {
            if (model.getStates().size() == 1) {
                criteria.and("state").is(model.getStates().get(0));
            } else if (model.getStates().size() > 1) {
                criteria.and("state").in(model.getStates());
            }
        }

        if (model.getRangeSt() != null && model.getRangeEt() != null && model.getRangeType() != null) {
            if (model.getRangeType() == WOCaseListQueryModel.SORT_BY_UPDATE_TIME_DESC) {
                criteria.and("updateTime").gt(model.getRangeSt()).lt(model.getRangeEt());
            } else if (model.getRangeType() == WOCaseListQueryModel.SORT_BY_CLOSE_TIME_DESC) {
                criteria.and("closeTime").gt(model.getRangeSt()).lt(model.getRangeEt());
            } else {
                criteria.and("createTime").gt(model.getRangeSt()).lt(model.getRangeEt());
            }
        }

        int p = model.getPage() == null ? 0 : model.getPage() - 1;
        int ps = model.getPageSize() == null ? 20 : model.getPageSize();
        Pageable pageable;
        if (model.getSortBy() == null) {
            pageable = PageRequest.of(p, ps, Sort.by(Sort.Direction.DESC, "_id"));
        } else if (model.getSortBy() == WOCaseListQueryModel.SORT_BY_UPDATE_TIME_DESC) {
            pageable = PageRequest.of(p, ps, Sort.by(Sort.Direction.DESC, "updateTime"));
        } else if (model.getSortBy() == WOCaseListQueryModel.SORT_BY_CLOSE_TIME_DESC) {
            pageable = PageRequest.of(p, ps, Sort.by(Sort.Direction.DESC, "closeTime"));
        } else {
            pageable = PageRequest.of(p, ps, Sort.by(Sort.Direction.DESC, "createTime"));
        }

        Query query = Query.query(criteria);
        long total = mongo().count(query, WOCase.class);
        List<WOCase> list = mongo().find(query.with(pageable), WOCase.class);
        return new PageImpl<>(list, pageable, total);
    }

    default WOCase updateProgress(
            String caseId, String state, String lastEventId, String resolver, String pendingTeam, String bizState) {
        Update update = new Update().set("updateTime", System.currentTimeMillis());
        if (state != null) {
            update.set("state", state);
        }
        if (resolver == null) {
            update.unset("resolver");
        } else {
            update.set("resolver", resolver).addToSet("resolverList", resolver);
        }
        if (pendingTeam != null) {
            update.set("pendingTeam", pendingTeam);
        }
        if (lastEventId != null) {
            update.set("lastEventId", lastEventId);
        }
        if (bizState != null) {
            update.set("bizState", bizState);
        }
        return mongo().findAndModify(
                new Query(Criteria.where("_id").is(new ObjectId(caseId))), update,
                new FindAndModifyOptions().returnNew(true).upsert(false),
                WOCase.class);
    }

    default WOCase updateRate(
            String caseId, String lastEventId,
            Integer rating, Integer userRating, Integer statRating) {
        return mongo().findAndModify(
                new Query(Criteria.where("_id").is(new ObjectId(caseId))),
                new Update()
                        .set("rating", rating)
                        .set("userRating", userRating)
                        .set("statRating", statRating)
                        .set("lastEventId", lastEventId)
                        .set("updateTime", System.currentTimeMillis()),
                new FindAndModifyOptions().returnNew(true).upsert(false),
                WOCase.class);
    }

    default WOCase updateStatRate(String caseId, Integer statRating) {
        return mongo().findAndModify(
                new Query(Criteria.where("_id").is(new ObjectId(caseId))),
                new Update()
                        .set("statRating", statRating)
                        .set("updateTime", System.currentTimeMillis()),
                new FindAndModifyOptions().returnNew(true).upsert(false),
                WOCase.class);
    }

    default WOCase updateStateAsAccepted(String caseId, String lastEventId, String resolver) {
        long now = System.currentTimeMillis();
        return mongo().findAndModify(
                new Query(Criteria.where("_id").is(new ObjectId(caseId))),
                new Update()
                        .set("state", WOCaseState.DOING.name())
                        .set("resolver", resolver).addToSet("resolverList", resolver)
                        .set("pendingTeam", resolver)
                        .set("lastEventId", lastEventId)
                        .set("updateTime", now),
                new FindAndModifyOptions().returnNew(true).upsert(false),
                WOCase.class);
    }

    default WOCase updateStateAsClosed(
            String caseId, String lastEventId, String closeType, String closeReasonText) {
        long now = System.currentTimeMillis();
        return mongo().findAndModify(
                new Query(Criteria.where("_id").is(new ObjectId(caseId))),
                new Update()
                        .set("state", WOCaseState.CLOSED.name())
                        .set("closeType", closeType)
                        .set("closeText", closeReasonText)
                        .unset("resolver")
                        .set("pendingTeam", WOIdentity.CLIENT.asTeam())
                        .set("lastEventId", lastEventId)
                        .set("closeTime", now)
                        .set("updateTime", now),
                new FindAndModifyOptions().returnNew(true).upsert(false),
                WOCase.class);
    }

    default WOCase updateBizState(String caseId, String bizState) {
        long now = System.currentTimeMillis();
        return mongo().findAndModify(
                new Query(Criteria.where("_id").is(new ObjectId(caseId))),
                new Update()
                        .set("state", WOCaseState.DOING.name())
                        .set("bizState", bizState)
                        .set("updateTime", now),
                new FindAndModifyOptions().returnNew(true).upsert(false),
                WOCase.class);
    }

    default WOCase updatePendingTeam(String caseId, String pendingTeam) {
        return mongo().findAndModify(
                new Query(Criteria.where("_id").is(new ObjectId(caseId))),
                new Update()
                        .set("pendingTeam", pendingTeam)
                        .set("updateTime", System.currentTimeMillis()),
                new FindAndModifyOptions().returnNew(true).upsert(false),
                WOCase.class);
    }

    default WOCase updateAcl(String caseId, List<WOAccess> acl) {
        Update update = new Update();
        if (acl == null || acl.isEmpty()) {
            update.unset("acl").set("updateTime", System.currentTimeMillis());
        } else {
            update.set("acl", acl).set("updateTime", System.currentTimeMillis());
        }
        return mongo().findAndModify(
                new Query(Criteria.where("_id").is(new ObjectId(caseId))), update,
                new FindAndModifyOptions().returnNew(true).upsert(false),
                WOCase.class);
    }

    default WOCase updateArchived(String caseId, boolean archived) {
        return mongo().findAndModify(
                new Query(Criteria.where("_id").is(new ObjectId(caseId))),
                new Update()
                        .set("archived", archived)
                        .set("updateTime", System.currentTimeMillis()),
                new FindAndModifyOptions().returnNew(true).upsert(false),
                WOCase.class);
    }

    default WOCase updateOnlookerThreadId(String caseId, String onlookerThreadId) {
        return mongo().findAndModify(
                new Query(Criteria.where("_id").is(new ObjectId(caseId))),
                new Update()
                        .set("onlookerThreadId", onlookerThreadId)
                        .set("updateTime", System.currentTimeMillis()),
                new FindAndModifyOptions().returnNew(true).upsert(false),
                WOCase.class);
    }

    default void attachClientUserIdByMobile(String biz, String mobile, Integer userId) {
        mongo().updateMulti(
                new Query(Criteria
                        .where("clientMobile").is(mobile)
                        .and("biz").is(biz)
                        .and("clientUserId").exists(false)),
                new Update()
                        .set("clientUserId", userId)
                        .set("updateTime", System.currentTimeMillis()),
                WOCase.class);
    }

    default WOCase updateClientUserId(String caseId, Integer userId) {
        return mongo().findAndModify(
                new Query(Criteria.where("_id").is(new ObjectId(caseId))),
                new Update().set("clientUserId", userId).set("updateTime", System.currentTimeMillis()),
                new FindAndModifyOptions().returnNew(true).upsert(false),
                WOCase.class);
    }

    default WOCase updateClientMobile(String caseId, String mobile) {
        return mongo().findAndModify(
                new Query(Criteria.where("_id").is(new ObjectId(caseId))),
                new Update().set("clientMobile", mobile).set("updateTime", System.currentTimeMillis()),
                new FindAndModifyOptions().returnNew(true).upsert(false),
                WOCase.class);
    }

    default WOCase updateUserRating(String caseId, Integer userRating) {
        return mongo().findAndModify(
                new Query(Criteria.where("_id").is(new ObjectId(caseId))),
                new Update()
                        .set("rating", userRating)
                        .set("userRating", userRating)
                        .set("updateTime", System.currentTimeMillis()),
                new FindAndModifyOptions().returnNew(true).upsert(false),
                WOCase.class);
    }

    default WOCase updateRating(String caseId, Integer rating) {
        return mongo().findAndModify(
                new Query(Criteria.where("_id").is(new ObjectId(caseId))),
                new Update()
                        .set("rating", rating)
                        .set("updateTime", System.currentTimeMillis()),
                new FindAndModifyOptions().returnNew(true).upsert(false),
                WOCase.class);
    }


    default WOCase updateRemark(String caseId, String remark) {
        return mongo().findAndModify(
                new Query(Criteria.where("_id").is(new ObjectId(caseId))),
                new Update()
                        .set("remark", remark)
                        .set("updateTime", System.currentTimeMillis()),
                new FindAndModifyOptions().returnNew(true).upsert(false),
                WOCase.class);
    }

}
