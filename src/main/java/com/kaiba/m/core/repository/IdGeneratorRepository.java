package com.kaiba.m.core.repository;

import com.kaiba.lib.base.response.KbCode;
import com.kaiba.lib.base.response.KbException;
import com.kaiba.m.core.domain.IdGenerator;
import com.kaiba.m.core.middleware.mongo.interfaces.KbMongoRepository;
import org.springframework.data.mongodb.core.FindAndModifyOptions;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Repository;

import java.util.Optional;

/**
 * author: lyux
 * date: 2024-06-06
 *
 * 递增 id 生成器
 */
@Repository
public interface IdGeneratorRepository extends KbMongoRepository<IdGenerator, String> {

    Optional<IdGenerator> findFirstByKey(String key);

    default long getSeq(String key) {
        return findFirstByKey(key).map(IdGenerator::getSeq).orElse(0L);
    }

    default long generateId(String key) {
        return generateId(key, null);
    }

    default long generateId(String key, String name) {
        IdGenerator result = incrSeq(key, name);
        if (result == null) {
            throw new KbException(KbCode.ILLEGAL_STATE, "insert id generator fail");
        } else {
            return result.getSeq();
        }
    }

    default IdGenerator incrSeq(String key) {
        return incrSeq(key, null);
    }

    default IdGenerator incrSeq(String key, String name) {
        Update update = new Update().setOnInsert("key", key).inc("seq", 1);
        if (name != null) {
            update.setOnInsert("name", name);
        }
        return mongo().findAndModify(
                new Query(Criteria.where("key").is(key)), update,
                new FindAndModifyOptions().returnNew(true).upsert(true),
                IdGenerator.class);
    }

}
