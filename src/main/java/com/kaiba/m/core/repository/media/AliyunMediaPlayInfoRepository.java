package com.kaiba.m.core.repository.media;

import com.kaiba.m.core.domain.media.AliyunMediaPlayInfo;
import com.kaiba.m.core.middleware.mongo.interfaces.KbMongoRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @version AliyunMediaPlayInfoRepository, v0.1 2024/1/10 14:25 daopei Exp $
 **/
@Repository
public interface AliyunMediaPlayInfoRepository extends KbMongoRepository<AliyunMediaPlayInfo, String> {

    List<AliyunMediaPlayInfo> findAllByRefId(String refId);

    void deleteAllByRefId(String refId);
}
