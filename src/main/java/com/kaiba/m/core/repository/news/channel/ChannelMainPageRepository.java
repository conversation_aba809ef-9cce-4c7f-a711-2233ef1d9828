package com.kaiba.m.core.repository.news.channel;

import com.kaiba.lib.base.domain.common.KbTimeRange;
import com.kaiba.m.core.domain.news.channel.ChannelMainPage;
import com.kaiba.m.core.middleware.mongo.interfaces.KbMongoRepository;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * author: lyux
 * date: 2022-09-07
 */
@Repository
public interface ChannelMainPageRepository extends KbMongoRepository<ChannelMainPage, String> {

    Optional<ChannelMainPage> findFirstByChannelKey(String channelKey);

    Optional<ChannelMainPage> findFirstByLayoutId(String layoutId);

    default Page<ChannelMainPage> getPageByQuery(KbTimeRange updateTimeRange, Pageable pageable) {
        Criteria criteria = new Criteria();
        if (updateTimeRange != null) {
            criteria = timeRangeCriteria(updateTimeRange, "updateTime", criteria);
        }
        Query query = new Query(criteria);
        long total = mongo().count(query, ChannelMainPage.class);
        List<ChannelMainPage> list = mongo().find(query.with(pageable), ChannelMainPage.class);
        return new PageImpl<>(list, pageable, total);
    }

    default Criteria timeRangeCriteria(KbTimeRange timeRange, String field, Criteria criteria) {
        if (timeRange.isUnbounded()) {
            return criteria;
        }
        Criteria c = criteria.and(field);
        if (timeRange.getLower() != null) {
            if (timeRange.isLowerOpen()) {
                c.gt(timeRange.obtainLowerInMillis());
            } else {
                c.gte(timeRange.obtainLowerInMillis());
            }
        }
        if (timeRange.getUpper() != null) {
            if (timeRange.isUpperOpen()) {
                c.lt(timeRange.obtainUpperInMillis());
            } else {
                c.lte(timeRange.obtainUpperInMillis());
            }
        }
        return c;
    }


}
