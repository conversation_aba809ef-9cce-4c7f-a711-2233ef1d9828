package com.kaiba.m.core.repository.issue;

import com.kaiba.m.core.domain.issue.IssueExpert;
import com.kaiba.m.core.middleware.mongo.interfaces.KbMongoRepository;
import com.kaiba.m.core.middleware.mongo.utils.KbMongoBeanUpdate;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.core.FindAndModifyOptions;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * author: lyux
 * date: 18-9-11
 */
@Repository
public interface IssueExpertRepository extends KbMongoRepository<IssueExpert, Integer> {

    Page<IssueExpert> findAllByOrderByAnswerCountDesc(Pageable pageable);

    Page<IssueExpert> findBySpecialBrandOrderByAnswerCountDesc(String specialCarCode, Pageable pageable);

    List<IssueExpert> findByIdIn(Integer[] expertIdList);

    List<IssueExpert> findByUserName(String name);

    List<IssueExpert> findByRecommend(Boolean recommended);

    default Page<IssueExpert> getList(Integer source, Integer level, Pageable pageable) {
        Criteria criteria = new Criteria();
        if (null != source) {
            criteria.and("source").is(source);
        }
        if (null != level) {
            criteria.and("level").is(level);
        }

        Query query = new Query(criteria);
        long totalPage = mongo().count(query, IssueExpert.class);
        List<IssueExpert> list = mongo().find(query.with(pageable), IssueExpert.class);
        return new PageImpl<>(list, pageable, totalPage);
    }

    default void updateLogin(Integer expertId) {
        mongo().findAndModify(
                new Query(Criteria.where("_id").is(expertId)),
                new Update().set("loginTime", System.currentTimeMillis() / 1000),
                IssueExpert.class);
    }

    default void updateRecommend(Integer expertId, Boolean recommend) {
        mongo().findAndModify(
                new Query(Criteria.where("_id").is(expertId)),
                new Update().set("recommend", recommend),
                IssueExpert.class);
    }

    default IssueExpert updateBasicInfo(IssueExpert expert) {
        return mongo().findAndModify(
                new Query(Criteria.where("_id").is(expert.getId())),
                new KbMongoBeanUpdate(expert)
                        .setIfNotEmpty("userName")
                        .setIfNotEmpty("avatar")
                        .setIfNotEmpty("mobile")
                        .setIfNotEmpty("source")
                        .setIfNotEmpty("company")
                        .setIfNotEmpty("description")
                        .setIfNotNull("level")
                        .setIfNotNull("rank")
                        .setIfNotEmpty("payLevels")
                        .setIfNotEmpty("phonePayLevels")
                        .setIfNotEmpty("specialBrand")
                        .setIfNotEmpty("certificate")
                        .setIfNotEmpty("specialArea")
                        .setIfNotEmpty("idCard")
                        .setIfNotEmpty("idCardNumber"),
                new FindAndModifyOptions().returnNew(true),
                IssueExpert.class);
    }

    default void updateSummary(Integer expertId,
                               Integer answerCount, Integer praiseCount, Integer followerCount,
                               Double satisfactionRatio, Double speedRatio, Double qualityRatio) {
        answerCount = null == answerCount ? 0 : answerCount;
        praiseCount = null == praiseCount ? 0 : praiseCount;
        followerCount = null == followerCount ? 0 : followerCount;
        satisfactionRatio = null == satisfactionRatio ? 0 : satisfactionRatio;
        speedRatio = null == speedRatio ? 0 : speedRatio;
        qualityRatio = null == qualityRatio ? 0 : qualityRatio;
        mongo().findAndModify(
                new Query(Criteria.where("_id").is(expertId)),
                new Update()
                        .set("answerCount", answerCount)
                        .set("praiseCount", praiseCount)
                        .set("followerCount", followerCount)
                        .set("satisfactionRatio", satisfactionRatio)
                        .set("speedRatio", speedRatio)
                        .set("qualityRatio", qualityRatio),
                IssueExpert.class);
    }

}
