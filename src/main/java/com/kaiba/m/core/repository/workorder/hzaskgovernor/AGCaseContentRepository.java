package com.kaiba.m.core.repository.workorder.hzaskgovernor;

import com.kaiba.m.core.domain.workorder.hzaskgovernor.AGCaseContent;
import com.kaiba.m.core.middleware.mongo.interfaces.KbMongoRepository;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.List;

/**
 * author: lyux
 * date: 2024-01-22
 */
@Repository
public interface AGCaseContentRepository extends KbMongoRepository<AGCaseContent, String> {

    List<AGCaseContent> findByIdIn(Collection<String> ids);

}
