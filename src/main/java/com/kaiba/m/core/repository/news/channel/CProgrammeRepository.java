package com.kaiba.m.core.repository.news.channel;

import com.kaiba.lib.base.constant.news.article.NDisplayState;
import com.kaiba.lib.base.domain.common.KbTimeRange;
import com.kaiba.lib.base.domain.common.ShareModel;
import com.kaiba.lib.base.domain.news.pool.bygroup.DisplayConfig;
import com.kaiba.m.core.domain.SeqBasedIdxFirstOrder;
import com.kaiba.m.core.domain.news.channel.CProgramme;
import com.kaiba.m.core.middleware.mongo.interfaces.KbMongoRepository;
import com.kaiba.m.core.middleware.mongo.utils.KbMongoUpdate;
import org.bson.types.ObjectId;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.core.BulkOperations;
import org.springframework.data.mongodb.core.FindAndModifyOptions;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.data.util.Pair;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;


/**
 * author: lyux
 * date: 2024-08-15
 */
@Repository
public interface CProgrammeRepository extends KbMongoRepository<CProgramme, String> {

    Optional<CProgramme> findFirstByKey(String programmeKey);

    Optional<CProgramme> findFirstByGroup(String groupKey);

    List<CProgramme> findByIdIn(Collection<String> programmeIds);

    Page<CProgramme> findByOrderByIdDesc(Pageable pageable);

    Page<CProgramme> findByChannelKeyOrderBySeqDesc(String channelKey, Pageable pageable);

    List<CProgramme> findCProgrammesByOuterIdNotNull();

    default Page<CProgramme> getPageByQuery(KbTimeRange updateTimeRange, Pageable pageable) {
        Criteria criteria = new Criteria();
        if (updateTimeRange != null) {
            criteria = timeRangeCriteria(updateTimeRange, "updateTime", criteria);
        }
        Query query = new Query(criteria);
        long total = mongo().count(query, CProgramme.class);
        List<CProgramme> list = mongo().find(query.with(pageable), CProgramme.class);
        return new PageImpl<>(list, pageable, total);
    }

    default CProgramme updateShare(String id, ShareModel model) {
        return mongo().findAndModify(
                new Query(Criteria.where("_id").is(id)),
                new Update()
                        .set("share", model)
                        .set("updateTime", System.currentTimeMillis()),
                FindAndModifyOptions.options().upsert(false).returnNew(true),
                CProgramme.class);
    }

    default CProgramme updateLayoutId(String id, String layoutId) {
        return mongo().findAndModify(
                new Query(Criteria.where("_id").is(id)),
                new Update()
                        .set("layoutId", layoutId)
                        .set("updateTime", System.currentTimeMillis()),
                FindAndModifyOptions.options().upsert(false).returnNew(true),
                CProgramme.class);
    }

    default CProgramme updateGroupAbsentDc(String id, DisplayConfig dc) {
        Update update = new Update().set("updateTime", System.currentTimeMillis());
        if (dc == null) {
            update.unset("groupAbsentDc");
        } else {
            update.set("groupAbsentDc", dc);
        }
        return mongo().findAndModify(
                new Query(Criteria.where("_id").is(id)), update,
                FindAndModifyOptions.options().upsert(false).returnNew(true),
                CProgramme.class);
    }

    default CProgramme updateState(String id, NDisplayState state) {
        return mongo().findAndModify(
                new Query(Criteria.where("_id").is(id)),
                new Update()
                        .set("state", state.name())
                        .set("updateTime", System.currentTimeMillis()),
                FindAndModifyOptions.options().upsert(false).returnNew(true),
                CProgramme.class);
    }

    default CProgramme updateKey(String id, String key) {
        return mongo().findAndModify(
                new Query(Criteria.where("_id").is(id)),
                new Update()
                        .set("key", key)
                        .set("updateTime", System.currentTimeMillis()),
                FindAndModifyOptions.options().upsert(false).returnNew(true),
                CProgramme.class);
    }

    default CProgramme updateGroup(String id, String group) {
        return mongo().findAndModify(
                new Query(Criteria.where("_id").is(id)),
                new Update()
                        .set("group", group)
                        .set("updateTime", System.currentTimeMillis()),
                FindAndModifyOptions.options().upsert(false).returnNew(true),
                CProgramme.class);
    }

    default CProgramme updateKeyAndGroup(String id, String key, String group) {
        return mongo().findAndModify(
                new Query(Criteria.where("_id").is(id)),
                new Update()
                        .set("key", key)
                        .set("group", group)
                        .set("updateTime", System.currentTimeMillis()),
                FindAndModifyOptions.options().upsert(false).returnNew(true),
                CProgramme.class);
    }

    default CProgramme updateChannel(String id, String channelKey) {
        return mongo().findAndModify(
                new Query(Criteria.where("_id").is(id)),
                new Update()
                        .set("channelKey", channelKey)
                        .set("updateTime", System.currentTimeMillis()),
                FindAndModifyOptions.options().upsert(false).returnNew(true),
                CProgramme.class);
    }

    default CProgramme updateIdx(String id, long seq, Long idx) {
        return mongo().findAndModify(
                new Query(Criteria.where("_id").is(id)),
                new KbMongoUpdate()
                        .setUnsetOnNull("idx", idx)
                        .set("seq", seq),
                new FindAndModifyOptions().upsert(false).returnNew(true),
                CProgramme.class);
    }

    default CProgramme updateOuterId(String id, Integer outerId) {
        return mongo().findAndModify(
            new Query(Criteria.where("_id").is(id)),
            new Update()
                .set("outerId", outerId)
                .set("updateTime", System.currentTimeMillis()),
            FindAndModifyOptions.options().upsert(false).returnNew(true),
            CProgramme.class);
    }

    default void multiUpdateIdx(List<SeqBasedIdxFirstOrder> list) {
        BulkOperations ops = mongo().bulkOps(BulkOperations.BulkMode.ORDERED, CProgramme.class);
        List<Pair<Query, Update>> updates = list.stream().
                map(m -> Pair.of(
                        new Query(Criteria.where("_id").is(new ObjectId(m.getId()))),
                        new Update().set("seq", m.getSeq()).set("idx", m.getIdx())))
                .collect(Collectors.toList());
        ops.updateMulti(updates);
        ops.execute();
    }

    default Criteria timeRangeCriteria(KbTimeRange timeRange, String field, Criteria criteria) {
        if (timeRange.isUnbounded()) {
            return criteria;
        }
        Criteria c = criteria.and(field);
        if (timeRange.getLower() != null) {
            if (timeRange.isLowerOpen()) {
                c.gt(timeRange.obtainLowerInMillis());
            } else {
                c.gte(timeRange.obtainLowerInMillis());
            }
        }
        if (timeRange.getUpper() != null) {
            if (timeRange.isUpperOpen()) {
                c.lt(timeRange.obtainUpperInMillis());
            } else {
                c.lte(timeRange.obtainUpperInMillis());
            }
        }
        return c;
    }


}
