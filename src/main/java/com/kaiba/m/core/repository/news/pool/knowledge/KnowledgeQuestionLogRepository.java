package com.kaiba.m.core.repository.news.pool.knowledge;

import com.kaiba.lib.base.domain.news.pool.knowledge.KnowledgeLogQueryModel;
import com.kaiba.m.core.domain.knowledge.KnowledgeQuestionLog;
import com.kaiba.m.core.middleware.mongo.interfaces.KbMongoRepository;
import java.util.List;
import java.util.Objects;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Repository;

/**
 * Description: 问答记录Repository
 * Author: ZM227
 * Date: 2025/1/2 10:30
 */
@Repository
public interface KnowledgeQuestionLogRepository extends
    KbMongoRepository<KnowledgeQuestionLog, String> {

    default Page<KnowledgeQuestionLog> findAllByCondition(KnowledgeLogQueryModel queryModel) {
        Criteria criteria = new Criteria();
        if (Objects.nonNull(queryModel.getAnswerType())) {
            criteria.and("answerType").is(queryModel.getAnswerType());
        }
        if (StringUtils.isNotBlank(queryModel.getKnowledgeId())) {
            criteria.and("knowledgeId").is(queryModel.getKnowledgeId());
        }
        if (Objects.nonNull(queryModel.getQuestionTimeBegin())) {
            criteria.and("questionTime").gte(queryModel.getQuestionTimeBegin());
        }
        if (Objects.nonNull(queryModel.getQuestionTimeEnd())) {
            criteria.and("questionTime").lte(queryModel.getQuestionTimeEnd());
        }
        Query query = new Query(criteria);
        query.with(Sort.by(Sort.Order.desc("createTime")));
        long total = mongo().count(query, KnowledgeQuestionLog.class);
        List<KnowledgeQuestionLog> list = mongo().find(query.with(queryModel.getPageable()),
            KnowledgeQuestionLog.class);
        return new PageImpl<>(list, queryModel.getPageable(), total);
    }

}
