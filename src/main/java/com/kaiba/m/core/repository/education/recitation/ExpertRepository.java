package com.kaiba.m.core.repository.education.recitation;

import com.kaiba.m.core.model.education.recitation.dto.ExpertQueryDTO;
import com.kaiba.m.core.domain.education.recitation.Expert;
import com.kaiba.m.core.domain.education.recitation.enums.CommonStatusEnum;
import com.kaiba.m.core.domain.education.recitation.enums.ExpertTypeEnum;
import com.kaiba.m.core.middleware.mongo.interfaces.KbMongoRepository;
import java.util.List;
import java.util.Objects;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.FindAndModifyOptions;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Repository;

/**
 * Description: 朗诵团专家repository
 * Author: ZM227
 * Date: 2024/8/5 15:00
 */
@Repository
public interface ExpertRepository extends KbMongoRepository<Expert, String> {

    default Expert updateByExpertCode(Expert expert) {
        Update update = new Update();
        if (StringUtils.isNotBlank(expert.getName())) {
            update.set("name", expert.getName());
        }
        if (Objects.nonNull(expert.getAge())) {
            update.set("age", expert.getAge());
        }
        if (Objects.nonNull(expert.getConnectPhone())) {
            update.set("connectPhone", expert.getConnectPhone());
        }
        if (Objects.nonNull(expert.getExpertType())) {
            update.set("expertType", expert.getExpertType());
        }
        if (Objects.nonNull(expert.getStatus())) {
            update.set("status", expert.getStatus());
        }
        if (StringUtils.isNotBlank(expert.getPhoto())) {
            update.set("photo", expert.getPhoto());
        }
        if (StringUtils.isNotBlank(expert.getGroupKey())) {
            update.set("groupKey", expert.getGroupKey());
        }
        if (StringUtils.isNotBlank(expert.getHobbies())) {
            update.set("hobbies", expert.getHobbies());
        }
        if (StringUtils.isNotBlank(expert.getSignature())) {
            update.set("signature", expert.getSignature());
        }
        if (CollectionUtils.isNotEmpty(expert.getPictures())) {
            update.set("pictures", expert.getPictures());
        }
        if (Objects.nonNull(expert.getPinned())) {
            update.set("pinned", expert.getPinned());
            if (expert.getPinned()) {
                update.set("sortValue", expert.getSortValue());
            } else {
                update.set("sortValue", Integer.MAX_VALUE);
            }
        }
        if (StringUtils.isNotBlank(expert.getAttribute().getContent())) {
            update.set("attribute", expert.getAttribute());
        }
        if (StringUtils.isNotBlank(expert.getExtendInfo().getContent())) {
            update.set("extendInfo", expert.getExtendInfo());
        }
        update.set("updateTime", System.currentTimeMillis());
        return mongo().findAndModify(
            new Query(Criteria.where("expertCode").is(expert.getExpertCode())), update,
            new FindAndModifyOptions().upsert(false).returnNew(true), Expert.class);
    }

    default Page<Expert> findAllByCondition(ExpertQueryDTO queryDTO) {
        Criteria criteria = new Criteria();
        if (StringUtils.isNotBlank(queryDTO.getExpertCode())) {
            criteria.and("expertCode").is(queryDTO.getExpertCode());
        }
        if (StringUtils.isNotBlank(queryDTO.getName())) {
            criteria.and("name").regex(queryDTO.getName());
        }
        if (Objects.nonNull(queryDTO.getExpertType())) {
            criteria.and("expertType").is(queryDTO.getExpertType());
        }
        criteria.and("status").is(CommonStatusEnum.VALID);
        Query query = new Query(criteria);
        if (Objects.equals(queryDTO.getExpertType(), ExpertTypeEnum.SENIOR)) {
            query.with(Sort.by(Sort.Order.asc("sortValue"), Sort.Order.desc("createTime")));
        } else {
            query.with(Sort.by(Sort.Order.desc("createTime")));
        }
        long total = mongo().count(query, Expert.class);
        List<Expert> list = mongo().find(query.with(queryDTO.getPageable()), Expert.class);
        return new PageImpl<>(list, queryDTO.getPageable(), total);
    }

    Expert findExpertByExpertCode(String expertCode);
}
