package com.kaiba.m.core.repository.wx;

import com.kaiba.m.core.domain.wx.WxOpenAppMessage;
import com.kaiba.m.core.middleware.mongo.interfaces.KbMongoRepository;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.core.FindAndModifyOptions;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @version WxGzhMessageRepository, v0.1 2024/4/17 15:02 daopei Exp $
 **/
@Repository
public interface WxOpenAppMessageRepository extends KbMongoRepository<WxOpenAppMessage, String> {


    default Page<WxOpenAppMessage> findByParam(Integer siteId, String wxMsgType, Long createTimeSt, Long createTimeEt, Integer state, Pageable pageable) {
        Criteria criteria = new Criteria();
        if (siteId != null) {
            criteria.and("siteId").is(siteId);
        }
        if (wxMsgType != null) {
            criteria.and("wxMsgType").is(wxMsgType);
        }
        if (state != null) {
            criteria.and("state").is(state);
        }
        if (createTimeSt != null || createTimeEt != null) {
            criteria = criteria.and("createTime");
            if (createTimeSt != null) {
                criteria.gte(createTimeSt);
            }
            if (createTimeEt != null) {
                criteria.lte(createTimeEt);
            }
        }
        Query query = new Query(criteria);
        long count = mongo().count(query, WxOpenAppMessage.class);
        List<WxOpenAppMessage> list = mongo().find(query.with(pageable), WxOpenAppMessage.class);
        return new PageImpl<>(list, pageable, count);
    }


    default WxOpenAppMessage updateNoteId(String id, String noteId, String noteReviewId) {
        return mongo().findAndModify(
                new Query(Criteria.where("id").is(id)
                        .and("noteId").exists(false)),
                new Update()
                        .set("noteId", noteId)
                        .set("noteReviewId", noteReviewId),
                FindAndModifyOptions.options().upsert(false).returnNew(true),
                WxOpenAppMessage.class);
    }

    default void updateStateByOpenId(String openId, Integer state) {
        mongo().updateMulti(
                new Query(Criteria.where("wxFromOpenId").is(openId)
                        .and("state").is(1)),
                new Update()
                        .set("updateTime", System.currentTimeMillis())
                        .set("state", state),
                WxOpenAppMessage.class);
    }
}
