package com.kaiba.m.core.repository.appcomponent.banner;

import com.kaiba.lib.base.constant.appwidget.WidgetItemState;
import com.kaiba.m.core.domain.appwidget.banner.BannerImage;
import com.kaiba.m.core.middleware.mongo.interfaces.KbMongoRepository;
import com.kaiba.m.core.middleware.mongo.utils.KbMongoBeanUpdate;
import com.kaiba.m.core.middleware.mongo.utils.KbMongoUpdate;
import org.bson.types.ObjectId;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.BulkOperations;
import org.springframework.data.mongodb.core.FindAndModifyOptions;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.data.util.Pair;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * author: lyux
 * date: 18-9-7
 */
@Repository
public interface AppBannerImageRepository extends KbMongoRepository<BannerImage, String> {

    List<BannerImage> getByInstanceIdAndStateOrderByIdxAsc(String instanceId, Integer state, Pageable pageable);

    List<BannerImage> getByInstanceIdAndTypeOrderByIdxAsc(String instanceId, Integer type, Pageable pageable);

    List<BannerImage> getByScheduledEndTimeBetweenOrderByIdAsc(long t1, long t2, Pageable pageable);

    List<BannerImage> getByScheduledStartTimeBetweenOrderByIdAsc(long t1, long t2, Pageable pageable);

    List<BannerImage> getByInstanceIdAndStateInOrderByIdxAsc(String instanceId, List<Integer> states, Pageable pageable);

    boolean existsByInstanceId(String instanceId);

    boolean existsByInstanceIdAndStateNot(String instanceId, Integer state);

    default Page<BannerImage> getPageByInstance(
            String instanceId, Integer type, Integer state, Pageable pageable) {
        Criteria criteria = Criteria.where("instanceId").is(instanceId);
        if (type != null) {
            criteria.and("type").is(type);
        }
        if (state != null) {
            criteria.and("state").is(state);
        }
        Query query = new Query(criteria);
        long count = mongo().count(query, BannerImage.class);
        List<BannerImage> list = mongo().find(
                query.with(pageable).with(Sort.by(Sort.Direction.ASC, "_id")), BannerImage.class);
        return new PageImpl<>(list, pageable, count);
    }

    default Page<BannerImage> searchPageByTitle(
            String instanceId, String keyword, Integer state, Pageable pageable) {
        Criteria criteria = Criteria.where("instanceId").is(instanceId).and("title").regex(keyword);
        if (state != null) {
            criteria.and("state").is(state);
        }
        Query query = new Query(criteria);
        long total = mongo().count(query, BannerImage.class);
        List<BannerImage> list = mongo().find(query.with(pageable), BannerImage.class);
        return new PageImpl<>(list, pageable, total);
    }

    default BannerImage updateData(BannerImage banner) {
        return mongo().findAndModify(
                new Query(Criteria.where("_id").is(new ObjectId(banner.getId()))),
                new KbMongoBeanUpdate(banner)
                        .setIfNotEmpty("url")
                        .setIfNotEmpty("title")
                        .setUnsetOnEmpty("subtitle")
                        .setIfNotEmpty("isPlayable")
                        .setIfNotEmpty("isLive")
                        .setIfNotEmpty("action")
                        .setIfNotNull("autoplay")
                        .setUnsetOnNull("actionParams")
                        .set("updateTime", System.currentTimeMillis()),
                new FindAndModifyOptions().returnNew(true),
                BannerImage.class);
    }

    default BannerImage updateState(String bannerId, WidgetItemState state) {
        long now = System.currentTimeMillis();
        Update update = new Update()
                .set("state", state.getValue())
                .set("updateTime", now);
        switch (state) {
            case SIGNED:
                update.set("signTime", now);
                break;
            case ONLINE:
                update.set("startTime", now).unset("scheduledStartTime");
                break;
            case OFFLINE:
                update.set("endTime", now).unset("scheduledStartTime").unset("scheduledEndTime");
                break;
        }
        return mongo().findAndModify(
                new Query(Criteria.where("_id").is(new ObjectId(bannerId))), update,
                new FindAndModifyOptions().returnNew(true),
                BannerImage.class);
    }

    default BannerImage updateScheduledTime(String bannerId, Long scheduledStartTime, Long scheduledEndTime) {
        return mongo().findAndModify(
                new Query(Criteria.where("_id").is(new ObjectId(bannerId))),
                new KbMongoUpdate()
                        .setUnsetOnNull("scheduledStartTime", scheduledStartTime)
                        .setUnsetOnNull("scheduledEndTime", scheduledEndTime)
                        .set("updateTime", System.currentTimeMillis()),
                new FindAndModifyOptions().returnNew(true),
                BannerImage.class);
    }

    default BannerImage updateAttr(String bannerId, Map<String, String> attr, boolean replace) {
        Update update = new Update().set("updateTime", System.currentTimeMillis());
        if (replace) {
            update.set("attr", attr);
        } else {
            attr.forEach((k, v) -> update.set("attr." + k, v));
        }
        return mongo().findAndModify(
                new Query(Criteria.where("_id").is(new ObjectId(bannerId))), update,
                FindAndModifyOptions.options().upsert(false).returnNew(true),
                BannerImage.class);
    }

    default BannerImage updateAttrKeyValue(String bannerId, String key, String value) {
        return mongo().findAndModify(
                new Query(Criteria.where("_id").is(new ObjectId(bannerId))),
                new KbMongoUpdate()
                        .setUnsetOnNull("attr." + key, value)
                        .set("updateTime", System.currentTimeMillis()),
                FindAndModifyOptions.options().upsert(false).returnNew(true),
                BannerImage.class);
    }

    default void updateIdxBatch(List<BannerImage> bannerImageList) {
        BulkOperations ops = mongo().bulkOps(BulkOperations.BulkMode.ORDERED, BannerImage.class);
        List<Pair<Query, Update>> updates = bannerImageList.stream().
                map(bannerImage ->
                        Pair.of(new Query(Criteria.where("_id").is(new ObjectId(bannerImage.getId()))),
                                new Update().set("idx", bannerImage.getIdx())))
                .collect(Collectors.toList());
        ops.updateMulti(updates);
        ops.execute();
    }

    default BannerImage updateDataUrl(String id, String url) {
        return mongo().findAndModify(
                Query.query(Criteria.where("_id").is(new ObjectId(id))),
                new Update()
                        .set("url",url)
                        .set("updateTime",System.currentTimeMillis()),
                new FindAndModifyOptions().returnNew(true),
                BannerImage.class
        );
    }
}
