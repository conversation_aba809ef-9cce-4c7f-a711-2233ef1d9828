package com.kaiba.m.core.repository.wx;

import com.kaiba.m.core.domain.wx.WxGzhMessage;
import com.kaiba.m.core.middleware.mongo.interfaces.KbMongoRepository;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 * @version WxGzhMessageRepository, v0.1 2024/4/26 16:54 daopei Exp $
 **/
@Repository
public interface WxGzhMessageRepository extends KbMongoRepository<WxGzhMessage, String> {

    Page<WxGzhMessage> findByIdLessThanOrderByIdDesc(String id, Pageable pageable);
}
