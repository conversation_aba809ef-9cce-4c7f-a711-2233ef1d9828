package com.kaiba.m.core.repository.appversion;

import com.kaiba.lib.base.constant.appversion.AppUpdatePolicy;
import com.kaiba.lib.base.constant.appversion.AppVersionState;
import com.kaiba.m.core.domain.appversion.AppVersionRoute;
import com.kaiba.m.core.middleware.mongo.interfaces.KbMongoRepository;
import org.bson.types.ObjectId;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.List;

@Repository
public interface AppVersionRouteRepository extends KbMongoRepository<AppVersionRoute, String> {

    Page<AppVersionRoute> findByOrderByIdDesc(Pageable pageable);

    Page<AppVersionRoute> findByPackageIdOrderByIdDesc(
            String packageId, Pageable pageable);

    Page<AppVersionRoute> findByPackageIdAndStateInOrderByIdDesc(
            String packageId, Collection<Integer> states, Pageable pageable);

    Page<AppVersionRoute> findByPackageIdAndOriginCodeOrderByIdDesc(
            String packageId, Integer code, Pageable pageable);

    Page<AppVersionRoute> findByPackageIdAndOriginCodeAndStateInOrderByIdDesc(
            String packageId, Integer code, Collection<Integer> states, Pageable pageable);

    List<AppVersionRoute> getByPackageIdAndOriginCodeOrderByIdDesc(String packageId, Integer code, Pageable pageable);

    default AppVersionRoute updateState(String id, AppVersionState state) {
        return mongo().findAndModify(
                new Query(Criteria.where("_id").is(new ObjectId(id))),
                new Update().set("state", state.getValue()),
                AppVersionRoute.class);
    }

    default AppVersionRoute updatePolicy(String id, AppUpdatePolicy policy) {
        return mongo().findAndModify(
                new Query(Criteria.where("_id").is(new ObjectId(id))),
                new Update().set("policy", policy.getValue()),
                AppVersionRoute.class);
    }

}
