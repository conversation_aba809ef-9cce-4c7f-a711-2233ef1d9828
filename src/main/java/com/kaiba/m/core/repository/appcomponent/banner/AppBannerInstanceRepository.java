package com.kaiba.m.core.repository.appcomponent.banner;

import com.kaiba.m.core.domain.appwidget.banner.BannerInstance;
import com.kaiba.m.core.middleware.mongo.interfaces.KbMongoRepository;
import com.kaiba.m.core.middleware.mongo.utils.KbMongoUpdate;
import org.bson.types.ObjectId;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * author: lyux
 * date: 18-9-7
 */
@Repository
public interface AppBannerInstanceRepository extends KbMongoRepository<BannerInstance, String> {

    Optional<BannerInstance> findFirstBySiteIdAndKey(Integer siteId, String key);

    Page<BannerInstance> findBySiteIdOrderByIdDesc(Integer siteId, Pageable pageable);

    Page<BannerInstance> findAllByOrderByIdDesc(Pageable pageable);

    default Page<BannerInstance> getInstancePage(Integer siteId, Integer backendFilterType, Pageable pageable) {
        Criteria criteria = new Criteria();
        if (siteId != null) {
            criteria.and("siteId").is(siteId);
        }
        if (backendFilterType != null) {
            criteria.and("backendFilterType").is(backendFilterType);
        }
        Query query = Query.query(criteria);
        long count = mongo().count(query, BannerInstance.class);
        List<BannerInstance> list = mongo().find(query.with(pageable), BannerInstance.class);
        return new PageImpl<>(list, pageable, count);
    }

    default void updateDesc(
            String instanceId, String name, String title, String description, Integer backendFilterType) {
        mongo().findAndModify(
                new Query(Criteria.where("_id").is(new ObjectId(instanceId))),
                new KbMongoUpdate()
                        .setIfNotEmpty("name", name)
                        .setIfNotEmpty("title", title)
                        .setIfNotEmpty("description", description)
                        .setIfNotEmpty("backendFilterType", backendFilterType)
                        .set("updateTime", System.currentTimeMillis()),
                BannerInstance.class);
    }

    default void updateSize(String instanceId, int width, int height) {
        mongo().findAndModify(
                new Query(Criteria.where("_id").is(new ObjectId(instanceId))),
                new Update()
                        .set("width", width)
                        .set("height", height)
                        .set("updateTime", System.currentTimeMillis()),
                BannerInstance.class);
    }

    default void updateAutoplay(String instanceId, int autoplay) {
        mongo().findAndModify(
                new Query(Criteria.where("_id").is(new ObjectId(instanceId))),
                new Update()
                        .set("autoplay", autoplay)
                        .set("updateTime", System.currentTimeMillis()),
                BannerInstance.class);
    }

}
