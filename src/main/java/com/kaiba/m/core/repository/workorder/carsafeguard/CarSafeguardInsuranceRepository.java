package com.kaiba.m.core.repository.workorder.carsafeguard;

import com.kaiba.m.core.domain.workorder.carsafeguard.CarSafeguardInsuranceCase;
import com.kaiba.m.core.middleware.mongo.interfaces.KbMongoRepository;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @version CarSafeguardInsuranceDetailRepository, v0.1 2024/7/16 16:24 daopei Exp $
 **/
@Repository
public interface CarSafeguardInsuranceRepository extends KbMongoRepository<CarSafeguardInsuranceCase, String> {

    List<CarSafeguardInsuranceCase> findByIdIn(Collection<String> ids);
}
