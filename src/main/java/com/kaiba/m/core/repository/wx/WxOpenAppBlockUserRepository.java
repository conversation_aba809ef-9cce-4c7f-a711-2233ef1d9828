package com.kaiba.m.core.repository.wx;

import com.kaiba.m.core.domain.wx.WxOpenAppBlockUser;
import com.kaiba.m.core.middleware.mongo.interfaces.KbMongoRepository;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.core.FindAndModifyOptions;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @version WxOpenAppBlockUserRepository, v0.1 2024/5/17 17:23 daopei Exp $
 **/
@Repository
public interface WxOpenAppBlockUserRepository extends KbMongoRepository<WxOpenAppBlockUser, String> {

    List<WxOpenAppBlockUser> findByOpenIdAndState(String openId, Integer state);

    List<WxOpenAppBlockUser> findByOpenIdInAndState(List<String> openIdList, Integer state);

    default Page<WxOpenAppBlockUser> getByParam(
            String openId,
            Integer state,
            Integer siteId,
            Pageable pageable
    ) {
        Criteria criteria = new Criteria();
        if (openId != null) {
            criteria.and("openId").is(openId);
        }
        if (state != null) {
            criteria.and("state").is(state);
        }
        if (siteId != null) {
            criteria.and("siteId").is(siteId);
        }
        Query query = new Query(criteria);
        long count = mongo().count(query, WxOpenAppBlockUser.class);
        List<WxOpenAppBlockUser> list = mongo().find(query.with(pageable), WxOpenAppBlockUser.class);
        return new PageImpl<>(list, pageable, count);
    }

    default WxOpenAppBlockUser updateBlockState(String id, Integer state) {
        return mongo().findAndModify(
                new Query(Criteria.where("_id").is(id)),
                new Update().set("state", state),
                FindAndModifyOptions.options().upsert(false).returnNew(true),
                WxOpenAppBlockUser.class
        );
    }

    default void cleanBlockState(Long lastEndTime) {
        mongo().updateMulti(
                new Query(Criteria.where("state").is(1)
                        .and("endTime").lte(lastEndTime)),
                new Update().set("state", 0),
                WxOpenAppBlockUser.class
        );
    }

}
