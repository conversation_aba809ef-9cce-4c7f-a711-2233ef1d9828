package com.kaiba.m.core.repository.route;

import com.kaiba.lib.base.constant.route.KbRouteState;
import com.kaiba.lib.base.domain.route.RouteQueryModel;
import com.kaiba.m.core.domain.route.KbRoute;
import com.kaiba.m.core.middleware.mongo.interfaces.KbMongoRepository;
import com.kaiba.m.core.middleware.mongo.utils.KbMongoBeanCriteria;
import org.bson.types.ObjectId;
import org.springframework.data.domain.*;
import org.springframework.data.mongodb.core.FindAndModifyOptions;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * author: lyux
 * date: 2021-03-24
 */
@Repository
public interface RouteRepository extends KbMongoRepository<KbRoute, String> {

    List<KbRoute> findByIdIn(String[] ids);

    Page<KbRoute> findAllBy(Pageable pageable);

    Page<KbRoute> findByPaths(String path, Pageable pageable);

    Page<KbRoute> findByPathsRegex(String path, Pageable pageable);

    Page<KbRoute> findByStateIn(Integer[] states, Pageable pageable);

    boolean existsByDispatchRuleId(String dispatchRuleId);

    boolean existsByMatchRuleIds(String matchRuleId);

    boolean existsByAuthRuleIds(String authRuleId);

    default Page<KbRoute> getListWithQueryModel(RouteQueryModel model) {
        if (null == model) {
            return Page.empty();
        }
        Query query = Query.query(new KbMongoBeanCriteria(model)
                .criteriaIfNotEmpty("path")
                .criteriaIfNotEmpty("matchRuleId")
                .criteriaIfNotEmpty("filterRuleId")
                .criteriaIfNotEmpty("authRuleId")
                .criteriaIfNotEmpty("duplicateRuleId")
                .criteriaInIfNotEmpty("permission")
                .criteriaInIfNotEmpty("downstreamType")
                .criteriaInIfNotEmpty("state"));
        int p = model.getPage() == null ? 0 : model.getPage() - 1;
        int ps = model.getPageSize() == null ? 20 : model.getPageSize();
        boolean orderAsc = model.getOrderAsc() == null || model.getOrderAsc();
        String orderByField = model.getOrderBy() == null ? "_id" : model.getOrderBy();
        Pageable pageable = PageRequest.of(
                p, ps, Sort.by(orderAsc ? Sort.Direction.ASC : Sort.Direction.DESC, orderByField));
        long total = mongo().count(query, KbRoute.class);
        List<KbRoute> list = mongo().find(query.with(pageable), KbRoute.class);
        return new PageImpl<>(list, pageable, total);
    }

    default void updateState(String id, KbRouteState state) {
        mongo().findAndModify(
                new Query(Criteria.where("_id").is(new ObjectId(id))),
                new Update()
                        .set("state", state.getValue())
                        .set("updateTime", System.currentTimeMillis()),
                FindAndModifyOptions.options().upsert(false).returnNew(true),
                KbRoute.class);
    }

}
