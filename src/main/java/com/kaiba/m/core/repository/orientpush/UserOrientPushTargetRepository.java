package com.kaiba.m.core.repository.orientpush;

import com.kaiba.m.core.domain.orientpush.UserOrientTarget;
import com.kaiba.m.core.middleware.mongo.interfaces.KbMongoRepository;
import com.kaiba.m.core.middleware.mongo.utils.KbMongoUpdate;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.core.FindAndModifyOptions;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @version UserOrientPushTargetRepository, v0.1 2025/5/15 09:50 daopei Exp $
 **/
@Repository
public interface UserOrientPushTargetRepository extends KbMongoRepository<UserOrientTarget, String> {

    Page<UserOrientTarget> findByTagsIn(List<String> tags, Pageable pageable);


    default UserOrientTarget upsert(Integer userId, List<String> tags) {
        return mongo().findAndModify(
                new Query(Criteria.where("userId").is(userId)),
                new KbMongoUpdate()
                        .setOnInsert("userId", userId)
                        .setOnInsert("createTime", System.currentTimeMillis())
                        .set("tags", tags)
                        .set("updateTime", System.currentTimeMillis()),
                FindAndModifyOptions.options().upsert(true).returnNew(true),
                UserOrientTarget.class
        );
    }
}
