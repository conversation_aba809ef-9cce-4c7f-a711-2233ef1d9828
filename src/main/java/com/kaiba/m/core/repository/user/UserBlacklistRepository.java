package com.kaiba.m.core.repository.user;

import com.kaiba.m.core.domain.user.UserBlacklist;
import com.kaiba.m.core.middleware.mongo.interfaces.KbMongoRepository;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.core.FindAndModifyOptions;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 用户黑名单
 *
 * <AUTHOR>
 * @date 2022/03/07 17:25
 **/
@Repository
public interface UserBlacklistRepository extends KbMongoRepository<UserBlacklist, String> {

    List<UserBlacklist> getAllBy(Pageable pageable);

    List<UserBlacklist> getByUid(Integer userId, Pageable pageable);

    Page<UserBlacklist> findByUid(Integer userId, Pageable pageable);

    long countAllBy();

    long countByUid(Integer userId);

    long countByBid(Integer userId);

    boolean existsByUid(Integer userId);

    boolean existsByUidAndBid(Integer userId, Integer targetUserId);

    void deleteByUidAndBid(Integer userId, Integer targetUserId);

    /**
     * 添加用户id到黑名单中
     * @param userId 操作的用户
     * @param targetUserId 被添加的用户
     * @return 数据
     */
    default UserBlacklist addToBlacklist(Integer userId, Integer targetUserId) {
        return mongo().findAndModify(
                new Query(Criteria.where("uid").is(userId).and("bid").is(targetUserId)),
                new Update()
                        .set("uid", userId)
                        .set("bid", targetUserId)
                        .set("updateTime", System.currentTimeMillis() / 1000),
                new FindAndModifyOptions().returnNew(true).upsert(true),
                UserBlacklist.class);
    }

}
