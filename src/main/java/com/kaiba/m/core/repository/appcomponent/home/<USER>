package com.kaiba.m.core.repository.appcomponent.home;

import com.kaiba.m.core.domain.apphome.AppFrameMatcher;
import com.kaiba.m.core.middleware.mongo.interfaces.KbMongoRepository;
import com.kaiba.m.core.middleware.mongo.utils.KbMongoUpdate;
import org.bson.types.ObjectId;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.core.FindAndModifyOptions;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Set;

/**
 * author: lyux
 * date: 2023-10-09
 */
@Repository
public interface AppFrameMatcherRepository extends KbMongoRepository<AppFrameMatcher, String> {

    Page<AppFrameMatcher> findBySiteIdOrderByIdDesc(Integer siteId, Pageable pageable);

    Page<AppFrameMatcher> findBySiteIdAndStateOrderByIdDesc(Integer siteId, Integer state, Pageable pageable);

    List<AppFrameMatcher> getByOrderByIdDesc(Pageable pageable);

    default void updateRemark(String matcherId, String remark) {
        mongo().findAndModify(
                Query.query(Criteria.where("_id").is(new ObjectId(matcherId))),
                new Update()
                        .set("remark", remark)
                        .set("updateTime", System.currentTimeMillis()),
                new FindAndModifyOptions().returnNew(false),
                AppFrameMatcher.class);
    }

    default void updateState(String matcherId, Integer state) {
        mongo().findAndModify(
                Query.query(Criteria.where("_id").is(new ObjectId(matcherId))),
                new Update()
                        .set("state", state)
                        .set("updateTime", System.currentTimeMillis()),
                new FindAndModifyOptions().returnNew(false),
                AppFrameMatcher.class);
    }

    default void updateVersionRange(String matcherId, Integer vcMin, Integer vcMax) {
        mongo().findAndModify(
                Query.query(Criteria.where("_id").is(new ObjectId(matcherId))),
                new Update()
                        .set("vcMin", vcMin)
                        .set("vcMax", vcMax)
                        .set("updateTime", System.currentTimeMillis()),
                new FindAndModifyOptions().returnNew(false),
                AppFrameMatcher.class);
    }

    default void updateUsers(String matcherId, Set<Integer> userIds) {
        mongo().findAndModify(
                Query.query(Criteria.where("_id").is(new ObjectId(matcherId))),
                new Update()
                        .set("userIds", userIds)
                        .set("updateTime", System.currentTimeMillis()),
                new FindAndModifyOptions().returnNew(false),
                AppFrameMatcher.class);
    }

    default void addUser(String matcherId, Integer userId) {
        mongo().findAndModify(
                Query.query(Criteria.where("_id").is(new ObjectId(matcherId))),
                new Update()
                        .push("userIds", userId)
                        .set("updateTime", System.currentTimeMillis()),
                new FindAndModifyOptions().returnNew(false),
                AppFrameMatcher.class);
    }

    default void removeUser(String matcherId, Integer userId) {
        mongo().findAndModify(
                Query.query(Criteria.where("_id").is(new ObjectId(matcherId))),
                new Update()
                        .pull("userIds", userId)
                        .set("updateTime", System.currentTimeMillis()),
                new FindAndModifyOptions().returnNew(false),
                AppFrameMatcher.class);
    }

    default void updateRosterKey(String matcherId, String rosterKey) {
        mongo().findAndModify(
                Query.query(Criteria.where("_id").is(new ObjectId(matcherId))),
                new KbMongoUpdate()
                        .setUnsetOnNull("rosterKey", rosterKey)
                        .set("updateTime", System.currentTimeMillis()),
                new FindAndModifyOptions().returnNew(false),
                AppFrameMatcher.class);
    }

    default void updateMainPageId(String matcherId, String mainPageId) {
        mongo().findAndModify(
                Query.query(Criteria.where("_id").is(new ObjectId(matcherId))),
                new Update()
                        .set("mainPageId", mainPageId)
                        .set("updateTime", System.currentTimeMillis()),
                new FindAndModifyOptions().returnNew(false),
                AppFrameMatcher.class);
    }

    default void updateHomeFrameId(String matcherId, String homeFrameId) {
        mongo().findAndModify(
                Query.query(Criteria.where("_id").is(new ObjectId(matcherId))),
                new Update()
                        .set("homeFrameId", homeFrameId)
                        .set("updateTime", System.currentTimeMillis()),
                new FindAndModifyOptions().returnNew(false),
                AppFrameMatcher.class);
    }

}
