package com.kaiba.m.core.repository.appcomponent.home;

import com.kaiba.lib.base.constant.applego.AppComponentState;
import com.kaiba.m.core.domain.apphome.AppServiceFrame;
import com.kaiba.m.core.middleware.mongo.interfaces.KbMongoRepository;
import com.kaiba.m.core.middleware.mongo.utils.KbMongoUpdate;
import org.apache.commons.collections4.CollectionUtils;
import org.bson.types.ObjectId;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.core.FindAndModifyOptions;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @version AppServiceFrameRepository, v0.1 2023/8/2 10:16 daopei Exp $
 **/
@Repository
public interface AppServiceFrameRepository extends KbMongoRepository<AppServiceFrame, String> {

    Optional<AppServiceFrame> findFirstBySiteIdAndStateOrderByOnlineTimeDesc(Integer siteId, Integer state);

    Optional<AppServiceFrame> findFirstBySiteIdAndStateOrderByCreateTimeDesc(Integer siteId, Integer state);

    default Page<AppServiceFrame> findPageBySiteIdAndStateIn(Integer siteId, List<Integer> states , Pageable pageable) {
        Query query = new Query();
        if (siteId != null) {
            query.addCriteria(Criteria.where("siteId").is(siteId));
        }
        if (CollectionUtils.isNotEmpty(states)) {
            query.addCriteria(Criteria.where("state").in(states));
        }
        long totalCount = mongo().count(query, AppServiceFrame.class);
        List<AppServiceFrame> list = mongo().find(query.with(pageable), AppServiceFrame.class);
        return new PageImpl<>(list, pageable, totalCount);
    }

    default AppServiceFrame updateStateById(String id,Integer state) {
        return mongo().findAndModify(
                Query.query(Criteria.where("_id").is(new ObjectId(id))),
                new KbMongoUpdate()
                        .setWhen(AppComponentState.ONLINE.getValue() == state.intValue(),"onlineTime",System.currentTimeMillis())
                        .set("state",state)
                        .set("updateTime",System.currentTimeMillis()),
                new FindAndModifyOptions().returnNew(true),
                AppServiceFrame.class
        );
    }

    default AppServiceFrame update(AppServiceFrame instance) {
        return mongo().findAndModify(
                Query.query(Criteria.where("_id").is(new ObjectId(instance.getId()))),
                new KbMongoUpdate()
                        .set("title",instance.getTitle())
                        .set("headBkgImage",instance.getHeadBkgImage())
                        .set("bannerInstanceKey",instance.getBannerInstanceKey())
                        .set("updateTime",System.currentTimeMillis()),
                new FindAndModifyOptions().returnNew(true),
                AppServiceFrame.class
        );
    }

    default void refreshOnlineTime(String id) {
        mongo().updateFirst(
                Query.query(Criteria.where("_id").is(new ObjectId(id))),
                new KbMongoUpdate()
                        .set("onlineTime",System.currentTimeMillis())
                        .set("updateTime",System.currentTimeMillis()),
                AppServiceFrame.class
        );
    }
}
