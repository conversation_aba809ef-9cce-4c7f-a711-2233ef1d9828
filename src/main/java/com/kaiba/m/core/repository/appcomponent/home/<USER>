package com.kaiba.m.core.repository.appcomponent.home;

import com.kaiba.lib.base.constant.applego.AppComponentState;
import com.kaiba.m.core.domain.apphome.AppHomeFrame;
import com.kaiba.m.core.middleware.mongo.interfaces.KbMongoRepository;
import com.kaiba.m.core.middleware.mongo.utils.KbMongoBeanUpdate;
import com.kaiba.m.core.middleware.mongo.utils.KbMongoUpdate;
import org.apache.commons.collections4.CollectionUtils;
import org.bson.types.ObjectId;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.core.FindAndModifyOptions;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @version AppHomeFrameRepository, v0.1 2023/7/28 15:43 daopei Exp $
 **/
@Repository
public interface AppHomeFrameRepository extends KbMongoRepository<AppHomeFrame, String> {

    Optional<AppHomeFrame> findFirstBySiteIdAndStateOrderByCreateTimeDesc(Integer siteId,Integer state);

    Optional<AppHomeFrame> findFirstBySiteIdAndStateOrderByOnlineTimeDesc(Integer siteId,Integer state);

    default Page<AppHomeFrame> findPageBySiteIdAndStateIn(Integer siteId, List<Integer> states , Pageable pageable) {
        Query query = new Query();
        if (siteId != null) {
            query.addCriteria(Criteria.where("siteId").is(siteId));
        }
        if (CollectionUtils.isNotEmpty(states)) {
            query.addCriteria(Criteria.where("state").in(states));
        }
        long totalCount = mongo().count(query, AppHomeFrame.class);
        List<AppHomeFrame> list = mongo().find(query.with(pageable), AppHomeFrame.class);
        return new PageImpl<>(list, pageable, totalCount);
    }

    default AppHomeFrame updateStateById(String id,Integer state) {
        return mongo().findAndModify(
                Query.query(Criteria.where("_id").is(new ObjectId(id))),
                new KbMongoUpdate()
                        .setWhen(AppComponentState.ONLINE.getValue() == state.intValue() ,"onlineTime", System.currentTimeMillis())
                        .set("state",state)
                        .set("updateTime",System.currentTimeMillis()),
                new FindAndModifyOptions().returnNew(true),
                AppHomeFrame.class
        );
    }

    default AppHomeFrame update(AppHomeFrame instance) {
        return mongo().findAndModify(
                Query.query(Criteria.where("_id").is(new ObjectId(instance.getId()))),
                new KbMongoUpdate()
                        .set("siteLogo",instance.getSiteLogo())
                        .set("siteName",instance.getSiteName())
                        .set("headBkgImage",instance.getHeadBkgImage())
                        .set("headBkgColor",instance.getHeadBkgColor())
                        .set("footBkgImage",instance.getFootBkgImage())
                        .set("footBkgColor",instance.getFootBkgColor())
                        .set("searchHint",instance.getSearchHint())
                        .set("tab1",instance.getTab1())
                        .set("tab2",instance.getTab2())
                        .set("tab3",instance.getTab3())
                        .set("tab4",instance.getTab4())
                        .set("tab5",instance.getTab5())
                        .set("tab6",instance.getTab6())
                        .set("updateTime",System.currentTimeMillis()),
                new FindAndModifyOptions().returnNew(true),
                AppHomeFrame.class
        );
    }

    default void refreshOnlineTime(String id) {
        mongo().updateFirst(
                Query.query(Criteria.where("_id").is(new ObjectId(id))),
                new KbMongoUpdate()
                        .set("onlineTime",System.currentTimeMillis())
                        .set("updateTime",System.currentTimeMillis()),
                AppHomeFrame.class
        );
    }

}
