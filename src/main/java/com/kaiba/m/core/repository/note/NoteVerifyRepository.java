package com.kaiba.m.core.repository.note;

import com.kaiba.m.core.domain.note.NoteVerify;
import com.kaiba.m.core.middleware.mongo.interfaces.KbMongoRepository;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Repository;

/**
 * author yanghy
 * date 2019/11/21
 */
@Repository
public interface NoteVerifyRepository extends KbMongoRepository<NoteVerify, String> {

    Page<NoteVerify> findBySiteId(Integer siteId, Pageable pageable);


}
