package com.kaiba.m.core.repository.program.adrush;

import com.kaiba.lib.base.response.KbCode;
import com.kaiba.lib.base.response.KbException;
import com.kaiba.m.core.domain.program.adrush.AdRush;
import com.kaiba.m.core.middleware.mongo.interfaces.KbMongoRepository;
import com.kaiba.m.core.middleware.mongo.utils.KbMongoBeanUpdate;
import org.bson.types.ObjectId;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.core.FindAndModifyOptions;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Repository;
import java.util.List;

/**
 * author yeqq
 * date 2021-02-05
 * 节目 - 广告抽奖
 */
@Repository
public interface AdRushRepository extends KbMongoRepository<AdRush, String> {

    Page<AdRush> findBySiteIdAndTypeInOrderByCreateTimeDesc(Integer siteId, Integer[] types, Pageable pageable);

    Page<AdRush> findBySiteIdAndRushStateInOrderByCreateTimeDesc(Integer siteId, Integer[] states, Pageable pageable);

    Page<AdRush> findBySiteIdAndRushStateInAndTypeInOrderByCreateTimeDesc(Integer siteId, Integer[] states,Integer[] types, Pageable pageable);

    Page<AdRush> findBySiteIdOrderByCreateTimeDesc(Integer siteId, Pageable pageable);

    List<AdRush> findByIdIn(List<String> adRushIdList);

    default AdRush updateAdRush(AdRush adRush) {
        long current = System.currentTimeMillis() / 1000;
        return mongo().findAndModify(
                new Query(Criteria
                        .where("_id").is(new ObjectId(adRush.getId()))),
                new KbMongoBeanUpdate(adRush)
                        .setIfNotEmpty("title")
                        .setIfNotEmpty("imageAdHead")
                        .setIfNotEmpty("imageAdBottomPrepare")
                        .setIfNotEmpty("imageAdBottomLucky")
                        .setIfNotEmpty("imageAdBottomUnlucky")
                        .setIfNotEmpty("imageAdNotifyLucky")
                        .setIfNotEmpty("imageAdRush")
                        .setIfNotEmpty("rushButtonColor")
                        .setIfNotEmpty("rushButtonBorderColor")
                        .setIfNotEmpty("rushButtonTextColor")
                        .setIfNotEmpty("adLink")
                        .setIfNotEmpty("originalUrl")
                        .setIfNotEmpty("mapperUrl")
                        .setIfNotNull("type")
                        .set("updateTime", current),
                new FindAndModifyOptions().returnNew(true).upsert(false),
                AdRush.class);
    }

    default void updateRushState(String rushId, Integer rushState) {
        if (rushId == null || rushState == null) {
            throw new KbException(KbCode.ILLEGAL_ARGUMENT);
        }
        long current = System.currentTimeMillis() / 1000;
        mongo().findAndModify(
                new Query(Criteria.where("rushId").is(rushId)),
                new Update().set("rushState", rushState).set("updateTime", current),
                AdRush.class);
    }

    default void updateAdRushViewCount(String adRushId, Long viewCount) {
        if (adRushId == null || viewCount == null) {
            throw new KbException(KbCode.ILLEGAL_ARGUMENT);
        }
        long current = System.currentTimeMillis() / 1000;
        mongo().findAndModify(
                new Query(Criteria.where("_id").is(new ObjectId(adRushId))),
                new Update().set("viewCount", viewCount).set("updateTime", current).inc("v", 1),
                AdRush.class);
    }
}
