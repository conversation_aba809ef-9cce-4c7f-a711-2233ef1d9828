package com.kaiba.m.core.repository.note;

import com.kaiba.m.core.domain.note.Note;
import com.kaiba.m.core.domain.note.NoteThreadHot;
import com.kaiba.m.core.middleware.mongo.interfaces.KbMongoRepository;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.core.FindAndModifyOptions;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * author: lyux
 * date: 19-5-28
 */
@Repository
public interface NoteThreadHotRepository extends KbMongoRepository<NoteThreadHot, String> {

    long countByThreadId(String threadId);

    void deleteByNoteIdAndThreadId(String noteId, String threadId);

    Optional<NoteThreadHot> findFirstByNoteIdAndThreadId(String noteId, String threadId);

    boolean existsByNoteIdAndThreadId(String noteId, String threadId);

    Page<NoteThreadHot> findByOrderByIdDesc(Pageable pageable);

    Page<NoteThreadHot> findByThreadIdOrderByRefreshTimeDesc(String threadId, Pageable pageable);

    List<NoteThreadHot> findByNoteId(String noteId);

    List<NoteThreadHot> findAllByNoteIdIn(List<String> noteIds);

    default NoteThreadHot updateHotNote(String threadId, Note note, String title, String cover) {
        long now = System.currentTimeMillis();
        Update update = new Update()
                .set("noteId", note.getId())
                .set("threadId", threadId)
                .setOnInsert("refreshTime", now / 1000)
                .setOnInsert("refreshTimeMS", now)
                .setOnInsert("createTime", now / 1000)
                .setOnInsert("createTimeMS", now)
                .set("title", title)
                .set("subTitle", note.contentAsString())
                .set("cover", cover);
        return mongo().findAndModify(
                new Query(Criteria.where("noteId").is(note.getId()).and("threadId").is(threadId)),
                update,
                new FindAndModifyOptions().upsert(true).returnNew(true),
                NoteThreadHot.class);
    }

    default NoteThreadHot updateHotNoteRefreshTime(String threadId, String noteId) {
        long now = System.currentTimeMillis();
        return mongo().findAndModify(
                new Query(Criteria.where("noteId").is(noteId).and("threadId").is(threadId)),
                new Update()
                        .set("refreshTime", now / 1000)
                        .set("refreshTimeMS", now),
                new FindAndModifyOptions().upsert(false).returnNew(true),
                NoteThreadHot.class);
    }

    default void updateHotNotesRefreshTime(List<String> noteIds, long refreshTime) {
        mongo().updateMulti(
            new Query(Criteria.where("noteId").in(noteIds)),
            new Update()
                    .set("refreshTime", refreshTime)
                    .set("refreshTimeMS", refreshTime * 1000),
            NoteThreadHot.class);
    }

}
