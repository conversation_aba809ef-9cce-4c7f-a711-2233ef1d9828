package com.kaiba.m.core.repository.program;

import com.kaiba.m.core.domain.program.Program;
import com.kaiba.m.core.middleware.mongo.interfaces.KbMongoRepository;
import org.bson.types.ObjectId;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.core.FindAndModifyOptions;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * author wangsj
 * date 2020-09-01
 */
@Repository
public interface ProgramRepository extends KbMongoRepository<Program, String> {

    Optional<Program> findFirstBySiteIdAndReserved(Integer siteId, Integer reserved);

    Optional<Program> findBySiteIdAndName(Integer siteId, String name);

    Page<Program> findAllBySiteIdAndOnline(Integer siteId, Integer online, Pageable pageable);

    default Optional<Program> updateOnlineById(String id, Integer userId, Integer online) {
        Program program = mongo().findAndModify(
                new Query(Criteria.where("_id").is(new ObjectId(id))),
                new Update().set("online", online).set("lastUpdate", System.currentTimeMillis() / 1000).set("updateUser", userId),
                FindAndModifyOptions.options().returnNew(true),
                Program.class
        );
        return Optional.ofNullable(program);
    }
}
