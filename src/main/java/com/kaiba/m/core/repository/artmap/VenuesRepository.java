package com.kaiba.m.core.repository.artmap;

import com.kaiba.m.core.domain.artmap.Venues;
import com.kaiba.m.core.middleware.mongo.interfaces.KbMongoRepository;
import com.kaiba.m.core.model.artmap.dto.VenuesQueryDTO;
import java.util.List;
import java.util.Objects;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.geo.Box;
import org.springframework.data.mongodb.core.FindAndModifyOptions;
import org.springframework.data.mongodb.core.geo.GeoJsonPoint;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Repository;

/**
 * Description: 艺术地图场馆管理repository
 * Author: ZM227
 * Date: 2025/6/3 17:14
 */
@Repository
public interface VenuesRepository extends KbMongoRepository<Venues, String> {

    /**
     * 条件查询场馆
     *
     * @param queryDTO 查询条件
     * @return 场馆信息
     */
    default Page<Venues> findVenuesByCondition(VenuesQueryDTO queryDTO) {
        Criteria criteria = new Criteria();
        if (StringUtils.isNotBlank(queryDTO.getVenuesCode())) {
            criteria.and("venuesCode").is(queryDTO.getVenuesCode());
        }
        if (StringUtils.isNotBlank(queryDTO.getName())) {
            criteria.and("name").regex(queryDTO.getName());
        }
        if (StringUtils.isNotBlank(queryDTO.getContactPhone())) {
            criteria.and("contactPhone").is(queryDTO.getContactPhone());
        }
        if (Objects.nonNull(queryDTO.getStatus())) {
            criteria.and("status").is(queryDTO.getStatus());
        }
        if (Objects.nonNull(queryDTO.getVenuesType())) {
            criteria.and("venuesType").is(queryDTO.getVenuesType());
        }
        if (Objects.nonNull(queryDTO.getLongitude()) && Objects.nonNull(queryDTO.getLatitude())) {
            GeoJsonPoint point = new GeoJsonPoint(queryDTO.getLongitude(), queryDTO.getLatitude());
            criteria.and("location").is(point);
        }
        if (Objects.nonNull(queryDTO.getXAxis()) && Objects.nonNull(queryDTO.getYAxis())) {
            criteria.and("xAxis").is(queryDTO.getXAxis());
            criteria.and("yAxis").is(queryDTO.getYAxis());
        }
        if (Objects.nonNull(queryDTO.getTheaterId())) {
            criteria.and("theaterIds").in(queryDTO.getTheaterId());
        }
        Query query = new Query(criteria);
        long total = mongo().count(query, Venues.class);
        Sort customSort = Sort.by(Sort.Order.asc("seq")); // 优先按seq正序
        if (Objects.isNull(queryDTO.getPageable())) {
            // 默认分页设置：优先seq正序 + 默认时间倒序
            Sort defaultSort = customSort.and(Sort.by(Sort.Direction.DESC, "createTime"));
            queryDTO.setPageable(PageRequest.of(0, Math.max(1, (int) total), defaultSort));
        } else {
            // 合并排序：将seq正序插入到现有排序最前面
            Sort existingSort = queryDTO.getPageable().getSort();
            queryDTO.setPageable(
                PageRequest.of(
                    queryDTO.getPageable().getPageNumber(),
                    queryDTO.getPageable().getPageSize(),
                    customSort.and(existingSort) // 保持原有其他排序但优先级后移
                )
            );
        }
        List<Venues> list = mongo().find(query.with(queryDTO.getPageable()), Venues.class);
        return new PageImpl<>(list, queryDTO.getPageable(), total);
    }

    /**
     * 通过场馆编码更新场馆信息
     *
     * @param venues 场馆信息
     * @return 更新后的场馆信息
     */
    default Venues updateByVenuesCode(Venues venues) {
        if (Objects.isNull(venues)) {
            return null;
        }
        Update update = new Update();
        if (StringUtils.isNotBlank(venues.getName())) {
            update.set("name", venues.getName());
        }
        if (CollectionUtils.isNotEmpty(venues.getPictures())) {
            update.set("pictures", venues.getPictures());
        }
        if (Objects.nonNull(venues.getMapPicture())) {
            update.set("mapPicture", venues.getMapPicture());
        }
        if (StringUtils.isNotBlank(venues.getContactPhone())) {
            update.set("contactPhone", venues.getContactPhone());
        }
        if (StringUtils.isNotBlank(venues.getAddress())) {
            update.set("address", venues.getAddress());
        }
        if (Objects.nonNull(venues.getVenuesType())) {
            update.set("venuesType", venues.getVenuesType());
        }
        if (Objects.nonNull(venues.getStatus())) {
            update.set("status", venues.getStatus());
        }
        if (Objects.nonNull(venues.getLocation())) {
            update.set("location", venues.getLocation());
        }
        update.set("suggestTime", venues.getSuggestTime());
        update.set("openingHours", venues.getOpeningHours());
        if (CollectionUtils.isNotEmpty(venues.getTheaterIds())) {
            update.set("theaterIds", venues.getTheaterIds());
        }
        if (StringUtils.isNotBlank(venues.getDescription())) {
            update.set("description", venues.getDescription());
        }
        if (Objects.nonNull(venues.getSeq())) {
            update.set("seq", venues.getSeq());
        }
        update.set("updateTime", System.currentTimeMillis());
        return mongo().findAndModify(
            new Query(Criteria.where("venuesCode").is(venues.getVenuesCode())), update,
            new FindAndModifyOptions().upsert(false).returnNew(true), Venues.class);
    }

    List<Venues> findAllByLocationWithinOrderBySeqAsc(Box box);

    @org.springframework.data.mongodb.repository.Query("{ 'xAxis' : { $gte: ?0, $lte: ?1 }, 'yAxis' : { $gte: ?2, $lte: ?3 } }")
    List<Venues> findByXAxisBetweenAndYAxisBetweenOrderBySeqAsc(Integer xMin, Integer xMax,
        Integer yMin, Integer yMax);

    Venues findFirstByVenuesCode(String venueCode);
}
