package com.kaiba.m.core.repository.workorder;

import com.kaiba.lib.base.constant.workorder.WOTeamMemberState;
import com.kaiba.m.core.domain.workorder.WOTeamMember;
import com.kaiba.m.core.middleware.mongo.interfaces.KbMongoRepository;
import com.kaiba.m.core.middleware.mongo.utils.KbMongoUpdate;
import org.bson.types.ObjectId;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.core.FindAndModifyOptions;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * author: lyux
 * date: 2023-08-11
 */
@Repository
public interface WOTeamMemberRepository extends KbMongoRepository<WOTeamMember, String> {

    List<WOTeamMember> getByMobileAndBiz(String mobile, String biz, Pageable pageable);
    List<WOTeamMember> getByMobile(String mobile, Pageable pageable);

    List<WOTeamMember> getByMobileAndBizAndState(String mobile, String biz, String state, Pageable pageable);
    List<WOTeamMember> getByMobileAndState(String mobile, String state, Pageable pageable);

    List<WOTeamMember> getByUserIdAndBiz(Integer userId, String biz, Pageable pageable);
    List<WOTeamMember> getByUserId(Integer userId, Pageable pageable);

    List<WOTeamMember> getByUserIdAndBizAndState(Integer userId, String biz, String state, Pageable pageable);
    List<WOTeamMember> getByUserIdAndState(Integer userId, String state, Pageable pageable);

    List<WOTeamMember> getByTeamId(String teamId, Pageable pageable);

    List<WOTeamMember> getByTeamIdAndState(String teamId, String state, Pageable pageable);

    long countByTeamId(String teamId);

    boolean existsByUserIdAndTeamId(Integer userId, String teamId);

    default WOTeamMember updateUserId(String memberId, Integer userId, WOTeamMemberState state) {
        Update update = new Update().set("updateTime", System.currentTimeMillis());
        if (userId == null) {
            update.unset("userId").set("state", state.name());
        } else {
            update.set("userId", userId).set("state", state.name());
        }
        return mongo().findAndModify(
                new Query(Criteria.where("_id").is(new ObjectId(memberId))),
                update,
                new FindAndModifyOptions().returnNew(true).upsert(false),
                WOTeamMember.class);
    }

    default WOTeamMember updateState(String memberId, WOTeamMemberState state) {
        return mongo().findAndModify(
                new Query(Criteria.where("_id").is(new ObjectId(memberId))),
                new Update()
                        .set("state", state.name())
                        .set("updateTime", System.currentTimeMillis()),
                new FindAndModifyOptions().returnNew(true).upsert(false),
                WOTeamMember.class);
    }

    default WOTeamMember updateMobile(String memberId, String mobile) {
        return mongo().findAndModify(
                new Query(Criteria.where("_id").is(new ObjectId(memberId))),
                new Update()
                        .set("mobile", mobile)
                        .set("updateTime", System.currentTimeMillis()),
                new FindAndModifyOptions().returnNew(true).upsert(false),
                WOTeamMember.class);
    }

    default WOTeamMember updateDisplay(String memberId, String title, String nickname, String realName, String mobile) {
        return mongo().findAndModify(
                new Query(Criteria.where("_id").is(new ObjectId(memberId))),
                new KbMongoUpdate()
                        .setIfNotEmpty("title", title)
                        .setIfNotEmpty("nickname", nickname)
                        .setIfNotEmpty("realName", realName)
                        .setIfNotEmpty("mobile", mobile)
                        .set("updateTime", System.currentTimeMillis()),
                new FindAndModifyOptions().returnNew(true).upsert(false),
                WOTeamMember.class);
    }

}
