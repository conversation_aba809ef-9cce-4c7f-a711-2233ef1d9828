package com.kaiba.m.core.repository.user;

import com.kaiba.m.core.domain.user.UserInfo;
import com.kaiba.m.core.middleware.mongo.interfaces.KbMongoRepository;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Repository;

/**
 * author: lyux
 * date: 18-12-7
 */
@Repository
public interface UserInfoRepository extends KbMongoRepository<UserInfo, Integer> {

    default void updateExpertDeviceToken(Integer userId, String deviceToken) {
        mongo().findAndModify(
                Query.query(Criteria.where("_id").is(userId)),
                new Update().set("deviceTokenExpert", deviceToken),
                UserInfo.class);
    }

    default void updateUserDeviceToken(Integer userId, String deviceNumber, String deviceToken) {
        Update update = new Update();
        if (null != deviceNumber) {
            update.set("deviceNumber", deviceNumber);
        }
        if (null != deviceToken) {
            update.set("deviceToken", deviceToken);
        }
        mongo().findAndModify(
                Query.query(Criteria.where("_id").is(userId)), update,
                UserInfo.class);
    }

    default void removeInterestedCar(Integer userId, String carSeries) {
        Update update = new Update().pull("interestedCar", carSeries);
        mongo().findAndModify(
                Query.query(Criteria.where("_id").is(userId)), update,
                UserInfo.class);
    }

    default void addInterestedCar(Integer userId, String carSeries) {
        Update update = new Update().addToSet("interestedCar", carSeries);
        mongo().findAndModify(
                Query.query(Criteria.where("_id").is(userId)), update,
                UserInfo.class);
    }

}
