package com.kaiba.m.core.repository.activityhub;

import com.kaiba.m.core.domain.SeqBasedIdxFirstOrder;
import com.kaiba.m.core.domain.activityhub.KbActivityPage;
import com.kaiba.m.core.middleware.mongo.interfaces.KbMongoRepository;
import com.kaiba.m.core.middleware.mongo.utils.KbMongoUpdate;
import org.bson.types.ObjectId;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.BulkOperations;
import org.springframework.data.mongodb.core.FindAndModifyOptions;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.data.util.Pair;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;

/**
 * author: lyux
 * date: 2023-08-08
 */
@Repository
public interface ActivityPageRepository extends KbMongoRepository<KbActivityPage, String>  {

    Page<KbActivityPage> findByActivityIdOrderBySeqDesc(String activityId, Pageable pageable);

    Page<KbActivityPage> findByUrlOrderByIdDesc(String url, Pageable pageable);

    List<KbActivityPage> findByIdIn(Collection<String> pageIds);

    default List<KbActivityPage> getPageByUrlParts(String host, String path, String query, Pageable page) {
        Criteria criteria = new Criteria();
        if (query != null) {
            criteria.and("urlQuery").is(query);
        }
        if (path != null) {
            criteria.and("urlPath").is(path);
        }
        if (host != null) {
            criteria.and("urlHost").is(host);
        }
        return mongo().find(
                new Query(criteria).with(page).with(Sort.by(Sort.Direction.DESC, "_id")),
                KbActivityPage.class);
    }

    default void updateIdx(String pageId, Long idx, long seq) {
        mongo().findAndModify(
                new Query(Criteria.where("_id").is(pageId)),
                new KbMongoUpdate()
                        .setUnsetOnNull("idx", idx)
                        .set("seq", seq)
                        .set("updateTime", System.currentTimeMillis()),
                new FindAndModifyOptions().upsert(false).returnNew(false),
                KbActivityPage.class);
    }

    default void updateIdxBatch(List<SeqBasedIdxFirstOrder> list) {
        BulkOperations ops = mongo().bulkOps(BulkOperations.BulkMode.ORDERED, KbActivityPage.class);
        List<Pair<Query, Update>> updates = list.stream().
                map(m -> Pair.of(
                        new Query(Criteria.where("_id").is(new ObjectId(m.getId()))),
                        new Update()
                                .set("seq", m.getSeq())
                                .set("idx", m.getIdx())
                                .set("updateTime", System.currentTimeMillis())))
                .collect(Collectors.toList());
        ops.updateMulti(updates);
        ops.execute();
    }

}
