package com.kaiba.m.core.repository.issue;

import com.kaiba.m.core.domain.issue.IssueMessage;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

import java.util.Optional;

/**
 * author: lyux
 * date: 18-9-2
 */
@Repository
public interface IssueMessageRepository extends MongoRepository<IssueMessage, String> {

    long countByIssueId(String issueId);

    long countByIssueIdAndSenderType(String issueId, Integer senderType);

    Page<IssueMessage> findByIssueIdOrderByTimestampAsc(String issueId, Pageable pageable);

    Page<IssueMessage> findByIssueIdOrderByTimestampDesc(String issueId, Pageable pageable);

    Page<IssueMessage> findByIssueIdAndSenderTypeOrderByTimestampAsc(String issueId, Integer senderType, Pageable pageable);

    Page<IssueMessage> findByIssueIdAndSenderTypeOrderByTimestampDesc(String issueId, Integer senderType, Pageable pageable);

    Optional<IssueMessage> findFirstByIssueIdOrderByTimestampDesc(String issueId);
}
