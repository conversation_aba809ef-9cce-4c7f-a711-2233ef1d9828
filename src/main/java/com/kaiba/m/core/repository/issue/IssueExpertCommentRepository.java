package com.kaiba.m.core.repository.issue;

import com.kaiba.lib.base.constant.issue.IssueCommentType;
import com.kaiba.m.core.domain.issue.Issue;
import com.kaiba.m.core.domain.issue.IssueExpertComment;
import com.kaiba.m.core.middleware.mongo.interfaces.KbMongoRepository;
import org.springframework.data.mongodb.core.FindAndModifyOptions;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * author: lyux
 * date: 18-9-11
 */
@Repository
public interface IssueExpertCommentRepository extends KbMongoRepository<IssueExpertComment, String> {

    List<IssueExpertComment> findAllByExpertId(Integer expertId);

    boolean existsByIssueIdAndUserId(String issueId, Integer userId);

    Optional<IssueExpertComment> findByIssueId(String issueId);

    Optional<IssueExpertComment> findByIssueIdAndType(String issueId, Integer type);

    boolean existsByIssueIdAndType(String issueId, Integer type);

    List<IssueExpertComment> findByIssueIdIn(String[] issueIds);

    List<IssueExpertComment> findByIssueIdInAndType(String[] issueIds, Integer type);

    default void userComment(Issue issue, Integer satisfaction, Integer quality, Integer speed, String content) {
        // overwrite system comment (if exists)
        mongo().findAndModify(
                new Query(Criteria
                        .where("issueId").is(issue.getId())
                        .and("userId").is(issue.getUserId())
                        .orOperator(
                                Criteria.where("type").is(IssueCommentType.USER.getValue()),
                                Criteria.where("type").is(IssueCommentType.SYSTEM.getValue())
                        )
                ),
                new Update()
                        .set("issueId", issue.getId())
                        .set("userId", issue.getUserId())
                        .set("expertId", issue.getExpertId())
                        .set("type", IssueCommentType.USER.getValue())
                        .set("satisfactionRatio", satisfaction)
                        .set("qualityRatio", quality)
                        .set("speedRatio", speed)
                        .set("content", content)
                        .set("createTime", System.currentTimeMillis() / 1000),
                new FindAndModifyOptions().upsert(true),
                IssueExpertComment.class);
    }

    default void systemComment(Issue issue) {
        mongo().findAndModify(
                new Query(Criteria
                        .where("issueId").is(issue.getId())
                        .and("userId").is(issue.getUserId())
                ),
                new Update()
                        .set("issueId", issue.getId())
                        .set("userId", issue.getUserId())
                        .set("expertId", issue.getExpertId())
                        .set("type", IssueCommentType.SYSTEM.getValue())
                        .set("satisfactionRatio", 5)
                        .set("qualityRatio", 5)
                        .set("speedRatio", 5)
                        .set("createTime", System.currentTimeMillis() / 1000),
                new FindAndModifyOptions().upsert(true),
                IssueExpertComment.class);
    }

}
