package com.kaiba.m.core.repository.education.recitation;

import com.kaiba.m.core.domain.education.recitation.Activity;
import com.kaiba.m.core.domain.education.recitation.enums.ActivityTypeEnum;
import com.kaiba.m.core.domain.education.recitation.enums.CommonStatusEnum;
import com.kaiba.m.core.middleware.mongo.interfaces.KbMongoRepository;
import java.util.List;
import java.util.Objects;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.core.FindAndModifyOptions;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Repository;

/**
 * Description: 活动repository
 * Author: ZM227
 * Date: 2024/8/8 18:00
 */
@Repository
public interface RecitationActivityRepository extends KbMongoRepository<Activity, String> {

    default List<Activity> findExistActivity(String activityCode, Long registerStart,
        Long registerEnd, ActivityTypeEnum activityType) {
        Criteria criteria = new Criteria();
        if (StringUtils.isNotBlank(activityCode)) {
            criteria.and("activityCode").is(activityCode);
        }
        if (Objects.nonNull(registerStart)) {
            criteria.and("registerStart").lt(registerEnd);
        }
        if (Objects.nonNull(registerEnd)) {
            criteria.and("registerEnd").gt(registerStart);
        }
        if (Objects.nonNull(activityType)) {
            criteria.and("activityType").is(activityType);
        }
        criteria.and("status").is(CommonStatusEnum.VALID);
        Query query = new Query(criteria);
        return mongo().find(query, Activity.class);
    }

    default Page<Activity> findActivities(Pageable pageable) {
        Criteria criteria = new Criteria();
        criteria.and("status").is(CommonStatusEnum.VALID);
        Query query = new Query(criteria);
        long total = mongo().count(query, Activity.class);
        List<Activity> list = mongo().find(query.with(pageable), Activity.class);
        return new PageImpl<>(list, pageable, total);
    }

    default Activity updateByActivityCode(Activity activity) {
        Update update = new Update();
        if (StringUtils.isNotBlank(activity.getTitle())) {
            update.set("title", activity.getTitle());
        }
        if (StringUtils.isNotBlank(activity.getSubTitle())) {
            update.set("subTitle", activity.getSubTitle());
        }
        if (Objects.nonNull(activity.getRegisterStart())) {
            update.set("registerStart", activity.getRegisterStart());
        }
        if (Objects.nonNull(activity.getRegisterEnd())) {
            update.set("registerEnd", activity.getRegisterEnd());
        }
        if (Objects.nonNull(activity.getActivityType())) {
            update.set("activityType", activity.getActivityType());
        }
        if (StringUtils.isNotBlank(activity.getAttribute().getContent())) {
            update.set("attribute", activity.getAttribute());
        }
        if (StringUtils.isNotBlank(activity.getExtendInfo().getContent())) {
            update.set("extendInfo", activity.getExtendInfo());
        }
        if (Objects.nonNull(activity.getStatus())) {
            update.set("status", activity.getStatus());
        }
        update.set("updateTime", System.currentTimeMillis());
        return mongo().findAndModify(
            new Query(Criteria.where("activityCode").is(activity.getActivityCode())), update,
            new FindAndModifyOptions().upsert(false).returnNew(true), Activity.class);
    }

    Activity findFirstByActivityCode(String activityCode);

}
