package com.kaiba.m.core.repository.advertisement;

import com.kaiba.m.core.domain.advertisement.NewsAd;
import com.kaiba.m.core.middleware.mongo.interfaces.KbMongoRepository;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.core.FindAndModifyOptions;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.data.mongodb.repository.Query;
import org.springframework.stereotype.Repository;

@Repository
public interface NewsAdRepository extends KbMongoRepository<NewsAd, String> {
    String simpleFields = "{title:1,siteId:1,signUser:1,signTime:1,images:1,viewCount:1,display:1,isSign:1,browser:1}";

    @Query(fields = simpleFields)
    Page<NewsAd> findBySiteIdAndType(Integer siteId, Integer type, Pageable pageable);

    @Query(fields = simpleFields)
    Page<NewsAd> findBySiteIdAndTypeAndTitleRegex(Integer siteId, Integer type, String title, Pageable pageable);

    @Query(fields = simpleFields)
    Page<NewsAd> findBySiteIdAndIsSignAndType(Integer siteId, Integer isSign, Integer type, Pageable pageable);

    default NewsAd viewNewsAd(String id) {
        return mongo().findAndModify(
                new org.springframework.data.mongodb.core.query.Query(Criteria.where("_id").is(id).and("isSign").is(1).and("display").is(1)),
                new Update().inc("viewCount", 1),
                FindAndModifyOptions.options().returnNew(true),
                NewsAd.class
        );
    }
}
