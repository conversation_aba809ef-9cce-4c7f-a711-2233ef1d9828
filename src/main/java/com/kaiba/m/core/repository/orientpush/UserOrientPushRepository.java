package com.kaiba.m.core.repository.orientpush;

import com.kaiba.m.core.domain.orientpush.UserOrientPush;
import com.kaiba.m.core.middleware.mongo.interfaces.KbMongoRepository;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 * @version UserOrientPushRepository, v0.1 2025/5/15 09:49 daopei Exp $
 **/
@Repository
public interface UserOrientPushRepository extends KbMongoRepository<UserOrientPush, String> {


    Page<UserOrientPush> findByBizAndRef1(String biz, String ref1, Pageable pageable);
}
