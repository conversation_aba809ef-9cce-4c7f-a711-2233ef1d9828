package com.kaiba.m.core.repository.hoolo.programme;

import com.kaiba.m.core.domain.hoolo.SyncNewsArticleRecord;
import com.kaiba.m.core.middleware.mongo.interfaces.KbMongoRepository;
import java.util.List;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

/**
 * Description: 资讯同步记录repository
 * Author: ZM227
 * Date: 2024/10/14 11:14
 */
public interface SyncNewsArticleRecordRepository extends
    KbMongoRepository<SyncNewsArticleRecord, String> {

    Page<SyncNewsArticleRecord> findSyncNewsArticleRecordsByProgrammeIdOrderByFinishTimeDesc(
        String programmeKey, Pageable pageable);

    Page<SyncNewsArticleRecord> findSyncNewsArticleRecordsByProgrammeIdAndSyncArticleCountGreaterThanOrderByFinishTimeDesc(
        String programmeKey, int articleCount, Pageable pageable);

    SyncNewsArticleRecord findSyncNewsArticleRecordById(String id);
}
