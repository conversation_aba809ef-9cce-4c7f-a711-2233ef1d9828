package com.kaiba.m.core.repository.workorder;

import com.kaiba.m.core.domain.workorder.WOCallbackTrigger;
import com.kaiba.m.core.domain.workorder.WOEvent;
import com.kaiba.m.core.middleware.mongo.interfaces.KbMongoRepository;
import org.bson.types.ObjectId;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.core.FindAndModifyOptions;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

/**
 * author: lyux
 * date: 2024-01-17
 */
@Repository
public interface WOCallbackTriggerRepository extends KbMongoRepository<WOCallbackTrigger, String> {

    List<WOCallbackTrigger> getByFullMarkOrderByFireTimeAsc(String fullMark);

    List<WOCallbackTrigger> getByFullMarkOrderByFireTimeDesc(String fullMark);

    List<WOCallbackTrigger> getByCaseIdOrderByFireTimeDesc(String caseId);

    Page<WOCallbackTrigger> findByFireTimeLessThan(long fireTime, Pageable pageable);

    List<WOCallbackTrigger> findByFullMarkIn(Collection<String> fullMarks);

    List<WOCallbackTrigger> findByCaseIdInOrderByFireTimeDesc(Collection<String> caseIds);

    void deleteByExpireTimeLessThan(long expireTime);

    void deleteByFullMark(String fullMark);

    void deleteByIdIn(Collection<String> triggerIds);

    void deleteByCaseId(String caseId);

    default void create(WOEvent event, String mark, long fireTime, long expireTime) {
        String fullMark = WOCallbackTrigger.generateFullMark(event.getCaseId(), mark);
        WOCallbackTrigger trigger = new WOCallbackTrigger();
        trigger.setBiz(event.getBiz());
        trigger.setCaseId(event.getCaseId());
        trigger.setEventId(event.getId());
        trigger.setMark(mark);
        trigger.setFullMark(fullMark);
        trigger.setFireTime(fireTime);
        trigger.setExpireTime(expireTime);
        trigger.setUpdateTime(System.currentTimeMillis());
        insert(trigger);
    }

    default void upsert(WOEvent event, String mark, long fireTime, long expireTime) {
        String fullMark = WOCallbackTrigger.generateFullMark(event.getCaseId(), mark);
        mongo().findAndModify(
                new Query(Criteria.where("fullMark").is(fullMark)),
                new Update()
                        .setOnInsert("biz", event.getBiz())
                        .setOnInsert("caseId", event.getCaseId())
                        .setOnInsert("eventId", event.getId())
                        .setOnInsert("fullMark", fullMark)
                        .set("fireTime", fireTime)
                        .set("expireTime", expireTime)
                        .set("updateTime", System.currentTimeMillis()),
                new FindAndModifyOptions().returnNew(false).upsert(true),
                WOCallbackTrigger.class);
    }

    default void upsert(String caseId, String biz, String eventId, String mark, long fireTime, long expireTime) {
        String fullMark = WOCallbackTrigger.generateFullMark(caseId, mark);
        mongo().findAndModify(
                new Query(Criteria.where("fullMark").is(fullMark)),
                new Update()
                        .setOnInsert("biz", biz)
                        .setOnInsert("caseId", caseId)
                        .setOnInsert("eventId", eventId)
                        .setOnInsert("fullMark", fullMark)
                        .set("fireTime", fireTime)
                        .set("expireTime", expireTime)
                        .set("updateTime", System.currentTimeMillis()),
                new FindAndModifyOptions().returnNew(false).upsert(true),
                WOCallbackTrigger.class);
    }

    default void updateTimeByTriggerId(String triggerId, long fireTime, long expireTime) {
        mongo().findAndModify(
                new Query(Criteria.where("_id").is(new ObjectId(triggerId))),
                new Update()
                        .set("fireTime", fireTime)
                        .set("expireTime", expireTime)
                        .set("updateTime", System.currentTimeMillis()),
                new FindAndModifyOptions().returnNew(false).upsert(false),
                WOCallbackTrigger.class);
    }

    default void deleteTriggerListExceptFirst(List<WOCallbackTrigger> triggers) {
        if (triggers.size() <= 1) {
            return;
        }
        List<String> deleteIds = new ArrayList<>(triggers.size() - 1);
        for (int i = 1; i < triggers.size(); i ++) {
            deleteIds.add(triggers.get(i).getId());
        }
        deleteByIdIn(deleteIds);
    }

}
