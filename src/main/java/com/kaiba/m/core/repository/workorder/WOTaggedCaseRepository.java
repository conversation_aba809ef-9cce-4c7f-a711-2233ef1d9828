package com.kaiba.m.core.repository.workorder;

import com.kaiba.m.core.domain.SeqBasedIdxFirstOrder;
import com.kaiba.m.core.domain.workorder.WOTag;
import com.kaiba.m.core.domain.workorder.WOTaggedCase;
import com.kaiba.m.core.middleware.mongo.interfaces.KbMongoRepository;
import com.kaiba.m.core.middleware.mongo.utils.KbMongoUpdate;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.core.BulkOperations;
import org.springframework.data.mongodb.core.FindAndModifyOptions;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.data.util.Pair;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * author: lyux
 * date: 2024-05-28
 */
@Repository
public interface WOTaggedCaseRepository extends KbMongoRepository<WOTaggedCase, String> {

    Optional<WOTaggedCase> getFirstByTag(long tag);

    Optional<WOTaggedCase> getFirstByCaseIdAndTag(String caseId, long tag);

    List<WOTaggedCase> getByCaseIdOrderByIdDesc(String caseId, Pageable pageable);

    List<WOTaggedCase> getByCaseIdInOrderByIdDesc(Collection<String> caseId, Pageable pageable);

    List<WOTaggedCase> getByTagOrderBySeqDesc(long tag, Pageable pageable);

    List<WOTaggedCase> getByTagAndCaseIdIn(long tag, Collection<String> caseIds);

    Page<WOTaggedCase> findByCaseIdOrderByIdDesc(String caseId, Pageable pageable);
    Page<WOTaggedCase> findByOrderByIdDesc(Pageable pageable);

    Page<WOTaggedCase> findByTagOrderBySeqDesc(long tag, Pageable pageable);

    long countByCaseIdAndParent(String caseId, long parent);

    void deleteByCaseIdAndTag(String caseId, long tag);
    void deleteByCaseIdAndTagIn(String caseId, List<Long> tags);

    default void upsert(String caseId, WOTag tag, Integer userId) {
        long now = System.currentTimeMillis();
        long seq = SeqBasedIdxFirstOrder.calculateSeq(now, null);
        Update update = new Update()
                .setOnInsert("seq", seq)
                .setOnInsert("caseId", caseId)
                .setOnInsert("tag", tag.getCode())
                .setOnInsert("parent", tag.getParent())
                .set("updateTime", now)
                .setOnInsert("createTime", now);
        if (userId != null) {
            update.set("userId", userId);
        }
        mongo().findAndModify(
                new Query(Criteria.where("caseId").is(caseId).and("tag").is(tag.getCode())),
                update,
                new FindAndModifyOptions().returnNew(false).upsert(true),
                WOTaggedCase.class);
    }

    default void updateIdx(String taggedId, long seq, Long idx) {
        mongo().findAndModify(
                new Query(Criteria.where("_id").is(taggedId)),
                new KbMongoUpdate()
                        .setUnsetOnNull("idx", idx)
                        .set("seq", seq),
                new FindAndModifyOptions().upsert(false).returnNew(false),
                WOTaggedCase.class);
    }

    default void updateIdxBatch(List<SeqBasedIdxFirstOrder> list) {
        BulkOperations ops = mongo().bulkOps(BulkOperations.BulkMode.ORDERED, WOTaggedCase.class);
        List<Pair<Query, Update>> updates = list.stream().
                map(m -> Pair.of(
                        new Query(Criteria.where("_id").is(m.getId())),
                        new Update().set("seq", m.getSeq()).set("idx", m.getIdx())))
                .collect(Collectors.toList());
        ops.updateMulti(updates);
        ops.execute();
    }

}
