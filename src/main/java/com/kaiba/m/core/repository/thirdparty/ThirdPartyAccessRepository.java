package com.kaiba.m.core.repository.thirdparty;

import com.kaiba.m.core.domain.thirdparty.ThirdPartyAccess;
import com.kaiba.m.core.middleware.mongo.interfaces.KbMongoRepository;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.core.FindAndModifyOptions;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Repository;

/**
 * author: lyux
 * date: 2020-09-02
 */
@Repository
public interface ThirdPartyAccessRepository extends KbMongoRepository<ThirdPartyAccess, String> {

    Page<ThirdPartyAccess> findAllByOrderByIdDesc(Pageable pageable);

    default ThirdPartyAccess updateKeyPair(ThirdPartyAccess access, String privateKey, String publicKey) {
        return mongo().findAndModify(
                new Query(Criteria.where("_id").is(access.getId()).and("v").is(access.getV())),
                new Update().set("privateKey", privateKey).set("publicKey", publicKey).inc("v", 1),
                new FindAndModifyOptions().returnNew(true).upsert(false),
                ThirdPartyAccess.class);
    }

    default ThirdPartyAccess updateToken(ThirdPartyAccess access, String token) {
        return mongo().findAndModify(
                new Query(Criteria.where("_id").is(access.getId()).and("v").is(access.getV())),
                new Update().set("token", token).inc("v", 1),
                new FindAndModifyOptions().returnNew(true).upsert(false),
                ThirdPartyAccess.class);
    }

    default ThirdPartyAccess updateSalt(ThirdPartyAccess access, String salt) {
        return mongo().findAndModify(
                new Query(Criteria.where("_id").is(access.getId()).and("v").is(access.getV())),
                new Update().set("salt", salt).inc("v", 1),
                new FindAndModifyOptions().returnNew(true).upsert(false),
                ThirdPartyAccess.class);
    }

    default void updateEnabled(String accessId, boolean enabled) {
        mongo().findAndModify(
                new Query(Criteria.where("_id").is(accessId)),
                new Update().set("enabled", enabled),
                ThirdPartyAccess.class);
    }

    default void updateDescription(String accessId, String description) {
        mongo().findAndModify(
                new Query(Criteria.where("_id").is(accessId)),
                new Update().set("description", description),
                ThirdPartyAccess.class);
    }

}
