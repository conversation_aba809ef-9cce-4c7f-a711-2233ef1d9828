package com.kaiba.m.core.repository.publicservice;

import com.kaiba.m.core.domain.publicservice.PublicServiceItem;
import com.kaiba.m.core.middleware.mongo.interfaces.KbMongoRepository;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.core.FindAndModifyOptions;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/01/08 10:18
 */
@Repository
public interface PublicServiceItemRepository extends KbMongoRepository<PublicServiceItem, String> {

    List<PublicServiceItem> findAllByIdIn(Collection<String> itemIds);

    default Page<PublicServiceItem> findAllByOrderByCreateTimeDesc(String instanceKey, String name, Integer state, Pageable pageable) {
        Criteria criteria = Criteria.where("instanceKey").is(instanceKey);
        if(name != null) {
            criteria.and("name").regex(name);
        }

        if(state != null) {
            criteria.and("state").is(state);
        }

        Query query = new Query(criteria);
        long count = mongo().count(query, PublicServiceItem.class);
        List<PublicServiceItem> list = mongo().find(query.with(pageable), PublicServiceItem.class);
        return new PageImpl<>(list, pageable, count);
    }

    default List<PublicServiceItem> findAllByNameContainsAndState(String instanceKey, String name, Integer state) {
        Criteria criteria = Criteria.where( "instanceKey").is(instanceKey);
        if(name != null) {
            criteria.and("name").regex(name);
        }

        if(state != null) {
            criteria.and("state").is(state);
        }

        return mongo().find(Query.query(criteria), PublicServiceItem.class);
    }

    default PublicServiceItem updateServiceItemState(String itemId, Integer state) {
        return mongo().findAndModify(
                Query.query(Criteria.where("_id").is(itemId)),
                new Update()
                        .set("state", state)
                        .set("updateTime", System.currentTimeMillis()),
                FindAndModifyOptions.options().upsert(false).returnNew(true),
                PublicServiceItem.class);
    }

    default PublicServiceItem updateServiceItem(PublicServiceItem item) {
        return mongo().findAndModify(
                Query.query(Criteria.where("_id").is(item.getId())),
                new Update()
                        .set("state", item.getState())
                        .set("name", item.getName())
                        .set("desc", item.getDesc())
                        .set("icon", item.getIcon())
                        .set("action", item.getAction())
                        .set("actionParams", item.getActionParams())
                        .set("updateTime", System.currentTimeMillis()),
                FindAndModifyOptions.options().upsert(false).returnNew(true),
                PublicServiceItem.class);
    }
}
