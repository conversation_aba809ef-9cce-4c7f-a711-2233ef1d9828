package com.kaiba.m.core.repository.enroll;

import com.kaiba.m.core.domain.enroll.EnrollModule;
import com.kaiba.m.core.middleware.mongo.interfaces.KbMongoRepository;
import org.bson.types.ObjectId;
import org.springframework.data.mongodb.core.FindAndModifyOptions;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Repository;
import org.springframework.data.mongodb.core.query.Query;

import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2019/12/30
 */

@Repository
public interface EnrollModuleRepository extends KbMongoRepository<EnrollModule, String> {

    List<EnrollModule> findAllByEnrollId(String enrollId);

    void deleteByIdIn(Set<String> ids);

    default EnrollModule updateEnrollModule(String id, Integer type, Integer must, Integer multiVote, String title, Object attr) {
        Update update = new Update();
        if (null != type) {
            update.set("type", type);
        }
        if (null != must) {
            update.set("must", must);
        }
        if (null != multiVote) {
            update.set("multiVote", multiVote);
        }
        if (null != title) {
            update.set("title", title);
        }
        if (null != attr) {
            update.set("attr", attr);
        }
        return mongo().findAndModify(
                new Query(Criteria.where("_id").is(new ObjectId(id))),
                update,
                new FindAndModifyOptions().returnNew(true).upsert(false),
                EnrollModule.class);
    }
}
