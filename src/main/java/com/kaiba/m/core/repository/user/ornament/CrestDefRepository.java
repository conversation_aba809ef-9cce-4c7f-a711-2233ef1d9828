package com.kaiba.m.core.repository.user.ornament;

import com.kaiba.lib.base.constant.user.UserOrnamentState;
import com.kaiba.m.core.domain.user.ornament.CrestDef;
import com.kaiba.m.core.middleware.mongo.interfaces.KbMongoRepository;
import com.kaiba.m.core.middleware.mongo.utils.KbMongoUpdate;
import org.bson.types.ObjectId;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.core.FindAndModifyOptions;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.List;
import java.util.Optional;

/**
 * author: lyux
 * date: 2025-04-10
 */
@Repository
public interface CrestDefRepository extends KbMongoRepository<CrestDef, String> {

    Optional<CrestDef> findFirstByCrest(String crest);

    Page<CrestDef> findAllByOrderByIdDesc(Pageable pageable);

    List<CrestDef> getAllByOrderByIdDesc(Pageable pageable);

    List<CrestDef> getByCrestIn(Collection<String> crests);

    default Page<CrestDef> searchByName(String keyword, Pageable pageable) {
        Query query = Query.query(Criteria.where("name").regex(keyword));
        long total = mongo().count(query, CrestDef.class);
        List<CrestDef> list = mongo().find(query.with(pageable), CrestDef.class);
        return new PageImpl<>(list, pageable, total);
    }

    default CrestDef updateData(CrestDef crest) {
        return mongo().findAndModify(
                new Query(Criteria.where("crest").is(crest.getCrest())),
                new KbMongoUpdate()
                        .setIfNotEmpty("name", crest.getName())
                        .setIfNotEmpty("desc", crest.getDesc())
                        .setIfNotEmpty("image", crest.getImage())
                        .setUnsetOnEmpty("initExpireRTE", crest.getInitExpireRTE())
                        .set("updateTime", System.currentTimeMillis()),
                FindAndModifyOptions.options().upsert(false).returnNew(true),
                CrestDef.class);
    }

    default void updateState(String crest, UserOrnamentState state) {
        mongo().findAndModify(
                new Query(Criteria.where("crest").is(crest)),
                new Update()
                        .set("state", state.name())
                        .set("updateTime", System.currentTimeMillis()),
                FindAndModifyOptions.options().upsert(false).returnNew(false),
                CrestDef.class);
    }

    default void updateCrestKey(String crestId, String crest) {
        mongo().findAndModify(
                new Query(Criteria.where("_id").is(new ObjectId(crestId))),
                new Update()
                        .set("crest", crest)
                        .set("updateTime", System.currentTimeMillis()),
                FindAndModifyOptions.options().upsert(false).returnNew(false),
                CrestDef.class);
    }

}
