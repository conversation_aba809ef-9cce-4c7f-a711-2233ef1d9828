package com.kaiba.m.core.repository.da.sensors.kanban;

import com.kaiba.m.core.domain.da.sensors.kanban.KanbanConfig;
import com.kaiba.m.core.middleware.mongo.interfaces.KbMongoRepository;
import com.kaiba.m.core.middleware.mongo.utils.KbMongoCriteria;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.core.FindAndModifyOptions;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/02/28 14:22
 */
@Repository
public interface KanbanConfigRepository extends KbMongoRepository<KanbanConfig, String> {

    List<KanbanConfig> findByIdIn(List<String> ids);

    default KanbanConfig updateKanbanConfig(KanbanConfig config) {
        return mongo().findAndModify(
                new Query(Criteria.where("_id").is(config.getId())),
                new Update()
                        .set("name", config.getName())
                        .set("key", config.getKey())
                        .set("columns", config.getColumns())
                        .set("desc", config.getDesc())
                        .set("stRTE", config.getStRTE())
                        .set("etRTE", config.getEtRTE())
                        .set("updateTime", System.currentTimeMillis()),
                new FindAndModifyOptions().returnNew(true),
                KanbanConfig.class
        );
    }

    default Page<KanbanConfig> findByParam(
            String id,
            String name,
            String key,
            String strategy,
            Pageable pageable
    ) {
        KbMongoCriteria criteria = new KbMongoCriteria();
        criteria.regexIfNotEmpty("name", name)
                .criteriaIfNotNull("id", id)
                .criteriaIfNotNull("key", key)
                .criteriaIfNotNull("strategy", strategy);
        Query query = new Query(criteria);
        long count = mongo().count(query, KanbanConfig.class);
        List<KanbanConfig> list = mongo().find(query.with(pageable), KanbanConfig.class);
        return new PageImpl<>(list, pageable, count);
    }
}
