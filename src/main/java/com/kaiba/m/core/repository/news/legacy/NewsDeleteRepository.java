package com.kaiba.m.core.repository.news.legacy;

import com.kaiba.m.core.domain.news.legacy.NewsDeleted;
import com.kaiba.m.core.middleware.mongo.interfaces.KbMongoRepository;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Repository;

@Repository
public interface NewsDeleteRepository extends KbMongoRepository<NewsDeleted, String> {

    Page<NewsDeleted> findAllByDeleteTimeAfterOrderByDeleteTimeAsc(Long time, Pageable pageable);

}
