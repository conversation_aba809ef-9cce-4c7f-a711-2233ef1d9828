package com.kaiba.m.core.repository.da.eventtrack;

import com.kaiba.lib.base.domain.common.KbTimeRange;
import com.kaiba.m.core.domain.da.eventtrack.ETDoc;
import com.kaiba.m.core.middleware.mongo.interfaces.KbMongoRepository;
import com.kaiba.m.core.middleware.mongo.utils.KbMongoUpdate;
import com.kaiba.m.core.model.da.eventtrack.ETDocQueryModel;
import com.kaiba.m.core.util.PageUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.FindAndModifyOptions;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @version ETDocRepository, v0.1 2025/3/26 10:40 daopei Exp $
 **/
@Repository
public interface ETDocRepository extends KbMongoRepository<ETDoc, String> {

    Optional<ETDoc> findByDocId(String docId);

    List<ETDoc> findByDocIdIn(Collection<String> docIds);

    default Page<ETDoc> getPageQuery(ETDocQueryModel query) {
        int sortBy = query.getSortBy() == null ? ETDocQueryModel.SORT_BY_UPDATE_TIME_ASC : query.getSortBy();
        Sort sort;
        if (sortBy == ETDocQueryModel.SORT_BY_CREATE_TIME_DESC) {
            sort = Sort.by(Sort.Direction.DESC, "createTime");
        } else if (sortBy == ETDocQueryModel.SORT_BY_UPDATE_TIME_DESC) {
            sort = Sort.by(Sort.Direction.DESC, "updateTime");
        } else {
            sort = Sort.by(Sort.Direction.ASC, "updateTime");
        }
        Pageable pageable = PageUtils.ofDefault(query.getPage(), query.getPageSize(), sort);
        Criteria criteria = new Criteria();
        if (query.getTitle() != null) {
            criteria.and("title").regex(query.getTitle());
        }
        if (query.getDocId() != null) {
            criteria.and("docId").is(query.getDocId());
        }
        if (query.getChannel() != null) {
            criteria.and("channel").is(query.getChannel());
        }
        if (query.getDepart() != null) {
            criteria.and("depart").is(query.getDepart());
        }
        if (query.getBiz() != null && !query.getBiz().isEmpty()) {
            criteria.and("biz").in(query.getBiz());
        }
        if (query.getUnit() != null && !query.getUnit().isEmpty()) {
            criteria.and("unit").in(query.getUnit());
        }
        if (query.getRef1() != null) {
            criteria.and("ref1").is(query.getRef1());
        }
        if (query.getRef2() != null) {
            criteria.and("ref2").is(query.getRef2());
        }
        if (query.getRef3() != null) {
            criteria.and("ref3").is(query.getRef3());
        }
        if (query.getSiteId() != null) {
            criteria.and("siteId").is(query.getSiteId());
        }
        if (query.getUpdateTimeRange() != null) {
            timeRangeCriteria(query.getUpdateTimeRange(), "updateTime", criteria);
        }
        Query queryQL = new Query(criteria);

        long count = mongo().count(queryQL, ETDoc.class);
        List<ETDoc> list = mongo().find(queryQL.with(pageable), ETDoc.class);
        return new PageImpl<>(list, pageable, count);
    }

    default ETDoc upsert(ETDoc doc) {
        return mongo().findAndModify(
                new Query(Criteria.where("docId").is(doc.getDocId())),
                new KbMongoUpdate()
                        .setOnInsert("createTime", System.currentTimeMillis())
                        .setOnInsert("biz", doc.getBiz())
                        .setOnInsert("unit", doc.getUnit())
                        .setOnInsert("ref1", doc.getRef1())
                        .setOnInsert("ref2", doc.getRef2())
                        .setOnInsert("ref3", doc.getRef3())
                        .setOnInsert("siteId", doc.getSiteId())
                        .setOnInsert("docId", doc.getDocId())
                        .set("updateTime", System.currentTimeMillis())
                        .set("docCreateTime", doc.getDocCreateTime())
                        .set("title", doc.getTitle())
                        .set("channel", doc.getChannel())
                        .set("depart", doc.getDepart())
                        .set("modules", doc.getModules()),
                FindAndModifyOptions.options().upsert(true).returnNew(true),
                ETDoc.class
        );
    }

    default ETDoc upsertByOVR(ETDoc doc, boolean ovr) {
        return mongo().findAndModify(
                new Query(Criteria.where("docId").is(doc.getDocId())),
                new KbMongoUpdate()
                        .setOnInsert("createTime", System.currentTimeMillis())
                        .setOnInsert("biz", doc.getBiz())
                        .setOnInsert("unit", doc.getUnit())
                        .setOnInsert("ref1", doc.getRef1())
                        .setOnInsert("ref2", doc.getRef2())
                        .setOnInsert("ref3", doc.getRef3())
                        .setOnInsert("siteId", doc.getSiteId())
                        .setOnInsert("docId", doc.getDocId())
                        .set("updateTime", System.currentTimeMillis())
                        .set("autoUpdateOVR", ovr)
                        .set("title", doc.getTitle())
                        .set("channel", doc.getChannel())
                        .set("depart", doc.getDepart())
                        .set("modules", doc.getModules()),
                FindAndModifyOptions.options().upsert(true).returnNew(true),
                ETDoc.class
        );
    }

    default ETDoc updateDocCreateTime(String docId, Long docCreateTime) {
        return mongo().findAndModify(
                new Query(Criteria.where("docId").is(docId)),
                new KbMongoUpdate()
                        .set("updateTime", System.currentTimeMillis())
                        .set("docCreateTime", docCreateTime),
                FindAndModifyOptions.options().upsert(false).returnNew(true),
                ETDoc.class
        );
    }

    default ETDoc updateOVR(String docId, boolean ovr) {
        return mongo().findAndModify(
                new Query(Criteria.where("docId").is(docId)),
                new KbMongoUpdate()
                        .set("updateTime", System.currentTimeMillis())
                        .set("autoUpdateOVR", ovr),
                FindAndModifyOptions.options().upsert(false).returnNew(true),
                ETDoc.class
        );
    }


    default Criteria timeRangeCriteria(KbTimeRange timeRange, String field, Criteria criteria) {
        if (timeRange.isUnbounded()) {
            return criteria;
        }
        Criteria c = criteria.and(field);
        if (timeRange.getLower() != null) {
            if (timeRange.isLowerOpen()) {
                c.gt(timeRange.obtainLowerInMillis());
            } else {
                c.gte(timeRange.obtainLowerInMillis());
            }
        }
        if (timeRange.getUpper() != null) {
            if (timeRange.isUpperOpen()) {
                c.lt(timeRange.obtainUpperInMillis());
            } else {
                c.lte(timeRange.obtainUpperInMillis());
            }
        }
        return c;
    }


}
