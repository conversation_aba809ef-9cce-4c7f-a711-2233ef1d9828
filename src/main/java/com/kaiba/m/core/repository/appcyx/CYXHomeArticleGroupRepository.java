package com.kaiba.m.core.repository.appcyx;

import com.kaiba.m.core.domain.SeqBasedIdxFirstOrder;
import com.kaiba.m.core.domain.appcyx.CYXArticleGroup;
import com.kaiba.m.core.middleware.mongo.interfaces.KbMongoRepository;
import com.kaiba.m.core.middleware.mongo.utils.KbMongoUpdate;
import org.bson.types.ObjectId;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.core.BulkOperations;
import org.springframework.data.mongodb.core.FindAndModifyOptions;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.data.util.Pair;
import org.springframework.stereotype.Repository;

import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version CYXHomeArticleGroupRepository, v0.1 2025/6/23 11:18 daopei Exp $
 **/
@Repository
public interface CYXHomeArticleGroupRepository extends KbMongoRepository<CYXArticleGroup, String> {

    List<CYXArticleGroup> findByIdIn(Collection<String> ids);

    default CYXArticleGroup update(CYXArticleGroup group) {
        return mongo().findAndModify(
                new Query(Criteria.where("_id").is(group.getId())),
                new KbMongoUpdate()
                        .setIfNotNull("groupKey", group.getGroupKey())
                        .setIfNotNull("groupType", group.getGroupType())
                        .setIfNotNull("state", group.getState())
                        .setIfNotNull("style", group.getStyle())
                        .set("title", group.getTitle())
                        .set("showMore", group.getShowMore())
                        .set("updateTime", System.currentTimeMillis()),
                FindAndModifyOptions.options().returnNew(true).upsert(false),
                CYXArticleGroup.class
        );
    }

    default CYXArticleGroup updateState(String id, String state) {
        return mongo().findAndModify(
                new Query(Criteria.where("_id").is(id)),
                new Update()
                        .set("state", state)
                        .set("updateTime", System.currentTimeMillis()),
                FindAndModifyOptions.options().returnNew(true).upsert(false),
                CYXArticleGroup.class
        );
    }

    default void updateIdxBatch(List<SeqBasedIdxFirstOrder> list) {
        BulkOperations ops = mongo().bulkOps(BulkOperations.BulkMode.ORDERED, CYXArticleGroup.class);
        List<Pair<Query, Update>> updates = list.stream().
                map(m -> Pair.of(
                        new Query(Criteria.where("_id").is(new ObjectId(m.getId()))),
                        new Update()
                                .set("seq", m.getSeq())
                                .set("idx", m.getIdx())
                                .set("updateTime", System.currentTimeMillis())))
                .collect(Collectors.toList());
        ops.updateMulti(updates);
        ops.execute();
    }

    default Page<CYXArticleGroup> getByParam(String state, String groupType, Pageable pageable) {
        Criteria criteria = new Criteria();
        if (state != null) {
            criteria.and("state").is(state);
        }
        if (groupType != null) {
            criteria.and("groupType").is(groupType);
        }
        Query query = new Query(criteria);
        long count = mongo().count(query, CYXArticleGroup.class);
        List<CYXArticleGroup> list = mongo().find(query.with(pageable), CYXArticleGroup.class);
        return new PageImpl<>(list, pageable, count);
    }

}
