package com.kaiba.m.core.event.listener;

import com.kaiba.lib.base.domain.education.KidModel;
import com.kaiba.m.core.domain.education.recitation.Member;
import com.kaiba.m.core.event.KidGradeModifyEvent;
import com.kaiba.m.core.repository.education.recitation.MemberRepository;
import com.kaiba.m.core.service.education.EducationK12KidService;
import java.util.Optional;
import javax.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

/**
 * Description: 更新朗诵团儿童团员信息监听器
 * Author: ZM227
 * Date: 2024/8/6 17:17
 */
@Component
public class UpdateRecitationListener {

    @Resource
    private EducationK12KidService educationK12KidService;
    @Resource
    private MemberRepository memberRepository;

    @EventListener
    @Async
    public void handleKidGradeModifyEvent(KidGradeModifyEvent event) {
        String kid = event.getKid();
        if (StringUtils.isBlank(kid)) {
            return;
        }
        Optional<KidModel> kidModelOptional = educationK12KidService.getKidById(kid);
        if (!kidModelOptional.isPresent()) {
            return;
        }
        KidModel kidModel = kidModelOptional.get();
        Member member = new Member();
        member.setKid(kid);
        member.setSchoolId(kidModel.getSchoolId());
        member.setSchoolName(kidModel.getSchoolName());
        member.setGrade(kidModel.getGrade());
        memberRepository.updateSchoolByKid(member, kid);
    }

}
