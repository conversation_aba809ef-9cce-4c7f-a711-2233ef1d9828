package com.kaiba.m.core.event.listener;

import com.kaiba.m.core.event.KnowledgeModifyEvent;
import com.kaiba.m.core.service.news.pool.knowledge.IKnowledgeSyncOpenSearchService;
import javax.annotation.Resource;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

/**
 * Description: 知识点同步OpenSearch监听器
 * Author: ZM227
 * Date: 2025/2/10 10:23
 */
@Component
public class KnowledgeSyncOpenSearchListener {

    @Resource
    private IKnowledgeSyncOpenSearchService knowledgeSyncOpenSearchService;

    @EventListener
    @Async
    public void handleKnowledgeModifyEvent(KnowledgeModifyEvent event) {
        knowledgeSyncOpenSearchService.syncOpenSearch(event.getKnowledgeId());
    }

}
