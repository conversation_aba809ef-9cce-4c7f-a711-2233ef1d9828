package com.kaiba.m.core.event.publisher;

import com.kaiba.m.core.event.KidGradeModifyEvent;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.ApplicationEventPublisherAware;
import org.springframework.stereotype.Component;

/**
 * Description: 学生年级信息修改事件发布器
 * Author: ZM227
 * Date: 2024/8/6 17:10
 */
@Component
public class KidGradeModifyPublisher implements ApplicationEventPublisherAware {

    private ApplicationEventPublisher eventPublisher;

    @Override
    public void setApplicationEventPublisher(ApplicationEventPublisher applicationEventPublisher) {
        this.eventPublisher = applicationEventPublisher;
    }

    /**
     * 发布事件
     * @param kid kid
     */
    public void publishEvent(String kid) {
        KidGradeModifyEvent modifyEvent = new KidGradeModifyEvent(this, kid);
        eventPublisher.publishEvent(modifyEvent);
    }
}
