package com.kaiba.m.core.event;

import lombok.Getter;
import org.springframework.context.ApplicationEvent;

/**
 * Description: newsArticle变更事件
 * Author: ZM227
 * Date: 2025/1/13 16:37
 */
@Getter
public class NewsArticleModifyEvent extends ApplicationEvent {

    private final String articleId;

    private final ModifyType modifyType;

    /**
     * Create a new {@code ApplicationEvent}.
     *
     * @param source the object on which the event initially occurred or with
     *               which the event is associated (never {@code null})
     */
    public NewsArticleModifyEvent(Object source, String articleId, ModifyType modifyType) {
        super(source);
        this.articleId = articleId;
        this.modifyType = modifyType;
    }

    public enum ModifyType {
        // 目前只放这三个，后续有需求再加
        ADD,
        MODIFY,
        STATE_MODIFY
    }
}
