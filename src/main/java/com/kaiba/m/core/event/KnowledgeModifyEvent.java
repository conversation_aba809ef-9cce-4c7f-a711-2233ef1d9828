package com.kaiba.m.core.event;

import lombok.Getter;
import org.springframework.context.ApplicationEvent;

/**
 * Description: 知识点变更事件
 * Author: ZM227
 * Date: 2025/2/10 10:04
 */
@Getter
public class KnowledgeModifyEvent extends ApplicationEvent {

    private final String knowledgeId;

    /**
     * Create a new {@code ApplicationEvent}.
     *
     * @param source the object on which the event initially occurred or with
     *               which the event is associated (never {@code null})
     */
    public KnowledgeModifyEvent(Object source, String knowledgeId) {
        super(source);
        this.knowledgeId = knowledgeId;
    }
}
