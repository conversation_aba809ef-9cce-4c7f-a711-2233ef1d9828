package com.kaiba.m.fuse.model.media;

import com.kaiba.lib.base.constant.fuse.media.MediaPlatform;
import lombok.Data;

/**
 * 媒资转码配置
 * <AUTHOR>
 * @version MediaTranscodeTemplateConfig, v0.1 2024/10/10 14:02 daopei Exp $
 **/
@Data
public class MediaAssetTemplateConfig {


    /** 三方存储平台. {@link MediaPlatform} */
    private String storagePlatform;

    /** 模板名称 */
    private String name;

    /** 模板ID */
    private String templateId;


    public static MediaAssetTemplateConfig on() {
        return new MediaAssetTemplateConfig();
    }

    public MediaAssetTemplateConfig platform(String storagePlatform) {
        this.storagePlatform = storagePlatform;
        return this;
    }

    public MediaAssetTemplateConfig name(String name) {
        this.name = name;
        return this;
    }

    public MediaAssetTemplateConfig templateId(String templateId) {
        this.templateId = templateId;
        return this;
    }

}
