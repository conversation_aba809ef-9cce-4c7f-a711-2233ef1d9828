package com.kaiba.m.fuse.model.media;

import com.kaiba.lib.base.constant.fuse.media.MediaAssetPullUploadState;
import lombok.Data;

/**
 * 媒体资源拉取上传信息
 * <AUTHOR>
 * @version MediaAssetPullUploadInfo, v0.1 2024/10/14 10:59 daopei Exp $
 **/
@Data
public class MediaAssetPullUploadInfo {

    /** 三方平台媒资ID */
    private String storageId;

    /** 三方平台任务ID */
    private String taskId;

    /** 三方平台类型 */
    private String storagePlatform;

    /** 状态. {@link MediaAssetPullUploadState} */
    private String state;

    /** 处理进度. 取值范围[0-100], 100表示100% */
    private Integer progress;

    /** 原始文件网络地址 */
    private String sourceUrl;

    /** 结果可读信息 */
    private String resultMessage;

}
