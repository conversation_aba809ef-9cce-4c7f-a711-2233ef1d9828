package com.kaiba.m.fuse.model.media;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version MediaAssetPreUploadRequest, v0.1 2024/11/1 14:15 daopei Exp $
 **/
@Data
public class MediaAssetPreUploadRequest {

    /** 媒资ID */
    private String mediaId;

    /** 三方存储平台ID */
    private String storageId;

    /** 工作流. 可能ID或者名称,由存储平台使用决定 */
    private String workflow;

    /** 转码模版ID */
    private List<String> templateIds;

    public static MediaAssetPreUploadRequest on() {
        return new MediaAssetPreUploadRequest();
    }

    public MediaAssetPreUploadRequest mediaId(String mediaId) {
        this.mediaId = mediaId;
        return this;
    }

    public MediaAssetPreUploadRequest storageId(String storageId) {
        this.storageId = storageId;
        return this;
    }

    public MediaAssetPreUploadRequest workflow(String workflow) {
        this.workflow = workflow;
        return this;
    }

    public MediaAssetPreUploadRequest templateIds(List<String> templateIds) {
        this.templateIds = templateIds;
        return this;
    }
}
