package com.kaiba.m.fuse.service.media;

import com.kaiba.lib.base.constant.fuse.media.MediaPlatform;
import com.kaiba.lib.base.domain.fuse.media.MediaAssetTemplateConfig;
import com.kaiba.lib.base.domain.fuse.media.MediaAssetWorkflowConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 媒资配置服务
 * <AUTHOR>
 * @version MediaConfigService, v0.1 2024/10/10 14:05 daopei Exp $
 **/
@Slf4j
@Service
public class MediaConfigService {

    private List<MediaAssetWorkflowConfig> workflowConfigs;

    private List<MediaAssetTemplateConfig> templateConfigs;

    public MediaConfigService() {
        this.workflowConfigs = initWorkflow();
        this.templateConfigs = initTranscodeConfig();
    }


    public List<MediaAssetTemplateConfig> getTranscodeConfigs() {
        return templateConfigs;
    }

    public List<MediaAssetTemplateConfig> getTranscodeConfigs(MediaPlatform platform) {
        return templateConfigs.stream()
                .filter(config -> config.getStoragePlatform().equals(platform.name()))
                .collect(Collectors.toList());
    }

    public List<MediaAssetWorkflowConfig> getWorkflowConfig(MediaPlatform platform) {
        return workflowConfigs.stream()
                .filter(config -> config.getStoragePlatform().equals(platform.name()))
                .collect(Collectors.toList());
    }




    // -----------------------------------------------------

    private List<MediaAssetWorkflowConfig> initWorkflow() {
        List<MediaAssetWorkflowConfig> configs = new ArrayList<>();
        configs.add(MediaAssetWorkflowConfig.on()
                .storagePlatform(MediaPlatform.ALI.name())
                .name("默认工作流")
                .workflow("c501e685433b2a6b6ab1b013877453d4")
                .setDefault());
        configs.add(MediaAssetWorkflowConfig.on()
                .storagePlatform(MediaPlatform.TENCENT.name())
                .name("默认工作流(无加密自适应流)")
                .workflow("默认转码工作流")
                .setDefault());
        return configs;
    }

    private List<MediaAssetTemplateConfig> initTranscodeConfig() {
        List<MediaAssetTemplateConfig> configs = new ArrayList<>();
        //tencent
        configs.add(MediaAssetTemplateConfig.on()
                .platform(MediaPlatform.TENCENT.name())
                .name("流畅-H264-MP4")
                .templateId("100010"));
        configs.add(MediaAssetTemplateConfig.on()
                .platform(MediaPlatform.TENCENT.name())
                .name("流畅-H264-HLS")
                .templateId("100210"));
        configs.add(MediaAssetTemplateConfig.on()
                .platform(MediaPlatform.TENCENT.name())
                .name("标清-H264-MP4")
                .templateId("100020"));
        configs.add(MediaAssetTemplateConfig.on()
                .platform(MediaPlatform.TENCENT.name())
                .name("标清-H264-HLS")
                .templateId("100220"));
        configs.add(MediaAssetTemplateConfig.on()
                .platform(MediaPlatform.TENCENT.name())
                .name("高清-H264-MP4")
                .templateId("100030"));
        configs.add(MediaAssetTemplateConfig.on()
                .platform(MediaPlatform.TENCENT.name())
                .name("高清-H264-HLS")
                .templateId("100230"));
        configs.add(MediaAssetTemplateConfig.on()
                .platform(MediaPlatform.TENCENT.name())
                .name("超清-H264-MP4")
                .templateId("100040"));
        configs.add(MediaAssetTemplateConfig.on()
                .platform(MediaPlatform.TENCENT.name())
                .name("超清-H264-HLS")
                .templateId("100240"));

        //ali
        configs.add(MediaAssetTemplateConfig.on()
                .platform(MediaPlatform.ALI.name())
                .name("不转码")
                .templateId("60389e028631cbb0488c325606fb39a9"));
        configs.add(MediaAssetTemplateConfig.on()
                .platform(MediaPlatform.ALI.name())
                .name("青少高清")
                .templateId("3a420f5ed200556c72b23b47d60ad21e"));
        configs.add(MediaAssetTemplateConfig.on()
                .platform(MediaPlatform.ALI.name())
                .name("媒资管理通用模版")
                .templateId("75f4f31707b334f600d8f28b705c0043"));

        return configs;
    }




}
