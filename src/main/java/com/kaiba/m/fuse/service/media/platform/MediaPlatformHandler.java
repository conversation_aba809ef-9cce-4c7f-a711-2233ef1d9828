package com.kaiba.m.fuse.service.media.platform;

import com.kaiba.m.fuse.model.media.MediaAssetMeta;
import com.kaiba.m.fuse.model.media.MediaAssetPreUploadRequest;
import com.kaiba.m.fuse.model.media.MediaAssetPullUploadInfo;
import com.kaiba.m.fuse.domain.media.MediaAssetPlayInfo;
import com.kaiba.lib.base.domain.fuse.media.MediaAssetPreUploadResult;
import com.kaiba.m.fuse.model.media.MediaAssetPullUploadModel;

import java.util.List;

/**
 * 媒资三方渠道处理器
 * <AUTHOR>
 * @version MediaPlatformHandler, v0.1 2024/9/26 15:07 daopei Exp $
 **/
public interface MediaPlatformHandler {

    /** 支持的渠道 */
    boolean support(String platform);

    /** 文件上传创建 */
    MediaAssetPreUploadResult createByFileUpload(MediaAssetCreateHandlerRequest request);

    /** 删除 */
    void delete(String storageId);

    /** 信息更新 */
    void updateTitle(String storageId, String title);

    /** 预上传 */
    MediaAssetPreUploadResult preUpload(MediaAssetPreUploadRequest request);

    /** 转码 */
    void transcode(String storageId, List<String> templateIds);

    /** 获取播放列表 */
    List<MediaAssetPlayInfo> getPlayInfo(String storageId);

    /** 获取视频源数据 */
    MediaAssetMeta getVideoInfo(String storageId);

    /** 渠道回调解析 */
    MediaPlatformCallbackModel parseCallback(String body);

    /** 网络源拉取上传 */
    String pullUpload(MediaAssetPullUploadModel uploadModel);

    /** 获取网络拉取任务信息 */
    MediaAssetPullUploadInfo getPullUploadInfo(String taskId);


    /** 播放地址URL鉴权(KEY防盗). 本地执行无网络请求 */
    default String playAuth(String sourceUrl) {
        return sourceUrl;
    }

}
