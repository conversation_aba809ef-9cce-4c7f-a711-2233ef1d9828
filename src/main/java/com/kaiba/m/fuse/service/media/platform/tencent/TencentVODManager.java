package com.kaiba.m.fuse.service.media.platform.tencent;

import com.kaiba.lib.base.response.KbCode;
import com.kaiba.lib.base.response.KbException;
import com.kaiba.lib.base.util.GsonUtils;
import com.tencentcloudapi.common.Credential;
import com.tencentcloudapi.common.exception.TencentCloudSDKException;
import com.tencentcloudapi.common.profile.ClientProfile;
import com.tencentcloudapi.common.profile.HttpProfile;
import com.tencentcloudapi.vod.v20180717.VodClient;
import com.tencentcloudapi.vod.v20180717.models.DeleteMediaRequest;
import com.tencentcloudapi.vod.v20180717.models.DeleteMediaResponse;
import com.tencentcloudapi.vod.v20180717.models.DescribeMediaInfosRequest;
import com.tencentcloudapi.vod.v20180717.models.DescribeMediaInfosResponse;
import com.tencentcloudapi.vod.v20180717.models.DescribeTaskDetailRequest;
import com.tencentcloudapi.vod.v20180717.models.DescribeTaskDetailResponse;
import com.tencentcloudapi.vod.v20180717.models.MediaProcessTaskInput;
import com.tencentcloudapi.vod.v20180717.models.ModifyMediaInfoRequest;
import com.tencentcloudapi.vod.v20180717.models.ModifyMediaInfoResponse;
import com.tencentcloudapi.vod.v20180717.models.ProcessMediaRequest;
import com.tencentcloudapi.vod.v20180717.models.ProcessMediaResponse;
import com.tencentcloudapi.vod.v20180717.models.PullUploadRequest;
import com.tencentcloudapi.vod.v20180717.models.PullUploadResponse;
import com.tencentcloudapi.vod.v20180717.models.TranscodeTaskInput;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import sun.misc.BASE64Encoder;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.util.Arrays;
import java.util.List;
import java.util.Random;
import java.util.concurrent.Callable;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version TencentVODManager, v0.1 2024/9/27 14:16 daopei Exp $
 **/
@Slf4j
@Service
public class TencentVODManager {

    @Value("${vod.tencent.appId:1500034464}")
    private Long SUB_APP_ID;

    // 腾讯云用户密钥(用户名:vod-user)
    private static final String TENCENT_SECRET_ID = "AKIDCF5innbLxOcbMuEIBrNj5E9JH2axOiqo";
    private static final String TENCENT_SECRET_KEY = "ra6hPyKRJxGQN9h6JMHzTn023tb8SeeG";

    private static final String HMAC_ALGORITHM = "HmacSHA1"; //签名算法
    private static final String CONTENT_CHARSET = "UTF-8";


    private final VodClient vodClient;


    public TencentVODManager(
    ) {
        Credential cred = new Credential(TENCENT_SECRET_ID, TENCENT_SECRET_KEY);
        HttpProfile httpProfile = new HttpProfile();
        httpProfile.setEndpoint("vod.ap-shanghai.tencentcloudapi.com");
        ClientProfile clientProfile = new ClientProfile();
        clientProfile.setHttpProfile(httpProfile);
        this.vodClient = new VodClient(cred, "ap-shanghai", clientProfile);
    }

    public String getUploadSign(String mediaId, String workflow) {
        try {
            return getUploadSignature(mediaId, workflow);
        } catch (Exception e) {
            log.error("tencent upload sign error : {}", e.getMessage());
            throw new KbException(KbCode.REQUEST_FAIL).r("获取上传签名错误, 联系管理员查看");
        }
    }


    public ModifyMediaInfoResponse updateMedia(String fileId, String fileName) {
        ModifyMediaInfoRequest request = new ModifyMediaInfoRequest();
        request.setFileId(fileId);
        request.setName(fileName);
        request.setSubAppId(SUB_APP_ID);
        return requestExecute(() -> vodClient.ModifyMediaInfo(request));
    }

    public DeleteMediaResponse deleteMedia(String fileId) {
        DeleteMediaRequest request = new DeleteMediaRequest();
        request.setFileId(fileId);
        request.setSubAppId(SUB_APP_ID);
        return requestExecute(() -> vodClient.DeleteMedia(request));
    }


    public DescribeMediaInfosResponse getVideoInfo(String fileId) {
        log.debug("vod tencent getVideoInfo fileId:{}", fileId);
        DescribeMediaInfosRequest request = new DescribeMediaInfosRequest();
        request.setFileIds(new String[]{fileId});
        request.setSubAppId(SUB_APP_ID);
        DescribeMediaInfosResponse response = requestExecute(() -> vodClient.DescribeMediaInfos(request));
        log.debug("vod tencent getVideoInfo response:{}", GsonUtils.getGson().toJson(response));
        return response;
    }

    public ProcessMediaResponse transcode(String fileId, List<String> templateIds) {

        MediaProcessTaskInput taskInput = new MediaProcessTaskInput();
        taskInput.setTranscodeTaskSet(templateIds.stream().map(templateId -> {
            TranscodeTaskInput transcodeT = new TranscodeTaskInput();
            transcodeT.setDefinition(Long.parseLong(templateId));
            return transcodeT;
        }).toArray(TranscodeTaskInput[]::new));
        ProcessMediaRequest request = new ProcessMediaRequest();
        request.setFileId(fileId);
        request.setMediaProcessTask(taskInput);
        request.setSubAppId(SUB_APP_ID);
        return requestExecute(() -> vodClient.ProcessMedia(request));
    }


    public String pullUpload(String sourceUrl, String title, String mediaId, String workflow) {
        PullUploadRequest request = new PullUploadRequest();
        request.setMediaUrl(sourceUrl);
        request.setMediaName(title);
        request.setSourceContext(mediaId);
        request.setSubAppId(SUB_APP_ID);
        request.setProcedure(workflow);
        PullUploadResponse response = requestExecute(() -> vodClient.PullUpload(request));
        return response.getTaskId();
    }

    public DescribeTaskDetailResponse getTaskInfo(String taskId) {
        DescribeTaskDetailRequest request = new DescribeTaskDetailRequest();
        request.setTaskId(taskId);
        request.setSubAppId(SUB_APP_ID);
        return requestExecute(() -> vodClient.DescribeTaskDetail(request));
    }


    // ----------------------------------------
    private <T> T requestExecute(Callable<T> callable){
        try {
            return callable.call();
        } catch (TencentCloudSDKException e) {
            log.error("vod tencent error:{}", GsonUtils.getGson().toJson(e));
            throw new KbException(KbCode.THIRD_PARTY_REQUEST_FAIL)
                    .r("请求失败, 联系管理员");
        } catch (Exception e) {
            log.error("vod tencent base error:{}", GsonUtils.getGson().toJson(e));
            // 捕获其他类型的异常，并转换为KbException
            throw new KbException(KbCode.THIRD_PARTY_REQUEST_FAIL)
                    .r("请求失败, 联系管理员");
        }
    }



    // -------------------------

    // 获取文件上传所需签名
    private String getUploadSignature(String mediaId, String workflow) throws Exception {
        String strSign = "";
        String contextStr = "";
        long currentTime = System.currentTimeMillis() / 1000;
        int random = new Random().nextInt(Integer.MAX_VALUE);
        int signValidDuration = 3600 * 12; // 有效期12个小时, 保证一些特大文件的上传
        // 生成原始参数字符串
        long endTime = (currentTime + signValidDuration);
        contextStr += "secretId=" + java.net.URLEncoder.encode(TENCENT_SECRET_ID, "utf8");
        contextStr += "&currentTimeStamp=" + currentTime;
        contextStr += "&expireTime=" + endTime;
        contextStr += "&random=" + random;
        contextStr += "&vodSubAppId=" + SUB_APP_ID;
        contextStr += "&sourceContext=" + mediaId;
        if (workflow != null) {
            contextStr += "&procedure=" + workflow;
        }



        Mac mac = Mac.getInstance(HMAC_ALGORITHM);
        SecretKeySpec secretKey = new SecretKeySpec(TENCENT_SECRET_KEY.getBytes(CONTENT_CHARSET), mac.getAlgorithm());
        mac.init(secretKey);


        byte[] hash = mac.doFinal(contextStr.getBytes(CONTENT_CHARSET));
        byte[] sigBuf = byteMerger(hash, contextStr.getBytes("utf8"));
        strSign = base64Encode(sigBuf);
        strSign = strSign.replace(" ", "").replace("\n", "").replace("\r", "");
        return strSign;
    }


    public static byte[] byteMerger(byte[] byte1, byte[] byte2) {
        byte[] byte3 = new byte[byte1.length + byte2.length];
        System.arraycopy(byte1, 0, byte3, 0, byte1.length);
        System.arraycopy(byte2, 0, byte3, byte1.length, byte2.length);
        return byte3;
    }

    private String base64Encode(byte[] buffer) {
        BASE64Encoder encoder = new BASE64Encoder();
        return encoder.encode(buffer);
    }

}
