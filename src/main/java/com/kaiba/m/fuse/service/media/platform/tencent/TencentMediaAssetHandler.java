package com.kaiba.m.fuse.service.media.platform.tencent;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.kaiba.lib.base.constant.fuse.media.MediaAssetCallbackEventNode;
import com.kaiba.lib.base.constant.fuse.media.MediaAssetFormat;
import com.kaiba.lib.base.constant.fuse.media.MediaAssetState;
import com.kaiba.lib.base.constant.fuse.media.MediaAssetStorageClass;
import com.kaiba.lib.base.util.GsonUtils;
import com.kaiba.lib.base.util.StringUtils;
import com.kaiba.m.fuse.model.media.MediaAssetMeta;
import com.kaiba.m.fuse.model.media.MediaAssetPreUploadRequest;
import com.kaiba.m.fuse.model.media.MediaAssetPullUploadInfo;
import com.kaiba.lib.base.constant.fuse.media.MediaAssetPullUploadState;
import com.kaiba.lib.base.constant.fuse.media.MediaPlatform;
import com.kaiba.m.fuse.domain.media.MediaAssetPlayInfo;
import com.kaiba.lib.base.constant.fuse.media.MediaAssetDefinition;
import com.kaiba.lib.base.domain.fuse.media.MediaAssetPreUploadResult;
import com.kaiba.m.fuse.model.media.MediaAssetPullUploadModel;
import com.kaiba.lib.base.domain.fuse.media.MediaAssetTencentPreUpload;
import com.kaiba.m.fuse.service.media.platform.MediaPlatformHandler;
import com.kaiba.m.fuse.service.media.platform.MediaAssetCreateHandlerRequest;
import com.kaiba.m.fuse.service.media.platform.MediaPlatformCallbackModel;
import com.kaiba.m.fuse.service.media.util.MediaAssetHelper;
import com.kaiba.m.fuse.service.media.util.MediaStringUtils;
import com.tencentcloudapi.vod.v20180717.models.DescribeMediaInfosResponse;
import com.tencentcloudapi.vod.v20180717.models.DescribeTaskDetailResponse;
import com.tencentcloudapi.vod.v20180717.models.MediaBasicInfo;
import com.tencentcloudapi.vod.v20180717.models.MediaInfo;
import com.tencentcloudapi.vod.v20180717.models.MediaMetaData;
import com.tencentcloudapi.vod.v20180717.models.MediaTranscodeItem;
import com.tencentcloudapi.vod.v20180717.models.PullUploadTask;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 *
 * 腾讯云点播服务
 *
 * <AUTHOR>
 * @version TencentMediaAssetHandler, v0.1 2024/9/27 16:13 daopei Exp $
 **/
@Slf4j
@Service
public class TencentMediaAssetHandler implements MediaPlatformHandler {

    /** 签名密钥 */
    private static final String URL_AUTH_KEY = "NNwznuJM2XshZ2WjLT7r";
    /** 签名对于域名 */
    private static final String URL_AUTH_DOMAIN = "vod-qc.kaiba315.com.cn";
    /** URL鉴权默认有效时间(秒). 1天 */
    private static final Long URL_AUTH_DEFAULT_TIME = 86400L;


    private final static String PLATFORM = MediaPlatform.TENCENT.name();
    private final TencentVODManager vodManager;

    public TencentMediaAssetHandler(TencentVODManager vodManager) {
        this.vodManager = vodManager;
    }

    @Override
    public boolean support(String platform) {
        return PLATFORM.equals(platform);
    }

    @Override
    public MediaAssetPreUploadResult createByFileUpload(MediaAssetCreateHandlerRequest request) {
        String sign = vodManager.getUploadSign(request.getMediaId(), request.getWorkflow());
        return MediaAssetPreUploadResult.asTencentUpload(PLATFORM, MediaAssetTencentPreUpload.as(sign));
    }

    @Override
    public void delete(String storageId) {
        vodManager.deleteMedia(storageId);
    }

    @Override
    public void updateTitle(String storageId, String title) {
        vodManager.updateMedia(storageId, title);
    }

    @Override
    public MediaAssetPreUploadResult preUpload(MediaAssetPreUploadRequest request) {
        String sign = vodManager.getUploadSign(request.getMediaId(), request.getWorkflow());
        return MediaAssetPreUploadResult.asTencentUpload(PLATFORM, MediaAssetTencentPreUpload.as(sign));
    }

    @Override
    public void transcode(String storageId, List<String> templateIds) {
        vodManager.transcode(storageId, templateIds);
    }

    @Override
    public List<MediaAssetPlayInfo> getPlayInfo(String storageId) {
        DescribeMediaInfosResponse response = vodManager.getVideoInfo(storageId);
        MediaInfo[] mediaInfos = response.getMediaInfoSet();
        if (mediaInfos == null || mediaInfos.length == 0) {
            return Collections.emptyList();
        }
        MediaInfo mediaMain = mediaInfos[0];
        MediaTranscodeItem[] transcodeItems = mediaMain.getTranscodeInfo().getTranscodeSet();
        if (transcodeItems == null || transcodeItems.length == 0) {
            return Collections.emptyList();
        }
        return Arrays.stream(transcodeItems)
                .map(TencentMediaAssetHandler::map2PlayInfo)
                .collect(Collectors.toList());
    }

    @Override
    public MediaAssetMeta getVideoInfo(String storageId) {
        DescribeMediaInfosResponse response = vodManager.getVideoInfo(storageId);
        MediaInfo[] mediaInfos = response.getMediaInfoSet();
        if (mediaInfos == null || mediaInfos.length == 0) {
            return null;
        }
        MediaInfo mediaMain = mediaInfos[0];
        return map2Meta(mediaMain);
    }


    @Override
    public MediaPlatformCallbackModel parseCallback(String body) {
        MediaPlatformCallbackModel result = convertCallback(body);
        if (result.getStorageId() == null) {
            return result;
        }
        //回填媒资ID
        DescribeMediaInfosResponse response = vodManager.getVideoInfo(result.getStorageId());
        MediaInfo[] mediaInfos = response.getMediaInfoSet();
        if (mediaInfos == null || mediaInfos.length == 0) {
            return result;
        }
        MediaInfo mediaMain = mediaInfos[0];
        if (mediaMain.getBasicInfo() != null && mediaMain.getBasicInfo().getSourceInfo() != null) {
            result.setMediaId(mediaMain.getBasicInfo().getSourceInfo().getSourceContext());
        }
        return result;
    }

    @Override
    public String pullUpload(MediaAssetPullUploadModel uploadModel) {
        return vodManager.pullUpload(uploadModel.getSourceUrl(), uploadModel.getTitle(), uploadModel.getMediaId(), uploadModel.getWorkflow());
    }

    @Override
    public MediaAssetPullUploadInfo getPullUploadInfo(String taskId) {
        DescribeTaskDetailResponse response = vodManager.getTaskInfo(taskId);
        return map2PullUploadInfo(response);
    }

    @Override
    public String playAuth(String sourceUrl) {
        if (!StringUtils.isValidUrl(sourceUrl)) {
            // 无法处理则默认返回原始值,不报错
            log.warn("sourceUrl sign fail in tencent. url not valid:{}", sourceUrl);
            return MediaPlatformHandler.super.playAuth(sourceUrl);
        }

        return signUrl(sourceUrl);
    }

    // -------------------------------

    private static MediaAssetPlayInfo map2PlayInfo(MediaTranscodeItem source) {
        MediaAssetPlayInfo playInfo = new MediaAssetPlayInfo();
        playInfo.setDefinition(map2Definition(source).name());
        playInfo.setEncrypt(false);
        playInfo.setHeightPixel(source.getHeight());
        playInfo.setWidthPixel(source.getWidth());
        playInfo.setSize(source.getSize());
        playInfo.setVideoUrl(source.getUrl());
        playInfo.setBitrate(String.valueOf((source.getBitrate() / 1024)));
        playInfo.setFormat(map2Format(source.getContainer()));
        MediaAssetHelper.calculateRatio(playInfo);
        return playInfo;
    }

    private static MediaAssetMeta map2Meta(MediaInfo source) {
        MediaBasicInfo basicInfo = source.getBasicInfo();
        MediaMetaData metaInfo = source.getMetaData();
        MediaAssetMeta meta = new MediaAssetMeta();
        meta.setStorageId(source.getFileId());
        if (basicInfo != null) {
            meta.setCoverUrl(basicInfo.getCoverUrl());
            meta.setVideoUrl(basicInfo.getMediaUrl());
            meta.setState(map2State(basicInfo.getStatus()).name());
            meta.setStorageClass(map2StorageClass(basicInfo.getStorageClass()).name());
            if (basicInfo.getSourceInfo() != null) {
                meta.setSourceInfoType(basicInfo.getSourceInfo().getSourceType());
            }
        }
        if (metaInfo != null) {
            meta.setDuration(metaInfo.getDuration().toString());
            meta.setSize(metaInfo.getSize());
            meta.setHeightPixel(metaInfo.getHeight());
            meta.setWidthPixel(metaInfo.getWidth());
            meta.setBitrate(convertToKb(metaInfo.getBitrate()));
            MediaAssetHelper.calculateRatio(meta);
        }
        return meta;
    }

    private static MediaAssetPullUploadInfo map2PullUploadInfo(DescribeTaskDetailResponse response) {
        if (response == null) {
            return null;
        }
        PullUploadTask pullUploadTask = response.getPullUploadTask();
        if (pullUploadTask == null) {
            return null;
        }
        MediaAssetPullUploadInfo task = new MediaAssetPullUploadInfo();
        task.setTaskId(pullUploadTask.getTaskId());
        task.setSourceUrl(null);
        task.setStoragePlatform(MediaPlatform.TENCENT.name());
        task.setState(map2PullUploadState(pullUploadTask).name());
        task.setResultMessage(pullUploadTask.getMessage());
        task.setStorageId(pullUploadTask.getFileId());
        //设置上传进度
        if (pullUploadTask.getProgress() == null) {
            task.setProgress(0);
        } else {
            task.setProgress(pullUploadTask.getProgress().intValue());
        }
        return task;
    }


    /**
     * 腾讯云API未有清晰度的定义, 只能通过分辨率定义. 标准参照官方文档中默认的转码模版说明配置
     * 参见[转码参数模板](https://cloud.tencent.com/document/product/266/33476)。
     *
     * 按照分辨率高度定义清晰度:
     * 源文件 : OD (definition = 0)
     * 取最小值的边计算. 腾讯云转码规律是短边缩放到指定值, 长边按比例缩放
     * FD : <= 360
     * SD : (360, 540]
     * HD : (540, 720]
     * FHD : (720, 1080]
     * 2K : (1080, 1440]
     * 4K : (1440, 2160]
     * @param source
     * @return
     */
    private static MediaAssetDefinition map2Definition(MediaTranscodeItem source) {
        if (source.getDefinition() == 0) {
            return MediaAssetDefinition.OD;
        }
        Long shortSide = Math.min(source.getWidth(), source.getHeight());
        if (shortSide <= 360) {
            return MediaAssetDefinition.FD;
        }
        if (shortSide <= 540) {
            return MediaAssetDefinition.SD;
        }
        if (shortSide <= 720) {
            return MediaAssetDefinition.HD;
        }
        if (shortSide <= 1080) {
            return MediaAssetDefinition.FHD;
        }
        if (shortSide <= 1440) {
            return MediaAssetDefinition.K2;
        }
        return MediaAssetDefinition.K4;
    }


    private static MediaPlatformCallbackModel convertCallback(String body) {
        JSONObject jsonObject = GsonUtils.getGson().fromJson(body, JSONObject.class);
        String EventType = jsonObject.getString("EventType");
        MediaPlatformCallbackModel.MediaPlatformCallbackModelBuilder builder =
                MediaPlatformCallbackModel.builder();
        switch (EventType) {
            case "NewFileUpload":
                JSONObject fileUpload = jsonObject.getJSONObject("FileUploadEvent");
                builder.storageId(empty2Null(fileUpload.getString("FileId")));
                builder.eventNode(MediaAssetCallbackEventNode.UPLOAD_FINISH);
                return builder.build();
            case "ProcedureStateChanged":
                JSONObject procedureState = jsonObject.getJSONObject("ProcedureStateChangeEvent");
                builder.storageId(empty2Null(procedureState.getString("FileId")));
                builder.eventNode(MediaAssetCallbackEventNode.TRANSCODE_FINISH);
                return builder.build();
            case "PullComplete":
                JSONObject pullComplete = jsonObject.getJSONObject("PullCompleteEvent");
                builder.storageId(empty2Null(pullComplete.getString("FileId")));
                builder.taskId(empty2Null(pullComplete.getString("TaskId")));
                builder.eventNode(MediaAssetCallbackEventNode.PULL_UPLOAD_FINISH);
                return builder.build();
            case "FileDeleted":
                JSONObject fileDeleted = jsonObject.getJSONObject("FileDeleteEvent");
                JSONArray idSet = fileDeleted.getJSONArray("FileIdSet");
                builder.storageId(empty2Null(idSet.getString(0)));
                builder.eventNode(MediaAssetCallbackEventNode.DELETE_FINISH);
                return builder.build();
            default:
                builder.eventNode(MediaAssetCallbackEventNode.OTHER);
                return builder.build();
        }
    }

    private static MediaAssetPullUploadState map2PullUploadState(PullUploadTask task) {
        if ("PROCESSING".equals(task.getStatus())) {
            return MediaAssetPullUploadState.DOING;
        }
        if (task.getErrCode() == 0L) {
            return MediaAssetPullUploadState.SUCCESS;
        }
        return MediaAssetPullUploadState.FAIL;
    }


    private static MediaAssetState map2State(String sourceStatus) {
        if (sourceStatus == null) {
            return MediaAssetState.EXCEPTION;
        }
        switch (sourceStatus) {
            case "Normal":
                return MediaAssetState.NORMAL;
            case "Forbidden":
                return MediaAssetState.BLOCKED;
            default:
                return MediaAssetState.EXCEPTION;
        }
    }

    private static MediaAssetStorageClass map2StorageClass(String sourceStorageClass) {
        if (sourceStorageClass == null) {
            return MediaAssetStorageClass.OTHER;
        }
        switch (sourceStorageClass) {
            case "STANDARD":
                return MediaAssetStorageClass.STANDARD;
            case "STANDARD_IA":
                return MediaAssetStorageClass.IA;
            case "ARCHIVE":
                return MediaAssetStorageClass.ARCHIVE;
            case "DEEP_ARCHIVE":
                return MediaAssetStorageClass.COLD_ARCHIVE;
            default:
                return MediaAssetStorageClass.OTHER;
        }
    }

    /**
     * 腾讯转码后的视频格式转换为通用格式
     * @param container
     * @return
     */
    private static String map2Format(String container) {
        if (container == null) {
            return MediaAssetFormat.MP4.name();
        }
        //腾讯侧数据样式:mov,mp4,m4a,3gp,3g2,mj2
        if (StringUtils.containsIgnoreCase(container, "mp4")) {
            return MediaAssetFormat.MP4.name();
        }
        //腾讯侧数据样式:hls
        if (StringUtils.containsIgnoreCase(container, "hls")) {
            return MediaAssetFormat.HLS.name();
        }
        //腾讯侧数据样式:flv
        if (StringUtils.containsIgnoreCase(container, "flv")) {
            return MediaAssetFormat.FLV.name();
        }
        return MediaAssetFormat.MP4.name();
    }


    /**
     * URL签名. 默认仅使用过期时间的属性
     *
     * 说明:
     *  URL签名鉴权仅在指定的域名下生效, KEY和DOMAIN都是成对出现
     * @param sourceUrl
     * @return
     */
    private String signUrl(String sourceUrl) {
        String path = MediaStringUtils.getPath(sourceUrl);

        String tHex = Long.toHexString(System.currentTimeMillis()/1000 + URL_AUTH_DEFAULT_TIME);
        String sign = StringUtils.toMd5(URL_AUTH_KEY + path + tHex);

        return "https://" + URL_AUTH_DOMAIN + path + "?t=" + tHex + "&sign=" + sign;
    }

    public static String convertToKb(long i) {
        BigDecimal b = new BigDecimal(i);
        return b.divide(new BigDecimal(1000), 2, RoundingMode.HALF_UP).toString();
    }

    public static String empty2Null(String str) {
        if (StringUtils.isEmpty(str)) {
            return null;
        }
        return str;
    }
}
