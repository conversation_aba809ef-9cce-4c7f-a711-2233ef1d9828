package com.kaiba.m.fuse.service.media.platform;

import com.kaiba.lib.base.constant.fuse.media.MediaPlatform;
import org.springframework.stereotype.Service;

/**
 * 本地存储处理
 * <AUTHOR>
 * @version MediaPlatformLocalHandler, v0.1 2024/10/23 15:02 daopei Exp $
 **/
@Service
public class MediaPlatformLocalHandler extends MediaPlatformAbstractHandler{

    private final static String PLATFORM = MediaPlatform.LOCAL.name();


    @Override
    public boolean support(String platform) {
        return PLATFORM.equals(platform);
    }
}
