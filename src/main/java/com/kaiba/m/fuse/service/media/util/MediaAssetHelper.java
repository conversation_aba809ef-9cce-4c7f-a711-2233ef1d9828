package com.kaiba.m.fuse.service.media.util;

import com.kaiba.lib.base.constant.fuse.media.MediaAssetCallbackRegisterBy;
import com.kaiba.lib.base.constant.fuse.media.MediaAssetOrigin;
import com.kaiba.lib.base.domain.fuse.media.MediaAssetCallbackCreateModel;
import com.kaiba.lib.base.lang.verifier.F;
import com.kaiba.lib.base.lang.verifier.FieldGetter;
import com.kaiba.lib.base.lang.verifier.V;
import com.kaiba.lib.base.lang.verifier.Verifier;
import com.kaiba.lib.base.lang.verifier.VerifierBuilder;
import com.kaiba.lib.base.domain.fuse.media.MediaAssetCreateModel;
import com.kaiba.m.fuse.domain.media.MediaAssetPlayInfo;
import com.kaiba.m.fuse.model.media.MediaAssetMeta;

/**
 * <AUTHOR>
 * @version MediaAssetHelper, v0.1 2024/10/16 14:16 daopei Exp $
 **/
public class MediaAssetHelper {

    public static final Verifier<MediaAssetCallbackCreateModel> CALLBACK_VERIFY = new VerifierBuilder<MediaAssetCallbackCreateModel>().defaultOrElseThrow()
            .addVerifier(V.or(
                    V.and(
                            V.or(
                                    F.str(MediaAssetCallbackCreateModel::getRegisterBy).isNull(),
                                    F.str(MediaAssetCallbackCreateModel::getRegisterBy).eq(MediaAssetCallbackRegisterBy.BY_URL.name())
                            ),
                            F.str(MediaAssetCallbackCreateModel::getCallbackUrl).isUrl().r("当注册方式为空或BY_URL时，回调URL不可为空")
                    ),
                    V.and(
                            F.str(MediaAssetCallbackCreateModel::getRegisterBy).eq(MediaAssetCallbackRegisterBy.BY_CONF_KEY.name()),
                            F.str(MediaAssetCallbackCreateModel::getCallbackKey).notEmpty().r("当注册方式为BY_CONFIG时，回调Key不可为空")
                    )
            ))
            .create();

    public static final Verifier<MediaAssetCreateModel> PRE_VERIFY = new VerifierBuilder<MediaAssetCreateModel>().defaultOrElseThrow()
            .and(F.str(MediaAssetCreateModel::getStoragePlatform).notEmpty())
            .and(F.str(MediaAssetCreateModel::getTitle).notEmpty())
            .and(F.str(MediaAssetCreateModel::getOrigin).in(MediaAssetOrigin.names()))
            .ifTrue(m -> m.getCallback() != null, V.bean(MediaAssetCreateModel::getCallback, CALLBACK_VERIFY))
            .ifTrue(m -> m.getOrigin().equals(MediaAssetOrigin.FILE_UPLOAD.name()), F.obj(MediaAssetCreateModel::getFileUpload).notNull())
            .ifTrue(m -> m.getOrigin().equals(MediaAssetOrigin.PULL_UPLOAD.name()), F.obj(MediaAssetCreateModel::getPullUpload).notNull())
            .ifTrue(m -> m.getOrigin().equals(MediaAssetOrigin.AS_IS.name()), F.obj(MediaAssetCreateModel::getAsisReq).notNull())
            .create();

    public static final Verifier<MediaAssetCreateModel.FileUploadReq> FILE_UPLOAD_VERIFY = new VerifierBuilder<MediaAssetCreateModel.FileUploadReq>().defaultOrElseThrow()
            .and(F.str(MediaAssetCreateModel.FileUploadReq::getFileName).notEmpty())
            .and(F.longF(MediaAssetCreateModel.FileUploadReq::getSize).notEmpty())
            .create();

    public static final Verifier<MediaAssetCreateModel.PullUploadReq> PULL_UPLOAD_VERIFY = new VerifierBuilder<MediaAssetCreateModel.PullUploadReq>().defaultOrElseThrow()
            .and(F.str(MediaAssetCreateModel.PullUploadReq::getSourceUrl).notEmpty())
            .create();

    public static final Verifier<MediaAssetCreateModel.ASISReq> AS_IS_VERIFY = new VerifierBuilder<MediaAssetCreateModel.ASISReq>().defaultOrElseThrow()
            .and(F.str(MediaAssetCreateModel.ASISReq::getFormat).notEmpty())
            .and(F.str(MediaAssetCreateModel.ASISReq::getSourceUrl).notEmpty())
            .and(F.longF(MediaAssetCreateModel.ASISReq::getHeight).notEmpty())
            .and(F.longF(MediaAssetCreateModel.ASISReq::getWidth).notEmpty())
            .create();


    /**
     * 计算宽高比
     * @param playInfo
     */
    public static void calculateRatio(MediaAssetPlayInfo playInfo) {
        if (playInfo.getWidthPixel() == null || playInfo.getWidthPixel() == 0L) {
            playInfo.setWidthPixel(1L);
        }
        if (playInfo.getHeightPixel() == null || playInfo.getHeightPixel() == 0L) {
            playInfo.setHeightPixel(1L);
        }
        long gcd = getGCD(playInfo.getWidthPixel(), playInfo.getHeightPixel());
        playInfo.setWidth(Math.abs(playInfo.getWidthPixel() / gcd));
        playInfo.setHeight(Math.abs(playInfo.getHeightPixel() / gcd));
    }

    /**
     * 计算宽高比
     * @param meta
     */
    public static void calculateRatio(MediaAssetMeta meta) {
        if (meta.getWidthPixel() == null || meta.getWidthPixel() == 0L) {
            meta.setWidthPixel(1L);
        }
        if (meta.getHeightPixel() == null || meta.getHeightPixel() == 0L) {
            meta.setHeightPixel(1L);
        }
        long gcd = getGCD(meta.getWidthPixel(), meta.getHeightPixel());
        meta.setWidth(Math.abs(meta.getWidthPixel() / gcd));
        meta.setHeight(Math.abs(meta.getHeightPixel() / gcd));
    }


    // 计算最大公约数的辅助方法(使用辗转相除法)
    private static long getGCD(long a, long b) {
        a = Math.abs(a);
        b = Math.abs(b);
        while (b > 0) {
            long temp = b;
            b = a % b;
            a = temp;
        }
        return a;
    }
}
