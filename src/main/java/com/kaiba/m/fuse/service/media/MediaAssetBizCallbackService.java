package com.kaiba.m.fuse.service.media;

import com.kaiba.lib.base.constant.fuse.media.MediaAssetCallbackRegisterBy;
import com.kaiba.lib.base.domain.fuse.media.MediaAssetCallbackBizNotifyModel;
import com.kaiba.lib.base.domain.fuse.media.MediaAssetCallbackCreateModel;
import com.kaiba.lib.base.domain.fuse.media.MediaAssetPlayInfoModel;
import com.kaiba.lib.base.response.KbCode;
import com.kaiba.lib.base.response.KbException;
import com.kaiba.lib.base.util.GsonUtils;
import com.kaiba.m.fuse.constant.media.MediaAssetCallbackConf;
import com.kaiba.m.fuse.domain.media.MediaAsset;
import com.kaiba.m.fuse.domain.media.MediaAssetCallbackContext;
import com.kaiba.m.fuse.domain.media.MediaAssetPlayInfo;
import com.kaiba.m.fuse.repository.media.MediaAssetCallbackRepository;
import com.kaiba.m.fuse.repository.media.MediaAssetPlayInfoRepository;
import com.kaiba.m.fuse.repository.media.MediaAssetRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.retry.annotation.Backoff;
import org.springframework.retry.annotation.Retryable;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.List;
import java.util.UUID;

/**
 * 媒资业务回调服务
 *
 * <AUTHOR>
 * @version MediaAssetBizCallbackService, v0.1 2024/12/2 11:21 daopei Exp $
 **/
@Slf4j
@Service
public class MediaAssetBizCallbackService {

    private final MediaAssetRepository assetRepository;
    private final MediaAssetPlayInfoRepository playInfoRepository;
    private final MediaAssetCallbackRepository callbackRepository;
    private final RestTemplate restTemplate;
    private final MediaAssetPlayService playService;


    public MediaAssetBizCallbackService(
            MediaAssetRepository assetRepository,
            MediaAssetPlayInfoRepository playInfoRepository,
            MediaAssetCallbackRepository callbackRepository,
            RestTemplate restTemplate,
            MediaAssetPlayService playService
    ) {
        this.assetRepository = assetRepository;
        this.playInfoRepository = playInfoRepository;
        this.callbackRepository = callbackRepository;
        this.restTemplate = restTemplate;
        this.playService = playService;

    }

    public MediaAssetCallbackContext upsert(MediaAssetCallbackCreateModel callback, String mediaId) {
        if (callback == null) {
            return null;
        }
        callback.setMediaId(mediaId);
        return upsert(callback);
    }


    public MediaAssetCallbackContext upsert(MediaAssetCallbackCreateModel callback) {
        if (callback == null) {
            return null;
        }
        if (callback.getMediaId() == null) {
            throw new KbException(KbCode.ILLEGAL_ARGUMENT).setReadableMessage("mediaId can not be null").li();
        }
        String callbackUrl = mapCallbackUrl(callback);
        if (callbackUrl == null) {
            throw new KbException(KbCode.ILLEGAL_ARGUMENT).setReadableMessage("callbackUrl can not be null").li();
        }

        MediaAssetCallbackContext callbackContext = new MediaAssetCallbackContext();
        callbackContext.setMediaId(callback.getMediaId());
        callbackContext.setCallbackUrl(callbackUrl);
        callbackContext.setCallbackEventNode(callback.getCallbackEventNode());
        callbackContext.setSourceContext(callback.getSourceContext());
        callbackContext.setUniqueId(callback.getUniqueId());
        //初始化uniqueId
        if (callbackContext.getUniqueId() == null) {
            callbackContext.setUniqueId(UUID.randomUUID().toString().replaceAll("-", ""));
        }
        callbackContext = callbackRepository.upsert(callbackContext);
        log.debug("add callback data:{}", GsonUtils.getGson().toJson(callbackContext));
        //注册成功后主动触发
        if (callback.isExecuteImmediately()) {
            log.debug("callback execute immediately .");
            callbackExecuteImme(callbackContext);
        }
        return callbackContext;
    }

    /**
     * 回调立刻触发执行
     * @param callbackContext
     */
    @Async("mediaAssetExecutor")
    public void callbackExecuteImme(MediaAssetCallbackContext callbackContext) {
        MediaAsset asset = assetRepository.findById(callbackContext.getMediaId())
                .orElseThrow(() -> new KbException(KbCode.RESOURCE_NOT_FOUND).li());
        List<MediaAssetPlayInfo> playInfos = playInfoRepository.findByMediaId(asset.getId());
        MediaAssetPlayInfoModel playInfo = playService.playInfoSelect(playInfos, null);

        MediaAssetCallbackBizNotifyModel data = new MediaAssetCallbackBizNotifyModel();
        data.setMediaId(asset.getId());
        data.setState(asset.getState());
        data.setStoragePlatform(asset.getStoragePlatform());
        data.setSourceContext(callbackContext.getSourceContext());
        data.setDuration(asset.getDuration());
        data.setCoverUrl(asset.getCoverUrl());
        data.setPlayInfo(playInfo);

        callbackRequest(callbackContext.getCallbackUrl(), GsonUtils.getGson().toJson(data));
    }

    @Async("mediaAssetExecutor")
    @Retryable(value = Exception.class, maxAttempts = 3, backoff = @Backoff(delay = 1000, multiplier = 2.0))
    public void sendBizCallback(String mediaId, MediaAssetPlayInfoModel playInfo) {
        MediaAsset asset = assetRepository.findById(mediaId).orElseThrow(() -> new KbException(KbCode.RESOURCE_NOT_FOUND).li());
        List<MediaAssetCallbackContext> callbackContexts = callbackRepository.findByMediaId(mediaId);
        for (MediaAssetCallbackContext callbackContext : callbackContexts) {
            MediaAssetCallbackBizNotifyModel data = new MediaAssetCallbackBizNotifyModel();
            data.setMediaId(mediaId);
            data.setState(asset.getState());
            data.setStoragePlatform(asset.getStoragePlatform());
            data.setSourceContext(callbackContext.getSourceContext());
            data.setDuration(asset.getDuration());
            data.setCoverUrl(asset.getCoverUrl());
            data.setPlayInfo(playInfo);

            callbackRequest(callbackContext.getCallbackUrl(), GsonUtils.getGson().toJson(data));
        }
    }


    private void callbackRequest(String url, String body) {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity<String> entity = new HttpEntity<>(body, headers);

        try {
            ResponseEntity<String> response = restTemplate.postForEntity(url, entity, String.class);
            int status = response.getStatusCodeValue();
            log.info("callback send request to url:{}, status:{}, body:{}",url, status, body);
        } catch (Exception e) {
            log.warn("callback send request to " + url + " fail", e);
            throw e;
        }

    }


    /**
     * 根据配置获取回调地址
     * @param model
     * @return
     */
    private String mapCallbackUrl(MediaAssetCallbackCreateModel model) {
        MediaAssetCallbackRegisterBy registerBy =
                MediaAssetCallbackRegisterBy.resolveByName(model.getCallbackUrl()).orElse(MediaAssetCallbackRegisterBy.BY_URL);

        if (registerBy == MediaAssetCallbackRegisterBy.BY_CONF_KEY) {
            return MediaAssetCallbackConf.resolveByName(model.getCallbackKey())
                    .map(MediaAssetCallbackConf::getCallbackUrl)
                    .orElse(null);
        } else if (registerBy == MediaAssetCallbackRegisterBy.BY_URL) {
            return model.getCallbackUrl();
        } else {
            return null;
        }
    }
}
