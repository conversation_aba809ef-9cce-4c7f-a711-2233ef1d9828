package com.kaiba.m.fuse.service.media.match;

import com.kaiba.m.fuse.domain.media.MediaAssetPlayInfo;
import lombok.Data;

/**
 * <AUTHOR>
 * @version MatchResult, v0.1 2024/10/24 18:01 daopei Exp $
 **/
@Data
public class MatchResult {

    /** 得分. 分值高表示匹配度越高 */
    private int score;

    /** 视频流 */
    private MediaAssetPlayInfo stream;

    public MatchResult(MediaAssetPlayInfo stream, int score) {
        this.score = score;
        this.stream = stream;
    }
}
