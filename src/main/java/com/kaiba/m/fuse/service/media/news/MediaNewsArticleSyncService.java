package com.kaiba.m.fuse.service.media.news;

import com.kaiba.lib.base.constant.KbProperties;
import com.kaiba.lib.base.constant.fuse.media.MediaAssetOrigin;
import com.kaiba.lib.base.constant.fuse.media.MediaAssetState;
import com.kaiba.lib.base.constant.fuse.media.MediaPlatform;
import com.kaiba.lib.base.domain.common.KbTimeRange;
import com.kaiba.lib.base.domain.fuse.media.MediaAssetCallbackCreateModel;
import com.kaiba.lib.base.domain.fuse.media.MediaAssetCreateModel;
import com.kaiba.lib.base.domain.fuse.media.MediaAssetModel;
import com.kaiba.lib.base.domain.news.article.ArticleQueryModel;
import com.kaiba.lib.base.domain.news.article.ArticleVideo;
import com.kaiba.m.core.domain.news.article.NewsArticle;
import com.kaiba.m.core.repository.news.article.NewsArticleRepository;
import com.kaiba.m.fuse.domain.media.MediaAsset;
import com.kaiba.m.fuse.domain.media.MediaNewsRegisterTemp;
import com.kaiba.m.fuse.repository.media.MediaNewsRegisterTempRepository;
import com.kaiba.m.fuse.service.media.MediaAssetService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 新资讯视频从葫芦网同步服务
 *
 * <AUTHOR>
 * @version MediaNewsArticleSyncService, v0.1 2024/11/5 14:55 daopei Exp $
 **/
@Slf4j
@Service
public class MediaNewsArticleSyncService {

    private final NewsArticleRepository newsArticleRepository;
    private final MediaNewsRegisterTempRepository tempRepository;
    private final MediaAssetService mediaService;

    public MediaNewsArticleSyncService(
            NewsArticleRepository newsArticleRepository,
            MediaNewsRegisterTempRepository tempRepository,
            MediaAssetService mediaService
    ) {
        this.newsArticleRepository = newsArticleRepository;
        this.tempRepository = tempRepository;
        this.mediaService = mediaService;
    }

    public void newsRegister() {
        int p = 1;
        boolean hasNext = true;
        Long startCreateTime = findLastTemp().map(MediaNewsRegisterTemp::getArticleTime)
                .orElse(null);

        do {
            log.info("sync page :{}, startCreateTime:{}", p, startCreateTime);
            List<NewsArticle> list = loadArticleList(startCreateTime, p, 300);
            if (list.isEmpty()) {
                hasNext = false;
            }

            List<NewsArticle> news = list.stream()
                    .filter(n -> containsHooloTv(n.getVideo()))
                    .collect(Collectors.toList());

            log.info("sync size :{}", news.size());
            for (NewsArticle newsArticle : news) {
                tempRepository.upsert(map2TempInit(newsArticle));
            }

            p++;
        } while (hasNext);
    }


    @XxlJob("media-hoolo-register-job")
    public ReturnT<String> mediaRegisterJob(String param) {
        Integer pageSize = null;
        if (param != null) {
            pageSize = Integer.parseInt(param);
        }
        mediaRegister(pageSize);
        return ReturnT.SUCCESS;
    }

    public void mediaRegister(Integer size) {
        int ps = size == null ? 1 : size;
        List<MediaNewsRegisterTemp> temps = tempRepository.findByRegister(false, p(1, ps, "updateTime"));
        for (MediaNewsRegisterTemp temp : temps) {
            tempRepository.refreshUpdateTime(temp.getArticleId());

            NewsArticle article = newsArticleRepository.findById(temp.getArticleId()).orElse(null);
            if (article == null) {
                log.warn("media news reset, not found article:{}", temp.getArticleId());
                continue;
            }
            if (article.getVideo() == null) {
                log.warn("media news reset, not found article video:{}", temp.getArticleId());
                continue;
            }
            MediaAssetCreateModel createModel = new MediaAssetCreateModel();
            createModel.setTitle(temp.getTitle());
            createModel.setOrigin(MediaAssetOrigin.PULL_UPLOAD.name());
            createModel.setStoragePlatform(MediaPlatform.TENCENT.name());

            MediaAssetCreateModel.PullUploadReq pullUploadReq = new MediaAssetCreateModel.PullUploadReq();
            pullUploadReq.setSourceUrl(temp.getSourceUrl());
            createModel.setPullUpload(pullUploadReq);
            //注册资讯回调
            MediaAssetCallbackCreateModel callbackCreate = new MediaAssetCallbackCreateModel();
            callbackCreate.setCallbackUrl("http://kaiba-m-core/admin/NeoNews/article/callback/media");
            callbackCreate.setSourceContext(temp.getArticleId());
            createModel.setCallback(callbackCreate);

            //设置仅截图, 不转码
            createModel.setWorkflow("葫芦网转存模版");
            MediaAssetModel assetModel = mediaService.createByPullUpload(createModel, KbProperties.ADMIN_USER_ID);
            String mediaId = assetModel.getId();
            tempRepository.updateRegister(temp.getArticleId(), mediaId);
        }
    }

    /**
     * 重置一批媒资上传失败的状态, 后续通过任务再次触发同步
     * @param pageSize
     */
    public void resetFailItem(Integer page, Integer pageSize) {
        Pageable pageable = p(page, pageSize, null);
        List<MediaNewsRegisterTemp> list = tempRepository.findByRegister(true, pageable);
        log.info("media news reset. find need reset check size:{}", list.size());
        if (list.isEmpty()) {
            return;
        }
        List<MediaNewsRegisterTemp> resetList = list.stream().filter(this::checkIfNeedResetRegister).collect(Collectors.toList());
        log.info("media news reset. find need reset flag size:{}", resetList.size());
        for (MediaNewsRegisterTemp temp : resetList) {
            log.warn("media news reset. reset flag for id:{}", temp.getArticleId());
            tempRepository.updateUnRegister(temp.getArticleId());
        }
    }


    // -----------------------------------------


    private boolean checkIfNeedResetRegister(MediaNewsRegisterTemp temp) {
        if (temp.getMediaId() == null) {
            return true;
        }
        MediaAsset asset = mediaService.getById(temp.getMediaId()).orElse(null);
        if (asset == null) {
            return true;
        }
        MediaAssetState state = MediaAssetState.resolveByName(asset.getState()).orElse(null);
        if (state != MediaAssetState.NORMAL && state != MediaAssetState.TRANSCODING) {
            return true;
        }
        return false;
    }

    private Optional<MediaNewsRegisterTemp> findLastTemp() {
        Page<MediaNewsRegisterTemp> page = tempRepository.findAll(p_desc(1, 1, "articleTime"));
        if (page.isEmpty()) {
            return Optional.empty();
        }
        return Optional.ofNullable(page.getContent().get(0));
    }

    private List<NewsArticle> loadArticleList(Long startCreateTime, Integer page, Integer pageSize) {
        ArticleQueryModel query = new ArticleQueryModel();
        query.setPage(page);
        query.setPageSize(pageSize);
        query.setCreator(KbProperties.ADMIN_USER_ID);
        query.setRenderers(Arrays.asList("VIDEO_FULL", "VIDEO_EPISODE", "VIDEO_EPISODE_CLIP"));
        if (startCreateTime != null) {
            query.setCreateTimeRange(KbTimeRange.rangeGT(startCreateTime));
        } else {
            query.setCreateTimeRange(KbTimeRange.unbounded());
        }
        return newsArticleRepository.getPageByQuery(query).toList();
    }


    private Pageable p(Integer page, Integer pageSize, String ascField) {
        int p = page == null ? 0 : page - 1;
        int ps = pageSize == null ? 20 : pageSize;
        if (ascField == null) {
            return PageRequest.of(p, ps, Sort.by(Sort.Direction.ASC, "createTime"));
        } else {
            return PageRequest.of(p, ps, Sort.by(Sort.Direction.ASC, ascField));
        }
    }

    private Pageable p_desc(Integer page, Integer pageSize, String descField) {
        int p = page == null ? 0 : page - 1;
        int ps = pageSize == null ? 20 : pageSize;
        if (descField == null) {
            return PageRequest.of(p, ps, Sort.by(Sort.Direction.DESC, "createTime"));
        } else {
            return PageRequest.of(p, ps, Sort.by(Sort.Direction.DESC, descField));
        }
    }

    /**
     * 筛选出包含葫芦网地址并且还未转储媒资的视频
     *
     * @param video
     * @return
     */
    private boolean containsHooloTv(ArticleVideo video) {
        if (video == null) {
            return false;
        }
        if (video.getVideoUrl() == null) {
            return false;
        }
        //以及存在媒资ID则忽略
        if (video.getMediaId() != null) {
            return false;
        }
        if (video.getVideoUrl().contains("hoolo.tv")) {
            return true;
        }
        return false;
    }

    private static MediaNewsRegisterTemp map2TempInit(NewsArticle article) {
        MediaNewsRegisterTemp temp = new MediaNewsRegisterTemp();
        temp.setArticleId(article.getId());
        temp.setSourceUrl(article.getVideo().getVideoUrl());
        temp.setCreateTime(System.currentTimeMillis());
        temp.setRegister(false);
        temp.setTitle(article.getTitle());
        temp.setArticleTime(article.getCreateTime());
        return temp;
    }
}
