package com.kaiba.m.fuse.service.media.util;

import com.kaiba.lib.base.response.KbCode;
import com.kaiba.lib.base.response.KbException;

import java.net.MalformedURLException;
import java.net.URL;

/**
 * <AUTHOR>
 * @version StringUtils, v0.1 2024/11/8 17:18 daopei Exp $
 **/
public class MediaStringUtils {


    /**
     * 替换URL中的域名
     * @param originalUrl 原始URL
     * @param newDomain 新域名
     * @return 替换域名后的URL
     */
    public static String replaceDomain(String originalUrl, String newDomain) {
        try {
            // 解析原始URL
            URL url = new URL(originalUrl);

            // 构建新的URL
            StringBuilder newUrl = new StringBuilder();
            newUrl.append(url.getProtocol()).append("://");
            newUrl.append(newDomain);

            // 添加端口(如果存在且不是默认端口)
            if (url.getPort() != -1 &&
                    !((url.getProtocol().equals("http") && url.getPort() == 80) ||
                            (url.getProtocol().equals("https") && url.getPort() == 443))) {
                newUrl.append(":").append(url.getPort());
            }

            // 添加路径
            if (url.getPath() != null) {
                newUrl.append(url.getPath());
            }

            // 添加查询参数
            if (url.getQuery() != null) {
                newUrl.append("?").append(url.getQuery());
            }

            // 添加片段标识符
            if (url.getRef() != null) {
                newUrl.append("#").append(url.getRef());
            }

            return newUrl.toString();

        } catch (MalformedURLException e) {
            throw new KbException(KbCode.ILLEGAL_ARGUMENT).r("URL 处理错误");
        }
    }

    /**
     * 获取URL的路径部分(不包含查询参数)
     * @param urlString URL字符串
     * @return 路径字符串
     */
    public static String getPath(String urlString) {
        try {
            URL url = new URL(urlString);
            String path = url.getPath();

            // 如果路径为空或只有/，返回/
            if (path == null || path.isEmpty()) {
                return "/";
            }

            // 确保路径以/开头
            if (!path.startsWith("/")) {
                path = "/" + path;
            }

            return path;

        } catch (MalformedURLException e) {
            throw new KbException(KbCode.ILLEGAL_ARGUMENT).r("URL 处理错误:" + urlString);
        }
    }

    /**
     * 规范化URL路径
     * @param path 路径字符串
     * @return 规范化后的路径
     */
    private static String normalizePath(String path) {
        if (path == null || path.isEmpty()) {
            return "/";
        }

        // 移除多余的斜杠
        path = path.replaceAll("/+", "/");

        // 确保以/开头
        if (!path.startsWith("/")) {
            path = "/" + path;
        }

        // 移除结尾的/（除非路径只有/）
        if (path.length() > 1 && path.endsWith("/")) {
            path = path.substring(0, path.length() - 1);
        }

        return path;
    }
}
