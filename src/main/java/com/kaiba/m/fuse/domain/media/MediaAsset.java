package com.kaiba.m.fuse.domain.media;

import com.kaiba.lib.base.constant.KbModule;
import com.kaiba.lib.base.constant.fuse.media.MediaAssetOrigin;
import com.kaiba.lib.base.constant.fuse.media.MediaAssetState;
import com.kaiba.lib.base.constant.fuse.media.MediaAssetStorageClass;
import com.kaiba.lib.base.constant.fuse.media.MediaPlatform;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.List;

/**
 * PGC 媒资信息
 *
 * <AUTHOR>
 * @version MediaAsset, v0.1 2024/9/25 14:27 daopei Exp $
 **/
@Data
@ToString
@NoArgsConstructor
@Document(collection = "k_media_asset")
public class MediaAsset {

    private String id;

    /** 标题 */
    private String title;

    /** 状态码 {@link MediaAssetState} */
    private String state;

    /** 三方存储ID */
    private String storageId;

    /** 三方存储平台 {@link MediaPlatform}*/
    private String storagePlatform;

    /** 三方存储类型, {@link MediaAssetStorageClass} 标准、低频、归档、深度归档 */
    private String storageClass;

    /** 文件大小. 单位byte. 可用做上传时的唯一校验,但是不强制 */
    private Long size;

    /** 媒资上传方式. {@link MediaAssetOrigin} */
    private String origin;

    /** 上传方式为{@link MediaAssetOrigin#PULL_UPLOAD}{@link MediaAssetOrigin#AS_IS}时, 此处有值 */
    private String sourceUrl;

    /** 网络地址MD5值. 用于索引和重复校验 */
    private String sourceUrlMD5;

    // 初始化转码配置项 --------------

    /** 转码模版组 */
    private List<String> templateIds;

    /** 工作流 */
    private String workflow;


    // 视频源文件信息 meta --------------

    /** 文件名称 */
    private String fileName;

    /** 封面地址 */
    private String coverUrl;

    /** 视频地址 */
    private String videoUrl;

    /** 时长. 单位秒 */
    private String duration;

    /** 格式 */
    private String format;

    /** 原始视频比特率. 单位:Kbit/s */
    private String bitrate;

    /** 媒体流高度。相对值比例*/
    private Long height;

    /** 媒体流宽度。相对值比例 */
    private Long width;

    /** 媒体流高度。单位：像素*/
    private Long heightPixel;

    /** 媒体流宽度。单位：像素*/
    private Long widthPixel;

    // --------------

    /** 三方存储创建时间 */
    private Long storageCreateTime;

    /** 三方存储修改时间 */
    private Long storageUpdateTime;

    /** 创建时间 */
    private Long createTime;

    /** 更新时间 */
    private Long updateTime;

    /** 创建人ID */
    private Integer creatorId;

    /** 修改人ID */
    private Integer operatorId;

}
