package com.kaiba.m.fuse.domain.media;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * 资讯葫芦网视频数据迁移任务临时表
 * <AUTHOR>
 * @version MediaNewsRegisterTemp, v0.1 2024/11/5 15:44 daopei Exp $
 **/
@Data
@ToString
@NoArgsConstructor
@Document(collection = "k_media_news_register_temp")
public class MediaNewsRegisterTemp {

    @Id
    private String articleId;

    @Indexed
    private String sourceUrl;

    /** 媒资ID */
    private String mediaId;

    /** 是否媒资注册 */
    private Boolean register;

    /** 资讯创建时间 */
    private Long articleTime;

    /** 创建时间 */
    private Long createTime;

    /** 修改时间 */
    private Long updateTime;


    //内容属性
    private String title;


}
