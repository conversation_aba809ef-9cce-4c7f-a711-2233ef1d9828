package com.kaiba.m.fuse.domain.media;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.index.CompoundIndex;
import org.springframework.data.mongodb.core.index.CompoundIndexes;
import org.springframework.data.mongodb.core.index.Indexed;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.List;

/**
 * 媒资回调上下文信息
 * <AUTHOR>
 * @version MediaAssetCallbackContext, v0.1 2024/12/2 09:28 daopei Exp $
 **/
@Data
@ToString
@NoArgsConstructor
@Document(collection = "k_media_asset_callback_context")
@CompoundIndexes({
        @CompoundIndex(unique = true,def = "{ mediaId: 1, uniqueId: 1}", name = "uniq_media_callback"),
})
public class MediaAssetCallbackContext {

    @Id
    private String id;

    /** 媒资ID */
    @Indexed
    private String mediaId;

    /** 业务唯一标识ID. 建议使用业务ID属性 */
    @Indexed
    private String uniqueId;

    /** 需要触发通知的事件节点. {@see MediaAssetCallbackEventNode } */
    private List<String> callbackEventNode;

    /** 回调通知地址 */
    private String callbackUrl;

    /** 回调时附带的请求上下文信息 */
    private String sourceContext;

    /** 创建事件 */
    private Long createTime;

    /** 通知时间 */
    private Long notifyTime;


}
