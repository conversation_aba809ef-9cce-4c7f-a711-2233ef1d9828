package com.kaiba.m.fuse.domain.media;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * 媒资额外属性表, 媒资业务外的属性记录
 * <AUTHOR>
 * @version MediaAssetExt, v0.1 2024/10/25 11:46 daopei Exp $
 **/
@Data
@ToString
@NoArgsConstructor
@Document(collection = "k_media_asset_ext")
public class MediaAssetExt {

    @Id
    private String id;

    /** 媒资ID */
    private String mediaId;

    /** 创建人 */
    private Integer creatorId;

    /** 频率频道 */
    private String channelKey;

    /** 创建时间 */
    private Long creatTime;

    /** 修改时间 */
    private Long updateTime;
}
