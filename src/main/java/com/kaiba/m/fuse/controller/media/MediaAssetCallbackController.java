package com.kaiba.m.fuse.controller.media;

import com.alibaba.fastjson.JSONObject;
import com.kaiba.lib.base.util.GsonUtils;
import com.kaiba.m.fuse.service.media.MediaAssetService;
import com.kaiba.lib.base.constant.fuse.media.MediaPlatform;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 媒资对接三方回调(http模式)接口
 * <AUTHOR>
 * @version MediaAssetCallbackController, v0.1 2024/10/10 14:32 daopei Exp $
 **/
@Slf4j
@RestController
@RequestMapping("/fuse/media/callback")
public class MediaAssetCallbackController {

    private final MediaAssetService mediaAssetService;

    public MediaAssetCallbackController(MediaAssetService mediaAssetService) {
        this.mediaAssetService = mediaAssetService;
    }


    @PostMapping("/ali")
    public void callbackFromAli(
            @RequestBody JSONObject body
    ) {
        log.info("mediaAsset - receive ali callback :{}",body.toString());
        mediaAssetService.handleCallback(body.toString(), MediaPlatform.ALI.name());
    }


    @PostMapping("/tencent")
    public void callbackFromTencent(
            @RequestBody JSONObject body
    ) {
        log.info("mediaAsset - receive tencent callback :{}",body.toString());
        mediaAssetService.handleCallback(GsonUtils.getGson().toJson(body), MediaPlatform.TENCENT.name());
    }
}
