package com.kaiba.m.fuse.controller.media;

import com.kaiba.lib.base.constant.KbHeader;
import com.kaiba.lib.base.constant.fuse.media.MediaAssetCallbackRegisterBy;
import com.kaiba.lib.base.constant.fuse.media.MediaAssetDefinition;
import com.kaiba.lib.base.constant.fuse.media.MediaAssetFormat;
import com.kaiba.lib.base.constant.fuse.media.MediaAssetOrigin;
import com.kaiba.lib.base.constant.fuse.media.MediaPlatform;
import com.kaiba.lib.base.domain.fuse.media.MediaAssetCallbackCreateModel;
import com.kaiba.lib.base.domain.fuse.media.MediaAssetCreateModel;
import com.kaiba.lib.base.domain.fuse.media.MediaAssetPlayFetchModel;
import com.kaiba.lib.base.domain.fuse.media.MediaAssetPlayResult;
import com.kaiba.lib.base.domain.fuse.media.MediaAssetPreUploadResult;
import com.kaiba.lib.base.middleware.KbRes;
import com.kaiba.lib.base.response.KbEntity;
import com.kaiba.lib.base.service.fuse.IMediaAssetService;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @version MediaAssetApiController, v0.1 2024/11/18 14:58 daopei Exp $
 **/
@RestController
@RequestMapping("/fuse/media/toc")
public class MediaAssetApiController {

    private final KbRes kbRes;
    private final IMediaAssetService mediaAssetService;


    public MediaAssetApiController(
            KbRes kbRes,
            IMediaAssetService mediaAssetService
    ) {
        this.kbRes = kbRes;
        this.mediaAssetService = mediaAssetService;
    }

    @ApiOperation("快速获取媒资播放信息, 仅需指定媒资ID")
    @PostMapping("/quickFetch")
    public KbEntity<MediaAssetPlayResult> quickFetch(
            @RequestParam String id
    ) {
        MediaAssetPlayFetchModel fetchQuery = new MediaAssetPlayFetchModel();
        fetchQuery.setMediaId(id);
        fetchQuery.setDefinition(MediaAssetDefinition.HD.name());
        fetchQuery.setAllowCache(true);
        fetchQuery.setKeyRefer(false);
        fetchQuery.setFormat(MediaAssetFormat.MP4.name());
        return mediaAssetService.playFetch(fetchQuery);
    }

    @ApiOperation("细致化获取媒资播放信息")
    @PostMapping("/fetch")
    public KbEntity<MediaAssetPlayResult> fetch(
            @RequestBody MediaAssetPlayFetchModel fetchQuery
    ) {
        fetchQuery.setAllowCache(true);
        fetchQuery.setKeyRefer(false);
        return mediaAssetService.playFetch(fetchQuery);
    }

    @ApiOperation("视频上传，用户端PGC内容（例如 圈子版主上传）")
    @PostMapping("/upload")
    public KbEntity<MediaAssetPreUploadResult> upload(
            @RequestHeader(KbHeader.KB_USER_ID) Integer userId,
            @RequestParam String fileName,
            @RequestParam String title,
            @RequestParam(required = false, defaultValue = "-1") Long size,
            @RequestParam(required = false) String callbackKey
    ) {
        MediaAssetCreateModel model = new MediaAssetCreateModel();
        model.setWorkflow("用户端视频转码V1");//todo 使用指定工作流
        model.setOrigin(MediaAssetOrigin.FILE_UPLOAD.name());
        model.setStoragePlatform(MediaPlatform.TENCENT.name());
        model.setTitle(title);

        MediaAssetCreateModel.FileUploadReq fileUploadReq = new MediaAssetCreateModel.FileUploadReq();
        fileUploadReq.setFileName(fileName);
        fileUploadReq.setSize(size);
        model.setFileUpload(fileUploadReq);

        if (callbackKey != null) {
            MediaAssetCallbackCreateModel callback = new MediaAssetCallbackCreateModel();
            callback.setRegisterBy(MediaAssetCallbackRegisterBy.BY_CONF_KEY.name());
            callback.setCallbackKey(callbackKey);
            model.setCallback(callback);
        }
        return mediaAssetService.createByFileUpload(userId, model);
    }


}
