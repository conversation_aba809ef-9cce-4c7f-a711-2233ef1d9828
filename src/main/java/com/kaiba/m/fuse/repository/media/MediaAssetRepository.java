package com.kaiba.m.fuse.repository.media;

import com.kaiba.m.core.middleware.mongo.interfaces.KbMongoRepository;
import com.kaiba.m.fuse.domain.media.MediaAsset;
import com.kaiba.lib.base.domain.fuse.media.MediaAssetQueryModel;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.core.FindAndModifyOptions;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @version MediaAssetRepository, v0.1 2024/9/26 16:26 daopei Exp $
 **/
@Repository
public interface MediaAssetRepository extends KbMongoRepository<MediaAsset, String> {

    Optional<MediaAsset> findByStorageId(String storageId);

    Optional<MediaAsset> findFirstBySize(Long size);
    Optional<MediaAsset> findFirstByFileName(String fileName);
    Optional<MediaAsset> findFirstByTitle(String title);
    Optional<MediaAsset> findFirstBySourceUrlMD5(String sourceUrlMD5);



    default Page<MediaAsset> getByParam(MediaAssetQueryModel model, Pageable pageable) {
        Criteria criteria = new Criteria();
        if (model.getMediaId() != null) {
            criteria.and("_id").is(model.getMediaId());
        }
        if (model.getStorageId() != null) {
            criteria.and("storageId").is(model.getStorageId());
        }
        if (model.getStoragePlatform() != null) {
            criteria.and("storagePlatform").is(model.getStoragePlatform());
        }
        if (model.getOrigin() != null) {
            criteria.and("origin").is(model.getOrigin());
        }
        if (model.getFileName() != null) {
            criteria.and("fileName").is(model.getFileName());
        }
        if (model.getTitle() != null) {
            criteria.and("title").is(model.getTitle());
        }
        if (model.getSourceUrl() != null) {
            criteria.and("sourceUrl").is(model.getSourceUrl());
        }
        if (model.getCreatorId() != null) {
            criteria.and("creatorId").is(model.getCreatorId());
        }
        Query query = new Query(criteria);
        long count = mongo().count(query, MediaAsset.class);
        List<MediaAsset> list = mongo().find(query.with(pageable), MediaAsset.class);

        return new PageImpl<>(list, pageable, count);
    }

    default MediaAsset updateStorageId(String id, String storageId) {
        return mongo().findAndModify(
                new Query(Criteria.where("_id").is(id)),
                new Update()
                        .set("storageId", storageId),
                FindAndModifyOptions.options().returnNew(true).upsert(false),
                MediaAsset.class
        );
    }

    default MediaAsset updateState(String id, String state) {
        return mongo().findAndModify(
                new Query(Criteria.where("_id").is(id)),
                new Update()
                        .set("updateTime", System.currentTimeMillis())
                        .set("state", state),
                FindAndModifyOptions.options().returnNew(true).upsert(false),
                MediaAsset.class
        );
    }

    default MediaAsset updateTitle(String id, String title) {
        return mongo().findAndModify(
                new Query(Criteria.where("_id").is(id)),
                new Update()
                        .set("updateTime", System.currentTimeMillis())
                        .set("title", title),
                FindAndModifyOptions.options().returnNew(true).upsert(false),
                MediaAsset.class
        );
    }



}
