package com.kaiba.m.fuse.repository.media;

import com.kaiba.m.core.middleware.mongo.interfaces.KbMongoRepository;
import com.kaiba.lib.base.constant.fuse.media.MediaAssetPullUploadState;
import com.kaiba.m.fuse.domain.media.MediaAssetPullTask;
import org.springframework.data.mongodb.core.FindAndModifyOptions;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Repository;

import java.util.Optional;

/**
 * <AUTHOR>
 * @version MediaAssetPullTaskRepository, v0.1 2024/10/12 16:38 daopei Exp $
 **/
@Repository
public interface MediaAssetPullTaskRepository extends KbMongoRepository<MediaAssetPullTask, String> {

    Optional<MediaAssetPullTask> findFirstBySourceUrlMD5(String sourceUrlMD5);

    Optional<MediaAssetPullTask> findFirstByStorageTaskId(String taskId);
    Optional<MediaAssetPullTask> findFirstByMediaId(String mediaId);

    default MediaAssetPullTask upsert(String mediaId, String taskId) {
        return mongo().findAndModify(
                new Query(Criteria.where("mediaId").is(mediaId)),
                new Update()
                        .setOnInsert("createTime", System.currentTimeMillis())
                        .setOnInsert("state", MediaAssetPullUploadState.DOING.name())
                        .set("taskId", taskId)
                        .set("updateTime", System.currentTimeMillis()),
                FindAndModifyOptions.options().upsert(true).returnNew(true),
                MediaAssetPullTask.class
        );
    }


    /**
     * 更新任务进度信息
     * @param id
     * @param state
     * @param progress
     * @param resultMessage
     * @return
     */
    default MediaAssetPullTask updateProgress(String id, String state, String storageId, Integer progress, String resultMessage) {
        return mongo().findAndModify(
                new Query(Criteria.where("_id").is(id)),
                new Update()
                        .set("state", state)
                        .set("storageId", storageId)
                        .set("progress", progress)
                        .set("resultMessage", resultMessage)
                        .set("updateTime", System.currentTimeMillis()),
                FindAndModifyOptions.options().upsert(false).returnNew(true),
                MediaAssetPullTask.class
        );
    }

}
