debug=false
logging.level.com.kaiba=INFO
kaiba-m-accounting.ribbon.ReadTimeout=2000
kaiba-m-issuetask.ribbon.ReadTimeout=2500
kaiba-mall.ribbon.ReadTimeout=2000

spring.redis.timeout=200ms

kaiba.path=/kaiba/${spring.profiles.active}/${spring.application.name}

kaiba.sms.ronglianyun.serverIP=app.cloopen.com
kaiba.sms.ronglianyun.serverPort=8883
kaiba.sms.ronglianyun.accountSid=8a48b55151f715fb01520c03cb682226
kaiba.sms.ronglianyun.accountToken=267f0ddf4506449e9f7cef7eb200aaf1

kaiba.sms.ronglianyun.appIdForKaiba=aaf98f8951f7362501520c0650f12185
kaiba.sms.ronglianyun.appIdForExpert=8aaf070867e8660f0167f2db31aa051e

kaiba.log.app.ali_log_project=springcloud-product
kaiba.log.app.ali_log_store=springcloud-app-product
kaiba.log.app.ali_log_topic=${spring.application.name}
kaiba.log.request.ali_log_project=springcloud-product
kaiba.log.request.ali_log_store=springcloud-request-product
kaiba.log.request.ali_log_topic=${spring.application.name}

kaiba.jiguang.app_key=190756bcd44b4f54174672b6
kaiba.jiguang.master_secret=79acc715540e56f51f1a087e

kaiba.auto-reply.robot=4602920

kaiba.tmuyun.send-article-url=https://import.tmuyun.com/import

kaiba.opensearch.app_name=search_prod
kaiba.opensearch.wo.app_name=work_order_prod

kaiba.sensors.project=production
vod.tencent.appId=**********