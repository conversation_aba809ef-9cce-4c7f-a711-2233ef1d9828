#!/bin/bash
# author : liuxu
# date   : 2018-09-06

PROJECT=kaiba_sc_core
PROFILE=dev
MAVEN_SKIP_TEST=
DEBUG=false

#JAVA_OPTS="-Xms256m -Xmx512m -XX:-TieredCompilation -Xss256k -XX:+UseG1GC -XX:+UseStringDeduplication"

#====================================

function Help() {
cat <<"EOF"

-----------------------------------------
USAGE:
start_as_docker.sh [-p profile] [-s]

OPTIONS:
-p : profile. default "dev"
-d : turn on debug
-s : skip test
-----------------------------------------

EOF
}

#process options
function ProcessOptions() {
    while getopts ":p:sd" opt; do
        case "$opt" in
            "p")
                PROFILE=$OPTARG
                ;;
            "s")
                MAVEN_SKIP_TEST="-Dmaven.test.skip=true"
                ;;
            "d")
                DEBUG=true
                ;;
            "?")
                #Unknown option
                echo "* unknown option: $opt"
                Help
                exit 1
                ;;
            ":")
                #an option needs a value, which, however, is not presented
                echo "* option -$opt needs a value, but it is not presented"
                Help
                exit 1
                ;;
            *)
                #unknown error, should not occur
                echo "* unknown error while processing options and params"
                Help
                exit 1
                ;;
        esac
    done
    return $OPTIND
}

ProcessOptions "$@"

#====================================

if [ "$PROFILE" == "" ]; then
    echo "* need to specify profile"
    Help
    exit 2
fi

if [ ! -e "Dockerfile" ]; then
    echo "* can not find Dockerfile"
    exit 2
fi

IMAGE_TAG="kaiba/$PROJECT"
CONTAINER_NAME="$PROJECT"

mvn_cmd="mvn clean package $MAVEN_SKIP_TEST"
echo "about to run mvn cmd:"
echo "$mvn_cmd"
$mvn_cmd

if [ $? -ne 0 ]; then
    echo "maven build fail"
    exit 1
fi

docker build --rm --tag $IMAGE_TAG .
if [ $? -ne 0 ]; then
    echo "docker build fail"
    exit 1
fi

application_properties_file="src/main/resources/application.properties"
application_port=$(sed -n '/^server.port/'p "$application_properties_file" | awk -F "=" '{print $2}')
if [ ! "$application_port" == "" ]; then
    echo "about to call http://127.0.0.1:$application_port/actuator/shutdown"
    curl -XPOST "http://127.0.0.1:$application_port/actuator/shutdown"
    sleep 5s
fi

container_id=$(docker ps -q --filter name="$CONTAINER_NAME")
if [ ! "$container_id" == "" ]; then
    echo "container running, stop: $container_id"
    docker stop --time 5 $container_id
    sleep 2s
fi

docker run \
     --rm -d \
     --net=host \
     --log-driver json-file --log-opt max-size=10m --log-opt max-file=3 \
     --name=$CONTAINER_NAME \
     -v /cloud/data/springcloud/$PROJECT:/kaiba \
     -e SPRING_PROFILES_ACTIVE=$PROFILE \
     $IMAGE_TAG

echo "-------------------------------------------------"
docker ps --filter name=$CONTAINER_NAME
date "+%Y-%m-%d_%H:%M:%S"
echo "-------------------------------------------------"

if $DEBUG; then
    echo "-------------------------------------------------"
    echo
    docker logs -f $CONTAINER_NAME
fi
